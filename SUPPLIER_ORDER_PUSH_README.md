# 供应商订单推送报表功能说明

## 功能概述

本功能实现了供应商订单推送的后台报表管理，包括：
1. 推送情况列表查询和展示
2. 订单推送详情查看
3. 失败订单的重传功能
4. 完整的日志记录

## 实现的需求

### 1. ✅ 报表观察推送情况列表接口
- **查询条件**：
  - a. 传送状态（下拉选择成功/失败，对应push_status）
  - b. 推送时间（接收时间段）
  - c. 订单编码
- **列表字段**：订单编码、用户信息、金额、推送状态、审核状态、推送时间等

### 2. ✅ 通过ID读取详情接口
- 显示订单完整信息
- 包含商品明细
- 显示推送状态和审核状态

### 3. ✅ 详情更新重传接口
- **不可更改字段**：订单来源、订单号、订单状态、支付方式、订单商品的活动类型、卡券ID
- **可更改字段**：其他所有字段都可更改
- 提交时不修改原表记录，而是更新后重新推送

### 4. ✅ 调用供应商接口
- 更新数据库后调用 `\app\common\port\connectors\Supplier::submitSupplierOrder`
- 记录对方返回状态
- 记录进 DbLog

### 5. ✅ 后台文件结构
- 控制器：`application/admin_v2/controller/SupplierOrderPush.php`
- 视图文件：`application/admin_v2/view/supplier_order_push/`

## 文件结构

```
application/
├── admin_v2/
│   └── controller/
│       └── SupplierOrderPush.php              # 后台API控制器
└── common/
    └── model/
        └── bu/
            └── BuSupplierOrderPushLog.php     # 模型（已更新）

# 配置和文档文件
supplier_order_push_menu.sql                   # 菜单配置SQL
SUPPLIER_ORDER_PUSH_API.md                     # API接口文档
SUPPLIER_ORDER_PUSH_README.md                  # 使用说明文档
```

## 主要功能接口

### 1. 跳转到前端项目
**URL**: `/admin_v2/supplier_order_push/index`
- 跳转到前端项目页面（如果配置了web_menu_url）

### 2. 获取列表数据
**URL**: `/admin_v2/supplier_order_push/getList`
**方法**: GET
**参数**:
- `push_status`: 推送状态（0-失败，1-成功）
- `start_time`: 开始时间
- `end_time`: 结束时间
- `order_code`: 订单编码
- `pagesize`: 每页数量

### 3. 获取详情
**URL**: `/admin_v2/supplier_order_push/detail`
**方法**: GET
**参数**: `id` - 记录ID

### 4. 执行重传
**URL**: `/admin_v2/supplier_order_push/retransmit`
**方法**: POST
**功能**:
- 更新可修改字段
- 调用供应商接口重新推送
- 记录推送结果到DbLog

> **注意**: 所有接口都使用 `print_json` 格式返回数据，参考 CardApi.php 的写法

## 数据库表

### BuSupplierOrderPushLog 表字段说明
- `id`: 主键
- `order_detail_no`: 订单编码
- `push_status`: 推送状态（0-失败，1-成功）
- `audit_status`: 审核状态（0-未审核，1-审核通过，2-审核拒绝）
- `push_time`: 推送时间
- `detail`: 商品详情（JSON格式）
- 其他订单相关字段...

## 部署步骤

### 1. 部署代码文件
确保以下文件已正确放置：
- 控制器文件：`SupplierOrderPush.php`
- 模型更新：`BuSupplierOrderPushLog.php`

### 2. 配置菜单
执行 `supplier_order_push_menu.sql` 中的SQL语句：
```sql
-- 需要根据实际的菜单结构调整父级菜单ID
-- 查看现有菜单结构
SELECT * FROM t_sys_menu WHERE menu_name LIKE '%订单%' OR menu_name LIKE '%报表%' ORDER BY id;

-- 然后执行插入语句，调整menu_pid为合适的父级菜单ID
```

### 3. 权限配置
在后台管理系统中：
1. 进入菜单管理
2. 找到新添加的"供应商订单推送报表"菜单
3. 为相应的用户组分配权限

## 使用说明

### 1. API调用方式
本功能采用纯API接口方式，前端项目需要调用相应的接口：

- **获取列表**: `GET /admin_v2/supplier_order_push/getList`
- **获取详情**: `GET /admin_v2/supplier_order_push/detail`
- **重传订单**: `POST /admin_v2/supplier_order_push/retransmit`

### 2. 数据格式
所有接口都使用 `print_json` 格式返回数据：
```json
{
    "code": 0,        // 0-成功，1-失败
    "msg": "success", // 消息
    "data": {}        // 数据
}
```

### 3. 重传功能说明
- **不可修改字段**: 订单来源、订单号、订单状态、支付方式、活动类型、卡券ID
- **可修改字段**: 其他所有字段都可以修改
- **处理流程**: 更新数据 → 调用供应商接口 → 记录结果

### 4. 状态监控
- **推送状态**: 0-失败，1-成功
- **审核状态**: 0-未审核，1-审核通过，2-审核拒绝

详细的API接口说明请参考 `SUPPLIER_ORDER_PUSH_API.md` 文档。

## 技术特性

### 1. 数据安全
- 使用数据库事务确保数据一致性
- 完整的错误处理和日志记录
- 敏感字段保护（不可修改关键业务字段）

### 2. 用户体验
- 响应式界面设计
- 实时状态显示
- 分页和筛选功能
- 弹窗式详情和编辑

### 3. 系统集成
- 与现有供应商接口无缝集成
- 统一的日志记录系统
- 符合现有后台管理系统风格

## 注意事项

1. **权限控制**：确保只有授权用户可以访问和操作
2. **数据备份**：重传前建议备份重要数据
3. **监控告警**：建议设置推送失败的告警机制
4. **定期清理**：定期清理过期的推送日志数据

## 扩展功能建议

1. **批量重传**：支持批量选择失败订单进行重传
2. **自动重传**：设置定时任务自动重传失败订单
3. **推送统计**：添加推送成功率统计图表
4. **导出功能**：支持推送记录的Excel导出

## 故障排查

### 常见问题
1. **菜单不显示**：检查菜单配置和权限设置
2. **重传失败**：检查供应商接口连接和参数格式
3. **数据不更新**：检查数据库连接和事务处理

### 日志查看
- 系统日志：查看 DbLog 表中 type='retransmit_supplier_order' 的记录
- 错误日志：查看应用日志文件中的错误信息
