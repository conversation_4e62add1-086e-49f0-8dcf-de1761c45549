# 项目开发规范文档

## 1. 项目概述

本项目是基于 ThinkPHP 5.0 框架开发的汽车经销商管理系统，主要包含商城管理、经销商管理、小程序端、API接口等多个模块。

### 1.1 技术栈
- **后端框架**: ThinkPHP 5.0
- **PHP版本**: >= 7.4.0
- **数据库**: MySQL
- **缓存**: Redis
- **第三方集成**: 微信小程序、京东API、联友支付等

### 1.2 主要业务模块
- 商城管理系统 (admin_v2)
- 经销商管理 (net_dealer)
- 小程序端 (net_small)
- API接口 (api, fuli_api, jifen_api, net_api)
- 支付模块 (pay, pay_chebaba, pay_dev)
- 企业版 (qiye, qc_qiye)
- 活动模块 (active)

## 2. 代码组织规范

### 2.1 目录结构规范
```
application/
├── common/          # 公共模块
│   ├── model/       # 数据模型
│   ├── service/     # 业务服务层
│   ├── validate/    # 数据验证
│   ├── lib/         # 工具库
│   └── port/        # 外部接口连接器
│   └── validate/    # 数据验证
│   └── queue/       # 队列任务
│   └── net_serivice/ # 当前主要业务服务层     
├── admin_v2/        # 后台管理模块
├── net_small/       # 小程序模块
├── api/             # API接口模块
└── [其他业务模块]/
```

### 2.2 命名规范

#### 2.2.1 文件命名
- **控制器**: 使用大驼峰命名，如 `HomeController.php`
- **模型**: 使用 `Db` 前缀 + 大驼峰，如 `DbUser.php`
- **服务类**: 使用 `Service` 后缀，如 `BaseDataService.php`
- **验证器**: 使用业务名称，如 `Order.php`

#### 2.2.2 类命名
- **数据库模型**: `Db` + 表名（去掉前缀），如 `DbUser`
- **业务模型**: `Bu` + 业务名称，如 `BuOrder`
- **活动模型**: `Ac` + 活动名称，如 `AcCarPointLog`
- **接口模型**: `I` + 接口名称，如 `IRequestLog`

#### 2.2.3 方法命名
- 使用小驼峰命名法
- 获取数据: `get` + 名称，如 `getUserCarSeries()`
- 设置数据: `set` + 名称
- 检查验证: `check` + 名称
- 处理业务: 动词 + 名词，如 `processOrder()`

### 2.3 数据库规范

#### 2.3.1 表命名
- 使用前缀区分模块：
  - `t_db_`: 基础数据表
  - `t_bu_`: 业务数据表
  - `t_ac_`: 活动相关表
  - `t_sys_`: 系统配置表

#### 2.3.2 字段命名
- 使用下划线分隔的小写字母
- 主键统一使用 `id`
- 时间字段: `created_time`, `updated_time`
- 操作人字段: `creator`, `modifier`
- 状态字段: `status`, `is_enable`
- 外键字段: `表名_id`

## 3. 开发规范

### 3.1 代码风格
- 遵循 PSR-4 自动加载规范
- 使用 4 个空格缩进
- 类的开始大括号另起一行
- 方法的开始大括号与方法名同行

### 3.2 注释规范
```php
/**
 * 方法描述
 * @param string $param1 参数1描述
 * @param int $param2 参数2描述
 * @return array 返回值描述
 * <AUTHOR>
 * @time 创建时间
 */
public function methodName($param1, $param2)
{
    // 方法实现
}
```

### 3.3 错误处理
- 使用统一的异常处理机制
- 记录详细的错误日志
- 返回标准化的错误响应格式

### 3.4 安全规范
- 所有用户输入必须进行验证和过滤
- 使用参数化查询防止SQL注入
- 敏感数据加密存储
- API接口使用token认证

## 4. API开发规范

### 4.1 接口设计
- 使用RESTful风格
- 统一的响应格式：
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {}
}
```

### 4.2 接口文档
- 使用注解自动生成API文档
- 每个接口必须包含完整的参数说明
- 提供请求和响应示例

### 4.3 版本控制
- API版本通过路径区分: `/api/v1/`, `/api/v2/`
- 向后兼容原则

## 5. 数据库操作规范

### 5.1 模型使用
- 统一继承 `Common` 基类
- 使用模型的验证功能
- 合理使用关联查询

### 5.2 查询优化
- 避免 N+1 查询问题
- 合理使用索引
- 大数据量查询使用分页

## 6. 缓存策略

### 6.1 缓存使用场景
- 频繁查询的基础数据
- 计算复杂的业务数据
- 第三方API调用结果

### 6.2 缓存命名
- 使用模块前缀: `module:key`
- 包含版本信息避免冲突

## 7. 日志规范

### 7.1 日志级别
- ERROR: 系统错误
- WARNING: 警告信息
- INFO: 重要业务操作
- DEBUG: 调试信息

### 7.2 日志内容
- 包含用户ID、操作时间、操作内容
- 敏感信息脱敏处理

## 8. 测试规范

### 8.1 单元测试
- 核心业务逻辑必须有单元测试
- 测试覆盖率不低于80%

### 8.2 接口测试
- 所有API接口必须有测试用例
- 包含正常和异常场景

## 9. 部署规范

### 9.1 环境配置
- 开发环境: develop
- 测试环境: testing  
- 生产环境: product
- 灰度环境: graw

### 9.2 配置管理
- 敏感配置使用环境变量
- 不同环境使用不同配置文件

## 10. 代码审查

### 10.1 审查要点
- 代码规范性
- 业务逻辑正确性
- 性能优化
- 安全性检查

### 10.2 提交规范
- 提交信息清晰描述修改内容
- 单次提交保持功能完整性
- 重要修改必须经过代码审查

## 11. 性能优化

### 11.1 数据库优化
- 合理设计索引
- 避免全表扫描
- 使用连接池

### 11.2 代码优化
- 减少不必要的循环
- 合理使用缓存
- 异步处理耗时操作

## 12. 监控和运维

### 12.1 系统监控
- 服务器性能监控
- 数据库性能监控
- 应用错误监控

### 12.2 业务监控
- 关键业务指标监控
- 用户行为分析
- 异常数据告警

## 13. 第三方集成规范

### 13.1 微信小程序集成
- 统一的微信API封装
- access_token 管理和刷新
- 用户信息加密处理
- 支付回调处理

### 13.2 京东API集成
- 订单同步机制
- 库存管理
- 商品信息同步

### 13.3 支付集成
- 多支付方式支持
- 支付回调验证
- 订单状态同步

## 14. 队列和定时任务

### 14.1 队列使用
- 异步处理耗时操作
- 订单处理队列
- 消息推送队列

### 14.2 定时任务
- 使用 Cron 管理定时任务
- 任务执行日志记录
- 失败重试机制

## 15. 数据迁移规范

### 15.1 迁移脚本
- 使用 think-migration 管理数据库变更
- 迁移脚本版本控制
- 回滚方案准备

### 15.2 数据同步
- 官微到车生活数据迁移
- 增量同步机制
- 数据一致性检查

## 16. 扩展开发规范

### 16.1 自定义扩展
- 扩展放置在 `extend/` 目录
- 遵循 PSR-4 命名空间规范
- 提供完整的使用文档

### 16.2 第三方包管理
- 使用 Composer 管理依赖
- 版本锁定避免兼容性问题
- 定期更新安全补丁

## 17. 文档维护

### 17.1 API文档
- 使用注解自动生成
- 及时更新接口变更
- 提供调试环境

### 17.2 开发文档
- 业务流程文档
- 技术架构文档
- 部署运维文档

## 18. 代码质量保证

### 18.1 静态代码分析
- 使用 PHPStan 进行静态分析
- 代码复杂度检查
- 潜在bug检测

### 18.2 代码格式化
- 使用 PHP-CS-Fixer 统一代码风格
- Git hooks 自动格式化
- IDE 配置统一

## 19. 安全防护

### 19.1 输入验证
- 所有用户输入必须验证
- 使用白名单过滤
- XSS 防护

### 19.2 权限控制
- 基于角色的访问控制
- API 接口权限验证
- 敏感操作二次确认

### 19.3 数据加密
- 用户密码加密存储
- 敏感数据传输加密
- 数据库连接加密

## 20. 故障处理

### 20.1 故障响应
- 建立故障响应机制
- 故障等级分类
- 应急处理流程

### 20.2 故障预防
- 定期备份数据
- 监控告警设置
- 容灾方案准备

---

**文档版本**: v1.0
**最后更新**: 2025-07-18
**维护人员**: 开发团队

> 本文档将根据项目发展持续更新，所有开发人员都有义务遵守以上规范，并及时反馈规范中的问题和改进建议。
