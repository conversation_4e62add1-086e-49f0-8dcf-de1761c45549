graph TD
    A[Component控制器开始] --> B{请求方法判断}
    B -->|pageComponentList| C[pageComponentList方法]
    B -->|componentDetail| D[componentDetail方法]
    
    %% pageComponentList 流程
    C --> C1[参数验证 ComponentLayoutValidate]
    C1 --> C2[获取home_sm_id<br/>默认值处理]
    C2 --> C3[调用getComponentData<br/>获取组件配置]
    
    C3 --> C3A[构建缓存Key<br/>home:component_data:pageId]
    C3A --> C3B{检查Redis缓存}
    C3B -->|缓存命中| C3C[返回缓存数据]
    C3B -->|缓存未命中| C3D[数据库查询<br/>DbHomeSm::find]
    C3D --> C3E["SELECT data_json<br/>FROM t_db_home_sm<br/>WHERE id=?"]
    C3E --> C3F[Redis缓存写入<br/>3600-7200*10秒]
    C3F --> C3G[Redis SADD集合管理<br/>缓存key管理]
    C3G --> C3H[JSON解析<br/>json_decode_assoc]
    C3C --> C3H
    
    C3H --> C4[遍历组件数组<br/>生成component_id]
    C4 --> C5{组件类型判断<br/>richComponentType?}
    C5 -->|是动态组件| C6[移除attribute属性<br/>不返回详情配置]
    C5 -->|是静态组件| C7[保留完整配置]
    C6 --> C8[staticComponentProcess<br/>静态组件处理]
    C7 --> C8
    
    %% staticComponentProcess 详细流程
    C8 --> C8A[getUserGroups<br/>获取用户群组]
    C8A --> C8B["WlzCrowdsLogs查询<br/>WHERE user_id=?"]
    C8B --> C8C[提取crowd_id数组]
    
    C8C --> C8D[检查是否需要千人千面<br/>遍历cCarousel组件]
    C8D --> C8E{推荐逻辑类型包含4?<br/>猜你喜欢}
    C8E -->|是| C8F[调用getAbsDataNew<br/>数据组推荐]
    C8E -->|否| C8G[跳过推荐数据获取]
    
    C8F --> C8F1["getAbsDataNew调用<br/>WHERE vin=?"]
    C8F1 --> C8F2[提取commodity_id数组]
    C8F2 --> C8F3[NetGoods::goodsList<br/>商品详情查询]
    C8F3 --> C8F4[BuGwRecommendationBannerCommodity<br/>Banner商品关联查询]
    C8F4 --> C8F5["复杂联表查询:<br/>t_bu_gw_recommendation_banner_commodity a<br/>JOIN t_bu_gw_recommendation_banner b<br/>WHERE a.commodity_id IN (...)"]
    C8F5 --> C8G
    
    C8G --> C8H[组件类型循环处理<br/>foreach componentList]
    C8H --> C8I{组件类型判断}
    
    %% 广告组件处理
    C8I -->|cAdvertising类型| C8J[广告组件时间验证]
    C8J --> C8J1[遍历imgs数组<br/>检查show_time]
    C8J1 --> C8J2{时间是否有效}
    C8J2 -->|无效| C8J3[移除该广告<br/>unset imgs[key]]
    C8J2 -->|有效| C8J4[用户群组验证<br/>userGroups检查]
    C8J4 --> C8J5{用户是否在群组中}
    C8J5 -->|否| C8J3
    C8J5 -->|是| C8J6[保留广告]
    C8J3 --> C8J7[检查下一个广告]
    C8J6 --> C8J7
    C8J7 --> C8K
    
    %% 轮播组件处理
    C8I -->|cCarousel轮播| C8L[轮播组件处理]
    C8L --> C8L1[获取推荐逻辑配置<br/>recommended_logical_types]
    C8L1 --> C8L2{推荐逻辑类型判断}
    C8L2 -->|类型2-后台配置| C8L3[后台配置图片处理]
    C8L2 -->|类型4-猜你喜欢| C8L4[千人千面推荐处理]
    
    C8L3 --> C8L3A[遍历imgs数组<br/>时间和群组验证]
    C8L3A --> C8L3B[图片排序处理<br/>sort_order]
    C8L3B --> C8L3C[渠道适配<br/>GWAPP/GWSM取首个]
    C8L3C --> C8K
    
    C8L4 --> C8L4A[匹配banner_commodity_list<br/>推荐商品数据]
    C8L4A --> C8L4B{是否固定顶部<br/>isFixedTop}
    C8L4B -->|是| C8L4C[匹配image_type=1]
    C8L4B -->|否| C8L4D[匹配bannerSize类型]
    C8L4C --> C8L4E[构建商品跳转数据<br/>savePath配置]
    C8L4D --> C8L4E
    C8L4E --> C8L4F{推荐数据是否匹配}
    C8L4F -->|否| C8L4G[使用默认配置数据]
    C8L4F -->|是| C8K
    C8L4G --> C8K
    
    %% 其他组件处理
    C8I -->|cTabs标签| C8M[设置is_classify=1]
    C8I -->|其他组件| C8N[保持原样]
    C8M --> C8K
    C8N --> C8K
    
    C8K[组件处理完成] --> C8O{是否还有组件<br/>循环继续?}
    C8O -->|是| C8H
    C8O -->|否| C9[返回组件列表<br/>array_values重新索引]
    
    C9 --> C10[返回JSON响应<br/>setResponseData]

    %% componentDetail 流程
    D --> D1[参数验证 ComponentDetailValidate]
    D1 --> D2[获取home_sm_id<br/>默认值处理]
    D2 --> D3[调用getComponentData<br/>获取组件配置]
    D3 --> D4[解析component_id<br/>提取索引]
    D4 --> D5[根据索引获取组件<br/>componentList[componentId]]
    D5 --> D6{组件是否存在}
    D6 -->|否| D7[返回空组件数据]
    D6 -->|是| D8{是否广告类组件<br/>需要act_code}
    D8 -->|是且缺少act_code| D9[返回错误<br/>act_code不能为空]
    D8 -->|否或有act_code| D10[GoodsCustomize::componentHandle<br/>组件详情处理]

    %% GoodsCustomize::componentHandle 详细流程
    D10 --> D10A{组件类型判断<br/>switch component type}

    D10A -->|cLottery抽奖| D11[componentLottery处理]
    D11 --> D11A[DbDraw::getOne<br/>抽奖活动查询]
    D11A --> D11B[活动时间验证<br/>end_time is_enable]
    D11B --> D11C[HaoWan::getLotteryNum<br/>用户抽奖次数查询]
    D11C --> D11D[抽奖信息更新<br/>game_id draw_url draw_num]
    D11D --> D12

    D10A -->|cSeckill秒杀| D13[componentSeckill处理]
    D13 --> D13A[Seckill::getSeckillTime<br/>活动时间查询]
    D13A --> D13B[秒杀商品状态验证]
    D13B --> D13C[NetGoods::goodsList<br/>秒杀商品查询]
    D13C --> D13D[秒杀库存和价格处理]
    D13D --> D13E[商品排序<br/>有库存优先]
    D13E --> D12

    D10A -->|cLimit限时折扣| D14[componentLimit处理]
    D14 --> D14A[check_activity活动验证<br/>DbLimitDiscount::getOne]
    D14A --> D14B["WHERE id=? AND is_enable=1<br/>AND act_status=2"]
    D14B --> D14C[活动信息添加到商品]
    D14C --> D12

    D10A -->|cCrowdfunding众筹| D15[componentCrowdfunding处理]
    D15 --> D15A[众筹活动和商品验证]
    D15A --> D15B[crowdfundList查询]
    D15B --> D12

    D10A -->|cSuit套餐| D16[componentSuit处理]
    D16 --> D16A[check_activity活动验证]
    D16A --> D16B[BuCheapSuitIndex查询<br/>套餐状态验证]
    D16B --> D16C[NetGoods::suitList<br/>套餐商品查询]
    D16C --> D12

    D10A -->|cCoupon卡券| D17[componentCoupon处理]
    D17 --> D17A[定向人群过滤<br/>range == 2检查]
    D17A --> D17B[NetUser::canGetCards<br/>用户权限验证]
    D17B --> D17C[check_activity<br/>卡券活动验证]
    D17C --> D17D[NetCard::getUserCard<br/>用户卡券状态查询]
    D17D --> D17E[卡券状态更新<br/>is_received available_count]
    D17E --> D12

    D10A -->|cFloatWindow浮窗| D18[componentFloatWindow处理]
    D18 --> D18A[浮窗类型判断<br/>attribute type]
    D18A --> D18B{类型是否为1<br/>单张卡券}
    D18B -->|是| D18C[canGetCards权限验证]
    D18B -->|否| D18D[其他类型处理]
    D18C --> D18E[NetCard::getUserCard<br/>单张卡券查询]
    D18E --> D18F[浮窗状态更新]
    D18F --> D12
    D18D --> D12

    D10A -->|cGoods商品| D19[componentGoods处理]
    D19 --> D19A[解析find_commodity_ids<br/>商品ID列表]
    D19A --> D19B[获取goods_data<br/>商品配置数据]
    D19B --> D19C[NetGoods::goodsList<br/>商品详情查询]
    D19C --> D19D[商品数据合并处理]
    D19D --> D19E[价格标签信息更新]
    D19E --> D19F[分页处理<br/>current_page page_size]
    D19F --> D12

    D10A -->|cCarousel轮播| D20[componentCCarousel处理]
    D20 --> D20A[解析act_code参数]
    D20A --> D20B[遍历imgs查找匹配项]
    D20B --> D20C[提取couponList<br/>卡券ID数组]
    D20C --> D20D[cardDraw卡券抽奖处理]
    D20D --> D12

    D10A -->|cAdvertising广告| D21[componentCAdvertising处理]
    D21 --> D21A[根据act_code匹配广告]
    D21A --> D21B[提取couponList<br/>卡券ID数组]
    D21B --> D21C[cardDraw卡券抽奖处理]
    D21C --> D12

    D12[组件处理完成] --> D22[返回处理后的组件数据]
    D7 --> D23[返回JSON响应<br/>setResponseData]
    D9 --> D23
    D22 --> D23

    %% 样式定义
    classDef dbQuery fill:#ffcdd2,stroke:#d32f2f,stroke-width:2px
    classDef heavyProcess fill:#ff5722,stroke:#bf360c,stroke-width:3px
    classDef cacheHit fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    classDef apiCall fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef startEnd fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef componentProcess fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px

    %% 应用样式
    class A,C10,D23 startEnd
    class C3E,C8B,C8F5,D11A,D13A,D14B,D16B,D17C,D17D,D19C dbQuery
    class C8F3,D10,D13C,D16C,D19C heavyProcess
    class C3C cacheHit
    class C8F1,D11C apiCall
    class C8,D10A componentProcess
