<?php /** @noinspection ForgottenDebugOutputInspection */

/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 2017/6/8
 * Time: 下午5:31
 */

namespace app\index_v2\controller;

use api\jd_sdk\JdOrderN;
use api\lianyou\Ccs;
use api\wechat\AccessToken;
use api\wechat\Carer;
use api\wechat\CarerNew;
use api\wechat\Clue;
use api\wechat\CustomMessage;
use api\wechat\JsSdk;
use api\wechat\Card;
use api\wechat\Mail;
use api\wechat\Menu;
use api\wechat\QrCode;
use api\wechat\Ticket;
use api\wechat\UserManage;
use app\active\controller\NewCard;
use app\common\fuli_service\UserOrder;
use app\common\model\act\AcBlacklist;
use app\common\model\act\AcHaveTradeList;
use app\common\model\act\AcPhonelist;
use app\common\model\act\AcRenewalQrc;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuComponentAuth;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderAct;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuOrderErrList;
use app\common\model\bu\BuOrderJd;
use app\common\model\bu\BuOrderMoreCardPoint;
use app\common\model\bu\BuOrderPoint;
use app\common\model\bu\BuOrderRefund;
use app\common\model\bu\BuOrderSettlement;
use app\common\model\bu\BuPhoneBlacklist;
use app\common\model\bu\BuQyDepartmentAll;
use app\common\model\bu\BuQyUserAll;
use app\common\model\bu\BuToE3sDetail;
use app\common\model\bu\BuToE3sIndex;
use app\common\model\bu\BuTplMsg;
use app\common\model\bu\BuUserBrowsing;
use app\common\model\bu\BuVinListHhr;
use app\common\model\bu\BuWxContrastOpenid;
use app\common\model\db\DbActivityMuchCard;
use app\common\model\db\DbActivity;
use app\common\model\db\DbAdvertisement;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbAfterSalePlatformAddresses;
use app\common\model\db\DbArea;
use app\common\model\db\DbCard;
use app\common\model\db\DbCarSeries;
use app\common\model\db\DbCarUser;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityCard;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommodityJdSku;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommoditySku;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbConsumeRuleRecord;
use app\common\model\db\DbDlr;
use app\common\model\db\DbJdExports;
use app\common\model\db\DbJdGoodsImage;
use app\common\model\db\DbJdSkuInfo;
use app\common\model\db\DbJifenSendLog;
use app\common\model\db\DbJobsLog;
use app\common\model\db\DbLog;
use app\common\model\db\DbLyPayLog;
use app\common\model\db\DbLySettleOrder;
use app\common\model\db\DbLyTransactionOrder;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSeparateAccountRules;
use app\common\model\db\DbSpecialSm;
use app\common\model\db\DbSystemValue;
use app\common\model\db\DbUser;
use app\common\model\db\DbUserCarSeries;
use app\common\model\db\DbUserDrawRecord;
use app\common\model\db\DbVinRule;
use app\common\model\e3s\E3sCarSeries;
use app\common\model\e3s\E3sLog;
use app\common\model\e3s\E3sMaintenanceProduct;
use app\common\model\e3s\E3sMaintenanceProductCarSeries;
use app\common\model\e3s\E3sPackage;
use app\common\model\e3s\E3sPackagePart;
use app\common\model\e3s\E3sPartCarSeries;
use app\common\model\e3s\E3sSparePart;
use app\common\model\e3s\E3sSpecificRelationPart;
use app\common\model\inter\IRequestLog;
use app\common\model\inter\IResponesLog;
use app\common\model\jd\JdMessage;
use app\common\model\jd\JdOrder;
use app\common\model\jd\JdOrderGoods;
use app\common\model\pss\PssInventoryInout;
use app\common\model\sys\SysAdmin;
use app\common\model\sys\SysLogModule;
use app\common\model\zz\ZzLog;
use app\common\net_service\Common;
use app\common\net_service\Inventory;
use app\common\net_service\LyPay;
use app\common\net_service\MaintainService;
use app\common\net_service\NetCard;
use app\common\net_service\NetCardKafKa;
use app\common\net_service\NetOrder;
use app\common\net_service\NetSupplier;
use app\common\net_service\NetUser;
use app\common\net_service\PaymentSystem;
use app\common\port\connectors\Crm;
use app\common\port\connectors\Bdp;
use app\common\port\connectors\E3s;
use app\common\port\connectors\E3spRefactor;
use app\common\port\connectors\E4s;
use app\common\port\connectors\Idm;
use app\common\port\connectors\JdCloudWarehouse;
use app\common\port\connectors\LyE3sDlr;
use app\common\port\connectors\MarketBase as MB;
use app\common\port\connectors\Member;
use app\common\port\connectors\Payment;
use app\common\port\connectors\PzPoint;
use app\common\port\connectors\QuickWin;
use app\common\service\CarIndexService;
use app\common\service\CommodityService;
use app\net_small\command\OrderSettle;
use think\Cache;
use think\cache\driver\Redis;
use think\Config;
use think\Controller;
use think\Db;
use think\Exception;
use think\Env;
use think\Hook;
use think\Log;
use think\Queue;
use think\Model;
use tool\Curl;
use tool\Logger;
use tool\OssUploadFile;
use function GuzzleHttp\Psr7\build_query;

class Lzx extends Controller
{
    public function __construct()
    {
        parent::__construct();
        $user = input('user_token');
        $data = input('user_data', '');
        if ($user != 'lzx123' || $data <> date('md')) {
            die('未经授权，不允许访问');
        }
    }

    public function changeCsi()
    {
//        $car = new Carer();
//        $res = $car->changCsi();
//        Logger::error('lzx-changecsi--'.$res);
//        echo $res;
    }

    protected function bytesToHuman($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function showErrorLogs()
    {
        if (!Env::get('app_debug', Config::get('app_debug'))) {
            return "非调试模式，无法打开工具！";
        }
        $log_dir      = RUNTIME_PATH . "log/system/";
        $arr_files    = array_diff(scandir($log_dir), array('.', '..'));
        $current_file = input('file');

        $log_files = [];

        usort($arr_files, function ($a, $b) use ($log_dir) {
            $file_a = $log_dir . '/' . $a;
            $file_b = $log_dir . '/' . $b;
            return filemtime($file_b) - filemtime($file_a);
        });
        foreach ($arr_files as $file) {
            if (is_file($log_dir . $file) && strpos($file, "_error.log") !== false) {
                $log_files[] = $file;
                if (empty($current_file)) {
                    $current_file = $file;
                }
            }
        }

        $file_contents = [];
        $file_size     = 0;
        if ($current_file && is_file($log_dir . $current_file)) {
            $file_size = $this->bytesToHuman(filesize($log_dir . $current_file));
            $contents  = file_get_contents($log_dir . $current_file);
            $json_arr  = explode("\n", $contents);
            $json_arr  = array_filter(array_reverse($json_arr));
            foreach ($json_arr as $item) {
                $arr = json_decode($item, true);
                if (strpos($current_file, "_error.log") !== false) {
                    $mssages        = array_filter($arr, function ($key) {
                        return !in_array($key, ['type', 'server', 'time']);
                    }, ARRAY_FILTER_USE_KEY);
                    $arr['message'] = current($mssages);
                } else {
                    if (!empty($arr['normal'])) {
                        continue;
                    }
                    $arr['server'] = $arr['var'] ?? '';
                }
                $file_contents[] = $arr;
            }
        }

        include THINK_PATH . 'tpl/logs_viewer.tpl';
    }

    public function showTracePage()
    {
        $trace_id   = input('trace_id');
        $trace_dir  = RUNTIME_PATH . "trace" . DS;
        $traceFile  = $trace_dir . $trace_id;
        $trace_info = '';
        if (!empty($trace_id) && is_file($traceFile)) {
            $trace_info = file_get_contents($traceFile);
        }
        $html      = <<<EOT
<html><head>
<link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.2.0/css/bootstrap.min.css" rel="stylesheet">
<style>.list-group-item.active a {color: white;}</style>
</head><body><div style="margin: 10px;">
EOT;
        $arr_files = array_diff(scandir($trace_dir), array('.', '..'));
        usort($arr_files, function ($a, $b) use ($trace_dir) { // 使用 usort 函数对文件进行排序
            $file_a = $trace_dir . '/' . $a;
            $file_b = $trace_dir . '/' . $b;
            return filemtime($file_b) - filemtime($file_a); // 通过比较文件修改时间进行排序，修改时间较新的在前面
        });
        foreach ($arr_files as $index_key => $file) {
            if (is_file($trace_dir . $file) && $file != ".gitignore" && $index_key < 2500) {
                $ctime    = date("Y-m-d H:i:s", filectime($trace_dir . $file));
                $param    = json_decode(urldecode($file), true);
                $index_id = md5($file);
                $url      = url('index_v2/lzx/showTracePage', ['user_data' => date('md'), 'user_token' => 'lzx123', 'trace_id' => urlencode($file)]) . "#" . $index_id;
                $color    = '#777';
                if ($param['runtime'] > 1) {
                    $color = 'red';
                } else if ($param['runtime'] > 0.5) {
                    $color = "#C7C700";
                }
                $member_color_val = substr($param['member_id'], -6, 6);
                $idx              = sprintf("%'04d", $index_key + 1);
                $active           = $trace_id == $file ? 'active' : '';
                $html             .= <<<EOT
                <li class="list-group-item {$active}" id="{$index_id}"><span class="badge" style="float: left; margin-right: 10px; background-color: #a09f9f">$idx</span><span class="badge" style="background-color: {$color}">{$param['runtime']}</span><a href="{$url}">$ctime - {$param['path_info']} </a> <span class="badge" style="margin-right:25px; background-color: #{$member_color_val}">{$param['member_id']}</span> <span class="badge" style="background-color: rgb(170,3,3)">{$param['channel_sm']}</span> </li>
EOT;
            } else if (is_file($trace_dir . $file) && $index_key >= 2500) {
                unlink($trace_dir . $file);
            }
        }
        $html .= "</ul></div>{$trace_info} </body></html>";
        return response($html);
    }


    public function repairPush()
    {
        set_time_limit(0);
        $car    = new Carer();
        $b_date = input('b_date', '');
        $e_date = input('e_date', '');
        $res    = $car->repairPush(['b_date' => $b_date, 'e_date' => $e_date]);
        Logger::error('维修点评条数--' . $res);
        echo $res;
    }

    public function newCarPush()
    {
        set_time_limit(0);
        $car    = new Carer();
        $b_date = input('b_date', '');
        $e_date = input('e_date', '');
        $res    = $car->newCarPush(['b_date' => $b_date, 'e_date' => $e_date]);
        Logger::error('新车点评条数--' . $res);
        echo $res;
    }

    public function redis_to_sql()
    {
        set_time_limit(0);
        ini_set('memory_limit', '800M');
        $time1      = time();
        $redis      = Cache::redisHandler();
        $redis_name = "wxstore_browing_all";
        $max        = $redis->Llen($redis_name);
//        var_dump($max);die();
        $times = (ceil($max / 800));
        for ($i = 0; $i < $times; $i++) {
            $count = 1;
            $arr   = [];
            while ($count <= 800) {
                $log_info = $redis->Rpop($redis_name);
                if (!$log_info) {
                    break;
                }
                $arr[] = json_decode($log_info, true);
                $count++;
            }
            $brow_model = new BuUserBrowsing();
            $res        = $brow_model->insertAll($arr);

            Logger::error("插入访问数据条数--" . $res . '**时间:' . (time() - $time1));
//            echo ("插入访问数据条数--".$res.'**时间:'.(time()-$time1)."</br>");
            if (!$res) {
                echo "插入出错--" . json_encode($arr) . "</br>";
                foreach ($arr as $vv) {
                    $redis->Lpush($redis_name, json_encode($vv));
                }
            }
        }
    }

    //访问的opend，user_id，url插入数据库
    public function redis_all_url_to_sql()
    {
        set_time_limit(0);
        ini_set('memory_limit', '800M');
        $time1      = time();
        $redis      = Cache::redisHandler();
        $redis_name = "wxstore_user_url_browing_all";
        $max        = $redis->Llen($redis_name);
        $times      = (ceil($max / 2000));
        for ($i = 0; $i < $times; $i++) {
            $count = 1;
            $arr   = [];
            while ($count <= 2000) {
                $log_info = $redis->Rpop($redis_name);
                if (!$log_info) {
                    break;
                }
                $arr[] = json_decode($log_info, true);
                $count++;
            }
            $brow_model = new ZzLog();
            $res        = $brow_model->insertAll($arr);

            Logger::error("插入url访问条数--" . $res . '**时间:' . (time() - $time1));
            echo("插入访问数据条数--" . $res . '**时间:' . (time() - $time1) . "</br>");
            if (!$res) {
                echo "插入出错--" . json_encode($arr) . "</br>";
                foreach ($arr as $vv) {
                    $redis->Lpush($redis_name, json_encode($vv));
                }
            }
        }
    }


    public function test_url()
    {
        echo request()->url();
    }

    public function str()
    {
        echo $this->_createNonceStr(43);
    }

    private function _createNonceStr($length = 20)
    {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        $str   = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

    public function clear_dlr_openid()
    {
        $dlr_code = input('dlr_code');
        session($dlr_code . '-openid', NULL);
    }

    public function dlr_openid()
    {
        $dlr_code = input('dlr_code');
        echo session($dlr_code . '-openid');
    }

    public function test_order_b()
    {
        $order_model = new BuOrder();
        $order_no    = input('get.order_no');
        $where       = ['order_code' => $order_no];
        $order       = $order_model->getOne(array('where' => $where));
        $Car         = new Carer();
        $log_model   = new DbLog();
        $b_money     = $order['money'];
        if ($order['gift_score']) {
            $point = $Car->point(['card_no' => $order['ic_card_no']]);
            if ($point) {
                if ($point['pv_point'] >= $order['gift_score']) {
                    $b_point = $order['gift_score'];
                } else {
                    $b_point = $point['pv_point'];
                }
                $data      = array(
                    'vin'        => $order['vin'],
                    'ic_card_no' => $order['ic_card_no'],
                    'order_id'   => $order['order_code'] . '-b',
                    'dlr_code'   => 'PV',
                    'deal_man'   => 'PV',//处理人
                    'point'      => $b_point,
                    'dlr_point'  => 0,
                );
                $point_res = $Car->deductPoint($data);
                if (!$point_res) {
                    print_json(1, '扣返回积分失败,请重新再试');
                    die();
                }
                $log_data = array(
                    'type'         => 'integral-b',
                    'send_note'    => json_encode_cn($data),
                    'receive_note' => json_encode_cn($point_res),
                );
                if ($point_res) {
                    $log_data['is_success'] = 'success';
//                    Logger::error('pay Notify deductPoint fail-',$data,'deductPointError.log');
                } else {
                    $log_data['is_success'] = 'fail';
//                    Logger::error('pay Notify deductPoint ok-',array('r'=>$point_res,'data'=>$data),'deductPointOK.log');
                }
                $log_model->insertData($log_data);
            } else {
                $b_point = 0;
            }
            $b_money = $order['money'] - ($order['gift_score'] - $b_point) / 10;
            var_dump($b_money);
        }

    }


    public function get_openid()
    {
        $appid      = config('appid');
        $dlr_code   = input('dlr_code');
        $big_openid = session($appid . '-openid');
        if ($big_openid) {
            $contrast_model = new BuWxContrastOpenid();
            $where          = array('nissan_openid' => $big_openid, 'dlr_code' => $dlr_code, 'is_enable' => 1);
            $res            = $contrast_model->getOne(array('where' => $where));
            if ($res) {
                session($dlr_code . '-openid', $res['openid']);
                echo "总部OPENID--" . $big_openid . "/专营店openid--" . $res['openid'];
            } else {
                $backurl  = urlencode(request()->url(true));
                $oauthUrl = url('wechat/Oauth/normal_new', ['dlr_code' => $dlr_code, 'backurl' => $backurl], false, true);
                $this->redirect($oauthUrl);
            }
        } else {
            $backurl  = urlencode(request()->url(true));
            $oauthUrl = url('wechat/Oauth/normal_new', ['dlr_code' => $dlr_code, 'backurl' => $backurl], false, true);

            $this->redirect($oauthUrl);
        }
    }

    public function sub()
    {
        $re    = "qrscene_20000";
        $reres = "optouid-oSET1jjD8ujhPUkDqasvlq2_Wamcinfo";
        echo strlen($re);
        echo "<br/>";
        echo strpos($re, '_');
        echo "<br/>";
        $res     = substr($re, strpos($re, '_') + 1, strlen($re) - strpos($re, '_'));
        $resress = substr($reres, 8, 28);
        echo $resress;
        echo "<br/>";
        echo $res;
    }

    public function sku()
    {
        $sku_id       = input('sku_id');
        $dlr_code     = input('dlr_code');
        $order_source = input('order_source', 1);
        $com          = new CommodityService();
        $sku          = $com->getOneSku($sku_id, $order_source, $dlr_code);
        var_dump($sku);
    }

    public function openid()
    {
        echo $this->openid();
    }

    public function card()
    {
        $dlr_code = input('dlr_code');
        $card_id  = input('card_id');
        $jsSdk    = JsSdk::get($dlr_code, true);
        $card     = JsSdk::getCard($dlr_code, array(array('cardid' => $card_id)));
        var_dump($card);
        die();
        $this->assign('card', $card);
        $this->assign('jsSdk', $jsSdk);
        return $this->fetch('lzx/card');
    }

    public function getToken()
    {
        //test==2019-04-14 18:08:23
        $dlr = input('dlr_code');

        $ref = input('ref');
        if ($ref == 1) {
            $ref = true;
        } else {
            $ref = false;
        }
        $token = get_access_token_by_dlr($dlr, $ref);

//        var_dump($token);
        echo($token);
        echo "<br/>";
        echo(AccessToken::get($dlr));
        $redis_name = input("redis");
        var_dump(redis($redis_name));
    }

    public function test_nn()
    {
        $res = floor(50 / 50) * 1 * 10;
        var_dump($res);
    }

    public function token_fail()
    {
        $dlr    = input('appid');
        $r_name = "get_wx_ac_token-fail-" . $dlr;
        var_dump(redis($r_name));
    }

    public function test_redis_name()
    {
        var_dump(redis(input('redis')));
    }

    public function getTokenAll()
    {
        $redis_name = "get_wx_ac_token-fail-";
        $au_model   = new BuComponentAuth();
        $where      = ['original_id' => ['exp', 'is not null']];
        $auth       = $au_model->getList(['where' => $where]);
        foreach ($auth as $v) {
            $redis = redis($redis_name . $v['auth_appid']);
            echo $v['dlr_code'] . '---';
            var_dump($redis);
            echo "<br>";
        }
    }

    public function get_menu()
    {
        $dlr_code = input('get.dlr_code');
        $menu     = new Menu();
        $menu_s   = $menu::get($dlr_code);
        echo json_encode_cn($menu_s);
    }

    public function get_menu_s()
    {
        $dlr_code = input('get.dlr_code');
        $menu     = new Menu();
        $menu_s   = $menu::get_s($dlr_code);
        echo json_encode_cn($menu_s);
    }


    public function c_menu()
    {
        $dlr_code = input('get.dlr_code');
        $post     = file_get_contents("php://input");
//        $post = input('data');
        $data = json_decode($post, true);
//        var_dump($data);die();
        $menu   = new Menu();
        $menu_s = $menu::create($data, $dlr_code);
        var_dump($menu_s);
    }

    public function test_go_url_re()
    {
        echo sprintf("<script src=\"https://s.chebaba.com/ext/js/plugin/jquery/1.11.2/jquery.min.js\"></script>");
        echo "<script src=\"https://s.chebaba.com/ext/js/lib/dndc/2.0.4/dndc.js\"></script>
    <script>
      $(function(){
        dndc.hrefTraceBind();
      })
    </script>";
        echo sprintf("<a href='%s'>走你</a>", url('url_re'));
    }

    public function url_re()
    {
        echo sprintf("<script src=\"https://s.chebaba.com/ext/js/plugin/jquery/1.11.2/jquery.min.js\"></script>");
        echo "<script src=\"https://s.chebaba.com/ext/js/lib/dndc/2.0.4/dndc.js\"></script>
    <script>
      $(function(){
        dndc.hrefTraceBind();
      })
    </script>";
        $res  = $_SERVER;
        $res1 = $this->request->request();
        if (!isset($_SERVER['HTTP_REFERER'])) {
            var_dump('错误链接无来源');
        } else {
            if (!strstr($_SERVER['HTTP_REFERER'], 'wxstore.chebaba.com')) {
                var_dump('错误链接');
            } else {
                var_dump('正确链接');
            }
        }


        var_dump($res);
        var_dump($res1);

    }

    //核销专营店卡券
    public function c_card()
    {
        $dlr_code = input('dlr_code');
        $card_id  = input('card_id', '');
        $card     = new Card($dlr_code);
        $code     = input('code');

        $res = $card->consume($code, $card_id);
        var_dump($res);
    }

    //核销总部卡券，用本地接口
    public function c_card_nissan()
    {
        $card    = new Card("NISSAN");
        $code    = input('code');
        $card_id = input('card_id', '');

        $res = $card->consume($code, $card_id);
        var_dump($res);
    }

    public function user_get_card()
    {
        $card          = new Card("nissan");
        $openid        = input('openid');
        $card_id       = input('card_id');
        $user_get_card = $card::userGetCard(['openid' => $openid, 'card_id' => $card_id]);
        var_dump($user_get_card);
    }

    //核销总部卡券
    public function c_card_old()
    {
        $card        = new Card();
        $code        = input('code');
        $dlr_code    = input('dlr_code');
        $_params     = array(
            'code' => $code,
            'sid'  => $dlr_code,
        );
        $consume_res = $card::consumeCard($_params);

        var_dump($consume_res);
    }

    public function q_card()
    {
        $dlr_code = input('dlr_code');
        $card     = new Card($dlr_code);
        $code     = input('code');
        $res      = $card->query($code);
        var_dump($res);
    }

    public function card_detail()
    {
        $dlr_code = input('dlr_code');
        $card     = new Card($dlr_code);
        $id       = input('id');
        $res      = $card->detail($id);
        var_dump($res);
    }

    public function g_card_list()
    {
        $dlr_code = input('dlr_code');
        $card     = new Card($dlr_code);
        $res      = $card->cardList();
        var_dump($res);
        var_dump($card::getError());
    }

    public function repair()
    {
        $repair_time = '2017-09-15 14:35:00';
        $dlr_code    = 'D2306';
        $clue        = new Clue();
        $res         = $clue->repair(['repair_time' => $repair_time, 'dlr_code' => $dlr_code]);
        var_dump($res);
    }

    public function testsql()
    {
        //        $card_id=235;
        $openid = 'oSET1jms82I6HPP4gWc3bSHIZBgQ';
//        $dlr_openid = 'oSET1jms82I6HPP4gWc3bSHIZBgQ';
//        $where = ['card_id'=>$card_id,'card_code'=>['exp','is NULL'],'card_status'=>1];
//        $where[]=['exp',sprintf(" openid='%s' or dlr_openid='%s'",$openid,$dlr_openid)];
////        $where=['card_code'=>['exp','is NULL'],'is_enable'=>1];
//        $where[]= ['exp'," 1=1 or openid='oYwsZv0PtBaNqQyLH-fmhdYVnwLY'"];
        $where                   = array('openid' => $openid, 'is_enable' => 1);
        $where['logistics_mode'] = 2;//快递
//        $where['order_status'] = 4;//已支付，已发货
        $where[] = ['exp', sprintf("order_status=4 or ( order_status=2 and  order_source!=2)")];

        $params      = array(
            'where' => $where,
            'field' => "*",
            'order' => "created_date desc",
            //            'limit' => sprintf("%s,%s", ($page - 1) * $pageSize, $pageSize),
        );
        $order_model = new BuOrder();

        $orderList = $order_model->getList($params);
        echo $order_model->getLastSql();


//        $card = new BuCardReceiveRecord();
//        $card->getOne(['where'=>$where]);
//        echo $card->getLastSql();
    }

    public function upload()
    {
        $file_name  = "img/headicon.jpg";
        $aliyun_oss = new OssUploadFile();
        $os_res     = $aliyun_oss::upload($file_name);
        var_dump($os_res);
    }

    public function wx_info()
    {
        $openid   = input('openid');
        $dlr_code = input('dlr_code');
        $user     = new UserManage();
        $res      = $user::getUserInfo($openid, $dlr_code);
        var_dump($res);
    }

    public function repair_vin()
    {
        $clue = new Clue();
        $vins = "'LGBF1AE0XCR116949','LGBG22E08EY691793'";
        $res  = $clue->repair_vin(['vins' => $vins]);
        var_dump($res);
    }

    public function testurl()
    {
        echo config('PAY_CHEBABA');
    }

    //积分反结算--通过订单号
    public function antibalance()
    {
        /**
         *
         * 2017年09月25日->G2923201709204946143
         */
        $user = input('user');
        if ($user == 'lzx') {
            $order_code  = input('order_code');
            $Car         = new Carer();
            $order_model = new BuOrder();
            $order       = $order_model->getOne(['where' => ['order_code' => $order_code]]);
            $data        = array(
                'openid'     => $order['openid'],
                'ic_card_no' => $order['ic_card_no'],
                'vin'        => $order['vin'],
                'order_id'   => $order['order_code'],
                'dlr_code'   => $order['dlr_code'],
                'deal_man'   => $order['dlr_code'],//处理人
                'brand'      => $order['brand'],//品牌
            );
            $point_res   = $Car->antiBalance($data);
            $log_model   = new DbLog();
            $log_data    = array(
                'type'         => 'antiBalance',
                'modifier'     => '手工退',
                'send_note'    => json_encode_cn($data),
                'receive_note' => json_encode_cn($point_res),
            );
            if ($point_res) {
                $log_data['is_success'] = 'success';
            } else {
                $log_data['is_success'] = 'fail';
            }
            $res = $log_model->insertData($log_data);
            var_dump($point_res);
        }
    }

    //店端开放功能
    public function dlrOpen()
    {
        $car    = new Carer();
        $params =
            [
                'dlr_code'   => '********',
                'open_time'  => '2017-11-02 11:27:46',
                'close_time' => '2027-11-02 19:27:46',
                'is_enable'  => '1'
            ];
        $res    = $car->dlrOpenRecord($params);
        var_dump($res);
    }

    public function dlrOpens()
    {
        $car       = new Carer();
        $dlr_model = new DbDlr();
        $dlrs      = $dlr_model->getList(['where' => ['type' => 2, 'is_enable' => 1]]);
        foreach ($dlrs as $v) {
            echo $v['dlr_code'] . '=====';
            $params =
                [
                    'dlr_code'   => $v['dlr_code'],
                    'open_time'  => '2017-11-02 11:27:46',
                    'close_time' => '2027-11-02 19:27:46',
                    'is_enable'  => '1'
                ];
            $res    = $car->dlrOpenRecord($params);
            var_dump($res);

            echo "<br/>";
        }
    }


    public function get_point()
    {
        $ic_card_no = input('car_no');
        $data       = ['card_no' => $ic_card_no];
        $Car        = new Carer();
        $point      = $Car->point($data);
        echo json_encode_cn($point);
    }

    //积分--反结算
    public function antibalance_order_code()
    {
        /**
         *
         *
         */
        $order_code = "B1116201711127115838";

        $Car        = new Carer();
        $vin        = 'LGBH52E04HY237301';
        $ic_card_no = 'B11162017071717525';
        $data       = array(
            'vin'        => $vin,
            'ic_card_no' => $ic_card_no,
            'order_id'   => $order_code,
            'dlr_code'   => 'B1116',
            'deal_man'   => 'B1116',//处理人
        );
        $point_res  = $Car->antiBalance($data);
        $log_model  = new DbLog();
        $log_data   = array(
            'type'         => 'antiBalance',
            'modifier'     => '手工退',
            'send_note'    => json_encode_cn($data),
            'receive_note' => json_encode_cn($point_res),
        );
        if ($point_res) {
            $log_data['is_success'] = 'success';
        } else {
            $log_data['is_success'] = 'fail';
        }
        $res = $log_model->insertData($log_data);
        var_dump($point_res);
    }

    /**
     * 反结算积分订单编码--openid
     */
    public function antipoint()
    {
        $order_code = 'TEST201903130019988';
        $openid     = 'oSET1jrNHpbH974sX99v5sHJiuoE';

        $Car       = new Carer();
        $data      = array(
            'openid'   => $openid,
            'order_id' => $order_code,
            'dlr_code' => 'T9932',
            'deal_man' => 'T9932',//处理人
        );
        $point_res = $Car->antiBalance($data);
        var_dump($point_res);
    }

    //积分抵扣
    public function deductPoint()
    {
        $openid     = 'oSET1jrNHpbH974sX99v5sHJiuoE';
        $vin        = '';
        $ic_card_no = 'D23019394516';
        $point      = 100;
        $order_code = $this->_createNonceStr(20);
        $order_code = 'TEST201903130019988';
//        echo $order_code;
        $data = array(
            'openid'    => $openid,
            //            'ic_card_no'=>$ic_card_no,
            'order_id'  => $order_code,
            'dlr_code'  => 'T9932',
            'deal_man'  => 'T9932',//处理人
            'point'     => 1,
            'dlr_point' => 0,
        );
//        $data = array(
//            'vin'=>$vin,
//            'ic_card_no'=>$ic_card_no,
//            'order_id'=>$order_code,
//            'dlr_code'=>'D2301',
//            'deal_man'=>'D2301',//处理人
//            'point'=>0,
//            'dlr_point'=>$point,
//        );

//        $ex_data = [
//            'dlr_code'=>'H2901',
//            'server_code'=>$order_code,
//            'use_point'=>12,
//            'bill_time'=>date('Y-m-d H:i:s'),
//            'remark'=>'微信售后支付',
//        ];
        $Car = new Carer();
//        $ex_res = $Car->dlrExchangeRecord($ex_data);
//        var_dump($ex_res);
        $point_res = $Car->deductPoint($data);
        var_dump($point_res);
    }


    public function sendMail()
    {
        $title   = "老友惠保养套餐新订单提醒！";
        $content = "<p style='color: red;font-size: 22px; font-weight: 600;'>该邮件为系统自动发送，请勿回复。谢谢！</p><br/>";
        $content .= "您好！<br/>";
        $content .= "客户在东风日产车主俱乐部会员商城上，已预定老友惠保养套餐，请及时与客户沟通，并按如下要求做好客户服务：<br/>";
        $content .= "一、订单信息：(未绑定车主不显示车牌号和VIN码)<br/>";

        $content   .= " <br/>";
        $content   .= "二、客户服务要求：<br/>";
        $content   .= "1、与客户电话确认回厂时间，并告知到店后联系人，客户回厂后按预约客户优先接待；<br/>";
        $content   .= "2、请确认贵店库存，提前为该客户预留库存（含订货补货）；<br/>";
        $content   .= "3、客户回店购买套餐时，与客户签订套餐合同，并说明套餐有效期等事项；<br/>";
        $content   .= " <br/>";
        $content   .= "三、售后处理：<br/>";
        $content   .= "1、客户购买套餐后，请做好后续定保回厂提醒邀约；<br/>";
        $content   .= " <br/>";
        $content   .= "四、联系窗口<br/>";
        $content   .= "在线订单购买核销等疑问，请联系：车巴巴平台--张晓莉：<EMAIL><br/>";
        $content   .= sprintf("<img src='%s' width='220px'>", "http://wxstore.chebaba.com/public/static/active/bao_yang_price/images/baoyou_qrcode.jpg");
        $mail_type = 3;

//        require_once VENDOR_PATH."/PHPMailer/Exception.php";
//        require_once VENDOR_PATH."/PHPMailer/PHPMailer.php";
//        require_once VENDOR_PATH."/PHPMailer/SMTP.php";
//        $mail = new PHPMailer(true);
//        $mail->setLanguage('zh_cn');
//        $mail->set('CharSet', 'UTF-8');
//        //$mail->SMTPDebug = 2;
//        $mail->isSMTP();
//        $mail->set('Port', 25);
//        $mail->set('SMTPAuth', true);
//        $mail->set('Username', '<EMAIL>');
//        $mail->set('Password', 'li@123');
//        $mail->set('Host', 'mail-hd.dfl.com.cn');
//        $mail->setFrom('<EMAIL>', '测试邮箱');
//        $mail->addAddress('<EMAIL>','LIANGYIJIAN');
//
//        $mail->set('Subject', '测试您有新的订单');
//        $mail->set('Body', $content);
////        $mail->setWordWrap(); // 设置每行字符串的    长度
//        $res =  $mail->send();


        $data = ['address' => '', 'address2' => '<EMAIL>', 'content' => $content, 'title' => '客诉提醒', 'type' => 3];
        $mail = new Mail();
        $res  = $mail->sendMail($data);

        var_dump($res);
    }

    public function send_mail()
    {
        $data = [
            'type'     => 2,
            'address'  => '<EMAIL>',
            'address2' => '',
            'content'  => "test : 大屎忽,你得屎忽点解甘大噶？
<p style='color: red;font-size: 38px; font-weight: 800;'>请及时联系客户解决，并上集团CRM平台填写跟进记录。</p>\n",
            'title'    => '专营店备件部（同步抄送售后经理、SA、财务部，请相互转达）'
        ];
        $mail = new Mail();
        $res  = $mail->sendMail($data);
        var_dump($res);
    }

    public function getCarer()
    {
        //        $carer = redis($car_cash_name);
        $Car           = new  Carer();
        $openid        = input('openid');
        $car_cash_name = 'USER-INFO-OPENID' . $openid;
//        $carer = redis($car_cash_name);
        $carer = $Car->vin(array('openid' => $openid));
        if ($carer) {
            $carer['card_class'] = '';
            if (strpos($carer['card_degree_name'], '金卡') != false || $carer['card_degree_name'] == '员工卡') {
                $carer['card_class'] = 'gold';
            } elseif (strpos($carer['card_degree_name'], '银卡') != false) {
                $carer['card_class'] = 'silver';
            } elseif (strpos($carer['card_degree_name'], '铂金') != false) {
                $carer['card_class'] = 'platinum';
            }
            if (isset($carer['ic_card_no'])) {
                $point = $Car->point(array('card_no' => $carer['ic_card_no']));//$car_info['ic_card_no']
                var_dump($point);
//                if ($point) {
//                    $carer['point'] = $point['point'];
//                }
            }
        }
        var_dump($carer);

    }


    public function order_err()
    {
        $order_model = new BuOrder();
        $where       = ['pay_order_code' => ['exp', 'is not null']];
        $list        = $order_model->getList(['where' => $where]);
        foreach ($list as $v) {
            if (substr($v['order_code'], 0, 5) != substr($v['pay_order_code'], 0, 5)) {
                echo $v['order_code'] . '=====' . $v['pay_order_code'];
                echo "<br/>";
            }
        }
    }

    public function h_c_card()
    {
        $where      = sprintf(" `card_id` in (258,259) and source=1 and act_id=0 and card_code is not null");
        $card_model = new BuCardReceiveRecord();
        $list       = $card_model->getList(['where' => $where]);
        $card       = new Card();
        foreach ($list as $v) {
            $_params     = array(
                'code' => $v['card_code'],
                'sid'  => 'H2906',
            );
            $consume_res = $card::consumeCard($_params);
            var_dump($consume_res);
            echo "<bn/>";
        }
    }

    public function c_qrc()
    {
        $dlr_code = input('dlr_code');
        $qrc_id   = input('id');
        $QRC      = new QrCode();
//        $qrc_id=20001;
        $qr_code = $QRC::createQrCodeLimit($dlr_code, $qrc_id);
        echo sprintf("<img src='%s'>", $qr_code['qrcode']);
        var_dump($qr_code);
    }

    public function c_qrc_tmp()
    {
        $dlr_code = input('dlr_code');
        $qrc_id   = input('id');
        $QRC      = new QrCode();
//        $qrc_id=20001;
        $qr_code = $QRC::createQrCodeTemp($dlr_code, $qrc_id);
        var_dump($QRC::getError());
        echo sprintf("<img src='%s'>", $qr_code['qrcode']);
        var_dump($qr_code);
    }


    public function test_proc()
    {
        $data = [
            'title'     => '测试标题2',
            'url'       => urlencode('http://wxstore.chebaba.com/index_v3/transfer/jump?dlr_code=NISSAN&gourl=http%3a%2f%2fwxstore.chebaba.com%2factive%2fnew_car%2findex%3fjp_type%3d2%26source%3d20'),
            //            'id'=>'86',//修改，删除时必传
            'pic'       => 'pic测试3',
            'edit_type' => '1',//1新增，2编辑,3删除
            'desc'      => 'desc测试',
            'type'      => '4',//4新车扫码,5回厂扫码,6新车点评,7回厂点评
            'dlr_code'  => 'H2906',
            'ac_title'  => 'H2906333444dfddggg',
            'pro_type'  => '1',//'1图文，2文本'
            'content'   => urlencode("<a href=\"http://wxstore.chebaba.com/index_v3/transfer/jump?dlr_code=NISSAN&gourl=http%3a%2f%2fwxstore.chebaba.com%2factive%2fnew_car%2findex%3fjp_type%3d2%26source%3d20\">3.点这里，送你288元新车主红包</a>"),//文本内容
        ];
        $clue = new Clue();
        $res  = $clue->proc($data);
        var_dump($res);
    }

    public function proc_list()
    {
        $data = ['p_size' => 5];
        $clue = new Clue();
        $res  = $clue->proc_list($data);
        var_dump($res);
    }

    public function testa()
    {
        return $this->fetch('lzx/test');
    }

    public function arr()
    {
        $news  = array(
            array(
                'title'       => '1【代金券】20.00-100.00元积分壕礼等你领！',
                'description' => '【代金券】20.00-100.00元积分壕礼等你领！',
                'picurl'      => 'huodong/Public/pointGift.jpg',
                'url'         => 'http://www.chebaba.com/wx/web/www/index.php?a=RecommendActivity&m=indexTo&channel=type9',
            ),
            array(
                'title'       => '2【代金券】20.00-100.00元积分壕礼等你领！',
                'description' => '【代金券】20.00-100.00元积分壕礼等你领！',
                'picurl'      => 'huodong/Public/pointGift.jpg',
                'url'         => 'http://www.chebaba.com/wx/web/www/index.php?a=RecommendActivity&m=indexTo&channel=type9',
            )
        );
        $news1 = [
            [
                'title'       => '3【代金券】20.00-100.00元积分壕礼等你领！',
                'description' => '【代金券】20.00-100.00元积分壕礼等你领！',
                'picurl'      => 'huodong/Public/pointGift.jpg',
                'url'         => 'http://www.chebaba.com/wx/web/www/index.php?a=RecommendActivity&m=indexTo&channel=type9',
            ]
        ];

//        var_dump($news);
//        echo "<br/>";
        $news1 = array_merge($news1, $news);
        var_dump($news1);
    }

    public function test_error()
    {
        $inp = input("get.");

        var_dump($inp[0]['error']);
    }

    public function test_gourl()
    {
        $backurl = input('url');
        $go_url  = urldecode($backurl);
        echo $go_url;
    }

    public function testsku()
    {
        $com = new CommodityService();
        $res = $com->getOneSku(4053, 1, 'TEST2901', '');
        echo $res['divided_price'];
    }

    public function testdlr()
    {
        $dlr_model = new DbDlr();
        $dlrs      = $dlr_model->getList(['where' => ['is_enable' => 1], 'field' => 'area_id', 'group' => 'area_id']);
        $are       = '';
        foreach ($dlrs as $v) {
            $are .= $v['area_id'] . ',';
        }

        $area_model = new DbArea();
        $areas      = $area_model->getList(['where' => ['area_parent_id' => 0], 'field' => "area_name name,CONCAT(area_id,'') code"]);
        foreach ($areas as $k => $v) {
            $city = $area_model->getList(['where' => ['area_parent_id' => $v['code'], 'area_id' => ['in', trim($are, ',')]], 'field' => "area_name name,CONCAT(area_id,'') code"]);
            if ($city) {
                $areas[$k]['sub'] = $city;
            } else {
                unset($areas[$k]);
            }
        }
        $areas = array_values($areas);
        echo json_encode_cn($areas);
    }

    public function testredis()
    {
        $dlr_code   = input('dlr_code');
        $redis_name = "user-pay-finish-" . $dlr_code;
        $qr_code    = redis($redis_name);
        var_dump($qr_code);
    }

    public function card_s()
    {
        $card_r_model = new BuCardReceiveRecord();
        $where        = ['created_date' => ['>=', '2018-01-30 14:38:04'], 'card_code' => ['exp', "is null"]];
        $card_r       = $card_r_model->getList(['where' => $where, 'limit' => "500"]);
        $card_model   = new DbCard();
        $card_api     = new Card();
        foreach ($card_r as $v) {
            $card = $card_model->getOneByPk($v['card_id']);
            if ($card) {
                if ($card['dlr_code'] == "NISSAN") {
                    $user_get_card = $card_api::userGetCard(['openid' => $v['openid'], 'card_id' => $card['card_id']]);
                    if ($user_get_card) {
                        if ($user_get_card['state'] == 0) {
                            $data = ['card_code' => $user_get_card['code'], 'last_updated_date' => date('Y-m-d H:i:s'), 'status' => 1];
                            $res  = $card_r_model->saveData($data, ['id' => $v['id']]);
                            echo $res;
                        } else {
                            $data = ['card_code' => $user_get_card['code'], 'last_updated_date' => date('Y-m-d H:i:s'), 'status' => 3, 'modifier' => '数据修复'];
                            $res  = $card_r_model->saveData($data, ['id' => $v['id']]);
                            echo $res;
                        }
                        echo "<br/>";
                    }
                }
            } else {
                echo "Meiyou--";
            }
        }
        var_dump(count($card_r));
    }

    //修复卡券数据
    public function card_xf()
    {
        $card_r_model = new BuCardReceiveRecord();
        $where        = ['created_date' => ['>=', '2017-12-30 14:38:04'], 'card_code' => ['exp', "is not null"], 'openid' => ['eq', '']];
        $card_r_l     = $card_r_model->getList(['where' => $where]);
        if ($card_r_l) {
            foreach ($card_r_l as $v) {
                $_where     = ['created_date' => ['>=', '2017-12-30 14:38:04'], 'card_code' => ['exp', "is  null"], 'openid' => $v['dlr_openid'], 'card_id' => $v['card_id']];
                $card_count = $card_r_model->getCount(['where' => $_where]);
                if ($card_count == 1) {
                    $data = ['card_code' => $v['card_code'], 'wx_card_id' => $v['wx_card_id'], 'status' => $v['status'], 'consume_date' => $v['consume_date'], 'dlr_openid' => $v['dlr_openid']];
                    $row  = $card_r_model->saveData($data, $_where);
                    echo $row;
                    if ($row) {
                        $card_r_model->where(['id' => $v['id']])->delete();
                    }
                }
//                echo $card_r_model->getLastSql();
//                echo $card_count;
                echo "<br/>";
            }
        }
    }


    public function test_com_info()
    {
        $com      = new CommodityService();
        $id       = input('id');
        $dlr_code = input('dlr_code');
        $s_type   = input("s_type");
        $suit     = input('sid');
        $goods    = $com->getCommodityInfo($id, $dlr_code, '', '', '', $s_type, $suit);
        var_dump($goods);
    }

    public function test_msg()
    {
        $dlr_code = input('dlr_code', "NISSAN");
        $openid   = input('openid');
        $msg      = new CustomMessage();
        $m_art    = [
            [
                'title'       => " N-club会员抢先看",
                'description' => "点击立即领取210元超值大红包~戳我速抢>>>",
                'url'         => "http://wxstore.chebaba.com/active/special/cards?id=83&dlr_code=GWSC&source=302",
                'picurl'      => "http://wx-dealer.oss-cn-shenzhen.aliyuncs.com/found/20180831/7bd962d605622aa8f24d6d2c42f1f1f3.jpg",
            ]
        ];
        $res      = $msg::news($openid, $m_art, $dlr_code);
        var_dump($res);
    }

    public function test_send_text()
    {
        $dlr_code = input('dlr_code', "NISSAN");
        $openid   = input('openid');
        $msg      = new CustomMessage();
        $content  = sprintf("绑定成功！您可以点击公众号中的菜单体验更多服务，我们还会不定期提供车主福利。\n <a href='http://wxstore.chebaba.com/active/special/cards?id=83&dlr_code=GWSC&source=301'>点击立即领取210元超值大红包~戳我速抢>>>>>></a>");
        $res      = $msg::text($openid, $content, $dlr_code);
        var_dump($msg::getError());
        var_dump($res);
    }

    public function test_s_rene()
    {
        $sql        = "SELECT a.dlr_code,a.user_id,b.dlr_code dlr_code2 from t_ac_renewal_qrc a 
inner JOIN t_bu_qy_user b on a.user_id= b.user_id and a.dlr_code!=b.dlr_code
where a.dlr_code!='' and a.dlr_code!='H9999' and a.dlr_code !=\"TEST2901\" and a.dlr_code !=\"CHEBABA\" and a.dlr_code!='NISSAN'
GROUP BY a.user_id";
        $re_model   = new AcRenewalQrc();
        $card_model = new BuCardReceiveRecord();
        $res        = $re_model->query($sql);
        if ($res) {
            foreach ($res as $v) {
                $re = $re_model->getList(['where' => ['user_id' => $v['user_id']]]);
                if ($re) {
                    //                    echo $v['dlr_code'].'=='.$v['dlr_code2'].'-----';
                    foreach ($re as $vv) {
                        //                        $r_card = $card_model->getList(['where'=>['act_id'=>$vv['id']]]);
//                        var_dump($r_card);
                        $u_card = $card_model->saveData(['dlr_code' => $v['dlr_code2']], ['act_id' => $vv['id']]);

                        var_dump($u_card);
                        echo "<br/>";
                    }
                    $re_model->saveData(['dlr_code' => $v['dlr_code2']], ['user_id' => $v['user_id']]);
                }
            }
        }
    }


    public function test_redis()
    {
        $redis      = Cache::redisHandler();
        $redis_name = "wxstore_user_url_browing_all";

//        $ee = $redis->get('122');
//        var_dump($ee);
//        die();
//        $res = $redis->Lpush('list2',3);
        $rr = $redis->Lrange($redis_name, 0, 100);
        var_dump($rr);
    }


    public function php_info()
    {

//        echo phpinfo();

    }


    public function test_ck()
    {
        $id   = input('id');
        $name = "test_ck";
        setcookie($name, $id);
        $ck = cookie($name);
        var_dump($ck);
    }

    public function cc_tiket_redis()
    {
        $CC = Ticket::jsapiTicket('GWSC', true);
        var_dump($CC);
    }

    public function test_pay()
    {
        $url = "http://4s-store.chebaba.com/dev_dealer/pay_chebaba/pay/index_h5?openid=oiblQs56YQvBsM6F0WBW1NbVOfjI&order_no=H2901201805141812257040&dlr_type=4&dlr_code=H2901&body=%E5%B9%B3%E5%8F%B0%E9%A5%AD%E7%9B%92...&money=4900&sub_app_id=wx56010a93fa662f1b&successUrl=http%3A%2F%2F4s-store.chebaba.com%2Fdev_dealer%2Findex_v2%2Fgoods%2Fpay_finish%3Fid%3D1876&failUrl=http%3A%2F%2F4s-store.chebaba.com%2Fdev_dealer%2Findex_v2%2Fgoods%2Forderlist&order_id_order=H2901201805141811120356";
        echo sprintf("<a href='%s'>点击</a>", $url);
    }


    public function test_inc()
    {
        $sku_id         = 6140;
        $step           = 1;
        $set_sku_model  = new DbCommoditySetSku();
        $set_comm_model = new DbCommoditySet();
        $res            = $set_sku_model->where('id', $sku_id)->setInc('stock', $step);
        $sku_info       = $set_sku_model->getOneByPk($sku_id);
        $where          = ['commodity_id' => $sku_info['commodity_id'], 'set_type' => $sku_info['set_type']];
        if ($sku_info['set_type'] == 2) {
            $where['dlr_code'] = $sku_info['dlr_code'];
        }
        //修改总库存
        $count_res = $set_comm_model->where($where)->setInc('count_stock', $step);
        var_dump($count_res);
    }

    public function test_set_stock()
    {
        $sto    = input('sto');
        $sku_id = input('sku');

//        $set_sku_model = new DbCommoditySetSku();
        $set_comm_model = new DbCommoditySet();
//        $sku_info = $set_sku_model->getOneByPk($sku_id);
        $where = ['commodity_id' => $sku_id];
//        if ($sku_info['set_type'] == 2) {
//            $where['dlr_code'] = $sku_info['dlr_code'];
//        }
        //修改总库存
        $count_res = $set_comm_model->save(['count_stock' => $sto], $where);
//        echo $set_comm_model->getLastSql();
        var_dump($count_res);

    }

    public function test_set_s_num()
    {
        $sto    = input('num');
        $sku_id = input('sku');

//        $set_sku_model = new DbCommoditySetSku();
        $set_comm_model = new DbCommodity();
//        $sku_info = $set_sku_model->getOneByPk($sku_id);
        $where = ['id' => $sku_id];
//        if ($sku_info['set_type'] == 2) {
//            $where['dlr_code'] = $sku_info['dlr_code'];
//        }
        //修改总库存
        $count_res = $set_comm_model->save(['sale_num' => $sto], $where);
//        echo $set_comm_model->getLastSql();
        var_dump($count_res);

    }

    public function test_ip_go()
    {
        $ip = "http://*************/";
        echo sprintf("<a href='%s'>跳转</a>", $ip);
    }

    public function test_car_index()
    {
        $car_index_service = new CarIndexService();
        $res               = $car_index_service->getGoodsList('GWSC', 3, null, null, null, null, 1, null, '', 'LIMIT -10,10', '2018-09-06 14:18:01', '3', null);
        var_dump($res);
    }

    public function test_s_price()
    {
        $shop_t_price = sprintf("%.2f", 1.8 * ((4.9 + 1.16) / (33.8 + 8)));
        echo $shop_t_price;
    }

    /**
     * @return View
     */
    public function test_bill_by_order()
    {
        $order_id          = input('order_code');
        $order_model       = new BuOrder();
        $order_goods_model = new BuOrderCommodity();
        $where             = ['order_code' => $order_id];
        $order             = $order_model->getOne(['where' => $where]);
        $goods             = $order_goods_model->getList(['where' => $where]);
        $way_bill = [];
        foreach ($goods as $good) {
            // 云仓
            if ($good['common_carrier'] != 'JD' || !$good['third_order_id']) {
                if ($good['common_carrier'] == 'CYS0000010') {
                    // 京东发货
                    $way_bill[] = JdCloudWarehouse::create('jd_cloud_warehouse')->commonQueryOrderTrace($good['jd_warehouse_send_id']);
                } else {
                    // 第三方
                    $way_bill[] = getExpressInfo($good['waybill_number'], '', $order['phone'] ?? '', $good['third_order_id']);

                }
            } else {

                $way_bill[] = getExpressInfo($good['waybill_number'], $good['common_carrier'], $order['phone'] ?? '');
            }

        }
        print_json($way_bill);

    }


    public function test_bill()
    {

        $no   = "70558824819206";
        $type = "HTKY";
        $res  = getExpressInfo_new($no, $type);
        echo $res;
        die();

        $host    = "https://wuliu.market.alicloudapi.com";
        $path    = "/kdi";
        $method  = "GET";
        $appcode = "4a0293607dbe439181b01f5b336e110e";
        $headers = array();
        array_push($headers, "Authorization:APPCODE " . $appcode);
        $querys = "no=70558824819206&type=HTKY";
        $bodys  = "";
        $url    = $host . $path . "?" . $querys;

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HEADER, false);
        if (1 == strpos("$" . $host, "https://")) {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        }
        $res = curl_exec($curl);
        echo($res);
        die();
        echo(curl_exec($curl));
        return $res;


//        $host = "https://jisukdcx.market.alicloudapi.com";
//        $path = "/express/query";
//        $method = "GET";
//        $appcode = "4a0293607dbe439181b01f5b336e110e";
//        $headers = array();
//        array_push($headers, "Authorization:APPCODE " . $appcode);
//        $querys = "number=70558824819206&type=HTKY";
//        $bodys = "";
//        $url = $host . $path . "?" . $querys;
//
//        $curl = curl_init();
//        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
//        curl_setopt($curl, CURLOPT_URL, $url);
//        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
//        curl_setopt($curl, CURLOPT_FAILONERROR, false);
//        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
////    curl_setopt($curl, CURLOPT_HEADER, true); //true：返回头部信息；FALSE不返回头部信息
//        if (1 == strpos("$".$host, "https://")) {
//            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
//            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
//        }
//        $result = curl_exec($curl);
//        var_dump($result);
//        if($test=1){
//            echo  $querys;
//            var_dump($result);
//        }
//        curl_close($curl);
//        $res=json_decode($result, true);
//        return $res;
    }

    public function send_tpl_double201811()
    {
        $tpl_id    = "WiNSvWVu_XZQCD3vLSfr3tUTKbStcF9XfJHfs_KXNQg";
        $b_date    = "2018-11-01 00:00:00";
        $e_date    = "2018-11-04 00:00:00";
        $sql       = sprintf("SELECT order_code,openid from t_bu_order a  where created_date>='%s' AND created_date<='%s' and order_status in (2,4,7,9,11,12) and sale_source=5 and money>0 and dlr_code='GWSC'
and  NOT EXISTS (SELECT act_id from t_bu_card_receive_record b where card_id >=653 and card_id<=659  and a.order_code = b.act_id) and money!=11", $b_date, $e_date);
        $tpl_model = new BuTplMsg();
        $list      = $tpl_model->query($sql);

        foreach ($list as $v) {
            $url        = url('active/Double201811/index', ['order_code' => $v['order_code'], 'dlr_code' => 'GWSC', 'source' => 'send_tpl_201811'], false, true);
            $data       = array(
                'first'    => array(
                    'value' => "“11.11狂欢到底”活动参与资格审核通知",
                    'color' => "",
                ),
                'keyword1' => array(
                    'value' => "通过",
                    'color' => "",
                ),
                'keyword2' => array(
                    'value' => "11月1日至3日购买原厂纯正车品",
                    'color' => "",
                ),
                'keyword3' => array(
                    'value' => "壕礼大转盘",
                    'color' => "",
                ),
                'remark'   => array(
                    'value' => "尊敬的车主您好，感谢您购买东风日产纯正精品，恭喜您获得“壕礼大转盘”抽奖机会壹次，点击本消息进入抽奖。预祝您在本次活动中满载而归！",
                    'color' => "#FF0000",
                )
            );
            $wx_url     = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . AccessToken::get("GWSC");
            $param      = [
                'touser'      => $v['openid'],
                'template_id' => $tpl_id,
                'url'         => $url,
                'data'        => $data,
            ];
            $redis_name = "send_double_201811_tpl:" . $v['order_code'];
            $is_send    = redis($redis_name);
            if (!$is_send) {
                $result = Curl::post($wx_url, $param);
                if ($result) {
                    var_dump($result);
                    $_data = array(
                        'openid'            => $param['touser'],
                        'dlr_code'          => 'GWSC',
                        'tpl_id'            => $param['template_id'],
                        'url'               => $param['url'],
                        'data'              => serialize($param['data']),
                        'errcode'           => $result['errcode'],
                        'errmsg'            => $result['errmsg'],
                        'msgid'             => $result['msgid'],
                        'created_date'      => date('Y-m-d H:i:s'),
                        'last_updated_date' => date('Y-m-d H:i:s'),
                    );
                    $res   = $tpl_model->insertGetId($_data);
                    echo "<br/>";
                    redis($redis_name, 1, 3600 * 24);
                }
            }
        }


    }


    public function test_mail_count()
    {
        $sku_id       = input('sku_id');
        $dlr_code     = input('dlr_code');
        $order_source = input('order_source', 1);
        $com          = new CommodityService();
        $sku          = $com->getOneSku($sku_id, $order_source, $dlr_code);
        $res          = mail_count($sku);
        var_dump($res);
    }

    public function test_echo()
    {
        echo "OK";
    }

    public function test_send_card_msg()
    {
        $openid  = input('openid');
        $card_id = input('card_id');
        $token   = AccessToken::get("GWSC");
        $url     = 'https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=' . $token;
        $param   = [
            'touser'  => $openid,
            'msgtype' => 'wxcard',
            'wxcard'  => [
                'card_id' => $card_id,
            ],
        ];
        $result  = Curl::post($url, $param);
        var_dump($result);

    }

    public function test_tt()
    {
        $ad_model = new DbAdvertisement();
        $list     = $ad_model->getList(['where' => ['is_enable' => 1], 'field' => 'pic,url,id']);
        $ad       = [];
        foreach ($list as $v) {
            $ad[$v['id']]['pic'] = $v['pic'];
            $ad[$v['id']]['url'] = $v['url'];
        }
        var_dump($ad);
    }

    public function userInfo()
    {
        $dlr    = input('dlr_code');
        $openid = input('openid');
        $token  = get_access_token_by_dlr($dlr);
        echo $token;
        echo "<br/>";
        $user = http_get(sprintf("https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN", $token, $openid));
        var_dump($user);
    }

    public function test_hl()
    {
        $openid  = input('openid');
        $car_api = new Carer();
        $v_data  = $car_api->vitality($openid);
        var_dump($v_data);

    }

    public function test_mail_order()
    {
        $order = input('order');
        $by    = input('by', 1);
        $res   = mail_order($order, 1, $by, 1);
        var_dump($res);
    }

    public function test_info()
    {
        $sku_info      = ' 年期:1年期, 容量:4L, 套餐油型:普通油（5W-30)';
        $sku_info_arr  = explode(',', $sku_info);
        $sku_info_jian = '';
        $mail_sku_info = '';
        foreach ($sku_info_arr as $vv) {
            $sku_tmp       = explode(':', $vv);
            $sku_tmp       = $sku_tmp[1];
            $sku_info_jian .= $sku_tmp . ' ';
        }


        $mail_sku_info .= $sku_info_jian;

        var_dump($mail_sku_info);
    }

    public function test_mo()
    {
        $order_code = input('order');
        $model      = new BuOrder();
        $order      = $model->getOne(['where' => ['order_code' => $order_code]]);
//        $order['money']=10.20;
        $money = 1020;
        if ($order['money'] != $money / 100) {
            var_dump("cuowu--");
            var_dump($order['money'] * 100);
            var_dump($money);
        } else {
            var_dump("duile--");
            var_dump($order['money']);
            var_dump($money);
        }

    }

    public function test_go_url()
    {
        $openid = input('get.openid');
        if ($openid) {
            var_dump(input('get.'));
        } else {
            $back_url = urlencode(url('test_go_url', ['id' => 393, 'ba_c' => 5], false, true));
            $url      = "http://acmswe.infiniti.com.cn/api/wei_xin/go_get_user_info?backurl=" . $back_url;
            $this->redirect($url);
        }

    }

    public function redis_null()
    {
        $redis_name = input('redis_name');
        redis($redis_name, null);
    }

    /**
     * 根据订单号查会员积分
     */
    public function order_point()
    {
        $order_model = new BuOrder();
        $order_no    = input('get.order_no');
        $eco         = input('get.eco', 0);
        $where       = ['order_code' => $order_no];
        $order       = $order_model->getOne(array('where' => $where));
        $Car         = new Carer();
        $carer       = $Car->vin(array('openid' => $order['openid']));
        if ($carer) {
            if (isset($carer['ic_card_no'])) {
                $order['ic_card_no'] = $carer['ic_card_no'];
                $point               = $Car->point(array('card_no' => $carer['ic_card_no']));//$car_info['ic_card_no']
                if ($point) {
                    $carer['point'] = $point;
                }
            }
        }

        var_dump($carer);

        if ($order['ic_card_no']) {
            $point = $Car->point(array('card_no' => $order['ic_card_no']));
            echo "<br/>订单会员号积分：";
            var_dump($point);

        }
    }


    public function order_point_list()
    {
        $order_model  = new BuOrder();
        $order_no     = input('get.order_no', "GWSC201908202011401040142,GWSC201908201941270549850,GWSC201908201938296401160,GWSC201908201826474886245,GWSC201908201821139898358,GWSC201908201820114026747,GWSC201908201819055215755,GWSC201908201813469512920,GWSC201908192036349465805,GWSC201908191705291888183,GWSC201908191700177556415,GWSC201908170930049883305,GWSC201908131435269447034");
        $order_no_arr = explode(',', $order_no);
        $where        = ['order_code' => ['in', $order_no_arr]];
        $orders       = $order_model->getList(array('where' => $where));


        $Car = new Carer();
        foreach ($orders as $v) {
            $point = $Car->point(array('card_no' => $v['ic_card_no']));
            echo "订单号:" . $v['order_code'] . '使用积分：' . $v['integral'] . '----有积分:' . $point['pv_point'] . '<br/>';
        }


    }

    /**
     * 根据订单号扣积分
     */
    public function dedu_point_order()
    {
        $order_model = new BuOrder();
        $order_no    = input('get.order_no');
        $member_type = input('get.member_type');
        $remark      = input('get.remark', '积分购买精品');
        $eco         = input('get.eco', 0);
        $where       = ['order_code' => $order_no];
        $order       = $order_model->getOne(array('where' => $where));
        $Car         = new Carer();

        if (!$order['ic_card_no']) {
            $carer = $Car->vin(array('openid' => $order['openid']));
            if ($carer) {
                if (isset($carer['ic_card_no'])) {
                    $order['ic_card_no'] = $carer['ic_card_no'];
                    $point               = $Car->point(array('card_no' => $carer['ic_card_no']));//$car_info['ic_card_no']
                    if ($point) {
                        $carer['point'] = $point;
                    }
                }
            }
            if ($eco == 0) {
                var_dump($carer);
                die();
            }
        }
        $log_model = new DbLog();
        $point     = $Car->point(['card_no' => $order['ic_card_no']]);
//        var_dump($point);
        if ($point) {
            $b_point = $order['integral'];
            $data    = array(
                'vin'        => $order['vin'],
                'openid'     => $order['openid'],
                'ic_card_no' => $order['ic_card_no'],
                'order_id'   => $order['order_code'],
                'dlr_code'   => $order['dlr_code'],
                'deal_man'   => $order['dlr_code'],//处理人
                'point'      => $b_point,
                'remark'     => $remark,
                'dlr_point'  => 0,
            );
            if ($member_type) {
                $data['member_type'] = $member_type;
            }
            $point_res = $Car->deductPoint($data);
            if (!$point_res) {

                print_json(1, '扣返回积分失败,请重新再试', $point_res);
                die();
            }
            $log_data = array(
                'type'         => 'integral',
                'send_note'    => json_encode_cn($data),
                'receive_note' => json_encode_cn($point_res),
            );
            if ($point_res) {
                $log_data['is_success'] = 'success';
//                    Logger::error('pay Notify deductPoint fail-',$data,'deductPointError.log');
            } else {
                $log_data['is_success'] = 'fail';
//                    Logger::error('pay Notify deductPoint ok-',array('r'=>$point_res,'data'=>$data),'deductPointOK.log');
            }
            $res = $log_model->insertData($log_data);

            print_json(1, $res, $point_res);
            die();
        } else {
            print_json(1, '用户没积分了');
        }


    }

    public function test_query_order()
    {
        require_once EXTEND_PATH . '/api/WxPay/WxPay.JsApiPay.php';
        $order_code = input('pay_order_code');
        $input      = new \WxPayOrderQuery();
        $input->SetOut_trade_no($order_code);
        $order = \WxPayApi::orderQuery($input);
        var_dump($order);
    }


    //vin对应电话
    public function vin_info()
    {
        $vin   = input('vin');
        $model = new DbCarUser();
        $where = ['vin' => $vin, 'is_enable' => 1];
        $info  = $model->getOne(['where' => $where]);
        if ($info) {
            echo $vin . '--电话--' . $info['mobile'];
        } else {
            echo $vin . '--无电话--';
        }
    }

    //扫描
    public function test_src()
    {
        $host    = "https://ocrapi-car-invoice.taobao.com";
        $path    = "/ocrservice/carInvoice";
        $method  = "POST";
        $appcode = "a527e9d95af14bf093df81c4a4b08e0b";
        $headers = array();
        array_push($headers, "Authorization:APPCODE " . $appcode);
        //根据API的要求，定义相对应的Content-Type
        array_push($headers, "Content-Type" . ":" . "application/json; charset=UTF-8");
        $querys = "";
        $bb     = ['url' => 'http://wxstore.chebaba.com/public/qy_xcx/fapiao1.png'];
//        $bodys = "{\"img\":\"\",\"url\":\"\"}";
        $bodys = json_encode($bb);
        $url   = $host . $path;

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HEADER, true);
        if (1 == strpos("$" . $host, "https://")) {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        }
        curl_setopt($curl, CURLOPT_POSTFIELDS, $bodys);
        $res = curl_exec($curl);
        var_dump($res);
    }

    public function test_component_list()
    {
        $c_token = get_component_access_token();
        $url     = sprintf('https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_list?component_access_token=%s', $c_token);
//        var_dump($url);die();
        $data = ['component_appid' => config('component_appid'), "offset" => 0, "count" => 400];
        $res  = http_post($url, $data, true);
        var_dump($res);

    }

    public function test_component_info()
    {
        $appid = input('appid');
        $res   = get_component_appinfo($appid);
        var_dump($res);

    }

    public function test_word()
    {
        $val    = input('id');
        $sku_id = substr($val, strripos($val, "id") + 2);
        $stt_id = strripos($val, "id");
        if ($stt_id === 0) {
            var_dump('有');
        }
        var_dump(strripos($val, "id"));
        var_dump($sku_id);
        var_dump(strstr($val, "id"));
        if (strstr($val, "id")) {
            var_dump('oklll');
        };

    }

    public function test_phone_code()
    {
        $phone = input('phone');
        $code  = redis("index_v5_goods_order" . $phone);
        echo $code;
    }

    public function test_sub_ts()
    {
        $b_url  = urlencode(request()->url(true));
        $app_id = "wx4b4bd4343de3f32b";
        $url    = sprintf("https://mp.weixin.qq.com/mp/subscribemsg?action=get_confirm&appid=%s&scene=1001&template_id=CvYqupS9IRavk1P7JXPCXLWb-4ctfKxQKLqq0_3_yTc&redirect_url=%s&reserved=dndc_test#wechat_redirect", $app_id, $b_url);
        echo sprintf("<a href='%s'>提醒</a>", $url);
    }

    public function test_ly()
    {
        $token = 'dndc-ly-weixin';
        $time  = time();
        $data  = [
            'timestamp' => $time,
            'sig'       => md5($time . $token),
            'dlr_code'  => 'GWSC',
            'back_url'  => urlencode(request()->url(true))
        ];
        $url   = url('api/Carer/get_user_info', $data, false, true);
        echo sprintf("<a href='%s'>跳转</a>", $url);

    }


    public function del_refund_r()
    {
        $user  = input('user');
        $order = input('order_code');
        if ($user == 111) {
            $re_model = new BuOrderRefund();
            $order_r  = $re_model->getOne(['where' => ['order_code' => $order]]);
            if ($order_r) {
                $res = $re_model->where(['order_code' => $order])->delete();
                echo $res;
            }


        }
    }

    public function go_url_test()
    {
        $go_url = "http://wxstore.chebaba.com/index_v5/car_index/car_index?dlr_code=GWSC";
        $this->redirect($go_url);
    }

    public function mdpwd()
    {
        $u_model = new DbUser();
        $list    = $u_model->getList(['where' => ['login_pwd' => ['>', 0]]]);
        foreach ($list as $v) {
            $data  = ['login_pwd' => md5($v['login_pwd'])];
            $where = ['id' => $v['id']];
            $u_model->saveData($data, $where);
            echo $u_model->getLastSql();
            echo "<br/>";
        }

    }

    public function test_updateStock()
    {
        $order_code = input('order_goods');
        $set_type   = input('set_type', 1);
        if (!$order_code) {
            die('缺少222');
        }
        $order_c_model = new BuOrderCommodity();
        $order_goods   = $order_c_model->getList(['where' => ['order_code' => input('order_goods')]]);
        $com           = new CommodityService();
        foreach ($order_goods as $v) {
            var_dump($v);
            $res = $com->updateStock($v['sku_id'], $v['commodity_id'], $v['dlr_code'], $set_type, $v['count']);
            var_dump($res);

        }
    }

    public function test_soap()
    {
        $url    = 'http://shelltac.yesno.com.cn/CcnOutService.asmx?wsdl';
        $params = array(
            'userID'   => '99IDJMNCVGDY26I9DJD0DIKAGFSVXCD',//防伪查询帐号
            'userPwd'  => '09JDJU1MDNB8DIKSBVX173DIJDUKLQO1',//防伪查询密码
            'ip'       => '*************',//查询端消费者IP地址
            'acCode'   => '123456',//被查询数码
            'language' => 'zh-cn',//查询语言（zh-cn）
            'channel'  => 21,//查询渠道 （21）
        );

//            $client = new \SoapClient($url);
        $client   = new \SoapClient($url);
        $response = $client->__soapCall('', array($params));
        var_dump($response);
    }

    public function clear_login()
    {
        $user_name  = input('user_name');
        $redis_name = "wx_store-admin-login:" . $user_name;
        redis($redis_name, 0);
    }

    public function test_act_code()
    {
        $order_model = new BuOrder();
        $where       = [
            'order_status' => 1,
            'act_code'     => [['neq', ''], ['neq', '[]'], ['neq', '0'], 'and']
        ];
        $res         = $order_model->getList(['where' => $where]);
        var_dump($res);
    }

    public function test_user_info()
    {
        $user_info = session(config('appid') . '-userinfo');
        $openid    = session(config('appid') . '-openid');
        if ($user_info) {
            var_dump($user_info);
        } else {
            $backurl  = urlencode(request()->url(true));
            $oauthUrl = url('wechat/Oauth/user_info_nissan', ['backurl' => $backurl], false, true);
            $this->redirect($oauthUrl);

        }

    }

    public function test_http()
    {
        $http_type = (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443) ? 'https://' : 'http://';
        var_dump($http_type);
        var_dump($_SERVER['SERVER_PORT']);

    }


    public function test_ccs()
    {
        $ccs    = new Ccs();
        $openid = input('openid', '');
        $vin    = input('vin', '');
        $res    = $ccs->page_info($openid, $vin);
        $he     = $ccs->health_data(['vin' => $vin]);
        var_dump($he);
        var_dump($res);
    }

    public function test_qk_send_point()
    {
        $car_no         = input('car_no');
        $order_no       = input('order_no', time());
        $point          = input('point', 1000);
        $dlr_code       = input('dlr_code', 'GWSC');
        $bus_code       = input('bus_code', 'JPSC');
        $bus_scene_code = input('bus_scene_code', 'WXSF');
        $remark         = input('remark', '商城发积分');
        $car            = new Carer();
        $params         = ['ic_card_no' => $car_no, 'point' => $point, 'dlr_code' => $dlr_code, 'order_id' => $order_no, 'bus_code' => $bus_code, 'bus_scene_code' => $bus_scene_code, 'remark' => $remark];
        $res            = $car->addCluePoint($params);
        $log_model      = new DbLog();
        $log_data       = array(
            'type'         => "add_clue_point",
            'order_code'   => $order_no,
            'send_note'    => json_encode_cn($params),
            'receive_note' => json_encode_cn($res),
        );
        if ($res) {
            $log_data['is_success'] = 'success';
        } else {
            $log_data['is_success'] = 'fail';
        }
        $log_model->insertData($log_data);
        echo json_encode($params);
        var_dump($res);
    }


    public function test_bk_send_point()
    {
        $car_no         = input('car_no');
        $vin            = input('vin');
        $order_no       = input('order_no', time());
        $point          = input('point', 1000);
        $dlr_code       = input('dlr_code', 'GWSC');
        $bus_code       = input('bus_code', 'JPSC');
        $bus_scene_code = input('bus_scene_code', 'WXSF');
        $point_type     = input('point_type', 1);//1专营店送厂家积分。2厂家送厂家积分
        $remark         = input('remark', '商城发积分');
        $brand_code     = input('brand_code', 1);
        $car            = new Carer();
        $params         = [
            'ic_card_no' => $car_no, 'vin' => $vin, 'point' => $point, 'dlr_code' => $dlr_code, 'order_id' => $order_no,
            'bus_code'   => $bus_code, 'bus_scene_code' => $bus_scene_code, 'remark' => $remark, 'point_type' => $point_type,
            'brand_code' => $brand_code
        ];
        $res            = $car->addPoint($params);

        echo json_encode($params);
        var_dump($res);
    }

    public function test_pvqk()
    {
        $vin = input('vin', "PVQK2020110684513");
        $res = strripos($vin, "PV");
        var_dump($res);
    }

    public function test_mz_send_point()
    {
        $data = [
            ['ic_card_no' => 'D23087699033', 'vin' => 'LGBG22E02DY179509'],
            ['ic_card_no' => 'B10312018092365701', 'vin' => 'LGBF5AE08JR096993'],
            ['ic_card_no' => 'H29012017122424990', 'vin' => 'LGBH52E09HY364769'],
            ['ic_card_no' => 'H29012019051139478', 'vin' => 'LGBH52E00KY013594'],
            ['ic_card_no' => 'H29012017111681860', 'vin' => 'LGBS1DE01FR000060'],
            ['ic_card_no' => 'H29925670440', 'vin' => 'LGBH52E01FY419695'],
            ['ic_card_no' => 'B10392018120171360', 'vin' => 'LGBH12E01JY473048'],
            ['ic_card_no' => 'E15032017121879204', 'vin' => 'LGBF5AE08HR331483'],
            ['ic_card_no' => 'E15062017122848748', 'vin' => 'LGBF5AE05HR297213'],
            ['ic_card_no' => 'F25052019081136151', 'vin' => 'LGBM4AE41KS539279'],

        ];
        if (date('Ymd') <> input('rz')) {
            die(date('Ymd') . "日子不对");
        }
        $bus_code       = input('bus_code', 'NIHZ');
        $bus_scene_code = input('bus_scene_code', 'NIHZDZ');
        $car            = new Carer();

        foreach ($data as $v) {
            $redis_name = "hand_send_point2" . date('Ymd');
            $is_send    = redis($redis_name . $v['ic_card_no']);
            if (!$is_send) {
                if (strripos($v['ic_card_no'], "PV") === 0) {
                    $res = $car->addCluePoint(['ic_card_no' => $v['ic_card_no'], 'point' => 1000, 'dlr_code' => 'PV', 'order_id' => time(), 'bus_code' => $bus_code, 'bus_scene_code' => $bus_scene_code]);
                    echo $v['ic_card_no'] . "----";
                    var_dump($res);
                    echo "<br/>";
                    redis($redis_name . $v['ic_card_no'], 1, 3600 * 24);
                } else {
                    $res = $car->addPoint(['ic_card_no' => $v['ic_card_no'], 'vin' => $v['vin'], 'point' => 1000, 'dlr_code' => 'PV', 'order_id' => time(), 'bus_code' => $bus_code, 'bus_scene_code' => $bus_scene_code]);
                    echo $v['ic_card_no'] . "----";
                    var_dump($res);
                    echo "<br/>";
                    redis($redis_name . $v['ic_card_no'], 1, 3600 * 24);
                }
            } else {
                echo $v['ic_card_no'] . "----已经发送了";
            }

        }

    }

    //手工设置redis值。
    public function set_redis()
    {
        $redis_name = input('redis');
        $val        = input('redis_val');
        $time       = input('reids_time', '');
        redis($redis_name, $val, $time);
    }

    public function sale_car()
    {
        $car  = new Carer();
        $vin  = input('vin');
        $user = $car->sale_car_info_new(['vin' => $vin]);
        var_dump($user);
    }

    public function test_num()
    {
//        $order_code=  input('order_code','H2901201804267063694');
//        $model  = new BuOrder();
//        $order =  $model->getOne(['where'=>['order_code'=>$order_code]]);
        $order['money'] = 106.55;
        $money          = input('money', 10655);
        var_dump(gettype((float)(round($order['money'] * 100))));
        var_dump(gettype((float)$money));

//        if(floatval($order['money']*100)!=floatval($money) && floatval($order['front_money']*100)!=floatval($money)){
//               echo 1;
//           }else{
//           echo 2;
//       }
    }


    /**
     * 根据订单号换新订单号并且扣积分
     */
    public function dedu_point_order_new_order()
    {
        $order_model = new BuOrder();
        $order_no    = input('get.order_no');
        $member_type = input('get.member_type');
        $eco         = input('get.eco', 0);
        $new_point   = input('get.new_point');
        $remark      = input('get.remark', '积分购买精品');
        $where       = ['order_code' => $order_no];
        $order       = $order_model->getOne(array('where' => $where));
        if (!$order) {
            die('meiyou订单');
        }
        $Car = new Carer();

        if (!$order['ic_card_no']) {
            $carer = $Car->vin(array('openid' => $order['openid']));
            if ($carer) {
                if (isset($carer['ic_card_no'])) {
                    $order['ic_card_no'] = $carer['ic_card_no'];
                    $point               = $Car->point(array('card_no' => $carer['ic_card_no']));//$car_info['ic_card_no']
                    if ($point) {
                        $carer['point'] = $point;
                    }
                }
            }
            if ($eco == 0) {
                var_dump($carer);
                die();
            }
        }
        if ($new_point) {
            $_order_goods_model = new BuOrderCommodity();
            $order_model->saveData(['order_code' => $order_no . '1', 'integral' => $new_point, 'last_updated_date' => date('Y-m-d H:i:s'), 'modifier' => 'v2_hand'], ['id' => $order['id']]);
            $_order_goods_model->saveData(['order_code' => $order_no . '1', 'last_updated_date' => date('Y-m-d H:i:s'), 'modifier' => 'v2_hand'], $where);
            $order_new = $order_model->getOne(array('where' => ['id' => $order['id']]));
            $log_model = new DbLog();
            $point     = $Car->point(['card_no' => $order['ic_card_no']]);
            if ($point) {
                $b_point = $order_new['integral'];
                $data    = array(
                    'vin'        => $order_new['vin'],
                    'openid'     => $order_new['openid'],
                    'ic_card_no' => $order_new['ic_card_no'],
                    'order_id'   => $order_new['order_code'],
                    'dlr_code'   => $order_new['dlr_code'],
                    'deal_man'   => $order_new['dlr_code'],//处理人
                    'point'      => $b_point,
                    'dlr_point'  => 0,
                    'remark'     => $remark,
                );
                if ($member_type) {
                    $data['member_type'] = $member_type;
                }
                $point_res = $Car->deductPoint($data);
                if (!$point_res) {

                    print_json(1, '扣返回积分失败,请重新再试', $point_res);
                    die();
                }
                $log_data = array(
                    'type'         => 'integral',
                    'send_note'    => json_encode_cn($data),
                    'receive_note' => json_encode_cn($point_res),
                );
                if ($point_res) {
                    $log_data['is_success'] = 'success';
//                    Logger::error('pay Notify deductPoint fail-',$data,'deductPointError.log');
                } else {
                    $log_data['is_success'] = 'fail';
//                    Logger::error('pay Notify deductPoint ok-',array('r'=>$point_res,'data'=>$data),'deductPointOK.log');
                }
                $res = $log_model->insertData($log_data);

                print_json(1, $res, $point_res);
                die();
            } else {
                print_json(1, '用户没积分了');
            }
        }

    }

    public function hand_send_card_by_order()
    {
        $source     = input('source', 1);
        $act        = input('act');
        $user       = input('user');
        $card_ids   = input('cards');
        $order_code = input('order_code');
        $res        = getRedisLock("hand_send_card_by_order" . $order_code . $card_ids, 60);
        if (!$res) {
            print_json(1, 'OK');
        }
        if ($user <> 'lzx') {
            print_json(1, '卡券已经被领取了~~~~~');
        }
        $card_arr    = explode(',', $card_ids);
        $order_model = new BuOrder();
        $order       = $order_model->getOne(['where' => ['order_code' => $order_code]]);
        if (!$order) {
            print_json(1, '订单不存在通过订单发卡券~~~~~');

        }
        $act_code = 'hand-send_card:' . $order['id'];
        $carer    = new Carer();
        if ($card_arr) {
            $card_model = new DbCard();
            foreach ($card_arr as $card_id) {
                $where = ['id' => $card_id, 'available_count' => ['>', 0]];
                $card  = $card_model->getOne(array('where' => $where));
                if ($card) {
                    if ($card['type'] == 1) {
                        print_json(1, "卡券类型错误");
                    }
                    $card_r_model = new BuCardReceiveRecord();
                    $_where       = ['card_id' => $card_id, 'user_id' => $order['user_id'], 'act_id' => $act_code];
                    $card_record  = $card_r_model->where($_where)->find();
                    if (!$card_record) {
                        $data                = array(
                            'user_id'       => $order['user_id'],
                            'openid'        => $order['openid'],
                            'vin'           => $order['vin'],
                            'license_plate' => $order['license_plate'],
                            'name'          => $order['name'],
                            'source'        => $source,
                            'act_id'        => $act_code,
                            'phone'         => $order['phone'],
                            'card_id'       => $card_id,
                            'dlr_code'      => $order['dlr_code'],
                        );
                        $card_code           = $this->_getOrderNo($order['dlr_code'] . 'c' . date('YmdHis'), 3);
                        $data['is_get_card'] = 1;
                        $data['card_code']   = $card_code;
                        $data['status']      = 1;
                        $res                 = $card_model->where($where)->setDec('available_count');
                        if ($res) {
                            $row = $card_r_model->insertData($data);
                            if ($row) {
                            } else {
                                $card_model->where($where)->setInc('available_count');//加回去
                                $error = 1;//领取失败
                                print_json(1, '领取失败!');
                            }
                        } else {
                            $error = 2;//扣库存失败
                            print_json(1, '更新卡券失败!');
                        }
                    } else {
                        $error = 3;//卡券已领取
                        print_json(1, '卡券已领取!');
                    }
                } else {
                    $error = 4;//卡券库存不足
                    print_json(1, '卡券库存不足!');

                }
            }
            redis($act . '-card', null);
            redis($act . '-act_code', null);
            print_json(0, 'ok');
        } else {
            print_json(1, '卡券异常');

        }

    }

    /**
     * 生成订单号
     */
    protected function _getOrderNo($preStr = '', $length = 7)
    {
        $chars = "0123456789";
        $str   = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }

        return $preStr . $str;
    }


    public function ec_unionid()
    {
        echo urlencode(nissanEncrypt(input('unionid')));
    }

    public function sc_url()
    {
        $id         = input('id');
        $user_model = new DbUser();
        $user       = $user_model->getOneByPk($id);
        $data       = [
            'time_sm'    => strtotime('+1 hour'),
            'random_sm'  => '013639',
            'test'       => '123456',
            'channel_sm' => input('channel'),
            'member_id'  => urlencode(nissanEncrypt($user['plat_id'])),
            'unique_sm'  => urlencode(nissanEncrypt($user['unionid'])),
            'open_sm'    => urlencode(nissanEncrypt($user['openid'])),
            'one_id'     => urlencode(nissanEncrypt($user['one_id'])),
        ];
//        build_query()
        echo build_query($data);
        //member_id=mzlFzsFlbVzmQEz%2FLBzGc9gwCGImx5ir%2B3GjNZInLxY%3D&unique_sm=IbGq9pAsW3ZZ5tSGTfrJxQ%3D%3D&time_sm=1656089870&open_sm=IbGq9pAsW3ZZ5tSGTfrJxQ%3D%3D&one_id=NqGk23ozskXzdnfhZH1la0oKCIaCDqQiaVBQSkCpNrbcmRD7%2FjrB4pwyW%2BIWy0uC&random_sm=013639&channel_sm=QCAPP
    }

    public function sc_url_more(){
        $id =  input('id');
        $user_model = new DbUser();
        $user =  $user_model->getOneByPk($id);
        $data = [
            'time_sm'=>strtotime('+1 hour'),
            'random_sm'=>'013639',
            'test'=>'123456',
            'channel_sm'=>input('channel'),
            'member_id'=>urlencode(nissanEncrypt($user['plat_id'])),
            'unique_sm'=>urlencode(nissanEncrypt($user['unionid'])),
            'open_sm'=>urlencode(nissanEncrypt($user['openid'])),
            'one_id'=>urlencode(nissanEncrypt($user['one_id'])),
        ];
//        build_query()
        print_json(build_query($data),$data);
        echo build_query($data);
        //member_id=mzlFzsFlbVzmQEz%2FLBzGc9gwCGImx5ir%2B3GjNZInLxY%3D&unique_sm=IbGq9pAsW3ZZ5tSGTfrJxQ%3D%3D&time_sm=1656089870&open_sm=IbGq9pAsW3ZZ5tSGTfrJxQ%3D%3D&one_id=NqGk23ozskXzdnfhZH1la0oKCIaCDqQiaVBQSkCpNrbcmRD7%2FjrB4pwyW%2BIWy0uC&random_sm=013639&channel_sm=QCAPP
    }

    public function ne_unionid(){
        echo  nissanDecrypt(input('unionid'));
    }

    public function test_add_point()
    {
        $car_er_api = new Carer();
        $data       = [
//            'openid' => input('get.openid'),
'vin'        => input('get.vin', 'LGBH52E02EY179667'),
'ic_card_no' => input('get.card_no', 'H29014355037'),
'order_id'   => 'GWSC' . date('YmdHis') . mt_rand(1000, 9999),
'dlr_code'   => 'GWSC',//送积分用PV
'point'      => input('get.point', 1),
'remark'     => '增加积分'
        ];
        $point_res  = $car_er_api->addPoint($data);
        dump($point_res);
        exit();

    }


    /**
     * 小程序版本重复扣积分回退
     */
    public function new_dedu_point_order_point()
    {
        $point_model      = new BuOrderPoint();
        $point_order_code = input('point_order_code');
        $member_type      = input('member_type');
        $dlr_code         = input('new_dlr', '');
        $anti             = input('anti', 0);
        $r_dedu           = input('r_dedu', 0);
        $new_point        = input('new_point', 0);
        $brand            = input('brand', 1);
        $point_order      = $point_model->getOne(['where' => ['point_order_code' => $point_order_code]]);
        $Car              = new Carer();
        if ($point_order) {
            $log_model = new DbLog();
            if ($anti == 1) {
                $data      = array(
                    'ic_card_no'  => $point_order['ic_card_no'],
                    'vin'         => $point_order['vin'],
                    'order_id'    => $point_order['point_order_code'],
                    'dlr_code'    => $point_order['dlr_code'],
                    'deal_man'    => $point_order['dlr_code'],//处理人
                    'brand'       => $brand,//品牌
                    'member_type' => $member_type,//潜客保客
                );
                $point_res = $Car->antiBalance($data);
                $log_data  = array(
                    'type'         => 'antiBalance',
                    'modifier'     => '手工退',
                    'send_note'    => json_encode_cn($data),
                    'receive_note' => json_encode_cn($point_res),
                    'order_code'   => $point_order['point_order_code'],
                );
                if ($point_res) {
                    $log_data['is_success'] = 'success';
                } else {
                    $log_data['is_success'] = 'fail';
                }
                $res = $log_model->insertData($log_data);
            }
            if ($r_dedu) {
                $is_cc_ok = 0;
                if (!$new_point) {
                    $new_point = $point_order['point'];
                }
                $data = array(
                    'vin'        => $point_order['vin'],
                    'ic_card_no' => $point_order['ic_card_no'],
                    'order_id'   => $point_order['point_order_code'] . '1',
                    'dlr_code'   => $dlr_code ? $dlr_code : $point_order['dlr_code'],
                    'deal_man'   => $dlr_code ? $dlr_code : $point_order['dlr_code'],//处理人
                    'point'      => $new_point,
                    'dlr_point'  => 0,
                    'remark'     => $point_order['remark'],
                    'brand_code' => $brand,//品牌
                );
                if ($member_type == 1) {
                    $data['member_type'] = 1;
                }
                $point_res = $Car->deductPoint($data);
                $log_data  = array(
                    'type'         => 'integral',
                    'send_note'    => json_encode_cn($data),
                    'receive_note' => json_encode_cn($point_res),
                );
                if ($point_res == 'ok') {
                    $log_data['is_success'] = 'success';
                } else {
                    $log_data['is_success'] = 'fail';
                    $is_cc_ok               = 1;
                }
                $point_data = [
                    'order_code'       => $point_order['order_code'],
                    'point_order_code' => $data['order_id'],
                    'vin'              => $data['vin'],
                    'ic_card_no'       => $data['ic_card_no'],
                    'dlr_code'         => $data['dlr_code'],
                    'creator'          => '手工',
                    'point_type'       => $point_order['point_type'],
                    'all_point'        => $point_order['all_point'],
                    'point'            => $data['point'],
                    'use_state'        => $is_cc_ok + 1,
                    'remark'           => $data['remark'],
                    'send_note'        => json_encode($data),
                    'back_json'        => json_encode_cn($point_res),
                ];
                $point_model->insertData($point_data);
                var_dump($point_data);
                if ($point_data['use_state'] == 2) {
                    $order_model = new BuOrder();
                    $res         = $order_model->saveData(['is_cc_ok' => 1], ['order_code' => $point_data['order_code']]);
                    Logger::error('net_order_usepoint', ['r' => $res, 'sql' => $order_model->getLastSql()]);
                }
                $point_model->saveData(['is_enable' => 0], ['point_order_code' => $point_order_code]);//把原来的弄成不可用
                $res = $log_model->insertData($log_data);
            }

        }
    }


    public function test_new_carer()
    {
        $openid  = input('openid');
        $vin     = input('vin');
        $unionid = input('unionid');
        $car     = new CarerNew();
        if ($vin) {
            $token = $car->vin(array('vin' => $vin));
        } elseif ($unionid) {
            $token = $car->vin(array('unionid' => $unionid));

        } else {
            $token = $car->vin(array('openid' => $openid));

        }
        var_dump($token);
    }


    public function point_order_point_back()
    {
        $test = input('test', 0);
        $sql  = "SELECT a.id,c.point_order_code,c.vin,c.dlr_code,c.ic_card_no,a.order_code from t_bu_order a
join t_bu_order_err_list b on a.order_code=b.order_code  and b.is_enable=1
join t_bu_order_point c on a.order_code=c.order_code
where a.order_status in (2,11,10,3)";
        if ($test == 1) {
            $sql = "SELECT a.id,c.point_order_code,c.vin,c.dlr_code,c.ic_card_no,a.order_code from t_bu_order a
join t_bu_order_err_list b on a.order_code=b.order_code  and b.is_enable=1
join t_bu_order_point c on a.order_code=c.order_code
where a.order_status in (2,11,10,3)
limit 10";
        }
        $Car              = new Carer();
        $point_model      = new BuOrderPoint();
        $order_model      = new BuOrder();
        $order_list_model = new BuOrderErrList();
        $log_model        = new DbLog();
        $list             = $point_model->query($sql);
        foreach ($list as $point_order) {
            $data      = array(
                'ic_card_no' => $point_order['ic_card_no'],
                'vin'        => $point_order['vin'],
                'order_id'   => $point_order['point_order_code'],
                'dlr_code'   => $point_order['dlr_code'],
                'deal_man'   => $point_order['dlr_code'],//处理人
            );
            $point_res = $Car->antiBalance($data);
            $log_data  = array(
                'type'         => 'antiBalance',
                'order_code'   => $point_order['point_order_code'],
                'modifier'     => '手工-hhr',
                'send_note'    => json_encode_cn($data),
                'receive_note' => json_encode_cn($point_res),
            );
            if ($point_res) {
                $log_data['is_success'] = 'success';
            } else {
                $log_data['is_success'] = 'fail';
            }
            $res        = $log_model->insertData($log_data);
            $order_data = ['order_status' => 18, 'modifier' => 'hhr-anti'];
            $where      = ['id' => $point_order['id']];
            $order_model->saveData($order_data, $where);
            $err_data = ['is_enable' => 0];
            $order_list_model->saveData($err_data, ['order_code' => $point_order['order_code']]);
            sleep(2);
        }
    }

    //合伙人扣积分多条
    public function dedu_point_hhr()
    {
        $pc        = input('pc');
        $hhr_model = new BuVinListHhr();
        $where     = ['status' => 1, 'pc' => $pc];
        $list      = $hhr_model->getList(['where' => $where]);
        $Car       = new Carer();
        if ($list) {
            foreach ($list as $v) {
                $data      = array(
                    'vin'            => $v['vin'],
                    'ic_card_no'     => $v['ic_card_no'],
                    'order_id'       => $v['order_id'],
                    'dlr_code'       => 'PV',
                    'deal_man'       => 'PV',//处理人
                    'point'          => $v['integral'],
                    'dlr_point'      => 0,
                    'member_type'    => 1,
                    'remark'         => "合伙人分享异常积分扣减",
                    'bus_scene_code' => "611d49ec200afd0ab85864b02b8ad5d9",
                    'bus_code'       => "WX",
                );
                $point_res = $Car->deductPoint($data);
                $log_data  = array(
                    'status'       => 2,
                    'order_id'     => $data['order_id'],
                    'send_note'    => json_encode_cn($data),
                    'receive_note' => json_encode_cn($point_res),
                );
                $g_where   = ['id' => $v['id']];
                $hhr_model->saveData($log_data, $g_where);
            }
        }
    }

    //合伙人扣积分单条
    public function dedu_point_hhr_one()
    {
        $vin       = input('vin');
        $id_hz     = input('id_hz', '');
        $hhr_model = new BuVinListHhr();
        $where     = ['vin' => $vin];
        $v         = $hhr_model->getOne(['where' => $where]);
        $Car       = new Carer();
        $data      = array(
            'vin'            => $v['vin'],
            'ic_card_no'     => $v['ic_card_no'],
            'order_id'       => $v['order_id'] . $id_hz,
            'dlr_code'       => 'PV',
            'deal_man'       => 'PV',//处理人
            'point'          => $v['integral'],
            'dlr_point'      => 0,
            'member_type'    => 1,
            'remark'         => "合伙人分享异常积分扣减",
            'bus_scene_code' => "611d49ec200afd0ab85864b02b8ad5d9",
            'bus_code'       => "WX",
        );
        $point_res = $Car->deductPoint($data);
        $log_data  = array(
            'status'       => 2,
            'order_id'     => $data['order_id'],
            'send_note'    => json_encode_cn($data),
            'receive_note' => json_encode_cn($point_res),
        );
        $g_where   = ['id' => $v['id']];
        $hhr_model->saveData($log_data, $g_where);

    }


    //合伙人异常订单反结算
    public function anti_hhr()
    {
        $vin         = input('vin');
        $id_hz       = input('id_hz', '');
        $hhr_model   = new BuVinListHhr();
        $log_model   = new DbLog();
        $where       = ['vin' => $vin];
        $point_order = $hhr_model->getOne(['where' => $where]);
        $Car         = new Carer();

        $data      = array(
            'ic_card_no'     => $point_order['ic_card_no'],
            'vin'            => $point_order['vin'],
            'order_id'       => $point_order['order_id'],
            'dlr_code'       => 'PV',
            'deal_man'       => 'PV',//处理人
            'member_type'    => 1,
            'remark'         => "合伙人分享异常积分扣减反结算",
            'bus_scene_code' => "611d49ec200afd0ab85864b02b8ad5d9",
            'bus_code'       => "WX",
        );
        $point_res = $Car->antiBalance($data);
        $log_data  = array(
            'type'         => 'antiBalance',
            'order_code'   => $point_order['point_order_code'],
            'modifier'     => '手工-hhr',
            'send_note'    => json_encode_cn($data),
            'receive_note' => json_encode_cn($point_res),
        );
        if ($point_res) {
            $log_data['is_success'] = 'success';
        } else {
            $log_data['is_success'] = 'fail';
        }
        $res = $log_model->insertData($log_data);
    }

    //根据订单号列表去退现金部分
    public function test_re_money()
    {
        $test   = input('test', 0);
        $user   = input('user');
        $remark = input('remark', '合伙人退款');
        $this->check_user($user);
        $order_model = new BuOrder();
        $list        = $order_model->alias('a')->join('t_bu_order_err_list b', 'a.order_code=b.order_code and b.type=2 and b.is_enable=1')->where(['a.money' => ['>', 0]])->field('a.*,b.id bid')->select();

        if ($test == 1) {
            echo $order_model->getLastSql();
            var_dump(count($list));
        }
        $redis_name      = 'test_re_money.1';
        $err_order_model = new BuOrderErrList();
        if ($list) {
            foreach ($list as $v) {
                $have_prs = redis($redis_name . $v['cashier_trade_no']);
                if (!$have_prs) {
                    $refund_req = [
                        'cashier_trade_no'  => $v['cashier_trade_no'],
                        'refund_type'       => 0,
                        'refund_desc'       => $remark,
                        'refund_fee'        => $v['money'] * 100,
                        'refund_notify_url' => config("DOMAIN_INNER_URL") . "net_small/pay_plat/refund"
                    ];

                    $prs = Payment::create('payment')->refundOrder($refund_req);
                    DbLog::create([
                        'type'         => 'refund_money_' . 0,
                        'send_note'    => json_encode($refund_req),
                        'receive_note' => json_encode($prs),
                        'is_success'   => !empty($prs['refund_status']) ? 'success' : 'fail'
                    ]);
                    $err_data = [
                        'is_enable'    => 0,
                        'send_note'    => json_encode($refund_req),
                        'receive_note' => json_encode($prs),
                    ];
                    $err_order_model->saveData($err_data, ['id' => $v['bid']]);
                    redis($redis_name . $v['cashier_trade_no'], 1, 300);
                }
            }
        }
    }

    //批量插入退现金操作--退积分操作
    public function in_err_list()
    {
        $err_order_model = new BuOrderErrList();
        $order_codes     = input('order_codes');
        $type            = input('type', 2);//类型1退积分2退现金9部分退积分10赠送积分
        $an_ti           = input('an_ti', 0);//反结算 0不1反
        $r_dedu          = input('r_dedu', 0);//扣积分 0不1扣

        $order_err = explode(',', $order_codes);
        foreach ($order_err as $v) {
            $in_data = [
                'order_code' => $v,
                'type'       => $type,
                'anti'       => $an_ti,
                'r_dedu'     => $r_dedu,
            ];
            $res     = $err_order_model->insertGetId($in_data);
            var_dump($res);
        }
    }

    //插入手机号码黑名单到数据库
    public function in_phone_backl()
    {
        $phone      = input('phone');
        $ac_type    = input('ac_type', 1);//1添加2删除
        $phone_type = input('phone_type', 1);//类型1车主积分短信黑名单
        $list_model = new BuPhoneBlacklist();
        $res        = 0;
        if ($ac_type == 1) {
            $res = $list_model->insertData(['phone' => $phone, 'creator' => 'qd_i_p_b', 'type' => $phone_type]);
        }
        if ($ac_type == 2) {
            $res = $list_model->saveData(['is_enable' => 0, 'modifier' => 'i_p_b_c'], ['phone' => $phone]);
        }
        var_dump($res);
    }

    //修改jdmsg未处理
    public function change_jd_message()
    {
        $msg_id = input('msg_id');
        if (!$msg_id) {
            die('msg_id--kong');
        }
        $is_solve  = input('is_solve', 0);
        $cp_b      = input('cp_b', 0);
        $msg_b     = input('msg_b', 'order');
        $msg_model = new JdMessage();
        $msg       = $msg_model->getOneByPk($msg_id);
        if (!$msg) {
            if ($cp_b == 1) {
                if ($msg_b == 'order') {
                    $sql = sprintf("INSERT into t_jd_message SELECT * from t_jd_message_order where id='%s'", $msg_id);
                } else {
                    $sql = sprintf("INSERT into t_jd_message SELECT * from t_jd_message_goods where id='%s'", $msg_id);
                }
                $res = $msg_model->query($sql);
            }
            $msg_model = new JdMessage();
            $msg       = $msg_model->getOneByPk($msg_id);
            if (!$msg) {
                die('shuju kong!');
            }
        }
        $res = $msg_model->saveData(['is_solve' => $is_solve], ['id' => $msg_id]);


    }

    public function re_bran_point()
    {

    }

    private function check_user($user)
    {
        if ($user <> 'lzx') {
            die('路径名字异常!');
        }

    }

    /**
     * 手工处理大量结算单从1-2.
     */
    public function hand_some_order_const()
    {
        $user = input('user');
        $this->check_user($user);
        $order_code  = input('order_code');
        $net_order   = new NetOrder();
        $order_model = new BuOrder();
        $list        = $order_model->alias('a')->join('t_bu_order_settlement b ', 'a.order_code=b.order_code  and a.order_status=9 and b.state=1')->field('a.order_code')->group('a.order_code')->select();
        if (input('test') == 1) {
            echo $order_model->getLastSql();
            die();
        }
        if ($list) {
            foreach ($list as $v) {
                $res = $net_order->confirmSett($v['order_code']);
                var_dump($res);
            }
        }
    }

    /**
     * @return void
     * 直接处理结算表从1-2.
     * SELECT * from t_bu_order_settlement where is_enable=1 and  intention_account_id='DNDC001' and state = 1 and pay_time >= "2022-06-01 00:00:00"
     */
    public function hand_order_set_confirm()
    {
        $user = input('user');
        $this->check_user($user);
        $net_order   = new NetOrder();
        $order_model = new BuOrderSettlement();
        $list        = $order_model->getList(['where' => ['is_enable' => 1, 'intention_account_id' => 'DNDC001', 'state' => 1]]);
        foreach ($list as $v) {
            if ($v['state'] == 1) {
                $set_c_data = [
                    'confirm_account_id'    => $v['intention_account_id'],
                    'cashier_settlement_no' => $v['cashier_settlement_no'],
                    'cashier_trade_no'      => $v['cashier_trade_no'],
                    'settlement_fee'        => $v['settlement_fee'],
                ];
                $res        = Payment::create('payment')->confirmSett($set_c_data);
                Logger::debug('pay-confirmSett-', ['data' => $set_c_data, 'r' => $res]);
                if (!isset($res['message'])) {
                    $ls_date = date('Y-m-d H:i:s');
                    $res     = $order_model->saveData(['state' => 2, 'settlement_state' => 2, 'confirm_account_id' => $v['intention_account_id'], 'last_updated_date' => $ls_date], ['id' => $v['id']]);
                    var_dump($res);
                }
            }
        }


    }

    //pv积分结算错误，反结算到GWSC--反结算 1
    public function anti_point_pv()
    {
        $pc        = input('pc');
        $hhr_model = new BuVinListHhr();
        $where     = ['status' => 1, 'pc' => $pc];
        $list      = $hhr_model->getList(['where' => $where]);
        $Car       = new Carer();
        if ($list) {
            foreach ($list as $v) {
                $data      = array(
                    'vin'         => $v['vin'],
                    'ic_card_no'  => $v['ic_card_no'],
                    'order_id'    => $v['order_id'],
                    'dlr_code'    => 'GWSC',
                    'deal_man'    => 'GWSC',//处理人
                    'point'       => $v['integral'],
                    'dlr_point'   => 0,
                    'member_type' => 1,
                    'remark'      => "结算店修复",
                    //                    'bus_scene_code'     => "611d49ec200afd0ab85864b02b8ad5d9",
                    //                    'bus_code'     => "WX",
                );
                $point_res = $Car->antiBalance($data);
                $log_data  = array(
                    'status'       => 2,
                    'order_id'     => $data['order_id'],
                    'send_note'    => json_encode_cn($data),
                    'receive_note' => json_encode_cn($point_res),
                );
                $g_where   = ['id' => $v['id']];
                $hhr_model->saveData($log_data, $g_where);
            }
        }
    }


    //pv积分结算错误，反结算到GWSC--扣积分部分 2已扣积分
    public function dedu_point_pv()
    {
        $pc        = input('pc');
        $hhr_model = new BuVinListHhr();
        $where     = ['status' => 2, 'pc' => $pc];
        $remark    = input('remark', "结算店修复");
        $dlr_code  = input('dlr_code', 'PV');
        $list      = $hhr_model->getList(['where' => $where]);
        $Car       = new Carer();
        if ($list) {
            foreach ($list as $v) {
                $data      = array(
                    'vin'         => $v['vin'],
                    'ic_card_no'  => $v['ic_card_no'],
                    'order_id'    => $v['order_id'],
                    'dlr_code'    => $dlr_code,
                    'deal_man'    => $dlr_code,//处理人
                    'point'       => $v['integral'],
                    'dlr_point'   => 0,
                    'member_type' => 1,
                    'remark'      => $remark,
                    //                    'bus_scene_code'     => "611d49ec200afd0ab85864b02b8ad5d9",
                    //                    'bus_code'     => "WX",
                );
                $point_res = $Car->deductPoint($data);
                $log_data  = array(
                    'status'        => 3,
                    'order_id'      => $data['order_id'],
                    'send_note1'    => json_encode_cn($data),
                    'receive_note1' => json_encode_cn($point_res),
                );
                $g_where   = ['id' => $v['id']];
                $hhr_model->saveData($log_data, $g_where);
            }
        }


    }

    //pv积分结算错误，反结算到GWSC--扣积分部分 2已扣积分  处理一条
    public function dedu_point_pv_one()
    {
        $order_code  = input('order_code');
        $car_no      = input('car_no');
        $member_type = input('member_type', 1);
        $hhr_model   = new BuVinListHhr();
        $where       = ['order_id' => $order_code];
        $list        = $hhr_model->getList(['where' => $where]);
        $Car         = new Carer();
        if ($list) {
            foreach ($list as $v) {
                $data      = array(
                    'vin'         => $v['vin'],
                    'ic_card_no'  => $car_no ? $car_no : $v['ic_card_no'],
                    'order_id'    => $v['order_id'],
                    'dlr_code'    => 'PV',
                    'deal_man'    => 'PV',//处理人
                    'point'       => $v['integral'],
                    'dlr_point'   => 0,
                    'member_type' => $member_type,
                    'remark'      => "结算店修复",
                    //                    'bus_scene_code'     => "611d49ec200afd0ab85864b02b8ad5d9",
                    //                    'bus_code'     => "WX",
                );
                $point_res = $Car->deductPoint($data);
                $log_data  = array(
                    'status'        => 3,
                    'order_id'      => $data['order_id'],
                    'send_note1'    => json_encode_cn($data),
                    'receive_note1' => json_encode_cn($point_res),
                );
                $g_where   = ['id' => $v['id']];
                $hhr_model->saveData($log_data, $g_where);
            }
        }
    }

    //pv积分结算错误，反结算到GWSC--扣积分部分 2已扣积分  处理一条--潜客
    public function dedu_point_pv_one_clue()
    {
        $order_code  = input('order_code');
        $car_no      = input('car_no');
        $member_type = input('member_type', 1);
        $hhr_model   = new BuVinListHhr();
        $where       = ['order_id' => $order_code];
        $list        = $hhr_model->getList(['where' => $where]);
        $Car         = new Carer();
        if ($list) {
            foreach ($list as $v) {
                $data      = array(
                    'ic_card_no'  => $car_no ? $car_no : $v['ic_card_no'],
                    'order_id'    => $v['order_id'],
                    'dlr_code'    => 'PV',
                    'deal_man'    => 'PV',//处理人
                    'point'       => $v['integral'],
                    'dlr_point'   => 0,
                    'member_type' => $member_type,
                    'remark'      => "结算店修复",
                    //                    'bus_scene_code'     => "611d49ec200afd0ab85864b02b8ad5d9",
                    //                    'bus_code'     => "WX",
                );
                $point_res = $Car->deductCluePoint($data);
                $log_data  = array(
                    'status'        => 3,
                    'order_id'      => $data['order_id'],
                    'send_note1'    => json_encode_cn($data),
                    'receive_note1' => json_encode_cn($point_res),
                );
                $g_where   = ['id' => $v['id']];
                $hhr_model->saveData($log_data, $g_where);
            }
        }
    }

    //pv积分结算错误，反结算到GWSC--反结算 1
    public function anti_point_pv_one()
    {
        $order_code = input('order_code');
        $dlr_code   = input('dlr_code', 'GWSC');
        $hhr_model  = new BuVinListHhr();
        $where      = ['order_id' => $order_code];
        $list       = $hhr_model->getList(['where' => $where]);
        $Car        = new Carer();
        if ($list) {
            foreach ($list as $v) {
                $data      = array(
                    'vin'         => $v['vin'],
                    'ic_card_no'  => $v['ic_card_no'],
                    'order_id'    => $v['order_id'],
                    'dlr_code'    => $dlr_code,
                    'deal_man'    => $dlr_code,//处理人
                    'point'       => $v['integral'],
                    'dlr_point'   => 0,
                    'member_type' => 1,
                    'remark'      => "结算店修复",
                    //                    'bus_scene_code'     => "611d49ec200afd0ab85864b02b8ad5d9",
                    //                    'bus_code'     => "WX",
                );
                $point_res = $Car->antiBalance($data);
                $log_data  = array(
                    'status'       => 2,
                    'order_id'     => $data['order_id'],
                    'send_note'    => json_encode_cn($data),
                    'receive_note' => json_encode_cn($point_res),
                );
                $g_where   = ['id' => $v['id']];
                $hhr_model->saveData($log_data, $g_where);
            }
        }
    }


    public function order_no_point()
    {
        $order_code  = ['GWSM202108271918447013562', 'GWSM202108302358432430985', 'GWSM202108310005373217629', 'GWSM202108310020180199224', 'GWSM202108310029028676689', 'GWSM202108311251506955074', 'GWSM202109011809012820980', 'GWSM202109012146263630438', 'GWSM202109022146395085793', 'GWSM202109031524445602392', 'GWSM202109031722475551727', 'GWSM202109031804088073716', 'GWSM202109040016402100962', 'GWSM202109041006212040650', 'GWSM202109041259209109912', 'GWSM202109041557503285676', 'GWSM202109062346229759549', 'GWSM202109062348100473516', 'GWSM202109062350499545108', 'GWSM202109062351323171426', 'GWSM202109071725423072356', 'GWSM202109071729333291153', 'GWSM202109071731512328240', 'GWSM202109071733329576585', 'GWSM202109081537347596280', 'GWSM202109081538522895201', 'GWSM202109081614052814642', 'GWSM202109081615063831478', 'GWSM202109081618068071966', 'GWSM202109091637476388915', 'GWSM202109101751290193765', 'GWSM202109181202595940149'];
        $order_model = new BuOrder();
        $order_list  = $order_model->getList(['where' => ['order_code' => ['in', $order_code]]]);

        $Car = new Carer();
        foreach ($order_list as $v) {
            echo $v['order_code'] . '==应扣积分:' . $v['integral'] . '===现有积分:';
            $data  = ['card_no' => $v['ic_card_no']];
            $point = $Car->point($data);
            if ($point) {
                echo $point['pv_point'];

            }
            echo "<br/>";


        }
    }

    //手工下发CCS
    public function hand_send_ccs()
    {
        $user = input('user');
        $this->check_user($user);
        $order_codes  = input('order_codes');
        $order_arr    = explode(',', $order_codes);
        $redis_t_name = "hand_send_ccs";
        foreach ($order_arr as $v) {
            $have_redis = redis($redis_t_name . $v);
            if (!$have_redis) {
                $res = send_order_ccs($v);
                var_dump($res);
                redis($redis_t_name . $v, 1, 3600);
            }
        }
    }

    //点评送分
    public function comment_send_point()
    {
        $user = input('user');
        $this->check_user($user);
        $model          = new BuOrderErrList();
        $dlr_code       = input('dlr_code', 'PV');
        $bus_code       = input('bus_code', 'JPSC');
        $order_code     = input('order_code', '');
        $point_type     = input('point_type', 2);//1专营店送厂家积分。2厂家送厂家积分
        $bus_scene_code = input('bus_scene_code', 'WXSF');
        $remark         = input('remark', '11-12月用户激励评论返88积分活动');
        $where          = ['a.is_enable' => 1, 'a.type' => 3];
        if ($order_code) {
            $where['a.order_code'] = $order_code;
        }
        $list = $model->alias("a")->join('t_bu_order b', "a.order_code=b.order_code")->where($where)->field("b.order_code,a.point,b.ic_card_no,b.vin,a.id bid")->select();
        if (input('test') == 1) {
            var_dump($model->getLastSql());
            die();
        }
        $car        = new Carer();
        $log_model  = new DbLog();
        $redis_name = 'comment_send_point';
        if ($list) {
            foreach ($list as $v) {
                $have_r_dis = getRedisLock($redis_name . $v['order_code'], 60);
                if (!$have_r_dis) {
                    $params = ['ic_card_no' => $v['ic_card_no'], 'vin' => $v['vin'], 'point' => $v['point'], 'dlr_code' => $dlr_code, 'order_id' => $v['order_code'], 'bus_code' => $bus_code, 'bus_scene_code' => $bus_scene_code, 'remark' => $remark, 'point_type' => $point_type];
                    $res    = $car->addPoint($params);
                    var_dump($res);
                    $err_data = [
                        'is_enable'    => 0,
                        'send_note'    => json_encode($params),
                        'receive_note' => json_encode($res),
                    ];
                    $model->saveData($err_data, ['id' => $v['bid']]);
//                    redis($redis_name.$v['order_code'],1,300);
                    $log_data = array(
                        'type'         => "add_point",
                        'order_code'   => $v['order_code'],
                        'send_note'    => json_encode_cn($params),
                        'receive_note' => json_encode_cn($res),
                    );
                    if ($res) {
                        $log_data['is_success'] = 'success';
                    } else {
                        $log_data['is_success'] = 'fail';
                    }
                    $log_model->insertData($log_data);
                }


            }
        }

    }

    //部分退积分+积分充值 9部分退积分10赠送积分
    public function some_order_point()
    {
        $order_err_model = new BuOrderErrList();
        $point_model     = new BuOrderPoint();
        $order_err_where = ['type' => ['in', [9, 10]], 'is_enable' => 1];
        $order_err_list  = $order_err_model->getList(['where' => $order_err_where]);
        $log_model       = new DbLog();
        if ($order_err_list) {
            foreach ($order_err_list as $v) {
                if ($v['type'] == 9) {
                    $point_order = $point_model->getOne(['where' => ['order_code' => $v['order_code'], 'point_type' => 1, 'is_enable' => 1]]);
                    $Car         = new Carer();
                    if ($point_order) {
                        if ($v['anti'] == 1) {
                            $data      = array(
                                'ic_card_no' => $point_order['ic_card_no'],
                                'vin'        => $point_order['vin'],
                                'order_id'   => $point_order['point_order_code'],
                                'dlr_code'   => $point_order['dlr_code'],
                                'deal_man'   => $point_order['dlr_code'],//处理人
                            );
                            $point_res = $Car->antiBalance($data);
                            $log_data  = array(
                                'type'         => 'antiBalance',
                                'modifier'     => '手工退',
                                'send_note'    => json_encode_cn($data),
                                'receive_note' => json_encode_cn($point_res),
                            );
                            echo $log_data['receive_note'];
                            if ($point_res) {
                                $log_data['is_success'] = 'success';
                            } else {
                                $log_data['is_success'] = 'fail';
                            }
                            $res = $log_model->insertData($log_data);
                        }
                        if ($v['r_dedu'] == 1) {
                            $is_cc_ok = 0;
                            if (!$v['point']) {
                                $new_point = $point_order['point'];
                            } else {
                                $new_point = $v['point'];

                            }
                            $data = array(
                                'vin'        => $point_order['vin'],
                                'ic_card_no' => $point_order['ic_card_no'],
                                'order_id'   => $point_order['point_order_code'] . '1',
                                'dlr_code'   => $point_order['dlr_code'],
                                'deal_man'   => $point_order['dlr_code'],//处理人
                                'point'      => $new_point,
                                'dlr_point'  => 0,
                                'remark'     => $point_order['remark']
                            );
                            if ($v['member_type'] == 1) {
                                $data['member_type'] = 1;
                            }
                            $point_res = $Car->deductPoint($data);
                            $log_data  = array(
                                'type'         => 'integral',
                                'send_note'    => json_encode_cn($data),
                                'receive_note' => json_encode_cn($point_res),
                            );
                            echo $log_data['receive_note'];
                            if ($point_res == 'ok') {
                                $log_data['is_success'] = 'success';
                            } else {
                                $log_data['is_success'] = 'fail';
                                $is_cc_ok               = 1;
                            }
                            $point_data = [
                                'order_code'       => $point_order['order_code'],
                                'point_order_code' => $data['order_id'],
                                'vin'              => $data['vin'],
                                'ic_card_no'       => $data['ic_card_no'],
                                'dlr_code'         => $data['dlr_code'],
                                'creator'          => '手工-ds',
                                'point_type'       => $point_order['point_type'],
                                'all_point'        => $point_order['all_point'],
                                'point'            => $data['point'],
                                'use_state'        => $is_cc_ok + 1,
                                'remark'           => $data['remark'],
                                'send_note'        => json_encode($data),
                                'back_json'        => json_encode_cn($point_res),
                            ];
                            $point_model->insertData($point_data);
                            if ($point_data['use_state'] == 2) {
                                $order_model = new BuOrder();
                                $res         = $order_model->saveData(['is_cc_ok' => 1], ['order_code' => $point_data['order_code']]);
                                Logger::error('net_order_usepoint', ['r' => $res, 'sql' => $order_model->getLastSql()]);
                            }
                            $point_model->saveData(['is_enable' => 0], ['point_order_code' => $point_order['point_order_code']]);//把原来的弄成不可用
                            $res = $log_model->insertData($log_data);
                        }

                    }
                }
                if ($v['type'] == 10) {
                    $car_no         = $v['ic_card_no'];
                    $vin            = $v['vin'];
                    $order_no       = $v['order_code'];
                    $point          = $v['point'];
                    $dlr_code       = $v['dlr_code'];
                    $bus_code       = 'JPSC';
                    $bus_scene_code = 'WXSF';
                    $point_type     = $v['point_type'] ?? 1;//1专营店送厂家积分。2厂家送厂家积分
                    $remark         = $v['remark'];
                    $car            = new Carer();
                    $params         = ['ic_card_no' => $car_no, 'vin' => $vin, 'point' => $point, 'dlr_code' => $dlr_code, 'order_id' => $order_no, 'bus_code' => $bus_code, 'bus_scene_code' => $bus_scene_code, 'remark' => $remark, 'point_type' => $point_type];
                    $res            = $car->addPoint($params);
                    $log_data       = array(
                        'type'         => 'add_point',
                        'send_note'    => json_encode_cn($params),
                        'receive_note' => json_encode_cn($res),
                    );
                    echo $log_data['receive_note'];
                    if ($res == 'ok') {
                        $log_data['is_success'] = 'success';
                    } else {
                        $log_data['is_success'] = 'fail';
                    }
                    $res = $log_model->insertData($log_data);
                }

            }
        }

    }

    public function test_em()
    {
        $order_m    = new BuOrder();
        $order_code = input('order_code', 'GWSM202112140900081524180');
        $order      = $order_m->getOne(['where' => ['order_code' => $order_code]]);
        if ($order['pre_point'] > 0 && !empty($order['pre_use_money'])) {
            var_dump($order['pre_point']);
            var_dump($order['pre_use_money']);
            echo 2222;
        } else {
            echo 3333;
        }
    }

    public function test_po()
    {
        $port_order = \app\common\port\connectors\Order::create('order')->statusOrder(
            [
                "order_no"     => "FS0c502020211218153639419317",
                "order_status" => 700,
            ]
        );
        var_dump($port_order);
    }

    public function test_pv_str()
    {
        $res               = strpos("PV2002020091518622", "PV800");
        $car['ic_card_no'] = "PV8002020091518622";
        if (strripos($car['ic_card_no'], "PV") === 0 && strpos($car['ic_card_no'], "PV800") === false) {
            echo 'feibaoke';
        } else {
            echo "baoke";
        }
    }

    public function test_member_user()
    {
        $member_id = input('member_id');
        $res       = Member::create("member")->member($member_id);
        var_dump($res);
    }


    //删除京东订单重新下单
    public function test_del_jd_order()
    {
        $order_code        = input('order_code');
        $order_jd_model    = new BuOrderJd();
        $order_goods_model = new BuOrderCommodity();
        $order_jd          = $order_jd_model->getOne(['where' => ['shop_order_id' => $order_code, 'is_enable' => 1]]);
        if ($order_jd) {
            $res = $order_jd_model->saveData(['is_enable' => 0, 'modifier' => 'hand_d'], ['id' => $order_jd['id']]);
            var_dump($res);
            $res = $order_goods_model->saveData(['third_order_id' => '', 'modifier' => 'hand_d'], ['third_order_id' => $order_jd['order_id']]);
            var_dump($res);
        } else {
            echo "订单不存在";
        }
    }

    //修改订单商品表信息
    public function change_order_goods()
    {
        $ids        = input('id', '');
        $data       = input('post.');
        if (!$ids) {
            die('缺少参数');
        }
        $where = ['is_enable' => 1];
        $idArr = explode(',', $ids);
        $where['id'] = ['in', $idArr];
        $goods_model = new BuOrderCommodity();
        unset($data['id']);
        unset($data['order_code']);
        unset($data['user_data']);
        unset($data['user_token']);

        $res = $goods_model->saveData($data, $where);
        print_json($res, $goods_model->getLastSql(), $data);
    }

    //修改订单商品表信息--新增order_code
    public function change_order_goods_oc(){
        $order_code = input('order_code','');
        $ids =  input('id','');
        $data = input('post.');
        if(!$order_code && !$ids){
            die('缺少参数');
        }
        $where = ['is_enable'=>1];
        if($order_code){
            $where['order_code'] = $order_code;
        }
        if($ids){
            $where['id'] = $ids;
        }
        $goods_model =  new BuOrderCommodity();
        unset($data['id']);
        unset($data['order_code']);
        unset($data['user_data']);
        unset($data['user_token']);

        $res =  $goods_model->saveData($data,$where);
        print_json($res,$goods_model->getLastSql(),$data);
    }

    //修改订单表信息
    public function change_order_info()
    {
        $order_code = input('order_code', '');
        $ids        = input('ids', '');
        $data       = input('post.');
        if (!$order_code && !$ids) {
            die('缺少参数');
        }
        $where = [];
        if ($order_code) {
            $where['order_code'] = $order_code;
        }
        if ($ids) {
            $idArr       = explode(',', $ids);
            $where['id'] = ['in', $idArr];
        }
        $goods_model = new BuOrder();
        unset($data['ids']);
        unset($data['order_code']);
        unset($data['user_data']);
        unset($data['user_token']);
        $res = $goods_model->saveData($data, $where);
        print_json($res, $goods_model->getLastSql());
    }
    //修改sku表
    public function change_sku_info(){
        $commodity = input('goods_id','');
        $ids =  input('ids','');
        $data = input('post.');
        if(!$commodity && !$ids){
            die('缺少参数');
        }
        $where = [];
        if($commodity){
            $where['commodity_id'] = ['in',$commodity];
        }
        if($ids){
            $idArr = explode(',', $ids);
            $where['id'] = ['in',$idArr];
        }
        $goods_model =  new DbCommoditySku();
        unset($data['ids']);
        unset($data['goods_id']);
        unset($data['user_data']);
        unset($data['user_token']);
        $res =  $goods_model->saveData($data,$where);
        print_json($res,$goods_model->getLastSql());
    }

    public function del_af_order()
    {
        $id       = input('id', 124949);
        $af_model = new DbAfterSaleOrders();
        $res      = $af_model->where(['id' => $id])->delete();
        print_json($res, $af_model->getLastSql());
    }

    //查询京东下单单号
    public function get_order_jd_info()
    {
        $order_code     = input('shop_order_code');
        $id             = input('order_id');
        $order_jd_model = new BuOrderJd();
        $where          = ['is_enable' => 1];
        if ($order_code) {
            $where['shop_order_id'] = $order_code;
        }
        if ($id) {
            $where['order_id'] = $id;
        }
        $order_jd = $order_jd_model->getOne(['where' => $where]);
        print_json($order_jd);

    }

    public function test_vin_rule()
    {
        $vin       = 'LGBM4AE43MS475619';
        $vin_model = new DbVinRule();
        $sys_model = new DbSystemValue();
        $car_model = new DbCarSeries();
        $digit     = 4;
        $code      = substr($vin, $digit - 1, 1);
        if ($code != 'G') {
            $car_name = $vin_model->getOne(array('where' => array('digit' => $digit, 'code' => $code, 'is_enable' => 1)));
            if ($car_name) {
                $car_name = $car_name['name'];
            } else {
                return false;
            }
        } else {
            $code = substr($vin, 4 - 1, 1);
            if ($code == 1) {
                $car_name = '颐达';
            } else {
                $car_name = '骐达';
            }
        }
        var_dump($car_name);
        $value_code = $sys_model->getOne(array('where' => array('is_enable' => 1, 'county_name' => $car_name, 'value_type' => 1)));
        $digit      = 6;
        $code       = substr($vin, $digit - 1, 1);
        $dis_code   = $vin_model->getOne(array('where' => array('digit' => $digit, 'code' => $code, 'is_enable' => 1)));
        var_dump($dis_code['name']);
        $digit = 10;
        $code  = substr($vin, $digit - 1, 1);
        $age   = $vin_model->getOne(array('where' => array('digit' => $digit, 'code' => $code, 'is_enable' => 1)));
        var_dump($age['name']);
        $car = $car_model->getOne(array('where' => array('car_series_code' => $value_code['value_code'], 'displacement' => $dis_code['name'], 'age' => $age['name'], 'is_enable' => 1)));
        var_dump($car['id']);
        die();

    }

    //给客户赠送同样多张卡券``
    public function test_to_user_card()
    {
        $user_ids     = input('user_ids');
        $user_id_arr  = explode(',', $user_ids);
        $card_id      = input('card_id');
        $count        = input('count', 1);
        $card_r_model = new BuCardReceiveRecord();
        $data         = [];
        foreach ($user_id_arr as $v) {
            for ($i = 1; $i <= $count; $i++) {
                $data[] = [
                    'user_id'   => $v,
                    'card_id'   => $card_id,
                    'creator'   => '活动导入',
                    'dlr_code'  => 'GWSM',
                    'card_code' => $this->_getOrderNo('GWSMc' . date('YmdHis'), 3),
                    'act_id'    => 'ac_hddr_' . date('m-d'),
                ];
                sleep(1);
            }
        }
        var_dump($data);
        $res = $card_r_model->insertAll($data);
        var_dump($res);
    }

    //修改数据库下单订单号 下单时候并没有拆单，要占库存的时候需要用拆单order_code去查询
    public function test_change_order_jd()
    {
        $order_code     = input('order_code');
        $new_order_code = input('new_order_code');
        $order_jd_model = new BuOrderJd();
        $res            = $order_jd_model->saveData(['shop_order_id' => $new_order_code, 'modifier' => 'hand_d'], ['shop_order_id' => $order_code]);
        var_dump($res);
    }

    //缺少京东标识补充JD+修改运费+补充京东订单号
    public function test_change_order_goods()
    {
        $id                = input('ids');
        $mail_price        = input('mail_price');
        $actual_money      = input('actual_use_money');
        $c_type            = input('c_type', 1);//1 修改京东，2修改运费3,修改实际使用价格,4修改京东单号
        $third_order_id    = input('t_order_id', '');//1 修改京东，2修改运费3,修改实际使用价格
        $order_goods_model = new BuOrderCommodity();
        $data              = ['modifier' => 'hand_SUP', 'last_updated_date' => date('Y-m-d H:i:s')];
        if ($c_type == 1) {
            $data = ['supplier' => 'JD'];
        }
        if ($c_type == 2) {
            $data = ['mail_price' => $mail_price];
        }
        if ($c_type == 3) {
            $data = ['actual_use_money' => $actual_money];
        }
        if ($c_type == 4) {
            $data = ['third_order_id' => $third_order_id];
        }
        $res = $order_goods_model->saveData($data, ['id' => ['in', $id]]);
        var_dump($res);
    }



    //更新专营店
    public function up_order_dlr()
    {
        $order_model  = new BuOrder();
        $order_code   = input('order_code');
        $new_dlr_code = input('new_dlr_code');
        $res          = $order_model->save(['dlr_code' => $new_dlr_code, 'modifier' => 'hand_uod'], ['order_code' => $order_code]);
        var_dump($res);

    }

    //取消售后单
    public function del_after_order()
    {
        $af_model          = new DbAfterSaleOrders();
        $afs_id            = input('afs_id', 0);
        $order_id          = input('order_id', 0);
        $status            = input('status', 0);
        $after_status      = input('af_status', 0);
        $cashier_refund_no = input('cashier_refund_no', '');
        if (!$order_id) {
            die('no_order');
        }
        $data = [
            'is_enable' => $status,
            'modifier'  => 'hand_del',
        ];
        if (!empty($after_status)) {
            $data['afs_status'] = $after_status;
        }
        if (!empty($cashier_refund_no)) {
            $data['cashier_refund_no'] = $cashier_refund_no;
        }
        if (!empty($afs_id)) {
            $res = $af_model->where(['id' => $afs_id])->update($data);
        } else {
            $res = $af_model->saveData($data, ['order_id' => $order_id]);
        }
        var_dump($res);
    }

    //测试通知事件
    public function test_notice()
    {
        $no_data   = array(
            'id'                            => 60686,
            'cashier_settlement_no'         => '*******************',
            'cashier_appid'                 => 'SC07',
            'total_fee'                     => 65400,
            'body'                          => '新奇骏至尊保养套餐（2014/02~最新年款新奇骏2.0L/',
            'settlement_fee'                => 65400,
            'settlement_net_fee'            => 65008,
            'settlement_commission_fee'     => 392,
            'settlement_can_refund_balance' => 65008,
            'intention_account_id'          => 'F2208',
            'confirm_account_id'            => 'F2208',
            'notify_url'                    => 'https://wxstore.dongfeng-nissan.com.cn/net_small/pay_plat/settlement',
            'settlement_state'              => 4,
            'finish_date'                   => '2022-02-28 14:30:07',
            'finish_refund_date'            => NULL,
            'cnsmr_seq_no'                  => 'N366952202281677375531',
            'settlement_front_serial_id'    => '****************',
            'settlement_serial_id'          => 'N366952202281677375531',
            'order_id'                      => 'F2208202202261940426257026',
            'cashier_trade_no'              => '*******************',
            'cashier_refund_no'             => '',
            'remark'                        => '交易成功',
            'settlement_remark'             => '商城分账',
            'appid'                         => '495ba405-6efa-4aba-9ccf-ed1ee816712e',
            'is_enable'                     => 1,
            'creator'                       => '',
            'modifier'                      => '',
            'created_date'                  => '2022-02-26 19:40:55',
            'modified_date'                 => '2022-02-28 14:30:07',
            'event_type'                    => 'SETTLEMENT_SUCCESS',
            'nonce_str'                     => '59NS9iMy88nzsGLA',
            'sign'                          => 'A088CF1395D8153E5045A911C4468CF2',
        );
        $pay_menet = new Payment();
        $check     = $pay_menet::create('payment')->checkSign($no_data);
        var_dump($check);

    }

    //测试支付通知事件
    public function test_notify_pay()
    {
        $no_data   = array(
            'event_type'        => 'PAY_SUCCESS',
            'notify_uuid'       => '********-611e-4471-94ea-3ec6a3208343',
            'created_date'      => '2022-03-02 10:59:27',
            'payment_method'    => '3',
            'center_account_id' => 'PL02',
            'pay_mode'          => 'JSAPI',
            'total_fee'         => '75200',
            'order_id'          => 'B1036202203021059092011723',
            'state'             => '2',
            'state_name'        => '已支付',
            'remark'            => '小程序',
            'attach'            => 'COkOosfg0B4YsxSWE33r0P6tGOSg0Mp GKiQBjRbKWQ=',
            'transaction_id'    => '4200001354202203023127626846',
            'paid_time'         => '**********',
            'result_code'       => 'SUCCESS',
            'nonce_str'         => 'AzyFBzOsnXFNikye',
            'sign'              => 'D1C442FBB4D9B715604ECAF19126E2DA',
        );
        $pay_menet = new Payment();
        $check     = $pay_menet::create('payment')->checkSign($no_data);
        var_dump($check);
    }

    //通过用户ID获取用户信息
    public function get_user_info_by_id()
    {
        $id = input('user_id');
        if (!$id) {
            die('输入用户ID');
        }
        $user_model = new DbUser();
        $user_info  = $user_model->getOneByPk($id);
        var_dump($user_info);
        if (isset($user_info['phone'])) {
            echo "用户手机：" . $user_info['phone'] . '--新用户注册手机：' . $user_info['mid_phone'];
        }
    }

    //把卡券不可用--业务需求调用
    public function test_change_card_anis()
    {
        $card_r_model        = new BuCardReceiveRecord();
        $card_code           = input('card_codes', "'GWSMc20220422162204688','GWSMc20220422162133248','GWSMc20220422162029417','GWSMc20220422161846713','GWSMc20220422155950839','GWSMc20220422145932429','GWSMc20220422134927585','GWSMc20220422130327604','GWSMc20220422130252420','GWSMc20220422124818826','GWSMc20220422124740571','GWSMc20220422124436479','GWSMc20220422123156756','GWSMc20220422122749290','GWSMc20220422122025786','GWSMc20220422121808480','GWSMc20220422121806065','GWSMc20220422121703237','GWSMc20220422121415450','GWSMc20220422121252461','GWSMc20220422152744157','GWSMc20220422124529672','GWSMc20220422123521842'");
        $is_enable           = input('is_enable', 1);
        $status              = input('status', 1);
        $is_del              = input('is_del', 0);
        $source              = input('source', '');
        $act_id              = input('act_id', '');
        $qusong              = input('qusong', '');
        $dlr_code            = input('dlr_code', '');
        $validity_date_start = input('validity_date_start', '');
        $validity_date_end   = input('validity_date_end', '');
        $user_id             = input('user_id', '');
        $where               = ['card_code' => ['in', $card_code]];
        if ($is_del == 1) {
            $res = $card_r_model->where($where)->delete();
        } elseif (!empty($qusong)) {
            $card_data  = $card_r_model->where($where)->column('act_id', 'id');
            $order_list = BuOrder::where(['id' => ['in', $card_data]])->column('pick_up_order_code', 'id');
            $i          = 0;
            foreach ($card_data as $k => $v) {
                $i++;
                $res = $card_r_model->saveData(['consume_order_code' => $order_list[$v]], ['id' => $k]);
            }
            dd($i);
        } else {
            $data = ['is_enable' => $is_enable, 'status' => $status, 'last_updated_date' => date('Y-m-d H:i:s'), 'modifier' => 't_c_card'];
            if (!empty($act_id)) $data['act_id'] = $act_id;
            if (!empty($source)) $data['source'] = $source;
            if (!empty($dlr_code)) $data['dlr_code'] = $dlr_code;
            if (!empty($validity_date_start)) $data['validity_date_start'] = $validity_date_start;
            if (!empty($validity_date_end)) $data['validity_date_end'] = $validity_date_end;
            if (!empty($user_id)) $data['user_id'] = $user_id;
            $res = $card_r_model->saveData($data, $where);
        }
        var_dump($res);
        echo $card_r_model->getLastSql();
    }

    //修改卡券为未核销
    public function change_card_status_card_code()
    {
        $ids = input('card_r_ids', "5787678,5787628,5787661,5787696,5787783,5787784,5787651,5787702,5787807,5787809,5787776,5787778,5787649,5787715,5787733,5787805,5787610,5787615,5787675,5787771,5787662,5787817,5787798,5787818,5787764,5787617,5787619,5787631,5787684,5787647,5787745,5787787,5787652,5787794,5787603,5787671,5787634,5787659,5787633,5787713,5787813,5787676,5787618,5787667,5787694,5787714,5787639,5787669,5787799");
        if (!$ids) {
            die("没有数据");
        }
        $type         = input('type', 1);
        $card_r_model = new BuCardReceiveRecord();
        if ($type == 1) {
            $where = ['id' => ['in', $ids]];
            $data  = ['status' => 1, 'consume_date' => '', 'consume_dlr_code' => ''];
            $res   = $card_r_model->saveData($data, $where);
            var_dump($res);
        } elseif ($type == 2) {
            $where = ['id' => ['in', $ids, 'status' => 1]];
            $list  = $card_r_model->getList(['where' => $where]);
            foreach ($list as $k => $v) {
                $s_data = ['card_code' => $v['card_code'] . $k];
                $res    = $card_r_model->saveData($s_data, ['id' => $v['id']]);
                var_dump($res);
                echo "<br/>";
            }
        } else {
            echo "999";
        }
    }

    public function two_back_point()
    {
        $car  = new Carer();
        $data = [
            'oneid'           => '866e0111a66244fbb7cb9d54ac5426c27389',
            'dlr_code'        => 'T9932',
            'order_id'        => 'AP2022042910035109625001',
            'return_order_id' => 'PZRP20220429100653293',
            'return_points'   => 6,
            'request_id'      => '999-888',
        ];
        $msg  = $point_res = $car->returnPzPoint($data);
        var_dump($msg);

    }

//{"DCRM_CODE":"WXDCC","INTERFACE_CODE":"PostDeductCluePoint","CARD_NO":"PVQK2021081245975","DLR_CODE":"T9932","BALANCE_BILL_CODE":"VN22052611280914565173","SOURCE_BILL_TIME":"2022-05-26 11:28:11","BUS_CODE":"QCHYSC","BUS_SCENE_CODE":"QCHYSC-JFXF","BILL_TOTAL_COST":"1.00","USE_CASH":"0.00","DEAL_MAN":"VENUCIA","USE_CLUE_POINT":"10.00","BRAND_CODE":2}
    public function test_qc_de_point()
    {
        $Car       = new Carer();
        $data      = array(
            'ic_card_no'     => "PVQK2021081245975",
            'order_id'       => 'VN22052611280914565173',
            'dlr_code'       => 'T9932',
            'deal_man'       => 'VENUCIA',//处理人
            'point'          => 2,
            'dlr_point'      => 0,
            'member_type'    => 2,
            'remark'         => "启辰测试扣潜客积分",
            'bus_scene_code' => "QCHYSC-JFXF",
            'bus_code'       => "QCHYSC",
        );
        $point_res = $Car->deductCluePoint($data);
        var_dump($point_res);
    }

    public function test_save_order()
    {
        $save_data = ['cashier_trade_no' => 123];
        $res       = BuOrder::update($save_data, ['id' => 2126305]);
        dd($res);
    }

    public function finish_order()
    {
        $order_code = input('order_code');
        $res        = order_finish($order_code, '');
        var_dump($res);
    }

    //修改订单的店.
    public function change_dd_dlr_code()
    {
        $order_code  = input('order_code');
        $dd_dlr_code = input('dd_dlr_code');
        $res         = BuOrder::update(['dd_dlr_code' => $dd_dlr_code], ['order_code' => $order_code]);
        dd($res);
    }

    //修改订单商品的店.
    public function change_dd_goods_code()
    {
        $id    = input('ids');
        $order = input('order_code');
        if ($id) {
            $where['id'] = ['in', $id];
        }
        if ($order) {
            $where['order_code'] = $order;
        }
        $dd_dlr_code = input('dd_dlr_code');
        $res         = BuOrderCommodity::update(['dd_dlr_code' => $dd_dlr_code], $where);
        dd($res);
    }


    public function spp_order()
    {
        $order_code = input('order_code');
        $cc         = new Common();
        $res        = $cc->spilt_order($order_code);
        var_dump($res);
    }

    public function sspi_order()
    {
        $order_code        = input('order_code');
        $order_model       = new BuOrder();
        $order_goods_model = new BuOrderCommodity();
        $order_now         = $order_model->getOne(['where' => ['order_code' => $order_code]]);
        $order_goods_list  = $order_goods_model->getList(['where' => ['parent_order_code' => $order_code, 'mo_sub_id' => 0, 'is_enable' => 1]]);
        $cc                = new NetOrder();
        $res               = $cc->orderUsePoint($order_now, $order_goods_list, 0);//计算积分与现金到每个商品
        print_json($res);

    }


    // 批量修改拆单分摊金额
    public function batch_upd_order_actual_use_money()
    {
        $order_commodity_model = new BuOrderCommodity();
        $orderCodes            = $order_commodity_model->alias('a')
            ->join('t_bu_order b', "a.order_code=b.order_code")
            ->field('a.parent_order_code')
            ->where('mo_id', '<>', 0)
            ->where('count', '>', 1)
            ->whereNotIn('b.order_status', [1, 3, 8, 18])
            ->select();
        $order_model           = new BuOrder();
        $cc                    = new NetOrder();
        $res                   = [];
        foreach ($orderCodes as $key => $item) {
            $order_code       = $item['parent_order_code'];
            $order_now        = $order_model->getOne(['where' => ['order_code' => $order_code]]);
            $order_goods_list = $order_commodity_model->getList(['where' => ['parent_order_code' => $order_code, 'mo_sub_id' => 0, 'is_enable' => 1]]);
            $res[]            = $cc->orderUsePoint($order_now, $order_goods_list, 0);//计算积分与现金到每个商品
        }
        print_json($res);
    }


    public function add_pz_point()
    {
        $params = input('get.');
        $car    = new Carer();
        $data   = [
            'oneid'           => $params['oneid'],
            'dlr_code'        => 'PZ1ASM',
            'scene_code'      => $params['scene_code'] ?? '',
            'scene_sub_class' => $params['scene_sub_class'] ?? '',
            'point'           => $params['point'] ?? 100000,
        ];
        $res    = $car->addPzPoint($data);
        var_dump($res);
    }


    //把订单结算状态改成1，方便重新发起2.老订单  批量处理结算单状态
    public function hand_set_set_one()
    {
        $set_model = new BuOrderSettlement();
        $id        = input('ids');
        $state     = input('state', 1);
        $res       = $set_model->saveData(['state' => $state], ['id' => ['in', $id]]);
        var_dump($res);
    }

    //修改订单来源方便结算[0 => '', 1 => '商品订单', 2 => '拼团订单', 3 => '套装', 4 => '礼包', 5 => '到店有礼', 6 => '积分-日产', 7 => "会员日", 8 => '锦尚阳光口罩', 11 => 'CCS订单', 12 => '虚拟商品订单', 13 => '电子卡券商品订单',14=>'积分-启辰',15=>'到店备件',16=>'保养套餐-老友惠保养套餐',17=>'保养套餐-心悦保养套餐',18=>'保养套餐-五年双保升级权益套餐',19=>'保养套餐-其他',20=>'到店代金券',21=>'到店电子券'];  @Rondo TANG
    public function change_order_source()
    {
        $order_code   = input('order_code');
        $order_source = input('order_source');

        $order_model = new BuOrder();
        $res         = $order_model->saveData(['order_source' => $order_source], ['order_code' => $order_code]);
        var_dump($res);
    }


    //修改ms_order_code;
    public function change_ms_order_code()
    {
        $order_code      = input('order_code');
        $ms_order_code   = input('ms_order_code');
        $link_order_code = input('link_order_code');
        $order_model     = new BuOrder();
        $data            = [
            'ms_order_code'    => $ms_order_code,
            'cashier_trade_no' => $ms_order_code,
            'link_order_code'  => $link_order_code,
        ];
        $where           = ['order_code' => $order_code];
        $order           = $order_model->getOne(['where' => $where]);
        $order_data      = [
            'ms_order_code'    => $order['ms_order_code'],
            'cashier_trade_no' => $order['cashier_trade_no'],
            'link_order_code'  => $order['link_order_code'],
        ];
        echo json_encode($order_data);
        $res = $order_model->saveData($data, $where);
        var_dump($order_model->getLastSql());
    }

    public function test_pz_use_point()
    {
        $js = '{"oneid":"866e0111a66244fbb7cb9d54ac5426c27389","dlr_code":"PZ1ASM","remark":"积分购买精品","order_id":"AP2211141437316711","use_elec_points":"10","oli_card_list":[],"request_id":"AP221114143731671598151"}';

        $js_arr    = json_decode($js, true);
        $CARER     = new Carer();
        $point_res = $CARER->usePzPoint($js_arr);
        dd($point_res);
    }

    public function test_time_diss()
    {
        $bigTime   = date('Y-m-d H:i:s');
        $smallTime = '';
        $res       = floor((strtotime($bigTime) - strtotime($smallTime)) / 86400);
        dd($res);
    }

    public function change_actual_price()
    {
        $order_codes          = input("order_codes", "'GWAPP220902214419733','GWAPP220907204110662','GWAPP220908200312881','GWAPP220911191935196','GWAPP220914113503644','GWAPP220914131041529','GWSM220905090620415','GWSM220909145831912','GWSM220909151236658','GWAPP220904202113434','GWAPP220908143334259','GWAPP220908195553153','GWAPP220910101600386','GWAPP220903113826437','GWAPP220910145808819','GWAPP220911111223686','GWAPP220911193653538','GWAPP220911231648335','GWAPP220913101513708','GWAPP220907142337790','GWAPP220913111449580','GWAPP220914075721383','GWAPP220902212705566','GWAPP220903111416256','GWAPP220903125120890','GWAPP220905120007012','GWAPP220906152516053','GWAPP220906162542321','GWAPP220907085846781','GWAPP220908072420150','GWAPP220909000847922','GWAPP220909003255902','GWAPP220911084226119','GWAPP220911193459491','GWAPP220912134516464','GWAPP220909061459837-09','GWAPP220909230815392-09','GWSM220903081651319','GWSM220901212917319','GWAPP220902130440488','GWAPP220903132754894','GWAPP220904082631610','GWAPP220905061153385','GWAPP220906053022122','GWAPP220907145734168','GWAPP220908102244803','GWSM220908181138040','GWAPP220908184156681','GWAPP220909204646511','GWSM220901212316798','GWSM220901212325335','GWSM220902085244358','GWSM220903162240326','GWSM220904213434547','GWSM220906133131324','GWSM220907154054667','GWSM220908164827884','GWSM220909092804815','GWSM220909134231400','GWSM220913083949337','GWSM220912131540613','GWSM220903113102427','GWSM220903213230563','GWSM220904121802652','GWSM220907172452212','GWSM220912124041944','GWSM220901225908848','GWSM220901233542718','GWSM220902111536034','GWSM220902112616654','GWSM220902164344720','GWSM220904074614606','GWSM220905220801725','GWSM220906163439717','GWSM220906223337843','GWSM220907185902649','GWSM220908211055794','GWSM220909102140290','GWSM220909122823289','GWSM220909150128722','GWSM220910005708464','GWSM220910122111237','GWSM220911154241565','GWSM220911165802867','GWSM220912213543192','GWAPP220914095954677','GWAPP220914102600335','GWSM220914084904591','GWAPP220913105808571','GWAPP220913223141158','GWSM220913180023058','GWSM220913152618245','GWSM220913161403158','GWSM220914005156770'");
        $order_goods_model    = new BuOrderCommodity();
        $data                 = [
            'last_updated_date' => date('Y-m-d H:i:s'),
            'modifier'          => 'c_a_p',
        ];
        $data['actual_price'] = ['exp', sprintf("b_act_price")];
        $res                  = $order_goods_model->saveData($data, sprintf("order_code in (%s)", $order_codes));
        var_dump($res);
        echo $order_goods_model->getLastSql();
        die();

    }

//重新扣结算PZ积分
    public function re_use_pz_point()
    {
        $user = input('user');
        $this->check_user($user);
        $order_code  = input('order_code');
        $remark      = input('remark', '积分购买精品');
        $modifier    = input('modifier', 'hand_re_use');
        $order_model = new BuOrder();
        $net_order   = new NetOrder();
        $user_model  = new DbUser();
        $order       = $order_model->getOne(['where' => ['order_code' => $order_code]]);
        $user_info   = $user_model->getOneByPk($order['user_id']);
        $res         = $net_order->settPointPz($order['order_code'], $remark, $user_info, $modifier, $order['dlr_code']);
        var_dump($res);

    }

    public function change_order_enable()
    {
        $order_code = input('order_code');
        $is_enable  = input('is_enable', 1);
        $user       = input('user');
        $this->check_user($user);
        if ($order_code) {
            $order_model = new BuOrder();
            $res         = $order_model->saveData(['is_enable' => $is_enable, 'last_updated_date' => date('Y-m-d H:i:s'), 'modifier' => 'hand_e'], ['order_code' => ['in', explode(',', $order_code)]]);
            var_dump($res);

        }
    }

    public function test_pz_point()
    {
        $one_id   = input('one_id');
        $Car      = new Carer();
        $pz_point = $Car->pzPonit(['oneid' => $one_id]);
        dd($pz_point);

    }

    public function test_pz_point_r()
    {
        $order_code = input('order_code');
        $request_id = input('request_id');
        $one_id     = input('one_id');
        $CARER      = new Carer();
        if (!$request_id) {
            $point_model = new BuOrderPoint();
            $point       = $point_model->getOne(['where' => ['order_code' => $order_code]]);
            $post_json   = $point['send_note'];
            $post_data   = json_decode($post_json, true);
            $one_id      = $post_data['oneid'];
            $request_id  = $post_data['request_id'];
        }
        $use_r = $CARER->usePointPzR(['data_list' => [['oneid' => $one_id, 'request_id' => $request_id]]]);
        var_dump($use_r);

    }

    //修改订单号
    public function change_order_code()
    {
        $old_order_code = input('old_order_code');
        $new_order_code = input('new_order_code');
        $order_model    = new BuOrder();
        $res            = $order_model->saveData(['order_code' => $new_order_code, 'modifier' => 'hand_c_c'], ['order_code' => $old_order_code]);
        var_dump($res);
    }

    //修改订单状态--批量
    public function change_order_status()
    {
        $old_order_code = input('order_code');
        $status         = input('status', 2);
        $order_model    = new BuOrder();
        $order_code_arr = explode(',', $old_order_code);
        $res            = $order_model->saveData(['order_status' => $status, 'modifier' => 'hand_c_s'], ['order_code' => ['in', $order_code_arr]]);
        var_dump($res);
    }

    //1012 通过error批量新建京东商品
    public function test_cc_jd_order()
    {
        set_time_limit(0);
        $user = input('user');
        $this->check_user($user);
        $type              = input('type');
        $order_goods_model = new BuOrderCommodity();
        $order_model       = new BuOrder();
        $err_model         = new BuOrderErrList();
        $list_where        = ['is_enable' => 1, 'type' => $type];
        $err_list          = $err_model->getList(['where' => $list_where]);
        foreach ($err_list as $val) {
            $sku_redis  = 'lzx-test-cc-jd-order' . $val['order_code'];
            $redis_lock = getRedisLock($sku_redis, 3600 * 24);
            if ($redis_lock) {
                $jd_order_goods_arr = [];
                $order_goods_where  = ['parent_order_code' => $val['order_code'], 'mo_sub_id' => 0, 'is_enable' => 1, 'supplier' => 'JD'];
                $order_goods_list   = $order_goods_model->getList(['where' => $order_goods_where]);
                if ($order_goods_list) {
                    foreach ($order_goods_list as $vv) {
                        $order_jd_model = new BuOrderJd();
                        $order_jd       = $order_jd_model->getOne(['where' => ['is_enable' => 1, 'shop_order_id' => $vv['parent_order_code'], 'error_code' => '0001']]);
                        if (!$order_jd) {
                            $jd_order_goods_arr[] = $vv;
                        }
                    }
                }
                if ($jd_order_goods_arr) {
                    $order     = $order_model->getOne(['where' => ['order_code' => $val['order_code']]]);
                    $net_order = new NetOrder();
                    $jd_order  = $net_order->create_jd_order($jd_order_goods_arr, $order['receipt_address'], $order);//创建京东订单 2021-11-22 20:24:44
                    if (isset($jd_order['code'])) {
                        if ($jd_order['code'] <> 200) {
                            echo $val['order_code'] . '==' . $jd_order['code'] . "--" . $jd_order['msg'] . '<br/>';
                        }
                    } else {
                        echo $val['order_code'] . '==' . $jd_order['code'] . "--" . $jd_order['msg'] . '<br/>';

                    }
                }
                $err_model->saveData(['is_enable' => 0], ['id' => $val['id']]);
            }
            sleep(1);
        }
    }

    //删除京东端订单
    public function cancel_jd_order()
    {
        $order_jd_id = input('id');
        $type        = input('type', 1);
        $user        = input('user');
        $this->check_user($user);
        $order_id = input('order_id');
        if ($type == 1) {
            $sdk_order = new JdOrderN();
            $res       = $sdk_order->cancel($order_id);
            dd($res);
        }
        if ($type == 2) {
            $jd_order_model = new BuOrderJd();
            $res            = $jd_order_model->saveData(['is_enable' => 1], ['id' => $order_jd_id]);
            dd($res);
        }

    }

    public function test_jd_address()
    {
        $address   = input('addr');
        $sdk_order = new JdOrderN();
        $res       = $sdk_order->jdAddressId($address);
        var_dump($res);
    }


    //插入order_point 表，异常订单，没有再该表中
    public function into_order_point()
    {
        $order_model = new BuOrder();
        $order_code  = input('order_code');
        $type        = input('point_type', 1);//1总部2店
        $where       = array('order_code' => $order_code);
        $order       = $order_model->getOne(array('where' => $where));
        if (!$order) die('订单不存在');
        $dlr_code    = input('dlr_code', $order['dd_dlr_code']);
        $re_point    = input('re_point', $order['integral']);
        $is_cc_ok    = input('is_cc_ok', 1);
        $remark      = input('remark', '积分购买精品');
        $point_model = new BuOrderPoint();
        $point_data  = [
            'order_code'       => $order_code,
            'point_order_code' => $order_code . $type,
            'vin'              => $order['vin'],
            'ic_card_no'       => $order['ic_card_no'],
            'dlr_code'         => $dlr_code,
            'creator'          => 'h_into_p',
            'point_type'       => $type,
            'all_point'        => $order['integral'],
            'point'            => $re_point,
            'use_state'        => $is_cc_ok,
            'remark'           => $remark,
            'send_note'        => '',
            'back_json'        => '',
        ];
        $res         = $point_model->insertData($point_data);
        dd($res);
    }


    //
    public function go_qsche_e3scard()
    {
        $order_model = new BuOrder();
        //20230719  做退库存操作 5 冻结
        $sql        = sprintf("SELECT a.order_code,a.order_status,b.commodity_id,b.commodity_name,b.sku_info,c.card_id,c.`status`,c.last_updated_date,a.dlr_code from t_bu_order a
join t_bu_order_commodity b on a.order_code=b.order_code
join t_bu_card_receive_record c on a.id=c.act_id   and c.`status`=5
where a.order_source=24 and a.order_status not in (1,3,8,18)
and not EXISTS(SELECT 1 from t_e3s_qsc_order qso  where a.order_code=qso.order_code and qso.qsc_type =2 )
limit 1000");
        $order_list = $order_model->query($sql);
        if ($order_list) {
            foreach ($order_list as $v) {
                $MaintainService = new MaintainService();
                $mData           = $MaintainService->cardMessageE3s($v['dlr_code'], [
                    "INTERFACE_CODE" => "SavePartBackOrderMain",
                    "order_code"     => $v['order_code'],
                    "post_type"      => 1,
                ]);
                var_dump($mData);
                echo "<br/>";
            }
        }
        //end
        die();

        $order_code  = input('order_codes', "'GWAPP221005014419913','GWAPP221001163242467','GWAPP220930102457451','GWAPP220929105009293','GWSM220927164351433','GWAPP220926150036224','GWAPP220926100134603','GWAPP220924231503385','GWSM220924220714152','GWSM220924195608092','NVGWAPP220924173416916','NVGWAPP220924173116755','NVGWAPP220924102223222','NVGWAPP220924100721256','NVGWSM220923152929182','NVGWSM220923152445293','NVGWSM220923152325255','NVGWSM220923152203846','NVGWAPP220923151909765','NVGWSM220923104703819','NVGWSM220923102314588','NVGWAPP220923101615418','NVGWAPP220923101412627','NVGWAPP220922192422538','NVGWAPP220921143237156','NVGWAPP220920152853484','NVGWAPP220918164530380','NVGWSM220918163134564','NVGWSM220918161539129','NVGWAPP220918153319985','NVGWAPP220916172341548'");
        $type        = input('type', 2);//1扣库存2退库存
        $order_model = new BuOrder();
        $order_list  = $order_model->getList(['where' => ['order_code' => ['in', explode(',', $order_code)]]]);
        echo $order_model->getLastSql();
        if ($order_list) {
            foreach ($order_list as $v) {
                if ($type == 1) {
                    $MaintainService = new MaintainService();
                    $mData           = $MaintainService->cardMessageE3s($v['channel'], [
                        "INTERFACE_CODE" => "SavePartOrderMain",
                        "order_code"     => $v['order_code'],
                        "post_type"      => 1,
                    ]);
                    var_dump($mData);
                } else {
                    $MaintainService = new MaintainService();
                    $mData           = $MaintainService->cardMessageE3s($v['channel'], [
                        "INTERFACE_CODE" => "SavePartBackOrderMain",
                        "order_code"     => $v['order_code'],
                        "post_type"      => 1,
                    ]);
                    var_dump($mData);
                }
            }
        }


    }

    public function test_sett_com()
    {
        $sett       = new OrderSettle();
        $order_code = input('order_code', 1);
        $res        = $sett::orderList($order_code);

    }


    public function setCardReceiveRecordTest()
    {
        $ids = input('ids', '');
        if (empty($ids)) {
            dd('not id');
        }
        $re = (new BuCardReceiveRecord())->where(['id' => ['in', $ids]])->update(['modifier' => '修复' . rand(10, 99)]);
        dd($re);
    }

    public function testExpireRefund()
    {
        $now = input('now') or die(12421);
        $before = input('before') or die(321312);
        $is_refund = input('is_refund', 0);

        // 到期订单自动退款
        $order_model = new BuOrder();
        $where       = [
            ['exp', "(order_status = 7 and order_source in (20, 21, 24)) OR order_status in (2, 20, 21, 22)"],
            'is_enable' => 1,
        ];

        if ($is_refund == 1) {
            $order_model
                ->field('*')
                ->where($where)
                ->whereBetween('dq_time', [$before, $now])->chunk(
                    100, function ($arr) {
                    $afs_model = new DbAfterSaleOrders();
                    $order_ids = array_column($arr, 'id');
                    $afs_list  = $afs_model->where(['order_id' => ['in', $order_ids], 'is_enable' => 1])->order('id desc')->column('*', 'order_id');
                    foreach ($arr as $item) {
                        $lock = 'ExpireRefund:' . $item['order_code'];
                        if (!getRedisLock($lock, mt_rand(40, 80)) || ($item['promotion_source'] != 3 && $item['parent_order_type'] == 3)) {
                            continue;
                        }
                        $afs_info = $afs_model->where(['order_id' => $item['id'], 'is_enable' => 1])->order('id desc')->find();
                        if (!empty($afs_info) && !in_array($afs_info['afs_status'], [2, 3, 5, 8, 11])) {
                            continue;
                        }

                        $net_order                = new NetOrder();
                        $service_parent_order_key = 'service_parent_order' . $item['parent_order_code'];
                        if (!redis($service_parent_order_key)) {
                            $can_after = $net_order->can_after($item, $afs_list[$item['id']] ?? []);
                            if ($can_after['can_after'] == 1) {
                                if ($item['promotion_source'] == 3) {
                                    redis($service_parent_order_key, $item['parent_order_code'], 3600);
                                }
                            } else {
                                continue;
                            }
                        }

                        $data = [
                            'afs_service_id'    => $item['dlr_code'] . '1' . date('ymdHis') . mt_rand(1000, 9999),
                            'order_id'          => $item['id'],
                            'afs_type'          => 1,
                            'afs_reason'        => '订单超时自动退款',
                            'afs_reason_detail' => '订单超时自动退款',
                            'user_info'         => json_encode_cn(['name' => $item['name'], 'phone' => $item['phone'], 'address' => $item['receipt_address']]),
                            'afs_status'        => 4,
                            'refund_money'      => $item['money'],
                            'refund_points'     => $item['integral'],
                            'pre_refund_money'  => $item['pre_use_money'],
                            'pre_refund_points' => $item['pre_point'],
                            'operate_list'      => json_encode_cn(
                                [
                                    [
                                        'afs_status' => 1, 'name' => '订单超时自动退款',
                                        'desc'       => '申请售后', 'time' => date("Y-m-d H:i:s")
                                    ]
                                ]
                            ),
                            'is_refund_order'   => ($item['promotion_source'] == 3 && $item['parent_order_type'] != 3) ? 0 : 1,
                        ];

                        $afs_id = $afs_model->insertGetId($data);

                        //数据对接到售后中心
                        $as_data = [
                            'afs_id'                           => $afs_id, 'order_code' => $item['order_code'], 'type' => 'create', 'user_id' =>
                                $item['user_id'], 'created_at' => time(), 'is_dq' => 1
                        ];
                        Queue::push('app\common\queue\AfterSale', json_encode($as_data), config('queue_type.after_sale'));

                        if ($item['promotion_source'] == 3 && $item['parent_order_type'] == 3) {
                            continue;
                        }
                        $dlr = false;
                        if ($item['order_status'] == 7 && in_array($item['order_source'], [20, 21, 24]) || $item['logistics_mode'] == 1) {
                            $dlr = true;
                        }
                        // 退款
                        Queue::push('app\common\queue\AfterSaleRefund', json_encode(['order_code' => $item['order_code'], 'is_dlr' => $dlr]), config('queue_type.after_sale'));
                    }
                }
                );
        } else {
            $return_data = $order_model
                ->field('*')
                ->where($where)
                ->whereBetween('dq_time', [$before, $now])->select();

            $afs_model = new DbAfterSaleOrders();
            $data      = [];
            foreach ($return_data as $v) {
                $lock = 'ExpireRefund:' . $v['order_code'];
                if (!getRedisLock($lock, mt_rand(40, 80)) || ($v['promotion_source'] != 3 && $v['parent_order_type'] == 3)) {
                    continue;
                }
                $afs_info = $afs_model->where(['order_id' => $v['id'], 'is_enable' => 1])->order('id desc')->find();
                if (!empty($afs_info) && !in_array($afs_info['afs_status'], [2, 3, 5, 8, 11])) {
                    continue;
                }

                $net_order                = new NetOrder();
                $service_parent_order_key = 'service_parent_order' . $v['parent_order_code'];
                if (!redis($service_parent_order_key)) {
                    $can_after = $net_order->can_after($v, $afs_list[$v['id']] ?? []);
                    if ($can_after['can_after'] == 1) {
                        if ($v['promotion_source'] == 3) {
                            redis($service_parent_order_key, $v['parent_order_code'], 3600);
                        }
                    } else {
                        continue;
                    }
                }
                $data[] = $v['order_code'];
            }
            dd($data);
        }
    }

    public function test_add_sto()
    {
        $order_model = new BuOrder();
        $order_where = ['a.order_status' => 8, 'a.last_updated_date' => ['<=', date('Y-m-d H:i:s', strtotime('-5 minute'))]];
        $order_list  = $order_model->alias('a')->join('t_bu_order_commodity b ', ' a.order_code=b.order_code')->where($order_where)->field('a.id,b.sku_id,b.mo_sub_id,b.count')->select();
        if ($order_list) {
            $net_order = new NetOrder();
            foreach ($order_list as $v) {
                $res = $net_order->_addStock($v['sku_id'], $v['count'], $v['mo_sub_id']);
                $order_model->saveData(['order_status' => 1, 'modifier' => 'b_s_tak'], ['id' => $v['id']]);
                Logger::error('orderbackstock', ['sku' => $v['sku_id'], 'count' => $v['count'], 'mo_sub_id' => $v['mo_sub_id'], 'res' => $res]);
            }
        }
    }

    public function test_sto_qq()
    {
//        Queue::push('app\common\queue\PushOrder', json_encode(['type' => 1, 'order_id' => 1312885]), config('queue_type.order'));
        $commodity_set_id = input('commodity_set_id', 9087);
        $as_data          = ['data' => ['commodity_set_id' => $commodity_set_id], 'type' => 'stock', 'created_at' => time()];
        Queue::push('app\common\queue\OrderQueue', json_encode($as_data), config('queue_type.after_sale'));
        Queue::push('app\common\queue\GoodsQueue', json_encode($as_data), config('queue_type.order'));
        die();
        $set_model     = new DbCommoditySet();
        $set_sku_model = new DbCommoditySetSku();
        $data          = $as_data['data'];
        $com_set       = $set_model->getList(['where' => ['id' => ['in', $data['commodity_set_id']], 'is_enable' => 1]]);
        $st_count      = '';
        if ($com_set) {
            foreach ($com_set as $v) {
                if (empty($v['group_commodity_ids_info'])) {
                    $set_sku = $set_sku_model->alias('a')->join('t_db_commodity_sku b ', ' a.commodity_sku_id=b.id')->where(['a.commodity_set_id' => $v['id']])->group('b.commodity_id ,b.sp_value_list')->field('a.stock')->select();
                    if ($set_sku) {
                        $count_stock = array_sum(array_column($set_sku, 'stock'));
                        $res         = $set_model->saveData(['count_stock' => $count_stock, 'modifier' => 'stock_q'], ['id' => $v['id']]);
                        if ($res) {
                            $st_count .= $v['id'] . '--' . $count_stock . '==' . $res;
                        }
                    }
                }
            }
        }
        echo $st_count;
    }

    public function testtt()
    {
        $order = input('order', '');
        $res   = $order ? $order : '999';
        var_dump($res);

    }

    //pz重新扣积分
    public function re_pz_point()
    {
        $point_model      = new BuOrderPoint();
        $point_order_code = input('point_order_code');
        $dlr_code         = input('new_dlr', '');
        $anti             = input('anti', 0);
        $r_dedu           = input('r_dedu', 0);
        $r_dedu_ok        = input('r_dedu_ok', 0);
        $oil_js           = input('oil_js', 0);//直接修改油车JS
        $new_el_point     = input('new_el_point', 0);//只能修改一辆车的油车
        if ($oil_js) {
            $oil_js = html_entity_decode($oil_js);
        }
        $point_order = $point_model->getOne(['where' => ['point_order_code' => $point_order_code]]);
//        {"oneid":"950b0903e2c25c9c2907c1268faee6240327","dlr_code":"PZ1AAPP","remark":"积分购买精品","order_id":"AP2211131109354901","use_elec_points":"2410","oli_card_list":[{"cardNo":"F16292020010597985","vin":"LGBG42E09KY305632","usePvPoint":"70","useCardBalance":0}],"request_id":"AP221113110935490665089"}
        $js_arr       = json_decode($point_order['send_note'], true);
        $car          = new Carer();
        $return_point = 0;
        if (isset($js_arr['use_elec_points'])) {
            $return_point += $js_arr['use_elec_points'];
        }
        if (isset($js_arr['oli_card_list'])) {
            $return_point += array_sum(array_column($js_arr['oli_card_list'], 'usePvPoint'));
        }
        if ($anti) {
            $return_order_id = $this->_getOrderNo('PZRP' . date('YmdHis'), 3);

            $data = [
                'oneid'           => $js_arr['oneid'],
                'dlr_code'        => $js_arr['dlr_code'],
                'order_id'        => $js_arr['order_id'],
                'return_order_id' => $return_order_id,
                'return_points'   => $return_point,
                'request_id'      => $this->_getOrderNo('R' . $js_arr['dlr_code'], 5),
            ];
            $msg  = $point_res = $car->returnPzPoint($data);
            DbLog::create(
                [
                    'type'         => 'antiBalance-pz',
                    'creator'      => 'hand-an',
                    'send_note'    => json_encode_cn($data),
                    'receive_note' => json_encode_cn($point_res),
                    'is_success'   => empty($point_res['code']) ? 'success' : 'fail'
                ]
            );
            $point_model->saveData(['is_enable' => 0, 'modifier' => 'ha-re-pz'], ['id' => $point_order['id']]);
            var_dump($msg);
        }
        if ($r_dedu) {
            $modifier       = 'ha-re-pza';
            $point_order_id = $js_arr['order_id'] . '1';
            $request_id     = $this->_getOrderNo($point_order['order_code'], 6);
            if ($dlr_code) {
                $js_arr['dlr_code'] = $dlr_code;
            }
//            {"oneid":"bd088d64c71e4018ae4425e261953bfb8747","dlr_code":"PZ1AAPP","remark":"积分购买精品","order_id":"AP22113019534693611","use_elec_points":0,"oli_card_list":[{"cardNo":"F16072021112995580","vin":"LGBH92E08MY825443","usePvPoint":"5240","useCardBalance":0}],"request_id":"AP221130195346936794911"}

            $new_return_point = 0;
            if ($new_el_point) {
                $js_arr['use_elec_points'] = $new_el_point;
                $new_return_point          += $new_el_point;
            }
            if ($oil_js) {
                $js_arr['oli_card_list'] = json_decode($oil_js, true);
                $new_return_point        += array_sum(array_column($js_arr['oli_card_list'], 'usePvPoint'));

            }
            $js_arr['order_id']   = $point_order_id;
            $js_arr['request_id'] = $request_id;
            if (!$r_dedu_ok) {
                var_dump($js_arr);
                die();
            }
            $point_res = $car->usePzPoint($js_arr);
            $log_data  = array(
                'type'         => 'use_point',
                'send_note'    => json_encode_cn($js_arr),
                'receive_note' => json_encode_cn($point_res),
            );

            $point_data  = [
                'order_code'       => $point_order['order_code'],
                'point_order_code' => $point_order_id,
                'vin'              => '',
                'ic_card_no'       => '',
                'dlr_code'         => $js_arr['dlr_code'],
                'creator'          => $modifier,
                'point_type'       => 3,
                'all_point'        => $return_point,
                'point'            => $new_return_point,
                'use_state'        => 2,
                'remark'           => $point_order['remark'],
                'send_note'        => json_encode_cn($js_arr),
                'back_json'        => json_encode_cn($point_res),
                'one_id'           => $point_order['one_id'],
                'request_id'       => $request_id,
            ];
            $point_model = new BuOrderPoint();
            $order_model = new BuOrder();
            $log_model   = new DbLog();
            $point_model->insertData($point_data);
            //事件没有通知之前都是1
            $res = $order_model->saveData(['is_cc_ok' => 1, 'last_updated_date' => date('Y-m-d H:i:s')], ['order_code' => $point_order['order_code']]);
            $log_model->insertData($log_data);
            var_dump($point_res);

        }


    }

    public function test_de_ms_st()
    {
        $kill_id      = input('kill_id', 91);
        $commodity_id = input('commodity_id', 5642);
        $count        = input('count', 10);
        $order_code   = input('order_code', 'GWSM221129135442045');


        $redis_name       = 'acKillCountById' . $kill_id . $commodity_id; //
        $redis_name_order = 'acKillCountById' . $kill_id . $commodity_id . $order_code; //
//        $net_count2 =  redis($redis_name);
        $redis     = new Redis(config('cache'));
        $net_count = $redis->get($redis_name);
        $arr_count = explode(':', $net_count);
//        var_dump($r);
        var_dump($arr_count);
        var_dump(count($arr_count));
        var_dump($net_count);
        dd($net_count);
//        if($r<0){
//            dd('没有了');
//        }else{
//
//        }
//
//        $res = redis($redis_name,$re_count,3600*24*100);
//        redis($redis_name_order,1,3600*24*100);
//
//
//
//        $net_order= new NetOrder();
//        $res   =  $net_order->dec_kill_count($kill_id,$commodity_id,$count,$order_code);
//        var_dump($res);
    }

    public function test_redis_ys()
    {
        $redis_head_name = "acKillCountById";
        Redis::get($redis_head_name . '*');

    }

    public function go_change_sub_price()
    {
        $par_order_code    = input('par_order_code', "'GWSM220902140437329','GWSM220919002912225','GWSM220921095013832','GWAPP220921105203606','GWAPP220925154746064','GWAPP220926165108214','GWAPP220908075734060'");
        $order_goods_where = ['parent_order_code' => ['in', $par_order_code], 'mo_sub_id' => 0, 'is_enable' => 1];
        $order_goods_model = new BuOrderCommodity();
        $order_goods_list  = $order_goods_model->getList(['where' => $order_goods_where]);
        foreach ($order_goods_list as $v) {
            if ($v['mo_id']) {
                $mo_sub_where         = ['parent_order_code' => $v['parent_order_code'], 'mo_sub_id' => $v['mo_id'], 'is_enable' => 1];
                $order_goods_sub_data = [
                    'card_codes'        => $v['card_codes'],
                    'card_ids'          => $v['card_ids'],
                    'act_sett_rule_id'  => $v['act_sett_rule_id'],
                    'card_sett_rule_id' => $v['card_sett_rule_id'],
                    'e3s_activity_id'   => $v['e3s_activity_id'],
                    'act_name'          => $v['act_name'],
                    'limit_id'          => $v['limit_id'],
                    'suit_id'           => $v['suit_id'],
                    'full_id'           => $v['full_id'],
                    'n_dis_id'          => $v['n_dis_id'],
                    'group_id'          => $v['group_id'],
                    'pre_sale_id'       => $v['pre_sale_id'],
                    'act_type'          => $v['act_type'],
                    'limit_wi_id'       => $v['limit_wi_id'],
                    'full_wi_id'        => $v['full_wi_id'],
                    'seckill_id'        => $v['seckill_id'],
                ];
                //limit_dis_money full_dis_money  n_dis_money   pre_sale_dis_money group_dis_money  suit_dis_money all_dis card_all_dis  actual_point actual_use_money card_sett_money act_sett_money
                $order_goods_model->saveData($order_goods_sub_data, $mo_sub_where);
                $mo_sub_goods        = $order_goods_model->getList(['where' => $mo_sub_where]);
                $toEnd               = count($mo_sub_goods);
                $mo_sy_limit_dis     = 0;
                $mo_sy_kill_dis      = 0;
                $mo_sy_full_dis      = 0;
                $mo_sy_n_dis         = 0;
                $mo_sy_pre_dis       = 0;
                $mo_sy_group_dis     = 0;
                $mo_sy_suit_dis      = 0;
                $mo_sy_all_dis       = 0;
                $mo_sy_card_all_dis  = 0;
                $mo_sy_work_time_dis = 0;
                $mo_sy_actual_price  = 0;

                $mo_sy_card_sett_money = 0;
                $mo_sy_act_sett_money  = 0;

                $mo_limit_dis           = 0;
                $mo_kill_dis            = 0;
                $mo_full_dis            = 0;
                $mo_n_dis               = 0;
                $mo_pre_dis             = 0;
                $mo_group_dis           = 0;
                $mo_suit_dis            = 0;
                $mo_actual_price        = 0;
                $mo_all_dis             = 0;
                $mo_card_all_dis        = 0;
                $mo_work_time_dis       = 0;
                $sub_one_work_count_dis = 0;
                $mo_card_sett_money     = 0;
                $mo_act_sett_money      = 0;
                $mo_mail_price          = 0;
                $mo_sy_mail_price       = 0;

                foreach ($mo_sub_goods as $mo_vv) {
                    $sub_one_work_money       = bcmul($mo_vv['work_time_money'], $mo_vv['count'], 2);
                    $sub_one_work_count_money = bcmul($sub_one_work_money, $v['count'], 2);
                    $mo_vv_price_count        = bcmul($mo_vv['price'], $mo_vv['count'], 2);//子商品*子数量后价格
                    $set_sku_model            = new DbCommoditySetSku();

                    //子商品工时 已经算了子商品count加上主商品count相乘 2022-07-22 21:44:14 tzl
                    if (0 === --$toEnd) {
                        $mo_limit_dis = $v['limit_dis_money'] * $v['count'] - $mo_sy_limit_dis;
                        $mo_kill_dis  = $v['seckill_dis_money'] * $v['count'] - $mo_sy_kill_dis;
                        $mo_full_dis  = $v['full_dis_money'] * $v['count'] - $mo_sy_full_dis;
                        $mo_n_dis     = $v['n_dis_money'] * $v['count'] - $mo_sy_n_dis;
                        $mo_pre_dis   = $v['pre_sale_dis_money'] * $v['count'] - $mo_sy_pre_dis;
                        $mo_group_dis = $v['group_dis_money'] * $v['count'] - $mo_sy_group_dis;
                        $mo_suit_dis  = $v['suit_dis_money'] * $v['count'] - $mo_sy_suit_dis;
//                            $mo_all_dis = $v['all_dis']-$mo_sy_all_dis;
                        $mo_card_all_dis  = $v['card_all_dis'] - $mo_sy_card_all_dis;
                        $mo_work_time_dis = $v['work_time_dis'] - $mo_sy_work_time_dis;

                        $mo_card_sett_money     = $v['card_sett_money'] - $mo_sy_card_sett_money;
                        $mo_act_sett_money      = $v['act_sett_money'] - $mo_sy_act_sett_money;
                        $mo_actual_price        = $v['actual_price'] - $mo_sy_actual_price;
                        $mo_mail_price          = $v['mail_price'] - $mo_sy_mail_price;
                        $sub_one_work_count_dis = bcmul($mo_work_time_dis, $v['count'], 2);

                    } else {
                        if ($v['limit_dis_money']) {
                            $mo_limit_dis    = round(($mo_vv_price_count * $v['limit_dis_money'] * $v['count'] / $v['price']), 2);
                            $mo_sy_limit_dis += $mo_limit_dis;
                        }
                        if ($v['seckill_dis_money']) {
                            $mo_kill_dis    = round(($mo_vv_price_count * $v['seckill_dis_money'] * $v['count'] / $v['price']), 2);
                            $mo_sy_kill_dis += $mo_kill_dis;
                        }
                        if ($v['full_dis_money']) {
                            $mo_full_dis    = round(($mo_vv_price_count * $v['full_dis_money'] * $v['count'] / $v['price']), 2);
                            $mo_sy_full_dis += $mo_full_dis;
                        }
                        if ($v['n_dis_money']) {
                            $mo_n_dis    = round(($mo_vv_price_count * $v['n_dis_money'] * $v['count'] / $v['price']), 2);
                            $mo_sy_n_dis += $mo_n_dis;
                        }
                        if ($v['pre_sale_dis_money']) {
                            $mo_pre_dis    = round(($mo_vv_price_count * $v['pre_sale_dis_money'] * $v['count'] / $v['price']), 2);
                            $mo_sy_pre_dis += $mo_pre_dis;
                        }
                        if ($v['group_dis_money']) {
                            $mo_group_dis    = round(($mo_vv_price_count * $v['group_dis_money'] * $v['count'] / $v['price']), 2);
                            $mo_sy_group_dis += $mo_group_dis;
                        }
                        if ($v['suit_dis_money']) {
                            $mo_suit_dis    = round(($mo_vv_price_count * $v['suit_dis_money'] * $v['count'] / $v['price']), 2);
                            $mo_sy_suit_dis += $mo_suit_dis;
                        }
//
                        if ($v['card_all_dis']) {
                            $mo_card_all_dis    = round(($mo_vv_price_count * $v['card_all_dis'] / $v['price']), 2);
                            $mo_sy_card_all_dis += $mo_card_all_dis;
                        }

                        if ($v['card_sett_money']) {
                            $mo_card_sett_money    = round(($mo_vv['price'] * $v['card_sett_money'] / $v['price']), 2);
                            $mo_sy_card_sett_money += $mo_card_sett_money;
                        }
//                            if($v['act_sett_money']){
//                                $mo_act_sett_money = round(($mo_vv['price']*$v['act_sett_money']/$v['price']),2);
//                                $mo_sy_act_sett_money += $mo_act_sett_money;
//                            }
//                            if($v['all_dis']){
//                                $mo_all_dis = round(($mo_vv_price_count*$v['all_dis']/$v['price']),2);
//                                $mo_sy_all_dis += $mo_all_dis;
//                            }
                        if ($v['actual_price']) {
                            $mo_actual_price    = round(($mo_vv_price_count * $v['actual_price'] / $v['price']), 2);
                            $mo_sy_actual_price += $mo_actual_price;
                        }
                        if ($v['mail_price']) {
                            $mo_mail_price    = round(($mo_vv_price_count * $v['mail_price'] / $v['price']), 2);
                            $mo_sy_mail_price += $mo_mail_price;//momail--
                        }


                        if ($v['work_time_dis'] > 0 && ($mo_vv['work_time_money'] > 0 || $v['work_time_money'] > 0)) {
                            $mo_work_time_dis       = round(($mo_vv['work_time_money'] * $mo_vv['count'] * $v['work_time_dis'] / $v['work_time_money']), 2);
                            $mo_sy_work_time_dis    += $mo_work_time_dis;
                            $sub_one_work_count_dis = bcmul($mo_work_time_dis, $v['count'], 2);

                        }
                        $one_all_dis_count = $mo_limit_dis + $mo_full_dis + $mo_n_dis + $mo_pre_dis + $mo_group_dis + $mo_suit_dis + $sub_one_work_count_dis;
                        if ($v['act_sett_money'] && $v['all_dis'] > 0) {
                            $mo_act_sett_money    = round(($one_all_dis_count * $v['act_sett_money'] / $v['all_dis']), 2);
                            $mo_sy_act_sett_money += $mo_act_sett_money;
                        }
//                            echo $mo_work_time_dis .'--'.$mo_vv['count'].'==';

                    }

                    $order_goods_sub_data = [
                        'limit_dis_money'        => $mo_limit_dis,
                        'full_dis_money'         => $mo_full_dis,
                        'n_dis_money'            => $mo_n_dis,
                        'pre_sale_dis_money'     => $mo_pre_dis,
                        'group_dis_money'        => $mo_group_dis,
                        'suit_dis_money'         => $mo_suit_dis,
                        'all_dis'                => $mo_limit_dis + $mo_full_dis + $mo_n_dis + $mo_pre_dis + $mo_group_dis + $mo_suit_dis + $sub_one_work_count_dis,
                        'card_all_dis'           => $mo_card_all_dis,
                        'work_time_dis'          => $sub_one_work_count_dis,
                        'work_time_money'        => $sub_one_work_count_money,
                        'work_time_actual_money' => $sub_one_work_count_money - $sub_one_work_count_dis,
                        //                            'actual_use_money'=>$mo_actual_use_money,
                        //                            'actual_point'=>$mo_actual_point,
                        'card_sett_money'        => $mo_card_sett_money,
                        'act_sett_money'         => $mo_act_sett_money,
                        'actual_price'           => $mo_actual_price,
                        'mail_price'             => $mo_mail_price,//mm--
                    ];
//                        print_json($order_goods_sub_data);
                    $order_goods_model->saveData($order_goods_sub_data, ['id' => $mo_vv['id']]);

                }
            }
        }


    }

    public function class_redis()
    {
        $channel_type = in_array('channel');
        $clean        = input('cl', 0);
        $key          = config('cache_prefix.catalog') . $channel_type;
        $class_info   = redis($key);
        var_dump($class_info);
        if ($clean) {
            \redis($key, null);
        }


    }

    public function upstock()
    {
        $commodity_set_id = input('commodity_set_id', 8436);
        $set_model        = new DbCommoditySet();
        $set_sku_model    = new DbCommoditySetSku();
        $com_set          = $set_model->getList(['where' => ['id' => ['in', $commodity_set_id], 'is_enable' => 1]]);
        $st_count         = '';
        if ($com_set) {
            foreach ($com_set as $v) {
                if (empty($v['group_commodity_ids_info'])) {
                    $set_sku = $set_sku_model->alias('a')->join('t_db_commodity_sku b ', ' a.commodity_sku_id=b.id')->where(['a.commodity_set_id' => $v['id'], 'a.is_enable' => 1])->group('b.commodity_id ,b.sp_value_list')->field('a.stock')->select();
                    if ($set_sku) {
                        $count_stock = array_sum(array_column($set_sku, 'stock'));
                        $res         = $set_model->saveData(['count_stock' => $count_stock, 'modifier' => 'stock_q'], ['id' => $v['id']]);
                        if ($res) {
                            $st_count .= $v['id'] . '--' . $count_stock . '==' . $res;
                        }
                    }
                }
            }
        }
        var_dump($st_count);
//        Logger::debug('order-create-change-',['data'=>$data,'res'=>$res]);
        return $st_count;
    }

    public function test_ff_sql()
    {
        $flat = new DbCommodityFlat();
        try {
            $sql1 = "SET GLOBAL group_concat_max_len=10000;";
            $sql2 = " SET SESSION group_concat_max_len=10000;";
            $flat->query($sql1);
            $flat->query($sql2);
        } catch (Exception $e) {
            echo $e->getMessage();
        }

    }

    public function test_cl_vin_log()
    {
        $com = new Common();
        $res = $com->cl_err_vin_goods();
        var_dump($res);
    }


    public function test_max_len()
    {
        $flat = new DbCommodityFlat();
        $sql1 = "SET GLOBAL group_concat_max_len=100000;";
        $sql2 = " SET SESSION group_concat_max_len=100000;";
        $flat->query($sql1);
        $res = $flat->query($sql2);
        dd($res);
    }

    public function test_save_mo_dis_price()
    {
        set_time_limit(0);

        $pp_order_code     = [
            'QCSM230201210818799', 'QCAPP230201204800395', 'QCSM230201204616473', 'QCSM230201204445629', 'QCSM230201135852438', 'QCSM230201134552067', 'QCAPP230201112309762', 'QCSM230201102319727', 'QCSM230201003619352', 'QCAPP230201003120494', 'QCSM230201002854045', 'QCSM230202154855747', 'QCSM230202105734017', 'QCSM230203222354783', 'QCAPP230203220802607', 'QCAPP230203175230675', 'QCAPP230203160057817', 'QCAPP230203145846778', 'QCSM230203140120488', 'QCSM230203020110199', 'QCSM230203020038139', 'QCAPP230204224751146', 'QCSM230204162541960', 'QCSM230204161039619', 'QCAPP230204125729227', 'QCAPP230204125516011', 'QCSM230204120824567', 'QCSM230204095913136', 'QCSM230205184346488', 'QCSM230205162841994', 'QCSM230205160036239', 'QCSM230205105846659', 'QCSM230205083243522', 'QCAPP230205043811030', 'QCSM230206230731608', 'QCSM230206202532662', 'QCSM230206165336821', 'QCSM230206163625657', 'QCSM230206163147730', 'QCSM230206162459180', 'QCAPP230206142536851', 'QCAPP230206132131489', 'QCSM230206123649071', 'QCSM230206122648722', 'QCSM230206120745896', 'QCSM230206115015195', 'QCAPP230206110034541', 'QCSM230206080724371', 'QCSM230206074716275', 'QCSM230207180639537', 'QCSM230207165125952', 'QCSM230207093637509', 'QCSM230207010532315', 'QCSM230208235527622', 'QCSM230208231142624', 'QCSM230208225459392', 'QCSM230208225432293', 'QCSM230208220022819', 'QCSM230208214809758', 'QCAPP230208192045470', 'QCSM230208162629183', 'QCSM230208143650023', 'QCSM230209234105150', 'QCSM230209231449194', 'QCAPP230209170039448', 'QCAPP230209160202370', 'QCAPP230209155505183', 'QCSM230209151115277', 'QCSM230209142906982', 'QCSM230209114342350', 'QCSM230210230152899', 'QCSM230210223118337', 'QCSM230210215947158', 'QCSM230210214548052', 'QCSM230210214519333', 'QCSM230210213112356', 'QCSM230210211733552', 'QCSM230210211040556', 'QCSM230210210809515', 'QCSM230210203559346', 'QCSM230210203311812', 'QCSM230210203001793', 'QCSM230210202858209', 'QCSM230210202640600', 'QCSM230210202632340', 'QCSM230210195806128', 'QCSM230210194336412', 'QCSM230210193645905', 'QCSM230210193101123', 'QCSM230210193038423', 'QCSM230210192914886', 'QCSM230210192609776', 'QCSM230210192402828', 'QCSM230210185148385', 'QCSM230210184617833', 'QCSM230210184000138', 'QCSM230210175151155', 'QCSM230210174652256', 'QCSM230210174608486', 'QCSM230210174556407', 'QCSM230210174155505', 'QCSM230210174004032', 'QCSM230210173754416', 'QCSM230210173523820', 'QCSM230210170348166', 'QCSM230210165948319', 'QCSM230210165926989', 'QCSM230210165703182', 'QCSM230210165126726', 'QCSM230210165105090', 'QCSM230210164620580', 'QCSM230210164607321', 'QCSM230210164209574', 'QCSM230210162913140', 'QCSM230210162830232', 'QCSM230210162811149', 'QCSM230210162641934', 'QCSM230210162514453', 'QCSM230210162455559', 'QCSM230210160319015', 'QCSM230210160134143', 'QCSM230210160113575', 'QCSM230210155127680', 'QCSM230210154707977', 'QCSM230210154550129', 'QCSM230210153326127', 'QCSM230210153302361', 'QCSM230210151723372', 'QCSM230210151526998', 'QCSM230210151345886', 'QCSM230210151239410', 'QCSM230210150305016', 'QCSM230210150011750', 'QCSM230210150003587', 'QCSM230210145852558', 'QCSM230210145824568', 'QCSM230210145414910', 'QCSM230210145344839', 'QCSM230210144604055', 'QCSM230210144512621', 'QCSM230210144028303', 'QCSM230210142843402', 'QCSM230210142725382', 'QCSM230210142430136', 'QCSM230210142324468', 'QCSM230210142227244', 'QCSM230210141721079', 'QCSM230210141608030', 'QCSM230210141356150', 'QCSM230210141246425', 'QCSM230210140606210', 'QCSM230210135600936', 'QCSM230210135543320', 'QCSM230210135435096', 'QCSM230210135329801', 'QCSM230210135323503', 'QCSM230210135233696', 'QCSM230210135214158', 'QCSM230210135033605', 'QCSM230210134803845', 'QCSM230210134344021', 'QCSM230210134212847', 'QCSM230210134207799', 'QCSM230210133849725', 'QCSM230210133848162', 'QCSM230210133615505', 'QCSM230210133553708', 'QCSM230210133533476', 'QCSM230210133218951', 'QCSM230210133021119', 'QCSM230210132811226', 'QCSM230210132558106', 'QCSM230210132527698', 'QCSM230210132504751', 'QCSM230210132348835', 'QCSM230210132201161', 'QCSM230210132032138', 'QCSM230210131709588', 'QCSM230210131704686', 'QCSM230210131448281', 'QCSM230210131339833', 'QCSM230210131233242', 'QCSM230210131213080', 'QCSM230210130948867', 'QCSM230210130943771', 'QCSM230210130909779', 'QCSM230210130842903', 'QCSM230210130725143', 'QCSM230210130648633', 'QCSM230210130531716', 'QCSM230210125858234', 'QCSM230210125705857', 'QCSM230210125655864', 'QCSM230210125639824', 'QCSM230210125637246', 'QCSM230210125627277', 'QCSM230210125621042', 'QCSM230210125603973', 'QCSM230210125320066', 'QCSM230210125130139', 'QCSM230210125117297', 'QCSM230210125047829', 'QCSM230210124748097', 'QCSM230210124726402', 'QCSM230210124656082', 'QCSM230210124615531', 'QCSM230210124557311', 'QCSM230210124431970', 'QCSM230210124411645', 'QCSM230210124256332', 'QCSM230210124249627', 'QCSM230210124144920', 'QCSM230210123904457', 'QCSM230210123830743', 'QCSM230210123637071', 'QCSM230210123620563', 'QCSM230210123428681', 'QCSM230210123426196', 'QCSM230210123415028', 'QCSM230210123254250', 'QCSM230210123158235', 'QCSM230210123154290', 'QCSM230210123100848', 'QCSM230210123059542', 'QCSM230210123022383', 'QCSM230210123017907', 'QCSM230210122957225', 'QCSM230210122925756', 'QCSM230210122843753', 'QCSM230210122829636', 'QCSM230210122826034', 'QCSM230210122654130', 'QCSM230210122629294', 'QCSM230210122533525', 'QCSM230210122523031', 'QCSM230210122502956', 'QCSM230210122445016', 'QCSM230210122444379', 'QCSM230210122430000', 'QCSM230210122350432', 'QCSM230210122347995', 'QCSM230210122333449', 'QCSM230210122316797', 'QCSM230210122238309', 'QCSM230210122226372', 'QCSM230210122219492', 'QCSM230210122201149', 'QCSM230210122130182', 'QCSM230210122129867', 'QCSM230210122102082', 'QCSM230210122100711', 'QCSM230210122034783', 'QCSM230210121949868', 'QCSM230210121935103', 'QCSM230210121933808', 'QCSM230210121858066', 'QCSM230210121828395', 'QCSM230210121823086', 'QCSM230210121802222', 'QCSM230210121748038', 'QCSM230210121713390', 'QCSM230210121709581', 'QCSM230210121703739', 'QCSM230210121652803', 'QCSM230210121636772', 'QCSM230210121609866', 'QCSM230210121600456', 'QCSM230210121511294', 'QCSM230210121458337', 'QCSM230210121452135', 'QCSM230210121444076', 'QCSM230210121412121', 'QCSM230210121401187', 'QCSM230210121356183', 'QCSM230210121346304', 'QCSM230210121315232', 'QCSM230210121312544', 'QCSM230210121217078', 'QCSM230210121215390', 'QCSM230210121123158', 'QCSM230210121116277', 'QCSM230210121115647', 'QCSM230210121114570', 'QCSM230210121104166', 'QCSM230210121031077', 'QCSM230210121015945', 'QCSM230210121014898', 'QCSM230210120948911', 'QCSM230210120937059', 'QCSM230210120859677', 'QCSM230210120855532', 'QCSM230210120850962', 'QCSM230210120848557', 'QCSM230210120825063', 'QCSM230210120816220', 'QCSM230210120805359', 'QCSM230210120751406', 'QCSM230210120728429', 'QCSM230210120720568', 'QCAPP230221182111000', 'QCAPP230221181826707', 'QCSM230221164927752', 'QCSM230221164630272', 'QCSM230221164322239', 'QCSM230221145754597', 'QCSM230221145739551', 'QCSM230221141438631', 'QCAPP230221131140128', 'QCAPP230221131127161', 'QCAPP230221130913952', 'QCAPP230221115120327', 'QCSM230221113444879', 'QCSM230221072627944', 'QCSM230222230421572', 'QCAPP230222223605111', 'QCSM230222223508640', 'QCSM230222223345752', 'QCAPP230222221607952', 'QCSM230222213757594', 'QCAPP230222212729288', 'QCSM230222205714458', 'QCSM230222025919532', 'QCSM230222025525664', 'QCSM230222025238503', 'QCSM230222025120020', 'QCSM230223235801985', 'QCAPP230223233448647', 'QCAPP230223233442478', 'QCAPP230223233320249', 'QCSM230223230505488', 'QCSM230223213402620', 'QCSM230223210312670', 'QCAPP230223203925664', 'QCSM230223195725346', 'QCSM230223195557576', 'QCAPP230223184458183', 'QCAPP230223184253183', 'QCAPP230223183427661', 'QCSM230223165512976', 'QCSM230223165413208', 'QCSM230223163524586', 'QCSM230223163138214', 'QCAPP230223162840383', 'QCAPP230223162635545', 'QCSM230223161932134', 'QCSM230223161908056', 'QCSM230223161855642', 'QCSM230223161223752', 'QCSM230223152233778', 'QCAPP230223152047664', 'QCSM230223151559677', 'QCSM230223014139971'
        ];
        $order_goods_model = new BuOrderCommodity();
        $order_goods_where = ['parent_order_code' => ['in', $pp_order_code], 'mo_sub_id' => 0, 'is_enable' => 1, 'seckill_id' => ['>', 0]];
        $order_goods_list  = $order_goods_model->getList(['where' => $order_goods_where]);
//        echo $order_goods_model->getLastSql();die();
        if ($order_goods_list) {
            foreach ($order_goods_list as $v) {
                $mo_sub_where   = ['parent_order_code' => $v['parent_order_code'], 'mo_sub_id' => $v['mo_id'], 'is_enable' => 1];
                $mo_sub_goods   = $order_goods_model->getList(['where' => $mo_sub_where]);
                $toEnd          = count($mo_sub_goods);
                $mo_kill_dis    = 0;
                $mo_sy_kill_dis = 0;
                foreach ($mo_sub_goods as $mo_vv) {
                    $mo_vv_price_count = bcmul($mo_vv['price'], $mo_vv['count'], 2);//子商品*子数量后价格
                    if (0 === --$toEnd) {
                        $mo_kill_dis = $v['seckill_dis_money'] * $v['count'] - $mo_sy_kill_dis;
                    } else {
                        if ($v['seckill_dis_money']) {
                            $mo_kill_dis    = round(($mo_vv_price_count * $v['seckill_dis_money'] * $v['count'] / $v['price']), 2);
                            $mo_sy_kill_dis += $mo_kill_dis;
                        }
                    }
                    $order_goods_sub_data = [
                        'seckill_dis_money' => $mo_kill_dis,
                        'seckill_id'        => $v['seckill_id'],
                        'all_dis'           => $mo_kill_dis,//手工的就按照这个来 只有秒杀优惠

                    ];
                    $order_goods_model->saveData($order_goods_sub_data, ['id' => $mo_vv['id']]);
                    echo $order_goods_model->getLastSql();
                }
            }
        }


    }

    public function test_new_user()
    {
        $one_id = input('one_id', '');
        if (!empty($one_id)) {
            $user = (new DbUser())->where(['one_id' => $one_id])->find();
            if (!$user) {
                $member = Member::create('member')->search(['oneid' => $one_id]);
                if (empty($member['member_id'])) {
                    dd($member);
                }

                $data = [
                    'headimg_market'    => $member['avatar'],
                    'headimg_app'       => $member['app_avatar'],
                    'nickname_market'   => $member['nickname'],
                    'nickname_app'      => $member['app_nickname'],
                    'one_id'            => $member['oneid'],
                    'mid_phone'         => $member['phone'],
                    'modifier'          => 'get_coupon_new',
                    'plat_id'           => $member['member_id'],
                    'last_updated_date' => date('Y-m-d H:i:s'),
                ];
                $id   = (new DbUser())->insertGetId($data);
                dd($id);
            }
            dd($user);
        }
    }


    public function test_qc_car()
    {
        $one_id       = input('one_id', '');
        $channel_type = input('channel_type', 'QCSM');
        $unionid      = input('unionid', '');
        $net_com      = new \app\common\net_service\Common();
        $car          = $net_com->_getCarer($unionid, 0, $one_id, $channel_type);
        print_json($car);
    }


    //pz多订单拆分支付积分
    public function pz_more_point()
    {
        //有拆单时候需要将原来的订单给is_enable=0然后再做新的拆单
        $order_code            = input('order_code', 'AP230310192528101');
        $more_card_point_model = new BuOrderMoreCardPoint();
        $order_model           = new BuOrder();
        $more_point            = $more_card_point_model->getList(['where' => ['order_code' => $order_code, 'is_enable' => 1]]);

        $sp_order      = $order_model->getList(['where' => ['parent_order_code' => $order_code, 'parent_order_type' => 2]]);
        $one_more_data = [];
//        $more_card_point_model->saveData(['is_enable'=>0] , ['order_code'=>$order_code]);
        if ($more_point) {
            $i  = 0;
            $sy = 0;
            foreach ($sp_order as $v) {
//                $more_card_point_model->saveData(['is_enable'=>0] , ['order_code'=>$v['order_code']]);
//                while ($sy<=0){
//                    $sy += $more_point[$i]['point'];
//                    if($v['integral']<=$sy){
//                        $sub_new_point = $v['integral'];
//                        $sy -=$sub_new_point;
//                    }
//                }
                if ($v['integral'] > 0) {
                    if ($sy == 0) {
                        $sy += $more_point[$i]['point'];
                    }
//                    echo $sy.'.='.$i.'=.'.$v['integral'].';';
                    if ($v['integral'] <= $sy) {
//                        echo "11-".$sy.'.='.$i.'=.'.$v['integral'].$v['order_code'].';<br/>';
                        $sub_new_point   = $v['integral'];
                        $sy              -= $sub_new_point;
                        $one_point_data  = [
                            'order_code'       => $v['order_code'],
                            'point_order_code' => $v['order_code'] . $v['logistics_mode'],
                            'vin'              => $more_point[$i]['vin'],
                            'ic_card_no'       => $more_point[$i]['ic_card_no'],
                            'all_point'        => $v['integral'],
                            'point'            => $sub_new_point,
                            'creator'          => 'nowhilepoint',
                            'point_type'       => $more_point[$i]['point_type'],
                        ];
                        $one_more_data[] = $one_point_data;
                    } else {
                        while ($v['integral'] >= $sy) {
                            $sub_new_point = $v['integral'];
                            $b_it          = 0;
//                                echo "2-".$sy.'.='.$i.'=.'.$v['integral'].$v['order_code'].';<br/>';
                            if ($sy >= $sub_new_point) {
                                $one_point = $more_point[$i]['point'] - ($sy - $sub_new_point);
                                $b_it      = 1;
                                if (isset($more_point[$i])) {
                                    $sy -= $sub_new_point;
                                }
                            } else {
                                try {
                                    $one_point = $sy;
                                } catch (Exception $e) {
                                    Logger::error('kjferror' . $v['integral'] . '==' . $sy . $v['order_code'] . ';');
//                                    echo $v['integral'].'=='.$sy.$v['order_code'].';';
//                                    die();
                                }
                            }
//                            echo "3-".$sy.'.='.$i.'=.'.$v['integral'].'[one]'.$one_point.$v['order_code'].';<br/>';
                            $one_point_data  = [
                                'order_code'       => $v['order_code'],
                                'point_order_code' => $v['order_code'] . $v['logistics_mode'],
                                'vin'              => $more_point[$i]['vin'],
                                'ic_card_no'       => $more_point[$i]['ic_card_no'],
                                'all_point'        => $v['integral'],
                                'point'            => $one_point,
                                'creator'          => 'whilepoint',
                                'point_type'       => $more_point[$i]['point_type'],
                            ];
                            $one_more_data[] = $one_point_data;
                            $i++;
                            if (isset($more_point[$i])) {
                                $sy += $more_point[$i]['point'];
                            }
                            if ($b_it == 1) {
                                break;
                            }
                        }
//                        try {
//
//                        }catch (Exception $e){
//                            echo $i;
//                        }
                    }
                }
            }
            echo json_encode_cn($one_more_data);
//            die();
//            $more_card_point_model->insertAll($one_more_data);

        }
    }

    public function test_ccc_get_config()
    {
        echo ROOT_PATH . 'config/' . Env::get('config_env') . '.php';


    }

    public function change_af_s(){
        $order_id = input('order_id');
        $af_id = input('af_id');
        $af_status =  input('af_status');
        $refund_info = input('refund_info');
        $model       = new DbAfterSaleOrders();
        if (!$order_id || !$af_status || !$af_id) {
            die('0000-----00000');
        }
        $data = ['afs_status' => $af_status, 'last_updated_date' => date('Y-m-d H:i:s')];
        if ($refund_info) {
            $data['refund_info'] = $refund_info;
        }
        $res = $model->saveData($data, ['order_id' => $order_id, 'id' => $af_id]);
        echo $model->getLastSql();
        dd($res);
    }

    public function test_back_sttt()
    {
        $order_model =  new BuOrder();
        $order_where = ['a.order_status'=>8,'a.last_updated_date'=>['<=',date('Y-m-d H:i:s',strtotime('-5 minute'))]];
        $field = 'a.id,b.sku_id,b.mo_sub_id,b.count,b.seckill_id,b.order_code,b.commodity_id,a.created_date,a.ms_order_code';
        $field .=',a.ms_order_code2,a.link_order_code,a.link_order_code2,a.order_status,a.parent_order_type,a.parent_order_code';
        $order_list = $order_model->alias('a')
            ->join('t_bu_order_commodity b ',' a.order_code=b.order_code')
            ->where($order_where)
            ->field($field)
            ->select();

        if($order_list){
            $net_order =  new NetOrder();
            foreach($order_list as $v){
                //回退秒杀缓存库存--暂时不退
                if($v['ms_order_code'] ){
                    $param =[
                        'msOrderCode'=>$v['ms_order_code'],
                        'linkOrderCode'=>$v['link_order_code'],
                    ];
                    $lyPay  = new LyPay();
                    $result = $lyPay->closeOrder($param);
                    if($result->isSuccess()){
                        //如果关闭订单成功
                    }else{
                        //如果不成功--直接不处理这个订单
                        continue;
                    }
                }
                if($v['seckill_id']){
                    $seckill_type = DbSeckill::where('id', $v['seckill_id'])->value('seckill_type');
                    $screening = '';
                    if ($seckill_type == 2) {
                        $screening = date('Y-m-d', strtotime($v['created_date']));
                    }
                    $net_order->inc_kill_count($v['seckill_id'],$v['commodity_id'],$v['count'],$v['order_code'],$screening);
                }
                $res = $net_order->_addStock($v['sku_id'],$v['count'],$v['mo_sub_id'],$v['order_code']);
                $upd = ['order_status'=> 1,'modifier'=>'b_s_tak'];
                $order_model->saveData($upd,['id'=>$v['id']]);
                Logger::error('orderbackstock',['sku'=>$v['sku_id'], 'count'=>$v['count'],'mo_sub_id'=>$v['mo_sub_id'],'res'=>$res]);
                // 判断是否是子单
                if ($v['parent_order_type'] == 2) {
                    // 更新主单的订单状态
                    $map = [
                        'parent_order_type' => 3,
                        'parent_order_code' => $v['parent_order_code'],
                    ];
                    $order_model->where($map)->update($upd);
                }

            }
        }
    }

    public function save_order_vin()
    {
        $order_model = new BuOrder();
        $order_code  = input('order_code', '');
        $order_vin   = input('order_vin');
        if (!$order_code || !$order_vin) {
            die('参数缺少');
        }
        $order_where = ['order_code' => $order_code];
        $data        = ['order_vin' => $order_vin];
        $res         = $order_model->saveData($data, $order_where);
        echo $res;
        echo $order_model->getLastSql();
        die();


    }

    public function changeorderpoint()
    {
        $order_code = input('order_code');
        $point      = input('point');
        if (!$order_code) {
            die('Please enter');
        }
        $order_model = new BuOrder();
        $order_where = ['order_code' => $order_code];
        $data        = ['integral' => $point];
        $res         = $order_model->saveData($data, $order_where);
        echo $res;
        echo $order_model->getLastSql();
        die();
    }

    public function add_staff()
    {
        $user_id    = input('user_id', '');
        $sp_id      = input('sp_id', '');
        $type       = input('type', 2);
        $is_staff   = input('is_staff', 1);
        $user_model = new DbUser();
        $sp_model   = new DbSpecialSm();
        //取消用户授权信息
        if ($type == 1) {
            if (!$user_id) {
                die(2222);
            }
            $res = $user_model->saveData(['staff_phone' => ''], ['id' => $user_id]);
        }
        //给专题页加必须授权
        if ($type == 2) {
            if (!$sp_id) {
                die(3333);
            }
            $res = $sp_model->saveData(['is_staff' => $is_staff], ['id' => $sp_id]);
        }
        var_dump($res);
        die();
    }

    public function test_sql()
    {
        $card_r_model   = new BuCardReceiveRecord();
        $card_r_where   = ['user_id' => 111, 'card_id' => ['in', [333, 444]], 'is_enable' => 1, 'status' => 1];
        $card_r_where[] = ['exp', sprintf("(validity_date_start<='%s' || validity_date_start is null ) and (validity_date_end>='%s' ||  validity_date_end is null ) ", date('Y-m-d H:i:s'), date('Y-m-d H:i:s'))];
        $card_r         = $card_r_model->getList(['where' => $card_r_where]);
        echo $card_r_model->getLastSql();

    }

    public function change_business_project()
    {
//        t_db_ly_settle_order
        $model  = new DbLySettleOrder();
        $id     = input('ly_id');
        $p_name = input('p_name');
        if (!$id) {
            dd('22222');
        }
        $res = $model->saveData(['business_project' => $p_name], ['id' => $id]);
        dd($res);
    }

    public function del_cj_order()
    {
        $order_code = input('order_code', 'GWAPP230725211558j9i');
        $model      = new DbUserDrawRecord();
        $res        = $model->where(['order_code' => $order_code])->delete();
        dd($res);

    }

    public function del_qc_ff_jf()
    {
        $order_code = input('order_codes', "'QCSM230521162503LJJ','QCAPP230525094205eys','QCAPP230531115634Xr5','QCSM230604144050uoG','QCSM230606121900h6R','QCSM230606122232RJv','QCSM2306061224416AI','QCSM230606123107mMM','QCSM230606130928Nbu','QCSM230606131243hAG','QCSM230607110437RpN','QCSM230609100728twS','QCAPP230613225523dNH','QCAPP230615000405ohE','QCAPP230616154143uTB','QCSM230618210854paN','QCSM230711084610Cwr','QCAPP230711123937KDn','QCSM230711152925F6m','QCAPP230712080100yp4','QCSM230718163040Id5','QCAPP230720135839Qws','QCAPP2307221635343E3','QCSM230722214719GXs','QCSM2307241534282h4','QCSM230724154349bPm','QCSM2307241605292Qb','QCAPP230725001315B0G','QCAPP2307251449329lW','QCAPP230726091007iuU','QCAPP230730084551oJ0','QCSM230730160248K7d','QCAPP230730162753NNm','QCSM230730162300cCo','QCAPP230730173829Fto','QCSM230730175226wNR','QCSM230730180301mGp','QCSM230730184810BwA','QCSM2307302034245P4','QCSM230730204318SX4','QCAPP2307302103122bw','QCAPP230730210850OCH','QCAPP2307302111544Kk','QCAPP230730211708pFg','QCSM230730215449wEz','QCSM230730220029XYS','QCSM230730220221GVo','QCSM230730220152wTJ','QCSM230731063305kEV'");
        $model      = new  DbConsumeRuleRecord();
        $order_code = explode(',', $order_code);
        $res        = $model->saveData(['is_enable' => 0, 'modifier' => '异常修复', 'modified_date' => date('Y-m-d H:i:s')], ['order_code' => ['in', $order_code], 'give_status' => 0]);
        dd($model->getLastSql());

    }

    public function re_kou_point()
    {
        die("已结束");
        $order_codes = [
            'QCSM230521162503LJJ',
            'QCAPP230531115634Xr5',
            'QCSM230604144050uoG',
            'QCSM230606121900h6R',
            'QCSM230606122232RJv',
            'QCSM2306061224416AI',
            'QCSM230606123107mMM',
            'QCSM230606130928Nbu',
            'QCSM230606131243hAG',
            'QCSM230607110437RpN',
            'QCSM230609100728twS',
            'QCAPP230615000405ohE',
            'QCAPP230616154143uTB',
            'QCSM230618210854paN'
        ];
        $ic_card_no  = [
            'PVQK2023030144804',
            'PVQK2023030856902',
            'PVQK2023030449473',
            'PVQK2019111636766',
            'PVQK2019111636766',
            'PVQK2019111636766',
            'PVQK2019111636766',
            'PVQK2019111636766',
            'PVQK2019111636766',
            'PVQK2023030653145',
            'PVQK2023030652878',
            'PVQK2023033129143',
            'PVQK2023033129143',
            'PVQK2023042066820'
        ];
        $point_arr   = [
            20,
            380,
            20,
            60,
            60,
            60,
            60,
            140,
            140,
            740,
            3760,
            100,
            20,
            660
        ];
        $Car         = new Carer();
        $log_model   = new DbLog();
        foreach ($order_codes as $k => $v) {
            $data                = array(
                'vin'        => '',
                'ic_card_no' => $ic_card_no[$k],
                'order_id'   => $v,
                'dlr_code'   => 'QCSM',
                'deal_man'   => 'QCSM',//处理人
                'point'      => $point_arr[$k],
                'dlr_point'  => 0,
                'remark'     => "充值错误回扣",
                'brand_code' => 2,//品牌
            );
            $data['member_type'] = 1;

            $point_res = $Car->deductPoint($data);
            $log_data  = array(
                'type'         => 'integral',
                'send_note'    => json_encode_cn($data),
                'receive_note' => json_encode_cn($point_res),
            );
            if ($point_res == 'ok') {
                $log_data['is_success'] = 'success';
            } else {
                $log_data['is_success'] = 'fail';
            }
            $res = $log_model->insertData($log_data);
        }

    }


    public function in_sys_log()
    {

        $controller           = input('controller');
        $model_sys_log_module = new SysLogModule();
        $data['controller']   = $controller;
        $data['log_on']       = 1;
        $res                  = $model_sys_log_module->insertData($data);
        dd($res);

    }

    public function cl_way_bill()
    {
//        waybill_number
        $order_code  = input('order_code');
        $order_model = new BuOrder();
        $res         = $order_model->saveData(['waybill_number' => ''], ['order_code' => $order_code]);
        dd($res);
    }

    public function change_qy_dep()
    {
        $sql   = "INSERT INTO `dealer`.`t_bu_qy_department_all`(`type`, `departid`, `name`, `parentid`, `order`, `dlr_code`, `dlr_name`, `is_enable`, `dlr_code_echo`) VALUES (0, 98, '售后服务部', 70002, NULL, 'Y4011', '中山众杰港口授权店', 1, 'Y4011'),(0, 97, '售后服务部', 70002, NULL, 'Z2943', '中山众杰延龄分店', 1, 'Z2943'),(0, 96, '售后服务部', 70002, NULL, 'Z2948', '中山众杰三乡分店', 1, 'Z2948')";
        $model = new BuQyDepartmentAll();
        $res   = $model->getOne(['where' => ['departid' => 97]]);
//        $user_id =  input('userid');
        if (!$res) {
            $model->query($sql);
        }
        $user_model = new BuQyUserAll();
        $row        = $user_model->saveData(['department' => 98], ['userid' => 318024]);
        $row1       = $user_model->saveData(['department' => 97], ['userid' => 257649]);
        $row2       = $user_model->saveData(['department' => 96], ['userid' => 191649]);
        echo $row . $row1 . $row2;
    }

    //后台日志模块
    public function sys_log_model()
    {
        $id       = input('id');
        $ccc_name = input('c_name');
        $ccc      = input('ccc');
        $model    = new SysLogModule();
        $data     = [
            'controller_name' => $ccc_name,
            'controller'      => $ccc
        ];
        if (!$id) {
            $res = $model->insertData($data);
            var_dump((['r' => $res, 'sql' => $model->getLastSql()]));
        } else {
            $res = $model->saveData($data, ['id' => $id]);
            var_dump((['r' => $res, 'sql' => $model->getLastSql()]));
        }

    }

    public function test_supp_order()
    {
        $pay_s = new PaymentSystem();
        $month = (int)input('month', date('m', strtotime(date('Y-m-01') . " - 1 month")));
        $res   = $pay_s->supplier_order(1, $month);
        dd($res);
    }


    public function card_r_change()
    {
        $data = input('');
        $ids  = $data['ids'] ?? '';
        if (empty($ids)) dd('no ids');
        unset($data['ids']);
        unset($data['user_token']);
        unset($data['user_data']);
        (new BuCardReceiveRecord())->saveData($data, ['id' => ['in', $ids]]);
        echo (new BuCardReceiveRecord())->getLastSql();
    }

    //吧point is_enable=0
    public function del_point_order()
    {
        $point_order_model = new BuOrderPoint();
        $id                = input('id');
        $is_enable         = input('enable', 0);
        $order_code        = input('order_code', '');
        if ($order_code) {
            $list = $point_order_model->getList(['where' => ['order_code' => $order_code]]);
            print_json($list);
        } else {
            $res = $point_order_model->saveData(['is_enable' => $is_enable], ['id' => $id]);
            echo $point_order_model->getLastSql();
            echo "...";
            var_dump($res);
        }
    }


    /**
     * 小程序版本重复扣积分回退
     */
    public function new_dedu_point_order_point_more()
    {
        $point_model           = new BuOrderPoint();
        $point_order_codes     = input('point_order_codes', "GWAPP2309131425211KP11,GWAPP230913170555M5q11,GWAPP230913170751TvX11,GWAPP230913170841LNY11,GWAPP230913171154Sf511,GWAPP230913171408ELD11,GWAPP230913171811oGS11,GWAPP230913172110aAz11,GWAPP2309131723021GN11,GWAPP230913172837Nqz11,GWAPP230913174111V3E11,GWAPP230913174313cLs11,GWAPP230913174327f8M11,GWAPP2309131749361i411,GWAPP230913175104ZF311,GWAPP230913175148aG611,GWAPP230913175420hwJ11,GWAPP230913175453Ysg11,GWAPP230913181540S9B11,GWAPP230913181833Kxg11,GWAPP230913181859pb811,GWAPP230913182553Xjo21,GWAPP230913184009lV711,GWAPP230913191555J5Z11,GWAPP230913191814DEJ11,GWAPP230913193426J6911,GWAPP230913193738mSE11,GWAPP230913194345MSk11,GWAPP230913194752ipf11,GWAPP230913194930AL811,GWAPP230913195427qYa11,GWAPP230913200136xmj11,GWAPP230913200838KhY11,GWAPP2309132011138Jx11,GWAPP230913203222jzR11,GWAPP230913203939N6X11,GWAPP230913204344Qw611,GWAPP230913204557NAQ11,GWAPP230913205221X8I11,GWAPP230913205635RXN11,GWAPP2309132106382e511,GWAPP2309132108184IO11,GWAPP230913211229H0f11,GWAPP230913211635Vod11,GWAPP230913211813hqM11,GWAPP230913212145hmj11,GWAPP230913212345ro111,GWAPP230913212434y1S11,GWAPP2309132124367iD11,GWAPP230913212808NJM11,GWAPP230913212917oaX11,GWAPP23091321292982l11,GWAPP230913213009KhA11,GWAPP230913213736RDo11,GWAPP230913214020c0h11,GWAPP2309132141031sL11,GWAPP230913214504pwF11,GWAPP230913214702oFR11,GWAPP230913215213ok211,GWAPP230913215455U9U11,GWAPP230913215852fMN11,GWAPP230913221259wds11,GWAPP230913222251QuE11,GWAPP230913223144UTN11,GWAPP230913223647dct11,GWAPP230913223846xCv11,GWAPP2309132242058Ed11,GWAPP230913225706V5s11,GWAPP2309132304505qR11,GWAPP2309132307388f111,GWAPP230913230909PLf11,GWAPP230913230923NNf11,GWAPP230913231144HNT11,GWAPP230913231904ev011,GWAPP2309132319481dz11,GWAPP23091323294043Q11,GWAPP230913233933Nv311,GWAPP230913233938hIb11,GWAPP230913234051uTk11,GWAPP230914001051dku11,GWAPP230914003554qlw11,GWAPP2309140040408oy11,GWAPP2309140041072Yv11,GWAPP230914004259khd11,GWAPP230914004956zWn11,GWAPP2309140154595AY11,GWAPP230914020335pHS11,GWAPP230914040043QoF11,GWAPP230914061120keO11,GWAPP230914074003GCm11,GWAPP230914075503UX611,GWAPP230914081827Mqr11,GWAPP230914083309IPE11,GWAPP230914083634l2811,GWAPP230914090151Rci21,GWAPP230914090400mxr11,GWAPP230914090404e8P11,GWAPP230914090746xu811,GWAPP230914091206UN811,GWAPP230914091821Ajb11,GWAPP230914091947lcP11,GWAPP2309140936033Cn11,GWAPP230914094618rzU11,GWAPP230914094845t1o11,GWAPP230914095152nMh11,GWAPP230914095728hZC11,GWAPP230914095946yoh11,GWAPP230914100405qo511,GWAPP2309141029119ij11,GWAPP230914102954k6I21,GWAPP230914105540M3011,GWAPP2309141058060VO11,GWAPP230914110012gFl11,GWAPP230914111007On911,GWAPP230914111026M3311,GWAPP230914111052Ddv11,GWAPP230914111217uc211,GWAPP2309141113480vO11,GWAPP230914111427nN311,GWAPP230914111849HG211,GWSM230912185953cit11,GWSM230913170821fqL11,GWSM230913171519nJL11,GWSM230913171620OnU11,GWSM230913174805qsJ11,GWSM230913174902IYO11,GWSM23091317501554i11,GWSM230913184142IfF11,GWSM2309131842334tJ11,GWSM230913190003JSe11,GWSM230913195733A9C11,GWSM230913203152Yi911,GWSM230913212911URY11,GWSM230913213856R5b11,GWSM230913232004cJS11,GWSM230914000418f4w11,GWSM230914052831yfs11,GWSM23091406392808q11,GWSM230914102535BjH11,GWSM230914111041L9r11,QCAPP230912124206b5w11,QCSM230913192931Upw11");
        $member_type           = input('member_type');
        $dlr_code              = input('new_dlr', '');
        $r_dedu                = input('r_dedu', 0);
        $brand                 = input('brand', 1);
        $point_order_codes_arr = explode(',', $point_order_codes);
        foreach ($point_order_codes_arr as $point_order_code) {
            $point_order = $point_model->getOne(['where' => ['point_order_code' => $point_order_code]]);
            $Car         = new Carer();
            if ($point_order) {
                $log_model = new DbLog();

                $data      = array(
                    'ic_card_no' => $point_order['ic_card_no'],
                    'vin'        => $point_order['vin'],
                    'order_id'   => $point_order['point_order_code'],
                    'dlr_code'   => $point_order['dlr_code'],
                    'deal_man'   => $point_order['dlr_code'],//处理人
                    'brand'      => $brand,//品牌
                );
                $point_res = $Car->antiBalance($data);
                $log_data  = array(
                    'type'         => 'antiBalance',
                    'modifier'     => '手工退',
                    'send_note'    => json_encode_cn($data),
                    'receive_note' => json_encode_cn($point_res),
                );
                if ($point_res) {
                    $log_data['is_success'] = 'success';
                } else {
                    $log_data['is_success'] = 'fail';
                }
                $res = $log_model->insertData($log_data);

                if ($r_dedu) {
                    $is_cc_ok  = 0;
                    $new_point = $point_order['point'];
                    $data      = array(
                        'vin'        => $point_order['vin'],
                        'ic_card_no' => $point_order['ic_card_no'],
                        'order_id'   => $point_order['point_order_code'] . '1',
                        'dlr_code'   => $dlr_code ? $dlr_code : $point_order['dlr_code'],
                        'deal_man'   => $dlr_code ? $dlr_code : $point_order['dlr_code'],//处理人
                        'point'      => $new_point,
                        'dlr_point'  => 0,
                        'remark'     => $point_order['remark'],
                        'brand_code' => $brand,//品牌
                    );
                    if ($member_type == 1) {
                        $data['member_type'] = 1;
                    }
                    $point_res = $Car->deductPoint($data);
                    $log_data  = array(
                        'type'         => 'integral',
                        'send_note'    => json_encode_cn($data),
                        'receive_note' => json_encode_cn($point_res),
                    );
                    if ($point_res == 'ok') {
                        $log_data['is_success'] = 'success';
                    } else {
                        $log_data['is_success'] = 'fail';
                        $is_cc_ok               = 1;
                    }
                    $point_data = [
                        'order_code'       => $point_order['order_code'],
                        'point_order_code' => $data['order_id'],
                        'vin'              => $data['vin'],
                        'ic_card_no'       => $data['ic_card_no'],
                        'dlr_code'         => $data['dlr_code'],
                        'creator'          => '手工',
                        'point_type'       => $point_order['point_type'],
                        'all_point'        => $point_order['all_point'],
                        'point'            => $data['point'],
                        'use_state'        => $is_cc_ok + 1,
                        'remark'           => $data['remark'],
                        'send_note'        => json_encode($data),
                        'back_json'        => json_encode_cn($point_res),
                    ];
                    $point_model->insertData($point_data);
                    if ($point_data['use_state'] == 2) {
                        $order_model = new BuOrder();
                        $res         = $order_model->saveData(['is_cc_ok' => 1], ['order_code' => $point_data['order_code']]);
                        Logger::error('net_order_usepoint', ['r' => $res, 'sql' => $order_model->getLastSql()]);
                    }
                    $point_model->saveData(['is_enable' => 0], ['point_order_code' => $point_order_code]);//把原来的弄成不可用
                    $res = $log_model->insertData($log_data);
                    $point_model->saveData(['is_enable' => 0], ['id' => $point_order['id']]);
                }

            }
        }


    }


    public function test_gg()
    {
        $list_model   = new DbCommodityJdSku();
        $images_model = new DbJdGoodsImage();
        $jd_sku_model = new DbJdSkuInfo();
        $jd_n         = new JdOrderN();

//        try {
        $sku_list       = $list_model->getList(['where' => ['is_enable' => 1], 'fields' => "sku_id", 'group' => 'sku_id']);
        $sku_list_chuck = array_chunk(array_column($sku_list, 'sku_id'), 100);
        foreach ($sku_list_chuck as $sk) {
            //获取图片 100张插入，
            $result  = $jd_n->getImage($sk);
            $img_res = $images_model->insertImage($result['result']);
            //获取上下架状态 array(1) { [0]=> array(2) { ["state"]=> int(0) ["sku"]=> int(1861100) } }
            $sku_state_arr = $jd_n->skuState($sk);

            $stat_arr  = [];
            $price_arr = [];
            if ($sku_state_arr) {
                foreach ($sku_state_arr as $sku_state) {
                    $stat_arr[$sku_state['sku']] = $sku_state;
                }
            }
//                [{"marketPrice":68.2,"nakedPrice":61.65,"price":67.2,"taxPrice":5.55,"jdPrice":67.2,"tax":9,"skuId":100013875646},{"marketPrice":170,"nakedPrice":105.31,"price":119,"taxPrice":13.69,"jdPrice":119,"tax":13,"skuId":100002688183}]
            $sku_price_arr = $jd_n->getSellPrice($sk);
            if ($sku_price_arr) {
                foreach ($sku_price_arr as $sku_price) {
                    $price_arr[$sku_price['skuId']] = $sku_price;
                }
            }
            $sku_detail_arr = [];

            foreach ($sk as $skv) {
                $sku_detail = $jd_n->getDetail($skv);
                if ($sku_detail) {
                    $jd_sku_model->insertGoods($sku_detail, $stat_arr[$skv] ?? [], $img_res[$skv] ?? [], $price_arr[$skv] ?? []);
                }
//                    $sku_detail_arr[$skv] = $sku_detail;
            }

        }
        sleep(1);
        $q_data = ['ss_type' => 'jd_contrast'];
        Queue::push('app\common\queue\CommodityJd', json_encode($q_data), config('queue_type.order'));


//        } catch (\Exception $e) {
//            Log::error("order  error : " . $e->getMessage() . ' : ');
//        }
    }

    public function test_app_carer()
    {
        $one_id        = input('one_id');
        $appMemberInfo = QuickWin::create('e3s_dlr')->appMemberInfo(['oneid' => $one_id]);
        print_json($appMemberInfo);

    }

    public function test_dlr_yx()
    {
        $dlr_arr_one = MB::create('market_base')->dealerOne(['is_enable' => 2]);
        print_json($dlr_arr_one);
    }


    public function cc_path()
    {
        $filenew = input('filenew');
        $path    = config('upload.path') . $filenew;
        if (!file_exists($path)) {
            mkdir($path, 0777, 'r');
        }
        return $path;
    }

    public function del_by()
    {
        $maintain_code = input('maintain_code');
        $model         = new E3sPackage();
//        $where =  ['maintain_group_code'=>['in',explode(',',$maintain_code)]];
        $where = sprintf("maintain_group_code in (%s) ", $maintain_code);
        $res   = $model->saveData(['is_enable' => 0, 'modifier' => 'hand_del'], $where);
        echo $model->getLastSql();
        dd($res);

    }


    public function test_ccc_config()
    {
        var_dump(config('cache'));
    }


    public function test_mm_in()
    {
        $order_goods_model = new BuOrderCommodity();
        $order_code        = input('order_code');
        $order_goods_where = ['parent_order_code' => $order_code, 'mo_sub_id' => 0, 'is_enable' => 1];
        $order_goods_list  = $order_goods_model->getList(['where' => $order_goods_where]);
        $all_goods_price   = 0;
        foreach ($order_goods_list as $v) {
            $all_goods_price += ($v['b_act_price'] * $v['count']) - $v['all_dis'];
        }
        var_dump($all_goods_price);

    }

    public function clpss()
    {
        $type = input('pstype', 0);
//        if(!$type && $type!==0){
//            die('Please select die');
//        }
        $pss_inout_model = new PssInventoryInout();
        $sql             = sprintf("  SELECT a.* from t_pss_inventory_inout a
join t_bu_order_commodity b on  a.order_commodity_id=b.id
where a.`inout`=3 and a.is_enable=1 and b.order_commodity_status=%s ", $type);
        $list            = $pss_inout_model->query($sql);
        $pss_id_arr      = [];
        $od_c_id_arr     = [];
        foreach ($list as $v) {
            $pss_id_arr[]  = $v['id'];
            $od_c_id_arr[] = $v['order_commodity_id'];
        }
        if ($type === 0 || $type == 7) {
            $where = ['id' => ['in', $pss_id_arr]];

            $pss_inout_model->saveData(['is_enable' => 0, 'modifier' => 'clp' . date('md')], $where);
            echo $pss_inout_model->getLastSql();
            die();
        }
        if (in_array($type, [4, 5])) {
            $order_c_model = new BuOrderCommodity();
            $order_c_list  = $order_c_model->getList(['where' => ['id' => ['in', $od_c_id_arr]]]);
            $in_stock      = new Inventory();
            $in_stock->outbound_stock($order_c_list, '');
        }
        echo 'ok';

    }

    //查询备件对应车型信息
    public function getE3sPartInfo()
    {
        $main_code = input('mcode');
        $car_n     = input('18n');
        $type      = input('car_type', 1);//1套餐 2备件
        if ($type == 1) {
            $e3s_model = new E3sMaintenanceProduct();
            $sql       = sprintf("SELECT b.*  FROM `dealer`.`t_e3s_maintenance_product_car_series` a
join t_e3s_car_series b on a.service_car_type=b.service_car_type  and b.is_enable=1
join t_e3s_maintenance_package c on  c.product_type_id=a.product_type_id and c.is_enable=1
where c.`maintain_group_code` = '%s' and a.is_enable=1", $main_code);
            if ($car_n) {
                $sql .= sprintf("  and   car_config_code='%s' ", $car_n);
            }
            $list = $e3s_model->query($sql);
            foreach ($list as $v) {
                echo $v['service_car_type'] . '--' . $v['car_series_cn'] . '--' . $v['car_config_code'] . $v['supply_status_cn'] . '<br/>';
            }
        }
        if ($type == 2) {
            $car_model = new E3sPartCarSeries();
            $where     = ['car_config_code' => $car_n];
            if ($main_code) {
                $where['part_no'] = $main_code;
            }
            $list = $car_model->getList(['where' => $where]);
            print_json($list);
        }
    }

    //特殊处理保养套餐
    public function cl_by_tc()
    {
        $type     = input('type', 1);//1 逍客 2 天籁，3处理天籁其他车型，4启辰DDI
        $goods_id = input('goods_id', 5993);

        if ($type == 1) {
            $sql     = sprintf("SELECT id FROM `t_db_commodity_sku` WHERE `commodity_id` = '%s' AND `sp_value_list` LIKE '%s' AND `sku_code` LIKE '%s'", $goods_id, "%,901%", "%_Q%");
            $model   = new DbCommoditySku();
            $list    = $model->query($sql);
            $sku_ids = array_column($list, 'id');
            $res     = $model->saveData(['is_enable' => 0], ['id' => array('in', $sku_ids)]);

            echo $model->getLastSql() . '===' . $res . '<br/>';
            //设置套餐车型 其它为 0
            $car_ids   = "58615,58617,61356,61357,61358,61359,61360,61361,61362,61363,61364,61365,61366,61367,61368,61369,61370,61371,61372,61373,61374,61375,61428,61429,61430,61431,61432,61433,61434,61435,61436,61437,61438,61439,61440,61441,61442,56887,56891,61490,61491,61498,61499,61500,61501,61502,61503,61504,61505,61506,61507,61508,61509,61510,61511,61512,61513,61514,61515,61516,61483,61484,61485,61486,61487,61488,61489,61517,61518,61519,61520,61521,61522,61523,61524,61525,58614,58616,61391,61392,61393,61394,61395,61396,61397,61398,61399,61400,61401,61402,61403,61404,61405,61406,61407,61408,61409,61410,61376,61377,61378,61379,61380,61381,61382,61383,61384,61385,61386,61387,61388,61389,61390";
            $where     = ['id' => ['in', $car_ids]];
            $e3s_model = new E3sMaintenanceProductCarSeries();
            $res       = $e3s_model->saveData(['is_enable' => 0], $where);
            echo $e3s_model->getLastSql() . '===' . $res . '<br/>';

            $row = $this->save_tc($goods_id);
            echo $row;

            $res = $model->saveData(['is_enable' => 1], ['id' => array('in', $sku_ids)]);
            echo $model->getLastSql() . '===' . $res . '<br/>';

            $where = ['id' => ['in', $car_ids]];
            $res   = $e3s_model->saveData(['is_enable' => 1], $where);
            echo $e3s_model->getLastSql() . '===' . $res . '<br/>';


        } elseif ($type == 2) {
            $sql     = sprintf("SELECT id FROM `t_db_commodity_sku` WHERE `commodity_id` = '%s' AND `sp_value_list` LIKE '%s' AND sku_code LIKE '%s'", $goods_id, "%,902%", "%_ASOIL_L260_NKV_%");
            $model   = new DbCommoditySku();
            $list    = $model->query($sql);
            $sku_ids = array_column($list, 'id');
            $res     = $model->saveData(['is_enable' => 0], ['id' => array('in', $sku_ids)]);

            echo $model->getLastSql() . '===' . $res . '<br/>';
            //设置套餐车型 其它为 0
            $car_ids   = "9797,61595,61605,61356,61357,61358,61359,61360,61361,61362,61363,61364,61365,61366,61367,61368,61369,61370,61371,61372,61373,61375,61428,61429,61430,61431,61432,61433,61434,61435,61436,61437,61438,61439,61440,61441,5806,61616,61617,61483,61484,61485,61486,61487,61488,61517,61518,61519,61520,61521,61522,61523,61524,61525,1496,61609,61610,61376,61377,61378,61379,61380,61381,61382,61383,61384,61385,61386,61387,61388,61389,43557,42454,43326,43445,43454,43468,43514,43515,43516,43517,43518,43546,43547,43548,43549,43550,43551,43552,43553,43554,43555,43556,43557,41,300,303,557,558,559,560,561,562,786,945,1930,2088,2497,2710,2826,62,301,304,333,334,335,336,337,338,787,946,1928,2460,2499,2705,42456,43206,43328,43420,43421,43422,43423,43424,43425,43426,43427,43428,43429,43430,43431,43432,43433,43434,43435,43447,43456,43470,64,302,305,528,529,530,531,532,533,788,980,1929,2461,2577,2708";
            $where     = ['id' => ['in', $car_ids]];
            $e3s_model = new E3sMaintenanceProductCarSeries();
            $res       = $e3s_model->saveData(['is_enable' => 0], $where);
            echo $e3s_model->getLastSql() . '===' . $res . '<br/>';

            $row = $this->save_tc($goods_id);
            echo $row;
            $res = $model->saveData(['is_enable' => 1], ['id' => array('in', $sku_ids)]);
            echo $model->getLastSql() . '===' . $res . '<br/>';

            $where = ['id' => ['in', $car_ids]];
            $res   = $e3s_model->saveData(['is_enable' => 1], $where);
            echo $e3s_model->getLastSql() . '===' . $res . '<br/>';


            $sql     = sprintf("SELECT id FROM `t_db_commodity_sku` WHERE `commodity_id` = '%s' AND `sp_value_list` LIKE '%s' AND sku_code not LIKE '%s'", $goods_id, "%,902%", "%_ASOIL_L260_NKV_%");
            $model   = new DbCommoditySku();
            $list    = $model->query($sql);
            $sku_ids = array_column($list, 'id');
            $res     = $model->saveData(['is_enable' => 0], ['id' => array('in', $sku_ids)]);

            echo $model->getLastSql() . '===' . $res . '<br/>';

            $car_ids = "61374,61442,61491,61489,61410,61390";//心悦+老优惠
            $where   = ['id' => ['in', $car_ids]];
            $res     = $e3s_model->saveData(['is_enable' => 0], $where);
            echo $e3s_model->getLastSql() . '===' . $res . '<br/>';

            $row = $this->save_tc($goods_id);
            echo $row;

            $res = $model->saveData(['is_enable' => 1], ['id' => array('in', $sku_ids)]);
            echo $model->getLastSql() . '===' . $res . '<br/>';

            $res = $e3s_model->saveData(['is_enable' => 1], ['id' => ['in', $car_ids]]);
            echo $e3s_model->getLastSql() . '===' . $res . '<br/>';


        } elseif ($type == 3) {
            //老优惠  SELECT * from t_e3s_maintenance_product_car_series where product_type_id in  ('CF2DB82A03D52E29E0531E891AAC7BC4','829B78F3A3854C0FE0531E891AAC6A3D') and  `service_car_type` = 'DFL7201(L34\\2.0T\\CVT)';

            //处理其他油不匹配
            $sql     = sprintf("SELECT id FROM `t_db_commodity_sku` WHERE `commodity_id` = '%s' AND
FIND_IN_SET('BDRALHZL34TXAD---B',relate_car_18n)  and `sp_value_list` not LIKE '%s'", $goods_id, "%,902%");
            $model   = new DbCommoditySku();
            $list    = $model->query($sql);
            $sku_ids = array_column($list, 'id');
            $res     = $model->saveData(['is_enable' => 0], ['id' => array('in', $sku_ids)]);

            echo $model->getLastSql() . '===' . $res . '<br/>';
            $car_ids   = "61374,61442,61491,61489,61410,61390";
            $where     = ['id' => ['in', $car_ids]];
            $e3s_model = new E3sMaintenanceProductCarSeries();
            $res       = $e3s_model->saveData(['is_enable' => 0], $where);
            echo $e3s_model->getLastSql() . '===' . $res . '<br/>';

            $row = $this->save_tc($goods_id);
            echo $row;
            $res = $model->saveData(['is_enable' => 1], ['id' => array('in', $sku_ids)]);
            echo $model->getLastSql() . '===' . $res . '<br/>';

            $where = ['id' => ['in', $car_ids]];
            $res   = $e3s_model->saveData(['is_enable' => 1], $where);
            echo $e3s_model->getLastSql() . '===' . $res . '<br/>';
        } elseif ($type == 4) { //启辰ddi
            $sql     = "SELECT id FROM t_db_commodity_sku WHERE commodity_id = '5997' AND sp_value_list LIKE '%,903%' AND `sku_code` LIKE '%4L_SUPOIL_L241_%'";
            $model   = new DbCommoditySku();
            $list    = $model->query($sql);
            $sku_ids = array_column($list, 'id');
            $res     = $model->saveData(['is_enable' => 0], ['id' => array('in', $sku_ids)]);

            echo $model->getLastSql() . '===' . $res . '<br/>';
            $car_ids   = "6016,6017,56511,56514,56577,56578,56579,56580,56581,56582,56583,56584,56585,56586,56587,56588,56589,56590,56591,56592,56593,56594,56595,56596,56597,56598,56599,56600,56601,56602,56613,56614,56615,56616,56617,61077,61078,61079,61080,61081,61082,61083,61084,61085,61086,61087,61088,61089,61090,61091,61092,61093,61094,61095,61096,61097,61098,61099,61100,61101,61102,61103,61104,61105,61106,61107,61108,61109,1718,1719,1720,1955,1956,62178,62179,62180,62181,62182,42169,42170,9213,9214,9215,9216,9217,9218,9219,9220,9221,9222,9223,9224,9225,9226,9227,9228,9256,9257,9258,9259,9260,9261,9262,9263,9264,9265,9266,9267,9268,9269,9270,9271,9272,9273,9274,9275,61051,61123,61124,61125,61126,61127,61128,61129,61130,61131,61132,61133,61134,61135,61136,61137,61138,61139,61140,61141,61142,61143,61144,61145,61146,61147,61148,61149,61150,61178,61179,61180,61181,61182,61183,61184,61185,61186,9880,9881,9882,9883,62187,62188,62189,62190,2039,2040,42412,42413,42414,42415,42416,42417,42418,42419,42420,42421,42422,42423,42424,42425,42426,42427,42428,42429,42430,42431,42432,42433,42434,42435,42436,42867,42868,42869,42870,42871,42872,42873,42874,42875,42876,42877,61052,61053,61054,61055,61056,61057,61058,61059,61060,61061,61062,61063,61064,61065,61066,61067,61068,61069,61070,61071,61072,61073,61074,61075,61076,61110,61111,61112,61113,61114,61115,61116,61117,61118,61119,61120,61121,61122,6067,6068,6069,6070,62184,62185,62186,62193";
            $where     = ['id' => ['in', $car_ids]];
            $e3s_model = new E3sMaintenanceProductCarSeries();
            $res       = $e3s_model->saveData(['is_enable' => 0], $where);
            echo $e3s_model->getLastSql() . '===' . $res . '<br/>';

            $row = $this->save_tc(5997);
            echo $row;
            $where = ['id' => ['in', $car_ids]];
            $res   = $e3s_model->saveData(['is_enable' => 1], $where);
            echo $e3s_model->getLastSql() . '===' . $res . '<br/>';
            //五：套餐服务车型设置回 （把ddi的服务车型取消掉 防止其它产品的套餐编码包含有ddi）

            $car_ids2 = "62177,62183,62191,62192,62194,62195";
            $where2   = ['id' => ['in', $car_ids2]];
            $res      = $e3s_model->saveData(['is_enable' => 0], $where2);
            echo $e3s_model->getLastSql() . '===' . $res . '<br/>';

            $row = $this->save_tc(5997, 1);
            echo $row;

            $res = $model->saveData(['is_enable' => 1], ['id' => array('in', $sku_ids)]);
            echo $model->getLastSql() . '===' . $res . '<br/>';

            $res = $e3s_model->saveData(['is_enable' => 1], $where2);
            echo $e3s_model->getLastSql() . '===' . $res . '<br/>';

        }


    }

    //修改备件不可用--
    public function save_part_no()
    {

        $car_code  = input('card_code');
        $par_no    = input('part_no');
        $is_enable = input('is_enable', 0);
        $goods_id  = input('goods_id', 0);
        if (!$goods_id) {
            die('商品ID--');
        }
        $par_no    = explode(',', $par_no);
        $car_code  = explode(',', $car_code);
        $e3s_model = new E3sPartCarSeries();
        //SELECT * FROM `dealer`.`t_e3s_part_car_series`  where car_config_code='BDTALDZ10NEXA---AK' and part_no in (
        //'D40602GG0A-C251','D40602GG0AKV-D532')
        $res       = $e3s_model->saveData(['is_enable' => $is_enable], ['car_config_code' => ['in', $car_code], 'part_no' => ['in', $par_no]]);
        $where     = ['commodity_id' => $goods_id, 'is_enable' => 1, 'sku_code' => ['in', $par_no]];
        $sku_model = new DbCommoditySku();
        $sku_list  = $sku_model
            ->where($where)
            ->field('id,sku_code')
            ->select();
        echo $sku_model->getLastSql();
        echo "---";
        echo json_encode($sku_list);
        if (!empty($sku_list)) {
            foreach ($sku_list as $value) {
                $sku_code   = explode(',', $value['sku_code']);
                $list       = $e3s_model->getList(['where' => ['part_no' => ['in', $sku_code], 'is_enable' => 1]]);
                $car_c_code = array_column($list, 'car_config_code');

                $car_18n        = Db::name('e3s_car_series')->where(['car_config_code' => array('in', $car_c_code)])->field('id,car_config_code')->select();
                $relate_car_18n = [];
                $relate_car_ids = [];
                foreach ($car_18n as $val) {
                    $relate_car_18n[] = $val['car_config_code'];
                    $relate_car_ids[] = $val['id'];
                }
                $find  = $sku_model->where(['id' => $value['id']])->update([
                    'relate_car_18n' => implode(',', $relate_car_18n),
                    'relate_car_ids' => implode(',', $relate_car_ids)
                ]);
                $find1 = Db::name('db_commodity_set_sku')->where(['commodity_sku_id' => $value['id']])->update([
                    'relate_car_18n' => implode(',', $relate_car_18n),
                    'relate_car_ids' => implode(',', $relate_car_ids)
                ]);
                $res   .= $find . '<br/>';
                echo $sku_model->getLastSql();
            }
        }


    }


    private function save_tc($goods_id = 5993, $is_enable = 0)
    {
        $where    = ['commodity_id' => $goods_id, 'is_enable' => $is_enable];
        $sku_list = Db::name("db_commodity_sku")
            ->where($where)
            ->field('id,sku_code')
            ->select();
        $res      = '';
        if (!empty($sku_list)) {
            $model = new E3sPackage();
            foreach ($sku_list as $value) {
                $sku_code       = explode(',', $value['sku_code']);
                $list           = $model->alias('a')
                    ->join('t_e3s_maintenance_product_car_series b', 'a.product_type_id = b.product_type_id')
                    ->where(['a.maintain_group_code' => array('in', $sku_code), 'b.is_enable' => 1])
                    ->column('b.service_car_type');
                $car_18n        = Db::name('e3s_car_series')->where(['service_car_type' => array('in', $list)])->field('id,car_config_code')->select();
                $relate_car_18n = [];
                $relate_car_ids = [];
                foreach ($car_18n as $val) {
                    $relate_car_18n[] = $val['car_config_code'];
                    $relate_car_ids[] = $val['id'];
                }
                $find  = Db::name('db_commodity_sku')->where(['id' => $value['id']])->update([
                    'relate_car_18n' => implode(',', $relate_car_18n),
                    'relate_car_ids' => implode(',', $relate_car_ids)
                ]);
                $find1 = Db::name('db_commodity_set_sku')->where(['commodity_sku_id' => $value['id']])->update([
                    'relate_car_18n' => implode(',', $relate_car_18n),
                    'relate_car_ids' => implode(',', $relate_car_ids)
                ]);
                $res   .= $find . '<br/>';

            }
        }
        return $res;
    }

    public function del_afs_address()
    {
        $id    = input('add_id', 0);
        $model = new DbAfterSalePlatformAddresses();
        $where = ['is_enable' => 1];
        if ($id) {
            $where['id'] = $id;
        }
        $res = $model->saveData(['is_enable' => 0], $where);
        var_dump($res);
    }

    public function test_jiemi()
    {
        $pwd        = "iG6kQ7NK2zy2y64PekfxUFsgSClKoi nKt1 c6xx8m916K3dYChzJg9dcwzK/hNEbDaoCDVNy7iSzL q3Bd/b1YCAAOAIigiKMojwzJimFm2owL6H4HVNp7ul9YLqucU9rXVXjMEr1Q2xtwujPjVqcGt6YLc0tMBAdt5jsp4Afo=";
        $privateKey = '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

        $publicKey = '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2Cryy9expTRuoalXblBNWvfqS
uR0RdVI0t5WwxhswJwloytC9dMMkNqojAT4SGjzHTdfER0Ei43HVp0JpscxUGI2e
r5PYP+svRtJoWCfC1lUYxPw0jzktuQ2UQDSzovY6ioNtPYr5yJjazHyHsQ5ww1vp
hpDtwQKWyokZ9+BI8wIDAQAB
-----END PUBLIC KEY-----';

//        $encryptStr = 'BquYteSYXcEGNrzakdMXKZogkhAqlxnin4XqISXOfPjyLUDj6QWp8H5kd03fb/mfFs08fMqWIjXMK3OzYgmgeuKLzGPHbPu7FzqMGhtfXbZUM3z79STPhOX6ZIo9eEWUP7EOXUCvYNXF86V4WzbbA+qpycsTI4wYpxs0RLTXXyk=';
        openssl_private_decrypt(base64_decode($pwd), $decrypted_data, $privateKey);

        var_dump($decrypted_data);


    }

    public function test_key()
    {
        $tag_name = [16 => 'ms', 4 => 'sp', 5 => 'sp2'];
        $sort_arr = [16, 12, 19, 20, 17, 11, 14, 13, 10, 15, 161];
        var_dump(array_replace(array_flip($sort_arr), $tag_name));
    }


    public function test_idm_list()
    {
        $list  = Idm::create('idm')->accountSync(['size' => 100, 'page' => 1]);
        $list2 = Idm::create('idm')->getRoles(['size' => 100, 'page' => 1]);
        print_json($list, $list2);
    }

    public function mk_file()
    {
        $path = "/var/www/html/runtime/trace/";

    }

    public function ml_zx()
    {
        $command = '/usr/local/bin/php think queue:listen --queue=e3s_dlr';
        exec($command, $output, $returnVar);

        // 处理输出或返回值
        if ($returnVar === 0) {
            // 命令成功执行
            return 'Command executed successfully';
        } else {
            // 命令执行失败
            return 'Command execution failed';
        }
    }

    public function test_queue_list()
    {
//        ini_set('memory_limit', '3000M');
        $key   = input('redis_key');
        $start = input('start');
        $end   = input('end');
        $redis = Cache::redisHandler();
        if (!$key) {
            $c_key                = "afm:queues:coupon";
            $e3s_key              = "afm:queues:e3s_dlr";
            $growth_key           = "afm:queues:growth";
            $push_order_key       = "afm:queues:push_order";
            $settlement_order_key = "afm:queues:settlement";
            echo $c_key . '--' . $redis->Llen($c_key) . "<br/>";
            echo $e3s_key . '--' . $redis->Llen($e3s_key) . "<br/>";
            echo $growth_key . '--' . $redis->Llen($growth_key) . "<br/>";
            echo $push_order_key . '--' . $redis->Llen($push_order_key) . "<br/>";
            echo $settlement_order_key . '--' . $redis->Llen($settlement_order_key) . "<br/>";
            die();
        }
        if (input('max')) {
            $max = 11;
        } else {
            $max = $redis->Llen($key);
        }
        if (input('list')) {
            $list = [];

        } else {
            $list = $redis->lrange($key, $start, $end);

        }
        $data = ['num' => $max, 'list' => $list];
        print_json(0, 'success', $data);
    }

    public function test_set()
    {
        $now_time     = date('Y-m-d H:i:s');
        $activity_img = DbCommoditySet::where(sprintf("`activity_image` <> '' AND `activity_start_time` <> '' AND `is_enable` = 1 and activity_start_time>='%s' and activity_end_time<='%s'", $now_time, $now_time))
            ->select();
        print_json($activity_img);
    }

    private function findCarTypeAdmin($sku_codes, $type = 0)
    {

        $error = [];
        if (empty($sku_codes)) {
            return $error;
        }
        $city_type = [];
        $part_time = [];
        if ($type == 0) {
            $spare_part    = new \app\common\model\e3s\E3sSparePart();
            $model         = new \app\common\model\e3s\E3sPartCarSeries();
            $CarSeries     = new \app\common\model\e3s\E3sCarSeries();
            $part_list     = $spare_part->where(['part_no' => $sku_codes, 'is_enable' => 1])
                ->field('part_no,sale_price,dlr_price,variety_code_mid_code,variety_code,variety_name')
                ->select();
            $dlr_code      = new E3sSpecificRelationPart();
            $part_del_code = $dlr_code->alias('a')->join('t_e3s_specific_relation_dlr b', 'a.spec_part_group_id = b.spec_part_group_id')
                ->where(['a.is_enable' => 1, 'b.is_enable' => 1, 'a.part_no' => $sku_codes])->column('b.dlr_code');
            $data          = [];
            foreach ($part_list as $value) {
                // 1120版本不上 start
                $fit_beg_time = $model->alias('a')
                    ->join('t_e3s_spare_part b', 'a.part_no = b.part_no')
                    ->where('a.part_no', $value['part_no'])
                    ->where('a.fit_beg_time', '<>', '')
                    ->where('a.is_enable', '=', 1)
                    ->field('a.part_no,b.sale_price,a.fit_beg_time,a.fit_end_time,b.variety_code_mid_code')
                    ->group('a.part_no,a.fit_beg_time,a.fit_end_time')
                    ->buildSql();
                $rep_part_no  = $model->alias('a')
                    ->join('t_e3s_spare_part b', 'a.part_no = b.part_no')
                    ->where(['b.rep_part_no' => array('like', '%' . $value['part_no'] . '%')])
                    ->where('a.fit_beg_time', '<>', '')
                    ->where('a.is_enable', '=', 1)
                    ->field('a.part_no,b.sale_price,a.fit_beg_time,a.fit_end_time,b.variety_code_mid_code')
                    ->union($fit_beg_time, false)
                    ->group('a.part_no,a.fit_beg_time,a.fit_end_time')
                    ->select();//查询原件是否是被人的替换件
                if (!empty($rep_part_no)) {
                    foreach ($rep_part_no as $k=>$val){
                        $n18 = $model->where(['part_no' => $val['part_no'], 'fit_beg_time' => $val['fit_beg_time'],
                            'fit_end_time' => $val['fit_end_time'], 'is_enable' => 1,
                        ])->column('car_config_code');
                        $work = $this->part_work_hour($val['part_no'],$val['fit_beg_time'],$val['fit_end_time']);
                        if(in_array($val['variety_code_mid_code'],[3,4,5,6,7])){
                            $val['fit_beg_time'] = $val['fit_end_time']='';// 只过滤备件上下架时间
                        }
                        $part_time = $val['fit_beg_time'].'_'.$val['fit_end_time'];
                        if(isset($data[$part_time])){
                            $array_18n = array_unique(array_merge(explode(',',$data[$part_time]['n_18']),$n18));
                            $data[$part_time]['n_18'] = implode(',',$array_18n);
                            $data[$part_time]['relate_car_work_hour'] = $data[$part_time]['relate_car_work_hour'] + $work['part_no_array'];

                        }else{
                            $data[$part_time] = [
                                'n_18' => implode(',',$n18),
                                'fit_beg_time' => $val['fit_beg_time'],
                                'fit_end_time' => $val['fit_end_time'],
                                'sku_code' => $value['part_no'],
                                'price' => $value['sale_price'],
                                'cost_price' => $value['dlr_price'],
                                'relate_car_work_hour' => $work['part_no_array'],
                                'e3s_bj_type_name' => implode(',', $work['bj_type_name']),
                                'rep_part_no' => '',
                                'relate_dlr_code' => implode(',',$part_del_code),
                            ];
                        }
                        $data[$part_time]['variety_code'] = $value['variety_code'];
                        $data[$part_time]['variety_name'] = $value['variety_name'];
                    }
                    $data = array_values($data);
                    foreach ($data as $key=>$item){
                        $relate_cars = $CarSeries->whereIn('car_config_code', $item['n_18'])
                            ->where('is_enable','=',1)->column('id');
                        $data[$key]['car_ids'] = implode(',',$relate_cars);
                        $data[$key]['relate_car_work_hour'] = json_encode($item['relate_car_work_hour']);

                    }
                }
            }
            return $data;
//            return ['n_18' => $relate_n18, 'car_ids' => $relate_cars,'part_time'=>$part_time];
        } elseif ($type == 2) {
            //pz1a套餐
            $where['a.sp_basic_code']     = array('in', $sku_codes);
            $city_type                    = Db::name('e3s_pz1a_maintenance_package')->alias('a')
                ->where($where)
                ->field('level_id')
                ->find();
            $city_type                    = $city_type['level_id'];
            $relate_n18                   = Db::name('e3s_pz1a_maintenance_package')->alias('a')
                ->join('t_e3s_pz1a_maintenance_car_series b', 'a.sp_basic_id = b.sp_basic_id')
                ->join('t_e3s_car_series c', 'b.service_car_type = c.service_car_type')
                ->where($where)
                ->group('c.car_config_code')
                ->column('c.car_config_code');
            $res_where['car_config_code'] = array('in', array_values(array_unique($relate_n18)));
            $res_where['is_enable']       = 1;
            $relate_cars                  = \app\common\model\e3s\E3sCarSeries::where($res_where)
                ->column('id');
        } elseif ($type == 1) {
            //老友惠和心悦获取18为码
            $where['a.maintain_group_code'] = array('in', $sku_codes);
            $where['a.is_enable']           = 1;
            $list                           = Db::name('e3s_maintenance_package')->alias('a')
                ->join('t_e3s_maintenance_product_car_series b', 'a.product_type_id = b.product_type_id')
                ->where($where)
                ->group('a.product_type_id')
                ->column('a.product_type_id');
            if (empty($list)) {
                $relate_n18  = [];
                $relate_cars = [];
            } else {
                $relate_n18                   = Db::name('e3s_maintenance_product_car_series')->alias('a')
                    ->join('t_e3s_car_series b', 'a.service_car_type = b.service_car_type')
                    ->where(['a.product_type_id' => array('in', $list), 'a.is_enable' => 1])
                    ->group('b.car_config_code')
                    ->column('b.car_config_code');
                $res_where['car_config_code'] = array('in', array_values(array_unique($relate_n18)));
                $res_where['is_enable']       = 1;
                $relate_cars                  = \app\common\model\e3s\E3sCarSeries::where($res_where)
                    ->column('id');
            }
        } else {
            $where['a.maintain_group_code'] = array('in', $sku_codes);
            $where['b.service_car_type']    = 'COMMOM_CARTYPE';
            $where['a.is_enable']           = 1;
            $where['b.is_enable']           = 1;
            $list                           = Db::name('e3s_maintenance_package')->alias('a')
                ->join('t_e3s_maintenance_product_car_series b', 'a.product_type_id = b.product_type_id')
                ->where($where)
                ->group('a.maintain_group_code')
                ->select();
            if (empty($list)) {
                $relate_n18 = Db::name('e3s_maintenance_package')->alias('a')
                    ->join('t_e3s_maintenance_product_car_series b', 'a.product_type_id = b.product_type_id')
                    ->join('t_e3s_car_series c', 'b.service_car_type = c.service_car_type')
                    ->where([
                        'a.maintain_group_code' => array('in', $sku_codes),
                        'a.is_enable'           => 1,
                        'b.is_enable'           => 1,
                        'c.is_enable'           => 1,
                    ])
                    ->column('c.car_config_code');
            } else {
                $relate_n18 = \app\common\model\e3s\E3sCarSeries::where('car_brand_code', $list[0]['dlr_brand_code'])
                    ->where('is_enable', '=', 1)
                    ->column('car_config_code');
            }
            $res_where['car_config_code'] = array('in', array_values(array_unique($relate_n18)));
            $res_where['is_enable']       = 1;
            $relate_cars                  = \app\common\model\e3s\E3sCarSeries::where($res_where)
                ->column('id');
        }
        return ['error' => $error, 'n_18' => array_values(array_unique($relate_n18)), 'car_ids' => array_unique($relate_cars), 'city_type' => $city_type, 'part_time' => $part_time];
    }


    private function findCarType($sku_codes, $type = 0)
    {

        $error = [];
        if (empty($sku_codes)) {
            return $error;
        }
        $city_type = [];
        $part_time = [];
        if ($type == 0) {

            $spare_part    = new \app\common\model\e3s\E3sSparePart();
            $model         = new \app\common\model\e3s\E3sPartCarSeries();
            $CarSeries     = new \app\common\model\e3s\E3sCarSeries();
            $part_list     = $spare_part->where(['part_no' => $sku_codes, 'is_enable' => 1])
                ->field('part_no,sale_price,dlr_price,variety_code_mid_code')
                ->select();
            $dlr_code      = new E3sSpecificRelationPart();
            $part_del_code = $dlr_code->alias('a')->join('t_e3s_specific_relation_dlr b', 'a.spec_part_group_id = b.spec_part_group_id')
                ->where(['a.is_enable' => 1, 'b.is_enable' => 1, 'a.part_no' => $sku_codes])->column('b.dlr_code');
            $data          = [];
            foreach ($part_list as $value) {
                //过滤 3机油、4变速箱油、5制动液、6防冻防锈液、7养护品，都不需要匹配车型和备件的上下线时间。

                //过滤 3机油、4变速箱油、5制动液、6防冻防锈液、7养护品，都不需要匹配车型和备件的上下线时间。--VV
                //改为只是不匹配时间而已--LZX
                $fit_beg_time = $model->alias('a')
                    ->join('t_e3s_spare_part b', 'a.part_no = b.part_no')
                    ->where('a.part_no', $value['part_no'])
                    ->where('a.fit_beg_time', '<>', '')
                    ->where('a.is_enable', '=', 1)
                    ->field('a.part_no,b.sale_price,a.fit_beg_time,a.fit_end_time,b.variety_code_mid_code')
                    ->group('a.fit_beg_time,a.fit_end_time')
                    ->buildSql();
                $rep_part_no  = $model->alias('a')
                    ->join('t_e3s_spare_part b', 'a.part_no = b.part_no')
                    ->where(['b.rep_part_no' => array('like', '%' . $value['part_no'] . '%')])
                    ->where('a.fit_beg_time', '<>', '')
                    ->where('a.is_enable', '=', 1)
                    ->field('a.part_no,b.sale_price,a.fit_beg_time,a.fit_end_time,b.variety_code_mid_code')
                    ->union($fit_beg_time, false)
                    ->group('a.part_no,a.fit_beg_time,a.fit_end_time')
                    ->select();//查询原件是否是被人的替换件
                if (!empty($rep_part_no)) {
                    foreach ($rep_part_no as $k => $val) {
                        $n18  = $model->where([
                            'part_no'      => $val['part_no'], 'fit_beg_time' => $val['fit_beg_time'],
                            'fit_end_time' => $val['fit_end_time'], 'is_enable' => 1,
                        ])->column('car_config_code');
                        $work = $this->part_work_hour($val['part_no'], $val['fit_beg_time'], $val['fit_end_time']);
                        if (in_array($val['variety_code_mid_code'], [3, 4, 5, 6, 7])) {
                            $val['fit_beg_time'] = $val['fit_end_time'] = '';// 只过滤备件上下架时间
                        }
                        $part_time = $val['fit_beg_time'] . '_' . $val['fit_end_time'];
                        if (isset($data[$part_time])) {
                            $array_18n                                = array_unique(array_merge(explode(',', $data[$part_time]['n_18']), $n18));
                            $data[$part_time]['n_18']                 = implode(',', $array_18n);
                            $data[$part_time]['relate_car_work_hour'] = $data[$part_time]['relate_car_work_hour'] + $work['part_no_array'];
                        } else {
                            $data[$part_time] = [
                                'n_18'                 => implode(',', $n18),
                                'fit_beg_time'         => $val['fit_beg_time'],
                                'fit_end_time'         => $val['fit_end_time'],
                                'sku_code'             => $value['part_no'],
                                'price'                => $value['sale_price'],
                                'cost_price'           => $value['dlr_price'],
                                'relate_car_work_hour' => $work['part_no_array'],
                                'e3s_bj_type_name'     => implode(',', $work['bj_type_name']),
                                'rep_part_no'          => '',
                                'relate_dlr_code'      => implode(',', $part_del_code)
                            ];
                        }
                    }
                    $data = array_values($data);
                    foreach ($data as $key => $item) {
                        $relate_cars                        = $CarSeries->whereIn('car_config_code', $item['n_18'])
                            ->where('is_enable', '=', 1)->column('id');
                        $data[$key]['car_ids']              = implode(',', $relate_cars);
                        $data[$key]['relate_car_work_hour'] = json_encode($item['relate_car_work_hour']);
                    }
                }


            }
            return $data;
        } elseif ($type == 2) {
            //pz1a套餐
            $where['a.sp_basic_code']     = array('in', $sku_codes);
            $city_type                    = Db::name('e3s_pz1a_maintenance_package')->alias('a')
                ->where($where)
                ->field('level_id')
                ->find();
            $city_type                    = $city_type['level_id'];
            $relate_n18                   = Db::name('e3s_pz1a_maintenance_package')->alias('a')
                ->join('t_e3s_pz1a_maintenance_car_series b', 'a.sp_basic_id = b.sp_basic_id')
                ->join('t_e3s_car_series c', 'b.service_car_type = c.service_car_type')
                ->where($where)
                ->group('c.car_config_code')
                ->column('c.car_config_code');
            $res_where['car_config_code'] = array('in', array_values(array_unique($relate_n18)));
            $res_where['is_enable']       = 1;
            $relate_cars                  = \app\common\model\e3s\E3sCarSeries::where($res_where)
                ->column('id');
        } elseif ($type == 1) {
            //老友惠和心悦获取18为码
            $where['a.maintain_group_code'] = array('in', $sku_codes);
            $where['a.is_enable']           = 1;
            $where['b.is_enable']           = 1;
            $list                           = Db::name('e3s_maintenance_package')->alias('a')
                ->join('t_e3s_maintenance_product_car_series b', 'a.product_type_id = b.product_type_id')
                ->where($where)
                ->group('a.product_type_id')
                ->column('a.product_type_id');
            if (empty($list)) {
                $relate_n18  = [];
                $relate_cars = [];
            } else {
                $relate_n18                   = Db::name('e3s_maintenance_product_car_series')->alias('a')
                    ->join('t_e3s_car_series b', 'a.service_car_type = b.service_car_type')
                    ->where(['a.product_type_id' => array('in', $list)])
                    ->group('b.car_config_code')
                    ->column('b.car_config_code');
                $res_where['car_config_code'] = array('in', array_values(array_unique($relate_n18)));
                $res_where['is_enable']       = 1;
                $relate_cars                  = \app\common\model\e3s\E3sCarSeries::where($res_where)
                    ->column('id');
            }
        } else {
            $where['a.maintain_group_code'] = array('in', $sku_codes);
            $where['b.service_car_type']    = 'COMMOM_CARTYPE';
            $where['a.is_enable']           = 1;
            $where['b.is_enable']           = 1;
            $list                           = Db::name('e3s_maintenance_package')->alias('a')
                ->join('t_e3s_maintenance_product_car_series b', 'a.product_type_id = b.product_type_id')
                ->where($where)
                ->group('a.maintain_group_code')
                ->select();
            if (empty($list)) {
                $relate_n18 = Db::name('e3s_maintenance_package')->alias('a')
                    ->join('t_e3s_maintenance_product_car_series b', 'a.product_type_id = b.product_type_id')
                    ->join('t_e3s_car_series c', 'b.service_car_type = c.service_car_type')
                    ->where([
                        'a.maintain_group_code' => array('in', $sku_codes),
                        'a.is_enable'           => 1,
                        'c.is_enable'           => 1,
                    ])
                    ->column('c.car_config_code');
            } else {
                $relate_n18 = \app\common\model\e3s\E3sCarSeries::where('car_brand_code', $list[0]['dlr_brand_code'])
                    ->where('is_enable', '=', 1)
                    ->column('car_config_code');
            }
            $res_where['car_config_code'] = array('in', array_values(array_unique($relate_n18)));
            $res_where['is_enable']       = 1;
            $relate_cars                  = \app\common\model\e3s\E3sCarSeries::where($res_where)
                ->column('id');
        }
        return ['error' => $error, 'n_18' => array_values(array_unique($relate_n18)), 'car_ids' => array_unique($relate_cars), 'city_type' => $city_type, 'part_time' => $part_time];
    }

    //备件新增时间查询车型工时和备件类型名称
    private function part_work_hour($part_no, $start_time, $end_time)
    {
        $list          = Db::name('e3s_part_car_series')->alias('a')
            ->join('t_e3s_spare_part b', 'a.part_no = b.part_no', 'left')
            ->where(['a.part_no' => $part_no, 'a.fit_beg_time' => $start_time, 'a.fit_end_time' => $end_time, 'a.is_enable' => 1])//备件时间
            ->field('a.id,a.car_config_code,b.variety_code_big_name,b.variety_code_small_name')
            ->select();
        $part_no_array = [];
        $array         = [];
        foreach ($list as $value) {
            $part_no_array[$value['id']][] = $value['car_config_code'];
            if ($value['variety_code_big_name'] != '养护品') {
                $array[] = $value['variety_code_small_name'];
            }
        }
        return ['part_no_array' => $part_no_array, 'bj_type_name' => array_unique($array)];
    }

    //删除备件车型
    public function del_parno_car()
    {
        $is_enable       = input('is_enable');
        $car_config_code = input('car_config_code');
        $part_no         = input('part_no');

        $where['car_config_code'] = $car_config_code;
        $where['part_no']         = ['in', $part_no];

        if (!empty($car_config_code) && $part_no) {
            echo Db::name('e3s_part_car_series')->where($where)->update([
                'is_enable' => $is_enable, 'modifier' => 'sg_' . date('md')
            ]);
        }
    }//删除备件车型

    public function del_parno()
    {
        $is_enable = input('is_enable', 0);
        $id        = input('id');
        $type      = input('type', 1);
        if (!$id) {
            die('id!!!');
        }
        $model = new E3sSparePart();
        $res   = $model->saveData(['is_enable' => $is_enable, 'modifier' => 'dl_p' . date('md')], ['id' => $id]);
        if ($type == 2) {
            $res = $model->where(['id' => $id])->delete();
        }
        print_json($res, $model->getLastSql());

    }

    public function test_use_card()
    {
        // 初始化卡券数组
        $cards = [
            ['card_id' => '1', 'card_value' => '10', 'can_use' => '', 'can_no_use' => 'all'],
            ['card_id' => '2', 'card_value' => '20', 'can_use' => '3,4', 'can_no_use' => ''],
            ['card_id' => '3', 'card_value' => '30', 'can_use' => '2,4', 'can_no_use' => ''],
            ['card_id' => '4', 'card_value' => '40', 'can_use' => '2,3,9', 'can_no_use' => ''],
            ['card_id' => '5', 'card_value' => '50', 'can_use' => '', 'can_no_use' => '6,8'],
            ['card_id' => '6', 'card_value' => '60', 'can_use' => '', 'can_no_use' => '5,7'],
            ['card_id' => '7', 'card_value' => '70', 'can_use' => '', 'can_no_use' => '6'],
            ['card_id' => '8', 'card_value' => '80', 'can_use' => '', 'can_no_use' => '5'],
            ['card_id' => '9', 'card_value' => '90', 'can_use' => '4', 'can_no_use' => ''],
            ['card_id' => '10', 'card_value' => '100', 'can_use' => 'all', 'can_no_use' => '']
        ];

// 预处理卡券数组
        foreach ($cards as &$card) {
            $card['can_use']    = $card['can_use'] ? explode(',', $card['can_use']) : [];
            $card['can_no_use'] = $card['can_no_use'] === 'all' ? 'all' : ($card['can_no_use'] ? explode(',', $card['can_no_use']) : []);
        }

// 递归函数来生成所有可能的组合
        function generateCombinationsRecursively($cards, $start = 0, $currentCombo = [], $currentActs = [], &$result = []) {
//        print_json($cards);
            for ($i = $start; $i < count($cards); $i++) {
                $newCombo = array_merge($currentCombo, [$cards[$i]]);
                $newActs = array_merge($currentActs, [$cards[$i]['act_id']]);

                // 检查组合是否有效

                if (isValidCombination($newCombo, $cards)) {
                    $combinations[] = $newCombo;
                    $combinations   = array_merge($combinations, generateCombinations($cards, $i + 1, $newCombo));
                }
            }
        }

// 检查组合是否有效
        function isValidCombination($combo, $cards)
        {
            $comboIds     = array_map('intval', $combo);
            $allCanUseIds = array_map(function ($card) {
                return $card['card_id'];
            }, $cards);

            foreach ($comboIds as $id) {
                $card = $cards[$id - 1];

                // 检查不可共用的卡券
                if ($card['can_no_use'] === 'all') {
                    // 如果当前卡券不可与所有其他卡券共用
                    if (count($comboIds) > 1) {
                        return false;
                    }
                } else {
                    foreach ($card['can_no_use'] as $noUseId) {
                        if (in_array($noUseId, $comboIds)) {
                            return false;
                        }
                    }
                }

                // 检查必须共用的卡券
                if ($card['can_use'] === ['all']) {
                    continue;
                } else {
                    foreach ($card['can_use'] as $useId) {
                        if (!in_array($useId, $comboIds)) {
                            return false;
                        }
                    }
                }
            }
            return true;
        }

// 生成所有有效组合
        $validCombinations = generateCombinations($cards);
        print_json($validCombinations);
        echo "Total combinations: " . count($validCombinations) . "\n";
        foreach ($validCombinations as $combo) {
            echo implode(", ", $combo) . "\n";
        }
    }

    //
    public function test_act_card()
    {


        $cards = [
            ['card_id' => '1', 'card_value' => '10', 'can_use' => '', 'can_no_use' => 'all', 'act_id' => '91', 'can_no_with' => '92,94'],
            ['card_id' => '2', 'card_value' => '20', 'can_use' => '3,4', 'can_no_use' => '', 'act_id' => '91', 'can_no_with' => '92,94'],
            ['card_id' => '3', 'card_value' => '30', 'can_use' => '2,4', 'can_no_use' => '', 'act_id' => '91', 'can_no_with' => '92,94'],
            ['card_id' => '4', 'card_value' => '40', 'can_use' => '2,3,9', 'can_no_use' => '', 'act_id' => '92', 'can_no_with' => '91'],
            ['card_id' => '5', 'card_value' => '50', 'can_use' => '', 'can_no_use' => '6,8', 'act_id' => '93', 'can_no_with' => ''],
            ['card_id' => '6', 'card_value' => '60', 'can_use' => '', 'can_no_use' => '5,7', 'act_id' => '93', 'can_no_with' => ''],
            ['card_id' => '7', 'card_value' => '70', 'can_use' => '', 'can_no_use' => '6', 'act_id' => '94', 'can_no_with' => '91'],
            ['card_id' => '8', 'card_value' => '80', 'can_use' => '', 'can_no_use' => '5', 'act_id' => '95', 'can_no_with' => 'all'],
            ['card_id' => '9', 'card_value' => '90', 'can_use' => '4', 'can_no_use' => '', 'act_id' => '95', 'can_no_with' => 'all']
        ];

// 预处理卡券数组
        foreach ($cards as &$card) {
            $card['can_use']     = $card['can_use'] ? explode(',', $card['can_use']) : [];
            $card['can_no_use']  = $card['can_no_use'] === 'all' ? 'all' : ($card['can_no_use'] ? explode(',', $card['can_no_use']) : []);
            $card['can_no_with'] = $card['can_no_with'] === 'all' ? 'all' : ($card['can_no_with'] ? explode(',', $card['can_no_with']) : []);
        }



// 递归函数来生成所有可能的组合
        function generateCombinationsRecursively($cards, $start = 0, $currentCombo = [], $currentActs = [], &$result = []) {
//        print_json($cards);
            for ($i = $start; $i < count($cards); $i++) {
                $newCombo = array_merge($currentCombo, [$cards[$i]]);
                $newActs  = array_merge($currentActs, [$cards[$i]['act_id']]);

                // 检查组合是否有效
                if (isValidCombination($newCombo, $newActs)) {

                    $combinations[] = $newCombo;
                    $combinations   = array_merge($combinations, generateCombinationsRecursively($cards, $i + 1, $newCombo, $newActs));
                }
            }
        }

// 检查组合是否有效
        function isValidCombination($combo, $currentActs)
        {
            $comboIds = array_map(function ($card) {
                return $card['card_id'];
            }, $combo);

            foreach ($combo as $card) {
                // 检查不可共用的卡券
                if ($card['can_no_use'] === 'all') {
                    if (count($combo) > 1) {
                        return false;
                    }
                } else {
                    foreach ($card['can_no_use'] as $noUseId) {
                        if (in_array($noUseId, $comboIds)) {
                            return false;
                        }
                    }
                }

                // 检查必须共用的卡券
                if (!empty($card['can_use']) && $card['can_use']!=='all') {
                    foreach ($comboIds as $id) {
                        if ($id !== $card['card_id'] && !in_array($id, $card['can_use'])) {
                            return false;
                        }
                    }
                }

                // 检查act层次的约束
                if ($card['can_no_with'] === 'all') {
                    if (count($currentActs) > 1) {
                        return false;
                    }
                } else {
                    foreach ($card['can_no_with'] as $noWithActId) {
                        if (in_array($noWithActId, $currentActs)) {
                            return false;
                        }
                    }
                }
            }

            return true;
        }

// 生成所有有效组合
        $validCombinations = generateCombinationsRecursively($cards);
// 计算每个组合的总价值并排序
        $sortedCombinations = [];
        foreach ($validCombinations as $combo) {
            $totalValue           = array_sum(array_map(function ($card) {
                return $card['card_value'];
            }, $combo));
            $sortedCombinations[] = ['combo' => $combo, 'total_value' => $totalValue];
        }

        usort($sortedCombinations, function ($a, $b) {
            return $b['total_value'] - $a['total_value'];
        });

// 输出结果
        echo "Total combinations: " . count($sortedCombinations) . "\n";
        foreach ($sortedCombinations as $comboData) {
            $comboIds = array_map(function ($card) {
                return $card['card_id'];
            }, $comboData['combo']);
            echo "Combination: " . implode(", ", $comboIds) . " | Total Value: " . $comboData['total_value'] . "\n";
        }
    }

    public function test_cc_card()
    {
        $json           = '[{"business_order_name":"","business_order_no":"GWSM240717170122W3B","coupon_code":"3001041417","coupon_discounted_price":0,"coupon_receive_id":558883352,"creator":"","order_source":"","used_store":"","used_store_id":"0"}]';
        $data_1         = json_decode($json, true);
        $coupon_receive = QuickWin::create('quick_win')->getCouponReceiveRecord(['coupon_code' => $data_1[0]['coupon_code'], 'request_channel' => 1]);
        print_r($coupon_receive);
        print_r(['coupon_code' => $data_1[0]['coupon_code'], 'request_channel' => 1]);
        $data['data']                         = $data_1;
        $data['data'][0]['coupon_receive_id'] = $coupon_receive['rows'][0]['id'];
        $ret                                  = QuickWin::create('quick_win')->postCouponConsume($data['data']);
        print_json($data['data'], $ret);
    }

    public function test_hock()
    {
        $param = [
            'commodity_id'     => 5905,
            'commodity_set_id' => 10684,
            'shelves_type'     => 5,
            'modifier'         => 't_lz'
        ];
        $res   = Hook::listen('flat', $param);
        dd($res);
    }

    public function test_card_rule_pp()
    {
        $card_rule = [
            ['goods_id' => 5993, 'card_id' => 16813997978321920, 'sub_goods_id' => 5239, 'sku_id' => [149586, 149587, 149598, 149600, 149604, 149608, 149609], 'group_card_type' => 2],
            ['goods_id' => 5993, 'card_id' => 16813997978321920, 'sub_goods_id' => 5197, 'sku_id' => [], 'group_card_type' => 2],
            ['goods_id' => 5993, 'card_id' => 16813522086298624, 'sub_goods_id' => 5197, 'sku_id' => [149503, 149504, 149505], 'group_card_type' => 1],
            ['goods_id' => 5993, 'card_id' => 16813997978321920, 'sub_goods_id' => 5240, 'sku_id' => [36], 'group_card_type' => 2]
        ];

        $goods_info = [
            ['goods_id' => 5993, 'sub_goods_id' => 5239, 'sku_id' => [149610, 149609], 'oil_type' => 0],
            ['goods_id' => 5993, 'sub_goods_id' => 5197, 'sku_id' => [149501, 149503], 'oil_type' => 0],
            ['goods_id' => 5993, 'sub_goods_id' => 5198, 'sku_id' => [33], 'oil_type' => 0],
            ['goods_id' => 5993, 'sub_goods_id' => '', 'sku_id' => [34], 'oil_type' => 0],
            ['goods_id' => 5993, 'sub_goods_id' => 5240, 'sku_id' => [35], 'oil_type' => 1]
        ];

    }

    public function test_card_zh()
    {
        // 初始化卡券数组
//        $cards = [
//            ['card_id' => '1', 'card_value' => '10', 'can_use' => '', 'can_no_use' => 'all', 'act_id' => '91', 'can_no_with' => '92,94'],
//            ['card_id' => '2', 'card_value' => '20', 'can_use' => '3,4', 'can_no_use' => '', 'act_id' => '91', 'can_no_with' => '92,94'],
//            ['card_id' => '3', 'card_value' => '30', 'can_use' => '2,4', 'can_no_use' => '', 'act_id' => '91', 'can_no_with' => '92,94'],
//            ['card_id' => '4', 'card_value' => '40', 'can_use' => '2,3,9', 'can_no_use' => '', 'act_id' => '92', 'can_no_with' => '91'],
//            ['card_id' => '5', 'card_value' => '50', 'can_use' => '', 'can_no_use' => '6,8', 'act_id' => '93', 'can_no_with' => ''],
//            ['card_id' => '6', 'card_value' => '60', 'can_use' => '', 'can_no_use' => '5,7', 'act_id' => '93', 'can_no_with' => ''],
//            ['card_id' => '7', 'card_value' => '70', 'can_use' => '', 'can_no_use' => '6', 'act_id' => '94', 'can_no_with' => '91'],
//            ['card_id' => '8', 'card_value' => '80', 'can_use' => '', 'can_no_use' => '5', 'act_id' => '95', 'can_no_with' => 'all'],
//            ['card_id' => '9', 'card_value' => '90', 'can_use' => '4', 'can_no_use' => '', 'act_id' => '95', 'can_no_with' => 'all']
//        ];
        $cards_str = '[{"id":21147429307974656,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1826079466507571201","act_card_id":304749},{"id":21243169433748480,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1826462328184475649","act_card_id":304849},{"id":21330231799350272,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"80","act_id":"1826810558810263554","act_card_id":304866},{"id":21353051793163264,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1826901852914794498","act_card_id":304878},{"id":21596899596338176,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1827877235525160961","act_card_id":304879},{"id":21597729903903744,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1827880532466208769","act_card_id":304880},{"id":21602527087723520,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"2.00","act_id":"1827899748149706754","act_card_id":304887},{"id":21605549178127360,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1827911862470053889","act_card_id":304889},{"id":21605905581769728,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"10","act_id":"1827913258498002945","act_card_id":304890},{"id":21606153099183104,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":166.8,"act_id":"1827914215344390145","act_card_id":304891},{"id":21615573818442752,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1827953739785125890","act_card_id":304892},{"id":21630269787243520,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"99999.00","act_id":"1828010744020148226","act_card_id":304915},{"id":21686071637083136,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1828234217605500929","act_card_id":304918},{"id":21705435471971328,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1828311435660566530","act_card_id":304932},{"id":21705993473786880,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1828313711083069442","act_card_id":304933},{"id":21796874504668160,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1828678500533972993","act_card_id":304945},{"id":21797695163237376,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1828680427325038594","act_card_id":304947},{"id":21799095930749952,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1828686109663240194","act_card_id":304949},{"id":21810085664687104,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1828730113802190850","act_card_id":304959},{"id":21868608502137856,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1828964094208286721","act_card_id":304961},{"id":21978530207269888,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1829403851401146369","act_card_id":305002},{"id":21979756050285568,"card_id":null,"can_use":[],"can_no_use":[],"can_no_with":[],"value":"1.00","act_id":"1829408759269502978","act_card_id":305005}]';
//        $cards = json_decode($cards_str,1);
        $cards = [
            ['card_id' => '1', 'card_value' => '10', 'can_use' => '', 'can_no_use' => 'all', 'act_id' => '91', 'can_no_with' => '92,94'],
            ['card_id' => '2', 'card_value' => '20', 'can_use' => '3,4', 'can_no_use' => '', 'act_id' => '91', 'can_no_with' => '92,94'],
            ['card_id' => '3', 'card_value' => '30', 'can_use' => '2,4', 'can_no_use' => '', 'act_id' => '91', 'can_no_with' => '92,94'],
            ['card_id' => '4', 'card_value' => '40', 'can_use' => '2,3,9', 'can_no_use' => '', 'act_id' => '92', 'can_no_with' => '91'],
            ['card_id' => '5', 'card_value' => '50', 'can_use' => '', 'can_no_use' => '6,8', 'act_id' => '93', 'can_no_with' => ''],
            ['card_id' => '6', 'card_value' => '60', 'can_use' => '', 'can_no_use' => '5,7', 'act_id' => '93', 'can_no_with' => ''],
            ['card_id' => '7', 'card_value' => '70', 'can_use' => '', 'can_no_use' => '6', 'act_id' => '94', 'can_no_with' => '91'],
            ['card_id' => '8', 'card_value' => '80', 'can_use' => '', 'can_no_use' => '5', 'act_id' => '95', 'can_no_with' => 'all'],
            ['card_id' => '9', 'card_value' => '90', 'can_use' => '4', 'can_no_use' => '', 'act_id' => '95', 'can_no_with' => 'all']
        ];
    }

    public function test_car_e3s()
    {
        $user_id    = input('user_id');
        $user_model = new DbUser();
        $user       = $user_model->getOneByPk($user_id);

        // 营销平台获取vin车
        $user_cards = Member::create('member')->crmCards(['member_id' => $user['plat_id'], 'ignore_e3s' => 1]);
        $ni_e3s_car = QuickWin::create('quick_win')->getCarInfos(['appCode' => 'nissan', 'oneId' => $user['one_id'], 'appSkin' => 1]);

        $qc_member_car = Crm::create('crm')->queryMemberInfo(['unionid' => $user['unionid']]);
        $qc_e3s_car    = QuickWin::create('e3s_dlr')->postQcCarInfoList(['oneid' => $user['one_id'], 'appCode' => 'venucia', 'clientid' => 'venuciaapp']);// 日产：nissan  启辰：venucia
        $data          = [
            'nissan_car'  => [
                'member_car' => $user_cards,
                'e3s_car'    => $ni_e3s_car
            ],
            'venucia_car' => [
                'member_car' => $qc_member_car,
                'e3s_car'    => $qc_e3s_car
            ],
        ];
        print_json(0, 'ok', $data);
    }


    public function updWaybillNumber()
    {
        $orderCommodityModel = new BuOrderCommodity();
        $map                 = [
            'a.supplier' => ['in', BuOrderCommodity::$jd_warehouse_supplier]
        ];
        $field               = 'a.id,a.order_code,a.supplier,a.common_carrier,a.waybill_number,jd_warehouse_send_id,b.third_common_carrier,third_waybill_number,a.created_date';
        $list                = $orderCommodityModel->alias('a')
            ->join('t_db_jd_warehouse b', "a.jd_warehouse_send_id = b.id and b.third_waybill_number != ''")
            ->where($map)
            ->field($field)
            ->select();
        $re                  = [];
        foreach ($list as $key => $item) {
            $map  = ['id' => $item['id']];
            $upd  = [
                'waybill_number'    => $item['third_waybill_number'],
                'last_updated_date' => date('Y-m-d H:i:s'),
            ];
            $re[] = $orderCommodityModel->where($map)->update($upd);
        }

        print_json(0, 'ok', $re);
    }



//    public function test_trans(){
//        // 只查询收款方是dndc账户的
//        $this->order_model =  new BuOrder();
//        $map       = ['order_status' => ['not in', [1, 3, 8]], 'dd_dlr_code' => '', 'parent_order_type' => array('<>', 3)];
//        $orderList = $this->order_model->where(function ($query) use ($start_time, $end_time) {
//            $query->where([
//                'pay_time' => ['between', [$start_time, $end_time]],
//            ])->whereOr([
//                'front_pay_time' => ['between', [$start_time, $end_time]]
//            ]);
//        })->where($map)->select();
//
//        $pay_money_static = 0;
//        $fee_amt_static   = 0;
//        $integral_static  = 0;
//        // 先把交易日期的数据is_enable=0;
//
//        $isSuccess = [];
//        foreach ($orderList as $key => $orderInfo) {
//            $orderCommodities = $orderInfo->orderCommodities;
//
//            $order_total_money = 0;  // 订单总价
//            $order_all_dis     = 0; // 订单活动总优惠金额
//            foreach ($orderCommodities as $value) {
//                // 订单总价 = 订单商品原价 * 数量
//                if ($value['mo_id'] > 0) continue;
//                $order_total_money += $value['b_act_price'] * $value['count'];
//                $order_all_dis     += $value['all_dis'];
//            }
//
//            $money            = $orderInfo['money'];
//            $integral         = $orderInfo['integral'];
//            $mail_price       = $orderInfo['mail_price']; // 订单运费
//            $card_money       = $orderInfo['card_money']; // 卡券总优惠金额
//            $order_money      = $order_total_money + $orderInfo['mail_price'] - $orderInfo['card_money'] - $order_all_dis;
//            $point_order_code = $this->order_point_model->where(['order_code' => $orderInfo['order_code']])->value('point_order_code');
//
//            // 判断是否有现金支付
//            $return         = $this->isMoneyPay($money, $orderInfo['order_code'], $username, $transactionDate, $fee_amt_static, $isSuccess);
//            $fee_amt_static = $return['fee_amt_static'];
//            $isSuccess      = $return['is_success'];
//
//
//            $tram_integral = 0;
//            // pz1a
//            if ($orderInfo['brand'] == 3) {
//                $map       = ['order_code' => $orderInfo['order_code'], 'is_enable' => 1];
//                $pointList = BuOrderMoreCardPoint::where($map)->select();
//                $oil       = [];
//                $tram      = [];
//                foreach ($pointList as $item) {
//                    // 电车
//                    if ($item['point_type'] == 1) {
//                        $tram[] = $item['point'];
//                    } else {
//                        $oil[] = $item['point'];
//                    }
//                }
//                $integral      = array_sum($oil); // 油车积分
//                $tram_integral = array_sum($tram); // 电车积分
//
//            }
//
//            $public = [
//                'transaction_time'     => $orderInfo['pay_time'],
//                'transaction_type'     => 1,
//                'order_code'           => $orderInfo['order_code'],
//                'brand'                => $orderInfo['brand'],
//                'order_money'          => $order_money,
//                'order_total_money'    => $order_total_money,
//                'money'                => $money,
//                'integral'             => $integral,
//                'tram_integral'        => $tram_integral, // 电车积分
//                'mail_price'           => $mail_price,
//                'card_money'           => $card_money,
//                'order_all_dis'        => $order_all_dis,
//                'payment_method'       => $orderInfo['payment_method'],
//                'order_type_name'      => $return['order_type_name'],
//                'ms_order_code'        => $return['ms_order_code'],
//                'fee_amt'              => $return['fee_amt'],
//                'k_order_code'         => $point_order_code ?? '',
//                't_order_code'         => '',
//                'after_order_code'     => '', // 售后订单
//                'return_money'         => '',
//                'return_integral'      => '',
//                'return_integral_time' => '',
//                'bank_name'            => $return['bank_name'],
//                'dlr_code'             => $orderInfo['dlr_code'],
//                'is_cc'                => $orderInfo['is_cc_ok'],
//                'logistics_mode'       => $orderInfo['logistics_mode'],
//                'order_source'         => $orderInfo['order_source'],
//            ];
//
//            $goods_num = count($orderCommodities);
//
//            // 计算1份手续费
//            $one_fee_amt = 0;
//            if ($return['fee_amt'] > 0) {
//                $one_fee_amt = $return['fee_amt'] / $orderInfo['money'];
//            }
//
//            $commodity_fee_amt_total = 0;
//            foreach ($orderCommodities as $ke => $value) {
//                $map           = ['id' => $value['commodity_id']];
//                $field_c       = 'id,commodity_name,commodity_code';
//                $commodityInfo = $this->commodity_model->where($map)->field($field_c)->find();
//                $set_sku_field = 'd.commodity_sku_id,e.sku_code,f.business_project';
//                $setSkuInfo    = $this->commodity_set_sku_model->alias('d')
//                    ->join('t_db_commodity_sku e', 'd.commodity_sku_id = e.id')
//                    ->join('t_db_commodity_set f', 'f.id = d.commodity_set_id')
//                    ->where(['d.id' => $value['sku_id']])
//                    ->field($set_sku_field)
//                    ->find();
//                // 单个商品总价
//                $one_total_price   = $value['count'] * $value['actual_use_money'];
//                $commodity_fee_amt = round(bcmul($one_fee_amt, $one_total_price, 3), 2);
//                // 最后一个商品
//                if ($ke == $goods_num - 1) {
//                    $commodity_fee_amt = round(bcsub($return['fee_amt'], $commodity_fee_amt_total, 3), 2);
//                } else {
//                    $commodity_fee_amt_total = round(bcadd($commodity_fee_amt_total, $commodity_fee_amt, 3), 2);
//                }
//
//
//            }
//
//            $integral_static  = bcadd($integral_static, $integral + $tram_integral, 2);
//            $pay_money_static = bcadd($pay_money_static, $money, 2);
//
//        }
//
//
//
//
//    }

    /**
     * @return View
     */
    public function test_staff_user()
    {
        $emp_no = input('emp_no');
        $phone  = input('phone');
        $data   = ['emp_no' => $emp_no, 'phone' => $phone];
//        $com  = new Common();
//        $res  = $com->check_staff($data);


        $EMPLOYEE_NUMBER = $data['emp_no'];
        $MOBILE_NUM      = $data['phone'];
        $p_data          = ['EMPLOYEE_NUMBER' => $EMPLOYEE_NUMBER, 'MOBILE_NUM' => $MOBILE_NUM];
        $url             = 'http://172.25.24.46:8090/WebServiceReconmandBuyCar.asmx/SingInCheck';
//        $url =  'http://172.26.137.108:8089/WebServiceReconmandBuyCar.asmx/SingInCheck';
//        $url = APP_STATUS == 'prod' ? 'http://172.25.24.46:8090/WebServiceReconmandBuyCar.asmx/SingInCheck' : 'http://172.26.137.108:8089/WebServiceReconmandBuyCar.asmx/SingInCheck';
        $result = http_get($url, $p_data, false);
        if (empty($result)) return [];
        $str = (string)simplexml_load_string($result);
        $arr = json_decode($str, true);

        print_json($arr, $result);
    }


    public function re_admin_pwd()
    {
        $user      = input('user_name');
        $sys_model = new SysAdmin();
        $res       = $sys_model->saveData(['password' => md5($user . '123456'), 'modifier' => 'qd_lzx'], ['username' => $user]);
        print_json($res);
    }


    /**
     * 删除用户车系
     */
    public function delUserCar()
    {
        $id      = input('id');
        $user_id = input('user_id');
        $vin     = input('vin');
        $map     = [
            'id'      => $id,
            'user_id' => $user_id,
            'vin'     => $vin
        ];
        $re      = DbUserCarSeries::where($map)->delete();
        print_json($re);
    }

//    public function test_change_ad_pwd(){
//        $user =  input('user_name');
//        $pwd =  input('pwd');
//        $sys = new SysAdmin();
//        $res =  $sys->saveData(['password'=>md5($pwd)],['username'=>$user]);
//        print_json($res,$sys->getLastSql());
//    }
    //强制上架
    public function test_goods_bdp_all()
    {
        set_time_limit(0);
        $goods_model  = new DbCommodity();
        $goods_list   = $goods_model->where(['is_enable' => 1, 'arrival_bdp' => 1])->select();
        $goods_id_arr = array_column($goods_list, 'id');
        $flat         = new DbCommodityFlat();
        $goods_id     = input('goods_id', '');
        $where        = ['shelves_type' => 5, 'is_enable' => 1, 'commodity_id' => ['in', $goods_id_arr]];
        if ($goods_id) {
            $where['commodity_id'] = $goods_id;
        }

        $flat_item = $flat->getList(['where' => $where]);
        foreach ($flat_item as $v) {
            $this->test_bdp_goods($v['commodity_id']);
        }
    }

    public function test_bdp_goods($goods_id)
    {
        $item = ['commodity_id' => $goods_id, 'shelves_type' => 5];

        $flat      = new DbCommodityFlat();
        $flat_item = $flat->getOne(['where' => ['commodity_id' => $item['commodity_id'], 'shelves_type' => $item['shelves_type']]]);

        $set_sku_model = new DbCommoditySetSku();
        $sku_list      = $set_sku_model->getSkuInfo($flat_item['commodity_set_id']);


        $bdp_list = [];

        $time = date("Y-m-d H:i:s");

        foreach ($sku_list as $one) {
            $bdp_list[] = [
                'setSkuId'       => $one['id'],
                'skuId'          => $one['commodity_sku_id'],
                'skuCode'        => $one['sku_code'],
                'relate_car_18n' => $one['relate_car_18n'],
            ];
        }


        $data[] = [
            'commodityId'     => $item['commodity_id'],
            'commodityName'   => $flat_item['commodity_name'],
            'commoditySetId'  => $flat_item['commodity_set_id'],
            'ddCommodityType' => $flat_item['dd_commodity_type'],
            'upDownType'      => 1,
            'shelvesType'     => $item['shelves_type'],
            'uploadTime'      => $time,
            'state'           => 0,
            'skuList'         => $bdp_list,
        ];

        $final = [
            'table'      => '',
            'uploadTime' => $time,
            'data'       => $data
        ];


        $api_start_at = microtime(true);
        $res          = Bdp::create('bdp')->commodity($final);
        (new DbLog())->insertData(
            [
                'type'         => 'bdp_push',
                'is_success'   => 'bdp:' . $item['commodity_id'] . ":up",
                'send_note'    => json_encode($data),
                'receive_note' => json_encode(['res' => $res, 'time' => number_format(microtime(true) - $api_start_at, 10, '.', '')]),
            ]
        );
//        print_json($res,$final);
    }

    public function test_goods_dl()
    {
        $data_arr     = input('data_arr','{"sku":{"sp_value_list":"5904","price":"165","stock":"1","image":"","sku_code":"HXLVT40000-Y375","tax":"1","tax_code":"1","cost_price":"","card_id":0,"hours_id":"","city_type":"","delivery_coefficient":"1"},"sku_list":"HXLVT40000-Y375","commodity_id":"5361","type":9}');
        $pp_test = input('pp_test',1);
        $data_onr     = json_decode($data_arr, true);
        $sku          = $data_onr['sku'];
        $sku_codes    = $data_onr['sku_list'];
        $commodity_id = $data_onr['commodity_id'];
        $data         = $this->findCarTypeAdmin($sku_codes, 0);
        if($pp_test){
            print_json($data);
        }
        $new_sku_list = [];
        $model_sku    = new DbCommoditySku();
        foreach ($data as $i => $item) {
            $new_sku_list[$i] = $sku;
            $find             = $model_sku->where([
                'commodity_id'    => $commodity_id,
                'sp_value_list'   => $sku['sp_value_list'],
                'sku_code'        => $item['sku_code'],
                'part_start_time' => $item['fit_beg_time'],
                'part_end_time'   => $item['fit_end_time']
            ])->find();
            if (!empty($find)) {
                $new_sku_list[$i]['id'] = $find['id'];
            }
            $new_sku_list[$i]['commodity_id']    = $commodity_id;
            $new_sku_list[$i]['price']           = $item['price'];
            $new_sku_list[$i]['cost_price']      = $item['cost_price'];
            $new_sku_list[$i]['sku_code']        = $item['sku_code'];
            $new_sku_list[$i]['relate_car_18n']  = $item['n_18'];
            $new_sku_list[$i]['part_start_time'] = $item['fit_beg_time'];
            $new_sku_list[$i]['part_end_time']   = $item['fit_end_time'];
            $new_sku_list[$i]['rep_part_no']     = $item['rep_part_no'];
            $new_sku_list[$i]['relate_dlr_code'] = $item['relate_dlr_code'];
            if (empty($item['car_ids'])) {
                $new_sku_list[$i]['relate_car_ids'] = 999999999;
            } elseif ($item['car_ids'] == '-1') {
                $new_sku_list[$i]['relate_car_ids'] = '';
            } else {
                $new_sku_list[$i]['relate_car_ids'] = $item['car_ids'];
            }
            $new_sku_list[$i]['relate_car_work_hour'] = $item['relate_car_work_hour'];
            $new_sku_list[$i]['e3s_bj_type_name']     = $item['e3s_bj_type_name'];
            $new_sku_list[$i]['is_enable']            = 1;
        }
        var_dump($new_sku_list);
        print_json($data);
        return $model_sku->SaveAll($new_sku_list);


    }


    public function test_change_ad_pwd()
    {
        $user = input('user_name');
        $pwd = input('pwd');
        $sys = new SysAdmin();
        $res = $sys->saveData(['password' => md5($pwd)], ['username' => $user]);
        print_json($res, $sys->getLastSql());
    }
    public function test_carer()
    {
        $u_id  = input('unionid', '');
        $mid   = input('mid');
        $cc    = new Common();
        $carer = $cc->_getCarer($u_id, $mid);
        var_dump($carer);

    }

    //手工上架bdp

    public function up_bdp()
    {
        $goods_ids  = input('goods_ids', 5993);
        $flat_model = new DbCommodityFlat();
        $flat       = $flat_model->getList(['where' => ['commodity_id' => ['in', $goods_ids]]]);
        foreach ($flat as $item) {
            $as_data['type'] = 'up';
            $as_data['item'] = [
                'commodity_id' => $item['commodity_id'],
                'shelves_type' => $item['shelves_type']
            ];
            Queue::push('app\common\queue\CommodityBdp', json_encode($as_data), config('queue_type.e3s_dlr'));
        }
    }


    /**
     * 更新企业员工信息
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function updQyUser()
    {
        $post       = input('post.');
        $user_model = new BuQyUserAll();
        $data       = [
            'userid'     => $post['userid'],
            'name'       => $post['name'],
            'department' => $post['department'],
            'position'   => $post['position'],
            'mobile'     => $post['mobile'],
            'gender'     => $post['gender'],
            'email'      => $post['email'],
            'avatar'     => $post['avatar'],
            'is_enable'  => $post['is_enable'],
        ];
        $map        = ['userid' => $post['userid']];
        $info       = $user_model->where($map)->find();
        if (empty($info)) {
            $re = $user_model->insertGetId($data);
        } else {
            $re = $user_model->where($map)->update($data);
        }
        print_json(0, 'success', $re);

    }


    // 修改京东商品
    public function jd_order_goods()
    {
        $type                 = input('type', 1);
        $order_id             = input('order_id');
        $new_order_id         = input('new_order_id');
        $jd_order_goods_model = new JdOrderGoods();
        if ($type == 1) {
            // 修改商品数量
            $re = $jd_order_goods_model->where('order_id', $order_id)->update(['goods_num' => 1]);
        } else {
            $info = $jd_order_goods_model->where('order_id', $order_id)->find()->toArray();
            unset($info['id']);
            $info['order_id'] = $new_order_id;
            $re               = $jd_order_goods_model->insertGetId($info);
        }
        print_json($re);
    }


    public function set_admin_group()
    {
        $admin_momdel = new SysAdmin();
        $username     = input('username');
        $id           = input('id');
        $gid          = input('gid');
        $where        = ['id' => $id];
        if ($username) {
            $where = ['username' => $username];
        }
        $res = $admin_momdel->saveData(['gid' => $gid], ['username' => $username]);
        print_json($res, $admin_momdel->getLastSql());
    }

    //修改用户信息.
    public function test_d_acc()
    {
        $model = new SysAdmin();
        $acc   = input('acc');
        $type  = input('type', 1);
        $id    = input('id');
        $gcid  = input('gcid');
        $list  = $model->getList(['where' => ['username' => $acc]]);
        if (!$list) {
            dd('meishuju');
        }
        if ($type == 1) {
            print_json($list);
        }
        if ($type == 3) {
            if (!$id) {
                dd('meiId');
            }
            $res = $model->where(['id' => $id])->delete();
            print_json($res, $model->getLastSql());
        }
        if ($type == 2) {
            if (!$id) {
                dd('meiId~');
            }
            $data = ['gid' => $gcid];
            $res  = $model->saveData($data, ['id' => $id]);
            print_json($res, $model->getLastSql());
        }

    }

    public function test_after_nc()
    {

        $is_refund   = input('is_refund', 1);
        $status      = input('order_status', "2,11");
        $ztk         = input('ztk', 0);
        $order_codes = input('order_codes', [
            'GWSM250507195006qC2','GWSM250507110748TF2','GWSM25060222225417V','GWSM2506022218584gO','GWSM250602220102Lej','GWSM250602210859Cul','GWSM250602184945SzF','GWSM250602173550lm2','GWSM250602165856Oer','GWSM250602163149mKq','GWSM250602152739JJc','GWSM250602140355O0U','GWSM250602130447fV9','GWSM250602121924rp6','GWSM250602120820ZNa','GWSM2506021151481MV','GWSM250602111802Ii6','GWSM250602102949YLB','GWAPP250602102339Nit','GWSM250602101544uZg','GWSM250602095520XyG','GWSM250602095414RvQ','GWSM250602095157g0E','GWSM2506020951154Mm','GWSM250602084631HoD','GWSM2506020051006V9','GWSM250602004812VYy','GWSM250601182837PGW','GWSM250601174634tsr','GWSM250601172757CwW','GWSM250601172652jfB','GWSM250601171657CDS','GWSM250601170251DfK','GWSM250601165913ucp','GWSM250601165737lnX','GWSM250601163137iDr','GWSM250601154212Nac','GWSM250601153717bev','GWSM250601143155pVR','GWSM250601133430YOv','GWSM2506011157470uE','GWSM250601115056tVS','GWSM250601103306roc','GWSM250601095314XtS','GWSM250531192054utW','GWSM250531185452g49','GWSM250531172435dCD','GWSM250531171459zz4','GWSM25053117115864z','GWSM250531164127pDX','GWSM250531161720QnO','GWSM250531161558UbG','GWSM250531152447G9E','GWSM250531143745dsp','GWSM250531141824DdJ','GWSM250531141430LNG','GWSM250531121944iUk','GWSM250531120815zcc','GWSM250531115700guQ','GWSM250531115134hoS','GWSM250531114341nrC','GWSM250531114209eRh','GWSM250531113906oyT','GWSM2505311059599Ib','GWSM2505311057163H1','GWSM250531105139oaJ','GWSM250531105050pnW','GWSM250531103941GSm','GWSM250531103431aI3','GWSM250531103253ewm','GWSM250531103011fXv','GWSM250531102142LeA','GWSM250531101136QYP','GWSM2505310956499lP','GWSM250531091503EdD','GWSM250530201827XbM','GWSM250530195100EQH','GWSM250530172309yvf','GWSM250530171032bPq','GWSM25053017015061D','GWSM250530163849BYI','GWSM250530162059NkB','GWSM250530162010D8H','GWSM250530161647WWB','GWSM250530161527KJw','GWSM2505301532240Fx','GWSM250530151733v8h','GWSM250530145717UUp','GWSM250530143146D8X','GWSM250530142526XqS','GWSM2505301215004Ql','GWSM250530120136J6C','GWSM2505301139104Ga','GWSM250530112915JRB','GWSM2505301111000vX','GWSM250530105541gRa','GWSM250530101734EyY','GWSM250530101717NTb','GWSM250530100837nzW','GWSM2505300944038Im','GWSM250530084431Ufv','GWSM250529202739qzj','GWSM2505291840048JE','GWSM250529171623uUB','GWSM250529164740yxg','GWSM2505291642104CL','GWSM2505291641275FQ','GWSM2505291607061cj','GWSM250529155935Ewk','GWSM250529153046j88','GWSM250529150615C52','GWSM250529145025K13','GWSM2505291446235Gb','GWSM250529131320bZT','GWSM2505291216166wc','GWSM250529120412BmZ','GWSM250529113919pv3','GWSM250529110227w3w','GWSM250529094445kt9','GWSM2505290938482ew','GWSM250529093344RCA','GWSM250529091020I4T','GWSM250529085657P3Q','GWSM250528220936rrz','GWSM250528185529kaw','GWSM25052817130905T','GWSM250528164022xNm','GWSM250528163411Kid','GWSM2505281626087pN','GWSM250528161130MsQ','GWSM250528160922lXq','GWSM250528160630TGu','GWSM250528153901TlR','GWSM250528152734EXt','GWSM2505281526591Rv','GWSM250528150813Tef','GWSM250528150105jK2','GWSM250528145554Efw','GWSM250528145259dsC','GWSM250528144548YWQ','GWSM250528143812EoD','GWSM250528143406GUj','GWSM250528142740wEz','GWSM250528142320bTL','GWSM250528133641wnS','GWSM250528132153d0T','GWSM250528123547eu1','GWSM250528122209mXQ','GWSM250528112654xEa','GWSM250528111359BQw','GWSM250528110714EoX','GWSM250528110208CRE','GWSM250528103916wLb','GWSM2505281021284HK','GWSM250528100913HL9','GWSM2505280941282g6','GWSM250528090441I36','GWSM250527181246jXn','GWSM2505271610525y5','GWSM250527160944eQI','GWSM2505271556174s3','GWSM250527155059aGT','GWSM250527154624p7w','GWSM250527142304lyP','GWSM250527142246Dwf','GWSM250527140606XZT','GWSM250527135720p9W','GWSM250527133342mDm','GWSM250527131653frN','GWSM250527110356Ppl','GWSM250527105649Z2j','GWSM250527105304b55','GWSM250527104706lhU','GWSM2505271042399TC','GWSM250527103745EbD','GWSM25052709560517s','GWSM250527094701Go1','GWSM250527094653rOJ','GWSM250527091801zcF','GWSM250527090420bh2','GWSM250527090004afp','GWSM2505270857149LS','GWSM2505270854554so','GWSM250526212807GO3','GWSM250526202228P3j','GWSM250526201447zpV','GWSM250526201203fyx','GWSM250526200953jeY','GWSM25052620030585k','GWSM250526193624k8t','GWSM250526192109jyN','GWSM250526191116n9M','GWSM250526174216NLF','GWSM2505261731103ya','GWSM250526172657hLq','GWSM250526163223RNF','GWSM250526162935WC6','GWSM2505261627598jJ','GWSM250526160240V91','GWSM250526160208uUk','GWSM250526154401ry4','GWSM250526154113E7q','GWSM250526150537XBY','GWSM250526143848GC0','GWSM250526143431ZCH','GWSM250526140916rEe','GWSM250526135816rA1','GWSM250526135354FJ4','GWSM250526131645M0T','GWSM250526124021tHj','GWSM250526122531wKo','GWSM250526121100FaZ','GWSM2505261206216ki','GWSM250526114750AeY','GWSM250526113755zqC','GWSM250526113651RHD','GWSM250526105341060','GWSM250526105159oG5','GWSM250526105009un8','GWSM2505261023592jo','GWSM250526101129gxN','GWSM250526100331otO','GWSM250525203450Ado','GWSM250525195849bK7','GWSM250525181205R2I','GWSM250525164027M8e','GWSM2505251637274z8','GWSM2505251536053Tn','GWSM250525150241YXQ','GWSM250525145021N8A','GWSM250525140428BwN','GWSM250525135455Xbz','GWSM2505251130177AK','GWSM250525105828RX6','GWSM250524210132ZuS','GWSM250524190640zqI','GWSM250524182516b10','GWSM2505241736333AR','GWSM250524173258Eoy','GWSM2505241710087xL','GWSM250524165911WsK','GWSM2505241553546aO','GWSM250524154429hl1','GWSM250524154015gFu','GWSM250524153245Li1','GWSM250524151824DZO','GWSM250524150557693','GWSM250524145737P3t','GWSM250524143916E6y','GWSM250524143456vSv','GWSM250524142805JUQ','GWSM250524141756fPm','GWSM250524141307C0X','GWSM250524140100f5E','GWSM250524135811C4p','GWSM250524120827Rn4','GWSM250524111912jQp','GWSM250524111112kS5','GWSM250524110942s76','GWSM250524101208M4Q','GWSM250524092709ZIW','GWSM2505240903345xl','GWSM250523183150lEn','GWSM2505231657291s7','GWSM250523161251Wr2','GWSM250523160452Idn','GWSM250523160249LQT','GWSM2505231536585YV','GWSM2505231536430aF','GWSM250523153352ICG','GWSM250523145312QnE','GWSM2505231444195cP','GWSM250523143801cyp','GWSM250523141831Q3d','GWSM250523135428Pct','GWSM250523134115aKl','GWSM250523133907V8x','GWSM250523130832rNI','GWSM250523125907olU','GWSM250523124257IiS','GWSM250523123703DNI','GWSM250523123508AHu','GWSM250523120706Rmp','GWSM250523112738m3r','GWSM250523111723qvA','GWSM2505231115447bx','GWSM250523111407KnJ','GWSM250523104201inY','GWSM2505231040182FE','GWSM250523102414X14','GWSM250523102230siv','GWSM250523101937eZB','GWSM2505231002391Hs','GWSM250523091627aVm'
        ]);


        // 到期订单自动退款
        $order_model = new BuOrder();
        $where       = [
            'order_code' => ['in', $order_codes], 'order_status' => ['in', $status]
        ];

        $afs_id_arr = [];
        if ($is_refund == 1) {

        }
        if ($ztk) {
            $order_model
                ->field('*')
                ->where($where)
                ->chunk(
                    100, function ($arr) {
                    $afs_model = new DbAfterSaleOrders();
                    $order_ids = array_column($arr, 'id');
                    var_dump($order_ids);
                    $afs_list = $afs_model->where(['order_id' => ['in', $order_ids], 'is_enable' => 1])->order('id desc')->column('*', 'order_id');
                    foreach ($arr as $item) {
                        $lock = 'ExpireRefund:' . $item['order_code'];
                        if (!getRedisLock($lock, 300)) {
                            continue;
                        }
                        $dlr = false;
                        // 退款
                        Queue::push('app\common\queue\AfterSaleRefund', json_encode(['order_code' => $item['order_code'], 'is_dlr' => $dlr]), config('queue_type.after_sale'));
                    }
//                if(input('test')==1){
//                    print_json($arr,$afs_id_arr);
//                }
                });
        } else {
            $order_model
                ->field('*')
                ->where($where)
                ->chunk(
                    100, function ($arr) {
                    $afs_model = new DbAfterSaleOrders();
                    $order_ids = array_column($arr, 'id');
                    var_dump($order_ids);
                    $afs_list = $afs_model->where(['order_id' => ['in', $order_ids], 'is_enable' => 1])->order('id desc')->column('*', 'order_id');
                    foreach ($arr as $item) {

                        $lock = 'ExpireRefund:' . $item['order_code'];
                        if (!getRedisLock($lock, 300)) {
                            continue;
                        }
                        $afs_info = $afs_model->where(['order_id' => $item['id'], 'is_enable' => 1])->order('id desc')->find();
                        if (!empty($afs_info)) {
                            continue;
                        }


                        $data = [
                            'afs_service_id'    => $item['dlr_code'] . '1' . date('ymdHis') . mt_rand(1000, 9999),
                            'order_id'          => $item['id'],
                            'afs_type'          => 1,
                            'afs_reason'        => '活动退款',
                            'afs_reason_detail' => '活动退款',
                            'user_info'         => json_encode_cn(['name' => $item['name'], 'phone' => $item['phone'], 'address' => $item['receipt_address']]),
                            'afs_status'        => 4,
                            'refund_money'      => $item['money'],
                            'refund_points'     => $item['integral'],
                            'pre_refund_money'  => $item['pre_use_money'],
                            'pre_refund_points' => $item['pre_point'],
                            'refund_remark'     => "活动退款",
                            'operate_list'      => json_encode_cn(
                                [
                                    [
                                        'afs_status' => 1, 'name' => '活动退款',
                                        'desc'       => '申请售后', 'time' => date("Y-m-d H:i:s")
                                    ]
                                ]
                            ),
                            'is_refund_order'   => ($item['promotion_source'] == 3 && $item['parent_order_type'] != 3) ? 0 : 1,
                        ];

                        $afs_id       = $afs_model->insertGetId($data);
                        $afs_id_arr[] = $afs_id;
                        //数据对接到售后中心
                        $as_data = [
                            'afs_id'                           => $afs_id, 'order_code' => $item['order_code'], 'type' => 'create', 'user_id' =>
                                $item['user_id'], 'created_at' => time(), 'is_dq' => 1
                        ];
                        Queue::push('app\common\queue\AfterSale', json_encode($as_data), config('queue_type.after_sale'));

                        if ($item['promotion_source'] == 3 && $item['parent_order_type'] == 3) {
                            continue;
                        }
                        $dlr = false;
                        // 退款
                        Queue::push('app\common\queue\AfterSaleRefund', json_encode(['order_code' => $item['order_code'], 'is_dlr' => $dlr]), config('queue_type.after_sale'));
                    }

                });
        }

        echo $order_model->getLastSql();
    }


    /**
     * 修改工会活动手机号
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function actPhone()
    {
        $input     = input('get.');
        $act_model = new AcPhonelist();
        if (empty($input['old_phone']) || empty($input['new_phone']) || empty($input['type'])) {
            print_json(1, '缺少参数');
        }
        $map  = [
            'phone' => $input['old_phone'],
            'type'  => $input['type'],
        ];
        $info = $act_model->where($map)->find();
        if (empty($info)) {
            print_json(1, '手机号不存在');
        }
        $upd = [
            'phone'             => $input['new_phone'],
            'last_updated_date' => date('Y-m-d H:i:s'),
            'modifier'          => 'index_v2'
        ];
        $re  = $act_model->where(['id' => $info['id']])->update($upd);


        print_json($re, $act_model->getLastSql());
    }

    /**
     * 删除工会信息
     * @return void
     */
    public function del_gonghui_user()
    {
        $input     = input('get.');
        $have_list_model =  new AcHaveTradeList();
        if (empty($input['type'])) {
            print_json(1, '缺少参数');
        }
        $have_where = [
            'sp_id'=>$input['type'],
        ];
        if($input['user_id']){
            $have_where['user_id'] = $input['user_id'];
        }
        if($input['phone']){
            $have_where['phone'] = $input['phone'];
        }
        if($input['user_id'] && $input['phone']){
            print_json(1, '缺少参数2');

        }
        $user_have =  $have_list_model->getOne(['where'=>$have_where]);
        if($user_have){
            $res = $have_list_model->where(['id'=>$user_have['id']])->delete();
            print_json($res,$have_list_model->getLastSql());
        }
        print_json(1, 'no----');

    }


    /**
     * 修改
     */
    public function updCommoditySku()
    {
        $id    = input('id');
        $model = new DbCommoditySku();
        $upd   = [
            'relate_car_work_hour' => '',
            'modifier'             => 'index_v2',
        ];
        $model->where('id', $id)->update($upd);
    }


    /**
     * 修改活动订单数据
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function actOrder()
    {
        $order_code  = input('order_code');
        $order_model = new BuOrder();
        $orderInfo   = $order_model->where('order_code', $order_code)->find();
        if (empty($orderInfo)) {
            print_json(1, '订单不存在');
        }
        $upd = [
            'dd_dlr_code'      => '',
            'promotion_source' => 10,
        ];
        $order_model->where('id', $orderInfo['id'])->update($upd);
        $act_order_model = new BuOrderAct();
        $act_info        = $act_order_model->where('order_code', $orderInfo['order_code'])->find();
        if (empty($act_info)) {
            $add = [
                'order_code'   => $orderInfo['order_code'],
                'act_dlr_code' => $orderInfo['dd_dlr_code'],
            ];
            $id  = $act_order_model->insertGetId($add);
        }
        print_json(0, 'success', ['sql' => $order_model->getLastSql()]);
    }


    // 修改订单商品价格
    public function updOrderMoney()
    {
        $order_code       = input('order_code');
        $buOrder          = new BuOrder();
        $buOrderCommodity = new BuOrderCommodity();
        $buOrder->where('order_code', $order_code)->update(['money' => '0.00']);
        $buOrderCommodity->where('order_code', $order_code)->update(['actual_use_money' => '0.00']);
        print_json(0, 'success');
    }




    public function jdExcelTest()
    {
        $search_time = '2024-09-01 ~ 2024-09-30';
        list($start_time, $end_time) = explode(' ~ ', $search_time);
    }



    /**
     * 去除订单商品规格编码的空格
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function updOrderCommodity()
    {
        $order_code            = input('order_code');
        $order_code_arr        = explode(',', $order_code);
        $where                 = ['order_code' => ['in', $order_code_arr]];
        $order_commodity_model = new BuOrderCommodity();
        $field                 = 'id,order_code,third_sku_code';
        $list                  = $order_commodity_model->where($where)->field($field)->select();
        $sql                   = [];
        foreach ($list as $key => $item) {
            $upd = [
                'third_sku_code' => trim($item['third_sku_code'])
            ];
            $order_commodity_model->where('id', $item['id'])->update($upd);
            $sql[] = $order_commodity_model->getLastSql();
        }
        print_json(0, 'success', $sql);

    }





    /**
     * 获取授权信息
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getAuthorCode()
    {
        $orderId     = input('order_id');
        $order_model = new BuOrder();
        $order       = $order_model->where('id', $orderId)->find();
        if (empty($order)) {
            print_json(1, '订单不存在');
        }
        $user_model = new DbUser();
        $user       = $user_model->where('id', $order['user_id'])->find();
        $author     = QuickWin::create('quick_win')->authorCode(['appCode' => 'nissan', 'oneId' => $user['one_id'], 'vin' => $order['order_vin']]);
        print_json(0, 'success', $author);
    }


    /**
     * 修改退款信息
     */
    public function updLyPayLog()
    {

        $post       = input('post.');
        $ly_pay_log = new DbLyPayLog();
        $map        = [
            'id'            => $post['pay_id'],
            'ms_order_code' => $post['ms_order_code'],
            'pay_type'      => 2
        ];

        $ms_refund_code = $post['result']['respData']['msRefundOderCode'];
        $upd            = [
            'ms_refund_code' => $ms_refund_code,
            'result'         => json_encode($post['result']),
            'modifier'       => '手动修改',
        ];
        $ly_pay_log->where($map)->update($upd);

        $after_model = new DbAfterSaleOrders();

        $upd = ['cashier_refund_no' => $ms_refund_code, 'afs_status' => 6];
        $after_model->where('id', $post['aft_id'])->update($upd);
        print_json(0, 'success');
    }


    public function delCarSeries()
    {
        $id                    = input('id');
        $user_car_series_model = new DbUserCarSeries();

        $re = $user_car_series_model->where('id', $id)->delete();
        print_json(0, 'success', $re);

    }


    /**
     * 修改订单商品发货状态
     */
    public function updOrderCommodityStatus()
    {
        $ids           = input('order_commodity_id');
        $idArr         = explode(',', $ids);
        $order_c_model = new BuOrderCommodity();
        $upd           = [
            'order_commodity_status' => 4,
        ];
        $re            = $order_c_model->whereIn('id', $idArr)->update($upd);
        print_json(0, 'success', $re);
    }


    public function test_fen_jf()
    {
        $order_code        = input('order_code');
        $order_goods_model = new BuOrderCommodity();
        $sep_rule_model    = new DbSeparateAccountRules();
        $this->order_model = new BuOrder();
        //外层已经做了循环，这一层就只有一单，正常
        $all_order        = $this->order_model->getList(['where' => ['order_code' => $order_code, 'parent_order_type' => ["<>", 3]]]);
        $dh_point         = 0;
        $openid           = '';
        $dlr_code         = '';
        $is_integral_shop = 0;
        foreach ($all_order as $order_a) {
            $order_goods = $order_goods_model->getList(['where' => ['order_code' => $order_a['order_code'], 'mo_sub_id' => 0]]);
            //            $set_point   = $order_a['integral'];
            $plat_m = 0;
            $dlr_m  = 0;
            foreach ($order_goods as $vv) {
                $is_integra = Db::name('db_commodity')->where(['id' => $vv['commodity_id']])->field('is_integral_shop')->find();
                if ($is_integra['is_integral_shop'] == 1) {
                    $is_integral_shop = 1;
                }
                $set_point = $vv['actual_point'];
                //加入预售点...
                //改改改
                if ($vv['pre_sale_id'] > 0) {
                    //应该是预售的时候使用的是预售的使用积分
                    if (in_array($order_a['order_status'], [1, 8])) {
                        $set_point = $order_a['pre_point'];
                    }
                }
                if ($vv['dd_dlr_code']) {
                    echo $set_point . '||' . $vv['dd_dlr_code'] . '//';
                    $dlr_m += $set_point;
                } else {
                    echo $set_point . '++' . $vv['dd_dlr_code'] . '//';

                    $plat_m += $set_point;
                }
                echo "<br/>";
                //不再考虑分账规则...
//                if ($vv['spr_id'] > 0) {
//                    $sep_rule = $sep_rule_model->getOneByPk($vv['spr_id']);
//                    $sep      = json_decode($sep_rule['rule_info'], true);
//                    $plat_m   += round($set_point * $sep['platform'] / 100, 1);
//                    //快递类应该扎账要过滤PV...
//                    if ($vv['order_mail_type'] == 2 && $vv['pay_dlr_code'] <> 'PV') {
//                        $plat_m += round($set_point * $sep['dlr'] / 100, 1);
//                    } else {
//                        $dlr_m += round($set_point * $sep['dlr'] / 100, 1);
//                    }
//                    Logger::error('set-point-all', ['order_code' => $vv['order_code'], 'pm' => $plat_m, 'dm' => $dlr_m, 'sp' => $set_point, 'sep' => $sep]);
//                } else {
//                    return false;//没有设置分账不往下走
//                    continue;
//                }
            }
            $order_a['set_point'] = $order_a['integral'];
            $plat_m               = round($plat_m, 0);
            echo $plat_m . '-1-' . $dlr_m;
            if ($plat_m > 0) {
                //GWSM代表总部
//                $this->userPoint($order_a, $plat_m, 'GWSM', $remark, $modifier);
                $dlr_m = $order_a['set_point'] - $plat_m;
            }
            echo '-2-' . $dlr_m;
            die();
            if ($dlr_m > 0) {
//                $this->userPoint($order_a, round($dlr_m, 0), $order_a['dd_dlr_code'] ?? $order_a['dlr_code'], $remark, $modifier);
            }
            $openid   = $order_a['openid'];
            $dlr_code = $order_a['dlr_code'];
            $dh_point = $order_a['set_point'];
        }
    }

    //插入关联分类
    public function test_in_ccc()
    {
        $in_sql               = input('in_sql', 0);
        $sql                  = "SELECT flat.commodity_id,flat.commodity_set_id,a.id card_id,b.class_id,b.group_card_type from t_db_card a
join (SELECT class_id,group_card_type,card_id from  t_db_commodity_card where class_id>0 GROUP BY class_id)  b on a.id=b.card_id
join t_db_commodity_flat flat on FIND_IN_SET(a.id,flat.card_id) and FIND_IN_SET(b.class_id,flat.comm_type_id_str)
where a.receive_start_date<=NOW() and a.validity_date_end>=NOW() ";
        $commodity_card_model = new DbCommodityCard();
        $list                 = $commodity_card_model->query($sql);
        $card_h_list          = $commodity_card_model->getList(['where' => ['class_id' => ['>', 0], 'commodity_set_id' => ['>', 0]]]);
        $cc_card              = [];
        if ($card_h_list) {
            foreach ($card_h_list as $v_v) {
                $cc_card[] = $v_v['card_id'] . $v_v['commodity_set_id'] . $v_v['class_id'];
            }
        }

        $inset_all = [];
        if ($list) {
            foreach ($list as $v) {
                $kkk           = $v['card_id'] . $v['commodity_set_id'] . $v['class_id'];
                $v['modifier'] = 'c_h_in';
                if (!in_array($kkk, $cc_card)) {
                    $inset_all[] = $v;
                }
            }
        }
        if (!$in_sql) {
            print_json($inset_all);
        }

        $res = $commodity_card_model->insertAll($inset_all);
        print_json($res);

    }


    // 核销 - 处理异常的订单
    public function couponConsume()
    {
        $coupon_codes    = input('coupon_codes');
        $coupon_code_arr = explode(',', $coupon_codes);
        $start_time      = input('start_time');
        $card_r_r_model  = new BuCardReceiveRecord();

        $map         = [
            'a.consume_date' => ['>', $start_time],
            'a.coupon_code'  => ['in', $coupon_code_arr]
        ];
        $record_list = $card_r_r_model->alias('a')
            ->join('t_bu_order b', 'a.consume_order_code=b.order_code')
            ->where($map)
            ->field('a.*,b.order_code,b.order_status')
            ->select();
        $order_model = new BuOrder();
        $jobsLog     = new DbJobsLog();
        foreach ($record_list as $key => $item) {
            if (!in_array($item['order_status'], [1, 3, 8, 18])) {
                $coupon_receive_id = $item['coupon_receive_id'];
                if ($item['coupon_receive_id'] == 0) {
                    $info              = ['coupon_code' => $item['coupon_code'], 'request_channel' => 1];
                    $coupon_receive    = QuickWin::create('quick_win')->getCouponReceiveRecord($info);
                    $coupon_receive_id = $coupon_receive['rows'][0]['id'];
                }

                // 支付成功的  核销卡券
                $data = [
                    [
                        "business_order_name"     => "",
                        "business_order_no"       => $item['consume_order_code'],
                        "coupon_code"             => $item['coupon_code'],
                        "coupon_discounted_price" => (int)($item['card_all_yh'] * 100),
                        "coupon_receive_id"       => $coupon_receive_id,
                        'consume_date'            => $item['consume_date'],
                        "creator"                 => "",
                        "order_source"            => "",
                        "used_store"              => $item['consume_dlr_code'],
                        'consume_channel'         => 3, // 核销渠道  3-商城
                    ]
                ];
                $str  = 'event_type:2';

                $add   = [
                    'queue'       => 'card_receive',
                    'source_type' => $item['coupon_code'],
                    'data_info'   => json_encode_cn($data),
                    'modifier'    => $str,
                ];
                $jobId = $jobsLog->insertGetId($add);

                // 核销卡券
                $ret = QuickWin::create('quick_win')->postCouponConsume($data);
                $upd = ['result_info' => json_encode_cn($ret)];
                $jobsLog->where('id', $jobId)->update($upd);

                if ($ret['result'] == 1) {
                    $upd = [
                        'coupon_receive_id'    => $coupon_receive_id,
                        'quick_win_is_consume' => 1,
                        'consume_date'         => date('Y-m-d H:i:s'),
                        'last_updated_date'    => date('Y-m-d H:i:s'),
                        'modifier'             => 'consumeJob'
                    ];
                    $card_r_r_model->where('id', $item['id'])->update($upd);
                    $map = ['parent_order_code' => $item['consume_order_code']];
                    $order_model->where($map)->update(['is_cc_ok' => 0]);
                }
            }
        }

        print_json(0, 'success');

    }

    // 反核销
    public function couponReviveV3()
    {
        $coupon_code    = input('coupon_code');
        $card_r_r_model = new BuCardReceiveRecord();
        $record_info    = $card_r_r_model->where(['coupon_code' => $coupon_code])->find();
        $net_user       = new NetUser();
        $re             = $net_user->postCouponReviveV3(['id' => $record_info['user_id']], ['coupon_code' => $coupon_code]);
        print_json(0, $re);
    }


    // 修改卡券信息
    public function updCardInfo()
    {
        $post = input('post.');
        $ids  = input('ids');
        if (empty($ids)) {
            print_json(1, '卡券id不能为空');
        }
        $idArr = explode(',', $ids);
        unset($post['ids']);
        unset($post['user_token']);
        unset($post['user_data']);
        $card_model = new DbCard();
        $re         = $card_model->whereIn('id', $idArr)->update($post);
        if ($re) {
            print_json(0, '修改成功', $card_model->getLastSql());
        } else {
            print_json(1, '修改失败');
        }
    }


    /**
     * 修改e3s推送信息主表
     */
    public function change_e3s_index()
    {
        $ids  = input('ids');
        $data = input('post.');
        if (empty($ids)) {
            print_json(1, '缺少参数');
        }
        $idArr       = explode(',', $ids);
        $where['id'] = ['in', $idArr];
        unset($data['ids']);
        unset($data['user_data']);
        unset($data['user_token']);
        $e3s_index_model = new BuToE3sIndex();
        $e3s_index_model->where($where)->update($data);
        print_json(0, $e3s_index_model->getLastSql());
    }


    /**
     * 修改e3s推送信息详情表
     */
    public function change_e3s_detail()
    {
        $ids  = input('ids');
        $data = input('post.');
        if (empty($ids)) {
            print_json(1, '缺少参数');
        }
        $idArr       = explode(',', $ids);
        $where['id'] = ['in', $idArr];
        unset($data['ids']);
        unset($data['user_data']);
        unset($data['user_token']);
        $e3s_index_detail = new BuToE3sDetail();
        $e3s_index_detail->where($where)->update($data);
        print_json(0, $e3s_index_detail->getLastSql());
    }


    /**
     * 修改E3S保养套餐设置_备件信息数据
     */
    public function change_e3s_maintenance_package_part()
    {
        $part_nos = input('part_nos');
        $data     = input('post.');
        if (empty($part_nos)) {
            die('缺少参数');
        }
        $part_no_arr            = explode(',', $part_nos);
        $e3s_package_part_model = new E3sPackagePart();
        $map                    = ['part_no' => ['in', $part_no_arr]];
        unset($data['part_nos']);
        unset($data['user_data']);
        unset($data['user_token']);
        $data['modifier'] = '手动修改';
        $e3s_package_part_model->where($map)->update($data);
        print_json(0, $e3s_package_part_model->getLastSql());
    }


    /**
     * 获取卡券信息
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getCardInfo()
    {
        $coupon_code = input('coupon_code', '');
        $coupon_id   = input('card_id', '');
        $vin         = input('vin', '');
        $m_id        = 0;
        if ($vin) {
            $m_id = 1;
        }
        $service = new NetCardKafKa();
        $re      = $service->getCouponReceiveRecord($coupon_code, $coupon_id, $vin, $m_id);
        if ($re->isSuccess()) {
            print_json(0, 'success', $re->getData() ?? $re->getMessage());
        } else {
            print_json(1, $re->getData() ?? $re->getMessage());
        }
    }


    public function c_card_act()
    {
        $activityId = input('act_id', '');
        $card_id    = input('card_id', '');
        $type       = input('type', 1);
        if ($type == 1) {
            $card_model = new DbCard();
            $map        = ['id' => $card_id];
            $upd        = [
                'activity_id'       => $activityId,
                'admin_act_id'      => $activityId, // 后台管理系统使用
                'modifier'          => 'hand_up',
                'last_updated_date' => date('Y-m-d H:i:s')
            ];
            $res        = $card_model->where($map)->update($upd);
            print_json($res, $card_model->getLastSql());
        }
        if ($type == 2) {
            $act_model = new DbActivity();
            $map       = ['activity_id' => $activityId];
            $res       = $act_model->where($map)->delete();
            print_json($res, $act_model->getLastSql());
        }

    }


    public function test_car_list()
    {
        $params = [
            'carBrandCode'=>1,
            'beginTime'=>'1990-04-07 10:42:40',
            'endTime'=>date('Y-m-d H:i:s'),
        ];
        $car_age = E3spRefactor::create('e3sp_refactor')->getCarSList($params);
        print_json($car_age);
        if($car_age){
            $e3_car_model =  new E3sCarSeries();
            foreach ($car_age as  $v){
                if($v['isEnable'] && isset($v['isNevCartypeFlag'])){
                    $where =['car_series_code'=>$v['carSeriesCode']];
                    $data = ['is_nev'=>$v['isNevCartypeFlag']];
                    $res = $e3_car_model->saveData($data,$where);
                }
            }
        }
        print_json($car_age['data']);
        echo ($car_age);

    }

    public function test_e_dlr()
    {
        $data = ["pageIndex"=>1,
            "carBrandCode"=>"1",
            "isDirectsellingDlr"=>"1",
            "pageSize"=>500,
            "cmap"=>[
                "applyDate"=>date("Y-m-d H:i:s",time())
            ]
        ];
        $res =  LyE3sDlr::create('ly_e3s_dlr')->getList($data);
        print_json($res);
    }


    public function order_card_consume()
    {
        $map         = [
            'a.created_date'    => ['>', '2024-12-28'],
            'a.pay_time'        => ['neq', ''],
            'a.order_source'    => 20,
            'a.order_status'    => ['in', [7, 19]],
            'verification_user' => 'e3s_c',
            'c.status'          => 1,

        ];
        $field       = 'a.id,a.order_code,a.user_id,a.order_status,a.order_source,a.dd_dlr_code,b.goods_card_ids,c.id as cid,
         c.coupon_code,c.`status`,c.consume_order_code,c.consume_date,c.validity_date_end,c.get_dlr_code';
        $order_model = new BuOrder();
        $list        = $order_model->alias('a')
            ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
            ->join('t_bu_card_receive_record c', 'a.user_id = c.user_id and b.goods_card_ids = c.card_id')
            ->field($field)
            ->where($map)
            ->where('a.dd_dlr_code=c.get_dlr_code')
            ->select();

        $card_r_model = new BuCardReceiveRecord();
        foreach ($list as $key => $item) {
            // 核销
            $data                = array(
                'status'             => 3,
                'consume_date'       => date('Y-m-d H:i:s'),
                'consume_dlr_code'   => $item['dd_dlr_code'],
                'consume_order_code' => $item['order_code'],
            );
            $where               = ['status' => 1, 'user_id' => $item['user_id'], 'card_id' => (string)$item['goods_card_ids']];
            $order_card_code_arr = $card_r_model->where($where)->column('card_code');
            if (empty($order_card_code_arr)) {
                continue;
            }
            $card_r_model->saveData($data, $where);//把卡券做成 已核销
            // 实时核销
            $res = (new NetUser())->couponReceive($order_card_code_arr, [], 2, 2);
        }
    }


    /**
     * 删除用户
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function del_user()
    {
        $user_id = input('id');
        $sign    = input('sign');
        if ($sign != 'e2a6de83acf8b4c24f4b6f2396aac027') {
            print_json(0, 'sign错误');
        }
        $user_model = new DbUser();
        $info       = $user_model->where('id', $user_id)->find();
        if (empty($info)) {
            print_json(1, '用户不存在');
        }
        $re = $user_model->where('id', $user_id)->delete();
        if ($re) {
            print_json(0, '删除成功', $info);
        } else {
            print_json(1, '删除失败');
        }
    }


    /**
     * 修改财务统计数据
     */
    public function change_transaction_order()
    {
        $ids  = input('ids', '');
        $data = input('post.');
        if (!$ids) {
            die('缺少参数');
        }
        $transaction_order_model = new DbLyTransactionOrder();

        $idArr = explode(',', $ids);
        $where = ['id' => ['in', $idArr]];
        unset($data['ids']);
        unset($data['order_code']);
        unset($data['user_data']);
        unset($data['user_token']);

        $data['modifier']      = 'change';
        $data['modified_date'] = date('Y-m-d H:i:s');
        $res                   = $transaction_order_model->where($where)->update($data);
        print_json($res, $transaction_order_model->getLastSql());
    }


    public function upDownChannelDlrByCommTypeName()
    {
        $comm_type_id               = input('comm_type_id');
        $commodity_flat_model       = new DbCommodityFlat();
        $code_val                   = 'GWNET';
        $name_val                   = '官网';
        $map                        = ['shelves_type' => 5];
        $map[]                      = ['exp', "(find_in_set({$comm_type_id},comm_type_id_str)) "];
        $map['up_down_channel_dlr'] = ['like', '%' . $code_val . '%'];

        $list                = $commodity_flat_model->where($map)->select();
        $commodity_set_model = new DbCommoditySet();

        foreach ($list as $item) {
            // 查询上架渠道
            $up_down_channel_dlr_arr  = explode(',', $item['up_down_channel_dlr']);
            $up_down_channel_name_arr = explode(',', $item['up_down_channel_name']);

            $new_up_down_channel_dlr_arr  = array_filter($up_down_channel_dlr_arr, function ($value) use ($code_val) {
                return $value !== $code_val;
            });
            $new_up_down_channel_name_arr = array_filter($up_down_channel_name_arr, function ($value) use ($name_val) {
                return $value !== $name_val;
            });
            $map                          = ['id' => $item['id']];
            $upd                          = [
                'up_down_channel_dlr'  => implode(',', $new_up_down_channel_dlr_arr),
                'up_down_channel_name' => implode(',', $new_up_down_channel_name_arr),

            ];
            $commodity_flat_model->where($map)->update($upd);
            // 修改set表
            $map = ['id' => $item['commodity_set_id']];
            $commodity_set_model->where($map)->update($upd);
        }
        print_json(0, 'success');

    }

    public function test_sql_new()
    {
        $much_model = new DbActivityMuchCard();
        $to_date    = date('Y-m-d');
        $where      = ['b.is_enable' => 1, 'a.is_enable' => 1, 'b.validity_date_start' => ['<=', $to_date], 'b.validity_date_end' => ['>=', $to_date]]; //
        $list       = $much_model->alias('a')->join('t_db_card b', 'b.id=a.card_id')->where($where)->field('b.quick_win_card_id,b.id')->select();
        echo $much_model->getLastSql();

    }



    public function change_ac_blacklist()
    {
        $id= input('id');
        $is_enable= input('is_enable');
        $model = new AcBlacklist();

        $map = ['id'=>$id];
        $upd = [
            'is_enable'=>$is_enable,
            'last_updated_date' => date('Y-m-d H:i:s'),
        ];
        $re = $model->where($map)->update($upd);
        echo $model->getLastSql();

    }

    //软删除用户
    public function upuser()
    {
        set_time_limit(0);
//        update t_db_user set is_enable=0,modifier='h_c_412' where is_enable=1 and id not in (SELECT user_id from t_bu_order GROUP BY user_id) and plat_id<>'' and plat_id<>'******' and plat_id in (SELECT plat_id from t_db_user where is_enable=1 GROUP BY plat_id HAVING count(1)>1)
        $user_model  =  new DbUser();
        $user_ids =  input('ids','');
        $is_enable =  input('is_enable',0);
        $limit = input('limit',50000);
        $c_data = input('c_data','2025-04-08 15:29:40');
        $data = ['is_enable'=>$is_enable,'modifier'=>'h_c_412'];
        if(!$user_ids){
            $order_model = new BuOrder();
//            $sql =  "SELECT plat_id from t_db_user where is_enable=1 and plat_id<>'' and plat_id<>'******' GROUP BY plat_id HAVING count(1)>1";
//            $list =  $user_model->query($sql);
//            $plat_id = array_column($list,'plat_id');
//            $user_order =  $order_model->getColumn(['column'=>'user_id']);
//            $where = [
//                'is_enable'=>1,
//                'id'=>['not in',$user_order],
//                'plat_id'=>['in',$plat_id],
//                ];

            $sql =  sprintf("SELECT id from  t_db_user  where is_enable=1 and created_date>'%s' and id not in (SELECT user_id from t_bu_order GROUP BY user_id) LIMIT %s",$c_data,$limit);
            $list =  $user_model->query($sql);
            $user_list = array_column($list,'id');
            $user_arr =  array_chunk($user_list,10000);
            foreach ($user_arr as $v){
                $res =  $user_model->saveData($data,['id'=>['in',$v]]);

            }
            print_json($res,$user_model->getLastSql());

        }
        $res =  $user_model->saveData($data,['id'=>['in',$user_ids]]);
        print_json($res,$user_model->getLastSql());
    }



    /**
     * 修改商品分类级别
     */
    public function updCommodityType()
    {
        $data = input('get.');
        $comm_type_model = new DbCommodityType();
        $map             = [
            'id' => $data['id']
        ];
        unset($data['id']);
        unset($data['order_code']);
        unset($data['user_data']);
        unset($data['user_token']);

        $data['last_updated_date'] = date('Y-m-d H:i:s');
        $data['modifier'] = '手动修改';
        $comm_type_model->where($map)->update($data);
        $sql = $comm_type_model->getLastSql();
        print_json(0, 'success', $sql);
    }
    /**
     * 删除京东订单
     */
    public function del_jd_orders()
    {
        $order_code        = input('order_codes',"GWSM250411165411xCr,GWSM250411164647kuj,GWSM250411164523U8E,GWSM250411160533VJt,GWSM250411155856HZS,GWSM2504111556439ie,GWSM250411155518k3q,GWSM250411154408HEp,GWSM250411153520ISG,GWSM250411153200gsY,GWSM250411153122AAG,GWSM250411152835y2s,GWSM2504111522550DM,GWSM250411151838hOb,GWSM250411151429Dbj,GWSM250411151303X6b,GWSM2504111511112Hw,GWSM250411150615qPr,GWSM250411142007Gr3,GWSM250411141112SwQ,GWSM250411141020xSv,GWSM250411134917KoB,GWSM250411133136EHn,GWSM250411132650Z5j,GWSM250411132016Y1Z,GWSM250411131342Y2F,GWSM250411122628BYJ,GWSM2504111210465Fk,GWSM250411115212sGV,GWSM250411112802MbA,GWSM2504111127218Gg,GWSM250411111849529,GWSM250411111113MRc,GWSM250411111009qZK,GWSM250411110236f6b,GWSM2504111057103RC,GWSM250411102529ziN,GWSM250411100244MWU,GWSM250411100149HBh,GWSM2504101802449Ot,GWSM2504101758271ov,GWSM250410174119xtF,GWAPP250410173554XkI,GWSM250410173020o4p,GWSM250410171149CeD,GWSM250410171017wdm,GWSM250410162354Cc3,GWSM250410162232KjO,GWSM250410162229PQD,GWSM2504101601377fo,GWSM250410155823R7H,GWSM250410154949rAJ,GWSM250410154857zSG,GWSM250410154601fX7,GWSM250410154402oeG,GWSM250410153552GcP,GWSM250410143922Jfw,GWSM250410143341xAM,GWSM250410142836FWd,GWSM250410141810fuF,GWSM250410141156Qyk,GWSM250410134717Jsh,GWSM250410134106Yeg,GWSM250410132100SjX,GWSM250410130856FQ3,GWSM250410130015QpA,GWSM2504101250460YM,GWSM250410125010QRu,GWSM250410121807ZLx,GWSM250410115331gBT,GWSM250410114226KBH,GWSM250410113739v8R,GWSM250410110749p3G,GWSM2504101057167yL,GWSM250410104326Bzh,GWSM250410103850rW1,GWSM250410103335dqY,GWSM250410103310alO,GWSM250410102659Ohi,GWSM250410102126HMi,GWSM2504101020139f6,GWSM250410101623IhJ,GWSM250410101229vH9,GWSM2504101012009yS,GWSM250410095239Y09,GWSM250410095155uA9,GWSM250410094953CM1,GWSM250410094929hp5,GWSM2504100944469BA,GWSM250410092919dU1,GWSM250410092122ELo,GWSM2504100918433Kq,GWSM2504100918047do,GWSM250410085045E3b,GWSM2504092015234Ff,GWSM250409190134FOg,GWSM2504091848005H4,GWSM250409182057wC8,GWSM2504091804594Zg,GWSM250409175157lZz,GWSM250409174947niF,GWSM250409174547Yf1,GWSM2504091729514pa,GWSM250409172841m53,GWSM250409171543Pop,GWSM250409171139UT5,GWSM2504091706307xb,GWSM2504091701040aM,GWSM2504091658426LW,GWSM250409165644EFA,GWSM250409165423fit,GWSM250409165243e02,GWSM250409164540ByL,GWSM250409163655VU2,GWSM2504091636455p1,GWSM250409163640gdU,GWSM250409163505YsI,GWSM2504091634556n5,GWSM250409163440q5h,GWSM250409163406m6f,GWSM250409162933Qix,GWSM250409162720RF0,GWSM250409162625tmx,GWSM250409162452Iuv,GWSM250409162207Pcr,GWSM250409155028KXi,GWSM250409154555b9o,GWSM250409154428x9l,GWSM250409153859sFj,GWSM2504091536040YG,GWSM250409153453jsw,GWSM250409153100Gac,GWSM25040915281519W,GWSM250409151531tXG,GWSM250409151403MkV,GWSM250409151050Edd,GWSM250409150724td3,GWSM250409150504shQ,GWSM2504091504224nL,GWSM250409150022aYV,GWSM250409145643nLt,GWSM250409145613GRV,GWSM250409144926Kes,GWSM250409144911RpG,GWSM250409142435VT5,GWSM250409142411Pkn,GWSM250409141214mhF,GWSM250409134722gzY,GWSM250409134316Lhc,GWSM2504091342231ZZ,GWSM250409133516ZFI,GWSM2504091240542FR,GWSM250409123813yI5,GWSM250409115719kQF,GWSM250409115656Z8v,GWSM2504091132160K1,GWSM25040911234744X,GWSM250409112022eNR,GWSM250409111749Pow,GWSM250409111650uMg,GWSM250409111046upe,GWSM250409111008ebC,GWSM250409105620e6M,GWSM2504091043520CJ,GWSM250409103703tJQ,GWSM25040910332283j,GWSM250409102519u4S,GWSM2504091022349Sc,GWSM250409100951JV3,GWSM250409094804Bel,GWSM250409094520jS0,GWSM2504090945060XZ,GWSM250409094211RDj,GWSM250409093908m2G,GWSM250409093819N67,GWSM250409093245vk0,GWSM250409092715LLo,GWSM250409092001b0r,GWSM250409091552JJ0,GWSM250409091438yN4,GWSM250409083230eew,GWSM250409083042nUl,GWSM250408174634v9h,GWSM250408172532zaM,GWSM2504081633143iw,GWSM250408160452255,GWSM250408155756sn8,GWSM250408155711qjy,GWSM250408154100FsW,GWSM250408153921Vji,GWSM2504081509152jD,GWSM250408150912QKt,GWSM250408150114z2S,GWSM250408150110nQ3,GWSM250408145747Ety,GWSM250408145310Wzq,GWSM250408145020PjL,GWSM250408145006EHF,GWSM250408144345aXr,GWSM250408144307jOf,GWSM250408143820Bd2,GWSM250408143750Cm0,GWSM250408142807sbs,GWSM250408142743tzq,GWSM250408142517vYB,GWSM2504081419472ug,GWSM250408141718r53,GWSM250408141037Mp1,GWSM250408140534oR9,GWSM250408135846W5b,GWSM250408135605Srn,GWSM250408135522wH4,GWSM250408133516pj8,GWSM250408133358CTg,GWSM250408133242HmX,GWSM250408131834TAO,GWSM250408131820jaA,GWSM250408130126USW,GWSM250408122535fw7,GWSM2504081224562sw,GWSM250408121214xGA,GWSM250408120738JWT,GWSM250408120536KjH,GWSM250408115501b2R,GWSM250408114321z03,GWSM2504081142528VI,GWSM250408113742yDK,GWSM250408112448jx1,GWSM250408112051sQ5,GWSM2504081109090AR,GWSM250408110755JOM,GWSM2504081104582PI,GWSM250408110106zwd,GWSM250408105814lKQ,GWSM250408105603meC,GWSM2504081052345b6,GWSM250408105125ESz,GWSM250408104921W0o,GWSM250408104533f6J,GWSM250408104352myk,GWSM250408103920eHh,GWSM250408103818MrP,GWSM250408103433zI4,GWSM2504081032334RC,GWSM250408102549Bgo,GWSM250408101125VnX,GWSM250408100846NG9,GWSM250408100547bBT,GWSM250408100059mFH,GWSM250408095818SfG,GWSM250408094953QTJ,GWSM250408094918X0e,GWSM250408094446pTH,GWSM250408094355ZXQ,GWSM250408094135lcq,GWSM250408092926OKk,GWSM250408092803g0g,GWSM250408091932Brw,GWSM250408091120g59,GWSM250408090308Vre");
        $order_jd_model    = new BuOrderJd();
        $order_goods_model = new BuOrderCommodity();
        $order_jds          = $order_jd_model->getList(['where' => ['shop_order_id' => ['in',$order_code],'is_enable'=>1]]);
        if ($order_jds) {
            foreach ($order_jds as $order_jd){
                $re1 = $order_jd_model->saveData(['is_enable' => 0, 'modifier' => 'h_c0424'], ['id' => $order_jd['id']]);
                $re2 = $order_goods_model->saveData(['third_order_id' => '', 'modifier' => 'h_c0424'], ['third_order_id' => $order_jd['order_id'],'order_code'=>$order_jd['shop_order_id']]);
                if ($re1 && $re2) {
                    echo $order_jd['shop_order_id'].'--删除成功<br/>';
                } else {
                    echo $order_jd['shop_order_id'].'--删除失败<br/>';
                }
            }
        } else {
            print_json(1, '订单不存在',$order_jd_model->getLastSql());//

        }
    }

    public function test_card_goods()
    {
        $net_card =  new NetCard();
        $card_id =  input('card_id','');
        $net_card->cardClassToGoods($card_id);
    }



    public function test_yb_sku()
    {
        $user_vin = input('vin', 'LGBH53E0XEY028002');
        $kilometer = input('yb', 0);
        $com = new Common();
        $yb_can_info = $com->yb_sku_code($user_vin, $kilometer);
        print_json($yb_can_info);
    }
    public function test_best_card()
    {
        $act_id =  input('act_id','');
        $card_id =  input('card_id','');
        $act_id_arr = explode(',',$act_id);
        $card_arr_id_arr = explode(',',$card_id);
        $act_moddel =  new DbActivity();
        $act_where = ['a.activity_id'=>['in',$act_id_arr],'b.card_id'=>['in',$card_arr_id_arr]];

        $act_list =  $act_moddel->alias('a')->join('t_db_activity_card b','a.activity_id=b.activity_id')
            ->where($act_where)->field('b.*,a.coupon_mutex_superpose_type,a.activity_mutex_flag,a.mutex_activity_list')->select();
        $act_arr = [];
        $card_model =  new DbCard();
        $card_list =  $card_model->getList(['where'=>['id'=>['in',$card_arr_id_arr]]]);
        $card_coo_list = [];
        foreach ($card_list as $v){
            $card_coo_list[$v['id']] = ['id'=>$v['id'],'card_name'=>$v['card_name'],'quick_win_card_id'=>$v['quick_win_card_id'],'value'=>1111,'is_gift_card'=>$v['is_gift_card']];
        }

        foreach($act_list as $act_v){
            $act_arr[$act_v['activity_id'].$act_v['card_id']] = $act_v;
        }
        foreach ($act_id_arr as $k=> $v) {
            $one_card_id =  $card_arr_id_arr[$k];
            $vv = $card_coo_list[$one_card_id];
            $card_act = $act_arr[$v . $one_card_id] ?? [];
            $act_not_with = '';
            $can_use = '';
            $can_no_use = '';

            if ($card_act) {

                if ($card_act['activity_mutex_flag'] == 1) {
                    $act_not_with = $card_act['mutex_activity_list'];
                }
                if ($card_act['coupon_mutex_superpose_type'] == 1) {
                    $can_use = $card_act['coupon_id'];
                }
                if ($card_act['coupon_mutex_superpose_type'] == 2) {
                    $can_no_use = $card_act['coupon_id'];
                    $can_use = '';
                }
                if ($card_act['coupon_mutex_superpose_type'] == 3) {
                    $can_use = 'all';
                    $can_no_use = '';
                }
                if ($card_act['coupon_mutex_superpose_type'] == 4) {
                    $can_no_use = 'all';
                    $can_use = '';

                }
            }
            $vv['act_card_id'] = $vv['quick_win_card_id'];
            $vv['can_use'] = $can_use;
            $vv['can_no_use'] = $can_no_use;
            $vv['act_id'] = $v;
            $vv['can_no_with'] = $act_not_with;
            $all_card_list[$k] = $vv;
        }

        $act_card_hc_arr = $this->act_card_hc($all_card_list);
        print_json($act_card_hc_arr,$all_card_list);
    }

    public function act_card_hc($new_cards)
    {
        $cards=[];
        $cards_arr =[];
        $can_no_use_arr = [];
        $can_use_arr = [];
        $can_no_with_arr = [];
        foreach ($new_cards as $v){
            $v['card_value'] =  $v['value'];
            $v['card_id'] =  $v['id'];
            if($v['can_use']!=='all'){
                if($v['can_use']){
                    $v['can_use'] = explode(',', $v['can_use']);
                    foreach ($v['can_use'] as $vv){
                        $can_use_arr[$vv][]=$v['act_card_id'];
                    }
                }else{
                    $v['can_use'] = [];
                }
            }

            if($v['can_no_use']!=='all'){
                if($v['can_no_use']){
                    $v['can_no_use'] = explode(',', $v['can_no_use']);

                    foreach ($v['can_no_use'] as $vv){
                        $can_no_use_arr[$vv][]=$v['act_card_id'];
                    }
                }else{
                    $v['can_no_use'] = [];
                }
            }

            if($v['can_no_with']!=='all'){
                if($v['can_no_with']){
                    $v['can_no_with'] = explode(',', $v['can_no_with']);
                    foreach ($v['can_no_with'] as $vv){
                        $can_no_with_arr[$vv][]=$v['act_id'];
                    }
                }else{
                    $v['can_no_with'] = [];
                }
            }
            if(!isset($v['card_code'])){
                $v['card_code'] = $v['id'];
            }

            $v['no_rel_value']= $v['value'];
            if($v['is_gift_card']==1){
                $v['no_rel_value'] = 999999999;//如果是买赠券--价格最高
            }

            $cards[]=[
                'id'=>$v['id'],
                'card_id'=>$v['id'],
                'can_use'=>$v['can_use'],
                'can_no_use'=>$v['can_no_use'],
                'can_no_with'=>$v['can_no_with'],//不可用活动
                'value'=>$v['value'],
                'card_value'=>$v['value'],
                'no_rel_value'=>$v['no_rel_value'],//配置卡券==gift时候给特殊值
//                'no_rel_value'=>$v['value'],
                'act_id'=>$v['act_id'],
                'act_card_id'=>$v['act_card_id'],
                'card_code'=>$v['card_code'],
            ];
            $cards_arr[$v['card_code']] = $v;
        }

        foreach ($cards as &$new_v){

            if(isset($can_use_arr[$new_v['act_card_id']])){
                if($new_v['can_use'] && $new_v['can_use']!=='all'){
                    $new_v['can_use'] =array_unique(array_merge($new_v['can_use'],$can_use_arr[$new_v['act_card_id']]));
                }elseif(!$new_v['can_use']){
                    $new_v['can_use']=array_unique(array_merge($new_v['can_use'],$can_use_arr[$new_v['act_card_id']]));
                }
            }

            if(isset($can_no_use_arr[$new_v['act_card_id']])){
                if($new_v['can_no_use'] && $new_v['can_no_use']!=='all'){
                    $new_v['can_no_use'] =array_unique(array_merge($new_v['can_no_use'],$can_no_use_arr[$new_v['act_card_id']]));
                }elseif(!$new_v['can_no_use']){
                    $new_v['can_no_use'] =array_unique(array_merge($new_v['can_no_use'],$can_no_use_arr[$new_v['act_card_id']]));
                }
            }

            if(isset($can_no_with_arr[$new_v['act_id']])){
                if($new_v['can_no_with'] && $new_v['can_no_with']!=='all'){
                    $new_v['can_no_with'] =array_unique(array_merge($new_v['can_no_with'],$can_no_with_arr[$new_v['act_id']]));
                }elseif(!$new_v['can_no_with']){
                    $new_v['can_no_with'] =array_unique(array_merge($new_v['can_no_with'],$can_no_with_arr[$new_v['act_id']]));
                }
            }

        }

        // 预处理卡券数组
        foreach ($cards as &$card) {
            $card['card_value'] =  $card['value'];
            $card['card_id'] =  $card['id'];


            if($card['can_use']!='all' && $card['can_use']){
                $card['can_use']=array_merge($card['can_use'],[(string)$card['act_card_id']]);
            }
        }
        Logger::error('act_card_hc:',json_encode($cards));
// 生成所有有效组合
        $validCombinations = [];
//        $this->generateCombinationsRecursively($cards, 0, [], [], $validCombinations);
        $sortedCombinations = $this->generateCombinationsRecursively($cards, 0, [], [], $validCombinations);
//// 计算每个组合的总价值并排序
        usort($sortedCombinations, function($a, $b) {
            return $b['not_rel_value'] - $a['not_rel_value'];
        });
// 输出结果
        return ['best'=>$sortedCombinations[0],'list'=>$sortedCombinations];
    }

    private function generateCombinationsRecursively($cards, $start = 0, $currentCombo = [], $currentActs = [], &$result = []) {
        $combinations = [];
        $count = count($cards);

        // 遍历所有可能的组合
        for ($i = 1; $i < (1 << $count); $i++) {
            $combination = [];
            $sum = 0;
            $total_value = 0;
            $valid = true;

            // 检查当前组合是否有效
            for ($j = 0; $j < $count; $j++) {
                if ($i & (1 << $j)) {
                    // 检查新卡片是否可以与组合中的其他卡片共组
                    foreach ($combination as $existingCard) {
                        if (!$this->canCombine($cards[$j], $existingCard)) {
                            $valid = false;
                            break 2;
                        }
                    }
                    $combination[] = $cards[$j];
                    $sum += $cards[$j]['no_rel_value'];
                    $total_value += $cards[$j]['value'];
                }
            }

            if ($valid) {
                $combinations[] = [
                    'combo' => $combination,
                    'not_rel_value' => $sum,
                    'total_value' => $total_value
                ];
            }
        }

        return $combinations;

    }

    private function canCombine($card1, $card2) {
        // 检查 card1 的 can_use 规则
        if ($card1['can_use'] !== 'all') {
            if (!in_array($card2['act_card_id'], $card1['can_use']) && $card2['can_use'] !== 'all') { // todo 改这里
                return false;
            }
        }

        // 检查 card1 的 can_no_use 规则
        if ($card1['can_no_use'] === 'all') {
            return false;
        } elseif (!empty($card1['can_no_use'])) {
            if (in_array($card2['act_card_id'], $card1['can_no_use'])) {
                return false;
            }
        }

        // 检查 card1 的 can_no_with 规则
        if ($card1['can_no_with'] === 'all') {
            return false;
        } elseif (!empty($card1['can_no_with'])) {
            if (in_array($card2['act_id'], $card1['can_no_with'])) {
                return false;
            }
        }

        // 检查 card2 的 can_use 规则
        if ($card2['can_use'] !== 'all') {
            if (!in_array($card1['act_card_id'], $card2['can_use']) && $card1['can_use'] !== 'all') {
                return false;
            }
        }

        // 检查 card2 的 can_no_use 规则
        if ($card2['can_no_use'] === 'all') {
            return false;
        } elseif (!empty($card2['can_no_use'])) {
            if (in_array($card1['act_card_id'], $card2['can_no_use'])) {
                return false;
            }
        }

        // 检查 card2 的 can_no_with 规则
        if ($card2['can_no_with'] === 'all') {
            return false;
        } elseif (!empty($card2['can_no_with'])) {
            if (in_array($card1['act_id'], $card2['can_no_with'])) {
                return false;
            }
        }

        return true;
    }

// 检查组合是否有效
    private function isValidCombination($combo, $currentActs) {
        $comboIds = array_map(function($card) { return $card['act_card_id']; }, $combo);

        foreach ($combo as $card) {
            // 检查不可共用的卡券
            if ($card['can_no_use'] === 'all') {
                if (count($combo) > 1) {
                    return false;
                }
            } else {
                foreach ($card['can_no_use'] as $noUseId) {
                    if (in_array($noUseId, $comboIds)) {
                        return false;
                    }
                }
            }

            // 检查必须共用的卡券
            if (!empty($card['can_use']) && $card['can_use']!=='all') {
                foreach ($comboIds as $id) {
                    if ($id !== $card['card_id'] && !in_array($id, $card['can_use'])) {
                        return false;
                    }
                }
            }

            // 检查act层次的约束
            if ($card['can_no_with'] === 'all') {
                if (count($currentActs) > 1) {
                    return false;
                }
            } else {
                foreach ($card['can_no_with'] as $noWithActId) {
                    if (in_array($noWithActId, $currentActs)) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    public function test_mmmm_card()
    {
        $card_mo_goods_id = [];
        $order_cards = [
            ['commodity_id' => 5333, 'id' => 123456],
            ['commodity_id' => 5333, 'id' => 999999]
        ];
        foreach ($order_cards as $o_k => $o_v) {
            $card_mo_goods_id[$o_v['commodity_id']][] = $o_v['id'];
//            if(isset($card_mo_goods_id[$o_v['commodity_id']])){
//                $card_mo_goods_id[$o_v['commodity_id']]=array_merge($o_v['id'],);
//            }else{
//                $card_mo_goods_id[$o_v['commodity_id']]=$o_v['id'];
//            }
        }
        if ($card_mo_goods_id) {
            $goods_card_model = new DbCommodityCard();
            $mo_goods_id = array_keys($card_mo_goods_id);
            $mo_goods_card_id = [];
            foreach ($card_mo_goods_id as $cc_vv) {
                $mo_goods_card_id = array_merge($mo_goods_card_id, $cc_vv);
            }
//            $mo_goods_card_id =  array_values($card_mo_goods_id);
            $goods_card_arr = $goods_card_model->getList(['where' =>
                ['commodity_id' => ['in', $mo_goods_id], 'card_id' => ['in', $mo_goods_card_id]
                ]]);
            print_json($mo_goods_card_id, $goods_card_model->getLastSql(), $card_mo_goods_id);
        }
    }

    public function test_redis_arr_new()
    {
        $de_redis_name =  "seckill-user_id111";
        $de_redis =  redis($de_redis_name);
        $de_user =  explode(',',$de_redis);
        print_json($de_user);
    }

    public function test_send_jf_log()
    {
        $order_code= input('order_code','GWSM250715140245Tf3');
        $order =  new NetOrder();
        $order->orderChange($order_code);
        print_json('ok');
        $orderModel = new BuOrder();
        $userModel =  new DbUser();
        $order = $orderModel->where(['order_code' => $order_code])->find();
        if (!$order) {
            $status_code = 400;
            $response = ['msg' => '订单不存在'];
        }

        // 判断dlr_code和channel
        $dlr_code = $order['dlr_code'];
        if ($dlr_code === 'GWSM') {
            $channel = 2;
        } elseif ($dlr_code === 'GWAPP') {
            $channel = 1;
        } else {
            $status_code = 400;
            $response = ['msg' => '不支持的dlr_code'];
        }
        $user_id = $order['user_id'];
        $user = $userModel->where(['id' => $user_id])->find();
        if (!$user) {
            $status_code = 400;
            $response = ['msg' => '用户不存在'];
        }
        $one_id = $user['one_id'];
        $params = [
            'channel' => $channel,
            'oneid' => $one_id,
            'brandCode' => 1,
            'orderNo' => $order_code,
            'taskCode' => 'RW250604',
        ];

        // 调用E3s接口
        $response = E3s::create('pz1a')->triggerJifenSend($params);
        $logModel = new DbJifenSendLog();

        // 记录日志
        $logModel->insert([
            'order_code' => $order_code,
            'user_id' => $user_id,
            'status_code' => 200,
            'response' => json_encode($response, JSON_UNESCAPED_UNICODE),
        ]);
        print_json($response);
    }
    public function test_supplier_order(){
        $order_code =  input('order_code','GWSM250717091723RsQ');
        $afs_id =  input('afs_id','332');

        $data_type =  input('d_type',1);
        $supp =  new NetSupplier();
        if($data_type==1){
            $res =  $supp->pushOrderToPartner($order_code);
        }elseif($data_type==2){
            $res =  $supp->updateSupplierOrderToSupplier($order_code,2);
        }elseif($data_type==3){
            $res =  $supp->sendAfterSaleToSupplier($afs_id);
        }elseif($data_type==4){
            $res =  $supp->updateAfterSaleToSupplier($afs_id);
        }

        print_json($res);
    }

}
