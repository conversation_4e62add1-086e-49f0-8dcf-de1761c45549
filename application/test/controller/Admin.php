<?php


namespace app\test\controller;


use app\common\model\bu\BuOrderCommodity;
use app\common\model\db\DbCommodityFlat;
use app\common\validate\Goods as GoodsValidate;
use think\Model;
use think\Request;
use app\net_small\controller\Common;
use hg\apidoc\annotation as Apidoc;


/**
 * 后台需要
 * @Apidoc\Group("mall")
 */
class Admin extends Common
{
    /**
     * @Apidoc\Title("商品列表")
     * @Apidoc\Url("/net-small/admin/goods-list")
     * @Apidoc\Tag("NI+ 商品 详情")
     * @Apidoc\Method("GET")
     * @Apidoc\Author("llj")
     *
     * @Apidoc\Param("id", type="int",require=false, desc="商品ID" )
     * @Apidoc\Param("search", type="string",require=false, desc="商品名称搜索" )
     * @Apidoc\Param("sale_num", type="int",require=false, desc="销量，返回大于参数的数据" )
     * @Apidoc\Param("page", type="int",require=false, desc="页码" )
     * @Apidoc\Param("pageSize", type="int",require=false, desc="数量，默认15条" )
     *
     * @Apidoc\Returned("total", type="int",desc="总数" ),
     * @Apidoc\Returned("current_page", type="int",desc="页码" ),
     * @Apidoc\Returned("per_page", type="int",desc="分页数量" ),
     * @Apidoc\Returned("first_page_url", type="string",desc="第一页链接" ),
     * @Apidoc\Returned("prev_page_url", type="string",desc="上一页链接" ),
     * @Apidoc\Returned("next_page_url", type="string",desc="下一页链接" ),
     * @Apidoc\Returned("last_page_url", type="string",desc="最后一页链接" ),
     * @Apidoc\Returned("last_page", type="string",desc="最后一页页码" ),
     * @Apidoc\Returned("path", type="string",desc="链接地址" ),
     * @Apidoc\Returned("data", type="object",desc="商品列表",
     *     @Apidoc\Returned("commodity_id", type="int(10)",desc="商品id" ),
     *     @Apidoc\Returned("commodity_name", type="varchar(200)",desc="商品名称" ),
     *     @Apidoc\Returned("tag", type="varchar(128)",desc="标签（多个用,隔开）：1热销;2推荐;3新品;4促销;5预售;10:优惠套装;11:满优惠;12:限时优惠;13:多人拼团;14:N件N折;15:预售活动" ),
     *     @Apidoc\Returned ("tag_name",type="array/json",desc="标签数组"),
     *     @Apidoc\Returned("original_price_range_start", type="decimal(10,2)",desc="原价范围开始价格" ),
     *     @Apidoc\Returned("original_price_range_end", type="decimal(10,2)",desc="原价范围结束价格" ),
     *     @Apidoc\Returned("discount_price_range_start", type="decimal(10,2)",desc="现价范围开始价格" ),
     *     @Apidoc\Returned("discount_price_range_end", type="decimal(10,2)",desc="现价范围结束价格" ),
     *     @Apidoc\Returned("cover_image", type="varchar(200)",desc="封面图" ),
     *     @Apidoc\Returned("creator", type="varchar(20)",desc="创建人" ),
     *     @Apidoc\Returned("sale_num", type="int(10)",desc="销量" ),
     *     @Apidoc\Returned("created_date", type="datetime",desc="创建时间" )
     * )
     */
    public function getGoodsList(){}

    /**
     * @Apidoc\Title("根据id获取商品信息")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-small/admin/getGoodsForIds")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 商品")
     *
     * @Apidoc\Param("goods_ids", type="string",require=true, desc="商品id，多个用,隔开" )
     *
     * @Apidoc\Returned ("commodity_name",type="varchar(200)",desc="商品名称")
     * @Apidoc\Returned ("tag",type="varchar(128)",desc="商品标签（多个用,隔开）: 1热销;2推荐;3新品;4促销;5满减;6团购;7限时购")
     * @Apidoc\Returned ("tag_name",type="array/json",desc="标签数组"),
     * @Apidoc\Returned("original_price_range_start", type="decimal(10,2)",desc="原价范围开始价格" )
     * @Apidoc\Returned("original_price_range_end", type="decimal(10,2)",desc="原价范围结束价格" )
     * @Apidoc\Returned("discount_price_range_start", type="decimal(10,2)",desc="现价范围开始价格" )
     * @Apidoc\Returned("discount_price_range_end", type="decimal(10,2)",desc="现价范围结束价格" )
     * @Apidoc\Returned ("cover_image",type="varchar(255)",desc="封面图")
     * @Apidoc\Returned ("created_date",type="datetime",desc="创建时间")
     * @Apidoc\Returned ("card_list",type="array/json",desc="可领取卡券列表",
     *      @Apidoc\Returned ("id",type="int(11)",desc="卡券id"),
     *      @Apidoc\Returned ("card_name",type="varchar(200)",desc="卡券名称"),
     * )
     */
    public function getGoodsForIds(){}


    /**
     * @Apidoc\Title("自定义专题列表")
     * @Apidoc\Url("/net-small/admin/special-custom")
     * @Apidoc\Tag("专题")
     * @Apidoc\Desc(" APP 中的路径为 /oneapp/Special?id=")
     * @Apidoc\Method("GET")
     * @Apidoc\Author("zxtdcyy")
     *
     * @Apidoc\Param("id", type="string",require=false, desc="专题ID" )
     * @Apidoc\Param("name", type="int",require=false, desc="专题名称" )
     * @Apidoc\Param("page", type="int",require=false, desc="页码" )
     * @Apidoc\Param("pageSize", type="int",require=false, desc="数量，默认15条" )
     *
     * @Apidoc\Returned("id", type="int",desc="ID" ),
     * @Apidoc\Returned("name", type="int",desc="标题名" ),
     *
     */
    public function specialCustom(AdminValidate $validate){}

    /**
     * @Apidoc\Title("定制专题列表")
     * @Apidoc\Url("/net-small/admin/special-fixed")
     * @Apidoc\Tag("专题")
     * @Apidoc\Method("GET")
     * @Apidoc\Author("zxtdcyy")
     *
     * @Apidoc\Returned("id", type="int",desc="ID" ),
     * @Apidoc\Returned("title", type="int",desc="标题" ),
     * @Apidoc\Returned("routes", type="string",desc="专题路由地址" ),
     *
     */
    public function specialFixed(){}

    /**
     * @Apidoc\Title("获取卡券关联商品列表")
     * @Apidoc\Url("/net-small/admin/card-goods")
     * @Apidoc\Tag("卡券 商品 win")
     * @Apidoc\Method("GET")
     * @Apidoc\Author("llj")
     *
     * @Apidoc\Param("card_id", type="int",require=true, desc="速赢卡券id" )
     * @Apidoc\Param("search", type="string",require=true, desc="商品名称" )
     * @Apidoc\Param("page", type="int",require=true, desc="页码" )
     * @Apidoc\Param("pageSize", type="int",require=false, desc="每页数，默认15" )
     *
     * @Apidoc\Returned("goods_list", type="json/array",desc="商品信息",
     *      @Apidoc\Returned("total", type="int",desc="总数" ),
     *      @Apidoc\Returned("per_page", type="int",desc="每页数" ),
     *      @Apidoc\Returned("current_page", type="int",desc="当前页数" ),
     *      @Apidoc\Returned("data", type="json/array",desc="商品列表",
     *          @Apidoc\Returned("commodity_set_id", type="int",desc="商品上架id" ),
     *          @Apidoc\Returned("comm_type_id_str", type="string",desc="分类名称" ),
     *          @Apidoc\Returned("commodity_id", type="int",desc="商品id" ),
     *          @Apidoc\Returned("commodity_name", type="string",desc="商品名称" ),
     *          @Apidoc\Returned("cover_image", type="string",desc="商品封面图" ),
     *          @Apidoc\Returned("count_stock", type="int",desc="商品库存" ),
     *          @Apidoc\Returned("is_mail", type="int",desc="是否支持邮寄：1是0否" ),
     *          @Apidoc\Returned("is_shop", type="int",desc="是否商城商品：1是0否" ),
     *          @Apidoc\Returned("is_pure", type="int",desc="是否纯正商品：1是0否" ),
     *          @Apidoc\Returned("original_price_range_start", type="decimal(10,2)",desc="原价范围开始价格" ),
     *          @Apidoc\Returned("original_price_range_end", type="decimal(10,2)",desc="原价范围结束价格" ),
     *          @Apidoc\Returned("discount_price_range_start", type="decimal(10,2)",desc="现价范围开始价格" ),
     *          @Apidoc\Returned("discount_price_range_end", type="decimal(10,2)",desc="现价范围结束价格" ),
     *          @Apidoc\Returned("unit", type="string",desc="商品单位" ),
     *          @Apidoc\Returned("sort", type="int",desc="商品排序" ),
     *          @Apidoc\Returned("created_date", type="date",desc="商品发布时间" ),
     *          @Apidoc\Returned("commodity_attr_name", type="date",desc="商品属性" ),
     *          @Apidoc\Returned("commodity_class_name", type="date",desc="商品种类" ),
     *          @Apidoc\Returned("settlement_rule_id", type="int",desc="卡券中心结算规则ID" ),
     *          @Apidoc\Returned("settlement_rule_name", type="int",desc="卡券中心结算规则名称" ),
     *          @Apidoc\Returned("settlement_rule_type", type="int",desc="卡券中心结算规则类型：1固定金额 2比例结算" ),
     *          @Apidoc\Returned("settlement_rule_value", type="int",desc="结算规则值" ),
     *          @Apidoc\Returned("e3s_activity_id", type="int",desc="e3s服务活动ID" ),
     *          @Apidoc\Returned("e3s_activity_name", type="int",desc="e3s服务活动名称" ),
     *      ),
     * )
     * @Apidoc\Returned("card_row", type="json/array",desc="优惠券信息",
     *      @Apidoc\Returned("id", type="int",desc="优惠券id" ),
     *      @Apidoc\Returned("card_name", type="string",desc="卡券名称" ),
     *      @Apidoc\Returned("card_type", type="int",desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券 ; 5优惠券)" ),
     *      @Apidoc\Returned("card_quota", type="decimal(10,2)",desc="卡券金额" ),
     *      @Apidoc\Returned("card_discount", type="decimal(10,2)",desc="折扣，折扣券专用" ),
     *      @Apidoc\Returned("date_type", type="int",desc="有效期类型：1表示固定日期区间，2表示固定时长" ),
     *      @Apidoc\Returned("validity_date_start", type="date",desc="固定日期区间专用，有效期开始" ),
     *      @Apidoc\Returned("validity_date_end", type="date",desc="固定日期区间专用，有效期结束" ),
     *      @Apidoc\Returned("fixed_term", type="int",desc="固定时长专用，领取后多少天内有效，单位为天" ),
     *      @Apidoc\Returned("fixed_begin_term", type="int",desc="固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天" ),
     * )
     *
     */
    public function getCardGoodsList(){}

    /**
     * @Apidoc\Title("获取商品sku")
     * @Apidoc\Url("/net-small/admin/card-goods-sku")
     * @Apidoc\Tag("卡券 商品 win")
     * @Apidoc\Method("GET")
     * @Apidoc\Author("llj")
     *
     * @Apidoc\Param("commodity_set_id", type="int",require=true, desc="商品上架id" )
     * @Apidoc\Param("page", type="int",require=true, desc="页码" )
     * @Apidoc\Param("pageSize", type="int",require=false, desc="每页数，默认15" )
     *
     * @Apidoc\Returned("id", type="int",desc="规格id")
     * @Apidoc\Returned("sku_code", type="varchar(250)",desc="规格编码")
     * @Apidoc\Returned("price", type="decimal(10,2)",desc="默认价格")
     * @Apidoc\Returned("stock", type="int",desc="默认库存")
     * @Apidoc\Returned("sku_image", type="varchar(250)",desc="图片")
     * @Apidoc\Returned("sp_value_msg", type="string",desc="sku信息")
     */
    public function getGoodsSku(){}

    /**
     * @Apidoc\Title("BDP更新车主喜好数据")
     * @Apidoc\Desc("根据oneid传数据，有则更新无则创建")
     * @Apidoc\Author("zxtdcyy")
     * @Apidoc\Url("/net-small/admin/bdp-info")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("kafka win")
     *
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("vin",type="string", require=true,default="",desc="vin")
     * @Apidoc\Param("goods",type="string", require=true,default="",desc="推荐信息")
     *
     * @Apidoc\Returned("message", type="string",desc="数据描述")
     */
    public function bdpInfo(){}


    /**
     * @Apidoc\Title("BDP订单商品数据信息")
     * @Apidoc\Desc("传某天，获得对应到订单商品数据信息;传vin，获取vin下")
     * @Apidoc\Author("zxtdcyy")
     * @Apidoc\Url("/net-small/admin/bdp-order")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("api win")
     *
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("created_date",type="date", require=false,default="",desc="获取时间")
     * @Apidoc\Param("page",type="int", require=false,default="1",desc="页码")
     * @Apidoc\Param("vin",type="string", require=false,default="",desc="vin")
     * @Apidoc\Param("brand",type="int", require=true,default="0",desc="品牌1日产2pz3启辰")
     *
     * @Apidoc\Returned("count", type="int",desc="总数")
     * @Apidoc\Returned("current_page", type="int",desc="当前页")
     * @Apidoc\Returned("total_page", type="int",desc="所有页")
     * @Apidoc\Returned("list", type="json/array",desc="信息列表",
     *      @Apidoc\Returned("id", type="int",desc="订单商品id" ),
     *      @Apidoc\Returned("commodity_id", type="int",desc="商品id" ),
     *      @Apidoc\Returned("vin", type="string",desc="车架号" ),
     *      @Apidoc\Returned("order_code", type="string",desc="订单编码" ),
     *      @Apidoc\Returned("by_tc_cnt", type="int",desc="保养次数" ),
     *      @Apidoc\Returned("dq_time", type="datetime",desc="到期时间" ),
     *      @Apidoc\Returned("dd_commodity_type", type="int",desc="到店商品类型,9到店备件 1保养套餐-老友惠保养套 3保养套餐-心悦保养套餐4保养套餐-五年双保升级权益套餐 41保养套餐-五年双保升级权益套餐(全合成) 6保养套餐-其他 7到店代金券" ),
     *      @Apidoc\Returned("e3s_bj_type_name", type="string",desc="E3S备件类型名称" ),
     *      @Apidoc\Returned("order_afs_type", type="int",desc="订单类型，0下单1退单" ),
     *      @Apidoc\Returned("created_at", type="string",desc="下单时间" ),
     *      @Apidoc\Returned("third_sku_code", type="string",desc="sku_code 信息" ),
     * )
     */
    public function bdpOrder(){}


    /**
     * @Apidoc\Title("到店代金券查询")
     * @Apidoc\Desc("根据vin,dlr_code,order_code查用户卡券数据")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/admin/order-card")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("E3S win")
     *
     * @Apidoc\Param("dlr_code", type="string",require=true, desc="专营店编码" )
     * @Apidoc\Param("order_code",type="string", require=false,default="",desc="order_code跟vin二者至少一个不能空")
     * @Apidoc\Param("vin",type="string", require=false,default="",desc="vin跟order_code二者至少一个不能空")
     *
     * @Apidoc\Returned("list", type="json/array",desc="信息列表",
     *      @Apidoc\Returned("order_code", type="string",desc="订单编码" ),
     *      @Apidoc\Returned("order_status", type="int",desc="订单状态" ),
     *      @Apidoc\Returned("order_status_name", type="string",desc="订单状态中文名" ),
     *      @Apidoc\Returned("order_name", type="int",desc="订单名称，" ),
     *      @Apidoc\Returned("order_channel", type="int",desc="订单来源0" ),
     *      @Apidoc\Returned("order_channel_name", type="string",desc="订单来源中文名微信" ),
     *      @Apidoc\Returned("card_all_money", type="int",desc="卡券面值总额" ),
     *      @Apidoc\Returned("order_total_money", type="int",desc="订单总金额" ),
     *      @Apidoc\Returned("yh_money", type="int",desc="优惠金额" ),
     *      @Apidoc\Returned("pay_money", type="int",desc="支付现金" ),
     *      @Apidoc\Returned("point", type="int",desc="积分支付" ),
     *      @Apidoc\Returned("order_goods", type="array/json", desc="订单商品",
     *         @Apidoc\Returned("order_code", type="string", desc="订单编码" ),
     *         @Apidoc\Returned("commodity_name", type="varchar", desc="商品名称" ),
     *         @Apidoc\Returned("act_name", type="varchar", desc="活动名称" ),
     *         @Apidoc\Returned("count", type="int", desc="数量" ),
     *         @Apidoc\Returned("actual_price", type="int", desc="商品实付" ),
     *         @Apidoc\Returned("price", type="int", desc="商品原价" ),
     *         @Apidoc\Returned("card_ids", type="String", desc="卡券id" ),
     *         @Apidoc\Returned("card_names", type="String", desc="卡券名" ),
     *         @Apidoc\Returned("card_part_no", type="String", desc="备件号" ),
     *      )
     * )
     */

    public function order_card(){
    }

    /**
     * @Apidoc\Title("到店代金券核销")
     * @Apidoc\Desc("根据dlr_code,order_code核销卡券")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/admin/order-card-consume")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("E3S win")
     *
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("dlr_code", type="string",require=true, desc="专营店编码" )
     * @Apidoc\Param("order_code",type="string", require=true,default="",desc="order_code不能空")
     *
     * @Apidoc\Returned("message", type="string",desc="数据描述成功或者失败描述")
     */
    public function order_card_consume(){
    }

    /**
     * @Apidoc\Title("卡券跳转路径")
     * @Apidoc\Desc("根据dlr_code,order_code核销卡券")
     * @Apidoc\Author("zxtdcyy")
     * @Apidoc\Url("/net-small/admin/card-redirect-url")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("卡券 win")
     *
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("center_card_id", type="string",require=true, desc="卡券中心卡券ID" )
     * @Apidoc\Param("id", type="int",require=true, desc="卡券领取记录id" )
     * @Apidoc\Param("coupon_code",type="string", require=true,desc="核销码")
     * @Apidoc\Param("shop_card_id",type="int", require=true,desc="商城本地卡券ID")
     * @Apidoc\Param("received_card_id",type="int", require=true,desc="商城本地领取记录ID")
     *
     * @Apidoc\Returned("url", type="string",desc="跳转路径")
     */
    public function card_redirect_url(){
    }

    /**
     * @Apidoc\Title("卡券核销码状态+是否退款中")
     * @Apidoc\Desc("根据dlr_code,order_code核销卡券")
     * @Apidoc\Author("zxtdcyy")
     * @Apidoc\Url("/net-small/admin/user-card-status")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("卡券 win")
     *
     * @Apidoc\Param("card_code", type="string",require=true, desc="卡券核销码" )
     *
     * @Apidoc\Returned("card_status", type="int",desc="卡券状态码")
     * @Apidoc\Returned("card_status_name", type="string",desc="卡券状态")
     * @Apidoc\Returned("order_status", type="int",desc="订单状态码 卡券没使用没这个参数")
     * @Apidoc\Returned("order_status_name", type="string",desc="订单状态 卡券没使用没这个参数")
     * @Apidoc\Returned("order_after", type="int",desc="订单是否正在售后中，1：是；0：否 卡券没使用没这个参数")
     */
    public function user_card_status()
    {



    }

}
