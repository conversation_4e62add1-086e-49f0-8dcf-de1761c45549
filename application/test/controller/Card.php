<?php
/**
 * Created by PhpStorm.
 * User: llj
 * Date: 2021/8/21
 * Time: 8:51 AM
 */

namespace app\test\controller;

use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\db\DbDlr;
use app\common\model\db\DbSendCardPage;
use app\common\validate\Card as CardValidate;
use app\common\validate\Cart as CartValidate;
use think\exception\HttpResponseException;
use app\common\model\db\DbCard;
use app\common\model\bu\BuQyPoster;
use app\net_small\controller\Common;
use hg\apidoc\annotation as Apidoc;

/**
 * 优惠券
 * @Apidoc\Group("mall")
 */
class Card extends Common
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @Apidoc\Title("查看更多券")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-small/card/more-card")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("购物车 NI+ win 0214")
     * @Apidoc\ParamType("json")
     *
     *     @Apidoc\Returned("card_id",type="int(11)", require=true,desc="卡券ID"),
         * @Apidoc\Returned("card_name",type="string", require=true,desc="卡券名称"),
         * @Apidoc\Returned("card_type",type="int(11)", require=false,desc="卡券类型"),
         * @Apidoc\Returned("card_quota",type="string", require=false,desc="卡劵额度"),
         * @Apidoc\Returned("validity_date_start",type="string", require=false,desc="开始时间"),
         * @Apidoc\Returned("validity_date_end",type="string", require=false,desc="结束时间"),
         * @Apidoc\Returned("card_discount",type="string", require=false,desc="折扣，折扣券专用"),
     *
     *
     *
     */
    public function moreCart(CartValidate $validate){

    }

    /**
     * @Apidoc\Title("海报领券页")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-small/card/card-poster")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 海报 领券 优惠券")
     *
     * @Apidoc\Param("poster_id",type="int(11)", require=true,desc="海报ID")
     *
     * @Apidoc\Returned("card_list", type="array/json",desc="优惠券列表",
     *     @Apidoc\Returned("card_name", type="varchar(200)",desc="卡劵名称"),
     *     @Apidoc\Returned("card_type", type="int(2)",desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券 ; 5优惠券)"),
     *     @Apidoc\Returned("card_quota", type="decimal(10,2)",desc="卡劵额度（减免金额）"),
     *     @Apidoc\Returned("card_discount", type="decimal(10,1)",desc="折扣，折扣券专用"),
     *     @Apidoc\Returned("least_type", type="int(1)",desc="最低消费类型，1金额2指定商品"),
     *     @Apidoc\Returned("least_cost", type="varchar(20)",desc="起用金额"),
     *     @Apidoc\Returned("date_type", type="int(1)",desc="有效期类型：1表示固定日期区间，2表示固定时长"),
     *     @Apidoc\Returned("validity_date_start", type="date",desc="固定日期区间专用，有效期开始"),
     *     @Apidoc\Returned("validity_date_end", type="date",desc="固定日期区间专用，有效期结束"),
     *     @Apidoc\Returned("fixed_term", type="int",desc="固定时长专用，领取后多少天内有效，单位为天"),
     *     @Apidoc\Returned("fixed_begin_term", type="int",desc="固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天"),
     *     @Apidoc\Returned("available_count", type="int(5)",desc="可领取库存"),
     *     @Apidoc\Returned("default_detail", type="text",desc="优惠说明"),
     *     @Apidoc\Returned("use_des", type="text",desc="使用须知，字数上限为1024个汉字。"),
     *     @Apidoc\Returned("apply_des", type="varchar",desc="适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内"),
     *     @Apidoc\Returned("not_apply_des", type="varchar",desc="不适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内"),
     *     @Apidoc\Returned("can_num", type="int(2)",desc="可领取数量"),
     *     @Apidoc\Returned("word", type="varchar(200)",desc="使用条件"),
     *     @Apidoc\Returned("dlr_name", type="varchar(200)",desc="适用门店"),
     * )
     * @Apidoc\Returned("poster_img", type="varchar(255)",desc="海报图片")
     * @Apidoc\Returned("poster_id", type="int(11)",desc="海报id")
     * @Apidoc\Returned("act_code", type="varchar(255)",desc="活动编码用于区别重复领取")
     *
     */
    public function getCardPoster(CardValidate $validate){}

    /**
     * @Apidoc\Title("领券列表页")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-small/card/card-list-by-point-code")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 海报 商城首页领券 优惠券")
     *
     * @Apidoc\Param("poster_id",type="int(11)", require=false,desc="海报ID")
     * @Apidoc\Param("page",type="int(11)", require=false,desc="页码")
     * @Apidoc\Param("page_size",type="int(11)", require=false,desc="每页总数")
     * @Apidoc\Param("point_code",type="String", require=false,desc="领券点位")
     * @Apidoc\Param("status",type="int(11)", require=false,desc="0全部1可领券，2已领券")
     *
     * @Apidoc\Returned("card_list", type="array/json",desc="优惠券列表",
     *     @Apidoc\Returned("card_name", type="varchar(200)",desc="卡劵名称"),
     *     @Apidoc\Returned("card_type", type="int(2)",desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券 ; 5优惠券)"),
     *     @Apidoc\Returned("card_quota", type="decimal(10,2)",desc="卡劵额度（减免金额）"),
     *     @Apidoc\Returned("card_discount", type="decimal(10,1)",desc="折扣，折扣券专用"),
     *     @Apidoc\Returned("least_type", type="int(1)",desc="最低消费类型，1金额2指定商品"),
     *     @Apidoc\Returned("least_cost", type="varchar(20)",desc="起用金额"),
     *     @Apidoc\Returned("date_type", type="int(1)",desc="有效期类型：1表示固定日期区间，2表示固定时长"),
     *     @Apidoc\Returned("validity_date_start", type="date",desc="固定日期区间专用，有效期开始"),
     *     @Apidoc\Returned("validity_date_end", type="date",desc="固定日期区间专用，有效期结束"),
     *     @Apidoc\Returned("fixed_term", type="int",desc="固定时长专用，领取后多少天内有效，单位为天"),
     *     @Apidoc\Returned("fixed_begin_term", type="int",desc="固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天"),
     *     @Apidoc\Returned("available_count", type="int(5)",desc="可领取总库存"),
     *     @Apidoc\Returned("default_detail", type="text",desc="优惠说明"),
     *     @Apidoc\Returned("use_des", type="text",desc="使用须知，字数上限为1024个汉字。"),
     *     @Apidoc\Returned("apply_des", type="varchar",desc="适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内"),
     *     @Apidoc\Returned("not_apply_des", type="varchar",desc="不适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内"),
     *     @Apidoc\Returned("can_num", type="int(2)",desc="可领取数量"),
     *     @Apidoc\Returned("word", type="varchar(200)",desc="使用条件"),
     *     @Apidoc\Returned("dlr_name", type="varchar(200)",desc="适用门店"),
     *     @Apidoc\Returned("can_use_card", type="int(2)",desc="领取数量 为0表示不可领取"),
     *     @Apidoc\Returned("available_quantity", type="int(2)",desc="可领取数"),
     *     @Apidoc\Returned("is_can_receive",  type="int(2)",desc="1可领取 0不可领取"),
     *     @Apidoc\Returned("available_quantity",  type="int(11)",desc="可领取数量"),
     *     @Apidoc\Returned("change_car",  type="int(11)",desc="1显示切换车辆0不显示切换车辆"),
     *     @Apidoc\Returned("is_received",  type="int(11)",desc="卡券是否领取过 1 已领取 0 未领取"),
     *     @Apidoc\Returned("gift_card_goods_ids", type="varchar(255)",desc="主品商品id"),
     *     @Apidoc\Returned("status", type="int(11)",desc="状态 5-冻结"),
     *     @Apidoc\Returned("freeze_type", type="int(11)",desc="冻结类型 0-无冻结 1-支付冻结  2-退款冻结"),
     *     @Apidoc\Returned("freeze_order_id", type="int(11)",desc="order_id"),
     *     @Apidoc\Returned("freeze_order_code", type="varchar(50)",desc="冻结订单号"),
     * )
     * @Apidoc\Returned("poster_img", type="varchar(255)",desc="海报图片")
     * @Apidoc\Returned("poster_id", type="int(11)",desc="海报id")
     * @Apidoc\Returned("act_code", type="varchar(255)",desc="活动编码用于区别重复领取")
     *
     */
    public function getCardListByPointCode(CardValidate $validate){}

    /**
     * @Apidoc\Title("助力页")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-small/card/help-msg")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 发起 助力")
     *
     * @Apidoc\Param("help_id",type="int(11)", require=false,desc="助力页id，从分享连接进来必传")
     * @Apidoc\Param("nick_name",type="varchar(100)", require=true,desc="用户昵称")
     * @Apidoc\Param("head_img",type="varchar(255)", require=true,desc="用户头像")
     *
     * @Apidoc\Returned("is_help", type="int(1)",desc="用户身份：0当前助力页发起人，1助力人")
     * @Apidoc\Returned("status", type="int(1)",desc="助力状态：1助力中，2助力完成（发起人已领券），3当前用户已经为此助力页助力过，4当前用户助力次数已达到上限，5活动未开始，6活动已结束，7非车主")
     * @Apidoc\Returned("initiator", type="object",desc="助力页发起人信息(无数据的时候为null)",
     *     @Apidoc\Returned("id", type="int(11)",desc="助力id"),
     *     @Apidoc\Returned("user_id", type="int(11)",desc="发起人用户id"),
     *     @Apidoc\Returned("nick_name", type="varchar(100)",desc="发起人昵称"),
     *     @Apidoc\Returned("head_img", type="varchar(255)",desc="发起人头像"),
     *     @Apidoc\Returned("card_id", type="int(11)",desc="卡券id"),
     * )
     * @Apidoc\Returned("help_list", type="object",desc="助力人列表(无数据的时候为null)",
     *     @Apidoc\Returned("user_id", type="int(11)",desc="助力人用户id"),
     *     @Apidoc\Returned("nick_name", type="varchar(100)",desc="助力人昵称"),
     *     @Apidoc\Returned("head_img", type="varchar(255)",desc="助力人头像"),
     * )
     */
    public function helpMsg(CardValidate $validate){}

    /**
     * @Apidoc\Title("助力")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-small/card/help-init")
     * @Apidoc\Method("PUT")
     * @Apidoc\Tag("NI+ 助力")
     *
     * @Apidoc\Param("help_id",type="int(11)", require=true,desc="助力页id，助力必传，不传则为发起助力")
     * @Apidoc\Param("nick_name",type="varchar(100)", require=true,desc="用户昵称")
     * @Apidoc\Param("head_img",type="varchar(255)", require=true,desc="用户头像")
     *
     * @Apidoc\Returned("", type="msg",desc="提示信息")
     */
    public function helpInit(CardValidate $validate){}

    /**
     * @Apidoc\Title("助力状态")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-small/card/help-status")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("助力 状态")
     *
     * @Apidoc\Returned("status",type="int(1)", require=true,desc="助力状态：1未完成；2已完成")
     * @Apidoc\Returned("card_id",type="int(11)", require=true,desc="卡券id")
     *
     */
    public function getHelpStatus(CardValidate $validate){}

    /**
     * @Apidoc\Title("获取卡券激活跳转信息")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/card/activation-jump")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("卡券 NI+ 待激活券")
     * @Apidoc\ParamType("json")
     *
     * @Apidoc\Param("card_id", type="int", require=true, desc="卡券ID")
     * @Apidoc\Param("card_code", type="varchar", require=true, desc="领券code")
     * @Apidoc\Param("scene_code", type="string", require=false, desc="激活场景代码，不传则使用第一个可用场景")
     *
     * @Apidoc\Returned("jump_type", type="string", desc="跳转类型：order_payment-订单支付，order_consume-订单核销，immediate_purchase-立即购买，goods_detail-商品详情，goods_list-商品列表，order_list-订单列表")
     * @Apidoc\Returned("jump_url", type="string", desc="跳转URL")
     * @Apidoc\Returned("jump_data", type="array", desc="跳转数据，商品列表或订单列表")
     * @Apidoc\Returned("scene_name", type="string", desc="场景名称")
     *
     * @Apidoc\Returned("jump_data", type="array", desc="跳转数据详情 当类型是商品的时候，直接将数组转,隔开跳转即可",
     *      @Apidoc\Returned("commodity_id", type="int", desc="商品ID（商品列表时--暂时忽略）"),
     *      @Apidoc\Returned("commodity_name", type="string", desc="商品名称（商品列表时--暂时忽略）"),
     *      @Apidoc\Returned("cover_image", type="string", desc="商品封面图（商品列表时--暂时忽略）"),
     *      @Apidoc\Returned("order_id", type="int", desc="订单ID（订单列表时）"),
     *      @Apidoc\Returned("order_code", type="string", desc="订单编号（订单列表时）"),
     *      @Apidoc\Returned("commodity_pic", type="string", desc="订单商品图"),
     *      @Apidoc\Returned("total_price", type="string", desc="订单总价（订单列表时）")
     * )
     */
    public function getCardActivationJump()
    {}

}
