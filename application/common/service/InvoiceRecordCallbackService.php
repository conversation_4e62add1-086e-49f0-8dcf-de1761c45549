<?php


namespace app\common\service;


use app\common\model\db\DbOrderInvoice;
use app\common\model\db\InvoiceRecordModel;
use app\common\port\connectors\InvoiceHX;
use app\common\return_data\JsonBuilder;
use app\common\return_data\MessageBag;
use app\admin_v2\service\InvoiceRecordService;
use app\admin_v2\service\InvoiceApplyService;
use think\Exception;

class InvoiceRecordCallbackService
{

    private $messageBag;
    public function __construct()
    {
        $this->messageBag     = new MessageBag();
    }


    /**
     * 发票回调处理
     * @param $params
     * @return MessageBag
     */
    public function sendInvoiceCallbackProcess($params)
    {

        try {
            if (empty($params['invoiceId'] ?? '')) {
                $this->messageBag->setCode(JsonBuilder::CODE_ERROR);
                $this->messageBag->setMessage('流水号为空');
                return $this->messageBag;

            }
            //1:开票成功、 2:开票失败、3:作废成功、4:作废失败；5:开票失败删除成功  // todo  详情接口没有这个字段
            if (!in_array($params['invoiceStatus'] ?? 0, [1, 2, 3, 4, 5])) {
                $this->messageBag->setCode(JsonBuilder::CODE_ERROR);
                $this->messageBag->setMessage('发票状态不正确');
                return $this->messageBag;

            }
            //2红字发票, 1蓝字发票 // todo  详情接口没有这个字段
            if (!in_array($params['invoiceType'] ?? 0, [1, 2])) {
                $this->messageBag->setCode(JsonBuilder::CODE_ERROR);
                $this->messageBag->setMessage('发票类型不正确');
                return $this->messageBag;

            }

            if ($params['invoiceType'] == 1) {
                // 蓝票
                $_where = [
                    'order_no'       => $params['orderNo'],
                    'invoice_data_status' => InvoiceRecordModel::INVOICE_REQUEST_WAITING,
                ];
                $invoice = '蓝票';
            } elseif ($params['invoiceType'] == 2) {
                // 红票 回调
                $_where = [
                    'order_no'       => $params['oriOrderNo'], // 详情接口没有这个字段
                    'red_invoice_data_status' => InvoiceRecordModel::INVOICE_REQUEST_WAITING, //
                ];
                $invoice = '红票';
            } else {
                // 红票 定时任务
                $_where = [
                    'red_invoice_code'       => $params['red_invoice_code'], // 详情接口没有这个字段
                    'red_invoice_data_status' => InvoiceRecordModel::INVOICE_REQUEST_WAITING, //
                ];
                $invoice = '红票';
            }

            $record = InvoiceRecordModel::where($_where)->order('id desc')->find();
            if (empty($record)) {
                $this->messageBag->setCode(JsonBuilder::CODE_ERROR);
                $this->messageBag->setMessage($invoice.'不存在');
                return $this->messageBag;
            }
            $data_update = [];

            $data_update['fault_message'] = json_encode_cn($params['errorMessage']);

            switch ($params['invoiceStatus']) {
                case 1:
                    // 1开票成功
                    //开票类型，1:蓝票；2:红票
                    if ($params['invoiceType'] == 1) {
                        if (empty($record->invoice_number)) {
                            //1蓝票处理
                            $data_update['invoice_status'] = InvoiceRecordModel::INVOICE_SUCCESS;
                            $data_update['invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_SUCCESS;
                            $data_update['message'] = json_encode_cn($params['errorMessage']);

                            $data_update['invoice_code'] = $params['invoiceCode'];
                            $data_update['invoice_number'] = $params['invoiceNumber'];

                            $data_update['invoice_date'] = date("Y-m-d H:i:s", $params['invoiceTime'] / 1000);
                            $data_update['els_code'] = $params['allElectronicInvoiceNumber'];
                            $data_update['image_url'] = $params['imageUrl'];
                            $data_update['pdf_url'] = $params['pdfUrl'];
                            $data_update['ofd_url'] = $params['ofdUrl'];
                            $data_update['picture_url'] = $params['pictureUrl'];

                            $data_update['last_updated_date'] = date('Y-m-d H:i:s');

                            $record->where('id', '=', $record->id)->update($data_update);


                            DbOrderInvoice::updateOrderInvoiceStatus($record->relation_good_order_no, InvoiceRecordService::getInstance()->getInvoiceStatusByStatus($record->invoice_status), $record->invoice_date);
                        }
                    }
                    if ($params['invoiceType'] == 2) {

                        if (empty($record->red_invoice_number)) {
                            //红票处理
                            $data_update['is_red_invoice'] = 1;

                            $data_update['invoice_status'] = InvoiceRecordModel::INVOICE_RED_SUCCESS;

                            $data_update['red_invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_SUCCESS;

                            $data_update['red_message'] = json_encode_cn($params['errorMessage']);

                            $data_update['red_invoice_code'] = $params['invoiceCode'];
                            $data_update['red_invoice_number'] = $params['invoiceNumber'];

                            //红字申请表编号
                            $data_update['red_billinfo_no'] = $params['billInfoNo'] ?? '';

                            $data_update['red_invoice_date'] = date("Y-m-d H:i:s", ($params['invoiceTime']) / 1000);
                            $data_update['red_els_code'] = $params['allElectronicInvoiceNumber'];
                            $data_update['red_image_url'] = $params['imageUrl'];
                            $data_update['red_pdf_url'] = $params['pdfUrl'];
                            $data_update['red_ofd_url'] = $params['ofdUrl'];
                            $data_update['red_picture_url'] = $params['pictureUrl'];

                            // 新增维护
                            $data_update['red_bill_status'] = 5; // 已开具

                            $record->where('id', '=', $record->id)->update($data_update);

                            if ($record->after_red_need_blue == 1) {
                                //换开数据时
                                //开红成功后，需要激活蓝票开票
                                InvoiceApplyService::getInstance()->submbitApplyByFromId($record->apply_id, $message);
                            }

                            DbOrderInvoice::updateOrderInvoiceStatus($record->relation_good_order_no, InvoiceRecordService::getInstance()->getInvoiceStatusByStatus($record->invoice_status));
                        }

                    }
                    break;

                case 2:
                    //开票失败
                    if ($params['invoiceType'] == 1) {
                        $data_update['invoice_status'] = InvoiceRecordModel::INVOICE_FAIL;
                        $data_update['invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_FAIL;

                        $data_update['message'] = json_encode_cn($params['errorMessage']);
                        $record->where('id', '=', $record->id)->update($data_update);

                        DbOrderInvoice::updateOrderInvoiceStatus($record->relation_good_order_no, InvoiceRecordService::getInstance()->getInvoiceStatusByStatus($record->invoice_status), $record->invoice_date);

                    }
                    if ($params['invoiceType'] == 2) {

                        $data_update['invoice_status'] = InvoiceRecordModel::INVOICE_RED_FAIL;
                        $data_update['red_invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_FAIL;

                        $data_update['red_message'] = json_encode_cn($params['errorMessage']);
                        $record->update($data_update);

                        DbOrderInvoice::updateOrderInvoiceStatus($record->relation_good_order_no, InvoiceRecordService::getInstance()->getInvoiceStatusByStatus($record->invoice_status), $record->invoice_date);

                    }

                    break;
                case 3:
                    // 3作废成功
                    break;

                case 4:
                    // 4作废失败
                    break;

                case 5:
                    // 5开票失败删除成功
                    break;

                default:
                    // 可选：处理其他状态或提供错误处理
                    break;
            }
            $this->messageBag->setMessage('业务方接收同步成功');
            return $this->messageBag;
        } catch (\Exception $e) {
            $this->messageBag->setCode(JsonBuilder::CODE_ERROR);
            $this->messageBag->setMessage($e->getMessage());
            return $this->messageBag;
        }

    }




    /**
     * 红字确认单
     * @param $params
     * @return MessageBag
     */
    public function runSendRedInvoiceBackType2($params)
    {
        try {

            $_where = [
                'red_invoice_id' => $params['billId']
            ];
            $record = InvoiceRecordModel::where($_where)->find();
            if (empty($record)) {
                $this->messageBag->setCode(JsonBuilder::CODE_ERROR);
                $this->messageBag->setMessage('发票不存在');
                return $this->messageBag;
            }
            $data_update = [];
            //确认单状态:-1 提交失败,00 提交中,01 无需确认02 销方录入待购方确认,03 购方录入待销方确认,04 购销双方已确认,
            // 05 作废（销方录入购方否认）,06 作废（购方录入销方否认）,07 作废（超72小时未确认）,
            // 08 作废（发起方撤销）,09 作废（确认方撤销）,10作废（异常凭证）,11作废（纳税人状态异常阻断）,15 提交税局中
            // 16 提交税局失败
            if (in_array($params['billStatus'], ["01", "04"])) {
                $data_update['red_message'] = "确认单状态:" . $params['billStatus'];

                $data_update['red_bill_status'] = $params['billStatus'];
                $data_update['red_billInfo_no'] = $params['billInfoNo'] ?? '';
                $data_update['red_record_date'] = date('Y-m-d H:i:s');

                $record->where('id', '=', $record->id)->update($data_update);
                //		开具状态0：未开具,1：已开具
                if ($params['openStatus'] == 0) {
                    //未开时，请求开
                    $this->fastRepeatedRedSingle($params);
                }
            } elseif ($params['billStatus'] == '16') {
                $data_update = [
                    'red_invoice_data_status' => InvoiceRecordModel::INVOICE_REQUEST_FAIL,
                    'red_bill_status'         => 4,
                    'red_message'             => $params['billMessage'],
                    'invoice_status'          => InvoiceRecordModel::INVOICE_RED_FAIL,
                ];
                $record->where('id', '=', $record->id)->update($data_update);

            }
            $this->messageBag->setMessage('业务方接收同步成功');
            return $this->messageBag;
        } catch (Exception $e) {
            $msg = $e->getMessage();
            $this->messageBag->setCode(JsonBuilder::CODE_ERROR);
            $this->messageBag->setMessage($msg);
            return $this->messageBag;
        }



    }




    /**
     * 快速冲红接口
     * @param $params
     * @return array|mixed|string
     */
    public function fastRepeatedRedSingle($params)
    {
        $callbackUrl = config('invoice_callback_url') .'net_small/invoice_callback/sendInvoiceCallback';

        $invoice_params = [
            'sellerTaxnum' => $params['sellerTaxnum'] ?? '',
            'companyCode' => '',
            'invoiceId' => '',
            'invoiceCode' => '',
            'invoiceNumber' => '',
            'billId' => $params['billId'] ?? '',
            'billNo' => $params['billNo'] ?? '',
            'billUuid' => $params['billUuid'] ?? '',
            'fastRedType' => 1,//快捷冲红类型 不填或者0为普通快捷冲红 1:数电发票快捷冲红(数电发票必传1)
            'invoiceLine' => $params['blueInvoiceLine'] ?? '',
            'orderNo' => $params['orderNo'] ?? '',
            'bizCallbackUrl' => $callbackUrl,
            'paperInvoiceType' => '',
            'columnFirst' =>'NI+商城',
            'columnSecond' => '',
            'columnThree' => '',
        ];
        return InvoiceHX::create('invoice_hx')->fastRepeatedRedSingle($invoice_params);
    }

}