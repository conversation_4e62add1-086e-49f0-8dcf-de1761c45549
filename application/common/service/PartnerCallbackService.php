<?php
/**
 * 合作伙伴回调服务
 * @author: AI Assistant
 * @time: 2025-01-21
 */

namespace app\common\service;

use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\inter\IPartnerCallbackLog;
use tool\Logger;
use think\Db;

class PartnerCallbackService
{
    /**
     * 处理合作伙伴发货回调
     * @param array $callbackData 回调数据
     * @return array
     */
    public function handleDeliveryCallback($callbackData)
    {
        $result = [
            'success' => false,
            'message' => '',
            'data' => []
        ];

        $logId = null;

        try {
            // 记录回调日志
            $logId = $this->logCallback('partner_delivery_callback', $callbackData);

            // 验证必要参数
            $validation = $this->validateCallbackData($callbackData);
            if (!$validation['valid']) {
                $result['message'] = $validation['message'];
                return $result;
            }

            $orderDetailNo = $callbackData['orderDetailNo'];
            $deliveryList = $callbackData['deliveryList'];

            // 开启事务
            Db::startTrans();

            try {
                // 根据order_detail_no查找订单
                $orderModel = new BuOrder();
                $order = $orderModel->getOne([
                    'where' => ['order_code' => $orderDetailNo],
                    'field' => 'id,order_code,order_status,dd_dlr_code'
                ]);

                if (empty($order)) {
                    throw new \Exception("订单不存在: {$orderDetailNo}");
                }

                // 检查是否为等待发货状态
                $isWaitDelivery = false;
                foreach ($deliveryList as $delivery) {
                    if (isset($delivery['deliveryStatus']) && $delivery['deliveryStatus'] === 'WAIT_DELIVERY') {
                        $isWaitDelivery = true;
                        break;
                    }
                }

                if ($isWaitDelivery) {
                    // 如果是等待发货状态，只更新订单状态为11
                    $orderUpdateData = [
                        'order_status' => 11, // 等待发货
                        'last_updated_date' => date('Y-m-d H:i:s')
                    ];
                    $orderModel->saveData($orderUpdateData, ['order_code' => $orderDetailNo]);
                } else {
                    // 正常发货流程
                    // 更新订单状态为已发货
                    $orderUpdateData = [
                        'order_status' => 4, // 已发货
                        'last_updated_date' => date('Y-m-d H:i:s')
                    ];

                    // 处理物流信息
                    foreach ($deliveryList as $delivery) {
                        if (!empty($delivery['deliveryTime'])) {
                            $orderUpdateData['delivery_time'] = $delivery['deliveryTime'];
                        }
                        if (!empty($delivery['logisticsCompanyName'])) {
                            $orderUpdateData['common_carrier'] = $delivery['logisticsCompanyName'];
                        }
                        if (!empty($delivery['logisticsNo'])) {
                            $orderUpdateData['waybill_number'] = $delivery['logisticsNo'];
                        }
                        break; // 只取第一个物流信息更新到订单主表
                    }

                    $orderModel->saveData($orderUpdateData, ['order_code' => $orderDetailNo]);

                    // 更新订单商品状态
                    $orderCommodityModel = new BuOrderCommodity();
                    
                    foreach ($deliveryList as $delivery) {
                        $commodityList = $delivery['commodityList'] ?? [];
                        
                        foreach ($commodityList as $commodity) {
                            $orderCommodityId = $commodity['orderCommodityId'];
                            
                            // 更新商品订单状态
                            $commodityUpdateData = [
                                'order_commodity_status' => 4, // 已发货
                                'last_updated_date' => date('Y-m-d H:i:s')
                            ];

                            // 更新物流信息
                            if (!empty($delivery['deliveryTime'])) {
                                $commodityUpdateData['delivery_time'] = $delivery['deliveryTime'];
                            }
                            if (!empty($delivery['logisticsCompanyName'])) {
                                $commodityUpdateData['common_carrier'] = $delivery['logisticsCompanyName'];
                            }
                            if (!empty($delivery['logisticsNo'])) {
                                $commodityUpdateData['waybill_number'] = $delivery['logisticsNo'];
                            }
                            // delivery_status字段在order_commodity表中不存在，暂时注释
                            // if (!empty($delivery['deliveryStatus'])) {
                            //     $commodityUpdateData['delivery_status'] = $delivery['deliveryStatus'];
                            // }

                            $orderCommodityModel->saveData($commodityUpdateData, ['id' => $orderCommodityId]);
                        }
                    }
                }

                // 提交事务
                Db::commit();

                $result['success'] = true;
                $result['message'] = '回调处理成功';
                $result['data'] = [
                    'order_code' => $orderDetailNo,
                    'updated_order_status' => $isWaitDelivery ? 11 : 4,
                    'updated_commodities' => $isWaitDelivery ? 0 : count($deliveryList)
                ];

                Logger::info('合作伙伴发货回调处理成功', [
                    'order_code' => $orderDetailNo,
                    'delivery_count' => count($deliveryList)
                ]);

                // 更新日志状态为成功
                if ($logId) {
                    $logModel = new IPartnerCallbackLog();
                    $logModel->updateProcessResult($logId, [
                        'process_status' => 1,
                        'response_data' => json_encode($result, JSON_UNESCAPED_UNICODE)
                    ]);
                }

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            $result['message'] = '回调处理失败: ' . $e->getMessage();
            Logger::error('合作伙伴发货回调处理失败', [
                'error' => $e->getMessage(),
                'callback_data' => $callbackData
            ]);

            // 更新日志状态为失败
            if ($logId) {
                $logModel = new IPartnerCallbackLog();
                $logModel->updateProcessResult($logId, [
                    'process_status' => 2,
                    'error_message' => $e->getMessage(),
                    'response_data' => json_encode($result, JSON_UNESCAPED_UNICODE)
                ]);
            }
        }

        return $result;
    }

    /**
     * 验证回调数据
     * @param array $data
     * @return array
     */
    private function validateCallbackData($data)
    {
        $result = ['valid' => true, 'message' => ''];

        // 验证必要字段
        if (empty($data['orderDetailNo'])) {
            $result['valid'] = false;
            $result['message'] = '缺少订单详情编号(orderDetailNo)';
            return $result;
        }

        if (empty($data['deliveryList']) || !is_array($data['deliveryList'])) {
            $result['valid'] = false;
            $result['message'] = '缺少发货列表(deliveryList)或格式错误';
            return $result;
        }

        // 验证发货列表中的必要字段
        foreach ($data['deliveryList'] as $index => $delivery) {
            if (empty($delivery['commodityList']) || !is_array($delivery['commodityList'])) {
                $result['valid'] = false;
                $result['message'] = "发货列表第{$index}项缺少商品列表(commodityList)或格式错误";
                return $result;
            }

            foreach ($delivery['commodityList'] as $commodityIndex => $commodity) {
                if (empty($commodity['orderCommodityId'])) {
                    $result['valid'] = false;
                    $result['message'] = "发货列表第{$index}项商品列表第{$commodityIndex}项缺少订单商品ID(orderCommodityId)";
                    return $result;
                }
            }
        }

        return $result;
    }

    /**
     * 记录回调日志
     * @param string $type
     * @param array $data
     * @return int|null
     */
    private function logCallback($type, $data)
    {
        try {
            $logModel = new IPartnerCallbackLog();
            $logData = [
                'callback_type' => $type,
                'order_detail_no' => $data['orderDetailNo'] ?? '',
                'request_url' => '/partner/delivery/callback',
                'request_method' => 'POST',
                'request_params' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'process_status' => 0, // 处理中
                'remark' => '合作伙伴发货回调'
            ];
            return $logModel->logCallback($logData);
        } catch (\Exception $e) {
            Logger::error('记录回调日志失败', ['error' => $e->getMessage()]);
            return null;
        }
    }
}
