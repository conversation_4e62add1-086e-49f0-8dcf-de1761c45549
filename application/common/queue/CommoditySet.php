<?php


namespace app\common\queue;


use app\common\model\db\DbCommoditySet;
use think\Exception;
use think\Hook;
use think\Log;
use think\queue\Job;

class CommoditySet extends Base
{
    public function fire(Job $job, $data)
    {
        trace('500,start commodity set queue');
        try {
            $data = json_decode($data, true);
            if (!empty($data)) {
                $type = $data['type'];
                $item = $data['data'];
                if ($type == 1) {
                    $this->commodity_flat($item);
                }
            }
            $job->delete();
        } catch (Exception $e) {
            Log::error('queue_commodity_set : ' . $e->getMessage());
            $job->attempts() > $this->_attempts ? $job->delete() : $job->release($this->_release_time);
        }
        trace('500,end commodity set queue');
    }

    public function commodity_flat($id)
    {
        $model = new DbCommoditySet();
        $list = $model->where(['id'=>array('in',$id)])->select();
        foreach ($list as $value)
        {
            $this->doHook('edit',$value);
        }
    }

    private function doHook($type = 'add', $param = [])
    {
        Hook::import(require ROOT_PATH . 'config/tags.php');
        $result = true;
        $param['modifier'] = 'commodity_set';
        switch ($type) {
            case 'add':
                # 添加商品不需要刷活动信息
                Hook::listen('flat_item', $param);
                break;
            case 'delete':
                # 删除就只需要将数据删除就ok
                $param = array_merge($param, ['is_delete' => true]);
                Hook::listen('flat_item', $param);
                break;
            case 'edit':
                # 重新上架需要刷新商品活动信息
                Hook::listen('flat', $param);
                break;
        }
        $detail_param = ['key' => 'cache_prefix.commodity_detail', 'suffix' => '', 'set' => 'cache_prefix.commodity_detail_set'];
        Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $detail_param);
        // 清除商品分类缓存
        $live_param = ['key' => 'cache_prefix.catalog', 'suffix' => '', 'set' => 'cache_prefix.goods_class_set'];
        Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $live_param);
        return $result;

    }
}