<?php


namespace app\common\queue;


use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityDepreciateMsg;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommoditySku;
use app\common\model\e3s\E3sCarSeries;
use app\common\model\e3s\E3sDelayInsurance;
use app\common\model\e3s\E3sPackage;
use app\common\model\e3s\E3sPartCarSeries;
use app\common\model\e3s\E3sSparePart;
use app\common\model\e3s\E3sSpecificRelationPart;
use think\Db;
use think\Exception;
use think\Log;
use think\Model;
use think\queue\Job;
use tool\Logger;

class CommoditySku extends Base
{
    /**
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data)
    {
        trace('500,start commodity sku kafka');
        $this->logDb([
            'queue'       => 'commodity_sku',
            'source_type' => 'commodity_sku',
            'data_info'   => json_encode($data),
        ]);
        $data = json_decode($data, true);
        $admin_name = $data['admin_name'] ?? '';
        set_time_limit(0);

        try {
            if($data['type'] == 9){
                try {
                    $sku = $data['sku'];
                    $sku_list = $data['sku_list'];
                    $commodity_id = $data['commodity_id'];
                    $this->part_commodity_sku($sku_list,$commodity_id,$sku,$admin_name);
                }catch (Exception $e){
                    Log::error('queue_commodity_set_sku : ' . $e->getMessage());
                }

            }elseif ($data['type'] == 2){
                try {
                    $sku_list = $data['sku_list'];
                    $comm_set_id = $data['comm_set_id'];
                    $admin_info = $data['admin_info'];
                    $this->commodity_set_sku($sku_list,$comm_set_id,$admin_info);
                }catch (Exception $e){
                    Log::error('queue_commodity_set_sku : ' . $e->getMessage());
                }

            }elseif ($data['type'] == 3) {
                try {
                    $sku_list         = $data['sku_list'];
                    $admin_info       = $data['admin_info'];
                    $commodity_set_id = $data['commodity_set_id'];
                    $this->re_commodity_set_sku($sku_list, $admin_info, $commodity_set_id);
                } catch (Exception $e) {
                    Log::error('queue_re_commodity_set_sku : ' . $e->getMessage());
                }
            } elseif ($data['type'] == 10) {
                // 延保服务包
                try {
                    $sku = $data['sku'];
                    $sku_code = $data['sku_list'];
                    $commodity_id = $data['commodity_id'];
                    $this->yan_bao_commodity_sku($sku_code,$commodity_id,$sku,$admin_name);
                }catch (Exception $e){
                    Log::error('queue_commodity_set_sku : ' . $e->getMessage());
                }

            } else {
                $sku_list = $data['sku_list'];
                $commodity_id = $data['commodity_id'];
                $commodity = new DbCommodity();
                $res = $commodity->where(['id'=>$commodity_id])->find();
                //套餐变商品才跑队列
                if(in_array($res['dd_commodity_type'],[1,3,4,12])){
                    $commodity->allowField(true)->isUpdate(true)->save(['is_update'=>0], ['id' => $commodity_id]);
                    $this->in_store($sku_list,$commodity_id,$res['dd_commodity_type']);
                }
            }

            $job->delete();
        } catch (\Exception $e) {
            Log::error('queue_commodity_sku : ' . $e->getMessage());
            $job->attempts() > $this->_attempts ? $job->delete() : $job->release($this->_release_time);
        }

        trace('500,commodity sku kafka');
    }


    private function in_store($sku_list,$id,$dd_commodity_type){
        $commodity = new DbCommodity();
        $e3s_main_pack = new E3sPackage();
        $commodity_sku = new DbCommoditySku();
        $data = [];
        $i = 0;
        //执行删除sku操作
        try {
            //根据商品id删除原先的sku
            foreach ($sku_list as $value){
                $i ++;
                $sku_code_e3s = $e3s_main_pack->where(['maintain_group_code'=>array('in',$value['sku_code']),'is_enable'=>1])->field('maintain_group_code')->select();
                if(empty($sku_code_e3s)){
                    $value['sku_code'] = '';
                }else{
                    $e3s_sku = [];
                    foreach ($sku_code_e3s as $v){
                        $e3s_sku[] = $v['maintain_group_code'];
                    }
                    $value['sku_code'] = implode(',', $e3s_sku);
                }
                $maintain_q = $e3s_main_pack->where(['maintain_group_code'=>array('in',$value['sku_code']),'is_enable'=>1])
                    ->field('discount,maintain_total_count,product_type_name,maintain_group_code')
                    ->find();
                $maintain_total_count = '';
                $discount = '';
                $oli_liters = '';
                if(!empty($maintain_q)){
                    $discount = $maintain_q['discount']*10;
                    $maintain_total_count = $maintain_q['maintain_total_count'];
                    $oli_liters = $this->oli_liters($maintain_q['maintain_group_code']);
                }
                $sku_relate_car_work_hour = '';
                $sku_bj_type_name = '';
                if($dd_commodity_type != 4){
                    $relate_car  = $this->findCarType($value['sku_code'],1);
                    if(isset($relate_car['n_18'])){
                        $relate_car_18n = implode(',', $relate_car['n_18']);
                    }else{
                        $relate_car_18n = '';
                    }
                    if(empty($relate_car['car_ids'])){
                        $relate_car_ids = 999999999;
                    }else{
                        $relate_car_ids = implode(',', $relate_car['car_ids']);
                    }
                    $data[] = [
                        'commodity_id'         => $id,
                        'sp_value_list' => $value['sp_value_list'],
                        'price' => $value['price'],
                        'stock' => $value['stock'],
                        'image' => $value['image'],
                        'sku_code' => $value['sku_code'],
                        'tax' => $value['tax'],
                        'tax_code' => $value['tax_code'],
                        'cost_price' => $value['cost_price'],//计算成本价
                        'card_id' => $value['card_id'],
                        'hours_id' => $value['hours_id'],
                        'city_type' => $value['city_type'],
                        'upgrade_type' => '',
                        'maintain_q' => sprintf("%.1f",$discount),
                        'maintain_num' => $maintain_total_count,
                        'oli_liters' => $oli_liters,
                        'relate_car_18n'       => $relate_car_18n,
                        'relate_car_ids'       => $relate_car_ids,
                        'relate_car_work_hour' => '',
                        'e3s_bj_type_name'     => '',
                    ];
                }else{
                    $relate_car  = $this->findCarType($value['sku_code'],4);
                    $relate_car_18n = '';
                    if (isset($relate_car['n_18'])) {
                        $relate_car_18n = implode(',', $relate_car['n_18']);
                    }
                    if(empty($relate_car['car_ids'])){
                        $relate_car_ids = 999999999;
                    }else{
                        $relate_car_ids = implode(',', $relate_car['car_ids']);
                    }
                    $data[] = [
                        'commodity_id'         => $id,
                        'sp_value_list' => $value['sp_value_list'],
                        'price' => $value['price'],
                        'stock' => $value['stock'],
                        'image' => $value['image'],
                        'sku_code' => $value['sku_code'],
                        'tax' => $value['tax'],
                        'tax_code' => $value['tax_code'],
                        'cost_price' => $value['cost_price'],//计算成本价
                        'card_id' => $value['card_id'],
                        'hours_id' => $value['hours_id'],
                        'city_type' =>'',
                        'upgrade_type' => $value['city_type'],
                        'maintain_q' => sprintf("%.1f",$discount),
                        'maintain_num' => $maintain_total_count,
                        'oli_liters' => $oli_liters,
                        'relate_car_18n'       => $relate_car_18n,
                        'relate_car_ids'       => $relate_car_ids,
                        'relate_car_work_hour' => $sku_relate_car_work_hour,
                        'e3s_bj_type_name'     => $sku_bj_type_name,
                    ];
                }
                if($i == 100){
                    $commodity_sku->insertAll($data);
                    $data=[];
                    $i = 0;
                }
            }
            if(!empty($data)){
                $commodity_sku->insertAll($data);
            }
            $commodity->allowField(true)->isUpdate(true)->save(['is_update'=>1], ['id' => $id]);
            Db::commit();
        }catch (Exception $exception) {
//            Db::rollback();
            Logger::info('queue_commodity_sku', $exception->getMessage());
        }
    }

    private function oli_liters($oil){
        $oli_liters = strstr($oil, '_3.5L_');
        if(!empty($oli_liters)){
            return 3.5;
        }
        $oli_liters = strstr($oil, '_4L_');
        if(!empty($oli_liters)){
            return 4;
        }
        $oli_liters = strstr($oil, '_5L_');
        if(!empty($oli_liters)){
            return 5;
        }
        $oli_liters = strstr($oil, '_6L_');
        if(!empty($oli_liters)){
            return 6;
        }
        return 99;
    }

    private function findCarType($sku_codes,$type = 0)
    {

        $error      = [];
        if (empty($sku_codes)) {
            return $error;
        }
        $city_type = [];
        $part_time = [];
        if($type == 0){

            $spare_part = new \app\common\model\e3s\E3sSparePart();
            // 备件车系车型
            $model = new \app\common\model\e3s\E3sPartCarSeries();
            $CarSeries = new \app\common\model\e3s\E3sCarSeries();
            $field = 'part_no,sale_price,dlr_price,variety_code_mid_code,variety_code,variety_name';
            // 备件
            $part_list = $spare_part->where(['part_no'=>$sku_codes,'is_enable'=>1])
                ->field($field)
                ->select();
            Logger::info('备件sql:'.$spare_part->getLastSql());
            // 备件分组
            $dlr_code = new E3sSpecificRelationPart();
            $part_del_code = $dlr_code->alias('a')->join('t_e3s_specific_relation_dlr b','a.spec_part_group_id = b.spec_part_group_id')
                ->where(['a.is_enable'=>1,'b.is_enable'=>1,'a.part_no'=>$sku_codes])->column('b.dlr_code');
            Logger::info('备件分组sql:'.$dlr_code->getLastSql());

            $data = [];
            foreach ($part_list as $value){
                //过滤 3机油、4变速箱油、5制动液、6防冻防锈液、7养护品，都不需要匹配车型和备件的上下线时间。
                if(in_array($value['variety_code_mid_code'],[3,4,5,6,7])){
//                    $data[] = [
//                        'n_18' => '',
//                        'fit_beg_time' => '',
//                        'fit_end_time' => '',
//                        'sku_code' => $value['part_no'],
//                        'price' => $value['sale_price'],
//                        'cost_price' => $value['dlr_price'],
//                        'relate_car_work_hour' => '',
//                        'e3s_bj_type_name' => '',
//                        'rep_part_no' => '',
//                        'relate_dlr_code' => implode(',',$part_del_code),
//                        'car_ids' => '-1',
//                    ];
                }else{
                    // 1120版本不上 start

                }
                //过滤 3机油、4变速箱油、5制动液、6防冻防锈液、7养护品，都不需要匹配车型和备件的上下线时间。--VV
                //改为只是不匹配时间而已--LZX
                // 备件车系车型



//                $fit_beg_time = $model->alias('a')
//                    ->join('t_e3s_spare_part b','a.part_no = b.part_no')
//                    ->where('a.part_no', $value['part_no'])
//                    ->where('a.fit_beg_time','<>','')
//                    ->where('a.is_enable','=',1)
//                    ->field('a.part_no,b.sale_price,a.fit_beg_time,a.fit_end_time,b.variety_code_mid_code')
//                    ->group('a.fit_beg_time,a.fit_end_time')
//                    ->buildSql();
//
//                $rep_part_no = $model->alias('a')
//                    ->join('t_e3s_spare_part b','a.part_no = b.part_no')
//                    ->where(['b.rep_part_no'=>array('like','%'.$value['part_no'].'%')])
//                    ->where('a.fit_beg_time','<>','')
//                    ->where('a.is_enable','=',1)
//                    ->field('a.part_no,b.sale_price,a.fit_beg_time,a.fit_end_time,b.variety_code_mid_code')
//                    ->union($fit_beg_time,false)
//                    ->group('a.part_no,a.fit_beg_time,a.fit_end_time')
//                    ->select();//查询原件是否是被人的替换件
                $where = [];
                $where[] = ['exp', "FIND_IN_SET('{$value['part_no']}',rep_part_no)"];
                $part_no_arr = $spare_part->where($where)->group('part_no')->column('part_no');
                $part_no_arr = array_merge($part_no_arr,[$value['part_no']]);
                $rep_part_no = $model->alias('a')
                    ->join('t_e3s_spare_part b','a.part_no = b.part_no')
                    ->whereIn('a.part_no', $part_no_arr)
                    ->where('a.fit_beg_time','<>','')
                    ->where('a.is_enable','=',1)
                    ->field('a.part_no,b.sale_price,a.fit_beg_time,a.fit_end_time,b.variety_code_mid_code')
                    ->group('a.fit_beg_time,a.fit_end_time')
                    ->select();


                if(!empty($rep_part_no)){
                    foreach ($rep_part_no as $k=>$val){
                        $n18 = $model->where(['part_no' => $val['part_no'], 'fit_beg_time' => $val['fit_beg_time'],
                            'fit_end_time' => $val['fit_end_time'], 'is_enable' => 1,
                        ])->column('car_config_code');
                        $work = $this->part_work_hour($val['part_no'],$val['fit_beg_time'],$val['fit_end_time']);
                        if(in_array($val['variety_code_mid_code'],[3,4,5,6,7])){
                            $val['fit_beg_time'] = $val['fit_end_time']='';// 只过滤备件上下架时间
                        }
                        $part_time = $val['fit_beg_time'].'_'.$val['fit_end_time'];
                        if(isset($data[$part_time])){
                            $array_18n = array_unique(array_merge(explode(',',$data[$part_time]['n_18']),$n18));
                            $data[$part_time]['n_18'] = implode(',',$array_18n);
                            $data[$part_time]['relate_car_work_hour'] = $data[$part_time]['relate_car_work_hour'] + $work['part_no_array'];

                        }else{
                            $data[$part_time] = [
                                'n_18' => implode(',',$n18),
                                'fit_beg_time' => $val['fit_beg_time'],
                                'fit_end_time' => $val['fit_end_time'],
                                'sku_code' => $value['part_no'],
                                'price' => $value['sale_price'],
                                'cost_price' => $value['dlr_price'],
                                'relate_car_work_hour' => $work['part_no_array'],
                                'e3s_bj_type_name' => implode(',', $work['bj_type_name']),
                                'rep_part_no' => '',
                                'relate_dlr_code' => implode(',',$part_del_code),
                            ];
                        }
                        $data[$part_time]['variety_code'] = $value['variety_code'];
                        $data[$part_time]['variety_name'] = $value['variety_name'];
                    }
                    $data = array_values($data);
                    foreach ($data as $key=>$item){
                        $relate_cars = $CarSeries->whereIn('car_config_code', $item['n_18'])
                            ->where('is_enable','=',1)->column('id');
                        $data[$key]['car_ids'] = implode(',',$relate_cars);
                        $data[$key]['relate_car_work_hour'] = json_encode($item['relate_car_work_hour']);

                    }
                }


            }
            return $data;
        }
        elseif ($type == 1){
            //老友惠和心悦获取18为码
            $where['a.maintain_group_code'] = array('in',$sku_codes);
            $where['a.is_enable'] = 1;
            $where['b.is_enable'] = 1;
            $list = Db::name('e3s_maintenance_package')->alias('a')
                ->join('t_e3s_maintenance_product_car_series b','a.product_type_id = b.product_type_id')
                ->where($where)
                ->group('a.product_type_id')
                ->column('a.product_type_id');
            if(empty($list)){
                $relate_n18 = [];
                $relate_cars = [];
            }else{
                $relate_n18 = Db::name('e3s_maintenance_product_car_series')->alias('a')
                    ->join('t_e3s_car_series b','a.service_car_type = b.service_car_type')
                    ->where(['a.product_type_id'=>array('in',$list)])
                    ->group('b.car_config_code')
                    ->column('b.car_config_code');
                $res_where['car_config_code'] = array('in',array_values(array_unique($relate_n18)));
                $res_where['is_enable'] = 1;
                $relate_cars = \app\common\model\e3s\E3sCarSeries::where($res_where)
                    ->column('id');
            }
        }
        elseif ($type == 2){
            //pz1a套餐
            $where['a.sp_basic_code'] = array('in',$sku_codes);
            $city_type = Db::name('e3s_pz1a_maintenance_package')->alias('a')
                ->where($where)
                ->field('level_id')
                ->find();
            $city_type = $city_type['level_id'];
            $relate_n18 = Db::name('e3s_pz1a_maintenance_package')->alias('a')
                ->join('t_e3s_pz1a_maintenance_car_series b','a.sp_basic_id = b.sp_basic_id')
                ->join('t_e3s_car_series c','b.service_car_type = c.service_car_type')
                ->where($where)
                ->group('c.car_config_code')
                ->column('c.car_config_code');
            $res_where['car_config_code'] = array('in',array_values(array_unique($relate_n18)));
            $res_where['is_enable'] = 1;
            $relate_cars = \app\common\model\e3s\E3sCarSeries::where($res_where)
                ->column('id');
        }
         elseif ($type == 5) {
            // 延保服务包
            $car_series_model = new \app\common\model\e3s\E3sCarSeries();
            $delay_insurance_model = new E3sDelayInsurance();
            $map = [
                'n_product_code' => $sku_codes,
                'is_enable' => 1,
            ];
            // 查询延保和车系数据
            $insurance_list = $delay_insurance_model->where($map)->select();
            // 查询18位码
            $car_series_codes = array_column($insurance_list, 'car_series_code');
            $map = [
                'car_series_code' => ['in', $car_series_codes],
                'is_enable' => 1,
            ];
            $car_series_list = $car_series_model->where($map)->group('car_config_code')->field('id,car_config_code,car_series_code')->select();
            return ['insurance_list'=>$insurance_list, 'car_series_list'=>$car_series_list];

        }
        else{
            $where['a.maintain_group_code'] = array('in',$sku_codes);
            $where['b.service_car_type'] = 'COMMOM_CARTYPE';
            $where['a.is_enable'] = 1;
            $where['b.is_enable'] = 1;
            $list = Db::name('e3s_maintenance_package')->alias('a')
                ->join('t_e3s_maintenance_product_car_series b','a.product_type_id = b.product_type_id')
                ->where($where)
                ->group('a.maintain_group_code')
                ->select();
            if(empty($list)){
                $relate_n18 = Db::name('e3s_maintenance_package')->alias('a')
                    ->join('t_e3s_maintenance_product_car_series b','a.product_type_id = b.product_type_id')
                    ->join('t_e3s_car_series c','b.service_car_type = c.service_car_type')
                    ->where([
                        'a.maintain_group_code'=>array('in',$sku_codes),
                        'a.is_enable' => 1,
                        'c.is_enable' => 1,
                    ])
                    ->column('c.car_config_code');
            }else{
                $relate_n18 = \app\common\model\e3s\E3sCarSeries::where('car_brand_code',$list[0]['dlr_brand_code'])
                    ->where('is_enable','=',1)
                    ->column('car_config_code');
            }
            $res_where['car_config_code'] = array('in',array_values(array_unique($relate_n18)));
            $res_where['is_enable'] = 1;
            $relate_cars = \app\common\model\e3s\E3sCarSeries::where($res_where)
                ->column('id');
        }
        return ['error' => $error, 'n_18' => array_values(array_unique($relate_n18)), 'car_ids' => array_unique($relate_cars),'city_type'=>$city_type,'part_time'=>$part_time];
    }

    //备件新增时间查询车型工时和备件类型名称
    private function part_work_hour($part_no,$start_time,$end_time)
    {
        $list = Db::name('e3s_part_car_series')->alias('a')
            ->join('t_e3s_spare_part b','a.part_no = b.part_no','left')
            ->where(['a.part_no'=>$part_no,'a.fit_beg_time'=>$start_time,'a.fit_end_time'=>$end_time,'a.is_enable'=>1])//备件时间
            ->field('a.id,a.car_config_code,b.variety_code_big_name,b.variety_code_small_name')
            ->select();
        $part_no_array = [];
        $array = [];
        foreach ($list as $value) {
            $part_no_array[$value['id']][] = $value['car_config_code'];
            if($value['variety_code_big_name'] != '养护品'){
                $array[] = $value['variety_code_small_name'];
            }
        }
        return ['part_no_array' =>$part_no_array,'bj_type_name' => array_unique($array)];
    }


    private function commodity_set_sku($sku_list,$comm_set_id,$admin_info){
        $commoditySku = new DbCommoditySku();
        $commoditySetSku = new DbCommoditySetSku();
        $sku_list_array = [];
        foreach ($sku_list as $sku) {
            $sku_model = $commoditySku->getList(['where'=>[
                'commodity_id'=>$sku['commodity_id'],
                'sku_code' => $sku['sku_code'],
                'is_enable' => 1,
                'sp_value_list' => $sku['sp_value_list'],
            ]]);
            foreach ($sku_model as $value){
                if ($sku['sp_value_list'] == $value['sp_value_list']){
                    $price = $sku['price'];
                    $stock = $sku['stock'];
                }else{
                    $price = $value['price'];
                    $stock = $value['stock'];
                }
                $sku_list_array[] = [
                    'commodity_id' => $value['commodity_id'],
                    'dlr_code' => $admin_info['dlr_code'],
                    'set_type' => $admin_info['type'],
                    'commodity_set_id' => $comm_set_id,
                    'stock' => $stock,
                    'price' => $price,
                    'commodity_sku_id' => $value['id'] ?? 0,
                    'relate_car_18n' => $value['relate_car_18n'] ?? '',
                    'relate_car_ids' => $value['relate_car_ids'] ?? '',
                    'city_type' => $value['city_type'] ?? '',
                ];
            }
        }
        if(!empty($sku_list_array)){
            $commoditySetSku->insertAll($sku_list_array);
        }

    }

    private function re_commodity_set_sku($sku_list,$admin_info,$commodity_set_id){
        $commoditySku = new DbCommoditySku();
        $commoditySetSku = new DbCommoditySetSku();
        foreach ($sku_list as $sku) {
            $sku_model =$commoditySku->getList(['where'=>[
                'commodity_id'=>$sku['commodity_id'],
                'sku_code' => $sku['sku_code'],
                'is_enable' => 1,
                'sp_value_list' => $sku['sp_value_list'],
            ]]);
            foreach ($sku_model as $value){
                // 判断是否降价10%
                $set_sku_find = $commoditySetSku->where([
                    'commodity_id'=>$value['commodity_id'],
                    'commodity_set_id'=>$commodity_set_id,
                    'commodity_sku_id'=>$value['id'],
                    'is_enable' => 1,
                ])->find();


                if ($sku['sp_value_list'] == $value['sp_value_list']){
                    $price = $sku['price'];
                    $stock = $sku['stock'];
                }else{
                    $price = $value['price'];
                    $stock = $value['stock'];
                }
                if(empty($set_sku_find)){
                    $update_add[] = [
                        'commodity_id' => $value['commodity_id'],
                        'dlr_code' => $admin_info['dlr_code'],
                        'set_type' => $admin_info['type'],
                        'commodity_set_id' => $commodity_set_id,
                        'stock' => $stock,
                        'price' => $price ?? 0,
                        'commodity_sku_id' => $value['id'] ?? 0,
                        'relate_car_18n' => $value['relate_car_18n'] ?? '',
                        'relate_car_ids' => $value['relate_car_ids'] ?? '',
                        'city_type' => $value['city_type'] ?? '',
                        'divided_into' => $sku['city_type'] ?? '',
                        'install_fee' => $sku['install_fee'] ?? '',
                        'cost_price' => $sku['cost_price'] ?? '',
                    ];
                }else{
                    $this->depreciateCommodity($value['commodity_id'], $set_sku_find);
                    $update_save[] = [
                        'commodity_id' => $value['commodity_id'],
                        'dlr_code' => $admin_info['dlr_code'],
                        'set_type' => $admin_info['type'],
                        'commodity_set_id' => $commodity_set_id,
                        'stock' => $stock,
                        'price' => $price,
                        'commodity_sku_id' => $value['id'] ?? 0,
                        'relate_car_18n' => $value['relate_car_18n'] ?? '',
                        'relate_car_ids' => $value['relate_car_ids'] ?? '',
                        'city_type' => $value['city_type'] ?? '',
                        'divided_into' => $sku['city_type'] ?? '',
                        'install_fee' => $sku['install_fee'] ?? '',
                        'cost_price' => $sku['cost_price'] ?? '',
                        'is_enable' => 1,
                        'id' => $set_sku_find['id'] ?? '',
                    ];
                }
            }
            if(!empty($update_add)){
                $commoditySetSku->insertAll($update_add);
            }
            if(!empty($update_save)){
                $commoditySetSku->saveAll($update_save);
            }
        }
    }

    /**
     * 降价10%记录
     * @param $commodityId
     * @param $setSku
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private function depreciateCommodity($commodityId, $setSku)
    {
        $commoditySetSku = new DbCommoditySetSku();
        $setSkuInfo = $commoditySetSku->where('id', $setSku['id'])->find();
        if (($setSkuInfo['price'] - $setSku['price']) >= ($setSkuInfo['price'] * 0.1)) {
            $add = [
                'commodity_id' => $commodityId,
                'set_sku_id' => $setSku['id'],
                'commodity_sku_id' => $setSkuInfo['commodity_sku_id'],
                'price' => $setSkuInfo['price'],
                'new_price' => $setSku['price'],
            ];
            DbCommodityDepreciateMsg::create($add);
        }
    }



    private function part_commodity_sku($sku_codes,$commodity_id,$sku,$admin_name)
    {
        $data = $this->findCarType($sku_codes,0);
        $new_sku_list = [];
        $model_sku = new DbCommoditySku();
        foreach ($data as $i => $item){
            $new_sku_list[$i] = $sku;
            $find = $model_sku->where([
                'commodity_id' => $commodity_id,
                'sp_value_list'=>$sku['sp_value_list'],
                'sku_code' => $item['sku_code'],
                'part_start_time' => $item['fit_beg_time'],
                'part_end_time' => $item['fit_end_time']
            ])->find();
            if (!empty($find)){
                $new_sku_list[$i]['id'] = $find['id'];
                $new_sku_list[$i]['creator'] = $find['creator'];
                $new_sku_list[$i]['modifier'] = $admin_name;
            } else {
                $new_sku_list[$i]['creator'] = $admin_name;
                $new_sku_list[$i]['modifier'] = '';
            }
            $new_sku_list[$i]['commodity_id'] = $commodity_id;
            $new_sku_list[$i]['price'] = $item['price'];
            $new_sku_list[$i]['cost_price'] = $item['cost_price'];
            $new_sku_list[$i]['sku_code'] = $item['sku_code'];
            $new_sku_list[$i]['relate_car_18n'] = $item['n_18'];
            $new_sku_list[$i]['part_start_time'] = $item['fit_beg_time'];
            $new_sku_list[$i]['part_end_time'] = $item['fit_end_time'];
            $new_sku_list[$i]['rep_part_no'] = $item['rep_part_no'];
            $new_sku_list[$i]['relate_dlr_code'] = $item['relate_dlr_code'];
            $new_sku_list[$i]['variety_code'] = $item['variety_code'];
            $new_sku_list[$i]['variety_name'] = $item['variety_name'];
            if(empty($item['car_ids'])){
                $new_sku_list[$i]['relate_car_ids'] = 999999999;
            }elseif ($item['car_ids'] == '-1'){
                $new_sku_list[$i]['relate_car_ids'] = '';
            }else{
                $new_sku_list[$i]['relate_car_ids'] = $item['car_ids'];
            }
            $new_sku_list[$i]['relate_car_work_hour'] = $item['relate_car_work_hour'];
            $new_sku_list[$i]['e3s_bj_type_name'] = $item['e3s_bj_type_name'];
            $new_sku_list[$i]['is_enable'] = 1;
        }
        return $model_sku->SaveAll($new_sku_list);
    }



    private function yan_bao_commodity_sku($sku_code,$commodity_id,$sku,$admin_name)
    {
        $data = $this->findCarType($sku_code,5);
        $commodity_sku_model = new DbCommoditySku();
        $insurance_list = $data['insurance_list'];
        $car_series_list = $data['car_series_list'];
        $new_sku_list = [];
        foreach ($insurance_list as $i => $item) {
            $new_sku_list[$i] = $sku;
            $find = $commodity_sku_model->where([
                'commodity_id' => $commodity_id,
                'sp_value_list'=>$sku['sp_value_list'],
                'sku_code' => $item['n_product_code'],
            ])->find();
            if (!empty($find)){
                $new_sku_list[$i]['id'] = $find['id'];
                $new_sku_list[$i]['creator'] = $find['creator'];
                $new_sku_list[$i]['modifier'] = $admin_name;
            } else {
                $new_sku_list[$i]['creator'] = $admin_name;
                $new_sku_list[$i]['modifier'] = '';
            }
            $new_sku_list[$i]['commodity_id'] = $commodity_id;
            $new_sku_list[$i]['price'] = $item['use_price'];
            $new_sku_list[$i]['cost_price'] = $item['dlr_price'];
            $new_sku_list[$i]['sku_code'] = $item['n_product_code'];
            // 当前车系下的18位码
            $relate_car_18n = [];
            $relate_car_ids = [];
            foreach ($car_series_list as $key => $val) {
                if ($item['car_series_code'] == $val['car_series_code']) {
                    $relate_car_18n[] = $val['car_config_code'];
                    $relate_car_ids[] = $val['id'];
                }
            }
            $new_sku_list[$i]['relate_car_18n'] = implode(',',$relate_car_18n);
            $new_sku_list[$i]['relate_car_ids'] = implode(',', $relate_car_ids);
            $new_sku_list[$i]['variety_code'] = $item['pro_guarantee_type_code'];
            $new_sku_list[$i]['variety_name'] = $item['pro_guarantee_pro_type_name'];

            $new_sku_list[$i]['is_enable'] = 1;
        }
        return $commodity_sku_model->SaveAll($new_sku_list);

    }
}
