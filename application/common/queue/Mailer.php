<?php

namespace app\common\queue;

use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\db\DbUser;
use app\common\port\connectors\Order;
use think\Log;
use think\Queue;
use think\queue\Job;
use app\common\model\inter\IRequestLog;
use app\common\net_service\NetOrder;
use tool\PHPMailer\PHPMailer;

/**
 * 订单
 * Class Order
 * @package app\common\queue
 */
class Mailer extends Base
{

    /**
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data)
    {
        trace('500,start send Mailer');

        try {
            $data = json_decode($data, true);
            $mail = new PHPMailer();
            $mail->setLanguage('zh_cn');
            $mail->set('CharSet', 'UTF-8');
            $mail->set('Debugoutput', 'html');
            $mail->set('SMTPDebug', 0);
            $mail->SMTPOptions = array(
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true,
                )
            );
            $mail->isSMTP();
            $mail->set('Port', 25);
            $mail->set('SMTPAuth', true);
            $mail->set('Username', '<EMAIL>');
            $mail->set('Password', '#3bw$bhs');
            $mail->set('Host', 'casarrayhuadu.corp.dfl');
            $mail->setFrom('<EMAIL>', $data['from']);

            if (isset($data['file']) && isset($data['file_name'])) {
                $mail->addAttachment($data['file'], $data['file_name']); // 添加附件
            }

            if (config('app_status') == "develop") {
//                $mail->AddAddress('<EMAIL>');
//                $mail->AddAddress('<EMAIL>');
                //$mail->AddAddress('<EMAIL>');
                if (!empty($data['emails'])) {
                    foreach($data['emails'] as $email){
                        $mail->AddAddress($email);
                    }
                }

//                $mail->AddAddress('<EMAIL>');
            }else{
                foreach($data['emails'] as $email){
                    $mail->AddAddress($email);
                }
            }
            $content_image = $data['content_image'] ?? '';

            if(!empty($content_image)){
                $mail->addStringEmbeddedImage(file_get_contents($content_image),"imgcid",$data['image_name']);
            }



            $mail->IsHTML(true);
            $mail->set('Subject', $data['title']);
            $mail->set('Body', $data['content']);
            $mail->setWordWrap(); // 设置每行字符串的    长度
            $re = $mail->send();
            $this->logDb([
                'queue'       => 'Mailer',
                'source_type' => 'send_mail',
                'data_info'   => json_encode_cn($data),
                'result_info' => json_encode_cn($re),
            ]);

            $job->delete();
        } catch (\Exception $e) {
            Log::error('sendMailer : ' . $e->getMessage());
            $job->attempts() > $this->_attempts ? $job->delete() : $job->release($this->_release_time);
        }
        trace('500,end send Mailer');
    }

}
