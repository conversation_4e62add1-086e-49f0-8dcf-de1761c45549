<?php


namespace app\common\net_service;


use app\common\model\db\DbSeckill;

class Seckill
{

    /**
     * 获取秒杀活动状态和时间
     * @param $seckillId
     * @return array
     */
    public function getSeckillTime($seckillId)
    {
        $seckill_model  = new DbSeckill();
        $param['where'] = ['id' => $seckillId];
        $seckill_info   = $seckill_model->getOne($param);
        $data           = [
            'id'              => $seckillId,
            'title'           => $seckill_info['title'],
            'act_status'      => $seckill_info['act_status'],
            'start_time'      => $seckill_info['start_time'],
            'end_time'        => $seckill_info['end_time'],
            'next_start_time' => '',
            'next_end_time'   => '',
            'is_enable'       => $seckill_info['is_enable'],
            'activity_image'  => $seckill_info['activity_image'],
        ];
        // 每天重复秒杀活动
        if ($seckill_info['seckill_type'] == 2) {
            // 判断当前时间
            $current_time        = date('Y-m-d H:i:s');
            $activity_start_time = date('Y-m-d ',strtotime($seckill_info['start_time'])) . $seckill_info['day_start_time'];
            $activity_end_time   = date('Y-m-d ',strtotime($seckill_info['end_time'])) . $seckill_info['day_end_time'];
            if ($current_time < $activity_start_time) {
                $act_status = 1; // 未开始
                $start_time = $activity_start_time;
//                $end_time   = $seckill_info['start_time'] . ' ' . $seckill_info['day_end_time'];
                $end_time   = date('Y-m-d '). $seckill_info['day_end_time'];
            } else if ($current_time > $activity_end_time) {
                $act_status = 3; // 已结束
//                $start_time = $seckill_info['end_time'] . ' ' . $seckill_info['day_start_time'];
                $start_time =  date('Y-m-d ') . $seckill_info['day_start_time'];
                $end_time   = $activity_end_time;
            } else {
                // 进行中
                $time = date('H:i:s');
                if ($time < $seckill_info['day_start_time']) {
                    $act_status = 1; // 未开始
                    $start_time = date('Y-m-d ') . $seckill_info['day_start_time'];
                    $end_time   = date('Y-m-d ') . $seckill_info['day_end_time'];
                } elseif ($time > $seckill_info['day_start_time'] && $time < $seckill_info['day_end_time']) {
                    $act_status = 2; // 进行中
                    $start_time = date('Y-m-d ') . $seckill_info['day_start_time'];
                    $end_time   = date('Y-m-d ') . $seckill_info['day_end_time'];
                } else {
                    // 跨天
                    $act_status = 1; // 未开始
                    $start_time = date('Y-m-d ', strtotime(' +1 day')) . $seckill_info['day_start_time'];
                    $end_time   = date('Y-m-d ', strtotime(' +1 day')) . $seckill_info['day_end_time'];
                }
            }
            $next_start_time = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($start_time)));
            $next_end_time   = date('Y-m-d H:i:s', strtotime('+1 day', strtotime($end_time)));
            if ($next_end_time > $activity_end_time) {
                $next_start_time = '';
                $next_end_time   = '';
            }
            $data = [
                'id'              => $seckillId,
                'title'           => $seckill_info['title'],
                'act_status'      => $act_status,
                'start_time'      => $start_time,
                'end_time'        => $end_time,
                'next_start_time' => $next_start_time,
                'next_end_time'   => $next_end_time,
                'is_enable'       => $seckill_info['is_enable'],
                'activity_image'  => $seckill_info['activity_image'],
            ];
        }
        return $data;
    }
}