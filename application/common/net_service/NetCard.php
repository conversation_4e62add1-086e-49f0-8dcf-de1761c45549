<?php
/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 2020/12/17
 * Time: 6:29 PM
 */

namespace app\common\net_service;

use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuQyPoster;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbCard;
use app\common\model\db\DbCardCarSeries;
use app\common\model\db\DbCommodityCard;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbUserCarSeries;
use app\common\model\db\DbSendCardPage;

class NetCard extends Common
{

    private function getDateTime(){
        return date("Y-m-d H:i:s",time());
    }

    private function getDate(){
        return date("Y-m-d",time());
    }

    public function getUsablecard($user,$data,$status){
        $data['poster_id'] = $data['poster_id']  ?? 0;
        $poster_model      = new BuQyPoster();
        $card_model        = new DbCard();
        $send_card_model   = new DbSendCardPage();
        $where = [];
        $cardModel = $card_model->alias("a");
        if(!empty($data['card_str_ids'])){
            $card_str_arr = explode(',',$data['card_str_ids']);
            $where['a.id'] = ['in',$card_str_arr];
        }

        if(!empty($data['point_code'])){
            $where['d.receive_coupon_points'] = ['LIKE','%'.$data['point_code'].'%'];
            $where['d.is_enable'] = 1;
            $cardModel = $cardModel->join("t_db_activity d","a.activity_id=d.activity_id");
        }

        //海报专用
        $poster_count_arr = [];
        $card_msg = [];
        if(!empty($data['poster_id'])){
            $poster            = $poster_model->getOne(['where' => ['id' => $data['poster_id']], 'dlr_code' => $data['channel_type']]);

            if (!$poster) {
                return $this->setResponseError('无海报信息')->send();
            }
            $send_params = [
                'field' => 'a.poster_img, b.card_list',
                'where' => [
                    'a.id'     => $poster['send_card_page_id'],
                    'b.module' => 3,
                ],
            ];
            $card_page   = $send_card_model->getSendMsg($send_params);
            $poster_msg = json_decode($poster['card_list'], true);
            $poster_count_arr = $poster_msg;
            $card_msg   = json_decode($card_page['card_list'], true);
            $card_ids   = array_keys($poster_msg);
            $where['a.id'] = ['in',$card_ids];
            $buCardReceiveRecord = new BuCardReceiveRecord();
            $poster_recive_arr = $buCardReceiveRecord->alias("a")->where(['a.card_id'=>['in',$card_ids],"poster_id"=>$data['poster_id']])->field("count(1) t,card_id")->select();
            $poster_card_count_arr = [];
            if(!empty($poster_recive_arr)){
                foreach($poster_recive_arr as $item){
                    $poster_card_count_arr[$item['card_id']] = $item['t'];
                }
            }
        }

        $canGetList = $cardModel->where('FIND_IN_SET("'.$user['channel_type'].'",a.up_down_channel_dlr) and a.receive_start_date <="'.$this->getDate(). '" AND a.receive_end_date >="'.$this->getDate().'"')
            ->where($where)
            ->where(["a.is_enable"=>1])
            ->whereIn("a.act_status",[1,2])
            ->field("a.*, '' as order_id")
            ->order("a.created_date desc")
            ->select();
     //   echo $cardModel->getLastSql();exit;
        if(empty($canGetList)){
            return  ['get_card_list'=>[]];
        }

        $query_card_arr_tmp = [];
        foreach($canGetList as $query_card_item){
            $haveGetCoun = $haveGetCountArr[$query_card_item['id']] ?? 0;
            $query_card_arr_tmp[$query_card_item['id']] = $query_card_item['get_limit'] - $haveGetCoun;
        }

        foreach($canGetList as $kk=>$activityCardItem){
                //1 可领取
                if(empty($query_card_arr_tmp[$activityCardItem['id']])){//空表示没领过
                    $canGetList[$kk]['is_can_receive'] = 1;
                }else{
                    $canGetList[$kk]['is_can_receive'] = $query_card_arr_tmp[$activityCardItem['id']] > 0 ? 1 : 0;
                }
                $canGetList[$kk]['card_id'] = strval($canGetList[$kk]['id']);
        }

        $card_list =  $this->card_get_use([],[],$user,$data['channel_type'],[],[],1,$canGetList,"","","",$status);
        $get_card_list = $card_list['get_card_list'];
        $got_out_list = [];
        if(!empty($get_card_list)){
            foreach($get_card_list as $k=>$item){
              //  $get_card_list[$k]['available_quantity'] = $get_card_list[$k]['get_limit'] - $get_card_list[$k]['have_get'] ;
//                if($get_card_list[$k]['id'] == 21705435471971328){
//                    dd($get_card_list[$k]['available_quantity']);
//                }
                if(!empty($poster_count_arr)){
                    //发放库存，这字段只有发券页有
                    if (isset($poster_card_count_arr[$item['id']])) {
                        $poster_have_get = $poster_card_count_arr[$item['id']] ?? 0 ;
                    } else {
                        $poster_have_get = 0;
                    }
                    $get_card_list[$k]['grant_count']= min($poster_count_arr[$item['id']],$item['available_count']) - $poster_have_get ;

                    //$card_msg不为空代码是发券页
                    if(!empty($card_msg) && $data['poster_id'] > 0){//发券页特别处理
                        $get_card_list[$k]['have_get'] = $poster_have_get;
                        $get_card_list[$k]['available_quantity'] = $card_msg[$item['id']] - $get_card_list[$k]['have_get'] ;

                    }
                }

                $get_card_list[$k]['is_received'] = 0;
                if($get_card_list[$k]['have_get'] > 0){
                    $get_card_list[$k]['is_received'] = 1;
                }

//                if($get_card_list[$k]['available_count'] > 0 && $get_card_list[$k]['available_quantity'] <= 0){
//                    unset($get_card_list[$k]);
//                }

                //者库存为0 或可领为0 都不能显示在可领页
                //可领去掉可领为0或库存为0
//                if($get_card_list[$k]['available_count'] <= 0 || $get_card_list[$k]['available_quantity'] <= 0){
//                    //$card_list = $this->getAllCard($user);
//                   // $card_vin_arr = $card_list['vin_arr_list'];
//                    //$article = "";
//                    //如果是海报进来的要显示抢光的卡券
//                    //刘洋那边要返回所有的券
//                    if($status == 1  && $data['poster_id'] == 0 ){
//                        unset($get_card_list[$k]);
//                    }
//                }
            }
        }
        return ['get_card_list'=>$this->addSort($get_card_list,0,2),'got_out_list'=>$got_out_list];

    }

    public function getAllCard($user){
        $carSerModel =  new DbUserCarSeries();
        $w = ['user_id' => $user['id'],'car_brand_code'=>$user['brand']];
        if($user['brand'] == 1){
            $w['channel_type'] = $user['channel_type'];
        }
        $userCarSeries_arr = $carSerModel->where($w)->select();

        $vin_arr_list = [];
        $centent_vin_arr_list = [];
        foreach($userCarSeries_arr as $userCarSeries_arr_item){
            if($userCarSeries_arr_item['vin'] == $user['vin']){
                $centent_vin_arr_list[$userCarSeries_arr_item['vin']] = $userCarSeries_arr_item['car_series_name'].$userCarSeries_arr_item['car_type_name'];
            }else{
                $vin_arr_list[$userCarSeries_arr_item['vin']] = $userCarSeries_arr_item['car_series_name'].$userCarSeries_arr_item['car_type_name'];
            }

        }
        return ['vin_arr_list'=>$vin_arr_list,'centent_vin_arr_list'=>$centent_vin_arr_list];
    }

    public function addSort($data,$isHaveGet,$wherePath){

        foreach($data as $k=>$item){
            if($isHaveGet == 0){ //只有全部时才要区分已领可领其它都按时间
                 if($item['available_count'] == 0){
                     $data[$k]['sort'] = 1;//抢光
                 }else if($item['have_get'] > 0 && $item['available_quantity'] > 0){
                     $data[$k]['sort'] = 2;//已领
                 }else{
                     $data[$k]['sort'] = 3;//可领
                 }
            }else{
                $data[$k]['sort'] = 0;
            }
            //处理可领的抢光
            if($data[$k]['sort'] == 1){
                $data[$k]['isHaveGet'] = 1;//1已领2可领
            }else{
                $data[$k]['isHaveGet'] = $wherePath;//1已领2可领
            }

        }
        return $data;
    }

    public function getHaveCard($user,$data,$status){
        $data['poster_id'] = $data['poster_id']  ?? 0;
        $poster_model      = new BuQyPoster();
        $send_card_model   = new DbSendCardPage();
        $where2 = " ";
        if(!empty($data['card_str_ids'])){
            $card_str_arr = explode(',',$data['card_str_ids']);
            $where2 .= " and a.id in ($card_str_arr)";
        }
           //二姐说已领的不用看点位
//        if(!empty($data['point_code'])){
//            $where2 .= " and act.receive_coupon_points LIKE '%".$data['point_code']."%'";
//        }

        //海报专用
        $poster_count_arr = [];
        $card_msg = [];
        if(!empty($data['poster_id'])){
            $poster            = $poster_model->getOne(['where' => ['id' => $data['poster_id']], 'dlr_code' => $data['channel_type']]);

            if (!$poster) {
                return $this->setResponseError('无海报信息')->send();
            }
            $send_params = [
                'field' => 'a.poster_img, b.card_list',
                'where' => [
                    'a.id'     => $poster['send_card_page_id'],
                    'b.module' => 3,
                ],
            ];
            $card_page   = $send_card_model->getSendMsg($send_params);
            $poster_msg = json_decode($poster['card_list'], true);
            $poster_count_arr = $poster_msg;
            $card_msg   = json_decode($card_page['card_list'], true);
            $card_ids   = array_keys($poster_msg);
           // $where['a.id'] = ['in',$card_ids];

            $card_ids = implode(',',$card_ids);
            $where2 .= " and d.id in ($card_ids)";
        }
        //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

        $carSerModel =  new DbUserCarSeries();
        //因为启辰的channel_type为空 日产不为空
        $w = ['user_id' => $user['id'],'car_brand_code'=>$user['brand']];
        if($user['brand'] == 1){
            $w['channel_type'] = $user['channel_type'];
        }

        $userCarSeries =     $carSerModel->where($w)->where(['is_bind'=>1])->find();
        $userCarSeries_arr = $carSerModel->where($w)->select();
        $all_vin_str = "";
        $vin_arr_list = [];
        $user_car_s_id_arr = [];
        $my_vin = [];
        foreach($userCarSeries_arr as $userCarSeries_arr_item){
            $vin_arr_list[$userCarSeries_arr_item['vin']] = $userCarSeries_arr_item['car_series_name'].$userCarSeries_arr_item['car_type_name'];
            $user_car_s_id_arr[] = $userCarSeries_arr_item['car_series_id'];
            $all_vin_str .= "'".$userCarSeries_arr_item['vin']."',";
            $my_vin[] = $userCarSeries_arr_item['vin'];
        }

        $where3 = "FIND_IN_SET('".$user['channel_type']."',d.up_down_channel_dlr) 
                    and (a.status in (1,2,5) or (a.status = 7 and activity.activity_status_flag =1) ) and a.is_enable=1 and d.brand_id=".$user['brand']." 
                    and a.validity_date_end>='".$this->getDateTime()."' 
                   
                    and d.act_status = 2 ";
        if(!empty($all_vin_str)){
            $all_vin_str = rtrim($all_vin_str,',');
            $where3 .= " and (a.receive_vin in (".$all_vin_str.") or a.user_id=".$user['id'] .") ";
        }else{
            $where3 .= " and a.user_id=".$user['id'];
        }




        $channel_type = "'".$user['channel_type']."'";
        $buCardReceiveRecord = new BuCardReceiveRecord();
        //当前用户已领券旧数据
//        $sql1 = "SELECT activity.select_obj,a.status,null as commodity_id,a.consume_order_code,d.is_enable,d.is_gift_card,a.card_code,d.quick_win_card_id,d.fixed_begin_term,d.fixed_term,d.date_type,d.validity_date_start as card_validity_date_start,d.validity_date_end as card_validity_date_end,d.car_series_id_str,a.id as reserve_id,a.created_date,`a`.`receive_vin`,a.act_id as order_id,d.least_cost,`a`.`use_vin`,d.card_name,d.id,d.card_id,d.card_type,d.card_quota,d.card_discount,d.get_limit,d.available_count,d.type,d.card_desc, a.validity_date_start,a.validity_date_end FROM
//        `t_bu_card_receive_record` `a` INNER JOIN `t_db_card` `d` ON `d`.`id`=`a`.`card_id` left join t_db_activity activity on d.activity_id = activity.activity_id and activity.is_enable =1
//          WHERE {$where3} and d.is_enable=1 and d.activity_id = 0 ";
        //当前用户已领券卡券融合部分
        $sql2 = "SELECT a.user_id,activity.select_obj,a.status,sku.commodity_id,a.consume_order_code,d.is_enable,d.is_gift_card,a.card_code,d.quick_win_card_id,d.fixed_begin_term,d.fixed_term,d.date_type,d.validity_date_start as card_validity_date_start,d.validity_date_end as card_validity_date_end,d.car_series_id_str,a.id as reserve_id,a.created_date,`a`.`receive_vin`,a.act_id as order_id,d.least_cost,`a`.`use_vin`,d.card_name,d.id,d.card_id,d.card_type,d.card_quota,d.card_discount,d.get_limit,d.available_count,d.type,d.card_desc, a.validity_date_start,a.validity_date_end FROM 
        `t_db_card` `d` left JOIN `t_db_activity` `act` ON `act`.`activity_id`=`d`.`activity_id` and act.is_enable = 1
		  inner join `t_bu_card_receive_record` `a` on `d`.`id` = `a`.`card_id`
		  left join t_db_activity_ass_sku ass_sku on d.activity_id = ass_sku.activity_id and ass_sku.is_enable=1
		  left join t_db_commodity_sku sku on ass_sku.sku_code = sku.sku_code and sku.is_enable = 1
		  inner join t_db_activity activity on d.activity_id = activity.activity_id and activity.is_enable =1
		  WHERE {$where3} {$where2}  and  a.validity_date_start <= '".$this->getDateTime()."'  and d.activity_id > 0 and (d.is_gift_card = 1   or  (d.is_gift_card = 0 and  d.is_enable=1 )) group by a.id";
       // $sql = "select * from ({$sql1} union all {$sql2}) as tt group by tt.reserve_id ORDER BY tt.created_date DESC";
      //  $query_card_list =  $buCardReceiveRecord->query($sql);

      //  $ret1 = $buCardReceiveRecord->query($sql1);
        $ret2 = $buCardReceiveRecord->query($sql2);
        $query_card_list = [];
        foreach($ret2 as $retItem){
            if (isset($retItem['is_gift_card']) && $retItem['is_gift_card'] == 1) {
                $retItem['coupon_activate_scene_list'] = 'KeHuZaiShangChengXiaDan';//买赠券跟支付一样，都是要购买，都跳商品列表
            }
            $retItem['activation_scenes'] = $this->parseActivationScenes($retItem['coupon_activate_scene_list'] ?? '');


            if($retItem['select_obj'] == 1){
                if($retItem['user_id'] ==  $user['id']){
                    $query_card_list[] = $retItem;
                }
            }

            if($retItem['select_obj'] == 2){
                if(!empty($my_vin)){
                    if(in_array($retItem['receive_vin'] , $my_vin)){
                        $query_card_list[] = $retItem;
                    }
                }
            }

            if($retItem['select_obj'] == 3){
                if(!empty($my_vin)){
                    if(in_array($retItem['receive_vin'] , $my_vin) && $retItem['user_id'] ==  $user['id']){
                        $query_card_list[] = $retItem;
                    }
                }
            }
        }


        if(!$query_card_list){
            return  [];
        }

//        print_json($query_card_list);
        //求出所有车的领券数量
       // $vin_arr = $this->getVinList($user['id']);

        $all_vin_get_card_arr = [];
//        if(!empty($vin_arr)){

            $all_vin_get_card_list = $buCardReceiveRecord->alias("a")->where(['a.user_id'=>$user['id']])->whereIn('receive_vin',$user['vin'])
                ->field("count(1) as have_get,a.card_id,receive_vin")
                ->group("receive_vin,card_id")
                ->select();
           // echo $buCardReceiveRecord->getLastSql();exit;
            foreach($all_vin_get_card_list as $all_vin_get_card_item){
                $all_vin_get_card_arr[$all_vin_get_card_item['receive_vin']][$all_vin_get_card_item['card_id']] = $all_vin_get_card_item['have_get'];
            }
//        }


        $query_card_have_get_list = $buCardReceiveRecord->alias('a')
            ->join("t_db_card d","d.id=a.card_id ")
            ->join("t_db_activity activity","a.activity_id =activity.activity_id and FIND_IN_SET($channel_type,activity.up_down_channel_dlr)",'left')
            ->where($where3)

            ->field("count(1) as have_get,a.card_id")
            ->group("a.card_id")
            ->select();
        //echo $buCardReceiveRecord->getLastSql();exit;
        $haveGetCountList = [];
        foreach($query_card_have_get_list as $query_card_have_get_item){
            $haveGetCountList[$query_card_have_get_item['card_id']] = $query_card_have_get_item['have_get'];
        }

        $query_card_list_tmp = [];
        $available_count_list = [];
        $freezeCode = [];
        foreach ($query_card_list as $kn=>$query_card_item){
            // 已冻结
            if ($query_card_item['status'] == 5 && !empty($query_card_item['consume_order_code'])) {
                $freezeCode[] = $query_card_item['consume_order_code'];
            }
            $query_card_list[$kn]['freeze_type'] = 0;
            $query_card_list[$kn]['freeze_order_id'] = 0;
            $query_card_list[$kn]['freeze_order_code'] = '';
            $query_card_list[$kn]['have_get'] = $haveGetCountList[$query_card_item['id']] ?? 0;
            $query_card_list[$kn]['card_id'] = $query_card_item['id'];

            if($query_card_item['available_count'] == 0 && $query_card_list[$kn]['have_get'] == 0){
                $query_card_list[$kn]['validity_date_start'] = $query_card_item['card_validity_date_start'];
                $query_card_list[$kn]['validity_date_end'] = $query_card_item['card_validity_date_end'];
            }
        }
        $freezeOrderList = [];
        if (!empty($freezeCode)) {
            $order_model = new BuOrder();
            $map = ['order_code'=>['in', $freezeCode]];
            $field = 'id,order_code,order_status';
            $orderList = $order_model->where($map)->field($field)->select();
            foreach ($orderList as $key => $item) {
                // 支付的冻结单号
                if (in_array($item['order_status'], [1,8])) {
                    $freezeOrderList[$item['order_code']] = $item;
                }
            }
        }


        $user_bind_car = '';
        if(!empty($userCarSeries)){
            $user_bind_car = $userCarSeries['car_series_name'].$userCarSeries['car_type_name'];
        }

        foreach($query_card_list as $k=>$query_card_item){
            if ($query_card_item['status'] == 5) {
                // 判断卡券是否是被支付冻结
                if (isset($freezeOrderList[$query_card_item['consume_order_code']])) {
                    $query_card_list[$k]['freeze_type'] = 1; // 支付冻结
                    $query_card_list[$k]['freeze_order_id'] = $freezeOrderList[$query_card_item['consume_order_code']]['id'];
                } else {
                    $query_card_list[$k]['freeze_type'] = 2; // 退款冻结
                }
                $query_card_list[$k]['freeze_order_code'] = $query_card_item['consume_order_code'];
            }

            if($query_card_item['have_get'] <=0){
                unset($query_card_list[$k]);
                continue;
            }
            if(!empty($query_card_item['car_config_code'])){
                if(empty($query_card_item['car_series_id_str'])){
                    unset($query_card_list[$k]);
                    continue;
                }
                if(!$user_car_s_id_arr){
                    unset($query_card_list[$k]);
                    continue;
                }
                if(!array_intersect($user_car_s_id_arr,explode(',',$query_card_item['car_series_id_str']))){
                    unset($query_card_list[$k]);
                    continue;
                }
            }
            $article = '';
           // dd($user_bind_car);
            if($user['vin'] != $query_card_item['receive_vin'] && !empty($query_card_item['receive_vin'])){
                //如果没指定车的就表示适用所有车
                $article1 = "";
                $car_name = $vin_arr_list[$query_card_item['receive_vin']] ??  '';
                if(!empty($query_card_item['car_series_id_str'])){
                    $query_card_list[$k]['change_car'] = 1;
                    if(!empty($car_name)){
                        $article1 = sprintf("本券适用您车辆:%s %s ",$car_name,$query_card_item['receive_vin']);
                        $article = $article1.sprintf("，当前默认车辆为:%s %s，请切换车辆领券 ",$user_bind_car,$user['vin']);
                    }
                }else{
                    $query_card_list[$k]['change_car'] = 0;
                    $article = sprintf("本券适用您车辆:%s %s ",$car_name,$query_card_item['receive_vin']);
                }
                if(!empty($query_card_item['receive_vin'])) {
                    $query_card_list[$k]['change_car'] = 1;
                    $article = sprintf("本券适用您车辆:%s %s，当前车辆为:%s %s，请切换车辆领券",$car_name,$query_card_item['receive_vin'],$user_bind_car,$user['vin']);
                }

                if(isset($all_vin_get_card_arr[$query_card_item['receive_vin']]) && $all_vin_get_card_arr[$query_card_item['receive_vin']]){
                    $card_id_str = $query_card_item['card_id'];
                    if($all_vin_get_card_arr[$query_card_item['receive_vin']][$card_id_str]){
                        $query_card_item['have_get'] = $query_card_list[$k]['have_get'] = $all_vin_get_card_arr[$query_card_item['receive_vin']][$card_id_str];
                    }
                }
            }else{
                $query_card_list[$k]['change_car'] = 0;
                if(!empty($query_card_item['receive_vin'])) {
                    $article = sprintf("本券适用您车辆:%s %s", $user_bind_car, $query_card_item['receive_vin']);
                }
            }

            $query_card_list[$k]['article'] = $article;
            $available_quantity =  $query_card_item['get_limit'] - $query_card_item['have_get'];
            $query_card_list[$k]['available_quantity'] = $available_quantity ;
            if($available_quantity < 0){
                $query_card_list[$k]['available_quantity']  = 0 ;
                $query_card_list[$k]['have_get'] = $query_card_item['get_limit'];
            }


            $card_time_word = sprintf("%s ~ %s",$this->dataFYmdpoint($query_card_item['validity_date_start']),$this->dataFYmdpoint($query_card_item['validity_date_end']));
//            if($query_card_item['date_type']==2){
//                if($query_card_item['fixed_begin_term']>0){
//                    $card_time_word=sprintf("领取后%s天生效，有效天数%s天",$query_card_item['fixed_begin_term'],$query_card_item['fixed_term']);
//                }else{
//                    $card_time_word=sprintf("领取后有效天数%s天",$query_card_item['fixed_term']);
//                }
//            }

            $query_card_list[$k]['card_time_word'] = $card_time_word;
            $query_card_list[$k]['is_card_use'] = 1;
            $query_card_list[$k]['is_received'] = 1;
            $query_card_list[$k]['gift_card_goods_ids'] = "";

            if($query_card_item['is_gift_card'] == 1 && $query_card_item['status'] == 7){
                $netGoods = new NetGoods();
                if(!empty($query_card_item['card_id']))   {
                  // if($query_card_item['card_id'] ==29049878126560256 ){
                     //   $gift_goods = $netGoods->getGiftMainGoods($query_card_item['card_id'],$user);
                      //  dd($gift_goods);
                   // }
                    $gift_goods = $netGoods->getGiftMainGoods($query_card_item['card_id'],$user);
                    $query_card_list[$k]['gift_card_goods_ids'] = $gift_goods['goods_id'] ?? "99999999";
                }
            }
        }

        return $this->addSort($query_card_list,$status,1);
    }

    //获取这个人所有车的vin
    public function getVinList($user_id){
       $dbUserCarSeriesObj =  new DbUserCarSeries();
       $carSdriesList = $dbUserCarSeriesObj->where(['is_enable'=>1,'user_id'=>$user_id])->select();
       $vin_arr = [];
       foreach($carSdriesList as $carSdriesItem){
           $vin_arr[] = $carSdriesItem['vin'];
       }
       return $vin_arr;
    }


    /**
     * 组件获取用户卡券信息
     * @param $user
     * @param $data
     * @param $componentType
     * @return array[]|false[]
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getUserCard($user,$data, $componentType='')
    {
        $card_model        = new DbCard();

        $where = [];
        if(!empty($data['card_str_ids'])){
            $card_str_arr = explode(',',$data['card_str_ids']);
            $where['a.id'] = ['in',$card_str_arr];
        }


        $canGetList = $card_model->alias("a")
            ->where('FIND_IN_SET("'.$user['channel_type'].'",a.up_down_channel_dlr) and a.receive_start_date <="'.$this->getDate(). '" AND a.receive_end_date >="'.$this->getDate().'"')
            ->where($where)
            ->where(["a.is_enable"=>1])
            ->whereIn("a.act_status",[1,2])
            ->field("a.*")
            ->order("a.created_date desc")
            ->select();
        if(empty($canGetList)){
            return  ['get_card_list'=>[]];
        }
        foreach($canGetList as $kk=>$activityCardItem){
            //1 可领取
            if(empty($query_card_arr_tmp[$activityCardItem['id']])){//空表示没领过
                $canGetList[$kk]['is_can_receive'] = 1;
            }else{
                $canGetList[$kk]['is_can_receive'] = $query_card_arr_tmp[$activityCardItem['id']] > 0 ? 1 : 0;
            }
            $canGetList[$kk]['card_id'] = strval($canGetList[$kk]['id']);
        }
        $result = $this->card_get_use([],[],$user,$data['channel_type'],[],[],1,$canGetList,'','',$componentType);

        return $result;
    }

    public function cardClassToGoods($card_id='')
    {
        $card_model =  new DbCard();
        $flat_model =  new DbCommodityFlat();
        $card_goods_model  =  new DbCommodityCard();
        $to_date  = date('Y-m-d');
        $where = ['b.is_enable'=>1,'a.is_enable'=>1,'b.receive_start_date'=>['<=',$to_date],'b.validity_date_end'=>['>=',$to_date],'a.class_id'=>['>',0],'a.commodity_set_id'=>0];
        if($card_id){
            $where['b.id'] = $card_id;
        }
        $field = "a.card_id,a.class_id";
        $list = $card_model->alias('b')->join('t_db_commodity_card a','a.card_id=b.id')->join('t_db_commodity_flat flat','find_in_set(b.id,flat.card_id)')->where($where)->field($field)->select();
        if($list){
            $card_to_cid = [];
            $cid = [];
            $all_cid=[];
            $all_1_cid=[];
            $all_2_cid=[];
            $all_3_cid=[];
            foreach ($list as $v){
                $card_to_cid[$v['card_id']][] = $v['class_id'];
                $cid[] = $v['class_id'];
            }
            unset($v);
            $model =  new DbCommodityType();
            $types = $model->alias('a')
                ->join("t_db_commodity_type t ", "a.id=t.comm_parent_id ")
                ->join("t_db_commodity_type b ", "t.id=b.comm_parent_id")
                ->where("a.is_enable=1 and b.is_enable=1 and t.is_enable=1 ")
                ->field('a.id aid,t.id bid,b.id cid')->select();
            foreach ($types as $v){
                if(in_array($v['aid'],$cid) || in_array($v['bid'],$cid) || in_array($v['cid'],$cid)){
                    $all_cid[]=$v['cid'];
                }
            }
            unset($v);
            $where = ['comm_type_id'=>['in',$all_cid],'is_enable'=>1];
            $flat_list= $flat_model->getList(['where'=>$where]);
            $class_goods = [];
            foreach ($card_to_cid as $k=>$v){
                foreach ($flat_list as $fv){
                    $comm_type_id_str_arr =  explode(',',$fv['comm_type_id_str']);
                    $card_id_arr = explode(',',$fv['card_id']);
                    if(in_array($k,$card_id_arr) && array_intersect($comm_type_id_str_arr,$v)){
                        $type_id_arr = array_intersect($comm_type_id_str_arr,$v);
                        $class_goods[$k.'-'.$type_id_arr[0]][]=['commodity_id'=>$fv['commodity_id'],'commodity_set_id'=>$fv['commodity_set_id']];
                    }
                }
            }
            if($class_goods){
                $data=[];
                foreach ($class_goods as $k=> $v){
                    $kk_arr =  explode('-',$k);
                    $where = ['card_id'=>$kk_arr[0],'class_id'=>$kk_arr[1],'commodity_set_id'=>['>',0]];
                    $goods_ids =  array_column($v,'commodity_id');
                    $where['commodity_id'] = ['not in',$goods_ids];
                    $res = $card_goods_model->where($where)->delete();
                    echo $card_goods_model->getLastSql().'-'.$res.'<br/>';
                    foreach ($v as $vv){
                        unset($where['commodity_id']);
                        $where['commodity_id'] = $vv['commodity_id'];
                        $row = $card_goods_model->getOne(['where'=>$where]);
                        if(!$row){
                            $data[] = ['commodity_id'=>$vv['commodity_id'],'commodity_set_id'=>$vv['commodity_set_id'],'class_id'=>$kk_arr[1],'card_id'=>$kk_arr[0]];
                        }
                    }
                }
                $res = $card_goods_model->insertAll($data);
                echo $card_goods_model->getLastSql().'-'.$res.'<br/>';
            }

        }



    }

}
