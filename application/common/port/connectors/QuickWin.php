<?php

namespace app\common\port\connectors;

use app\common\model\db\DbJobsLog;
use app\common\model\db\DbLog;
use GuzzleHttp\Exception\RequestException;
use think\Cache;
use tool\Logger;

/**
 * 速赢卡券接口
 * Class QuickWin
 * @package app\common\port\connectors
 */
class QuickWin extends Connect
{

    private $prefix = '';
    private $prefix_tob = 'java-coupon-middle-tob-service/v1/';
    private $prefix_toc = 'java-coupon-middle-toc-service/v1/';
    private $prefix_toc_v2 = 'java-coupon-middle-toc-service/v2/';
    private $prefix_toc_v3 = 'java-coupon-middle-toc-service/v3/';
    private $prefix_toc_sync = 'java-coupon-middle-toc-service/e3s/coupon/';
    /**
     * @return array
     */
    protected function createToken(): array
    {
        return [
            'token'   => 'from_nissan_pusher',
            'timeout' => 7200
        ];
    }

    public function rpc($method, $httpUri, $param, $headers = [])
    {
        if ($method == 'POST') {
            $headers['Content-Type'] = 'application/json';
            $options['headers']      = $headers;
            return $this->send($method, $httpUri, $param, $options);
        }
        return $this->send($method, $httpUri, $param);
    }

    /*
 * 编辑卡券关联商品和描述信息
 * */
    public function refreshMallProduct($params = [])
    {
        return $this->postJson($this->prefix_toc_sync . 'refreshMallProduct', $params);
    }

    /*
     * 卡券中心结算规则列表
     * */
    public function getCouponSettlementRuleList($params = [])
    {
        return $this->get($this->prefix_tob . 'coupon-settlement-rule', $params);
    }

    /*
     * 新增卡券
     * */
    public function addCouponInfo($params = [])
    {
        return $this->postJson($this->prefix_tob . 'coupon-info', $params);
    }

    /*
     * 卡券信息
     * */
    public function getCouponInfo($id)
    {
        return $this->get($this->prefix_tob . 'coupon-info/' . $id);
    }

    /*
     * 更新卡券
     * */
    public function updateCouponInfo($id, $params = [])
    {
        return $this->putJson($this->prefix_tob . 'coupon-info/' . $id, $params);
    }

    /*
     * 启用、禁用卡券
     * */
    public function postCouponInfo($id)
    {
        return $this->postJson($this->prefix_tob . 'coupon-info/' . $id);
    }

    /*
     * 领取卡券
     * */
    public function postCouponReceive($params = [])
    {
        return $this->postJson($this->prefix_toc . 'receive-coupon', $params);
    }

    /*
     * 核销卡券
     * */
    public function postCouponConsume($params = [])
    {
        return  $this->postJson($this->prefix_toc . 'consume-coupon', $params);

    }

    /*
     * 卡券冻结
     * */
    public function postCouponFreezeCoupon($params = [])
    {
        return $this->postJson($this->prefix_toc . 'reserve-freeze/freezeCoupon', $params);
    }

    /*
     * 卡券解冻
     * */
    public function postCouponUnfreezeCoupon($params = [])
    {
        return $this->postJson($this->prefix_toc . 'reserve-freeze/unfreezeCoupon', $params);
    }

    /*
     * 卡券失效
     * */
    public function postCouponInvalid($params = [])
    {
        return $this->postJson($this->prefix_toc . 'coupon-revive-or-invalid/invalid', $params);
    }

    /*
     * 卡券复活
     * */
    public function postCouponRevive($params = [])
    {
        return $this->postJson($this->prefix_toc_v2 . 'coupon-revive-or-invalid/revive', $params);
    }

    /*
 * 卡券返核销
 * */
    public function postCouponReviveV3($params = [])
    {
        return $this->postJson($this->prefix_toc_v3 . 'coupon-revive-or-invalid/revive', $params);
    }

    /*
     * 领券记录
     * */
    public function getCouponReceiveRecord($params = [])
    {
        return $this->get($this->prefix_tob . 'coupon-receive-record', $params);
    }

    /*
 * 领券记录--通过多入参
     * coupon_id_list
     * vin
     * coupon_code_list
     *
 * */
    public function getCouponReceiveRecordMore($params = [])
    {
        return $this->postJson($this->prefix_tob . 'coupon-receive-record/list', $params);
    }

    /*
     * 更新coupon_receive_record记录
     * */
    public function couponReceiveRecord($params = [])
    {
        return $this->put($this->prefix_tob . 'coupon-receive-record/'.$params['id'], $params);
    }


    /**
     * 卡券中心结算信息接口
     * @param $params
     * @return array|mixed|string
     */
    public function saveSubsidyInfo($params)
    {
        $result = $this->postJson($this->prefix_tob . 'subsidy-info/save', $params);
        Logger::info('saveSubsidyInfo', json_encode(['param' => $params, 'result' => $result]));
        return $result;
    }



    /*
     * 批量领取卡券(存在用户重复校验)
     * */
    public function receiveCouponBatch($params = [])
    {
        return $this->postJson($this->prefix_toc . 'receive-coupon/batch', $params);
    }


    /**
     * 批量更换冻结卡券业务单号
     * @param $params
     * @return array|mixed|string
     */
    public function freezeCouponChange($params)
    {
        return $this->postJson($this->prefix_toc . 'coupon-mall/freezeCouponChange', $params);
    }





    /*
     * e3s经销商
     * */
    public function postDlr($params = ['rows' => 10], $brand = 1)
    {
        $headers = $this->config['cashier'];
        if ($brand == 2) {
            $headers['appCode']  = 'venucia';
            $headers['clientid'] = 'venuciaapp';
        }
        return $this->postJson($this->prefix . 'ly.mp.sy.dealer.main/ly/online/sale/dealer/list', $params, ['headers' => $headers]);
    }
    /*
     * 启辰经销商列表
     * */
    public function postDlrQc($params = [], $header = [])
    {
        $headers = $this->config['cashier'];
        $headers = array_merge($headers, $header);
        $headers['sign'] =hash('sha512',$headers['clientid'].$headers['timestamp'].$headers['token'].$headers['noncestr'].$headers['range'].$headers['uuid'].json_encode($params));

        return $this->postJson('vmsp-repair/ly/dfn/appMaintainAndRepair/getDlrListSupportSort', $params, ['headers' => $headers]);
    }

    /*
     * 启辰 根据oneid获取车辆信息
     * */
    public function postQcCarInfo($params = [])
    {
        $params['clientid'] = "nissanapp";
        $params['appCode']  = "nissan";

        $headers = $this->config['cashier'];
        return $this->postJson($this->prefix . 'ly-mp-dfn-unioncar-service/ly/dfn/app/unioncar/querycarinfo', $params, ['headers' => $headers]);
    }

    /*
     * 启辰 根据oneid获取车辆信息集合
     * */
    public function postQcCarInfoList($params = [])
    {
        $headers = $this->config['cashier'];
        return $this->postJson($this->prefix . 'ly-mp-dfn-unioncar-service/ly/dfn/app/unioncar/querycarinfolist', $params, ['headers' => $headers]);
    }

    /*
     * 启辰 根据oneid获取车辆信息集合
     * */
    public function test($params = [])
    {
        $headers = $this->config['cashier'];
        return $this->postJson($this->prefix . '/dfn/user/car/getCarinfos', $params, ['headers' => $headers]);
    }

    /*
     * e3s plus接口
     * */
    public function e3sMessage($params)
    {
        $headers['appid']        = $this->config['cashier']['appid'];
        $headers['securetkey']   = $this->config['cashier']['secure_key'];
        $headers['Content-Type'] = 'application/json';
        $params['E3S_CODE']                 = 'ZZYY';
        return $this->post('proxy/e3s/', $params, ['headers' => $headers]);
    }

    /*
     * 获取日产app端默认燃油车会员信息
     * */
    public function appMemberInfo($params = [])
    {
        $params['clientid'] = "nissanapp";
        $params['appCode']  = "nissan";

        $headers = $this->config['cashier'];
        return $this->postJson($this->prefix . 'ly-mp-vmsp-feedback-service/vmsp/member/info', $params, ['headers' => $headers]);
    }

    /**
     *
     * @param $params
     */
    public function e3sDlrLevel($params)
    {
        $headers['appid']        = config('port.e3s')['cashier']['appid'];
        $headers['securetkey']   = config('port.e3s')['cashier']['secure_key'];
        $headers['gversion']   = config('port.e3s')['cashier']['gversion'];
        return $this->postJson($this->prefix . 'busiplat-se-salemake-service/Sync/timeAreaSyncMall', $params, ['headers' => $headers]);
    }

    /**
     * APP支付回调
     * @param $params
     * @return mixed|string
     */
    public function appPayCallBack($params)
    {
        $headers['appid']        = $this->config['appid'];
        $headers['securetkey']   = $this->config['secure_key'];
        $headers['Content-Type'] = 'application/json';

        try {
            $result = $this->postJson($this->prefix . 'ly.mp.sy.client.proxy.service/pickupDelivery/pay_call_back', $params, ['headers' => $headers]);

            $requestData = [
                'param' => $params,
                'headers' => $headers
            ];

            Logger::error('QuickWin-appPayCallBack：', json_encode([
                'param' => $params,
                'headers' => $headers,
                'result' => $result
            ]));

            $DbJobsLog = new DbJobsLog();
            $DbJobsLog->insertData([
                'queue'       => 'order',
                'source_type' => 'appPayCallBack',
                'data_info'   => json_encode($requestData, JSON_UNESCAPED_UNICODE),
                'result_info' => json_encode($result, JSON_UNESCAPED_UNICODE),
            ]);


            return $result;
        } catch (RequestException $e) {
            $msg = $e->getMessage();
            Logger::error('QuickWin-appPayCallBack：' . $msg);
            return ['code' => '-1', 'msg' => $msg];
        }
    }

    /**
     * 车联服务包上下架通知OTA
     * */
    public function otaNotice($params = [], $header = []){
        $headers = $this->config['cashier'];
        $headers = array_merge($headers, $header);
        $headers['sign'] =md5($headers['appid'].$headers['api'].json_encode($params).$headers['noncestr'].$headers['timestamp'].$headers['appkey']);
        return $this->postJson('', $params, ['headers' => $headers]);
    }

    /**
     * 成长值发放
     * */
    public function growthValue($params = []){
        return $this->postJson($this->prefix . 'wxmp-msg-service/send/growth-value', $params);
    }

    /**
     * 成长值发放--启辰
     * */
    public function growthValueVenucia($params = []){
        return $this->postJson($this->prefix . 'wxmp-msg-service/venucia/send/growth-value', $params);
    }

    /**
     * 商城卡券自动领取
     * @param $oneId
     * @return array|mixed|string
     */
    public function mallCrowd($oneId)
    {
        $params = [
            'oneid' => $oneId
        ];
        return $this->postJson($this->prefix_toc . 'coupon-grant/mall-crowd', $params);
    }

    /**
     * 省广接口
     * */
    public function wangDian($params = [])
    {
        $params['appId'] = $this->config['cashier']['appId'];
        $params['timestamp'] = $this->config['cashier']['timestamp'];
        $params['accessToken'] = md5($params['appId'] . $this->config['cashier']['secret'] . $params['timestamp']);
        $re = $this->postJson('', $params);
        DbLog::create(
            [
                'type' => $params['relativeUrl'],
                'is_success' => (isset($re['code']) && $re['code'] == 0) ? 1 : 0,
                'send_note' => json_encode_cn($params),
                'receive_note' => json_encode_cn($re),
            ]
        );
        return $re;
    }

    /**
     * 网联接口 -- 用户认证、授权车
     * */
    public function getCarInfos($params = []){
        $data = $this->postJson('certification-middle-service/ly/platform/user/carinfos', $params);
        return empty($data['rows']) ? [] : $data['rows'];
    }
    /**
     * 网联接口 -- 用户授权信息
     * */
    public function authorCode($params = []){
        $data = $this->postJson('certification-middle-service/ly/platform/user/authorCode', $params);
        return empty($data['rows']) ? [] : $data['rows'];
    }
    public function getUserIdByOneId($oneId)
    {
        $cacheKey = "ccs_uuid_convert:{$oneId}";
        if (Cache::has($cacheKey)) {
            $userId = Cache::get($cacheKey);
        } else {
            $options['headers'] = ['api' => 'ly.ccsv.external.user.v2.getuseridbyuuid'];
            $result             = $this->send('POST', 'https://vitappzh.venucia.com/v3/api', ['uuid' => $oneId], $options);
            if (empty($result['userId'])) {
                return 0;
            } else {
                $userId = $result['userId'];
            }
            Cache::remember($cacheKey, $result['userId']);
        }
        return $userId;
    }

    /**
     * 充电桩事件通知
     * */
    public function chargeApply($params = []){
        $headers = $this->config['cashier'];
        $headers['api'] = 'ly.dfv.chargepile.datamall.apply';
        $headers['sign'] =md5($headers['appid'].$headers['api'].json_encode($params).$headers['noncestr'].$headers['timestamp'].$headers['appkey']);
        unset($headers['appkey']);
        return $this->postJson('', $params, ['headers' => $headers]);
    }

    /**
     * 查询是否员工
     * */
    public function mdmOrgEmployeeQueryFindAll($params = []){
        return $this->postJson($this->prefix . 'busiplat-usc-org-service/employee/mdmOrgEmployeeQueryFindToC', $params);
    }

    /**
     * 助农订单推送
     * */
    public function zhuNong($params){
        return $this->postJson('https://ard-ytest.tuzililei.com/orderSync/orderData', $params);
    }

    /*
    * 已核销转未核销且待激活
    * */
    public function postBatchUnConsumeUnactive($params = [])
    {
        return  $this->postJson($this->prefix_toc . 'coupon-activity/batch-unConsume-unactive', $params);
    }


    /**
     * 核销详情
     * @param $couponCodeArr
     * @return array|mixed|string
     */
    public function consumeList($couponCodeArr)
    {
        $params = [
            'coupon_codes' => $couponCodeArr
        ];
        $url = 'coupon-receive-record/findReceiveRecordStatus';
        return  $this->postJson($this->prefix_tob . $url, $params);
    }


}
