<?php

namespace app\common\port\connectors;

use app\common\model\db\DbJobsLog;
use app\common\model\db\DbLog;
use GuzzleHttp\Exception\RequestException;
use think\Cache;
use tool\Logger;

/**
 * 速赢卡券接口
 * Class QuickWin
 * @package app\common\port\connectors
 */
class Supplier extends Connect
{

    private $prefix = '';
    /**
     * @return array
     */
    protected function createToken(): array
    {
        return [
            'token'   => 'from_nissan_pusher',
            'timeout' => 7200
        ];
    }

    public function rpc($method, $httpUri, $param, $headers = [])
    {
        if ($method == 'POST') {
            $headers['Content-Type'] = 'application/json';
            $headers['appid'] = 'mall1509871386';
            $headers['appsecret'] = 'e5bd9084-2481-409a-99c6-16abe3ff960e';
            $options['headers']      = $headers;
            return $this->postJson( $httpUri, $param, $options);
        }
        return $this->send($method, $httpUri, $param);
    }


    /**
     * 查询供应商订单信息（根据提供的参数获取订单详情）
     * @param array $params 请求参数，如订单ID或其他筛选条件
     * @return mixed 返回订单详细信息
     */
    public function supplierOrderInfo($params = [])
    {
        $res =  $this->rpc('POST',$this->prefix . 'mall/order/supplier/order-info', $params);
        return  $res;
    }

    /**
     * 供应商提交订单
     * @param array $params
     * @return mixed
     */
    public function submitSupplierOrder($params = [])
    {
        $res =  $this->rpc('POST',$this->prefix . 'mall/order/supplier/order/save', $params);
        return  $res;
    }

    /**
     * 更新供应商订单信息
     * @param array $params
     * @return mixed
     */
    public function updateSupplierOrder($params = [])
    {
        return $this->rpc('POST',$this->prefix . 'mall/order/supplier/order/update', $params);
    }





    /**
     * 查询供应商发货单
     * @param array $params
     * @return mixed
     */
    public function getSupplierDeliveryInfo($params = [])
    {
        return $this->rpc('POST',$this->prefix . 'mall/order/supplier/delivery-info', $params);
    }

    /**
     * 查询供应商订单商品
     * @param array $params
     * @return mixed
     */
    public function getSupplierOrderCommodity($params = [])
    {
        return $this->rpc('POST',$this->prefix . 'mall/order/supplier/commodity', $params);
    }

    /**
     * 更新供应商售后
     * @param array $params
     * @return mixed
     */
    public function updateSupplierAfterSale($params = [])
    {
        return $this->rpc('POST',$this->prefix . 'mall/order/supplier/after-sale/update', $params);
    }

    /**
     * 提交供应商售后
     * @param array $params
     * @return mixed
     */
    public function saveSupplierAfterSale($params = [])
    {
        return $this->rpc('POST',$this->prefix . 'mall/order/supplier/after-sale/save', $params);
    }

    /**
     * 取消供应商售后
     * @param array $params
     * @return mixed
     */
    public function cancelSupplierAfterSale($params = [])
    {
        return $this->rpc('POST',$this->prefix . 'mall/order/supplier/after-sale/cancel', $params);
    }


}
