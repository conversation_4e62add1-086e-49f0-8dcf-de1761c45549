<?php

/*** Created by demo.
 * PROJECT：php-wxmp-dealers
 * User: <EMAIL>
 * Date: 2025/1/19
 * Time: 20:21
 * remark:
 */


namespace app\common\port\connectors;

use app\common\model\db\InvoiceLogModel;
use think\Model;
use tool\Logger;

class InvoiceHX extends Connect
{
    /**
     * 开票接口
     * @param $params
     * @return array|mixed|string
     */
    public function Invoice($params)
    {
        $url = 'salescore/lan/invoice.do';

        $log = new InvoiceLogModel();
        try {
            $log->request_url = $url;
            $log->request_params = json_encode_cn($params);

            $result = $this->postJson($url, $params);
            Logger::info('Invoice.do', json_encode(['params' => $params, 'return' => $result]));
            $log->response = json_encode_cn($result);
            $log->order_no = $params['orderNo'];


        } catch (\Exception $exception) {
            $request_data = [
                'code' => '9999',
                'message' => '同步失败的异常信息',
            ];
            $log->response = json_encode_cn($request_data);
        }
        $log->save();

        return $result;
    }

    /**
     * 红字确认单接口
     * @param $params
     * @return array|mixed|string
     */
    public function apply($params)
    {
        $url = 'salescore/lan/redConfirm/apply.do';

        $log = new InvoiceLogModel();
        try {
            $log->request_url = $url;
            $log->request_params = json_encode_cn($params);

            $result = $this->postJson($url, $params);
            Logger::info('apply.do', json_encode(['params' => $params, 'return' => $result]));
            $log->response = json_encode_cn($result);

        } catch (\Exception $exception) {
            $request_data = [
                'code' => '9999',
                'message' => '同步失败的异常信息',
            ];
            $log->response = json_encode_cn($request_data);
        }
        $log->save();

        return $result;
    }

    /**
     * 3.45发票交付接口，用于提供给企业对诺税通中开具的发票进行交付，具体的短信、邮件发送通道根据不同企业配置决定。
     * @param $params
     * @return array|mixed|string
     */
    public function delivery($params)
    {
        $url = 'salescore/lan/invoice/delivery.do';

        $log = new InvoiceLogModel();
        try {
            $log->request_url = $url;
            $log->request_params = json_encode_cn($params);

            $result = $this->postJson($url, $params);
            Logger::info('delivery.do', json_encode(['params' => $params, 'return' => $result]));
            $log->response = json_encode_cn($result);

        } catch (\Exception $exception) {
            $request_data = [
                'code' => '9999',
                'message' => '同步失败的异常信息',
            ];
            $log->response = json_encode_cn($request_data);
        }
        $log->save();

        return $result;
    }

    /**
     * 2.27.1 快捷冲红接口
     * 支持直接请求蓝字发票直接触发开具对应的红字发票或红字信息表。
     * 场景1：增值税普通发票全额冲红时，红票开具的快捷入口，红票回传地址优先使用接口传入，未传入使用蓝票回传地址
     * 场景2：增值税专用发票全额冲红时，红字信息表申请的快捷入口
     * 场景3：数电发票部分、全额冲红时，开具数电红票的入口，红票回传地址优先使用接口传入，未传入使用蓝票回传地址
     * @param $params
     * @return array|mixed|string
     */
    public function fastRepeatedRedSingle($params)
    {
        $url = 'salescore/lan/fastRepeatedRedSingle.do';

        $log = new InvoiceLogModel();
        try {
            $log->request_url = $url;
            $log->request_params = json_encode_cn($params);

            $result = $this->postJson($url, $params);
            Logger::info('fastRepeatedRedSingle.do', json_encode(['params' => $params, 'return' => $result]));
            $log->response = json_encode_cn($result);

        } catch (\Exception $exception) {
            $request_data = [
                'code' => '9999',
                'message' => '同步失败的异常信息',
            ];
            $log->response = json_encode_cn($request_data);
        }
        $log->save();

        return $result;
    }

    /**
     * 开票结果查询接口:
     * 通过该接口提交流水号或订单号信息，查询开票结果
     * @param $params [
     * invoiceId array 流水号列表与订单号列表、发票号码列表、数电号码四选一，单次最多支持查询50个流水号。
     * orderNo  array 订单号列表
     * invoiceNumber array 发票号码列表
     * invoiceCode string  发票代码
     * fuzzyFlag bool 订单号是否模糊查询，当orderNo列表元素个数为1且本字段为true时，可以模糊查询订单号对应的发票记录
     * sellerTaxnum string 企业税号和组织编码必填其一
     * companyCode string 组织编码
     * returnInvoiceDetailFlag bool 返回发票明细标识
     * allElectronicInvoiceNumber   array 数电发票号码（数电电票、数电纸票都支持
     * invoiceType int 非必填 0.全部 1:蓝票；2:红票
     * billId string 红字确认单申请单号 当发票种类=2：红票时，确认单申请单号与流水号列表、订单
     * ]
     * @return array
     *
     */
    public function queryInvoice($params)
    {
        $url = 'salescore/lan/query-invoice.do';

        $log = new InvoiceLogModel();
        $result = [];
        try {
            $log->request_url = $url;
            $log->request_params = json_encode_cn($params);
            $log->creator = 'port';
            $result = $this->postJson($url, $params);
            Logger::info('query-invoice.do', json_encode(['params' => $params, 'return' => $result]));
            $log->response = json_encode_cn($result);

        } catch (\Exception $exception) {
            $request_data = [
                'code' => '9999',
                'message' => '同步失败的异常信息',
            ];
            $log->response = json_encode_cn($request_data);
        }
        $log->save();

        return $result;
    }




    /**
     * 删除失败的订单
     * @param $sellerTaxNum
     * @param $orderNo
     * @return array|mixed|string
     */
    public function deleteInvoice($sellerTaxNum, $orderNo)
    {
        $params = [
            'sellerTaxnum' => $sellerTaxNum,
            'valueType' => 1, // 订单编号
            'value' => $orderNo
        ];
        $url = 'salescore/lan/invoiceList/delete.do';
        $log = new InvoiceLogModel();
        try {
            $log->request_url = $url;
            $log->request_params = json_encode_cn($params);
            $log->order_no = $orderNo;

            $result = $this->postJson($url, $params);
            Logger::info('delete.do', json_encode(['params' => $params, 'return' => $result]));
            $log->response = json_encode_cn($result);

        } catch (\Exception $exception) {
            $request_data = [
                'code' => '9999',
                'message' => '删除失败',
            ];
            $log->response = json_encode_cn($request_data);
        }
        $log->save();

        return $result;

    }



    // 3.48 红字确认单查询接口
    public function redConfirmQuery($redRecord)
    {
        $params = [
            'taxNum' => $redRecord['seller_tax_num'], // 税号
            'applySource' => 0,
            'billId' => $redRecord['red_invoice_id'],
        ];
        $url = 'salescore/lan/redConfirm/query.do';
        $log = new InvoiceLogModel();
        $result = [];
        try {
            $log->request_url = $url;
            $log->request_params = json_encode_cn($params);
            $log->order_no = $redRecord['order_no'];

            $result = $this->postJson($url, $params);
            Logger::info('redConfirm-query', json_encode(['params' => $params, 'return' => $result]));
            $log->response = json_encode_cn($result);

        } catch (\Exception $exception) {
            $request_data = [
                'code' => '9999',
                'message' => '获取红字确认单详情失败',
            ];
            $log->response = json_encode_cn($request_data);
        }
        $log->save();

        return $result;

    }
}