<?php


namespace app\common\port\connectors;


use app\common\model\db\DbLog;

class JavaNewMedia extends Connect
{

    private $options;
    private $log_model;

    public function __construct()
    {

        $config          = config('port')['java_new_media'];
        $this->log_model = new DbLog();

        $gVersion      = $config['gversion'];
        $this->options = [];
        if (!empty($gVersion)) {
            $this->options['headers']['gversion'] = $gVersion;
        }
        $this->options['headers']['appid'] = $config['app_id'];
        $this->options['headers']['appsecret'] = $config['app_secret'];
        $this->options['headers']['clientid'] = $config['client_id'];
        $this->options['headers']['brandid'] = $config['brand_id'];

    }


    // E3S-抖音-到店代金券查询
    public function orderCardList($dlrCode, $orderCode, $vin)
    {
        $params = [
            "dlr_code" => $dlrCode,
            "order_code" => $orderCode,
            'vin' => $vin
        ];
        $add = [
            'type' => 'new_media',
            'send_note' => json_encode_cn($params),
            'order_code' => !empty($orderCode)?$orderCode:$vin,
            'creator' => '查询',
        ];
        $logId = $this->log_model->insertGetId($add);
        $url = 'third/order/order-card-list';
        $result = $this->postJson($url, $params, $this->options);
        $is_success = 0;
        if (isset($result['result']) && $result['result'] == 1) {
            $is_success = 1;
        }
        $upd = [
            'is_success' => $is_success,
            'receive_note' => json_encode_cn($result)
        ];
        $this->log_model->where('id', $logId)->update($upd);
        return $result;
    }


    // 代金券核销
    public function cardConsume($dlrCode, $orderCode)
    {
        $params = [
            "dlr_code"   => $dlrCode,
            "order_code" => $orderCode
        ];
        $add = [
            'type' => 'new_media',
            'send_note' => json_encode_cn($params),
            'order_code' => $orderCode,
            'creator' => '核销',
        ];
        $logId = $this->log_model->insertGetId($add);
        $url    = 'third/order/order-card-consume';
        $result = $this->postJson($url, $params, $this->options);
        $is_success = 0;
        if (isset($result['result']) && $result['result'] == 1) {
            $is_success = 1;
        }
        $upd = [
            'is_success'   => $is_success,
            'receive_note' => json_encode_cn($result)
        ];
        $this->log_model->where('id', $logId)->update($upd);
        return $result;
    }

    // 状态同步
    public function cardConsumeStatus($params)
    {
        $add = [
            'type' => 'new_media',
            'send_note' => json_encode_cn($params),
            'order_code' => $params['ORDER_CODE'],
            'creator' => '状态同步',
        ];
        $logId = $this->log_model->insertGetId($add);
        $url    = 'third/order/order-card-consume-status';
        $result = $this->postJson($url, $params, $this->options);
        $is_success = 0;
        if (isset($result['result']) && $result['result'] == 1) {
            $is_success = 1;
        }
        $upd = [
            'is_success'   => $is_success,
            'receive_note' => json_encode_cn($result)
        ];
        $this->log_model->where('id', $logId)->update($upd);
        return $result;
    }

}