<?php


namespace app\common\port\connectors;

//e3s库相关接口
use app\common\model\db\DbLog;

use GuzzleHttp\Exception\RequestException;
use tool\Logger;

class E3s extends Connect
{
//    /**
//     * E3S相关
//     * @param $code
//     * @return mixed
//     */
//    private function getPath($code)
//    {
//        // 白名单列表
//        $api_url = [
//            // 工时价格
//            'QueryWorkTimePrice' => 'rest/busiplat-se-salemake-service/message/Message?FunctionCode=QueryWorkTimePrice',
//            // 根据vin查询18位码
//            'QueryCarTypeByVin' => 'rest/busiplat-se-salemake-service/message/Message?FunctionCode=QueryCarTypeByVin',
//            'QueryMdmCarVIN' => 'rest/busiplat-se-salemake-service/message/Message?FunctionCode=QueryMdmCarVIN',
//        ];
//
//        return $api_url[$code];
//    }



//
//    /**
//     * @param $code
//     * @param $parameters
//     * @return array
//     */
//    public function request($code, $parameters)
//    {
//        list($msec, $sec) = explode(' ', microtime());
//        $msecs_start_time       = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
//        try {
//            $parameters['e3s_code'] = 'ZZYY';
//            $parameters['interface_code'] = $code;
//            $header['headers']['appid'] = $this->config['cashier']['appid'];
//            $header['headers']['securetkey'] = $this->config['cashier']['secure_key'];
//            $header['headers']['Content-Type'] = 'application/json';
////            if($code == 'QueryWorkTimePrice')
////            {
////                $ret = [
////                    'RESULT_CODE' => 0,
////                    'RESULT_DESC' => '成功',
////                    'DATA' => [
////                        'WI_PRICE' => '100.00'
////                    ],
////                ];
////            } else {
//            $ret = $this->send('POST',$this->getPath($code),array_change_key_case($parameters,CASE_UPPER),$header);
////            }
//
//        } catch (RequestException $e) {
//            $this->log($this->getPath($code) . '异常', ['params' => $parameters], 'error');
//            return $this->return(1, 'E3S接口异常');
//        }
//        if ($ret['RESULT_CODE'] == 0) {
//            return $this->return($ret['RESULT_CODE'], $ret['RESULT_DESC'], $this->arraykeyToLower($ret['DATA']));
//        } else {
//            $this->log($this->getPath($code), ['params' => $parameters, 'return' => $ret]);
//            list($msec, $sec) = explode(' ', microtime());
//            $msecs_end_time       = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
//            return $this->return(1, 'E3S接口异常');
//        }
//    }
//
//    /*
//         * 将数组中，大写的键名转换为小写
//         */
//    public static function arraykeyToLower( $data = array() )
//    {
//        if( !empty($data) )
//        {
//            //一维数组
//            if(count($data) == count($data,1))
//            {
//                $data = array_change_key_case($data , CASE_LOWER);
//            }
//            else    //二维数组
//            {
//                foreach( $data as $key => $value )
//                {
//                    $data[$key] = array_change_key_case($value , CASE_LOWER);
//                }
//            }
//        }
//        return $data;
//    }


    private $prefix;
    private $prefix_g;
    private $options;

    public function __construct()
    {

        $config                                 = config('port')['pz1a'];
        $gVersion                               = $config['gversion'];
        $this->prefix                           = $config['prefix'];
        $this->prefix_g                           = $config['prefix']['g'];
        $this->options['headers']['appid']      = $config['app_id'];
        $this->options['headers']['securetkey'] = $config['secure_key'];
        if (!empty($gVersion)) {
            $this->options['headers']['gversion'] = $gVersion;
        }

    }



    public function queryCarTypeByVin($params)
    {
        try {
            $result = $this->postJson($this->prefix['d'] . 'message/Message?FunctionCode=QueryCarTypeByVin', $params, $this->options);
            Logger::info('QueryCarTypeByVin:', json_encode(['param' => $params, 'result' => $result]));
            return $result;
        } catch (RequestException $e) {
            $msg = $e->getMessage();
            Logger::error('QueryCarTypeByVin:' . $msg);
            return ['code' => '-1', 'msg' => $msg];
        }
    }

    public function triggerJifenSend($params)
    {
        try {
            $url = $this->prefix_g . 'ly/mgs/task/checktasklabel';
            $result = $this->postJson($url, $params, $this->options);
            $result['http_code'] = $this->getStatusCode();
            Logger::info('triggerJifenSend:', json_encode(['param' => $params, 'result' => $result]));
            return $result;

        } catch (RequestException $e) {
            $msg = $e->getMessage();
            Logger::error('triggerJifenSend-error:' . $msg);
            return false;
            // return [
            //     'http_code' => $http_code,
            //     'data' => ['code' => '-1', 'msg' => $msg]
            // ];
        }
    }


}
