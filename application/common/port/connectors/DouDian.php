<?php


namespace app\common\port\connectors;


class DouDian extends Connect
{


    /**
     * 获取商品列表
     * @param $page
     * @param $per_page
     * @return array|mixed|string
     */
    public function getGoodsList($page, $per_page)
    {
        $url                = '/api/net-small/get-goods-list';
        $timestamp          = time();
        $params             = [
            'page'     => (string)$page,
            'per_page' => (string)$per_page,
        ];
        $sign               = $this->sign($timestamp, $params);
        $options['headers'] = [
            'sign'      => $sign,
            'timestamp' => $timestamp,
        ];
        return $this->get($url, $params, $options);
    }

    public function sign($timestamp, $params)
    {
        $this->recSort($params);
        $param_json = json_encode($params);
        $config     = config('port')['dou_dian'];
        $key        = $config['cashier']['key'];
        return md5(md5($timestamp . $param_json . $key) . substr($key, 2, 8));
    }

    private function recSort(array &$arr)
    {
        $kstring = true;
        foreach ($arr as $k => &$v) {
            if (!is_string($k)) {
                $kstring = false;
            }
            if (is_array($v)) {
                $this->recSort($v);
            }
        }
        if ($kstring) {
            ksort($arr);
        }
    }

}