<?php


namespace app\common\model\new_media;


use app\common\model\Common;

class NewMediaOrderCommodity extends Common
{
    // 商品类型
    public static $commodityType = [
        'GROUP'   => '团购套餐',
        'CARD'    => '次卡',
        'VOUCHER' => '代金券',
        'PICKUP'  => '提货券',
    ];

    // 备件类型
    public static $partType = [
        'MAINTAIN' => '保养',
        'UPKEEP'   => '维修',
    ];

    // 工时类别
    public static $wiType = [
        'MAINTAIN' => '保养',
        'UPKEEP'   => '维修',
    ];

    protected $table = 't_new_media_order_commodity';
    protected $pk = 'id';
    protected $createTime = 'created_date';
    protected $updateTime = 'modified_date';



}