<?php


namespace app\common\model\new_media;


use app\common\model\Common;

class NewMediaCommoditySpareParts extends Common
{
    protected $table = 't_new_media_commodity_spare_parts';
    protected $pk    = 'id';

    protected $createTime = 'created_date';
    protected $updateTime = 'modified_date';



    public function getSparePartsPriceAttr($value)
    {
        return number_format($value/100, 2);
    }


    public function getSparePartsActivityPriceAttr($value)
    {
        return number_format($value/100, 2);
    }

}