<?php

namespace app\common\model\new_media;

use app\common\model\Common;

class NewMediaKsCommodity extends Common
{

    // 商品类型
    static public $product_type_arr = [
        1 => '团购',
        11 => '代金券',
        21 => '次卡',
        31 => '日历票',
        41 => '兑换券'
    ];

    // 商品状态
    static public $commodity_status_arr = [
        1 => '正常',
        2 => '下架',
        3 => '售罄',
        5 => '删除'
    ];

    // 商品审核状态
    static public $check_status_arr = [
        1 => '审核中',
        2 => '审核通过',
        4 => '审核驳回'
    ];


    public function getProductTypeTextAttr($value, $data)
    {
        return self::$product_type_arr[$data['product_type']] ?? '';
    }


    public function getCommodityStatusTextAttr($value, $data)
    {
        return self::$commodity_status_arr[$data['commodity_status']] ?? '';
    }


    public function getCheckStatusTextAttr($value, $data)
    {
        return self::$check_status_arr[$data['check_status']] ?? '';
    }



    protected $table = 't_new_media_ks_commodity';
    protected $pk = 'id';

}