<?php


namespace app\common\model\new_media;


use app\common\model\Common;

class NewMediaCommodityWorkingHours extends Common
{
    protected $table = 't_new_media_commodity_working_hours';
    protected $pk    = 'id';

    protected $createTime = 'created_date';
    protected $updateTime = 'modified_date';



    public function getWorkingHoursPriceAttr($value)
    {
        return number_format($value/100, 2);
    }


    public function getWorkingHoursActivityPriceAttr($value)
    {
        return number_format($value/100, 2);
    }
}