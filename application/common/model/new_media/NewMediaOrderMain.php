<?php


namespace app\common\model\new_media;


use app\common\model\Common;

class NewMediaOrderMain extends Common
{

    // 端
    public static $clientId = [
        'dynissanminiapp' => '抖音',
    ];

    // 映射渠道来源
    public static $channelSource = [
        'DOU_YIN'   => '抖音',
        'KUAI_SHOU' => '快手',
    ];

    // 渠道和端映射
    public static $channelSourceClientId = [
        'DOU_YIN'   => ['dynissanminiapp'],
        'KUAI_SHOU' => []
    ];


    // 订单来源
    public static $orderSource = [
        'TO_STORE_PART'    => '到店备件',
        'TO_STORE_VOUCHER' => '到店代金券',
    ];


    // 订单类型
    public static $orderType = [
        'GROUP'   => '团购套餐',
        'CARD'    => '次卡',
        'VOUCHER' => '代金券',
    ];

    // 支付方式
    public static $paymentWay = [
        'CASH' => '现金',
    ];


    protected $table = 't_new_media_order_main';
    protected $pk = 'id';

    protected $createTime = 'created_date';
    protected $updateTime = 'modified_date';


}