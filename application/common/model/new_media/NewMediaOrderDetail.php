<?php


namespace app\common\model\new_media;


use app\common\model\Common;

class NewMediaOrderDetail extends Common
{

    // 订单状态
    public static $orderStatus = [
        'WAIT_PAY'          => '已下单',
        'FINISHED_PAY'      => '已支付',
        'VERIFICATION'      => '已核销',
        'INSTALLING'        => '安装中',
        'COMPLETED'         => '已完成',
        'CLOSED'            => '交易关闭',
        'FAIL_VERIFICATION' => '核销失败',
        'CANCELED'          => '已取消',
    ];

    // 交付方式
    public static $orderDeliveryType = [
        'PICKUP' => '自提',
    ];


    protected $table = 't_new_media_order_detail';
    protected $pk = 'id';

    protected $createTime = 'created_date';
    protected $updateTime = 'modified_date';



    // 次卡原价
    public function getCardPriceAttr($value)
    {
        return number_format($value/100, 2);
    }


    // 代金券面值
    public function getVoucherValueAttr($value)
    {
        return number_format($value/100, 2);
    }


    public function getTotalAmountAttr($value)
    {
        return number_format($value/100, 2);
    }

    public function getActualPaymentAmountAttr($value)
    {
        return number_format($value/100, 2);
    }


    public function getTotalDiscountAmountAttr($value)
    {
        return number_format($value/100, 2);
    }

    public function getTotalPlatformDiscountAmountAttr($value)
    {
        return number_format($value/100, 2);
    }


    public function getTotalMfrsDiscountAmountAttr($value)
    {
        return number_format($value/100, 2);
    }


    public function getTotalMfrsSubsidyAmountAttr($value)
    {
        return number_format($value/100, 2);
    }



    // 商品备件原价
    public function getPartPriceAttr($value)
    {
        return number_format($value/100, 2);
    }


    // 商品备件活动价
    public function getPartActivityPriceAttr($value)
    {
        return number_format($value/100, 2);
    }


    // 商品备件原价总价
    public function getTotalPartPriceAttr($value)
    {
        return number_format($value/100, 2);
    }


    // 商品备件活动总价
    public function getTotalPartActivityPriceAttr($value)
    {
        return number_format($value/100, 2);
    }


    // 商品备件实付金额
    public function getTotalPartActualPaymentAttr($value)
    {
        return number_format($value/100, 2);
    }


    // 商品备件优惠金额
    public function getTotalPartDiscountAttr($value)
    {
        return number_format($value/100, 2);
    }



    // 商品工时原价
    public function getWiPriceAttr($value)
    {
        return number_format($value/100, 2);
    }

    // 商品工时活动价
    public function getWiActivityPriceAttr($value)
    {
        return number_format($value/100, 2);
    }

    // 商品工时原价总价
    public function getTotalWiPriceAttr($value)
    {
        return number_format($value/100, 2);
    }


    // 商品工时活动总价
    public function getTotalWiActivityPriceAttr($value)
    {
        return number_format($value/100, 2);
    }


    // 商品工时实付金额
    public function getTotalWiActualPaymentAttr($value)
    {
        return number_format($value/100, 2);
    }


    // 商品工时优惠金额
    public function getTotalWiDiscountAttr($value)
    {
        return number_format($value/100, 2);
    }


}