<?php
/**
 * 公共model类
 * @author: xiepeihui
 * @date: 2017-03-13
 */
namespace app\common\model;

use tests\thinkphp\library\think\validateTest;
use think\Exception;
use think\Model;
use tool\Logger;

class Common extends Model
{


    /**
     * 统一查询公共方法 使用Tp5自带分页
     *
     * @param array $param
     * @return $list
     */

    protected $_pageSzie=10;
    //验证规则
    protected $rule = [];
    //验证提示消息
    protected $msg  = [];
    // 创建时间字段
    protected $createTime         = false;
    // 更新时间字段
    protected $updateTime         = 'last_updated_date';
    protected $autoWriteTimestamp = 'datetime';
    protected function  initialize(){
        //需要调用`Model`的`initialize`方法
        parent::initialize();
        //TODO:自定义的初始化
        $this->validate($this->rule,$this->msg);
    }




    public function getListPaginate($params=[]){
        $params=$this->_checkParams($params);
        //var_dump($params);
        $list=$this->where($params['where'])->whereOr($params['where_or'])->field($params['field'])->order($params['order'])
                   ->paginate($params['pagesize'],false,array('query'=>$params['query']));
        return $list;
    }

    /**
     * 获取多条数据
     * @param array $param
     * @return $list
     */
    public function getList($params=[]){
        $params=$this->_checkParams($params);
        isset($params['group'])    or $params['group']=[];
        if(isset($params['db-read'])){
            $list = $this->alias('a')->connect(config('database-read'))->where($params['where'])->field($params['field'])->order($params['order'])->group($params['group'])->limit($params['limit'])->select();
        } else {
            $list = $this->alias('a')->where($params['where'])->field($params['field'])->order($params['order'])->group($params['group'])->limit($params['limit'])->select();
        }
        return $list;
    }

    /**
     * 获取单条数据
     * @param $params
     * @return $row
     */
    public function getOne($params=[]){
        isset($params['field'])    or $params['field']='*';
        isset($params['where'])    or $params['where']=[];
        isset($params['order'])    or $params['order']=[];

        if(isset($params['db-read'])){
            $row=$this->connect(config('database-read'))->where($params['where'])
                ->field($params['field'])->order($params['order'])
                ->find();
        } else {
            $row=$this->where($params['where'])->field($params['field'])->order($params['order'])->find();
        }

        return $row;

    }


    /**
     * 通过主键获取
     * @param $pk
     */
    public function getOneByPk($pk){
        if(empty($pk)) return false;
        $row=$this->where([$this->pk=>$pk])->find();
        return $row;
    }

    /***
     * 获取总数 by where
     * @param $params
     * @return $count
     *
     */
    public function getCount($params=[]){
        isset($params['where'])    or $params['where']=[];
        isset($params['_where'])    or $params['_where']=null;
        $count= $this->where($params['where'])->where($params['_where'])->count();
        return $count;
    }

    /***
     * 通过主键获取修改状态
     * @param $pk
     *
     */
    public function changeState($pk){
        if(empty($pk)) return false;
        $where = [$this->pk=>$pk];
        $open= $this->where($where)->value('is_enable');
        if($open==1){
            $n_stat = 0;
        }else{
            $n_stat = 1;
        }
        $s_data = array('is_enable'=>$n_stat);
        $res = $this->where($where)->setField($s_data);
        return array('res'=>$res,'state'=>$n_stat);
    }

    /**
     * 状态颜色
     * */
    public function stateUrl($params){
        if(empty($params['id'])) return false;
        if(empty($params['url'])) return false;
        if(empty($params['text'])) $params['text']='可用';
        if(empty($params['un_text'])) $params['un_text']='不可用';
        if(empty($params['color'])) $params['color']='blue';
        if(empty($params['un_color'])) $params['un_color']='red';
        if($params['is_enable']==1){
            $color = $params['color'];
            $text = $params['text'];
        }else{
            $color = $params['un_color'];
            $text = $params['un_text'];
        }
        $backUrl= sprintf("<a class=\"btn btn-xs %s open-btn\" href='%s' data-id='%s'>%s</a>",$color,$params['url'],$params['id'],$text);
        return $backUrl;

    }

    /**
     * 查询单个字段值，返回数组
     * @param $params
     */
    public function getColumn($params){
        if(empty($params['column'])) return [];
        $params=$this->_checkParams($params);

        if(isset($params['db-read'])){
            $list  =$this->connect(config('database-read'))->where($params['where'])->column($params['column']);
        } else {
            $list  =$this->where($params['where'])->column($params['column']);
        }

        return $list;
    }


    protected function _checkParams($params){


        //自动调用配置文件list_rows
        isset($params['pagesize']) or $params['pagesize']=null;  //自动调用配置文件list_rows
        isset($params['query'])    or $params['query']=[];
        isset($params['field'])    or $params['field']='*';
        isset($params['order'])    or $params['order']=null;
        isset($params['where'])    or $params['where']=[];
        isset($params['where_or']) or $params['where_or']=[];
        isset($params['limit'])    or $params['limit']=null;
        isset($params['group'])    or $params['group'] = null;
        isset($params['count'])    or $params['count'] = false;
        return $params;
    }

    public function isEnable($v=''){

        $data=['0'=>'不可用','1'=>'可用'];
        return $v!=='' ? $data[$v]:$data;


    }

    public function saveData($data,$where){
        $r = false;
        try{
            $r = $this->where($where)->update($data);
        }catch (Exception $e){
            Logger::error($this->table.' model save have error ',array('msg'=>$e->getMessage(),'data'=>$data,'where'=>$where));
        }
        return $r;
    }

    public function insertData($data){
        $r = false;
        try{
            $r = $this->insertGetId($data);
        }catch (Exception $e){
            Logger::error($this->table.' model insertData have error ',array('msg'=>$e->getMessage(),'sql'=>$this->getLastSql(),'data'=>$data));
        }
        return $r;
    }


}
