<?php
/**
 * 卡券
 * @author: lzx
 * @time: 2017年04月11日
 */
namespace app\common\model\db;

use think\Exception;
use tool\Logger;
use app\common\model\Common;

class DbCard extends Common
{
    protected $table = 't_db_card';
    protected $pk    = 'id';

    /**
     * 卡劵类型
     */
    const CARD_TYPE_KEY1 = 1;//卡劵类型--立减券
    const CARD_TYPE_KEY1_VALUE = '立减券';
    const CARD_TYPE_KEY2 = 2;//卡劵类型--折扣劵
    const CARD_TYPE_KEY2_VALUE = '折扣劵';
    const CARD_TYPE_KEY3 = 3;//卡劵类型--兑换券
    const CARD_TYPE_KEY3_VALUE = '兑换券';
    const CARD_TYPE_KEY4 = 4;//卡劵类型--满减券
    const CARD_TYPE_KEY4_VALUE = '满减券';
    const CARD_TYPE_KEY5 = 5;//卡劵类型--优惠券
    const CARD_TYPE_KEY5_VALUE = '优惠券';
    const CARD_TYPE_KEY6 = 6;//卡劵类型--到店代金券
    const CARD_TYPE_KEY6_VALUE = '到店代金券';
    const CARD_TYPE_KEY7 = 7;//卡劵类型--上门取送车券
    const CARD_TYPE_KEY7_VALUE = '上门取送车券';
    const CARD_TYPE_LIST = [
        self::CARD_TYPE_KEY1 => self::CARD_TYPE_KEY1_VALUE,
        self::CARD_TYPE_KEY2 => self::CARD_TYPE_KEY2_VALUE,
        self::CARD_TYPE_KEY3 => self::CARD_TYPE_KEY3_VALUE,
        self::CARD_TYPE_KEY4 => self::CARD_TYPE_KEY4_VALUE,
        self::CARD_TYPE_KEY5 => self::CARD_TYPE_KEY5_VALUE,
        self::CARD_TYPE_KEY6 => self::CARD_TYPE_KEY6_VALUE,
        self::CARD_TYPE_KEY7 => self::CARD_TYPE_KEY7_VALUE,
    ];


    //

    /**
     * 卡券中心结算标准值映射
     * @param $id
     * @return int
     */
    public static function getSettStandardVal($id)
    {
        $arr = [1=>1,2=>4,3=>2,4=>3];
        return $arr[$id] ?? 0;
    }


    /**
     * @param string $id
     * @return string
     */
    public static function sett_standard($id='') {
        $arr = [
            1 => '商城商品单价',
            2 => '实际优惠金额',
            3 => '卡券面值',
            4 => '网点价'
        ];
        return $arr[$id] ?? '';

    }

    /**
     * 结算规则
     * @param $id
     * @return string
     */
    public static function sett_rule_type_text($id)
    {
        $arr = [1=>'固定金额', 2=>'比例结算'];
        return $arr[$id] ?? '';
    }





    public static function getChannel($brand){
        $brand_channel = [
            1=> ['app'=>'GWAPP','mini_app'=>'GWSM','official_website'=>'GWNET'],
            2=> ['app'=>'QCAPP','mini_app'=>'QCSM','official_website'=>'QCNET'],
           // 3=> ['PZ1ASM','PZ1ASM']
        ];
        return $brand_channel[$brand] ?? [];
    }

    public static function getChannelName($brand){
        $brand_channel = [
            1=> ['app'=>'日产APP','mini_app'=>'日产小程序','official_website'=>'日产官网'],
            2=> ['app'=>'启辰APP','mini_app'=>'启辰小程序','official_website'=>'启辰官网'],
            // 3=> ['PZ1ASM','PZ1ASM']
        ];
        return $brand_channel[$brand] ?? [];
    }



    public static function getRange($v='')
    {
        $data=[
            '1'=>'所有人','2'=>'所有车主','3'=>'指定用户','4'=>'指定用户除外',
        ];
        if ($v!='' && !isset($data[$v])) {
            return '';
        }
        return empty($v) ? $data : $data[$v];
    }

    public static function cardType()
    {
        return  self::CARD_TYPE_LIST;
    }

    public static function type()
    {
        return [1=>'微信优惠券',2=>'商城优惠券'];
    }

    public static function cardStatus()
    {
        //优惠券状态,0关闭中;1未开始;2:可使用;3:已过期
        return  [1=>'未开始',2=>'进行中',3=>'已结束'];
    }

    /**
     * 应用类型
     * @return array
     */
    public static function set_type()
    {
        return [1=>'平台',2=>'专营店',3=>'集团'];
    }

    /**
     * 获取活动名称列表
     * @param $params
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getCardActName($params)
    {
        $params=$this->_checkParams($params);
        $list=$this->alias('a')
            ->join('t_bu_card_receive_record b', 'a.id=b.card_id')
            ->where($params['where'])->order($params['order'])->distinct($params['distinct'])
            ->field($params['field'])->select();
        return $list;
    }

    public function getCardPaginate($params)
    {
        $params=$this->_checkParams($params);
        $list=$this->alias('a')
            ->join('t_bu_card_receive_record b', 'a.id=b.card_id', 'left')
            ->join('t_db_dlr c', 'a.dlr_code=c.dlr_code', 'left')
            ->where($params['where'])->field($params['field'])
            ->order($params['order'])->distinct($params['distinct'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }

    /**
     * 导出数据方法 - 查询所有
     * @param $params
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getCardPaginateDown($params)
    {
        $params=$this->_checkParams($params);
        $list=$this->alias('a')
            ->join('t_bu_card_receive_record b', 'a.id=b.card_id', 'left')
            ->join('t_db_dlr c', 'a.dlr_code=c.dlr_code', 'left')
            ->where($params['where'])->order($params['order'])->distinct($params['distinct'])
            ->field($params['field'])->select();
        return $list;
    }

    //卡券关联商品 20170828添加 d.activity_address =''  去掉  GROUP BY  card_id-- 曾毅 增加商品表方便通过分类过滤
    //20180424 增加t_db_commodity_card 判断 ---贱贱
    public function cardGoods($params=[])
    {
        $params=$this->_checkParams($params);
        isset($params['_where'])    or $params['_where']=null;
        isset($params['group'])    or $params['group']=null;
        $list = $this->alias('a')->join("t_db_commodity_card b", "a.id=b.card_id")->join("t_db_commodity_dlr c", "b.commodity_set_id=c.commodity_set_id")->join("t_db_commodity d", "c.commodity_id = d.id")->join("t_db_commodity_set e", "e.id=c.commodity_set_id")->where($params['where'])->where($params['_where'])->field($params['field'])->group($params['group'])->order($params['order'])->limit($params['limit'])->select();
        return $list;
    }


    public function getCardList($params=[])
    {
        $params=$this->_checkParams($params);
        isset($params['_where'])    or $params['_where']=null;
        $list=$this->alias('a')
            ->where($params['where'])->where($params['_where'])
            ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query'=>$params['query']));
        return $list;
    }

    public function  getCommodityCard($params=[])
    {
        $params=$this->_checkParams($params);

        $list = $this->alias("a")->join("t_db_commodity_card b", "a.id=b.card_id")->where($params['where'])->where(['validity_date_start'=>[['ELT',date('Y-m-d')],['exp','is null'],'or'],'validity_date_end'=>[['EGT',date('Y-m-d')],['exp','is null'],'or']])->where(['a.is_enable'=>1])->field($params['field'])->order("id desc")->group("a.id,b.commodity_id")->select();
        return $list;
    }

    public function getCardByOrder($params=[])
    {
        $params=$this->_checkParams($params);
        //增加is_enable=1 不可用的统统不查出来
        //增加  ->join('t_db_commodity_flat flat','FIND_IN_SET( b.class_id, flat.comm_type_id_str ) OR   b.commodity_set_id = flat.commodity_set_id  ')  分类关联
        //'a.validity_date_start'=>[['ELT',date('Y-m-d')],['exp','is null'],'or'],'a.validity_date_end'=>[['EGT',date('Y-m-d')],['exp','is null'],'or'], 卡券时间不再判断
        //->join('t_db_activity act','a.activity_id = act.id')
        //group('a.id,c.sku_id') 应该要改成C.id 这样子同样的组合商品才能是多条，不然只有一条
        //需要增加card_code进去
        //((a.apply_dlr_code='' or  c.dd_dlr_code='' or a.apply_dlr_code is null or  c.dd_dlr_code is null ) or   FIND_IN_SET(c.dd_dlr_code,act.up_down_channel_dlr))) 核销专营店==匹配专营店
//and ((a.apply_dlr_code='' or  c.dd_dlr_code='' or a.apply_dlr_code is null or  c.dd_dlr_code is null ) or   FIND_IN_SET(c.dd_dlr_code, a.apply_dlr_code))  在card_js处理
        //$where[]      = ['exp', sprintf("(a.user_id=%s  and a.user_id>0 and  (a.receive_vin='' or a.receive_vin is null)) or ( a.receive_vin in ('%s') and (a.user_id=%s || a.user_id=0) )", $user_id,trim($order_vin,"'"),$user_id)];
        //新增 and (rec.intention_store=c.dd_dlr_code or  rec.intention_store='' )
        if(!$params['order']){
            $params['order'] = 'a.is_gift_card desc';
        }

        //and ((rec.user_id=od.user_id and rec.user_id>0 and (rec.receive_vin='' or rec.receive_vin is null)) or ( rec.receive_vin = od.order_vin and rec.receive_vin>'' and (rec.user_id=od.user_id or rec.user_id=0)))
        //FIND_IN_SET( b.class_id, flat.comm_type_id_str ) OR
        $list = $this->alias("a")->join("t_db_commodity_card b", "a.id=b.card_id and b.is_enable=1")

            ->join('t_db_commodity_flat flat','(FIND_IN_SET( b.class_id, flat.comm_type_id_str ) OR   b.commodity_set_id = flat.commodity_set_id  ) and b.is_enable=1 and flat.is_enable=1 ')
            ->join("t_bu_order_commodity c","c.commodity_set_id=flat.commodity_set_id and ((c.is_gift=1 and c.gift_card_id>0 and c.gift_card_id=a.id) or c.is_gift=0) ")
            ->join("t_bu_order od","od.order_code=c.order_code")

            ->join('t_bu_card_receive_record rec',"rec.card_id=a.id and ((rec.status in (7) and c.gift_card_id>0 and c.is_gift=1) || (rec.status=1 && c.is_gift=0) || (rec.status=5 and (FIND_IN_SET(rec.card_code,c.pre_card_code) || FIND_IN_SET(rec.card_code,c.card_codes)) )) and rec.is_enable=1  and (rec.intention_store=c.dd_dlr_code or  rec.intention_store='' )")
            ->join('t_db_activity act',"act.activity_id=rec.activity_id and rec.activity_id>0",'left')
            ->where($params['where'])
            ->where(['a.is_enable'=>1])
            ->field($params['field'])
            ->order($params['order'])->group('c.id,a.id,c.sku_id')->select();
        //rec.status=1 && (c.gift_card_id = '' || c.gift_card_id =0) 改成 rec.status=1 && c.is_gift=0  主品也可以用券

        return $list;
    }


    public function getNewCardActName()
    {
        return $this->where(['act_name'=>['exp','is not null']])->group('act_name')->column('act_name');
    }

    /**
     * 专营店查询卡券
     * @param $set_type
     * @param $dlr_code
     * @param $params
     * @return \think\Paginator
     */

    public function getCardByDlr($set_type, $dlr_code, $params)
    {
        $params=$this->_checkParams($params);
        $date = date('Y-m-d');
        $where=" is_enable=1 and available_count>0 AND ((date_type=1 AND validity_date_end>='{$date}') OR date_type=2 ) ";
        if ($set_type==1 || $set_type==3) {
            $where.=" AND set_type={$set_type} AND dlr_code='{$dlr_code}'";
        } elseif ($set_type==2) {
            $where.=" AND FIND_IN_SET('{$dlr_code}',apply_dlr_code) ";
        }
        if (empty($params['field'])) {
            $params['field']='id,set_type,dlr_code,type,card_name,card_id,card_type,wx_card_type,card_quota,card_discount,least_type,least_cost';
        }
        return $this->where($where)->where($params['where'])->field($params['field'])->order('id DESC')->paginate($params['pagesize'], false, array('query' => $params['query']));
    }

    /**
     * 通过专营店获取卡券
     * @param $dlr_code
     * @return \think\Paginator
     */
    public function getDlrCard($params)
    {
        $params=$this->_checkParams($params);
       // $params['field']='id,set_type,dlr_code,type,card_name,card_id,card_type,wx_card_type,card_quota,card_discount,least_type,least_cost';
        return $this->where($params['where'])->field($params['field'])->order('id DESC')->paginate($params['pagesize'], false, array('query' => $params['query']));
    }

    /**
     * 获取卡券名称
     * @param $params
     * @return string
     */
    public function getCardName($params){
        $card_list = $this->getList($params);
        $card_named_list = '';
        foreach ($card_list as $k=>$value){
            $card_named_list.=$value['card_name'].',';
        }
        return rtrim($card_named_list,',');
    }


    /**
     * 获取所有数据
     * @param $params
     * @return array|bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getAllCardList($params = [])
    {
        $channelType = $params['channel_type'] ?? '';
        $where['a.is_enable']   = 1;
        $where['a.commodity_class']   = DbCommodity::COMMODITY_CLASS_KEY6;
        $where[] = ['exp', " (find_in_set('{$channelType}',a.up_down_channel_dlr)) "];
        #销售渠道--不需要过滤
//        $where[] = ['exp', " find_in_set(1,a.sales_channel)"];
        $field  = "a.commodity_id,a.commodity_name,d.card_id,d.sku_code,c.price,b.count_stock,
        e.id,e.card_name,e.card_id,e.card_quota,e.use_des,d.commodity_id,c.id set_sku_id,e.up_down_channel_dlr,e.shelves_type";
        $params = array(
            'where'    => $where,
            'group'    => "d.commodity_id", //暂时看看这个能不能读取出组合商品最低最高价
            'order'    => "a.updated_at asc", #商品排序规则和上架时间
            'field'    => $field,
        );
        $flat = new DbCommodityFlat();
        $list = $flat->getCommodityListUSku2($params);

        return !empty($list) ? $list : [];
    }

    /**
     * 获取所有数据配置
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getAllCardConfig($params = [])
    {
        $dataList = self::getAllCardList($params);
        $newCard = [];

        $e3sEnvConfig = config("port.e3s");
        $configData = $e3sEnvConfig['card_fixed_dlr']['card_quota'];

        foreach($dataList as $item){
            if(!isset($configData[$item['commodity_id']])){
                continue;
            }
            $kStr = $configData[$item['commodity_id']];

//            $kStr = 'card_quota_' . intval($item['card_quota']);
            if(isset($newCard[$kStr])){
                continue;
            }

            $newCard[$kStr] = [
                'id' => $item['id'],
                'commodity_name' => $item['commodity_name'],
                'card_name' => $item['card_name'],
                'card_code' => $item['card_id'],
                'card_quota' => $item['card_quota'],
                'price' => $item['price'],
                'use_des' => $item['use_des'],
                'commodity_id' => $item['commodity_id'],
                'set_sku_id' => $item['set_sku_id'],
                'sku_code' => trim($item['sku_code']),
            ];

        }

        return $newCard;
    }

    /**
     * 查询当前卡券信息
     * @param $id
     * @param $channelType
     * @param $params
     * @return array|bool|\PDOStatement|string|\think\Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getCardOne($id, $channelType, $params = [])
    {
        $field = $params['field'] ?? 'a.id,a.card_name,a.card_id,a.card_quota,a.use_des,a.card_type,a.validity_date_start,a.validity_date_end';
        $where = $params['where'] ?? [];
        $where['a.id'] = $id;

        //查询参数
        $str = md5('id='.$id .','. $field);
        $key = config('cache_prefix.basic_data') . $channelType .'_'. $str ;
        $data = redis($key);

        $data = false;
        if (empty($data) || getRedisLock($key . '-lock', mt_rand(7200, 17200))) {
            $data = self::find()
                ->alias('a')
                ->where($where)
                ->field($field)
                ->find();

            redis($key, $data, 60);
        }

        return $data;
    }

    //卡券关联活动
    public function cardActive($params){
        $params=$this->_checkParams($params);
        return $this->alias('a')->join('t_db_activity act','a.activity_id=act.activity_id','left')->where($params['where'])->field($params['field'])->select();

    }


    /**
     * 获取卡券列表
     * @param $params
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getActivityCardPage($params)
    {
        $params=$this->_checkParams($params);
        return $this->alias('a')
            ->join('t_db_activity b', 'a.activity_id=b.activity_id')
            ->where($params['where'])
            ->field($params['field'])
            ->order($params['order'])
            ->paginate($params['pagesize'],false,array('query'=>$params['query']));
    }
}
