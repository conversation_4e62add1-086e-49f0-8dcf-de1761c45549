<?php

/**
 *
 * @author: lian<PERSON><PERSON>an
 * @time: 2017-07-25
 */


namespace app\common\model\db;

use app\common\model\Common;

class DbCommoditySku extends Common
{
    protected $table = 't_db_commodity_sku';
    protected $pk    = 'id';



    public function getSetSku($params = [])
    {
        $params = $this->_checkParams($params);
        if (empty($params['join'])) {
            $params['join'] = 'inner';
        }
        return $this->alias('a')->join('t_db_commodity_set_sku b', 'a.id=b.commodity_sku_id', $params['join'])
            ->where($params['where'])->field($params['field'])->select();
    }

    public function getNewSetSku($params = [], $dlr_code)
    {
        $params = $this->_checkParams($params);

        return $this->alias('a')->join('t_db_commodity_set_sku b', "a.id=b.commodity_sku_id AND b.dlr_code='{$dlr_code}'", 'left')
            ->where($params['where'])->field($params['field'])->select();
    }

    //速赢版本需要增加车型去关联sku
    public function getSetSkuByCommoditySetId($commodity_set_id, $commodity_id, $car_s_id = '', $sub_commodity_id = '', $dlr_level = '',$car_offline_date='',$maintain_type='',$maintain_upgrade_type='',$maintain_times=0,$oil_type='',$dd_dlr_code='',$sku_id_list=[],$car_config_code='')
    {
        $field = "a.sp_value_list,a.id as commodity_sku_id,b.divided_into,b.install_fee,b.cost_price, a.sku_code,b.id,b.id set_sku_id,b.set_type,IFNULL(b.stock,0) stock,IFNULL(b.price,a.price) as price,a.image,a.relate_car_work_hour,a.relate_car_ids,b.commodity_id,a.commodity_id sub_commodity_id,a.commodity_id group_sub_commodity_id,a.relate_car_ids,a.sku_code,a.rep_part_no,a.maintain_num,a.maintain_q,a.upgrade_type,a.variety_code";
        //没有传车型的时候查全部，查最低价 groupby skuid ,sku.sku_code re_sku_code ,a.relate_car_18n
        //保养套餐显示原价，其他的有活动才显示划线价
        if($dlr_level || $maintain_type || $maintain_upgrade_type || $maintain_times){
            $field.=",a.price old_price";
        }else{
            $field.=",b.price old_price";
        }
        $data_car_where = "a.is_enable=1 and b.is_enable=1";
        //用户没车型的时候取最低价。

        //保险的传入了18位码，这个时候没有交车
        if ($car_config_code) {
            $data_car_where .= sprintf(" and (FIND_IN_SET('%s',a.relate_car_18n) || a.relate_car_18n='' || a.relate_car_18n is null)", $car_config_code);
        }else{
            if ($car_s_id) {
                $data_car_where .= sprintf(" and (FIND_IN_SET(%s,a.relate_car_ids) || a.relate_car_ids='' || a.relate_car_ids is null)", $car_s_id);
            }
        }
        if ($dlr_level) {
            $data_car_where .= " and b.city_type = '$dlr_level'";
        }
        $field.=',sub_set.stock sub_stock,set.count_stock';
        //折扣
        if($maintain_type){
            $data_car_where .= " and a.maintain_q = '$maintain_type'";
        }
        //油型
        if($maintain_upgrade_type){
            $data_car_where .= " and a.upgrade_type = '$maintain_upgrade_type'";
        }
        //次数
        if($maintain_times){
            $data_car_where .= " and a.maintain_num <= '$maintain_times'";
        }
        if($oil_type){
            $data_car_where .=  sprintf(" and (a.oli_liters in (%s) || a.oli_liters='')",$oil_type);
        }
        $field.=',sub_set.stock sub_stock';
        if ($sub_commodity_id) {
            $sub_com = sprintf("and b.group_sub_commodity_id ='%s' ", $sub_commodity_id);
        } else {
            $sub_com = '';
        }
        if($dd_dlr_code){
            $data_car_where .= sprintf(" and (FIND_IN_SET('%s',a.relate_dlr_code) || a.relate_dlr_code='' || a.relate_dlr_code is null)", $dd_dlr_code);

        }
//        echo $data_car_where;die();
        if($car_offline_date){
            $car_offline_date =date('Y/m',strtotime($car_offline_date));
            $data_car_where .= sprintf(" and (a.part_start_time<='%s' || a.part_start_time='') and (a.part_end_time>='%s' || a.part_end_time ='') ", $car_offline_date,$car_offline_date);
        }
        if($sku_id_list){
            $data_car_where .=  sprintf(" and  b.id in (%s)",implode(',',$sku_id_list));
        }
        return $this->alias('a')->connect(config('database-read'))
            ->join('t_db_commodity_set_sku b', "a.id=b.commodity_sku_id  AND b.commodity_set_id={$commodity_set_id}   {$sub_com}", 'left')
            ->join("t_db_commodity_set_sku sub_set "," sub_set.id=b.group_sub_set_sku_id and b.group_sub_set_sku_id>0 and sub_set.is_enable=1",'left')
//            ->join('t_db_commodity_sku sku','find_in_set(sku.sku_code,a.rep_part_no) and a.sp_value_list=sku.sp_value_list', 'left')
            ->join('t_db_commodity_set set','b.commodity_set_id = set.id','left')
            ->where(['b.commodity_id' => $commodity_id, 'a.is_enable' => 1])->where($data_car_where)->group('a.id')->order('b.price DESC,a.id desc ,b.id desc ')->field($field)->select();
        //b.commodity_id 必须要用sku的，不然组合商品会is_mate=0，但是用的b.commodity_id会导致读起来慢,暂时用这个b.，后续再改
            //->join('t_db_commodity_sku sku','find_in_set(sku.sku_code,a.rep_part_no) and a.sp_value_list=sku.sp_value_list', 'left') 暂时不拿被替换件了
        //sy 增加一个规格过滤车型 ->where(sprintf("FIND_IN_SET(%s,a.relate_car_ids)",$car_s_id))
    }

    //后台规格
    public function getSetSkuByCommoditySetIdAdmin($commodity_set_id, $commodity_id, $car_s_id = '', $sub_commodity_id = '', $dlr_level = '')
    {
//        $field = "a.sp_value_list,a.id as commodity_sku_id,b.divided_into,b.install_fee,b.cost_price, a.sku_code,b.id,b.id set_sku_id,b.set_type,IFNULL(b.stock,0) stock,IFNULL(b.price,a.price) as price,b.price old_price,a.image,a.relate_car_work_hour,a.relate_car_ids,b.commodity_id,a.commodity_id sub_commodity_id,a.commodity_id group_sub_commodity_id,a.relate_car_ids,a.relate_car_18n,a.sku_code,a.rep_part_no,sku.sku_code re_sku_code";
        $field = "a.sp_value_list,a.id as commodity_sku_id,b.divided_into,b.install_fee,a.cost_price, a.sku_code,b.id,b.id set_sku_id,b.set_type,IFNULL(b.stock,0) stock,IFNULL(b.price,a.price) as price,b.price old_price,a.image,b.commodity_id,a.commodity_id sub_commodity_id,a.commodity_id group_sub_commodity_id,a.sku_code";
        //没有传车型的时候查全部，查最低价 groupby skuid
        $data_car_where = "a.is_enable=1 and b.is_enable=1";
        //用户没车型的时候取最低价。
        if ($car_s_id) {
            $data_car_where .= sprintf(" and (FIND_IN_SET(%s,a.relate_car_ids) || a.relate_car_ids='' || a.relate_car_ids is null)", $car_s_id);
        }
        if ($dlr_level) {
            $data_car_where .= " and b.city_type = '$dlr_level'";
        }
        $field.=',sub_set.stock sub_stock,set.count_stock';
        if ($sub_commodity_id) {
            $sub_com = sprintf("and b.group_sub_commodity_id ='%s' ", $sub_commodity_id);
        } else {
            $sub_com = '';
        }

        if ($sub_commodity_id) {
            $sub_com = sprintf("and b.group_sub_commodity_id ='%s' ", $sub_commodity_id);
        } else {
            $sub_com = '';
        }

        return $this->alias('a')
            ->join('t_db_commodity_set_sku b', "a.id=b.commodity_sku_id  AND b.commodity_set_id={$commodity_set_id}   {$sub_com}", 'left')
            ->join("t_db_commodity_set_sku sub_set "," sub_set.id=b.group_sub_set_sku_id and b.group_sub_set_sku_id>0",'left')
//            ->join('t_db_commodity_sku sku','find_in_set(sku.sku_code,a.rep_part_no) and a.sp_value_list=sku.sp_value_list', 'left')
            ->join('t_db_commodity_set set','b.commodity_set_id = set.id','left')
            ->where(['b.commodity_id' => $commodity_id, 'a.is_enable' => 1])->where($data_car_where)
//            ->group('a.sku_code,a.sp_value_list')
            ->order('price desc,b.id')->field($field)->select();
        //sy 增加一个规格过滤车型 ->where(sprintf("FIND_IN_SET(%s,a.relate_car_ids)",$car_s_id))
    }

    //后台商品上架规格显示去重
    public function getSetSkuByCommoditySetAdmin($commodity_set_id, $commodity_id, $car_s_id = '', $sub_commodity_id = '', $dlr_level = '')
    {
        $field = "a.sp_value_list,a.id as commodity_sku_id,b.divided_into,b.install_fee,a.cost_price, a.sku_code,b.id,b.id set_sku_id,b.set_type,IFNULL(b.stock,0) stock,IFNULL(b.price,a.price) as price,b.price old_price,a.image,b.commodity_id,a.commodity_id sub_commodity_id,a.commodity_id group_sub_commodity_id,a.sku_code";
        //没有传车型的时候查全部，查最低价 groupby skuid
        $data_car_where = "a.is_enable=1";
        //用户没车型的时候取最低价。
        if ($car_s_id) {
            $data_car_where .= sprintf(" and (FIND_IN_SET(%s,a.relate_car_ids) || a.relate_car_ids='' || a.relate_car_ids is null)", $car_s_id);
        }
        if ($dlr_level) {
            $data_car_where .= " and b.city_type = '$dlr_level'";
        }
        $field.=',sub_set.stock sub_stock,set.count_stock';
        if ($sub_commodity_id) {
            $sub_com = sprintf("and b.group_sub_commodity_id ='%s' ", $sub_commodity_id);
        } else {
            $sub_com = '';
        }

        if ($sub_commodity_id) {
            $sub_com = sprintf("and b.group_sub_commodity_id ='%s' ", $sub_commodity_id);
        } else {
            $sub_com = '';
        }

        return $this->alias('a')
            ->join('t_db_commodity_set_sku b', "a.id=b.commodity_sku_id  AND b.commodity_set_id={$commodity_set_id}   {$sub_com}", 'left')
            ->join("t_db_commodity_set_sku sub_set "," sub_set.id=b.group_sub_set_sku_id and b.group_sub_set_sku_id>0",'left')
            ->join('t_db_commodity_set set','b.commodity_set_id = set.id','left')
            ->where(['b.commodity_id' => $commodity_id, 'a.is_enable' => 1])->where($data_car_where)
            ->group('a.sku_code,a.sp_value_list')
            ->order('price desc,b.id')->field($field)->select();
    }



    //积分专区获取sku
    public function newGetSetSkuByCommoditySetId($commodity_set_id, $commodity_id, $car_s_id = '', $sub_commodity_id = '', $dlr_level = '',$is_integral_shop='',$card_name = '',$car_offline_date='')
    {
        $field = "a.sp_value_list,a.id as commodity_sku_id,b.divided_into,b.install_fee,b.cost_price, a.sku_code,b.id,b.id set_sku_id,b.set_type,IFNULL(b.stock,0) stock,IFNULL(b.price,a.price) as price,b.price old_price,a.image,a.relate_car_work_hour,a.relate_car_ids,b.commodity_id,a.commodity_id sub_commodity_id,a.commodity_id group_sub_commodity_id,a.relate_car_ids,a.relate_car_18n,a.sku_code,a.rep_part_no,a.variety_code";
        //没有传车型的时候查全部，查最低价 groupby skuid ,sku.sku_code re_sku_code
        $data_car_where = "a.is_enable=1 and b.is_enable=1";
        //用户没车型的时候取最低价。
        if ($car_s_id) {
            $data_car_where .= sprintf(" and (FIND_IN_SET(%s,a.relate_car_ids) || a.relate_car_ids='' || a.relate_car_ids is null)", $car_s_id);
        }
        if ($dlr_level) {
            $data_car_where .= " and b.city_type = '$dlr_level'";
        }
        $spec = 0;
        if(in_array($card_name,['会员金卡', '会员金卡(VIP)','会员金卡(启辰VIP)','会员金卡(启辰)'])){
            $spec = 3930;
        }
        if(in_array($card_name,['会员银卡', '会员银卡VIP','会员银卡(启辰)'])){
            $spec = 3931;
        }
        if(in_array($card_name,['会员普卡','会员普卡(启辰)','员工卡(启辰)','员工卡'])){
            $spec = 3932;
        }
        if($spec != 0){
            $data_car_where .= " and a.sp_value_list like '%".$spec."%'";
        }
        if ($sub_commodity_id) {
            $sub_com = sprintf("and b.group_sub_commodity_id ='%s' ", $sub_commodity_id);
        } else {
            $sub_com = '';
        }
        if($car_offline_date){
            $car_offline_date =strtotime($car_offline_date);
            $data_car_where .= sprintf(" and (unix_timestamp(a.part_start_time)<='%s') and (unix_timestamp(a.part_end_time)>='%s' || a.part_end_time ='') ", $car_offline_date,$car_offline_date);
        }
        return $this->alias('a')
            ->join('t_db_commodity_set_sku b', "a.id=b.commodity_sku_id  AND b.commodity_set_id={$commodity_set_id}   {$sub_com}", 'left')
            ->join("t_db_commodity_set_sku sub_set "," sub_set.id=b.group_sub_set_sku_id and b.group_sub_set_sku_id>0",'left')

            ->where(['b.commodity_id' => $commodity_id, 'a.is_enable' => 1])->where($data_car_where)->group('a.id')->order('price desc,b.id')->field($field)->select();
        //->join('t_db_commodity_sku sku','find_in_set(sku.sku_code,a.rep_part_no) and a.sp_value_list=sku.sp_value_list', 'left')
        //sy 增加一个规格过滤车型 ->where(sprintf("FIND_IN_SET(%s,a.relate_car_ids)",$car_s_id))
    }

    /**
     * [官网]版获取sku信息
     * @param $where
     * @param $field
     * @return array|false|\PDOStatement|string|\think\Model
     */

    public function getSkuInfo($where, $field)
    {
        $list = $this->alias('a')->join('t_db_commodity c', 'a.commodity_id=c.id')->join("t_db_commodity_flat e", "e.commodity_id=c.id")->join("t_db_commodity_set d", "d.commodity_id=c.id")
            ->where($where)->field($field)->find();
        return $list;
    }

    //速赢版本需要增加车型去关联sku -- 分页版
    public function getSetSkuByCommoditySetIdPage($params)
    {
        return $this->alias('a')->join('t_db_commodity_set_sku b', "a.id=b.commodity_sku_id", 'left')
            ->where($params['where'])->group($params['group'])->order($params['order'])->field($params['field'])->paginate($params['pageSize'], false, array('query' => $params['query']));
    }

    // 获取sku名称
    public function getSkuName($commodity_id){

        $return_data = [];
        $sp_list = $this->where(['commodity_id' => $commodity_id, 'is_enable' => 1])->select();
        foreach ($sp_list as $k => $item) {
            $sp_value_list = (new DbSpecValue())->getAllList(['a.id' => ['in', $item['sp_value_list']]]);
            $sku_value     = '';
            if ($sp_value_list) {
                foreach ($sp_value_list as $vv) {
                    $sku_value .= $vv['sp_name'] . ":" . $vv['sp_value_name'] . ", ";
                }
                $tmp = [
                    'sku_id' => $item['id'],
                    'sp_value_name' => trim($sku_value, ', '),
                ];
                $return_data[$item['id']] = $tmp;
            }
        }
        return $return_data;
    }


    public function setSkus()
    {
        return $this->hasMany(DbCommoditySetSku::class, 'commodity_sku_id');
    }


    public function commodity()
    {
        return $this->belongsTo(DbCommodity::class, 'commodity_id');
    }
}
