<?php
/**
 *
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @time: 2017-04-18
 */

namespace app\common\model\bu;

use app\admin_v2\controller\OrderSettle;
use app\common\model\Common;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbDlr;
use app\common\model\db\InvoiceApplyDetailModel;
use app\common\model\db\InvoiceApplyModel;
use app\common\net_service\NetGoods;
use think\Db;
use think\Model;
use tool\OssUploadFile;
use tool\QrCode;
use app\common\model\db\DbCard;

class BuOrder extends Common
{
    protected $table = 't_bu_order';
    protected $pk = 'id';

    static public $order_type_info = [
        1 => '直邮',
        2 => '安装',
        3 => 'CSS套餐',
        4 => 'N延保',
        5 => '电子卡券',
    ];

    static public $channel = [
        1 => '小程序',
        2 => '官网',
        3 => 'App',
        4 => 'PZ1A小程序',
        5 => 'PZ1AAPP',
        6 => '启辰小程序',
        7 => '启辰App',
    ];

    static public $can_not_afs = [11, 12];

    static public $channel_code = [
        1 => 3,
        2 => 1,
        3 => 2,
        4 => 4,
        5 => 5,
        6 => 6,
        7 => 7
    ];

    static public $promotion_source = [
        1 => '团购',
        2 => '套装',
        3 => '臻享服务包',
        4 => '保养推荐',
        5 => '保养套餐类',
        6 => '移动积分兑换',
        7 => '双11随心配',
        8 => '取送车服务包',
        9 => 'CIP23-38活动',
        10 => '奶茶活动'
    ];

    static public $payment_method_name = [
        1 => '现金',
        2 => '积分',
        3 => '卡劵',
        4 => '现金+积分',
        5 => '现金+优惠券',
        6 => '积分+优惠',
        7 => '现金+积分+卡劵'
    ];

    /**
     * 来源
     * @return array
     */
    public function order_source()
    {
        return [1 => '商品订单', 2 => '拼团订单', 11 => 'CCS订单', 12 => '虚拟商品订单', 13 => '电子卡券商品订单'];
    }


    /**
     * 获取e3s渠道
     * @param $channel
     * @return int
     */
    public function getE3sChannel($channel)
    {
//        1 => '小程序',
//        2 => '官网',
//        3 => 'App',
//        4 => 'PZ1A小程序',
//        5 => 'PZ1AAPP',
//        6 => '启辰小程序',
//        7 => '启辰App',
//        3-小程序，4-官网，5-APP
        $channelArr = [1 => 3, 2 => 4, 3 => 5, 4 => 3, 5 => 5, 6 => 3, 7 => 5];
        return $channelArr[$channel] ?? 0;
    }


    static function brandText($v)
    {
        $data = [1 => '日产', 2 => '启辰', 3 => 'PZ1A'];
        if ($v != '' && !isset($data[$v])) return '';
        return empty($v) ? $data : $data[$v];
    }


    /**
     * 订单大类别
     * @param $type_id
     * @return array|mixed
     */
    static function orderBigType($type_id)
    {
        //订单类型：1直邮、2到店安装、3合单、4拼团、5预售、6虚拟、7电子卡券、8CCS、9N延保
//        [0 => '', 1 => '商品订单', 2 => '拼团订单', 3 => '套装', 4 => '礼包', 5 => '到店有礼', 6 => '积分-日产', 7 => "会员日", 8 => '锦尚阳光口罩', 11 => 'CCS订单', 12 => '虚拟商品订单', 13 => '电子卡券商品订单',14=>'积分-启辰',15=>'到店备件',16=>'保养套餐-老友惠保养套餐',17=>'保养套餐-心悦保养套餐',18=>'保养套餐-双保专属权益套餐',19=>'保养套餐-其他',20=>'到店代金券',21=>'到店电子券','22'=>'pz1a套餐','23'=>'工时商品','24'=>'取送车券'];
//        $data = [
//            1  => '直邮', 2 => '到店安装', 3 => '合单', 4 => '拼团', 5 => '预售', 6 => '虚拟', 7 => '电子卡券', 8 => 'CCS',
//            9  => 'N延保', 15 => '到店备件', 16 => '保养套餐-老友惠保养套餐', 17 => '保养套餐-心悦保养套餐',
//            18 => '保养套餐-双保专属权益套餐', 19 => '保养套餐-其他', 20 => '到店代金券', 21 => '到店电子券', 22 => 'pz1a套餐',
//            23 => '工时商品', 24 => '取送车券', 25 => '启辰充电桩', 26 => "众筹商品", 27 => 'PV工会线上订单', 28 => 'PV总工会', //27-37 都给工会
//            40 => '保养套餐-五年双保专享心悦套餐', 42 => '日产充电桩', 43 => '延保服务包商品'
//        ];
        $data = self::orderSource();
        if (isset($data[$type_id])) {
            return $data[$type_id];
        } else {
            return [];
        }

    }

    public function getCanOrderAfsList($params)
    {
        $params = $this->_checkParams($params);
        isset($params['group']) or $params['group'] = [];
        $list = $this->alias('a')
            ->join("t_db_tax_invoice_apply_detail d", "a.order_code = d.good_order_no", "left")
            ->join("t_db_tax_invoice_apply c", "d.apply_id = c.id", "left")
            ->join("t_db_tax_invoice_record record", "record.apply_id = c.id and record.is_red_invoice=0", "left")
            ->join('t_db_after_sale_orders b', 'a.id=b.order_id', 'left')
            ->where($params['where'])
            ->field($params['field'])
            ->order($params['order'])
            ->group($params['group'])
            ->limit($params['limit'])
            ->select();

        return $list;
    }

    public function getOrderAfsList($params)
    {
        $params = $this->_checkParams($params);
        isset($params['group']) or $params['group'] = [];
        $list = $this->alias('a')
            ->join("t_db_tax_invoice_apply_detail d", "a.order_code = d.good_order_no", "left")
            ->join("t_db_tax_invoice_apply c", "d.apply_id = c.id", "left")
            ->join("t_db_tax_invoice_record record", "record.apply_id = c.id and record.is_red_invoice=0", "left")
            ->join('t_db_after_sale_orders b', 'a.id=b.order_id', 'right')
            ->where($params['where'])->field($params['field'])->order($params['order'])->group($params['group'])->limit($params['limit'])->select();
        return $list;
    }

    public function getOrderLimit($params, $group = true)
    {
        $params = $this->_checkParams($params);
        if ($group) {
            $list = $this->alias('a')
                ->field($params['field'])
                ->join('t_bu_order_commodity b', 'a.order_code=b.order_code', 'left')
                ->join('t_db_commodity c', 'b.commodity_id=c.id', 'left')
                ->join('t_db_dlr f', 'a.dd_dlr_code = f.dlr_code', 'left')
                ->where($params['where'])
                ->where($params['where_or'])
                ->order($params['order'])
                ->group($params['group'])
                ->select();
        } else {
            if (isset($params['order_s_q']) && $params['order_s_q'] == 1) {
                $list = $this->alias('a')
                    ->field($params['field'])
                    ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
                    ->join('t_db_commodity c', 'b.commodity_id=c.id')
                    ->join('t_db_commodity_set_sku d', 'b.sku_id = d.id')
                    ->join('t_db_commodity_sku e', 'd.commodity_sku_id = e.id')
                    ->join('t_db_dlr f', 'a.dd_dlr_code = f.dlr_code', 'left')
                    ->join('t_db_commodity_dlr_type h', 'a.commodity_dlr_type_id=h.id')
                    ->join('t_bu_wx_pay i', 'i.order_id=a.pay_order_code ', 'left')
                    ->join('t_db_commodity_type t1', 'c.comm_type_id=t1.id', 'left')
                    ->join('t_db_commodity_type t2', 't1.comm_parent_id=t2.id', 'left')
                    ->join('t_db_commodity_type t3', 't2.comm_parent_id=t3.id', 'left')
                    ->join('t_db_user j', 'a.user_id=j.id', 'left')
                    ->join('t_db_full_discount k', 'b.full_id=k.id', 'left')
                    ->join('t_jd_user l', 'a.jifen_user_id=l.id', 'left')
                    ->join('t_jd_jifen_index m', 'l.phone=m.mobile and l.app_type=m.brand+1', 'left')
                    ->join('t_db_separate_account_rules n', 'b.spr_id = n.id', 'left')
                    ->join('t_bu_card_order o', 'a.order_code = o.order_code', 'left')
                    ->join('t_bu_order_point point', 'a.order_code = point.order_code and point.use_state=1 and point.is_enable=1', 'left')
                    ->where($params['where'])
                    ->where($params['where_or'])
                    ->order($params['order'])
                    ->group($params['group'])
                    ->select();
            } else {
                $list = $this->alias('a')
                    ->field($params['field'])
                    ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
                    ->join('t_db_commodity c', 'b.commodity_id=c.id')
                    ->join('t_db_commodity_set_sku d', 'b.sku_id = d.id')
                    ->join('t_db_commodity_sku e', 'd.commodity_sku_id = e.id')
                    ->join('t_db_dlr f', 'a.dd_dlr_code = f.dlr_code', 'left')
                    ->join('t_db_commodity_dlr_type h', 'a.commodity_dlr_type_id=h.id')
                    ->join('t_bu_wx_pay i', 'i.order_id=a.pay_order_code ', 'left')
                    ->join('t_db_commodity_type t1', 'c.comm_type_id=t1.id', 'left')
                    ->join('t_db_commodity_type t2', 't1.comm_parent_id=t2.id', 'left')
                    ->join('t_db_commodity_type t3', 't2.comm_parent_id=t3.id', 'left')
                    ->join('t_db_user j', 'a.user_id=j.id', 'left')
                    ->join('t_db_full_discount k', 'b.full_id=k.id', 'left')
                    ->join('t_db_separate_account_rules n', 'b.spr_id = n.id', 'left')
                    ->join('t_bu_order_point point', 'a.order_code = point.order_code and point.use_state=1 and point.is_enable=1', 'left')
                    ->where($params['where'])
                    ->where($params['where_or'])
                    ->order($params['order'])
                    ->group($params['group'])
                    ->select();
            }

        }

        return $list;
    }

    public function getOrderLimitOpt($params, $group = true)
    {
        $params = $this->_checkParams($params);
        if ($group) {
            $list = $this->alias('a')
                ->field($params['field'])
                ->join('t_bu_order_commodity b', 'a.order_code=b.order_code', 'left')
                ->join('t_db_commodity c', 'b.commodity_id=c.id', 'left')
                ->join('t_db_dlr f', 'a.dd_dlr_code = f.dlr_code', 'left')
                ->where($params['where'])
                ->where($params['where_or'])
                ->order($params['order'])
                ->group($params['group'])
                ->select();
        } else {
            if (isset($params['order_s_q']) && $params['order_s_q'] == 1) {
                $list = $this->alias('a')
                    ->field($params['field'])
                    ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
                    ->join('t_db_commodity c', 'b.commodity_id=c.id')
                    ->join('t_db_commodity_set_sku d', 'b.sku_id = d.id')
                    ->join('t_db_commodity_sku e', 'd.commodity_sku_id = e.id', 'left')
                    ->join('t_db_dlr f', 'a.dd_dlr_code = f.dlr_code', 'left')
                    ->join('t_db_commodity_dlr_type h', 'a.commodity_dlr_type_id=h.id')
                    ->join('t_db_commodity_type t1', 'c.comm_type_id=t1.id', 'left')
                    ->join('t_db_commodity_type t2', 't1.comm_parent_id=t2.id', 'left')
                    ->join('t_db_commodity_type t3', 't2.comm_parent_id=t3.id', 'left')
                    ->join('t_db_user j', 'a.user_id=j.id', 'left')
                    ->join('t_db_full_discount k', 'b.full_id=k.id', 'left')
                    ->join('t_jd_user l', 'a.jifen_user_id=l.id', 'left')
                    ->join('t_jd_jifen_index m', 'l.phone=m.mobile and l.app_type=m.brand+1', 'left')
                    ->join('t_db_separate_account_rules n', 'b.spr_id = n.id', 'left')
                    ->join('t_bu_card_order o', 'a.order_code = o.order_code', 'left')
                    ->join('t_bu_order_point point', 'a.order_code = point.order_code and point.use_state=1 and point.is_enable=1', 'left')
                    ->join('t_db_gift g', 'b.gift_act_id=g.id', 'left')
                    ->join('t_db_seckill se', 'b.seckill_id=se.id', 'left')
                    ->join('t_bu_order_yd yd', 'a.order_code = yd.order_code', 'left')
                    ->where($params['where'])
                    ->where($params['where_or'])
                    ->order($params['order'])
                    ->group($params['group'])
                    ->select();
            } else {
                $list = $this->alias('a')
                    ->field($params['field'])
                    ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
                    ->join('t_db_commodity_set_sku d', 'b.sku_id = d.id')
                    ->join('t_db_commodity c', '(b.commodity_id=c.id and d.group_sub_commodity_id=0) || (d.group_sub_commodity_id=c.id)')
                    ->join('t_db_commodity_sku e', 'd.commodity_sku_id = e.id', 'left')
                    ->join('t_db_dlr f', 'a.dd_dlr_code = f.dlr_code', 'left')
                    ->join('t_db_commodity_dlr_type h', 'a.commodity_dlr_type_id=h.id')
                    ->join('t_db_commodity_type t1', 'c.comm_type_id=t1.id', 'left')
                    ->join('t_db_commodity_type t2', 't1.comm_parent_id=t2.id', 'left')
                    ->join('t_db_commodity_type t3', 't2.comm_parent_id=t3.id', 'left')
                    ->join('t_db_user j', 'a.user_id=j.id', 'left')
                    ->join('t_db_full_discount k', 'b.full_id=k.id', 'left')
                    ->join('t_db_separate_account_rules n', 'b.spr_id = n.id', 'left')
                    ->join('t_bu_order_point point', 'a.order_code = point.order_code and point.use_state=1 and point.is_enable=1', 'left')
                    ->join('t_bu_order_yd yd', 'a.order_code = yd.order_code', 'left')
//                    ->join('t_bu_card_receive_record l', 'a.user_id=l.user_id and a.id=l.act_id', 'left')
                    ->join('t_db_gift g', 'b.gift_act_id=g.id', 'left')
                    ->join('t_db_seckill se', 'b.seckill_id=se.id', 'left')
                    ->where($params['where'])
                    ->where($params['where_or'])
                    ->order($params['order'])
                    ->group($params['group'])
                    ->select();
            }

        }

        return $list;
    }

    //简单版导出
    public function getOrdersm($params, $group = true)
    {
        $params = $this->_checkParams($params);
        $subQuery = Db::table('t_db_order_invoice')
            ->field('id as invoice_id, order_id,invoice_status,invoice_time')
            ->order('invoice_id', 'desc')
            ->group('order_id')
            ->buildSql();

        $list = $this->alias('a')
            ->field($params['field'])
            ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
            ->join('t_db_commodity_set_sku d', 'b.sku_id = d.id')
            ->join('t_db_commodity c', 'b.commodity_id=c.id')
//            ->join('t_db_commodity_sku e', 'd.commodity_sku_id = e.id')
            ->join('t_db_dlr f', 'a.dd_dlr_code = f.dlr_code', 'left')
            ->join('t_db_commodity_dlr_type h', 'a.commodity_dlr_type_id=h.id')
            ->join('t_db_commodity_type t1', 'c.comm_type_id=t1.id', 'left')
            ->join('t_db_commodity_type t2', 't1.comm_parent_id=t2.id', 'left')
            ->join('t_db_commodity_type t3', 't2.comm_parent_id=t3.id', 'left')
            ->join('t_db_user j', 'a.user_id=j.id', 'left')
            ->join('t_db_full_discount k', 'b.full_id=k.id', 'left')
            ->join('t_db_separate_account_rules n', 'b.spr_id = n.id', 'left')
            ->join('t_bu_order_point point', 'a.order_code = point.order_code and point.use_state=1 and point.is_enable=1', 'left')
            ->join('t_bu_order_yd yd', 'a.order_code = yd.order_code', 'left')
//                    ->join('t_bu_card_receive_record l', 'a.user_id=l.user_id and a.id=l.act_id', 'left')
            ->join('t_db_gift g', 'b.gift_act_id=g.id', 'left')
            ->join('t_db_seckill se', 'b.seckill_id=se.id', 'left')
            ->join('t_db_pre_sale ps', 'b.pre_sale_id=ps.id', 'left')
            ->join("$subQuery invoice", 'a.id = invoice.order_id', "left")
            ->where($params['where'])
            ->where($params['where_or'])
            ->order($params['order'])
            ->group($params['group'])
            ->select();

        return $list;

    }

    public function getOrderLimitAjax($params, $group = true)
    {
        $params = $this->_checkParams($params);
        $subQuery = Db::table('t_db_order_invoice')
            ->field('id as invoice_id, order_id,invoice_status,invoice_time')
            ->order('invoice_id', 'desc')
            ->group('order_id')
            ->buildSql();

        if (isset($params['order_s_q']) && $params['order_s_q'] == 1) {
            $list = $this->alias('a')
                ->field($params['field'])
                ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
                ->join('t_db_commodity_set_sku d', 'b.sku_id = d.id')
                ->join('t_db_commodity c', 'b.commodity_id=c.id')
                ->join('t_db_dlr f', 'a.dd_dlr_code = f.dlr_code', 'left')
                ->join('t_db_commodity_dlr_type h', 'a.commodity_dlr_type_id=h.id')
                ->join('t_db_user j', 'a.user_id=j.id', 'left')
                ->join('t_jd_user l', 'a.jifen_user_id=l.id', 'left')
                ->join('t_jd_jifen_index m', 'l.phone=m.mobile and l.app_type=m.brand+1', 'left')
                ->join('t_bu_card_order o', 'a.order_code = o.order_code', 'left')
                ->join('t_bu_order_point point', 'a.order_code = point.order_code and point.use_state=1 and point.is_enable=1', 'left')
                ->join('t_db_gift g', 'b.gift_act_id=g.id', 'left')
                ->join('t_db_seckill se', 'b.seckill_id=se.id', 'left')
                ->join('t_bu_order_yd yd', 'a.order_code = yd.order_code', 'left')
                ->join('t_db_pre_sale ps', 'b.pre_sale_id=ps.id', 'left')
                ->join("$subQuery invoice", 'a.id = invoice.order_id', "left")
                ->where($params['where'])
                ->where($params['where_or'])
                ->limit($params['limit'])
                ->order($params['order'])
                ->group($params['group'])
                ->select();
        } else {
            $list = $this->alias('a')
                ->field($params['field'])
                ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
                ->join('t_db_commodity_set_sku d', 'b.sku_id = d.id')
                ->join('t_db_commodity c', 'b.commodity_id=c.id', 'left')
                ->join('t_db_home_type_commodity htc', 'c.id=htc.comm_id', 'left')
                ->join('t_db_home_type_commodity htc2', 'c.comm_type_id=htc2.old_comm_type_id', 'left')
                ->join('t_db_dlr f', 'a.dd_dlr_code = f.dlr_code', 'left')
                ->join('t_db_commodity_dlr_type h', 'a.commodity_dlr_type_id=h.id')
                ->join('t_db_commodity_type t1', 'c.comm_type_id=t1.id', 'left')
                ->join('t_db_commodity_type t2', 't1.comm_parent_id=t2.id', 'left')
                ->join('t_db_commodity_type t3', 't2.comm_parent_id=t3.id', 'left')
                ->join('t_db_user j', 'a.user_id=j.id', 'left')
                ->join('t_bu_order_point point', 'a.order_code = point.order_code and point.use_state=1 and point.is_enable=1', 'left')
                ->join('t_bu_order_yd yd', 'a.order_code = yd.order_code', 'left')
//                ->join('t_bu_card_receive_record l', 'a.user_id=l.user_id and a.id=l.act_id', 'left')
                ->join("$subQuery invoice", 'a.id = invoice.order_id', "left")
                ->join('t_db_gift g', 'b.gift_act_id=g.id', 'left')
                ->join('t_db_seckill se', 'b.seckill_id=se.id', 'left')
                ->join('t_db_pre_sale ps', 'b.pre_sale_id=ps.id', 'left')
                ->where($params['where'])
                ->where($params['where_or'])
                ->limit($params['limit'])
                ->order($params['order'])
                ->group($params['group'])
                ->select();
        }

        return $list;
    }

    //查询列表
    public function getOrderPaginate($params)
    {
        $params = $this->_checkParams($params);
        if (!$params['count']) {
            $params['count'] = 100000;
        }
        $params = $this->_checkParams($params);
        $subQuery = Db::table('t_db_order_invoice')
            ->field('id as invoice_id, order_id,invoice_status,invoice_time')
            ->order('invoice_id', 'desc')
            ->group('order_id')
            ->buildSql();
        if (isset($params['order_s_q']) && $params['order_s_q'] == 1) {
            $list = $this->alias('a')
                ->field($params['field'])
                ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
                ->join('t_db_commodity_set_sku d', 'b.sku_id = d.id')
                ->join('t_db_commodity c', 'b.commodity_id=c.id')
                ->join('t_db_commodity_dlr_type f', 'a.commodity_dlr_type_id=f.id')
                ->join('t_db_dlr e', 'a.dd_dlr_code = e.dlr_code', 'left')
//                ->join('t_bu_wx_pay i', 'i.order_id=a.pay_order_code ', 'left')
                ->join('t_db_user j', 'a.user_id=j.id', 'left')
                ->join('t_db_full_discount k', 'b.full_id=k.id', 'left')
                ->join('t_jd_user l', 'a.jifen_user_id=l.id', 'left')
                ->join('t_jd_jifen_index m', 'l.phone=m.mobile and l.app_type=m.brand+1', 'left')
                ->join('t_db_gift g', 'b.gift_act_id=g.id', 'left')
                ->join('t_db_seckill se', 'b.seckill_id=se.id', 'left')
                ->join('t_bu_order_yd yd', 'a.order_code=yd.order_code', 'left')
                ->join('t_db_pre_sale ps', 'b.pre_sale_id=ps.id', 'left')
                ->join('t_bu_order_point op', 'a.order_code=op.order_code', 'left')
                // ->join('t_db_order_invoice invoice', 'invoice.order_id=a.id', 'left')
                ->join("$subQuery invoice", 'a.id = invoice.order_id', "left")
                ->where($params['where'])
                ->where($params['where_or'])
                ->order($params['order'])
                ->group($params['group'])
                ->paginate($params['pagesize'], $params['count'], array('query' => $params['query']));

        } else {
            $list = $this->alias('a')
                ->field($params['field'])
                ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
                ->join('t_db_commodity_set_sku d', 'b.sku_id = d.id')
                ->join('t_db_commodity c', 'b.commodity_id=c.id')
                ->join('t_db_commodity_dlr_type f', 'a.commodity_dlr_type_id=f.id')
                ->join('t_db_dlr e', 'a.dd_dlr_code = e.dlr_code', 'left')
//                ->join('t_bu_wx_pay i', 'i.order_id=a.pay_order_code ', 'left')
                ->join('t_db_user j', 'a.user_id=j.id', 'left')
                ->join('t_db_full_discount k', 'b.full_id=k.id', 'left')
                ->join('t_db_gift g', 'b.gift_act_id=g.id', 'left')
                ->join('t_bu_order_yd yd', 'a.order_code=yd.order_code', 'left')
//                ->join('t_bu_card_receive_record l', 'a.user_id=l.user_id and a.id=l.act_id', 'left')
                ->join('t_db_seckill se', 'b.seckill_id=se.id', 'left')
                ->join('t_db_pre_sale ps', 'b.pre_sale_id=ps.id', 'left')
                ->join('t_bu_order_point op', 'a.order_code=op.order_code', 'left')
                ->join("$subQuery invoice", 'a.id = invoice.order_id', "left")
//                ->join('t_db_order_invoice invoice', 'invoice.order_id=a.id', 'left')
//                ->join('t_db_jd_warehouse jdw', 'b.jd_warehouse_send_id=jdw.id', 'left')
                ->where($params['where'])
                ->where($params['where_or'])
                ->order($params['order'])
                ->group($params['group'])
                ->paginate($params['pagesize'], $params['count'], array('query' => $params['query']));
        }
        return $list;
    }

    /**
     * 获取可以申请退款订单信息
     * @param $params
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getOrderRefundApply($params)
    {
        $params = $this->_checkParams($params);
        $list = $this->alias('a')
            ->field($params['field'])
            ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
            ->join('t_db_commodity_set_sku d', 'b.sku_id = d.id')
            ->join('t_db_commodity c', 'b.commodity_id=c.id')
            ->join('t_db_commodity_dlr_type f', 'a.commodity_dlr_type_id=f.id')
            ->join('t_db_dlr e', 'a.dd_dlr_code = e.dlr_code', 'left')
            ->join('t_bu_wx_pay i', 'i.order_id=a.pay_order_code ', 'left')
            ->join('t_db_user j', 'a.user_id=j.id', 'left')
            ->join('t_db_full_discount k', 'b.full_id=k.id', 'left')
            ->where($params['where'])
            ->where($params['where_or'])
            ->order($params['order'])
            ->group($params['group'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));

        return $list;
    }

    //获取一条数据连表查询
    public function getOneJoin($params)
    {
        $params = $this->_checkParams($params);
        $row = $this->alias('a')
            ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
            ->join('t_db_commodity c', 'b.commodity_id=c.id')
            ->join('t_db_user d', 'a.user_id=d.id', 'left')
            ->join('t_db_dlr e', 'a.dd_dlr_code = e.dlr_code', 'left')
            ->where($params['where'])->field($params['field'])->find();
        return $row;
    }

    /**
     * 订单信息菜单：连表查询获取订单信息
     * @param $params
     * @return array|false|\PDOStatement|string|\think\Model
     */
    public function getOneJoinInOrderInfo($params)
    {
        $params = $this->_checkParams($params);
        $info = $this->alias('a')
            ->field($params['field'])
            ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
            ->join('t_db_dlr c', 'a.dd_dlr_code=c.dlr_code', 'left')
            ->where($params['where'])
            ->group('a.order_code')
            ->find();
        return $info;
    }

    //获取所有数据连表查询
    public function getListJoin($params)
    {
        $params = $this->_checkParams($params);
        $row = $this->alias('a')->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
            ->join('t_db_commodity c', 'b.commodity_id=c.id')->where($params['where'])->field($params['field'])->select();
        return $row;
    }

    /**
     * @param $v
     * @return string|string[]
     */
    static function applyRefundorderStatus($v = '')
    {
        $data = [
            '2' => '已支付', '13' => '安装中', '19' => '已核销'
        ];
        if ($v != '' && !isset($data[$v])) return '';
        return empty($v) ? $data : $data[$v];

    }

    static function orderStatus($v = '')
    {
        //受理中-- 联友返回
        $data = [
            '1' => '已下单', '2' => '已支付', '3' => '已取消', '4' => '已发货', '5' => '已退款',
            '6' => '已过期', '7' => '已完成', '8' => '未支付', '9' => '已收货', '10' => '退款中',
            '11' => '发货中', '12' => '待发货', '13' => '安装中', '14' => '充值成功', '15' => '已付定金',
            '16' => '审核通过', '17' => '审核不通过', '18' => "交易关闭", '19' => '已核销', '20' => '待接单', '21' => '备货中', '22' => '待安装', '23' => '众筹中','24'=>'受理中'
        ];
        if ($v != '' && !isset($data[$v])) return '';
        return empty($v) ? $data : $data[$v];
    }


    /**
     * 延保服务投保进度
     * @param string $order_status
     * @return string|string[]
     */
    static function planStatus($order_status = '')
    {
        $data = [1 => '待付款', 2 => '已取消', 3 => '待受理', 4 => '受理中', 5 => '交易成功', 6 => '交易关闭'];
        if ($order_status == '') {
            return $data;
        } else {
            $plan_status = '';
            switch ($order_status) {
                case 1:
                case 8:
                    $plan_status = $data[1];
                    break;
                case 3:
                    $plan_status = $data[2];
                    break;
                case 2:
                    $plan_status = $data[3];
                    break;
                case 24:
                    $plan_status = $data[4];
                    break;
                case 7:
                    $plan_status = $data[5];
                    break;
                case 18:
                    $plan_status = $data[6];
                    break;
            }
            return $plan_status;
        }
    }


    /**
     * 投保进入转订单状态
     * @param $planStatus
     * @return array|int[]
     */
    static function planStatusToOrderStatus($planStatus)
    {
        $orderStatus = [];
        switch ($planStatus) {
            case 1:$orderStatus = [1,8]; break;
            case 2:$orderStatus = [3];break;
            case 3:$orderStatus = [2]; break;
            case 4: $orderStatus = [24];break;
            case 5:$orderStatus = [7];break;
            case 6: $orderStatus = [18];break;
        }
        return $orderStatus;
    }




    static function paymentMethod($v = '')
    {
        $data = [
            '1' => '现金', '2' => '积分', '3' => '卡劵', '4' => '现金+积分', '5' => '现金+卡劵', '6' => '积分+卡劵', '7' => '现金+积分+卡劵',
        ];
        if ($v != '' && !isset($data[$v])) return '';
        return empty($v) ? $data : $data[$v];

    }


    /**
     * 订单来源
     */
    static function orderSource()
    {

        return [
            0 => '无', 1 => '商品订单', 2 => '拼团订单', 3 => '套装', 4 => '礼包', 5 => '到店有礼', 6 => '积分-日产', 7 => "会员日",
            8 => '锦尚阳光口罩',9  => 'N延保', 11 => 'CCS订单', 12 => '虚拟商品订单', 13 => '电子卡券商品订单', 14 => '积分-启辰', 15 => '到店备件',
            16 => '保养套餐-老友惠保养套餐', 17 => '保养套餐-心悦保养套餐', 18 => '保养套餐-双保专属权益套餐', 19 => '保养套餐-其他',
            20 => '到店代金券', 21 => '到店电子券', 22 => 'pz1a套餐', 23 => '工时商品', 24 => '取送车券', 25 => '启辰充电桩',
            26 => "众筹商品", 27 => 'PV工会线上订单', 28 => 'PV总工会', //27-37 都给工会
            40 => '保养套餐-五年双保专享心悦套餐',42=>'日产充电桩',43=>'延保服务包商品'
        ];

    }


    /**
     * 获取订单商品详情
     * @param $params
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getOrderCommodityInfo($params)
    {
        $params = parent::_checkParams($params);
        $list = $this->alias('a')
            ->field($params['field'])
            ->join('t_bu_order_commodity b', 'a.order_code = b.order_code')
            ->join('t_db_commodity c', 'b.commodity_id = c.id')
            ->join('t_db_commodity_set_sku d', 'b.sku_id = d.id')
            ->join('t_db_commodity_sku e', 'd.commodity_sku_id = e.id', 'left')
            ->join('t_db_separate_account_rules f', 'b.spr_id = f.id', 'left')
            ->where($params['where'])
            ->order($params['order'])
            ->select();
        return $list;
    }


    /**
     * 获取订单商品详情--2023-03-03 14:20:00 尽量不要拿skuID，去获取下单时候的信息
     * 应该是202302月版本导致sku会在下架之后被删除，导致查不出结果。--补坑
     * @param $params
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getOrderCommodityInfoNoSku($params)
    {
        $params = parent::_checkParams($params);
        $list = $this->alias('a')
            ->field($params['field'])
            ->join('t_bu_order_commodity b', 'a.order_code = b.order_code')
            ->join('t_db_commodity c', 'b.commodity_id = c.id')
            ->join('t_db_commodity_type e', 'c.comm_type_id=e.id', 'left')
            ->join('t_db_commodity_type f', 'e.comm_parent_id=f.id', 'left')
            ->join('t_db_commodity_type g', 'f.comm_parent_id=g.id', 'left')
//            ->join('t_db_commodity_set_sku d', 'b.sku_id = d.id')
//            ->join('t_db_commodity_sku e', 'd.commodity_sku_id = e.id')
//            ->join('t_db_separate_account_rules f', 'b.spr_id = f.id', 'left')
            ->where($params['where'])
            ->order($params['order'])
            ->select();
        return $list;
    }


    public function getReportList($params)
    {
        $params = parent::_checkParams($params);
        $list = $this->alias('c')
            ->join('t_ac_new_car_bag e', 'c.new_car_bag_ac_id=e.id', 'inner')
            ->join('t_bu_order_commodity b', 'c.order_code=b.order_code', 'inner')
            ->join('t_bu_new_car_bag_index a', 'find_in_set(a.id,e.new_car_bag_index_id)', 'inner')
            ->join('t_db_dlr d', 'c.dd_dlr_code=d.dlr_code', 'inner')
            ->where($params['where'])
            ->field($params['field'])->order($params['order'])->group($params['group'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;

    }

    public function getPz1aList($params)
    {
        $params = parent::_checkParams($params);
        $list = $this->alias('a')
            ->join("t_bu_order_commodity b", "a.order_code=b.order_code")
            ->join('t_db_dlr c', 'a.dd_dlr_code=c.dlr_code')
            ->where($params['where'])
            ->field($params['field'])->order($params['order'])->group($params['group'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }


    /**
     * 销售来源
     */

    static function salesource()
    {
        return ['0' => '旧数据', '1' => '平台自营销售', '2' => '专营店销售', '3' => '官微销售',];
    }


    /**
     * 活动id
     */

    static function act_name($act_code)
    {
        $res = [0 => '', 1 => '限时折扣', 2 => '团购', 3 => '满减', 4 => '全积分折扣', 5 => '套装', 6 => 'N件N折', 7 => '预售', '8' => '立减'];
        return $res[$act_code];
    }

    /**
     * 支付方式
     */

    static function pay_type($act_code)
    {
        $res = array(1 => '现金', 2 => '积分', 3 => '卡劵', 4 => '现金+积分', 5 => '现金+卡劵', 6 => '积分+卡劵', 7 => '现金+积分+卡劵');
        return $res[$act_code];
    }


    public function getOrderAndRefund($params)
    {
        $params = $this->_checkParams($params);
        $list = $this->alias('a')
            ->field($params['field'])
            ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
            ->join('t_db_commodity c', 'b.commodity_id=c.id')
            ->join('t_db_commodity_dlr_type f', 'a.commodity_dlr_type_id=f.id')
            ->join('t_db_dlr e', 'a.dd_dlr_code = e.dlr_code', 'left')
            ->join('t_bu_component_auth d', 'd.dlr_code=a.dd_dlr_code ', 'left')
            ->join('t_bu_order_refund g', 'a.order_code=g.order_code and g.status=0 and g.is_check=1', 'left')
            ->where($params['where'])
            ->where($params['where_or'])
            ->order($params['order'])
            ->group($params['group'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }

    public function getOneOrder($user_id, $order_id)
    {
        $row = $this->where(['order_code' => $order_id, 'jifen_user_id' => $user_id])->field('order_code as order_id,name,phone,total_money sales_total_price, order_status as shop_order_state,receipt_address address,total_money-mail_price goods_price,created_date created_order_time,mail_price freight')
            ->find();
        return $row;
    }

    public function getOneCarOrder($user_id, $order_id)
    {
        $row = $this->alias("a")->join("t_bu_card_order b", "a.order_code = b.order_code")->where(['a.order_code' => $order_id, 'a.jifen_user_id' => $user_id])->field('a.order_code as order_id,a.name,a.phone,a.total_money sales_total_price, a.order_status as shop_order_state,a.receipt_address address,goods_money goods_price,a.created_date created_order_time,a.mail_price freight')
            ->find();
        return $row;
    }

    public function getOneOrderCommodity($order_code, $sku_id)
    {
        $row = $this->alias('a')->join('t_bu_order_commodity b', 'a.order_code = b.order_code')->where(['a.order_code' => $order_code, 'b.sku_id' => $sku_id])
            ->field('a.*,b.sku_id,b.commodity_id,b.commodity_name as goods_name,b.count as goods_num,b.sku_info,b.price')->find();
        return $row;
    }

    //查询一个商品的购买数量
    public function getGoodsSum($param)
    {
        $param = $this->_checkParams($param);
        $row = $this->alias('a')->join('t_bu_order_commodity b', 'a.order_code = b.order_code')->where($param['where'])->field($param['field'])->group($param['group'])->find();
        return $row;
    }


    /**
     * 联flat表查询相关优惠
     * @param $params
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getListFlat($params)
    {
        $params = $this->_checkParams($params);
        $res = $this->alias('a')->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
            ->join('t_db_commodity c', 'b.commodity_id=c.id')
            ->join('t_db_commodity_flat d', "d.commodity_id=c.id")
            ->where($params['where'])->field($params['field'])->select();
        return $res;
    }

    /**
     * 经销商订单列表
     * @param $params
     */
    public function getDlrOrderList($params)
    {
        if ($params['is_down'] == 1) { // 下载文件不需要分页
            $order_list = $this->alias('a')
                ->field($params['field'])
                ->join('t_db_after_sale_orders b', 'a.id=b.order_id', 'left')
                ->join('t_bu_order_commodity c', 'a.order_code=c.order_code', 'left')
                ->join('t_bu_order_settlement d', 'a.order_code=d.order_code', 'left')
                ->where($params['where'])
                ->order($params['order'])
                ->group('a.id')
                ->select();
            $total = '';
            $per_page = '';
            $current_page = '';
        } else {
            if (isset($params['field1'])) {
                $list = $this->alias('a')
                    ->field($params['field1'])
                    ->join('t_bu_order_settlement d', 'a.order_code=d.order_code and a.dd_dlr_code = d.intention_account_id', 'left')
                    ->join('t_bu_order_commodity c', 'a.order_code=c.order_code', 'left')
                    ->where($params['where'])
                    ->group($params['group'])
                    ->order($params['order'])
                    ->paginate($params['pagesize'], false, array('query' => $params['query']))
                    ->toArray();
            } else {
                $list = $this->alias('a')
                    ->field($params['field'])
                    ->join('(select * from (select order_id, afs_service_id from t_db_after_sale_orders order by id desc) afs group by order_id) b', 'a.id=b.order_id', 'left')
                    ->join('t_bu_order_commodity c', 'a.order_code=c.order_code', 'left')
                    ->join('t_bu_order_settlement d', 'a.order_code=d.order_code and a.dd_dlr_code = d.intention_account_id', 'left')
                    ->where($params['where'])
                    ->group($params['group'])
                    ->order($params['order'])
                    ->paginate($params['pagesize'], false, array('query' => $params['query']))
                    ->toArray();
            }

            $order_list = $list['data'];
            $total = $list['total'];
            $per_page = $list['per_page'];
            $current_page = $list['current_page'];
        }

        $c_model = new DbCard();
        $after_sale = new DbAfterSaleOrders();

        foreach ($order_list as $key => $val) {
            $order_list[$key]['order_source_name'] = $this::orderSource()[$val['order_source']];
            $order_list[$key]['order_status_name'] = $this::orderStatus($val['order_status']);
            if (!empty($val['card_id'])) {
                $c_params = [
                    'where' => ['id' => ['in', $val['card_id']]],
                    'field' => 'card_id,card_name'
                ];

                $order_list[$key]['card_name'] = $c_model->getCardName($c_params);
            } else {
                $order_list[$key]['card_name'] = '';
            }
            if (!isset($val['afs_service_id'])) {
                $after_sale_orders = $after_sale->getOne(['field' => 'afs_service_id', 'where' => ['order_id' => $val['id']]]);

                $order_list[$key]['afs_service_id'] = $after_sale_orders['afs_service_id'] ?? null;
            }

            // 手机号脱敏
            $order_list[$key]['phone'] = $val['phone'] ? substr_replace($val['phone'], '****', 3, 4) : '';;
        }

        return ['total' => $total, 'per_page' => $per_page, 'current_page' => $current_page, 'order_list' => $order_list];
    }


    /**
     * 订单号关联 1：N
     * @return mixed
     */
    public function orderSettlements()
    {
        return $this->hasMany(BuOrderSettlement::class, 'order_code', 'order_code')->field('cashier_settlement_no');
    }


    /**
     * 预售支付订单号关联
     * @return mixed
     */
    public function orderSettlementByTradeNo2()
    {
        return $this->hasOne(BuOrderSettlement::class, 'cashier_trade_no', 'cashier_trade_no2')->field('cashier_settlement_no');
    }


    // 查询售后单号
    public function afsServiceId()
    {
        return $this->hasOne(DbAfterSaleOrders::class, 'order_id', 'id')->order('id desc');
    }

    // 获取银行名称
    public function lyBankName($val)
    {
        $arr = ['01' => '招行', '02' => '工行'];
        return $arr[$val] ?? '';
    }


    /**
     * 尾款支付订单号关联
     * @return mixed
     */
    public function orderSettlementByTradeNo()
    {
        return $this->hasOne(BuOrderSettlement::class, 'cashier_trade_no', 'cashier_trade_no')->field('cashier_settlement_no');
    }


    /**
     * 订单商品 订单号关联 1:N
     * @return \think\model\relation\HasMany
     */
    public function orderCommodities()
    {
        return $this->hasMany(BuOrderCommodity::class, 'order_code', 'order_code');
    }

    /**
     * 可后台手动申请售后订单
     * */
    public function canAfterOrder($params)
    {
        $params = $this->_checkParams($params);
        return $this
            ->alias('a')
            ->field($params['field'])
            ->join('(select * from (select order_id,afs_status,afs_service_id from t_db_after_sale_orders order by id desc) as c group by c.order_id) b', 'a.id=b.order_id', 'left')
            ->join('t_bu_card_receive_record d', 'a.user_id=d.user_id and a.id=d.act_id', 'left')
            ->where($params['where'])
            ->order($params['order'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));

    }


    /**
     * 订单商品 订单号关联 1:N
     * @return \think\model\relation\HasMany
     */
    public function orderCommoditiesNoMoId()
    {
        return $this->hasMany(BuOrderCommodity::class, 'order_code', 'order_code')->where(['mo_id' => 0]);
    }

    //查询列表
    public function getDfsOrderPaginate($params)
    {
        $params = $this->_checkParams($params);
        $list = $this->alias('a')
            ->field($params['field'])
            ->join('t_bu_order_commodity b', 'a.order_code=b.order_code')
            ->join('t_db_commodity_set_sku d', 'b.sku_id = d.id')
            ->join('t_db_commodity c', 'b.commodity_id=c.id')
            ->join('t_db_commodity_dlr_type f', 'a.commodity_dlr_type_id=f.id')
            ->join('t_db_dlr e', 'a.dd_dlr_code = e.dlr_code', 'left')
            ->join('t_db_user j', 'a.user_id=j.id', 'left')
            ->join('t_db_full_discount k', 'b.full_id=k.id', 'left')
            ->join('t_db_gift g', 'b.gift_act_id=g.id', 'left')
            ->join('t_bu_order_yd yd', 'a.order_code=yd.order_code', 'left')
            ->join('t_db_seckill se', 'b.seckill_id=se.id', 'left')
            ->join('t_db_pre_sale ps', 'b.pre_sale_id=ps.id', 'left')
            ->join('t_bu_order_point op', 'a.order_code=op.order_code', 'left')
            ->where($params['where'])
            ->where($params['where_or'])
            ->order($params['order'])
            ->group($params['group'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }


    public function commodities()
    {
        $field = 'id,order_code,commodity_id,commodity_name';
        return $this->hasMany(BuOrderCommodity::class, 'order_code', 'order_code')->where('mo_id', 0)->field($field);
    }

    public static function getListWithInvoiceOnly($order_codes)
    {
        if (is_string($order_codes)) {
            $order_codes = explode(',', $order_codes);
        }
        $instance = new self();
        $instance->alias('t1');
        $instance->field([
                "order_code",
                "dlr_code",
                "channel",
                "source",
                //  "total_money",
                "money",
                "created_date as order_created_date",
                "settlement_time",
                "name as receipt_name",
                "phone as receipt_phone",
                "receipt_address"
            ]
        );

        $instance->whereIn('order_code', $order_codes);
        return $instance->select();
    }

    public static function getListWithInvoice($order_codes)
    {
        if (is_string($order_codes)) {
            $order_codes = explode(',', $order_codes);
        }
        $instance = new self();
        $instance->alias('t1');
        $instance->field([
                "order_code",
                "dlr_code",
                "channel",
                "source",
                //  "total_money",
                "money",
                "created_date as order_created_date",
                "settlement_time",
                "name as receipt_name",
                "phone as receipt_phone",
                "receipt_address"
            ]
        );
        $instance->where('settlement_state', '=', '1');

        $instance->where(function ($query) {
            $sql2 = "EXISTS(SELECT 1 FROM t_db_tax_invoice_apply_detail detail INNER JOIN t_db_tax_invoice_apply apply ON ( detail.apply_id = apply.id ) WHERE apply.apply_invoice_status in('101','104') AND detail.good_order_no = t1.order_code)";
            $sql = "EXISTS(SELECT 1 FROM t_db_tax_invoice_apply_detail detail INNER JOIN t_db_tax_invoice_record record ON ( detail.apply_id = record.apply_id ) WHERE record.invoice_status IN('1001','1004') and is_red_invoice=0 AND detail.good_order_no = t1.order_code)";

            $query->whereOr($sql);
            $query->whereOr($sql2);
        });

        $instance->whereIn('order_code', $order_codes);
        return $instance->select();
    }

    /**
     *
     * @param $order_codes
     * @return bool
     */
    public function orderFromSameUser($order_codes)
    {
        if (is_string($order_codes)) {
            $order_codes = explode(',', $order_codes);
        }
        $instance = new self();
        $instance->whereIn('order_code', $order_codes);
        $list = $instance->select();

        $user_ids = [];
        foreach ($list as $item) {
            $user_ids[$item['user_id']] = $item['user_id'];
        }
        return !(count($user_ids) > 1);
    }

    public static function getListWithoutInvoice($params)
    {
        if (empty($params['page'])) {
            $params['page'] = 1;
        }

        if (empty($params['pagesize'])) {
            $params['pagesize'] = 10;
        }
        $instance = new self();
        $instance->alias('t1');
        $instance->field([
            "`t1`.`order_code`",
            "`t1`.`dlr_code`",
            "`t1`.`channel`",
            "if(`t1`.`order_source`=0,1,`t1`.`order_source`) as source",
            "`t1`.`money`",
            "`t1`.`created_date` as order_created_date",
            "`t1`.`settlement_time`",
            "`t1`.`name`         as receipt_name",
            "`t1`.`phone`        as receipt_phone",
            "`t1`.`receipt_address`",
            "`user`.`phone`      as user_phone",
        ]);
        $instance->join("t_db_user user", "user.id = t1.user_id");
        $instance->where('t1.money', '>', 0);
        $instance->where('logistics_mode', '=', '2');
//        $instance->where("dd_dlr_code is null OR dd_dlr_code=''");
        $instance->where('settlement_state', '=', '1');

//      开票请求中的 record.`invoice_data_status` IN (0, 1)
//      审核通过，请求中、未开票、未冲红、冲红中 (record.`invoice_data_status` = 2 and record.`is_red_invoice` IN (2, 1)
//     未确认的申请、等待审核的 `apply`.`apply_invoice_status` in (101, 100)

        $sql_str = <<<STR
SELECT 1
        FROM `t_db_tax_invoice_apply_detail` detail
                 INNER JOIN `t_db_tax_invoice_apply` apply ON detail.`apply_id` = apply.`id`
                 LEFT JOIN `t_db_tax_invoice_record` record ON record.`apply_id` = detail.`apply_id`
        WHERE detail.`good_order_no` = t1.order_code
          and (
                (`apply`.`apply_invoice_status` in (101, 100))
                OR (record.`invoice_data_status` IN (0, 1))
                OR (`apply`.`apply_invoice_status`=104 AND record.`is_red_invoice`=0)
              )
STR;

        $instance->whereNotExists($sql_str);
        $instance->order('`t1`.id desc');

        //1待开票/2已冲红/3开票驳回
        if (!empty($params['invoice_status'])) {

            if (is_string($params['invoice_status'])) {
                $params['invoice_status'] = explode(',', $params['invoice_status']);
            }
            $instance->where(function ($query) use ($params) {
                if (in_array(1, $params['invoice_status'])) {
                    //待开票：未开过票
                    $sql = <<<STR
NOT EXISTS(
SELECT
	1
FROM
	`t_db_tax_invoice_apply_detail` detail
	INNER JOIN `t_db_tax_invoice_apply` apply ON detail.`apply_id` = apply.`id`
	LEFT JOIN `t_db_tax_invoice_record` record ON record.`apply_id` = detail.`apply_id` 
WHERE
	detail.`good_order_no`=t1.order_code)
STR;
                    $query->whereOr($sql);
                   // $query->whereNotExists($sql);
                }
                if (in_array(2, $params['invoice_status'])) {
                    //已冲红
                    $sql = "EXISTS(SELECT 1 FROM t_db_tax_invoice_apply_detail detail INNER JOIN t_db_tax_invoice_record record ON ( detail.apply_id = record.apply_id ) WHERE record.invoice_data_status = 2 and record.is_red_invoice=1 AND detail.good_order_no = t1.order_code)";
                    $query->whereOr($sql);
                   // $query->whereExists($sql);
                }
                if (in_array(3, $params['invoice_status'])) {
                    //开票驳回
                    $sql = "EXISTS(SELECT 1 FROM t_db_tax_invoice_apply_detail detail INNER JOIN t_db_tax_invoice_apply apply ON ( detail.apply_id = apply.id ) WHERE apply.apply_invoice_status = '107' AND detail.good_order_no = t1.order_code)";
//                    $query->whereExists($sql);
                    $query->whereOr($sql);
                }
            });


        }
        if (!empty($params['channel'])) {
            $channel_array = [];
            if (is_string($params['channel'])) {
                $channel_array = explode(',', $params['channel']);
            }
            if (is_array($params['channel'])) {
                $channel_array = $params['channel'];
            }
            $instance->whereIn('t1.channel', $channel_array);
        }

        if (!empty($params['order_code'])) {
            if (is_string($params['order_code'])) {
                $order_code = explode(',', $params['order_code']);
            } else {
                $order_code = $params['order_code'];
            }

            $instance->whereIn('t1.order_code', $order_code);
        }

        if (!empty($params['source'])) {
            $source = [];
            if (is_string($params['source'])) {
                $source = explode(',', $params['source']);
            }
            if (is_array($params['source'])) {
                $source = $params['source'];
            }
            $instance->whereIn('t1.order_source', $source);
        }

        if (!empty($params['receipt_name'])) {
            $instance->where('t1.name', 'like', "%{$params['receipt_name']}%");
        }

        if (!empty($params['receipt_phone'])) {
            $instance->where('t1.phone', '=', $params['receipt_phone']);
        }

        if (!empty($params['user_phone'])) {
            $instance->where('user.phone', '=', $params['user_phone']);
        }

        if (!empty($params['order_start_date']) && $params['order_end_date']) {
            $instance->where('t1.created_date', '>=', $params['order_start_date']);
            $instance->where('t1.created_date', '<=', $params['order_end_date']);
        }

        if (!empty($params['settlement_start_time']) && $params['settlement_end_time']) {
            $instance->where('t1.settlement_time', '>=', $params['settlement_start_time']);
            $instance->where('t1.settlement_time', '<=', $params['settlement_end_time']);
        } else {
            $instance->where('t1.settlement_time', '>=', '2025-04-01');
        }

        $result = $instance->paginate($params['pagesize'] ?? 10, false);

        $result_array = $result->toArray();
        static::setFieldAttr($result_array['data']);
        return $result_array;
    }

    protected static function setFieldAttr(&$data)
    {
        $order_codes = collection($data)->column('order_code');

        //订单最新的状态
        $order_sort_data = Db::table('t_db_tax_invoice_apply_detail')
            ->field([
                "detail.good_order_no",
                "apply.id as apply_id",
                "apply.apply_invoice_status",
                "record.is_red_invoice",
                "record.invoice_data_status",
            ])
            ->alias('detail')
            ->join('t_db_tax_invoice_apply apply', 'detail.apply_id = apply.id')
            ->join('t_db_tax_invoice_record record', 'record.apply_id = detail.apply_id', 'left')
            ->order('apply.id desc')
            ->whereIn('detail.good_order_no', $order_codes)
            ->select();

        $order_data = [];
        foreach ($order_sort_data as $item) {
            if (!isset($order_data[$item['good_order_no']])) $order_data[$item['good_order_no']] = $item;
        }

        foreach ($data as &$datum) {
            $datum['invoice_status'] = 1;//等待开票
            $datum['apply_invoice_status'] = 0;//审核状态
            $datum['is_red_invoice'] = 0;//未冲红
            if (isset($order_data[$datum['order_code']])) {
                $tmp_data = $order_data[$datum['order_code']];
                if ($tmp_data['apply_invoice_status'] == '107') {
                    //审核驳回
                    $datum['invoice_status'] = 3;
                    continue;
                }
                if ($tmp_data['invoice_data_status'] == 2 && $tmp_data['is_red_invoice'] == 1) {
                    //冲红
                    $datum['invoice_status'] = 2;
                    continue;
                }
            }
        }
    }

    public static function getListWithoutInvoice2($params)
    {
        if (empty($params['page'])) {
            $params['page'] = 1;
        }

        if (empty($params['pagesize'])) {
            $params['pagesize'] = 10;
        }

        $instance = new self();
        $instance->alias('t1');
        $instance->field([
                "t1.order_code",
                "t1.dlr_code",
                "t1.channel",
                "t1.source",
                "IF(ISNULL( apply.id),1,IF(apply.apply_invoice_status = '107',3,IF(apply.apply_invoice_status ='104' AND record.is_red_invoice = 1, 2, 1 ))) AS invoice_status",
                //  "IF(ISNULL(apply.apply_invoice_status),0,apply.apply_invoice_status) as apply_invoice_status",
                "apply.apply_invoice_status",
                //   "IF(ISNULL(record.is_red_invoice),0,record.is_red_invoice) as is_red_invoice",
                "record.is_red_invoice",
                "money",
                "t1.created_date as order_created_date",
                "settlement_time",
                "t1.name as receipt_name",
                "t1.phone as receipt_phone",
                "t1.receipt_address",
                "user.phone as user_phone"
            ]
        );
        $instance->where('logistics_mode', '=', '2');
        $instance->where("dd_dlr_code is null OR dd_dlr_code=''");
        $instance->where('settlement_state', '=', '1');

        $params['invoice_status'] = $params['invoice_status'] ?? 0;

        //1待开票/2已冲红/3开票驳回
        if (!empty($params['invoice_status'])) {

            if ($params['invoice_status'] == 1) {
                //待开票
                $sql = <<<STR
SELECT
	1
FROM
	`t_db_tax_invoice_apply_detail` detail
	INNER JOIN `t_db_tax_invoice_apply` apply ON detail.`apply_id` = apply.`id`
	LEFT JOIN `t_db_tax_invoice_record` record ON record.`apply_id` = detail.`apply_id` 
WHERE
	detail.`good_order_no`=t1.order_code
STR;
//待开票：未开过票
                $instance->whereNotExists($sql);
            }
            if ($params['invoice_status'] == 2) {
                //已冲红
                $sql = "SELECT 1 FROM t_db_tax_invoice_apply_detail detail INNER JOIN t_db_tax_invoice_record record ON ( detail.apply_id = record.apply_id ) WHERE record.invoice_data_status = 2 and record.is_red_invoice=1 AND detail.good_order_no = t1.order_code";
                $instance->whereExists($sql);
            }
            if ($params['invoice_status'] == 3) {
                //开票驳回
                $sql = "SELECT 1 FROM t_db_tax_invoice_apply_detail detail INNER JOIN t_db_tax_invoice_apply apply ON ( detail.apply_id = apply.id ) WHERE apply.apply_invoice_status = '107' AND detail.good_order_no = t1.order_code";
                $instance->whereExists($sql);
            }
        }


        //不存在开票成功，未冲红的数据，申请中的数据
        $sql2 = <<<STR
SELECT
	1
FROM
	`t_db_tax_invoice_apply_detail` detail
	INNER JOIN `t_db_tax_invoice_apply` apply ON detail.`apply_id` = apply.`id`
	LEFT JOIN `t_db_tax_invoice_record` record ON record.`apply_id` = detail.`apply_id` 
WHERE
	detail.`good_order_no`=t1.order_code and ((record.invoice_data_status = 2 and record.is_red_invoice=0) OR record.invoice_data_status=0 OR apply.apply_invoice_status IN('100','101'))
STR;
//待开票：未开过票
        $instance->whereNotExists($sql2);

        if (!empty($params['channel'])) {
            $channel_array = [];
            if (is_string($params['channel'])) {
                $channel_array = explode(',', $params['channel']);
            }
            if (is_array($params['channel'])) {
                $channel_array = $params['channel'];
            }
            $instance->whereIn('t1.channel', $channel_array);
        }

        if (!empty($params['order_code'])) {
            if (is_string($params['order_code'])) {
                $order_code = explode(',', $params['order_code']);
            } else {
                $order_code = $params['order_code'];
            }

            $instance->whereIn('t1.order_code', $order_code);
        }

        if (!empty($params['source'])) {
            $source = [];
            if (is_string($params['source'])) {
                $source = explode(',', $params['source']);
            }
            if (is_array($params['source'])) {
                $source = $params['source'];
            }
            $instance->whereIn('t1.source', $source);
        }

        if (!empty($params['receipt_name'])) {
            $instance->where('t1.name', 'like', "%{$params['receipt_name']}%");
        }

        if (!empty($params['receipt_phone'])) {
            $instance->where('t1.phone', '=', $params['receipt_phone']);
        }

        if (!empty($params['order_start_date']) && $params['order_end_date']) {
            $instance->where('t1.created_date', '>=', $params['order_start_date']);
            $instance->where('t1.created_date', '<=', $params['order_end_date']);
        }

        if (!empty($params['settlement_start_time']) && $params['settlement_end_time']) {
            $instance->where('t1.settlement_time', '>=', $params['settlement_start_time']);
            $instance->where('t1.settlement_time', '<=', $params['settlement_end_time']);
        }

        $instance->join("t_db_tax_invoice_apply_detail detail", "detail.good_order_no = t1.order_code", "LEFT");
        $instance->join("t_db_tax_invoice_apply apply", "detail.apply_id = apply.id", "LEFT");
        $instance->join("t_db_tax_invoice_record record", "record.apply_id = detail.apply_id", "LEFT");
        $instance->join("t_db_user user", "user.id = t1.user_id", "LEFT");
        $instance->group('t1.id');
        $instance->order('t1.id', 'desc');

        return $instance->paginate($params['pagesize'] ?? 10, false);
    }

    public function getOrderList($params = [])
    {
        $params = $this->_checkParams($params);
        $list = $this->alias('a')
            ->join("t_db_tax_invoice_apply_detail d", "a.order_code = d.good_order_no", "left")
            ->join("t_db_tax_invoice_apply c", "d.apply_id = c.id", "left")
            ->join("t_db_tax_invoice_record record", "record.apply_id = c.id and record.is_red_invoice=0", "left")
            ->where($params['where'])->field($params['field'])->order($params['order'])->group($params['group'])->limit($params['limit'])->select();
        return $list;
    }

    public function getMainOrderId($orderId){
        $orderObj = new BuOrder();
        $spitOrder = $orderObj->where(['id'=>$orderId,'parent_order_type'=>2])->find();
        if(!empty($spitOrder)){
            $suborder = $orderObj->where(['order_code'=>$spitOrder['parent_order_code']])->find();
            $data['order_id'] = $suborder['id'];
            $data['order_code'] = $suborder['order_code'];
        }else{
            $order = $orderObj->where(['id'=>$orderId])->find();
            $data['order_id'] = $order['id'];
            $data['order_code'] = $order['code'];
        }
        return $data;
    }

    /**
     * 映射本地 payment_method 到供应商接口要求的格式
     * 1=现金，2=积分，3=卡券，积分+卡券传1,2
     * @param int|string $v
     * @return string
     */
    static function mapPaymentMethodForSupplier($v)
    {
        // 现金=1，积分=2，卡券=3
        // 本地：1=现金，2=积分，3=卡劵，4=现金+积分，5=现金+卡劵，6=积分+卡劵，7=现金+积分+卡劵
        $map = [
            1 => '1', // 现金
            2 => '2', // 积分
            3 => '3', // 卡券
            4 => '1,2', // 现金+积分
            5 => '1,3', // 现金+卡券
            6 => '2,3', // 积分+卡券
            7 => '1,2,3', // 现金+积分+卡券
        ];
        return isset($map[$v]) ? $map[$v] : '';
    }

}
