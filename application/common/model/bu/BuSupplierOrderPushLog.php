<?php

namespace app\common\model\bu;

use app\common\model\Common;

/**
 * 供应商订单推送日志表
 */
class BuSupplierOrderPushLog extends Common
{
    // 表名
    protected $table = 't_bu_supplier_order_push_log';
    // 自动写入时间戳
    protected $pk = 'id';

    /**
     * 获取列表数据
     * @param array $params
     * @return \think\Paginator
     */
    public function getListData($params)
    {
        $params = $this->_checkParams($params);
        return $this->alias('a')
            ->field($params['field'])
            ->where($params['where'])
            ->order($params['order'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));
    }

    /**
     * 导出数据方法 - 查询所有
     * @param array $params
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getListDataDown($params)
    {
        $params = $this->_checkParams($params);
        $list = $this->alias('a')
            ->where($params['where'])
            ->field($params['field'])
            ->order($params['order'])
            ->group($params['group'])
            ->select();
        return $list;
    }
}
