<?php
/**
 * 合作伙伴回调日志模型
 * @author: AI Assistant
 * @time: 2025-01-21
 */

namespace app\common\model\inter;

use app\common\model\Common;

class IPartnerCallbackLog extends Common
{
    protected $table = 't_i_partner_callback_log';
    protected $pk = 'id';

    /**
     * 获取回调日志列表
     * @param array $params
     * @return \think\Paginator
     */
    public function getListData($params)
    {
        $params = $this->_checkParams($params);
        return $this->alias('a')
            ->field($params['field'])
            ->where($params['where'])
            ->order($params['order'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));
    }

    /**
     * 导出数据方法 - 查询所有
     * @param array $params
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getListDataDown($params)
    {
        $params = $this->_checkParams($params);
        $list = $this->alias('a')
            ->where($params['where'])
            ->field($params['field'])
            ->order($params['order'])
            ->group($params['group'])
            ->select();
        return $list;
    }

    /**
     * 记录回调日志
     * @param array $data
     * @return int|string
     */
    public function logCallback($data)
    {
        $logData = [
            'callback_type' => $data['callback_type'] ?? '',
            'order_detail_no' => $data['order_detail_no'] ?? '',
            'request_url' => $data['request_url'] ?? '',
            'request_method' => $data['request_method'] ?? 'POST',
            'request_params' => $data['request_params'] ?? '',
            'response_data' => $data['response_data'] ?? '',
            'process_status' => $data['process_status'] ?? 0, // 0-处理中 1-成功 2-失败
            'error_message' => $data['error_message'] ?? '',
            'created_date' => date('Y-m-d H:i:s'),
            'remark' => $data['remark'] ?? ''
        ];

        return $this->insertGetId($logData);
    }

    /**
     * 更新处理结果
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateProcessResult($id, $data)
    {
        $updateData = [
            'process_status' => $data['process_status'],
            'response_data' => $data['response_data'] ?? '',
            'error_message' => $data['error_message'] ?? '',
            'last_updated_date' => date('Y-m-d H:i:s')
        ];

        return $this->where('id', $id)->update($updateData);
    }
}
