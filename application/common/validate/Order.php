<?php

namespace app\common\validate;

use think\Validate;

/**
 * 订单 验证器
 * Class Order
 * @package app\common\validate
 */
class Order extends Validate
{
    /**
     * @var string[]
     */


    protected $rule = [
        'order_id'                 => 'number',
        'hit_type_code'            => 'length:0,100',
        'sku_ids'                  => 'require|length:0,1000',
        'is_subs'                  => 'length:0,1000',
        'numbers'                  => 'require|length:0,1000',
        'suit_id'                  => 'number',
        'cart_id'                  => 'length:0,1000',
        'group_id'                 => 'number',
        'order_source'             => 'length:0,1000',
        'promotion_source'         => 'length:0,1000',
        'group_order_code'         => 'length:0,1000',
        'is_by_tc'                 => 'number|length:1',
        'name'                     => 'length:0,1000',
        'phone'                    => 'alphaDash',
        'choose_dlr_code'          => 'length:0,10',
        'act_type_id'              => 'length:0,1000',
        'act_id'                   => 'length:0,1000',
        'wi_act_type_id'           => 'length:0,1000',
        'wi_act_id'                => 'length:0,1000',
        'address'                  => 'length:2,1000',
        'logistics_mode'           => 'number',
        'point'                    => 'number',
        'dlr_point'                => 'number',
        'cards'                    => 'length:0,1000',
        'pick_delivery_card_codes' => 'length:0,1000',
        'pick_up_order_code'       => 'length:0,1000',
        'card_money'               => 'length:0,1000',
        'mail_method'              => 'require|length:0,1000',

        'page'             => 'number',
        'pageSize'         => 'number|between:1,100',
        'order_status'     => 'number',
        'by_type'          => 'number',
        'list_type'        => 'in:1,2',
        'order_code'       => 'length:1,50',
        'nick_name'        => 'length:0,50',
        'head_img'         => 'length:0,1000',
        'choose_card'      => 'length:0,1000',
        'all_card'         => 'length:0,1000',
        'return_url'       => 'length:0,1000',
        'address_area_ids' => 'length:0,1000',

        'rea_id'      => 'require|number',
        'code'        => 'number',
        'order_phone' => 'number|length:11|require',
        'vin_phone'   => 'number|length:11|require',
        //        'mail_type'   => 'number',

        'created_date'    => 'dateFormat:Y-m-d H:i:s',
        'distance'        => 'number',
        'goods_id'        => 'number',
        'point_js'        => 'length:0,10000',
        'old_dd_dlr_code' => 'length:0,10000',
        'new_dd_dlr_code' => 'length:0,10000',
        'dd_dlr_code'     => 'length:0,10000',
        'work_time_json'  => 'length:0,10000',
        'com_json'        => 'length:0,10000',
        'pay_type'        => 'length:0,20',
        'vin'             => 'length:0,20',
        'part_no'         => 'length:0,1000',
        'dlr_code'        => 'length:0,100',
        'sourcecode'      => 'length:0,1000',

        'gift_act_id' => 'number',
        'gift_card_id' => 'length:0,1000',
        'gift_c_json' => 'length:0,1000',
        'gift_c_json_list' => 'length:0,1000',

        'choose_promotion_json' => 'length:0,1000',
        'choose_card_ids'       => 'length:0,2000',
        'is_choose'             => 'number',

        'use_discount'            => 'length:0,1000',
        'pick_up_order_vin'       => 'length:0,1000',
        'pick_up_order_prices'    => 'length:0,1000',
        'pick_up_order_type'      => 'length:0,1000',
        'pick_up_order_sku_codes' => 'length:0,1000',
        'pick_up_order_sku_nums'  => 'length:0,1000',
        'choose_card_codes'  => 'length:0,1000',

        'source_special' => 'length:0,1000',
        'activity_images' => 'length:0,100000',
        'card_codes' => 'length:0,1000',

        'card_id'        => 'max:50',
        'start_pay_time' => 'dateFormat:Y-m-d H:i:s',
        'end_pay_time'   => 'dateFormat:Y-m-d H:i:s',
        'coupon_code'    => 'max:50',
        'act_dlr_code'   => 'length:0,10000',
        'buy_car_no'   => 'length:0,10000',
        'return_common_carrier'   => 'length:0,10000',
        'return_waybill_number'   => 'length:0,10000',
        'invoice_json'   => 'min:0',
        'rea_id'         => 'number',
        'return_carrier_code'   => 'length:0,10000',
        'car_config_code'   => 'length:0,10000',

        'type'   => 'number',
        'is_nev' => 'number|in:0,1',

        'vehicle_order_no' => 'length:0,128',

    ];


    protected $scene = [
        'fill'                  => ['hit_type_code', 'sku_ids', 'numbers', 'suit_id', 'cart_id', 'group_id', 'order_source', 'group_order_code', 'is_by_tc', 'name', 'phone', 'dlr_code', 'nick_name', 'head_img', 'mail_method', 'distance', 'act_type_id', 'act_id', 'sourcecode', 'dd_dlr_code', 'work_time_json', 'com_json', 'is_subs', 'gift_act_id', 'gift_c_json', 'use_discount', 'pick_up_order_type', 'pick_up_order_vin', 'pick_up_order_prices', 'pick_up_order_code', 'source_special', 'pick_up_order_sku_codes', 'pick_up_order_sku_nums', 'act_dlr_code','gift_card_id','gift_c_json_list','buy_car_no','car_config_code','is_nev','vehicle_order_no'],
        'confirm'               => ['order_id'],
        'changOrderDlr'         => ['order_code', 'old_dd_dlr_code', 'new_dd_dlr_code'],
        'detail'                => ['order_id', 'order_code'],
        'finish'                => ['order_id', 'pick_up_order_code'],
        'cancel'                => ['order_id'],
        'check_phone'           => ['order_phone', 'order_code' => "require|length:0,100", 'vin_phone',],
        'app_sm_pay'            => ['order_code' => "require|length:0,100"],
        'pay_status'            => ['order_code' => "require"],
        'sms'                   => ['phone'],
        'by_suit'               => ['by_type', 'vin'],
        'check_sms'             => ['phone', 'code'],
        'refund_save'           => ['order_id', 'rea_id'],
        'refund'                => ['order_id'],
        'card_clash'            => ['choose_card', 'all_card'],
        'list'                  => ['page', 'pageSize', 'order_code', 'order_status', 'list_type', 'created_date', 'source_special'],
        'mail_price'            => ['order_id', 'address'],
        'go_pay'                => ['order_code' => 'require|length:0,1000', 'name' => 'require|length:0,1000', 'phone' => 'require|alphaDash', 'address', 'cards', 'logistics_mode', 'point', 'dlr_point', 'choose_dlr_code', 'return_url', 'address_area_ids', 'point_js', 'pay_type', 'pick_delivery_card_codes', 'pick_up_order_code','invoice_json'],
        'receive'               => ['order_id'],#主动收货
        'order_del'             => ['order_id'],
        'drive_add_order'       => ['goods_id', 'sku_ids' => "length:0,1000", 'order_code' => "require|length:0,100", 'name', 'phone', 'address'],
        'drive_can_order'       => ['order_code' => "require|length:0,100"],
        'card_most_point'       => ['order_code' => "require|length:0,100", 'choose_card'],
        'order_status'          => ['order_code'],
        'calcDiscounts'         => [
            'order_code'            => 'require',
            'choose_promotion_json' => 'length:0,1000',
            'choose_card_ids'       => 'length:0,5000',
            'choose_card_codes'       => 'length:0,500',
            'is_choose'             => 'require|in:0,1'
        ],
        'confirmDynamic'        => [
            'order_id'              => 'require',
            'choose_promotion_json' => 'length:0,1000',
            'choose_card_codes' => 'length:0,1000',
            'choose_card_ids'       => 'length:0,5000',
        ],
        'order_complete'        => ['order_code' => "require|length:0,100"],
        'order_list_by_card_id' => ['card_id', 'start_pay_time', 'end_pay_time', 'coupon_code'],
        'check_dlr' => ['dd_dlr_code'],
        'check_buy_car_order' => ['buy_car_no' => 'require|length:1,10000'],
        'return_ship' => ['order_code'=> "require|length:0,1000" ,'return_waybill_number','return_common_carrier','return_carrier_code'],
        'logistics_info' => [
            'order_code' => 'require|length:1,100',
            'type' => 'require|in:1,2',
        ],


        'check_refund_reason_by_commodity_class' => [
            'order_code' => 'require|length:1,50',
            'rea_id' => 'require|number',
        ],
    ];

    /**
     * @var string[]
     */
    protected $message = [
        'order_id.number'         => '订单ID格式有误',
        'rea_id.require'          => '退款原因ID必填',
        'rea_id.number'           => '退款原因ID格式有误',
        'hit_type_code.number'    => '埋点编码有误',
        'sku_ids.require'         => '规格必填',
        'sku_ids.length'          => '规格长度限制',
        'vin.length'              => 'Vin长度限制',
        'numbers.require'         => '数量必填',
        'numbers.length'          => '数量长度限制',
        'suit_id.number'          => '套餐ID数据有误',
        'group_id.number'         => '团购ID数据有误',
        'gift_card_id.length'     => '买赠券ID数据有误',
        'cart_id.number'          => '购物车ID数据有误',
        'order_source.length'     => '来源长度限制',
        'group_order_code.length' => '团购单号长度限制',
        'is_by_tc.number'         => '是否保养套餐数据有误',
        'is_by_tc.length'         => '是否保养套餐长度限制',
        'choose_dlr_code.length'  => '选择专营店长度限制',
        'name.length'             => '姓名长度限制',
        'phone.number'            => '手机号码数据有误',
        'act_type_id.length'      => '活动类型ID数据有误',
        'act_id.length'           => '活动ID数据有误',
        'address.length'          => '省市地址数据有误',
        'logistics_mode.number'   => '配送方式数据有误',
        'point.number'            => '积分数据有误',
        'dlr_point.number'        => '专营店积分数据有误',
        'page.number'             => '分页有误',
        'pageSize.number'         => '分页数据有误',
        'pageSize.between'        => '分页只能100以内',
        'order_status.number'     => '订单状态有误',
        'by_type.number'          => '保养套餐类型有误',
        'list_type.in'            => '列表类型有误',
        'mail_method.length'      => '快递类型有误',
        'mail_method.require'     => '快递类型必填',
        'order_code.length'       => '订单编码数据有误',
        'nick_name.length'        => '昵称数据有误',
        'head_img.length'         => '头像数据有误',
        'choose_card.length'      => '已选卡券数据有误',
        'all_card.length'         => '所有卡券数据有误',
        'return_url.length'       => '返回数据有误',
        'address_area_ids.length' => '地址ID数据有误',
        'distance.number'         => '公里数有误',
        'name.require'            => '姓名必填',
        'phone.require'           => '手机号码必填',
        'goods_id.number'         => '商品ID有误',
        'point_js.length'         => 'js长度有误',

        'old_dd_dlr_code.length' => '旧专营店编码长度有误',
        'new_dd_dlr_code.length' => '新专营店编码长度有误',
        'dd_dlr_code.length'     => '到店专营店编码长度有误',
        'work_time_json.length'  => '工时长度有误',
        'com_json.length'        => '子商品信息长度有误',
        'pay_type.length'        => '支付方式长度有误',
        'part_no.length'         => '备件长度不在范围内',
        'dlr_code.length'        => '经销商编号长度不在范围内',
        'sourcecode.length'        => '来源长度有误',
        'is_subs.length'        => '是否组合商品长度有误',
        'pick_up_order_type.length'        => '订单类型有误',
        'pick_up_order_prices.length'        => '价格长度有误',
        'pick_up_order_vin.length'        => '车架长度有误',
        'source_special.length'        => '专题来源有误',
        'card_codes.length'        => '卡券长度有误',


    ];

}
