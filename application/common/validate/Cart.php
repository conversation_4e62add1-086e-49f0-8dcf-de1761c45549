<?php

namespace app\common\validate;

use think\Validate;

/**
 * 购物车 验证器
 * Class Cart
 * @package app\common\validate
 */
class Cart extends Validate
{
    /**
     * @var string[]
     */


    protected $rule = [

        'commodity_id'   => 'number|gt:0',
        'sku_id'         => 'require|number|gt:0',
        'mail_method'    => 'require|number',
        'count'          => 'number',
        'act_type_id'    => 'number',
        'act_id'         => 'number',
        'wi_act_type_id' => 'number',
        'wi_act_id' => 'number',
        'area_id'        => 'number',
        'group_id'       => 'number',
        'shop_cart_ids'  => 'require',
        'shop_cart_id'   => 'require',
        'have_work_time'   => 'require',
        'order'          => 'length:0,10',
        'longitude'      => 'float|length:0,100',
        'latitude'       => 'float|length:0,100',
        'sku_ids'        => 'length:0,100',
        'counts'         => 'length:0,100',
        'mail_methods'   => 'length:0,100',
        'dd_dlr_code'    => 'length:0,100',
        'work_time_json' => 'length:0,100',
        'com_json'       => 'length:0,100',
        'ly_bank_code'   => 'length:0,50',
        'hit_type_code'  =>'length:0,100',
        'dlr_name'       => 'length:0,1000',

        'use_discount' => 'number|in:0,1',
        'gift_act_id' => 'number',
        'gift_card_id' => 'number',
        'gift_c_json'=> 'length:0,1000',
        'distance'        => 'number',
        'source_special'        => 'length:0,1000',
        're_dlr_str'        => 'length:0,1000',
        'filter_dlr'        => 'number',
        'goods_ids'        => 'length:0,1000',
        'vehicle_order_no' => 'length:0,128',

    ];


    protected $scene = [
        'cart'      => ['hit_type_code','commodity_id', 'sku_id', 'count', 'act_type_id', 'act_id', 'mail_method','dd_dlr_code','work_time_json','com_json','shop_cart_id'=>"length:0,1000", 'gift_act_id', 'gift_c_json','distance','source_special','gift_card_id','vehicle_order_no'],
        'dlr'       => ['commodity_id', 'area_id', 'group_id', 'ly_bank_code', 'dlr_name','longitude','latitude','re_dlr_str','goods_ids','filter_dlr'],
        'putCart'   => ['shop_cart_ids', 'counts', 'sku_ids','mail_methods','work_time_json','dd_dlr_code','have_work_time', 'gift_act_id', 'gift_c_json', 'use_discount','gift_card_id'],
        'delCart'   => ['shop_cart_ids'],
        'actChange' => ['shop_cart_ids', 'act_type_id', 'act_id'],
        'pointMoney' => ['shop_cart_ids'],
        'cartCardNum' => ['shop_cart_ids'],

    ];

    /**
     * @var string[]
     */
    protected $message = [
        'hit_type_code.number'=>'埋点编码有误',
        'act_type_id.number' => '活动类型ID数据有误',
        'act_id.number'      => '活动ID数据有误',
        'gift_card_id.number'      => '卡券ID数据有误',

        'sku_id.require'        => '规格必填',
        'commodity_id.require'  => '商品ID必填',
        'have_work_time.require'  => '是否需要工时必填',
//        'count.require'          => '数量必填',
        'count.number'          => '数量格式 有误',
        'commodity_id.number'   => '商品id格式有误',
        'sku_id.number'         => '规格id有误',
        'area_id.number'        => '地区id有误',
        'group_id.number'       => '组id有误',
        'shop_cart_id.require'  => '购物车ID必填',
        'shop_cart_ids.require' => '购物车ID必填',
        'mail_method.require'   => '配送方式必传',
        'mail_method.number'    => '配送方式异常',
        'mail_methods.length'   => '快递方式有误',
        'ly_bank_code.length'   => '开户行编号长度有误',
        'dlr_name.length'       => '经销商名称长度有误',
        'longitude.float'       => '经纬度异常',
        'latitude.float'       => '经纬度异常',
        'source_special.length'       => '专题来源异常',
        'goods_ids.length'       => '商品ID',

    ];

}
