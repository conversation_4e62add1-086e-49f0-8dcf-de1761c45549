<?php
/**
 * 应用公共文件
 * @author: xie<PERSON><PERSON><PERSON>
 * @date: 2017-02-16
 */

use api\jd_sdk\JdOrderN;
use api\wechat\AccessToken;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\db\DbBdpRecommend;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbLog;
use app\common\model\db\DbSystemValue;
use app\common\model\db\DbUser;
use app\common\model\db\DbAdsMallGoodsRefereeD;
use app\common\net_service\E3sPostData;
use app\common\net_service\NetUser;
use app\common\port\connectors\E3spRefactor;
use app\common\return_data\JsonBuilder;
use app\common\return_data\MessageBag;
use GuzzleHttp\Client;
use Tencent\TLSSigAPIv2;
use think\Exception;
use tool\Curl;
use tool\Str;
use think\Config;
use app\common\model\bu\BuOrder;
use app\common\model\inter\IRequestLog;
use app\common\model\inter\IResponesLog;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbNDiscount;
use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbCommoditySkuCode;
use app\common\model\db\DbCard;
use app\common\model\bu\BuToE3sIndex;
use app\common\model\bu\BuToE3sDetail;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbFightGroup;
use app\common\model\e3s\E3sPartCarSeries;
use app\common\model\e3s\E3sSparePart;
use think\Request;
use \think\Cache;

if (!function_exists('get_rand_str')) {
    /*
     * 获取生成字符串
     * @param int $length 字符串长度
     * @param int $type 组合类型 1数字 2字母 3或其他的为字母+数字
     * @return string
     */
    function get_rand_str($length = 16, $type = 3)
    {
        switch ($type) {
            case 1:
                $randStr = Str::randomNumber($length);
                break;
            case 2:
                $randStr = Str::randomLetters($length);
                break;
            case 3:
            default:
                $randStr = Str::random($length);
        }

        return $randStr;
    }
}

if (!function_exists('get_access_token')) {
    /**
     * 获取微信公众号access_token
     * @param boolean $refresh 是否强制刷新
     * @return mixed
     */
    function get_access_token($refresh = false)
    {
        if ($refresh) {
            AccessToken::reset();
        }
        return AccessToken::get();
    }
}

if (!function_exists('json_encode_cn')) {
    /**
     * 将数组转成json格式
     * @param $arr
     * @return string
     */
    function json_encode_cn($arr)
    {
        //JSON_UNESCAPED_UNICODE不转义中文 JSON_UNESCAPED_SLASHES不转义HTTP链接中的斜杠"/"
        return urldecode(json_encode($arr, JSON_UNESCAPED_UNICODE ^ JSON_UNESCAPED_SLASHES));
    }
}

if (!function_exists('json_decode_assoc')) {

    function json_decode_assoc($json, $associative = true)
    {
        return json_decode($json, $associative);
    }
}

if (!function_exists('get_user_info')) {

    function get_user_info()
    {
        return session('net-api-user-info');
    }
}

if (!function_exists('get_user_segment_info')) {

    function get_user_segment_info()
    {
        $user_segment['membership_level'] = 'NONE';
        $user_segment['brand_owner_label'] = 'NONE';
        $user_segment['owner'] = null;
        $user_segment['is_logged_in'] = 0;

        $user = get_user_info();
        if ($user) {
            if ($user['bind_unionid'] && $user['member_id']) {
                $com_service = new \app\common\net_service\Common();
                $owner = $com_service->getCarOwner($user);
                if ($owner) {
                    $user_segment['membership_level'] = $owner['card_degree_code'] ?? 'NONE';
                    $user_segment['brand_owner_label'] = $owner['brand_car_owner'] ?? 'NONE';
                    $user_segment['owner'] = $owner;
                }
            }
            $user_segment['is_logged_in'] = $user['member_id'] ? 1 : 0;
        }
        return $user_segment;
    }
}

if (!function_exists('get_user_segment_label')) {

    function get_user_segment_label($segment)
    {
        $label = 'NONE';
        if ($segment) {
            $user_segment = get_user_segment_info();
            if ($segment == 1) {
                $label = $user_segment['membership_level'];
            } else if ($segment == 2) {
                $label = $user_segment['brand_owner_label'];
            }
        }
        return $label == 'NONE' ? '' : $label;
    }
}

if (!function_exists('get_user_segment_label_cn')) {

    function get_user_segment_label_cn($label)
    {
        $cn = '';
        if ($label) {
            if (in_array($label, ['N', 'V', 'P'])) {
                $channel_type = session('net_api_channel_type');
                if (in_array($channel_type, ['PZ1ASM', 'PZ1AAPP'])) {
                    $cn = '车主价';
                } else {
                    $cn = '车主';
                }
            } else {
                $cache_key = 'user_segment_label_cn_arr';
                $arr = Cache::get($cache_key);
                if (empty($arr)) {
                    $system_val_model = new DbSystemValue();
                    $arr = $system_val_model->getNameList('25');
                    Cache::remember($cache_key, $arr, 2 * 3600);
                }
                $cn = $arr[$label] ?? '';
            }
        }
        return $cn;
    }
}

if (!function_exists('price_fmt')) {

    function price_fmt($price)
    {
        return bcmul($price, 1, 2);
    }
}

if (!function_exists('price_positive_fmt')) {

    function price_positive_fmt($price)
    {
        return price_fmt(max(0, $price));
    }
}

if (!function_exists('round_bcadd')) {

    function round_bcadd($left_value, $right_value, $decimal_places = 2)
    {
        return bcmul(round(bcadd($left_value, $right_value, $decimal_places + 2), $decimal_places), 1, $decimal_places);
    }
}

if (!function_exists('round_bcsub')) {

    function round_bcsub($left_value, $right_value, $decimal_places = 2)
    {
        return bcmul(round(bcsub($left_value, $right_value, $decimal_places + 2), $decimal_places), 1, $decimal_places);
    }
}

if (!function_exists('round_bcdiv')) {

    function round_bcdiv($left_value, $right_value, $decimal_places = 2)
    {
        return bcmul(round(bcdiv($left_value, $right_value, $decimal_places + 2), $decimal_places), 1, $decimal_places);
    }
}

if (!function_exists('round_bcmul')) {

    function round_bcmul($left_value, $right_value, $decimal_places = 2)
    {
        return bcmul(round(bcmul($left_value, $right_value, $decimal_places + 2), $decimal_places), 1, $decimal_places);
    }
}

if (!function_exists('alert_msg')) {
    /*
     * 关闭活动时提醒并关闭微信浏览器 xiepeihui
     * @param string $msg
     */
    function alert_msg($msg = '活动已结束')
    {
        $str = <<<EOF
		<script type="text/javascript">
		var readyFunc = function onBridgeReady() {
		    alert("$msg");
            WeixinJSBridge.invoke('closeWindow');
        }

        if (typeof WeixinJSBridge === "undefined") {
            document.addEventListener('WeixinJSBridgeReady', readyFunc, false);
        } else {
            readyFunc();
        }
		</script>
EOF;
        echo $str;
        exit;
    }
}

if (!function_exists('sub_decimal')) {
    /**
     * 截取小数点位数
     * @param int $num 需要格式化的数字
     * @param int $len 小数点后长度 默认是两位
     * @return string
     */
    function sub_decimal($num, $len = 2)
    {
        return sprintf("%01.2f", $num, $len);
    }
}

if (!function_exists('make_url')) {
    /**
     * 将参数添加到指定url后 xiepeihui
     * @param string $url
     * @param array $params
     * @return string
     */
    function make_url($url, $params = array())
    {
        if (empty($params) || !is_array($params)) {
            return $url;
        }

        $fragment = '';
        $findex   = strpos($url, '#');
        if (false !== $findex) {
            $fragment = substr($url, $findex);
        }
        $url = rtrim(str_replace($fragment, '', $url), '&');
        $url = $url . (false == strrpos($url, '?') ? '?' : '&') . http_build_query($params) . $fragment;

        return $url;
    }
}

if (!function_exists('print_json')) {
    /**
     * 输出json数据  xiepeihui
     * @param mixed $data 主数据
     * @param int $error error code
     * @param string $msg error message
     */
    function print_json($error = 0, $msg = 'ok', $data = null, $exit = true)
    {
//        header("Content-type:application/json");

        $arr = array('error' => $error,  'msg' => $msg,'data' => $data);

        /* if (function_exists('json_encode_cn')) {
             echo json_encode($arr);
         } else {
             echo json_encode($arr);
         }*/
        echo json_encode($arr);  // TODO json_encode_cn +会被转义显示不了
        if ($exit === true) {
            exit();
        }
    }
}
if (!function_exists('print_array')) {
    /**
     * @param $msg      提示
     * @param $error    状态：0-成功；1-失败；
     * @param $data     返回数据
     * @return array
     */
    function print_array($msg = 'ok', $error = 0, $data = null)
    {
        return [
            'data' => $data,
            'error' => $error,
            'msg' => $msg
        ];
    }
}
if (!function_exists('get_browser_versions')) {

    /**
     * 获取浏览器类型和版本号
     * @return array|string
     */
    function get_browser_versions()
    {
        $agent   = $_SERVER['HTTP_USER_AGENT']; //获取客户端信息
        if (empty($agent)){
            return false;
        }
        $browser = '';
        /* 定义浏览器特性正则表达式 */
        $regex = array(
            'ie_<10'  => '/(MSIE)(\d+\.\d)/',
            'ie_>10'  => '/(rv:)(\d+\.\d)/',
            'firefox' => '/(Firefox)\/(\d+\.\d+)/',
            'safari'  => '/Version\/(\d+\.\d+\.\d) (Safari)/',
            'chrome'  => '/(Chrome)\/(\d+\.\d+)/',
            'edge'    => "/(Edge)\/(\\d+\\.\\d+)/"
        );
        foreach ($regex as $type => $reg) {
            preg_match($reg, $agent, $data);
            if (!empty($data) && is_array($data)) {
                //                $browser = $type === 'safari' ? array($data[2], $data[1]) : array($data[1], $data[2]);
                $browser = $data[1] . '-' . $data[2];
                if ($type == 'ie_<10') {
                    $browser = 'ie-' . $data[2];
                }
                if ($type == 'ie_>10') {
                    $browser = 'ie-' . $data[2];
                }
            }
        }
        return $browser;
    }
}

if (!function_exists('get_file_ext')) {
    /**
     * 取上传文件的后缀名
     * @param type $file_name
     * @return string
     */
    function get_file_ext($file_name)
    {
        return substr($file_name, strrpos($file_name, ".") + 1);
    }
}

if (!function_exists('clear_emoji')) {
    /**
     * 将emoji转换成空，方便数据库存储
     * @param $str
     * @return mixed
     */
    function clear_emoji($str)
    {
        $_json    = json_encode($str);
        $_content = preg_replace_callback('/(\\\\u[ed]{1}[0-9a-f]{3})/i', function ($match) {
            return '';
        }, $_json);
        return addslashes(json_decode($_content));
    }
}

if (!function_exists('emoji2unicode')) {
    /**
     * 将emoji转换成unicode编码，方便数据库存储
     * @param $str
     * @return mixed
     */
    function emoji2unicode($str)
    {
        $_json    = json_encode($str);
        $_content = preg_replace_callback('/(\\\\u[ed]{1}[0-9a-f]{3})/i', function ($match) {
            return addslashes($match[0]);
        }, $_json); //将emoji的unicode留下，其他不动
        //    $_content = preg_replace_callback('/(\\\\ud[0-9a-f]{3})/i', function($match) { return addslashes($match[0]);}, $_content); //将emoji的unicode留下，其他不动
        return addslashes(json_decode($_content));
    }
}

if (!function_exists('emoji2wechat')) {
    /**
     * 微信显示emoji表情
     * @param string $str 含unicode编码的字符串
     * @return mixed
     */
    function emoji2wechat($str)
    {
        if (empty($str)) {
            return '';
        }

        $unicode = sprintf('"%s"', $str);
        $unicode = str_replace('\\\\', '\\', $unicode);

        return json_decode($unicode);
    }
}

if (!function_exists('emoji2html')) {
    /**
     * 浏览器显示emoji表情
     * 注：必须先引入emoji样式   路径:public/emoji
     * @param $str
     * @return mixed
     */
    function emoji2html($str)
    {
        if (empty($str)) {
            return '';
        }

        //TODO 判断是否有unicode，如果没有则直接输出结果.
        $list   = M()->table('t_db_emoji_code')->field('class, softb_unicode, wechat')->select();
        $emoji  = $emojiClass = [];
        $wechat = $wechatClass = [];
        foreach ($list as $val) {
            if ($val['softb_unicode'] != '-') {
                $emoji[]      = $val['softb_unicode'];
                $emojiClass[] = sprintf("<span class='emoji %s'></span>", $val['class']);
            }
            if ($val['wechat'] != '-') {
                $wechat[]      = $val['wechat'];
                $wechatClass[] = sprintf("<span class='emoji %s'></span>", $val['class']);
            }
        }
        $str = str_replace('\\\\', '\\', $str);
        //替换softbank
        $result = str_replace($emoji, $emojiClass, $str);
        //替换wechat
        $result = str_replace($wechat, $wechatClass, $result);

        return $result;
    }
}

//-----------------实例化接口类---------------------
if (!function_exists('dcrm')) {
    /**
     * 实例化Dcrm类
     * @return \api\lianyou\Dcrm
     */
    function dcrm()
    {
        static $dcrm = null;

        if (is_null($dcrm)) {
            $dcrm = new \api\lianyou\Dcrm();
        }
        return $dcrm;
    }
}

if (!function_exists('e4s')) {
    /**
     * 实例化E4S类
     * @return \api\lianyou\E4S
     */
    function e4s()
    {
        static $e4s = null;
        if (is_null($e4s)) {
            $e4s = new \api\lianyou\E4S();
        }
        return $e4s;
    }
}
if (!function_exists('sms')) {
    /**
     * 实例化sms类
     * @return \api\xuanwu\SMS
     */
    function sms()
    {
        static $sms = null;
        if (is_null($sms)) {
            $sms = new \api\xuanwu\SMS();
        }
        return $sms;
    }
}
/**
 * js返回上页
 */
if (!function_exists('return_back')) {
    function return_back($msg = '')
    {
        if (empty($msg)) {
            exit('<script>history.back(-1);</script>');
        } else {
            exit("<script>alert('{$msg}');history.back(-1);</script>");
        }
    }
}
/**
 * 生成参数列表,以数组形式返回
 * @param string
 * @return array
 */
function param2array($tag = '')
{
    $param = array();
    $array = explode(';', $tag);
    foreach ($array as $v) {
        $v = trim($v);
        if (!empty($v)) {
            list($key, $val) = explode(':', $v);
            $param[trim($key)] = trim($val);
        }
    }
    return $param;
}

/**
 * 数字到字母列
 * @param int
 * @param int
 * @return string
 * <AUTHOR> <<EMAIL>>
 */
function num2alpha($index, $start = 65)
{
    $str = '';
    if (floor($index / 26) > 0) {
        $str .= num2alpha(floor($index / 26) - 1);
    }
    return $str . chr($index % 26 + $start);
}

/**
 * 数据表导出excel
 *
 * @param string $data ,所有数据,必须
 * @param string $fieldsall ,所有字段
 * @param string $file ,保存的excel文件名,默认表名为文件名
 * @param string $fields ,需要导出的字段名,默认全部,以半角逗号隔开
 * @param string $field_titles ,需要导出的字段标题,需与$field一一对应,为空则表示直接以字段名为标题,以半角逗号隔开
 */
function export2excel($data, $fieldsall, $file = '', $fields = '', $field_titles = '')
{
    //处理传递的参数

    if (!$data) {
        return "数据不能为空";
    }
    $field_titles = empty($field_titles) ? array() : explode(",", $field_titles);
    if (empty($fields)) {
        $fields = $fieldsall;
        //成员数不一致,则取字段名为标题
        if (count($fields) != count($field_titles)) {
            $field_titles = $fields;
        }
    } else {
        $fields    = explode(",", $fields);
        $rst       = array();
        $rsttitle  = array();
        $title_y_n = (count($fields) == count($field_titles)) ? true : false;
        foreach ($fields as $k => $v) {
            if (in_array($v, $fieldsall)) {
                $rst[] = $v;
                //一一对应则取指定标题,否则取字段名
                $rsttitle[] = $title_y_n ? $field_titles[$k] : $v;
            }
        }
        $fields       = $rst;
        $field_titles = $rsttitle;
    }
    //处理tag标签

    //处理数据
    //import("Org.Util.PHPExcel");
    error_reporting(E_ALL);
    date_default_timezone_set('Asia/chongqing');
    $objPHPExcel = new PHPExcel();
    //import("Org.Util.PHPExcel.Reader.Excel5");
    /*设置excel的属性*/
    $objPHPExcel->getProperties()->setCreator("lzx")//创建人
    ->setLastModifiedBy("lzx")//最后修改人
    ->setKeywords("excel")//关键字
    ->setCategory("result file");//种类

    //第一行数据
    $objPHPExcel->setActiveSheetIndex(0);
    $active = $objPHPExcel->getActiveSheet();
    foreach ($field_titles as $i => $name) {
        $ck = num2alpha($i++) . '1';
        $active->setCellValue($ck, $name);
    }
    //填充数据
    foreach ($data as $k => $v) {
        $k   = $k + 1;
        $num = $k + 1;//数据从第二行开始录入
        $objPHPExcel->setActiveSheetIndex(0);
        foreach ($fields as $i => $name) {
            $ck = num2alpha($i++) . $num;
            $active->setCellValue($ck, $v[$name]);
        }
    }
    $objPHPExcel->getActiveSheet()->setTitle($file);
    $objPHPExcel->setActiveSheetIndex(0);
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="' . $file . '.xls"');
    header('Cache-Control: max-age=0');
    $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
    $objWriter->save('php://output');
    exit;
}


/**
 * 微信卡券签名
 * @param array $data 卡券参与签名数据：$apiTicket, $cardId, $time, $cardCode, $openid
 * @return mixed
 */
function card_sign($data)
{
    foreach ($data as &$val) {
        $val = strval($val);
    }
    sort($data, SORT_STRING);
    return sha1(implode($data));
}

/*
 * 获取生成字符串
 * @param int $length 字符串长度
 * @param int $type 组合类型 1数字 2字母 3或其他的为字母+数字
 * @return string
 */
function get_rand_str($length = 16, $type = 3)
{
    if ($type == 1) {
        $chars = '0123456789';
    } elseif ($type == 2) {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    } else {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    }

    $str = "";
    for ($i = 0; $i < $length; $i++) {
        $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
    }

    return $str;
}

/**
 * HTTP请求 GET方式
 * @param $url
 * @param string $param
 * @param bool $is_json
 * @return bool|mixed
 */
function http_get($url, $param = '', $is_json = true)
{
    return Curl::get($url, $param, $is_json);
}

/**
 * HTTP请求 POST方式
 * @param $url
 * @param string $param
 * @param bool $is_json
 * @param bool $is_urlencode
 * @return bool|mixed
 */
function http_post($url, $param, $is_wx = true, $is_json = true, $is_urlencode = true,$headers_arr=[])
{
    return Curl::post($url, $param, $is_wx, $is_json, $is_urlencode,$headers_arr);
}

/**
 * 设置，获取，删除缓存redis的值 xieph
 * ************************
 * value有值时设置redis
 * value不填写则获取redis的值
 * value为null时删除缓存的值
 * *************************
 * @param string $name
 * @param string $value
 * @param null|int $expire
 * @param string $custom_prefix
 * @return mixed
 */
function redis($name, $value = '', $expire = null, $custom_prefix = '')
{
    if (empty($name)) {
        return false;
    }
    $cache_init_arr = config('cache');
    if (!empty($custom_prefix)) {
        $cache_init_arr['prefix'] = $custom_prefix;
    }

    //设置缓存类型
    cache($cache_init_arr);
    Config::set('cache', $cache_init_arr);
    if (is_null($value)) {
        //删除缓存
        return cache($name, null);
    } else {
        if (empty($value) && $value!=='0') {
            //获取缓存的值
            $val = cache($name);
            if (empty($val)) {
                return false;
            }
            return unserialize($val);
        } else {
            //设置缓存的值
            $val = serialize($value);
            return cache($name, $val, $expire);
        }
    }
}

/**
 * 获取GUID
 */
function guid()
{
    mt_srand((double)microtime() * 10000);//optional for php 4.2.0 and up.
    $charid = strtoupper(md5(uniqid(rand(), true)));
    $hyphen = chr(45);// "-"
    $uuid   = substr($charid, 0, 8) . $hyphen
        . substr($charid, 8, 4) . $hyphen
        . substr($charid, 12, 4) . $hyphen
        . substr($charid, 16, 4) . $hyphen
        . substr($charid, 20, 12);
    return $uuid;
}

/**
 * url编码数组和字符串 xieph
 * 因php5.4版本不支持，并且传入的内容有双引号，加上addslashes
 * @param array|string $data
 * @return array|string
 */
function code_urlencode($data)
{
    if (is_array($data)) {
        foreach ($data as $k => $v) {
            $v                   = is_array($v) ? array_map('code_urlencode', $v) : urlencode(addslashes($v));
            $data[urlencode($k)] = $v;
        }
        return $data;
    } else {
        return urlencode(addslashes($data));
    }
}

/**
 * 获取图片完整路径
 * @param string $url 待获取图片url
 * @param int $cat 待获取图片类别 0为文章 1前台头像 2后台头像--暂时不用，默认0
 * @return string 完整图片imgurl
 */
function get_imgurl($url, $cat = 0)
{
    if (stripos($url, 'http') !== false) {
        //网络图片
        return $url;
    } elseif (empty($url)) {
        //$url为空
        if ($cat == 2) {
            $imgurl = 'girl.jpg';
        } elseif ($cat == 1) {
            $imgurl = 'headicon.png';
        } else {
            $imgurl = 'no_img.jpg';
        }
        return config('upload.url') . 'img/' . $imgurl;
    } else {
        //本地上传图片
        return config('upload.url') . $url;
    }
}


function export_excel($data_array, $fields = '', $titles = '', $filename = '', $width = [])
{
    set_time_limit(0);

    $excel_letter_array = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ');
    /** PHPExcel */

    include VENDOR_PATH . "phpexcel/Classes/PHPExcel.php";

    /** PHPExcel_IOFactory */
    include VENDOR_PATH . "phpexcel/Classes/PHPExcel/IOFactory.php";

    include VENDOR_PATH . "phpexcel/Classes/PHPExcel/Writer/Excel2007.php";

    $objPHPExcel   = new PHPExcel();
    $getProperties = $objPHPExcel->getProperties();
    $getProperties->setCreator("Maarten Balliauw");
    $getProperties->setLastModifiedBy("Maarten Balliauw");
    $getProperties->setTitle("Office 2007 XLSX Test Document");
    $getProperties->setSubject("Office 2007 XLSX Test Document");
    $getProperties->setDescription("Test document for Office 2007 XLSX, generated using PHP classes.");
    $getProperties->setKeywords("office 2007 openxml php");
    $getProperties->setCategory("Test result file");
    $objPHPExcel->setActiveSheetIndex(0);

    $getActiveSheet = $objPHPExcel->getActiveSheet();

    if (!empty($fields) && !empty($titles)) {
        $fields = explode(',', $fields);
        $titles = explode(',', $titles);
        foreach ($titles as $key => $value) {
            $getActiveSheet->setCellValue($excel_letter_array[$key] . '1', $value);
            $width = isset($width[$key]) ? $width[$key] : 20;
            $objPHPExcel->getActiveSheet()->getColumnDimension($excel_letter_array[$key])->setWidth(intval($width));
        }
    } else {
        if (count($data_array) <= 0) {
            return_back('导出数据为空');
        }
        $titles = array_keys($data_array[0]);
        foreach ($data_array[0] as $key => $value) {
            $getActiveSheet->setCellValue($excel_letter_array[$key] . '1', $value);
            $width = isset($width[$key]) ? $width[$key] : 20;
            $objPHPExcel->getActiveSheet()->getColumnDimension($excel_letter_array[$key])->setWidth(intval($width));
        }
    }

    foreach ($data_array as $key2 => $arr) {
        foreach ($titles as $key => $value) {
            $getActiveSheet->setCellValueExplicit($excel_letter_array[$key] . ($key2 + 2) . "", $arr[$fields[$key]], PHPExcel_Cell_DataType::TYPE_STRING);
//            $getActiveSheet->setCellValue($excel_letter_array[$key].($key2+2)."", $arr[$fields[$key]]);
        }
    }

    $getActiveSheet->setTitle('Sheet1');

    // Set active sheet index to the first sheet, so Excel opens this as the first sheet

    $objPHPExcel->setActiveSheetIndex(0);
    $filename = iconv('utf-8', 'gbk', $filename);

    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '.xls"');
    header('Cache-Control: max-age=0');

    /** PHPExcel_IOFactory */
    $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');//Excel2007
    $objWriter->save('php://output');
    exit;
}

/**
 * 百度编辑器视频内容过滤
 * @param string $content
 * @return stringi
 */
function preg_baidu_content($content = '')
{
    //$content='sdfsfsfsd<img src="ddd"/>   <img my_att="video" width="300" height="300" _url="http://dfldata-dev.dongfeng-nissan.com.cn/mhome/public/uploads/ueditor/video/20160813/1471054757339707.flv"
    //src="http://dfldata-dev.dongfeng-nissan.com.cn/mhome/public/static/ueditor/baidu/themes/default/images/spacer.gif"/> 2342423';
    // $content='<img />';
    $content    = explode('<img', $content);
    $newContent = '';
    foreach ($content as $key => $val) {
        if (strpos($val, '_url') !== false) {
            $val = '<video class="edui-upload-video  vjs-default-skin video-js" controls="" preload="none"' . $val;
            $val = str_replace('src=', 'my_attr=', $val);
            $val = str_replace(array('_url=', 'spacer.gif"/>'), array('src=', 'spacer.gif" data-setup="{}"></video>'), $val);
        }
        if ($key == 0 || strpos($val, 'edui-upload-video') !== false) {
            $newContent .= $val;
        } else {
            $newContent .= '<img ' . $val;
        }
    }
    return $newContent;
}

function get_number($key, $page_size = 0)
{
    if (empty($page_size)) {
        $page_size = Config('paginate.list_rows');
    }
    $page = input('get.page', 1);
    return ($page - 1) * $page_size + $key + 1;
}


//------------- 微信开放平台相关函数 ---------------------------

/**
 * 通过专营店编码获取微信公众号access_token
 * @param string $dlrCode
 * @param boolean $refresh 是否强制刷新
 * @return mixed
 */
function get_access_token_by_dlr($dlrCode, $refresh = false)
{
    if (empty($dlrCode)) {
        return false;
    }
    $auth_model = new \app\common\model\bu\BuComponentAuth();
    $row        = $auth_model->getOne(array('where' => ['dlr_code' => $dlrCode, 'is_enable' => 1]));

    if (!$row) {
        \tool\Logger::error('获取token--无授权:' . $auth_model->getLastSql());
        return false;
    } else {
        //增加redis存入
        redis('wx_auth_refresh_token_' . $row['auth_appid'], $row['auth_refresh_token']);
        $accessToken = get_authorizer_access_token($row['auth_appid'], $refresh);
        return $accessToken;
    }
}


/**
 * 获取第三方平台预授权码
 * @param bool $refresh
 * @return mixed|string
 */
function get_pre_auth_code()
{
    $component_appid        = config('component_appid');
    $component_access_token = get_component_access_token();

    $url  = 'https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode?component_access_token=' . $component_access_token;
    $data = ['component_appid' => $component_appid];
    $res  = http_post($url, $data, true);

    if (!empty($res['pre_auth_code'])) {
        $pre_auth_code = $res['pre_auth_code'];
    } else {
        $pre_auth_code = '';
    }

    return $pre_auth_code;
}

/**
 * 获取第三方应用平台access_token
 * @param bool $refresh
 * @return mixed|string
 */
function get_component_access_token($refresh = false)
{
    $component_appid        = config('component_appid');
    $redisKey               = 'wx_component_access_token_' . $component_appid;
    $component_access_token = redis($redisKey);


    if ($refresh || empty($component_access_token)) {
        $component_appsecret     = config('component_appsecret');
        $component_verify_ticket = redis('wx_component_verify_ticket_' . $component_appid);

        //获取微信开放平台access_token
        $url  = 'https://api.weixin.qq.com/cgi-bin/component/api_component_token';
        $data = [
            'component_appid'         => $component_appid,
            'component_appsecret'     => $component_appsecret,
            'component_verify_ticket' => $component_verify_ticket,
        ];
        $res  = http_post($url, $data, true);


        if (!empty($res['component_access_token'])) {
            $component_access_token = $res['component_access_token'];
            redis($redisKey, $component_access_token, 6600);
        } else {
            \tool\Logger::error('get_component_access_token_error', ['r' => $res, 'd' => $data]);
            $component_access_token = '';
        }
    }
    return $component_access_token;
}

/**
 * 获取授权公众号信息
 * @param $auth_appid
 * @return array|bool
 */
function get_component_appinfo($auth_appid)
{
    if (empty($auth_appid)) {
        return false;
    }

    $component_appid        = config('component_appid');
    $component_access_token = get_component_access_token();
    $url                    = "https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_info?component_access_token={$component_access_token}";
    $data                   = [
        'component_appid'  => $component_appid,
        'authorizer_appid' => $auth_appid,
    ];
    $return                 = http_post($url, $data, true);

    return $return;
}

/**
 * 获取公众号授权access_token
 * @param $auth_appid
 * @param bool $refresh
 * @return bool|mixed|string
 */
function get_authorizer_access_token($auth_appid, $refresh = false)
{
    if (empty($auth_appid)) {
        return false;
    }

    $aatKey            = 'wx_auth_access_token_' . $auth_appid;
    $auth_access_token = redis($aatKey);

    if ($refresh || empty($auth_access_token)) {
        $component_appid        = config('component_appid');
        $component_access_token = get_component_access_token();

        $auth_access_token = '';
        //获取authorizer_refresh_token
        $refresh_token = redis('wx_auth_refresh_token_' . $auth_appid);
//        \tool\Logger::error("获取公众号授权access_token.".$refresh_token);
        if (!empty($refresh_token)) {
            $data = [
                'component_appid'          => $component_appid,
                'authorizer_appid'         => $auth_appid,
                'authorizer_refresh_token' => $refresh_token
            ];
            $url  = "https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token={$component_access_token}";

            $return = http_post($url, $data, true);
//            \tool\Logger::error("获取公众号授权access_token.--POST",['r'=>$return,'u'=>$url,'d'=>$data]);
            if (isset($return['authorizer_access_token'])) {
                $auth_access_token = $return['authorizer_access_token'];
                //写入redis缓存
                redis($aatKey, $return['authorizer_access_token'], 6600);
            } else {
                //获取失败的写入缓存
                redis('get_wx_ac_token-fail-' . $auth_appid, json_encode(['r' => $return, 'd' => $data, 'u' => $url]), 6600);
            }
        }
    }

    return $auth_access_token;
}

//------------- 微信开放平台相关函数 end---------------------------

//------------- 阿里云平台相关函数 begin---------------------------
/**
 * 获取全国快递公司信息
 * @return mixed
 */
function getExpressCompanyList()
{
    $host    = "http://jisukdcx.market.alicloudapi.com";
    $path    = "/express/type";
    $method  = "GET";
    $appcode = "4a0293607dbe439181b01f5b336e110e";
    $headers = array();
    array_push($headers, "Authorization:APPCODE " . $appcode);
    $querys = "";
    $bodys  = "";
    $url    = $host . $path;

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl, CURLOPT_FAILONERROR, false);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
//    curl_setopt($curl, CURLOPT_HEADER, true);//true：返回头部信息；FALSE不返回头部信息
    if (1 == strpos("$" . $host, "https://")) {
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    }
    $result = curl_exec($curl);
    curl_close($curl);
    $res = json_decode($result, true);
    return $res;
}

/**
 * @param $waybill_number 运单编号
 * @param $type 快递公司编码
 * @return mixed
 * [
 *  'time'=>'时间’,
 *  'status'=>'状态’, 0正常，201 快递单号为空 ，202 快递公司为空，203 快递公司不存在，204 快递公司识别失败，205 没有信息，206 快递单号错误
 *  'type'=>'快递公司编码’, //参照基础设置表value_code=13
 *  'number'=>'快递单号’,
 *  'deliverystatus'=>'物流状态’    //1在途中 2派件中 3已签收  4派件失败（拒签等）
 * ]
 * 旧版，那个公司跑路了
 */
function getExpressInfo_old($waybill_number, $type)
{

    $host    = "https://jisukdcx.market.alicloudapi.com";
    $path    = "/express/query";
    $method  = "GET";
    $appcode = "4a0293607dbe439181b01f5b336e110e";
    $headers = array();
    array_push($headers, "Authorization:APPCODE " . $appcode);
    $querys = "number=" . $waybill_number . "&type=" . $type;
    $bodys  = "";
    $url    = $host . $path . "?" . $querys;

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl, CURLOPT_FAILONERROR, false);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
//    curl_setopt($curl, CURLOPT_HEADER, true); //true：返回头部信息；FALSE不返回头部信息
    if (1 == strpos("$" . $host, "https://")) {
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    }
    $result = curl_exec($curl);
    curl_close($curl);
    $res = json_decode($result, true);
    return $res;
}


/**
 * @param $waybill_number 运单编号
 * @param $type 快递公司编码
 * @return mixed
 * [
 *  'time'=>'时间’,
 *  'status'=>'状态’, 0正常，201 快递单号为空 ，202 快递公司为空，203 快递公司不存在，204 快递公司识别失败，205 没有信息，206 快递单号错误
 *  'type'=>'快递公司编码’, //参照基础设置表value_code=13
 *  'number'=>'快递单号’,
 *  'issign'=>'是否签收’,
 *  'deliverystatus'=>'物流状态’    // 0：快递收件(揽件)1.在途中 2.正在派件 3.已签收 4.派送失败 5.疑难件 6.退件签收
 * ]
 */
function getExpressInfo($waybill_number, $type, $phone = '', $third_order_id = '')
{

    if (in_array($type,['SFEXPRESS','ZTO']) && !empty($phone)){
        $waybill_number .= ':' . substr($phone, -4);
    }
    if ($type == 'JD' && $third_order_id){
        $msg = [];
        $last_stat = 0;
        $wl_id_arr = [];
        $sdk_order = new JdOrderN();
        $res = $sdk_order->orderTrack($third_order_id);
        if(isset($res['result'])){
            $res= $res['result'];
        }
        if (!$res) return false;

        if(isset($res['orderTrack'])){
            if (count($res['orderTrack']) > 1){
                $last_stat = 1; // 派件中
            }
            foreach ($res['orderTrack'] as $key => $val) {
                $msg[] = [
                    'status' => $val['content'],
                    'time' => $val['msgTime'],
                ];
                if (is_numeric(strpos($val['content'], '配送员'))){
                    $last_stat = 2;
                }
                if (is_numeric(strpos($val['content'], '完成配送'))){
                    $last_stat = 3;
                }
            }
        }

        if(isset($res['waybillCode'])){
            foreach ($res['waybillCode'] as $k=>$v){
                $wl_id_arr[]=$v['deliveryOrderId'];
            }
        }
        $wl_id = implode(',',$wl_id_arr);
        $data = [
            'deliverystatus' => $last_stat,
            'number'         => $wl_id,
            'type'           => $type,
            'list'           => array_reverse($msg),
            'issign'         => 1,
            'expName'        => '京东',
            'expSite'        => '',
            'expPhone'       => '950616',
            'logo'           => '',
            'courier'        => '',
            'courierPhone'   => '',
            'updateTime'     => '',
            'takeTime'       => '',
        ];
        $res = ['status' => 0,'msg' => 'ok','result' => $data];
        \tool\Logger::error('expbill_jd',['order_id' => $third_order_id,'res'=>$res]);

    } else {
        $host    = "https://wuliu.market.alicloudapi.com";
        $path    = "/kdi";
        $method  = "GET";
        $appcode = "4a0293607dbe439181b01f5b336e110e";
        $headers = array();
        array_push($headers, "Authorization:APPCODE " . $appcode);
        $querys = "no=" . $waybill_number . "&type=" . $type;
        $bodys  = "";
        $url    = $host . $path . "?" . $querys;

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HEADER, false);
        if (1 == strpos("$" . $host, "https://")) {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        }
        $result = curl_exec($curl);
        \tool\Logger::error('expbill',['curl' => $curl,'res'=>$result]);
        $res    = json_decode($result, true);
    }

    return $res;
}

/**
 * 下发ccs订单-new
 * @param $order_code
 */
function send_order_ccs($order_code)
{
    $order_model = new BuOrder();
    $where       = ['a.order_code' => $order_code];
    $order       = $order_model->getOrderCommodityInfo(['where' => $where, 'field' => "a.vin,a.order_code,a.openid,e.sku_code,b.commodity_name,b.price"]);
    if ($order) {
        $order = end($order);
        $ccs   = new \api\lianyou\Ccs();
        $data = [
            'vin'             => $order['vin'],
            'openid'          => $order['openid'],
            'orderCode'       => $order['order_code'],
            'commodityCode'   => $order['sku_code'],
            'commodityName'   => $order['commodity_name'],
            'commodityAmount' => $order['price'],
        ];
        $res  = $ccs->order($data);
        if($res){
            $order_data = [
                'order_status' => 14,
                'verification_user' => 'ccs_order',
                'last_updated_date' => date('Y-m-d H:i:s'),
            ];
            $is_order = $order_model->saveData($order_data, ['order_code' => $order['order_code']]);
            if ($is_order) {
                $net_order = new \app\common\net_service\NetOrder();
                $net_order->orderChange($order['order_code']);
            }
        }

    } else {
        return false;
    }
}

/**
 * 3,4新春活动用户支付发送卡劵到用户
 * @param $type 类型 1 50优惠劵 2 150优惠券
 * @param $card_id 卡劵id
 * @param $order 信息
 * @return bool
 * <AUTHOR>
 * Date: 2021/2/27
 * Time: 10:19
 */
function send_card($type, $card_id, $order)
{
    $chars = "0123456789";
    $str   = "";
    for ($i = 0; $i < 7; $i++) {
        $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
    }
    if ($type == 1) {
        $bu_card                     = new BuCardReceiveRecord();
        $params['where']['openid']   = $order['openid'];
        $params['where']['act_name'] = '开春活动50';
        $params['where']['status']   = array('neq', 4);
        $count                       = $bu_card->getCount($params);
        if ($count == 0) {
            $array[] = [
                'vin'         => $order['vin'],
                'card_id'     => $card_id,//卡劵id
                'is_get_card' => 1,
                'card_code'   => 'shop' . date('YmdHis') . $str,
                'status'      => 1,
                'dlr_code'    => 'GWSC',
                'openid'      => $order['openid'],
                'act_name'    => '开春活动50',
                'source'      => 2,
            ];
            $res     = $bu_card->insertAll($array);
            if ($res) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    if ($type == 2) {
        $bu_card                     = new BuCardReceiveRecord();
        $params['where']['openid']   = $order['openid'];
        $params['where']['act_name'] = '开春活动150';
        $params['where']['status']   = array('neq', 4);
        $count                       = $bu_card->where($params['where'])->select();
        $array                       = [];
        if (count($count) == 0) {
            foreach ($card_id as $key => $value) {
                $array[] = [
                    'vin'         => $order['vin'],
                    'card_id'     => $value,//卡劵id
                    'is_get_card' => 1,
                    'card_code'   => 'shop' . date('YmdHis') . rand(100000, 999999),
                    'status'      => 1,
                    'dlr_code'    => 'GWSC',
                    'openid'      => $order['openid'],
                    'act_name'    => '开春活动150',
                    'source'      => 2,
                ];
            }
            $res = $bu_card->insertAll($array);
            if ($res) {
                return true;
            } else {
                return false;
            }
        }

        if (count($count) != count($card_id)) {
            foreach ($count as $key => $value) {
                unset($card_id[array_search($value['card_id'], $card_id)]);
            }

            $card_array = array_values($card_id);
            foreach ($card_array as $key => $value) {
                $array[] = [
                    'vin'         => $order['vin'],
                    'card_id'     => $value,//卡劵id
                    'is_get_card' => 1,
                    'card_code'   => 'shop' . date('YmdHis') . rand(100000, 999999),
                    'status'      => 1,
                    'dlr_code'    => 'GWSC',
                    'openid'      => $order['openid'],
                    'act_name'    => '开春活动150',
                    'source'      => 2,
                ];
            }
            $res = $bu_card->insertAll($array);
            if ($res) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }
    return false;
}

/**
 * 订单完成处理
 * @param $order_code
 * @param int $order_goods_class 订单商品分类首先给ccs套餐使用
 * @param int $local 1pay 2pay_chebaba 3goods/pay_save
 *
 */
function order_finish($order_code, $order_goods_class = 4, $local = 1)
{


    //ccs套餐  在ordersave处理
//    if ($order_goods_class == 4) {
//        send_order_ccs($order_code); //下发LY..
//    }
//
//    //这里推送到用户卡包 3,4新春
    $order                                  = new BuOrder();
    $params['where']['a.parent_order_code'] = $order_code;
    $params['field']                        = 'a.id,a.order_code,a.vin,b.commodity_id,a.openid,a.sale_source,a.phone,a.user_id';
    $commodity_id                           = $order->getOneJoin($params);
//    $good_id1                               = [1800, 1801];//正式环境
////    $good_id1 = [2959,2957];//测试环境
//    if (in_array($commodity_id['commodity_id'], $good_id1)) {
//        if (time() <= '1619798399' && time() >= '1614528000') {
//            $card_id = 1108;//正式卡劵
////            $card_id = 761;//测试卡劵
//            send_card(1, $card_id, $commodity_id);
//        }
//    }
//
//    $good_id2 = [3541, 3540, 3539, 3538, 1718, 1717, 1716, 1715, 1589, 1588, 1587, 1586, 1585, 1584, 1583, 1582, 1581, 1580, 1579, 1578, 1577, 1575, 1574, 1573, 1572, 1571];
////    $good_id2 = [2954,2953,2913];//测试商品id
//    if (in_array($commodity_id['commodity_id'], $good_id2)) {
//        $card_id = [1104, 1105, 1102, 1101, 1103, 1096, 1106, 1098, 1107, 1100];//正式卡劵id
////        $card_id = [762,763,764];//测试卡劵id
//        if (time() <= '1619798399' && time() >= '1614528000') {
//            send_card(2, $card_id, $commodity_id);
//        }
//    }
    //CDP项目的idmapping 下单上报
    $unionid = '';
    if ($commodity_id) {
        $user_model = new \app\common\model\db\DbUser();
        $user       = $user_model->getOneByPk($commodity_id['user_id']);
        $unionid    = $user['unionid'];
    }
    $data = array(
        "app_host"=> config("CDP.app_host"),
        "gateway_host"=> config("CDP.gateway_host"),
        "projectId"=> config("CDP.projectId"),
        "token"=> config("CDP.token"),
    );
    $sdk_data=[
        'data'=>$data,
        's_code_info'=>array("site_code" => "dn_service", "phone" => $commodity_id['phone'], "vin" => $commodity_id['vin'], "openid" => $commodity_id['openid'], "unionid" => $unionid),
    ];
    $data = ['type' => 'send_sdk', 'data' => $sdk_data,'created_at'=>time()];
//    $dndcio = DndcSdk::getInstance($sdk_data['data']);
//    $res    = $dndcio->track("商品订单", $sdk_data['s_code_info']);
//    $res    = $dndcio->track("商品订单", $sdk_data['s_code_info']);
    \think\Queue::push('app\common\queue\OrderQueue', json_encode($data),config('queue_type.order'));
}

/**
 * 记录分账
 * @param $order_code
 * @return bool
 */
function settlement($order_code)
{
    $order_model    = new BuOrder();
    $order_set_list = $order_model->getList(['where' => ['parent_order_code' => $order_code]]);
    return true;
}

/**
 * 3,4月新春活动 退款判断卡劵是否使用，没有使用核销
 * @param $order_code
 * <AUTHOR>
 * Date: 2021/3/3
 * Time: 18:00
 */
function refundWipedCard($order_code)
{
    //这里推送到用户卡包 3,4新春
    $order                           = new BuOrder();
    $params['where']['a.order_code'] = $order_code;
    $params['field']                 = 'a.id,a.order_code,a.vin,b.commodity_id,a.openid';
    $commodity_id                    = $order->getOneJoin($params);

    $good_id1 = [1800, 1801];
//    $good_id1 = [2959,2957];//测试环境
    $bu_card = new BuCardReceiveRecord();
    if (in_array($commodity_id['commodity_id'], $good_id1)) {
        //1108  核销卡劵
        $card_id = 1108;//正式卡劵
//        $card_id = 761;//测试卡劵
        $where_one['openid']   = $commodity_id['openid'];
        $where_one['act_name'] = '开春活动50';
        $where_one['status']   = array('eq', 1);
        $where_one['card_id']  = $card_id;
        $count                 = $bu_card->where($where_one)->find();
        if (!empty($count)) {
            $res = $bu_card->save(['status' => 4, 'is_get_card' => 4], ['id' => $count['id']]);
            if ($res) {
                return true;
            } else {
                return false;
            }
        }
    }

    $good_id2 = [3541, 3540, 3539, 3538, 1718, 1717, 1716, 1715, 1589, 1588, 1587, 1586, 1585, 1584, 1583, 1582, 1581, 1580, 1579, 1578, 1577, 1575, 1574, 1573, 1572, 1571];
//    $good_id2 = [2954,2953,2913];//测试商品id
    if (in_array($commodity_id['commodity_id'], $good_id2)) {
        $card_id = [1104, 1105, 1102, 1101, 1103, 1096, 1106, 1098, 1107, 1100];
//        $card_id = [762,763,764];//测试卡劵id
        $where_two['openid']   = $commodity_id['openid'];
        $where_two['act_name'] = '开春活动150';
        $where_two['status']   = array('eq', 1);
        $where_two['card_id']  = array('in', $card_id);
        $count                 = $bu_card->where($where_two)->select();
        $array                 = [];
        foreach ($count as $key => $value) {
            $array[] = [
                'id'          => $value['id'],
                'status'      => 4,
                'is_get_card' => 4
            ];
        }
        $res = $bu_card->saveAll($array);
        if ($res) {
            return true;
        } else {
            return false;
        }
    }
    return true;
}


/**
 *邮件提醒
 * $type 1支付完成,2退款申请
 */
function mail_order($order_code, $type, $bao_yang = 0, $test = 0)
{
    //改成定时任务去跑

    $sendMail =  new \app\common\net_service\SendMailer();
    $sendMail->send_mail(['type'=>$type,'order_code'=>$order_code]);
    return 1;

    $sdk_data = [
        'order_code'=>$order_code,
        'type'=>$type,
        'bao_yang'=>$bao_yang,
        'test'=>$test,
    ];
    $data = ['type' => 'send_mail', 'data' => $sdk_data,'created_at'=>time()];
    \think\Queue::push('app\common\queue\OrderQueue', json_encode($data),config('queue_type.order'));
    return 1;
//    $order_goods_model = new \app\common\model\bu\BuOrderCommodity();
//    $order_model       = new \app\common\model\bu\BuOrder();
//    $order             = $order_model->getOne(['where' => ['order_code' => $order_code]]);
//
//
//    if (!$order) {
//        return false;
//    }
//    $act_name = '';
//    if ($order['act_code']) {
//        $act_code = json_decode($order['act_code'], true);
//        if ($act_code) {
//            foreach ($act_code as $v) {
//                $act_name .= $order_model::act_name($v) . ",";
//            }
//        }
//    }
//
//    $order_g_sql        = sprintf("SELECT a.*,b.commodity_name,d.sku_code from t_bu_order_commodity a
//INNER JOIN t_db_commodity b on a.commodity_id= b.id
//INNER JOIN t_db_commodity_set_sku c on a.sku_id=c.id
//INNER JOIN t_db_commodity_sku d on c.commodity_sku_id=d.id
//WHERE  a.order_code='%s'", $order_code);
//    $order_goods        = $order_goods_model->query($order_g_sql);
//    $mail_goods_name    = '';
//    $mail_sku_code      = '';
//    $mail_sku_price     = '';
//    $mail_sku_info      = '';
//    $mail_sku_price_old = '';
//    if ($order_goods) {
//        foreach ($order_goods as $v) {
//            $mail_goods_name    .= $v['commodity_name'] . ",";
//            $mail_sku_code      .= $v['sku_code'] . ",";
//            $mail_sku_price     .= $v['price'] . ",";
//            $mail_sku_price_old .= $v['b_act_price'] . ",";
//            if ($v['sku_info']) {
//                $sku_info_arr  = explode(',', $v['sku_info']);
//                $sku_info_jian = '';
//                foreach ($sku_info_arr as $vv) {
//                    if ($vv) {
//                        $sku_tmp       = explode(':', $vv);
//                        $sku_tmp       = end($sku_tmp);
//                        $sku_info_jian .= $sku_tmp . ' ';
//                    }
//
//                }
//                $mail_sku_info .= $sku_info_jian;
//            }
//
//        }
//    }
//
//    if ($bao_yang != 0) {
//        $dlr_model = new \app\common\model\act\AcByDlrCode();
//    } else {
//        $dlr_model = new \app\common\model\db\DbDlr();
//    }
//
//    $dlr = $dlr_model->getOne(['where' => ['dlr_code' => $order['dlr_code']]]);
//    if (!$dlr) {
//        return false;
//    }
//    if ($type == 1) {
//        //支付推送
//        if ($test == 0) {
//            order_send_tpl($order['order_code'], date('Y-m-d H:i:s'), $dlr['dlr_name'], $order['total_money'], sprintf("%s(%s)", trim($mail_goods_name, ','), trim($mail_sku_info, ',')), $order['openid']);
//        }
//
//    }
//    if (!$dlr['email1'] && !$dlr['email2']) {
//        return false;
//    }
//    if ($type == 1) {
//        if ($order['sale_source'] != 5) {
//            $title     = "专营店备件部";
//            $title     .= "（同步抄送售后经理、SA、财务部，请相互转达）:<br/>";
//            $content   = "您好！<br/>";
//            $content   .= "客户在微信线商城上，购买以下精品，请确认店内是否有库存，若无库存，请自行在系统上订货；请查看以下订单。<br/>";
//            $content   .= "一、订单信息：<br/>";
//            $content   .= sprintf("1、客户：%s<br/>", $order['name']);
//            $content   .= sprintf("2、手机号：%s<br/>", $order['phone']);
//            $content   .= sprintf("3、车牌号：%s<br/>", $order['license_plate']);
//            $content   .= sprintf("4、精品名称：%s<br/>", trim($mail_goods_name, ','));
//            $content   .= sprintf("5、备件号：%s<br/>", trim($mail_sku_code, ','));
//            $content   .= sprintf("6、订单号：%s<br/>", $order['order_code']);
//            $content   .= sprintf("7、备注：%s<br/>", $order['remark']);
//            $content   .= "二、联系窗口 在线订单购买核销等疑问，请联系车巴巴平台——毕老师*******************<br/>";
//            $content   .= "<p style='color: red;font-size: 22px; font-weight: 600;'>该邮件为系统自动发送，请勿回复。谢谢！</p><br/>";
//            $mail_type = 1;//1:车管家 2,精品科
//        } else {
//            if ($bao_yang != 0) {
//                #保养套餐专用邮件--
//                $title     = "老友惠保养套餐新订单提醒！";
//                $content   = "<p style='color: red;font-size: 22px; font-weight: 600;'>该邮件为系统自动发送，请勿回复。谢谢！</p>";
//                $content   = "<p style='color: red;font-size: 18px; font-weight: 600;'>(请扫文末二维码添加微信客服，第一时间接收保养套餐订单线索)</p>";
//                $content   .= "您好！<br/>";
//                $content   .= "客户在东风日产车主俱乐部会员商城上，已预定老友惠保养套餐，请及时与客户沟通，并按如下要求做好客户服务：<br/>";
//                $content   .= "一、订单信息：(未绑定车主不显示车牌号和VIN码)<br/>";
//                $content   .= sprintf("1、客户：%s<br/>", $order['name']);
//                $content   .= sprintf("2、手机号：%s<br/>", $order['phone']);
//                $content   .= sprintf("3、车牌号：%s<br/>", $order['license_plate']);
//                $content   .= sprintf("4、VIN：%s<br/>", $order['vin']);
//                $content   .= sprintf("5、商品名称：%s(%s)<br/>", trim($mail_goods_name, ','), trim($mail_sku_info, ','));
//                $content   .= sprintf("6、订单号：%s<br/>", $order['order_code']);
//                $content   .= " <br/>";
//                $content   .= "二、客户服务要求：<br/>";
//                $content   .= "1、与客户电话确认回厂时间，并告知到店后联系人，客户回厂后按预约客户优先接待；<br/>";
//                $content   .= "2、请确认贵店库存，提前为该客户预留库存（含订货补货）；<br/>";
//                $content   .= "3、客户回店购买套餐时，与客户签订套餐合同，并说明套餐有效期等事项；<br/>";
//                $content   .= " <br/>";
//                $content   .= "三、售后处理：<br/>";
//                $content   .= "1、客户购买套餐后，请做好后续定保回厂提醒邀约；<br/>";
//                $content   .= " <br/>";
//                $content   .= "四、联系窗口<br/>";
//                $content   .= "在线订单购买核销等疑问，请联系：车巴巴平台--张晓莉：<EMAIL><br/>";
//                $content   .= "扫一扫添加微信客服，或者复制微信号添加（微信号：LYHBYTC2019）每家店需至少一位SA添加，添加后请马上提供大区、专营店名称、编码，以免错过线索下发。如已有负责人添加则无需再添加。<br/>";
//                $content   .= sprintf("<img src='%s'><br/>", "http://wxstore.chebaba.com/public/static/active/bao_yang_price/images/baoyou_qrcode.jpg");
//                $mail_type = 3;//1:车管家 2,精品科 3售后服务部服务营销科
//
//
//            } else {
//                $title = "东风日产会员商城纯正备件/精品新订单提醒！";
////            $title .= "（同步抄送售后经理、SA、财务部，请相互转达）:<br/>";
//                $content   = "<p style='color: red;font-size: 22px; font-weight: 600;'>该邮件为系统自动发送，请勿回复。谢谢！</p><br/>";
//                $content   .= "您好！<br/>";
//                $content   .= "客户在东风日产会员商城上，购买以下备件/精品，请检查库存是否足够，并按如下要求做好客户服务：<br/>";
//                $content   .= "一、订单信息：<br/>";
//                $content   .= sprintf("1、客户：%s<br/>", $order['name']);
//                $content   .= sprintf("2、手机号：%s<br/>", $order['phone']);
//                $content   .= sprintf("3、车牌号：%s<br/>", $order['license_plate']);
//                $content   .= sprintf("4、产品名称：%s<br/>", trim($mail_goods_name, ','));
//                $content   .= sprintf("5、备件号：%s<br/>", trim($mail_sku_code, ','));
//                $content   .= sprintf("6、单品原价：%s<br/>", trim($mail_sku_price_old, ','));
//                $content   .= sprintf("7、单品活动价：%s<br/>", trim($mail_sku_price, ','));
//                $content   .= sprintf("8、订单总金额：%s<br/>", $order['total_money']);
//                $content   .= sprintf("9、实付厂家积分：%s<br/>", $order['integral']);
//                $content   .= sprintf("10、实付现金：%s<br/>", $order['money']);
//                $content   .= sprintf("11、客户支付方式：%s<br/>", $order_model::pay_type($order['payment_method']));
//                $content   .= sprintf("12、参与活动：%s<br/>", $act_name);
//                $content   .= sprintf("13、订单编码：用于查询订单明细：%s<br/>", $order['order_code']);
//                $content   .= sprintf("14、商户订单号：用于查询资金到账：%s<br/>", $order['pay_order_code']);
//                $content   .= sprintf("15、提醒：%s<br/>", $order['remark']);
//                $content   .= "如无库存，请及时向东风日产下单订购并致电联系客户确认回店时间，订购流程同现备件订货流程。或在征得客户同意的情况下，可以使用同等级同型号且价格相同的产品，进行代替。<br/>";
//                $content   .= " <br/>";
//                $content   .= "二、客户服务要求：<br/>";
//                $content   .= "1、请确认贵店库存，提前为该客户预留库存（含订货补货）；<br/>";
//                $content   .= "2、主动联系客户邀约回厂时间，客户回厂后按预约客户优先接待；<br/>";
//                $content   .= "3、客户到店后，请让客户出示订单二维码，进行核销操作，具体操作可查看指引；<br/>
//订单操作指引文件： 《线上商城备件/精品订单操作指引》 <br/>
//百度云下载地址链接：<br/>
//https://pan.baidu.com/s/1wOPSchi3A1A8xuXZ_9EYhQ 提取码：re50<br/>";
//                $content   .= "4，	安装后，向客户交付产品说明书，质保书等资料；<br/>";
//                $content   .= "5、专营店不得以次充好、调换其他产品。如有违反，总部将予以通报。<br/>";
//                $content   .= " <br/>";
//                $content   .= "三、售后处理：<br/>";
//                $content   .= "1，	若客户对线下产品有质保疑问，请各店耐心为客户解惑；<br/>";
//                $content   .= "1，	客户对产品的质量投诉，请按线下产品质量投诉流程处理。<br/>";
//                $content   .= " <br/>";
//                $content   .= "四、联系窗口<br/>";
//                $content   .= "在线订单购买核销等疑问，请联系车巴巴平台——毕老师*******************，电话18027348765<br/>";
//                $mail_type = 2;//1:车管家 2,精品科
//            }
//
//        }
//
//    } else {
//        if ($order['sale_source'] != 5) {
//            $title     = "专营店备件部";
//            $title     .= "（同步抄送售后经理、SA、财务部，请相互转达）:<br/>";
//            $content   = "您好！<br/>";
//            $content   .= "客户在车管家商城上，购买以下精品，该客户已申请退款，请前往核销后台审核，根据实际情况给客户退款。<br/>";
//            $content   .= "一、订单信息：<br/>";
//            $content   .= sprintf("1、客户：%s<br/>", $order['name']);
//            $content   .= sprintf("2、手机号：%s<br/>", $order['phone']);
//            $content   .= sprintf("3、车牌号：%s<br/>", $order['license_plate']);
//            $content   .= sprintf("4、精品名称：%s<br/>", trim($mail_goods_name, ','));
//            $content   .= sprintf("5、备件号：%s<br/>", trim($mail_sku_code, ','));
//            $content   .= sprintf("6、订单号：%s<br/>", $order['order_code']);
//            $content   .= sprintf("7、备注：%s<br/>", $order['remark']);
//            $content   .= "二、联系窗口 在线订单购买核销等疑问，请联系：车巴巴平台--毕老师*******************<br/>";
//            $content   .= "<p style='color: red;font-size: 22px; font-weight: 600;'>该邮件为系统自动发送，请勿回复。谢谢！</p><br/>";
//            $mail_type = 1;//1:车管家 2,精品科
//        } else {
//            $title = "东风日产会员商城退款提醒！";
////            $title .= "（同步抄送售后经理、SA、财务部，请相互转达）:<br/>";
//            $content = "<p style='color: red;font-size: 22px; font-weight: 600;'>该邮件为系统自动发送，请勿回复。谢谢！</p><br/>";
//            $content .= "您好！<br/>";
//            $content .= "客户在车管家商城上，购买以下备件/精品，该客户已申请退款，请前往核销后台审核，根据实际情况给客户退款。<br/>";
//            $content .= "一、订单信息：<br/>";
//            $content .= sprintf("1、客户：%s<br/>", $order['name']);
//            $content .= sprintf("2、手机号：%s<br/>", $order['phone']);
//            $content .= sprintf("3、车牌号：%s<br/>", $order['license_plate']);
//            $content .= sprintf("4、产品名称：%s<br/>", trim($mail_goods_name, ','));
//            $content .= sprintf("5、备件号：%s<br/>", trim($mail_sku_code, ','));
//            $content .= sprintf("6、单品原价：%s<br/>", trim($mail_sku_price_old, ','));
//            $content .= sprintf("7、单品活动价：%s<br/>", trim($mail_sku_price, ','));
//            $content .= sprintf("8、订单总金额：%s<br/>", $order['total_money']);
//            $content .= sprintf("9、实付厂家积分：%s<br/>", $order['integral']);
//            $content .= sprintf("10、实付现金：%s<br/>", $order['money']);
//            $content .= sprintf("11、客户支付方式：%s<br/>", $order_model::pay_type($order['payment_method']));
//            $content .= sprintf("12、参与活动：%s<br/>", $act_name);
//            $content .= sprintf("13、订单编码：用于查询订单明细：%s<br/>", $order['order_code']);
//            $content .= sprintf("14、商户订单号：用于查询资金到：%s<br/>", $order['pay_order_code']);
//
//            $content .= "二、客户服务要求：<br/>";
//            $content .= "1、请主动联系客户，确认退款订单信息，及时对客户订单进行操作退款；<br/>";
//            $content .= "2、退款操作，具体可查看完整指引；<br/>
//订单操作指引文件： 《线上商城备件/精品订单操作指引》 <br/>
//百度云下载地址链接：<br/>
//https://pan.baidu.com/s/1wOPSchi3A1A8xuXZ_9EYhQ 提取码：re50<br/>";
//            $content .= "三、联系窗口<br/>";
//            $content .= "在线订单购买核销等疑问，请联系车巴巴平台——毕老师*******************，电话18027348765<br/>";
////            $content .= "<p style='color: red;font-size: 22px; font-weight: 600;  '>该邮件为系统自动发送，请勿回复。谢谢！</p><br/>";
//            $mail_type = 2;//1:车管家 2,精品科
//        }
//    }
//    $data = ['address' => $dlr['email1'], 'address2' => $dlr['email2'], 'content' => $content, 'title' => $title, 'type' => $mail_type];
//    $mail = new \api\wechat\Mail();
//    $res  = $mail->sendMail($data);
//    return $res;
}

/**
 * 发送下单通知模板消息
 * @param $order_no
 * @param $time
 * @param $dlr_name
 * @param int $money
 * @param $goods_name
 * @param $openid
 */
function order_send_tpl($order_no, $time, $dlr_name, $money, $goods_name, $openid)
{
    if (empty($money)) $money = 0;
    $data   = array(
        'first'    => array(
            'value' => "尊敬的客户，您已成功下单。",
            'color' => '',
        ),
        'keyword1' => array(
            'value' => $order_no,
            'color' => '',
        ),
        'keyword2' => array(
            'value' => $time,
            'color' => '',
        ),
        'keyword3' => array(
            'value' => $dlr_name,
            'color' => '',
        ),
        'keyword4' => array(
            'value' => $money,
            'color' => '',
        ),
        'keyword5' => array(
            'value' => $goods_name,
            'color' => '',
        ),
        'remark'   => array(
            'value' => "服务类订单30天内有效，请及时回店使用。直邮类订单将在48小时内发货。如有疑问请联系会员商城在线客服咨询，路径：车主福利-NI+商城。",
            'color' => '',
        )
    );
    $wx_url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . AccessToken::get("GWSC");
    $param  = [
        'touser'      => $openid,
        'template_id' => '7zH9lmRwYb1PglNTIdvpjz5DMjbyTQwHhTReyRo0NVc',
        'url'         => '',
        'data'        => $data,
    ];
    $result = Curl::post($wx_url, $param);
    if ($result['errcode'] == 0) {
        $tpl_model = new \app\common\model\bu\BuTplMsg();
        $_data     = array(
            'openid'            => $param['touser'],
            'dlr_code'          => 'GWSC',
            'tpl_id'            => $param['template_id'],
            'url'               => $param['url'],
            'data'              => serialize($param['data']),
            'errcode'           => $result['errcode'],
            'errmsg'            => $result['errmsg'],
            'msgid'             => $result['msgid'],
            'created_date'      => date('Y-m-d H:i:s'),
            'last_updated_date' => date('Y-m-d H:i:s'),
        );
        $res       = $tpl_model->insertGetId($_data);
    }
}

/**
 * 库存报警
 *
 *
 */
function mail_count($sku, $type = 1)
{
    $commodity_set_model = new \app\common\model\db\DbCommoditySet();
    $commodity_set       = $commodity_set_model->getOneByPk($sku['commodity_set_id']);
    $sku_value           = '';
    if ($sku['sp_value_list']) {
        foreach ($sku['sp_value_list'] as $vv) {
            $sku_value .= $vv['sp_name'] . ":" . $vv['sp_value_name'] . ", ";
        }
    }

    $content = '';
    $content .= sprintf("商品：%s<br/>", $sku['commodity_name']);
    $content .= sprintf("商品ID：%s<br/>", $sku['commodity_id']);
    $content .= sprintf("商品总库存：%s<br/>", $commodity_set['count_stock']);
    $content .= sprintf("规格信息：%s<br/>", $sku_value);
    $content .= sprintf("规格库存：%s<br/>", $sku['stock']);
    $content .= sprintf("请维护");
    $data    = ['address' => "<EMAIL>", 'address2' => "<EMAIL>", 'content' => $content, 'title' => "商品库存不足5个", 'type' => $type];
    $mail    = new \api\wechat\Mail();
    $res     = $mail->sendMail($data);
//    $data2 = ['address' => "<EMAIL>", 'content' => $content, 'title' => "商品库存不足5个", 'type' => $type];
//    $res2 = $mail->sendMail($data2);
}

/**
 * @param $order_code
 * 送积分
 */
function add_point($order_code)
{
//    die();
    return '';// 20211213  黄敏华  先停 李工，那个50元送10分的更新先不要发版了，现在涉及到费用的问题，麻烦先暂停吧
    $add_p_type  = "add_point";
    $order_model = new \app\common\model\bu\BuOrder();
    $order       = $order_model->getOne(['where' => ['order_code' => $order_code, 'dlr_code' => ['in', ['GWSC', 'CHEBABA','GWSM','GWAPP','GWNET']]], 'field' => "order_code,openid,money"]);
    if ($order) {
        if ($order['money'] >= 50) {
            $log_model = new \app\common\model\db\DbLog();
            $log       = $log_model->getOne(['where' => ['order_code' => $order_code, 'type' => $add_p_type], 'field' => "id"]);
            if (!$log) {
                $car_er_api = new \api\wechat\Carer();
                $carer      = $car_er_api->vin(array('openid' => $order['openid']));
                if (!$carer) {
                    return false;
                }
                $muli = 0;
                if (strpos($carer['card_degree_name'], '会员金卡') !== false || strpos($carer['card_degree_name'], '铂金卡') !== false) {
                    $muli = 2;
                } elseif (strpos($carer['card_degree_name'], '普卡') !== false || strpos($carer['card_degree_name'], '银卡') !== false) {
                    $muli = 1;
                } else {
                    return false;
                }
                $point     = floor($order['money'] / 50) * $muli * 10;//50送1  10倍
                $data      = [
                    'openid'   => $order['openid'],
                    'order_id' => $order['order_code'],
                    'dlr_code' => 'PV',//送积分用PV
                    'point'    => $point,
                ];
                $point_res = $car_er_api->addPoint($data);

                $o_data = [
                    'gift_score' => $point,
                    'ic_card_no' => $carer['ic_card_no'],
                    'vin'        => $carer['vin']
                ];
                $order_model->saveData($o_data, ['order_code' => $order_code]);//记录赠送积分


                $log_data = array(
                    'type'         => $add_p_type,
                    'order_code'   => $order_code,
                    'send_note'    => json_encode_cn($data),
                    'receive_note' => json_encode_cn($point_res),
                );
                if ($point_res) {
                    $log_data['is_success'] = 'success';
                } else {
                    $log_data['is_success'] = 'fail';
                }
                $log_model->insertData($log_data);
            }
        }

    }


}

/**
 *
 * 直接插入到缓存
 */
function browing($data)
{
    if (config('PAY_CHEBABA') == 0) {
        return false;
    }
    $redis_name = "wxstore_browing_all";
    $time       = date('Y-m-d H:i:s');
    $_data      = array(
        'openid'            => isset($data['openid']) ? $data['openid'] : '',
        'user_id'           => isset($data['user_id']) ? $data['user_id'] : '',
        'commodity_id'      => isset($data['commodity_id']) ? $data['commodity_id'] : '',//id，一般为商品ID
        'dlr_code'          => isset($data['dlr_code']) ? $data['dlr_code'] : '',
        'source'            => isset($data['source']) ? $data['source'] : '',
        'vin'               => isset($data['vin']) ? $data['vin'] : '',
        'mothod'            => isset($data['mothod']) ? $data['mothod'] : '',
        'license_plate'     => isset($data['license_plate']) ? $data['license_plate'] : '',
        'name'              => isset($data['name']) ? $data['name'] : '',
        'phone'             => isset($data['phone']) ? $data['phone'] : '',
        'car_series_id'     => isset($data['car_series_id']) ? $data['car_series_id'] : '',
        'last_updated_date' => $time,
        'created_date'      => $time,
    );

    $redis = \think\Cache::redisHandler();
    $res   = $redis->Lpush($redis_name, json_encode($_data));
}

if (!function_exists('getRedisLock')) {
    /**
     * 获取redis锁，默认十秒过期
     *
     * @param string $index
     * @param integer $expire
     * @param string $suffix 后缀
     * @return bool
     */
    function getRedisLock($index, $expire = 10, $suffix = '')
    {
        $key     = $index . '-' . $suffix;
        $redis   = \think\Cache::redisHandler();
        $is_lock = $redis->set($key, time() , ['NX', 'EX'=>$expire]);
        return !empty($is_lock);
    }
}

if (!function_exists('delRedisLock')) {
    /**
     * 删除过期键
     *
     * @param integer $index
     * @param string $suffix 后缀
     * @return bool
     */
    function delRedisLock($index, $suffix = '')
    {
        $key      = $index . '-' . $suffix;
        $redis    = \think\Cache::redisHandler();
        $del_lock = $redis->rm($key);

        return $del_lock ? true : false;
    }
}


/**
 * 企业微信JSSDK
 * @param string $dlrCode 专营店编码
 * @param boolean $debug 调试模式
 */
function qy_js_sdk($a_code, $debug = false)
{
    $url       = request()->url(true);//"http://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
    $timestamp = time();
    $nonceStr  = get_rand_str();
    $tt        = new \api\WxQy\Qy_ticket();
    $ticket    = $tt::get($a_code);
    $data      = array(
        'jsapi_ticket' => $ticket,
        'noncestr'     => $nonceStr,
        'timestamp'    => $timestamp,
        'url'          => $url,
    );

    ksort($data, SORT_STRING);
    $string    = urldecode(http_build_query($data));
    if($a_code == "cgj_002"){
        $corpid    = config('qc_wx_qy_setting.corpid');
    }else{
        $corpid    = config('wx_qy_setting.corpid');
    }
    $signature = sha1($string);
    \tool\Logger::debug('qy_js_jdsk', ['str' => $string, 'sig' => $signature]);
    $debug = $debug ? 'true' : 'false';
//    $http_type = (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443) ? 'https://' : 'http://';
    $wx_url = "//res.wx.qq.com/open/js/jweixin-1.2.0.js?v=*******";
    $jsSdk  = <<<EOF
    <script src="$wx_url" type="text/javascript"></script>
    <script type="text/javascript">
        wx.config({
            beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
            debug: $debug, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: "$corpid", // 必填，公众号的唯一标识
            timestamp:$timestamp, // 必填，生成签名的时间戳
            nonceStr: "$nonceStr", // 必填，生成签名的随机串
            signature: "$signature",// 必填，签名，见附录1
            jsApiList: [
            'checkJsApi',
            'openEnterpriseChat',
            'openEnterpriseContact',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'onMenuShareQQ',
            'onMenuShareWeibo',
            'onMenuShareQZone',
            'startRecord',
            'stopRecord',
            'onVoiceRecordEnd',
            'playVoice',
            'pauseVoice',
            'stopVoice',
            'onVoicePlayEnd',
            'uploadVoice',
            'downloadVoice',
            'chooseImage',
            'previewImage',
            'uploadImage',
            'downloadImage',
            'translateVoice',
            'getNetworkType',
            'openLocation',
            'getLocation',
            'hideOptionMenu',
            'showOptionMenu',
            'hideMenuItems',
            'showMenuItems',
            'hideAllNonBaseMenuItem',
            'showAllNonBaseMenuItem',
            'closeWindow',
            'scanQRCode']
        });
    </script>
EOF;
    return $jsSdk;
}


/**
 * @param $fileName
 * @param array $head
 * @param $body
 * <AUTHOR>
 */
function exportCSV($fileName, $head = [], $body = [])
{
    //声明头结构
    header('Content-Encoding: UTF-8');
    header('Content-type: text/csv; charset=UTF-8');
    header('Content-Type: application/vnd.ms-excel');   //header设置
    header("Content-Disposition: attachment;filename=" . $fileName . ".csv");
    header('Cache-Control: max-age=0');

    echo "\xEF\xBB\xBF";

    $fp = fopen('php://output', 'a');

    foreach ($head as $k => $v) {
        $t        = mb_detect_encoding($v);
        $head[$k] = mb_convert_encoding($v, 'UTF-8', $t);
    }

    //写入标题
    fputcsv($fp, $head);

    $data = [];
    foreach ($body as $key => $val) {
        foreach ($val as $i => $item) {  //$item为一维数组
            //日期类型单独处理
            if (is_array($item) && $item['key'] == 'date') {
                if (!empty($item['data'])) {
                    $data[$i] = date('Y-m-d H:i:s', strtotime($item['data']));
                } else {
                    $data[$i] = '';
                }
            } else {
                //排除日期类型，其他转
                $t        = mb_detect_encoding($item);
                $data[$i] = mb_convert_encoding($item, 'UTF-8', $t);
            }
        }

        //逐行写入
        fputcsv($fp, $data);
    }

    exit;
}


function pay_order_query($pay_order_code)
{
    $input = new WxPayOrderQuery();
    $input->SetOut_trade_no($pay_order_code);
    $order = WxPayApi::orderQuery($input);
    return $order;
}


//判断N件N折

function _checkNDis($com_set_id)
{
    $n_dis_goods_model = new \app\common\model\db\DbNDiscountCommodity();
    $time              = date('Y-m-d H:i:s');
    $where             = ['b.start_time' => ['<=', $time], 'b.end_time' => ['>=', $time], 'b.is_enable' => 1, 'a.is_enable' => 1, "a.commodity_set_id" => $com_set_id
    ];

    $param['where'] = $where;
    $param['field'] = "a.n_id,a.commodity_id,a.commodity_set_id,b.title,b.des";
    $row            = $n_dis_goods_model->getNdisInfo($param);
//    echo $n_dis_goods_model->getLastSql();
    if ($row) {
//            //已售多少。
//            $order_model =  new model\bu\BuOrder();
//            $count_order_where = [
//                'order_status'=>['not in',[1,3,5,6,8,10]],
//                'sale_source'=>5,
//                'n_dis_id'=>$row['n_id'],
//                'dlr_code'=>$this->dlrCode,
//            ];
//            $order_count = $order_model->getCount(['where'=>$count_order_where]);//统计该活动订单数
//            if($order_count){
//                $row['s_c']=  $order_count;
//            }else{
//                $row['s_c']= 0;
//            }
        $dis_url        = url('search', ['n_dis_id' => $row['n_id']], false, true);
        $row['dis_url'] = $dis_url;
    }
    return $row;
}


function redisStrSet($set_name, $cache_key)
{
    $set_str   = redis($set_name);
    $set_arr   = json_decode($set_str, true);
    $set_arr[] = $cache_key;
    return redis($set_name, json_encode($set_arr), 86400);
}

/**
 * tim 调用接口
 * @param $path
 * @param $data
 * @return bool|mixed
 * @throws Exception
 */
function imApi($path, $data)
{
    $api = new TLSSigAPIv2(config('tim.sdk_app_id'), config('tim.sdk_app_key'));
    $sig = $api->genSig('administrator');

    $random = mt_rand(0, 4294967295);
    $url    = "https://console.tim.qq.com/v4/{$path}?"
        . "sdkappid=" . config('tim.sdk_app_id') . "&"
        . "identifier=administrator&"
        . "usersig={$sig}&"
        . "random={$random}&contenttype=json";

    return http_post($url, $data, false, true);
}


/**
 * 电商订单主表接口表
 * http://172.26.130.168:8096/postdata/DNDC_ONLINESHOP/DNDC_RECEIVE_MAIN
 */
function postOrderIndexToly($order_code, $status = 0)
{
    if (empty($order_code)) {
        return false;
    }
    $BuOrderObj          = new BuOrder();
    $toE3sIndex          = new BuToE3sIndex();
    $BuOrderCommodityObj = new BuOrderCommodity();

    //传过的不传
    $is_have = $toE3sIndex->where("to_order_code =  '{$order_code}'")->find();
    if (!empty($is_have)) {
        return false;
    }

//    //有轮胎不传
//    $found_info = $BuOrderCommodityObj->where("order_code = '{$order_code}' and commodity_id in (1800,1801)")->find();
//    if(!empty($found_info)){
//        return false;
//    }

    $field = 'a.b_act_goods_price,a.pre_point,a.pre_use_money,a.all_act_yh,a.all_card_yh,a.order_vin,a.channel,
    sum(b.work_time_actual_money) work_time_actual_money,a.n_dis_id,a.total_money,a.order_code,b.dd_dlr_code dlr_code,
    a.id,b.b_act_price,b.count,a.money,a.integral,a.mail_price,a.dlr_integral,a.card_money,a.vin,a.b_work_time_price,
    a.pay_time,a.brand';
    $order_info = $BuOrderObj->alias("a")
        ->join("t_bu_order_commodity b","a.order_code = b.order_code")
        ->join("t_db_commodity g","g.id=b.commodity_id")
        ->where("a.sale_source= 5 and  a.order_status=19 and a.logistics_mode=1 and a.order_code = '{$order_code}'  or (a.is_by_tc >=1 and  a.order_code = '{$order_code}' and a.order_status=19)")
        ->field($field)
        ->group("order_code")
        ->find();

    if (!empty($order_info)) {
//        $sql                = "SELECT a.count,sc.name as scname,sc.price as price_item,sc.sku_code as sku_code_item,`e`.`sku_code`,`c`.`commodity_set_id`
//    FROM `t_bu_order_commodity` `a`
//    INNER JOIN `t_db_commodity` `g` ON `g`.`id`=`a`.`commodity_id`
//    INNER JOIN `t_bu_order` `b` ON `a`.`order_code`=`b`.`order_code`
//    INNER JOIN `t_db_commodity_set_sku` `c` ON `a`.`commodity_id`=c.commodity_id and c.id=a.sku_id
//    INNER JOIN `t_db_commodity_sku` `e` ON `e`.`id`=`c`.`commodity_sku_id`
//    INNER JOIN `t_db_commodity_sku_code` `sc` ON FIND_IN_SET(sc.sku_code,e.sku_code)
//    WHERE ( a.order_code = '{$order_code}' ) order by a.id asc";
//        $commodity_list_tmp = $BuOrderCommodityObj->query($sql);
//        $true_all_price     = 0;
//        foreach ($commodity_list_tmp as $item) {
//
//            $true_all_price = $true_all_price + $item['price_item'] * $item['count'];//商品原价
//        }
//
//        $true_all_price = $true_all_price + $order_info['mail_price'];//加运费
//        $data           = [];
//        $true_dis_price = 0;
//
//        $money_one      = $order_info['total_money'] - $order_info['money'];
//        $money_two      = $order_info['integral'] / 10;
//        $true_dis_price = bcsub($true_all_price, $order_info['total_money'], 2);
        $data           = array(
            'to_id'=>$order_info['id'],
            'to_order_code'=> $order_info['order_code'] ,
            'to_order_name'=>'',
            'to_order_amount'=>bcadd($order_info['b_act_goods_price'], $order_info['b_work_time_price'], 2),
            'to_order_fact_amount'=>$order_info['money'] + ($order_info['integral'] /10) + ($order_info['pre_point'] /10) + $order_info['pre_use_money'],
            'to_wechat_pay_amount'=>$order_info['money'] +  $order_info['pre_use_money'],
            'to_point_pay_amount'=>$order_info['integral'] + $order_info['pre_point'],
            'to_alipay_pay_amount'=>0,
            'to_action_discount_amount'=>$order_info['all_act_yh'] + $order_info['all_card_yh'],
            'to_source_type'=>$BuOrderObj->getE3sChannel($order_info['channel']),
            'to_dlr_code'=>$order_info['dlr_code'],
            'to_vin'=>$order_info['order_vin'],
            'to_status'=>$status,
            'to_pay_date' => $order_info['pay_time'],
            'to_source_shop_mall_type' => $order_info['brand'],
//            'MAINTAIN_GROUP_TYPE' => '',//
        );
        $toE3sIndex->insertGetId($data);
        return true;
    }
    return false;
}

/**
 * 电商订单成功退款
 */
function postOrderRefundToly($order_code)
{
//    $IRequestLogObj  = new IRequestLog();
//    $IResponesLogObj = new IResponesLog();
//    $BuOrderObj      = new BuOrder();
//    $order_info      = $BuOrderObj->alias("a")
//        ->join("t_bu_order_commodity b", "a.order_code = b.order_code")
//        // ->join("t_db_commodity_dlr e","e.commodity_id = b.commodity_id and e.dlr_code = b.dlr_code")
//        ->join("t_db_commodity g", "g.id=b.commodity_id")
//
//        ->where("a.sale_source= 5 and a.order_status=5 and a.logistics_mode=1 and a.order_code = '{$order_code}'  or (a.is_by_tc >=1 and  a.order_code = '{$order_code}' and a.order_status=5)")
//
//        ->field("a.act_code,a.n_dis_id,a.total_money,a.order_code,a.dlr_code,a.id,b.b_act_price,b.count,a.money,a.integral,a.mail_price,a.dlr_integral,a.card_money,a.vin")
//        ->group("order_code")
//        ->find();

//    if (!empty($order_info)) {
//        $datas          = array('ORDER_CODE' => $order_code, 'STATUS' => 9);
//        $data['BODY'][] = $datas;
//        $ridd           = $IRequestLogObj->insertGetId(array('module' => 'api', 'action' => 'common', 'function' => 'postOrderRefundToly', 'data' => json_encode($data)));
//        $headers = ['Content-Type: application/json'];
//        $res            = http_post(config("order_to_ly.refund"), $data, true,true,true,true,$headers);
//        $res = E3spRefactor::create('e3sp_refactor')->toRefund($data);

        $toE3sIndex     = new BuToE3sIndex();
        $toE3sIndex->update(['to_status' => 5, 'is_enable'=>1], ['to_order_code' => $order_code]);
//        $IResponesLogObj->insertGetId(array('request_id' => $ridd, 'module' => 'api', 'action' => 'common', 'function' => 'postOrderRefundToly', 'data' => json_encode($res)));
//    }
        // 重推主表
        $service = new E3sPostData();
        $service->pushIndexOrder($order_code);
}


/**
 * 卡券折扣金额
 * @param $price
 * @param $cardDiscount
 * @return string
 */
function getCardPrice($price, $cardDiscount)
{
    return bcmul(bcdiv(bcsub(10,$cardDiscount),10,2), $price,2);
}


/**
 * 获取活动折扣
 * @param $order_commodity_item
 * @return int|string|null
 * @throws \think\db\exception\DataNotFoundException
 * @throws \think\db\exception\ModelNotFoundException
 * @throws \think\exception\DbException
 */
function getActivityDiscount($order_commodity_item)
{
    // 电商活动折扣
    $to_action_discount = 1;
    $discount_price = 0;
    $discount_price_n = 0;
    // 限时折扣
    if ($order_commodity_item['limit_id'] != 0) {
        // 查询限时折扣
        $info = DbLimitDiscount::where('id',$order_commodity_item['limit_id'])->find();
        $dis_commodity_model = new \app\common\model\db\DbLimitDiscountCommodity();
        $map = ['commodity_id'=>$order_commodity_item['commodity_id'], 'limit_discount_id'=>$info['id'], 'is_enable'=>1];
        $dis_commodity_info = $dis_commodity_model->where($map)->find();

        if (!empty($info) && $info['dis_type'] == 1 && in_array($info['discount_type'], [1,3])) {
            $discountArr = json_decode($dis_commodity_info['sku_dis'],true);
            $discount = $discountArr[$order_commodity_item['sku_id']] ?? 0;
            $to_action_discount = bcdiv($discount,10, 2);
            $discount_price = bcmul(bcsub(1,$to_action_discount,2), $order_commodity_item['price'],2);
        }
    }
    // N减N折
    if($order_commodity_item['n_dis_id'] != 0) {
        $to_action_discount = bcdiv($order_commodity_item['actual_price'], $order_commodity_item['price'], 2);
        $discount_price_n = bcmul(bcsub(1,$to_action_discount,2), $order_commodity_item['price'],2);
    }

    // 券
    $card_discount_price = 0;
    if (!empty($order_commodity_item['card_ids'])) {
        $cardIdArr = explode(',', $order_commodity_item['card_ids']);
        $map = [
            'id' => ['in', $cardIdArr],
            'card_type' => 2, // 折扣券
        ];
        $list = DbCard::where($map)->select();
        $cardPrice = [];
        foreach ($list as $item) {
            $cardPrice[] = getCardPrice($order_commodity_item['price'], $item['card_discount']);
        }
        $card_discount_price = array_sum($cardPrice);
    }
    $total_discount_price = bcadd(bcadd($discount_price, $discount_price_n,2), (string)$card_discount_price,2);
    if ($total_discount_price > 0) {
        // 折扣金额大于原价
        if ($total_discount_price >= $order_commodity_item['price']) {
            $to_action_discount = 0;
        } else {
            // 实付金额
            $use_price = bcsub($order_commodity_item['price'], $total_discount_price, 2);
            // 折扣金额小于原价
            $to_action_discount = bcdiv($use_price,$order_commodity_item['price'],2);
        }
    }
    return $to_action_discount;
}

/**
 * 电商订单明细表接口表
 */
function postOrderDetailToly($order_code)
{
    if(empty($order_code)) {
        print_json(-1,'订单号不能为空');
    }
    $buOrderCommodityObj = new BuOrderCommodity();
    $buOrderObj = new BuOrder();
    $dbCommoditySkuCodeObj  = new DbCommoditySkuCode();
    $toE3sDetialObj = new BuToE3sDetail();
    $buE3sPartCartObj = new E3sPartCarSeries();

    $order_info_index_info = $buOrderObj->where(['order_code'=>$order_code])->field("order_source,channel,is_by_tc,distance")->find();
    $is_by_tc = $order_info_index_info['is_by_tc'];
    $distance =  $order_info_index_info['distance'];
    $order_source = $order_info_index_info['order_source'];
    $order_commodity_list = $buOrderCommodityObj->alias('a')
        ->where("a.order_code = '$order_code' and a.mo_id = 0")->field("a.dd_commodity_type,a.third_sku_code sku_code,a.*")->select();

    $arr = [];
    //echo "buOrderCommodityObj:"; echo "<br/>";echo $buOrderCommodityObj->getLastSql();echo "<br/>";echo "<br/>";
    foreach($order_commodity_list as $k=>$order_commodity_item){
        $commodity_sum = 0;//商品总价
        if(!empty($order_commodity_item['sku_code'])){

            $to_action_discount = getActivityDiscount($order_commodity_item);
            $action_name = '';
            if(in_array($order_source,[16,17,18,19,22,40]) ){
                //保养套餐
                $to_maintain_group_type = 0;
                if($order_source == 17) $to_maintain_group_type = 1; // 心悦保养套餐
                if($order_source == 18) $to_maintain_group_type = 2; // 双保升级套餐
                if($order_source == 22) $to_maintain_group_type = 3; // ariya套餐
                if($order_source == 40) $to_maintain_group_type = 4; // 保养套餐-五年双保专享心悦套餐
                $commodity_sku_tmp = [];
                $sku_code_arr = explode(',',$order_commodity_item['sku_code']);
//                $commodity_sum_list =  $dbCommoditySkuCodeObj->whereIn("sku_code",$sku_code_arr)->field('price,sku_code,name')->order("price desc")->select();

                if($order_commodity_item['act_type'] == 1 || $order_commodity_item['act_type'] == 3){
                    $action_name = $order_commodity_item['act_name'];
                }

                //处理两个一样的sku_code
                foreach($sku_code_arr  as $kk=>$sku_code){
                    $commodity_sum += $commodity_sku_tmp[$sku_code]['price'] ?? 0;
                }



                $data = array(
                    'to_id'=>$order_commodity_item['id']."_".($k +1),
                    'to_order_code'=>$order_commodity_item['order_code'],
                    'to_maintain_group_code'=>$order_commodity_item['sku_code'],
                    'to_maintain_group_amount'=> $order_commodity_item['price'] ?? 0,
                    'to_part_no'=>"",
                    'to_part_name'=>"",
                    'to_part_qty'=>"",
                    'to_part_price'=>"",
//                    'to_part_pre_amount'=>round($order_commodity_item['price'] * $order_commodity_item['count'],2) ,
                    'to_part_pre_amount'=>round($order_commodity_item['price'] * $order_commodity_item['count'],2) ,
                    'to_action_name'=>$action_name,
                    'to_discount'=>round($order_commodity_item['actual_price'] / ($order_commodity_item['price'] * $order_commodity_item['count']), 4),
                    'to_detail_type'=>1,
                    'to_source_type'=> $buOrderObj->getE3sChannel($order_info_index_info['channel']),
                    'to_maintain_group_type'=>$to_maintain_group_type,
                    'to_vehice_mileage'=>$distance,
                    'to_action_discount' => $to_action_discount,
                    'to_commodity_type' => 0,

                );

                $arr[] = $data;
                if($order_commodity_item['work_time_money'] > 0 && !empty($order_commodity_item['work_time_json']) && $order_commodity_item['work_time_json'] != '""'){
                    // var_dump($order_commodity_item['work_time_json']);
                    $ret = workTime($order_commodity_item['work_time_json'],$order_commodity_item);
                    if(!empty($ret)) $arr[] = $ret;
                }
                break;
            }else{
                // 普通商品
                if ($order_commodity_item['mo_sub_id'] == 0) {
                    $count = $order_commodity_item['count'];
                    $to_part_pre_amount =  bcsub(bcmul($order_commodity_item['actual_price'], $order_commodity_item['count'], 2), $order_commodity_item['card_all_dis'], 2);
                    if (in_array($order_commodity_item['subscribe_type'], [1,2])) {
                        $commodity_type = 1;
                    } else {
                        $commodity_type = 2;
                    }
                } else {
                    // 子商品
                    $map = ['order_code'=>$order_commodity_item['order_code'], 'mo_id'=>$order_commodity_item['mo_sub_id']];
                    $zCommodity = $buOrderCommodityObj->where($map)->field('count,subscribe_type')->find();
                    $count = bcmul($zCommodity['count'], $order_commodity_item['count']); // 主商品数量*子商品数量
                    $to_part_pre_amount = bcsub(bcmul($order_commodity_item['actual_price'], $zCommodity['count'], 2), $order_commodity_item['card_all_dis'], 2);

                    if (in_array($zCommodity['subscribe_type'], [1,2])) {
                        $commodity_type = 1;
                    } else {
                        $commodity_type = 2;
                    }
                }


                if ($to_part_pre_amount < 0) {
                    $to_part_pre_amount = 0;
                }

                // 折扣
                $discount = bcdiv($to_part_pre_amount, bcmul($order_commodity_item['price'], $count, 2), 4);

                // 判断单备件还是多备件
                $thirdSkuCodeArr = explode(',',$order_commodity_item['third_sku_code']);
                // 单备件
                if (count($thirdSkuCodeArr) == 1) {

                    // 活动商品
                    if (in_array($order_commodity_item['act_type'], [1,3])) {
                        $action_name = $order_commodity_item['act_name'];
                        if ($order_commodity_item['card_all_dis'] > 0) {
                            $action_name = $action_name.';'.'优惠券金额:'.$order_commodity_item['card_all_dis'];
                        }
                    }


                    $to_detail_type = 0;
                    if($order_commodity_item['dd_commodity_type'] == 11){
                        $to_detail_type = 2;
                        if(!empty($order_commodity_item['third_sku_code'])){
                            $part_time_info = $buE3sPartCartObj->alias("a")->join("t_e3s_spare_part b","a.part_no = b.part_no")->where(['a.wi_code'=>$order_commodity_item['third_sku_code']])->group("a.wi_code")->find();
                            $order_commodity_item['third_sku_name'] = $part_time_info['wi_name'];
                        }
                    }

                    $data = array(
                        'to_id'=>$order_commodity_item['id']."_".($k +1),
                        'to_order_code'=>$order_commodity_item['order_code'],
                        'to_maintain_group_code'=>"",
                        'to_maintain_group_amount'=>0,
                        'to_part_no'=>$order_commodity_item['third_sku_code'],
                        'to_part_name'=>$order_commodity_item['third_sku_name'],
                        'to_part_qty'=>$count, // 备件数量/工时数量
                        'to_part_price'=>$order_commodity_item['price'],
                        'to_part_pre_amount'=> $to_part_pre_amount,
                        'to_action_name'=>$action_name,
                        'to_discount'=> $discount,
                        'to_detail_type'=>$to_detail_type,
                        'to_source_type'=> $buOrderObj->getE3sChannel($order_info_index_info['channel']),
                        'to_maintain_group_type'=>0,
                        'to_vehice_mileage'=>0,
                        'to_action_discount' => $to_action_discount,
                        'to_commodity_type' => $commodity_type,
                    );

                    $arr[] = $data;
                    if($order_commodity_item['work_time_money'] > 0 && !empty($order_commodity_item['work_time_json']) && $order_commodity_item['work_time_json'] != '""'){
                        $ret = workTime($order_commodity_item['work_time_json'],$order_commodity_item);
                        if(!empty($ret)) $arr[] = $ret;
                    }

                }
                // 多备件
                else{
                    $part_pre_amount_sum = 0;
                    $sale_price_arr = E3sSparePart::whereIn('part_no', $thirdSkuCodeArr)->column('sale_price');
                    // 配件总和
                    $sale_price_sum = bcmul(array_sum($sale_price_arr), $count,2);

                    foreach ($thirdSkuCodeArr as $key => $item) {
                        // 备件名称 t_e3s_spare_part
                        $spare_part = E3sSparePart::where('part_no', $item)->field('part_name,sale_price')->find();

                        // 最后一个配件
                        if ($key == (count($thirdSkuCodeArr) -1)) {
                            $sale_price_key = bcsub($to_part_pre_amount, $part_pre_amount_sum, 2);

                        } else {
                            $sale_price = bcmul($spare_part['sale_price'], $count,2); // 配件售价
                            $sale_price_key = bcmul(bcdiv($sale_price,$sale_price_sum,2),$to_part_pre_amount,2);
                            $part_pre_amount_sum = bcadd($part_pre_amount_sum, $sale_price_key,2);
                        }

                        $data = array(
                            'to_id'=>$order_commodity_item['id']."_".($key +1),
                            'to_order_code'=>$order_commodity_item['order_code'],
                            'to_maintain_group_code'=>"",
                            'to_maintain_group_amount'=>0,
                            'to_part_no'=>$item,
                            'to_part_name'=>$spare_part['part_name'],
                            'to_part_qty'=>$count, // 备件数量/工时数量
                            'to_part_price'=>$order_commodity_item['price'],
                            'to_part_pre_amount'=> $sale_price_key,
                            'to_action_name'=>$action_name,
                            'to_discount'=> $discount,
                            'to_detail_type'=>0,
                            'to_source_type'=> $buOrderObj->getE3sChannel($order_info_index_info['channel']),
                            'to_maintain_group_type'=>0,
                            'to_vehice_mileage'=>0,
                            'to_action_discount' => $to_action_discount,
                            'to_commodity_type' => $commodity_type,
                        );
                        $arr[] = $data;
                        if($order_commodity_item['work_time_money'] > 0 && !empty($order_commodity_item['work_time_json']) && $order_commodity_item['work_time_json'] != '""'){
                            $ret = workTime($order_commodity_item['work_time_json'],$order_commodity_item);
                            if(!empty($ret)) $arr[] = $ret;
                        }
                    }
                }

            }

        }
    }
    //echo json_encode($arr);
    $r = $toE3sDetialObj->insertAll($arr);
    if ($r) {
        // 推送联友
        $e3sPostDataService = new E3sPostData();
        $e3sPostDataService->pushOrder([$order_code]);
    }

//    if($r){
//        $request = Request::instance();
//        //$IResponesLogObj = new IResponesLog();
//        // $IResponesLogObj->insertGetId(array('request_id' => 123123, 'module' => 'api', 'action' => 'common', 'function' => 'postOrderRefundToly', 'data' =>5555));
//
//        $url = $request->domain()."/api/order/selectOrder";
//        $ret= Curl::post($url);
//        // $IResponesLogObj->insertGetId(array('request_id' => 111, 'module' => 'api', 'action' => 'common', 'function' => 'postOrderRefundToly', 'data' =>json_encode($ret)));
//    }
}

function discount($is_last_one, $all_price, $money_type, $commodity_sku_price, $sum_product_price, $dis_money)
{
    if ($is_last_one == 1) {
        $discount_price = $all_price - $dis_money[$money_type];
    } else {
        //商品占比
        $sku_discount = round($commodity_sku_price / $sum_product_price, 2);
        //真正优惠
        $discount_price = $all_price * $sku_discount;
    }
    return ['discount_price' => $discount_price];
}

function workTime($work_data,$order_commodity_item){
    $work_data_arr = json_decode($work_data, true);
    if(empty($work_data_arr)) return [];
//    $to_action_name = '';
//    $to_discount = 1;
//    if($work_data_arr['work_time_price'] > 0 && $work_data_arr['work_time_number'] >0 ){
//        $to_discount = round(1 - $order_commodity_item['work_time_dis'] / ( $work_data_arr['work_time_price'] * $work_data_arr['work_time_number'] ), 2);
//    }
//
//    if(!empty($order_commodity_item['full_id'])){
//        $dbFullDiscountObj = new DbFullDiscount();
//        $full_info =  $dbFullDiscountObj->where(array('id'=>$order_commodity_item['full_id']))->whereIn('discount_type',[2,3])->field("activity_title,preferential_money")->find();
//        if(!empty($full_info)) {
//            $to_action_name = $full_info['activity_title'] ;
//        }
//    }
//
//    if(!empty($order_commodity_item['limit_id'])){
//        $dbLimitDiscountObj = new DbLimitDiscount();
//        $limit_info =  $dbLimitDiscountObj->where(array('id'=>$order_commodity_item['limit_id']))->whereIn('discount_type',[2,3])->field("title")->find();
//        if(!empty($limit_info)){
//            $to_action_name = $limit_info['title'];
//        }
//    }

    if($order_commodity_item['mo_sub_id'] ==0){ //普通商品
        $count = bcmul($work_data_arr['work_time_number'], $order_commodity_item['count'], 1);
        $to_part_pre_amount = bcmul($order_commodity_item['work_time_actual_money'], $order_commodity_item['count'], 2);
    }else{ // >0 子商品
        $map = ['order_code'=>$order_commodity_item['order_code'], 'mo_id'=>$order_commodity_item['mo_sub_id']];
        $zCount = BuOrderCommodity::where($map)->value('count');
        $count = bcmul($zCount, bcmul($work_data_arr['work_time_number'], $order_commodity_item['count'], 1),1);
        $to_part_pre_amount = $order_commodity_item['work_time_actual_money'];
    }

    if ($to_part_pre_amount < 0) {
        $to_part_pre_amount = 0;
    }

    $to_action_name = "";
    if(in_array($order_commodity_item['act_type'], [2,3])){
        $to_action_name = $order_commodity_item['act_name'];
    }


    $discount = bcdiv($to_part_pre_amount, bcmul($work_data_arr['work_time_price'], $count, 2), 4);

    // 电商活动折扣
    $to_action_discount = getActivityDiscount($order_commodity_item);
    // N减N折
    if($order_commodity_item['n_dis_id'] != 0) {
        $to_action_discount = bcdiv($order_commodity_item['actual_price'], $order_commodity_item['price'], 2);
    }
    return array(
        'to_id'=>$order_commodity_item['id']."_work_time",
        'to_order_code'=>$order_commodity_item['order_code'],
        'to_maintain_group_code'=>"",
        'to_maintain_group_amount'=>0,
        'to_part_no'=>$work_data_arr['work_time_code'],
        'to_part_name'=>$work_data_arr['work_time_name'],
//        'to_part_qty'=>$work_data_arr['work_time_number'] * $order_commodity_item['count'],
        'to_part_qty'=>$count,
        'to_part_price'=>$work_data_arr['work_time_price'],
        'to_part_pre_amount'=>$to_part_pre_amount,
       // 'to_action_name'=>$to_action_name,
        'to_action_name'=>'商品数量'.$order_commodity_item['count'].'/'.$order_commodity_item['third_sku_code'].';'.$to_action_name,
        'to_discount'=>$discount,
        'to_detail_type'=>2,
        'to_source_type'=>0,
        'to_maintain_group_type'=>0,
        'to_vehice_mileage'=>0,
        'to_action_discount' => $to_action_discount,
        'to_commodity_type' => 0,
    );
}


function activeList($order_info)
{
    $action_name = '';
    $sum_discount_price = 0;
    if(!empty($order_info['full_id'])){ //满减
        $dbFullDiscountObj = new DbFullDiscount();
        $full_info =  $dbFullDiscountObj->where(array('id'=>$order_info['full_id']))->whereIn('discount_type',[1,3])->field("activity_title,preferential_money,discount_type")->find();
        if(!empty($full_info)){
            //   $ret = $this->discount($is_last_one,$order_info['full_dis_money'],'full_money',$commodity_sku_price,$sum_product_price,$dis_money);
            //  $dis_money['full_money'] = $ret['discount_price'];
            $action_name .= $full_info['activity_title']." ";
            //  $sum_discount_price += $ret['discount_price'];

        }
    }

    if(!empty($order_info['limit_id'])){ //限时折扣表
        $dbLimitDiscountObj = new DbLimitDiscount();
        $limit_info =  $dbLimitDiscountObj->where(array('id'=>$order_info['limit_id']))->whereIn('discount_type',[1,3])->field("title,discount_type")->find();
        if(!empty($limit_info)){
            //  $ret = $this->discount($is_last_one, $order_info['limit_dis_money'], 'limit_money', $commodity_sku_price, $sum_product_price, $dis_money);
            //  $dis_money['limit_money'] = $ret['discount_price'];
            $action_name .= $limit_info['title'] . " ";
            //  $sum_discount_price += $ret['discount_price'];
        }
    }

    if(!empty($order_info['n_dis_id'])){ //N件N折
        $dbNDiscountObj = new DbNDiscount();
        $cheap_info  = $dbNDiscountObj->alias("a")->where("a.id = '{$order_info['n_dis_id']}'")->field('a.title')->find();
        // $ret = $this->discount($is_last_one,$order_info['n_dis_money'],'n_money',$commodity_sku_price,$sum_product_price,$dis_money);
        //  $dis_money['n_money'] = $ret['discount_price'];
        $action_name .= $cheap_info['title']. " ";
        //  $sum_discount_price += $ret['discount_price'];
    }

    if(!empty($order_info['suit_id'])){ //套装
        $buSuitObj = new BuCheapSuitIndex();
        $suit_info  = $buSuitObj->alias("a")->where("a.id = '{$order_info['suit_id']}'")->field('a.name')->find();
        //  $ret = $this->discount($is_last_one,$order_info['suit_dis_money'],'suit_money',$commodity_sku_price,$sum_product_price,$dis_money);
        // $dis_money['suit_money'] = $ret['discount_price'];
        $action_name .= $suit_info['name']. " ";
        // $sum_discount_price += $ret['discount_price'];
    }

    if(!empty($order_info['group_id'])){ //团购
        $dbFightGroupObj = new DbFightGroup();
        $acgroup_info = $dbFightGroupObj->alias("a")->where("a.id = '{$order_info['group_id']}'")->field('a.title')->find();
        // $ret = $this->discount($is_last_one,$order_info['group_dis_money'],'group_money',$commodity_sku_price,$sum_product_price,$dis_money);
        // $dis_money['group_money'] = $ret['discount_price'];
        $action_name .= $acgroup_info['title']. " ";
        // $sum_discount_price += $ret['discount_price'];
    }

    if(!empty($order_info['pre_sale_id'])){ //预售
        $dbPreSaleObj = new DbPreSale();
        $pre_info = $dbPreSaleObj->where(['id'=>$order_info['pre_sale_id']])->find();
        // $ret = $this->discount($is_last_one,$order_info['pre_sale_dis_money'],'sale_money',$commodity_sku_price,$sum_product_price,$dis_money);
        //  $dis_money['sale_money'] = $ret['discount_price'];
        $action_name .= $pre_info['title']. " ";
        //  $sum_discount_price += $ret['discount_price'];
    }

    if(!empty($order_info['card_ids'])){ //卡券
        $dbCardObj = new DbCard();
        $card_tmp = [];
        $card_list = $dbCardObj->where("id in ({$order_info['card_ids']}) and card_type < 6 ")->select();
        foreach($card_list as $card_item){
            $card_tmp[$card_item['id']] = $card_item['card_name'];
        }
        $card_arr =  explode(',',$order_info['card_ids']);
        // $card_yh_arr =  explode(',',$order_info['card_yh']);
        //  $card_discount_price = 0;
        foreach($card_arr as $card_k=>$card_item_id){
//                $all_price =  $card_yh_arr[$card_k];
//                if($is_last_one == 1){
//                    $discount_price =  $all_price -  $dis_money[$card_k]['card_money'];
//                }else{
//                    //商品占比
//                    $sku_discount = round($commodity_sku_price  / $sum_product_price ,2);
//                    //真正优惠
//                    $discount_price = $all_price * $sku_discount;
//                    $dis_money[$card_k]['card_money'] = $discount_price;
//                }
            //  $card_discount_price +=$discount_price;
            $action_name .= $card_tmp[$card_item_id]." ";
            //  $sum_discount_price += $discount_price;
        }
    }

//        //真正折扣
//        $discount =  round((($commodity_sku_price - $sum_discount_price) / $commodity_sku_price) ,2) ;
//        if($discount != 1){
//            $discount =  $discount * 100 % 100 / 100;
//        }
    return ['action_name'=>$action_name];
}


/**
 * 获得指定月份月初月末
 * @param $date
 * @return array
 */
function getTheMonth($date)
{
    $firstday = date('Y-m-01', strtotime($date));
    $lastday  = date('Y-m-d 23:59:59', strtotime("$firstday +1 month -1 day"));
    return [$firstday, $lastday];
}

/**
 * 字符串替换
 */
function substr_cut($user_name)
{
    $strlen   = mb_strlen($user_name, 'utf-8');
    $firstStr = mb_substr($user_name, 0, 1, 'utf-8');
    $lastStr  = mb_substr($user_name, -1, 1, 'utf-8');
    if ($strlen > 2) {
        $return = $firstStr . str_repeat("*", mt_rand(1, 3)) . $lastStr;
    } else if ($strlen == 2) {
        $return = $firstStr . str_repeat('*', mb_strlen($user_name, 'utf-8') - 1);
    } else {
        $return = $user_name;
    }
    return $return;
}

/**
 * 打印函数
 */
function dd($params)
{
    dump($params);
    die();
}


function pd($params)
{
    echo '<pre/>';
    print_r($params);
    die;
}

function containsSpecificPattern($input) {
    // 使用正则表达式来检查字符串是否包含 GWSM, GWAPP, QCSM 或 QCAPP
    $pattern = '/(GWSM|GWAPP|QCSM|QCAPP)/';
    return preg_match($pattern, $input);
}



/**
 * 判断是否是索引数组
 * @param $var
 * @return bool
 */
function isListArray($var) {
    if (!is_array($var)) {
        return false;
    }
    $keys = array_keys($var);
    return $keys === range(0, count($var) - 1);
}

/**
 * @param $data
 * @param $rule
 * @return MessageBag
 */
function judgeEmpty($data, $rule): MessageBag
{
    $messageBag = new MessageBag(JsonBuilder::CODE_SUCCESS,'success');
    foreach ($rule as $item) {
        if (!isset($data[$item]) || ($data[$item] == '')) {
            $messageBag->setCode(JsonBuilder::CODE_ERROR);
            $messageBag->setMessage($item . '不能为空');
            return $messageBag;
        }
    }
    return $messageBag;
}


/**
 * @param $timestamp1
 * @param $timestamp2
 * @return false|string
 */
function time_diff($timestamp1, $timestamp2)
{
    if ($timestamp2 <= $timestamp1) {
        return "00:00:00";
    }
    $seconds = $timestamp2 - $timestamp1;
    if ($seconds > 3600) {
        $hours   = intval($seconds / 3600);
        $minutes = $seconds % 3600;
        $time    = $hours . ":" . gmstrftime('%M:%S', $minutes);
    } else {
        $time = gmstrftime('%H:%M:%S', $seconds);
    }
    return $time;
}


/**
 * 判断第一个数组是否在第二个数组内
 * @param $arr1
 * @param $arr2
 * @return bool
 */
function array_in_array($arr1, $arr2) {
    $flag = true;
    foreach ($arr1 as $item) {
        if (!in_array($item, $arr2)) {
            $flag = false;
            break;
        }
    }
    return $flag;
}
/**
 * @param string $up_down_channel
 * @param string $dlr_code
 * @return string
 */
function getUpDownChannel($up_down_channel = '', $dlr_code = '')
{
    $channel_dlr = trim($up_down_channel . ',' . $dlr_code, ',');
    $channel_arr = explode(',', $channel_dlr);
    foreach ($channel_arr as $k => $one) {
        if (empty($one)) {
            unset($channel_arr[$k]);
        }
    }
    $channel_dlr_arr = array_unique($channel_arr);
    return implode(',', $channel_dlr_arr);
}

function getCommTypeIdInfo($comm_type_id_str = '')
{
    $key        = 'comm_type_key' . $comm_type_id_str;
    $comm_cache = redis($key);
    if (!empty($comm_cache)) {
        return $comm_cache;
    }

    $comm_type_arr_info = explode(',', $comm_type_id_str);
    $comm_type_arr      = DbCommodityType::where('is_enable', 1)->column('id,comm_type_name', 'id');
    $arr                = collection($comm_type_arr)->toArray();
    $return             = '';
    foreach ($comm_type_arr_info as $comm_id) {
        if (empty($return)) {
            $return = empty($arr[$comm_id]) ? '' : $arr[$comm_id];
        } else {
            $return = $return . ' > ' . empty($arr[$comm_id]) ? '' : $arr[$comm_id];
        }
    }

    redis($key, $return, mt_rand(600, 1200));
    return $return;
}

function nissanEncrypt($input)
{
    $default = md5(config('nissan_en_key'));

    $encrypt = openssl_encrypt(
        $input,
        'aes-128-cbc',
        substr(md5(hash('sha256', $default, true)), -16),
        OPENSSL_RAW_DATA,
        substr($default, -16)
    );
    return base64_encode($encrypt);
}

function nissanDecrypt($input)
{
    $default   = md5(config('nissan_en_key'));
    $input     = str_replace(' ', '+', $input);
    $encrypted = base64_decode($input);
    return openssl_decrypt(
        $encrypted,
        'aes-128-cbc',
        substr(md5(hash('sha256', $default, true)), -16),
        OPENSSL_RAW_DATA,
        substr($default, -16)
    );
}

if (!function_exists('mb_rtrim')) {
    /**
     * @param $string
     * @param $trim
     * @param $encoding
     * @return string
     */
    function mb_rtrim($string, $trim, $encoding)
    {

        $mask       = [];
        $trimLength = mb_strlen($trim, $encoding);
        for ($i = 0; $i < $trimLength; $i++) {
            $item   = mb_substr($trim, $i, 1, $encoding);
            $mask[] = $item;
        }

        $len = mb_strlen($string, $encoding);
        if ($len > 0) {
            $i = $len - 1;
            do {
                $item = mb_substr($string, $i, 1, $encoding);
                if (in_array($item, $mask)) {
                    $len--;
                } else {
                    break;
                }
            } while ($i-- != 0);
        }

        return mb_substr($string, 0, $len, $encoding);
    }
}

if (!function_exists('getCbbToken')) {
    /**
     * @param false $refresh
     * @return false|mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    function getCbbToken($refresh = false)
    {
        $cbb_config = config('port.cbb');
        $key        = 'afm_port_cbb_token' . $cbb_config['client_id'];
        $rc  = redis($key);
        if (empty($rc) || !empty($refresh)) {

            $get_lock = getRedisLock('tmp_port_cbb_token_lock');
            if(empty($get_lock)){
                return false;
            }

            $client     = new Client();
            $rs = $client->request('post', $cbb_config['client']['base_uri'] . 'oauth/token', [
                'query'     => ['grant_type' => 'client_credentials'],
                'auth'      => [$cbb_config['client_id'], $cbb_config['client_secret']],
                'headers' => [
                    'x-feat' => $cbb_config['x-feat']
                ],
                'auth_type' => 'normal'
            ]);

            $results = json_decode($rs->getBody()->getContents(), true);

            DbLog::create([
                'type'         => 'port_cbb',
                'send_note'    => json_encode([
                    'refresh'   =>  empty($refresh) ? 'manual' : 'cron',
                    'client_id' =>  $cbb_config['client_id'],
                    'url'       =>  $cbb_config['client']['base_uri'],
                ]),
                'receive_note' => json_encode($results),
                'is_success'   => !empty($results['access_token']) ? 'success' : 'fail'
            ]);

            if (empty($results['access_token'])) {
                return false;
            }
            $rc = $results['access_token'];
            redis($key, $rc, 6000);
        }
        return $rc;
    }
}

if (!function_exists('trimall')) {
    function trimall($str){
        $old = [" ","　","\t","\n","\r","\r\n"];
        $new = ["","","","","",""];
        return str_replace($old, $new ,$str);
    }
}

if (!function_exists('passConstruct')) {

    /**
     * 验证构造器
     * @param array $con
     * @param string $method
     * @return bool
     */
    function passConstruct($con = [], $method = '')
    {
        if (empty($con) || empty($method)) {
            return true;
        }

        foreach ($con as $item){
            if (strpos($method, $item) !== false) {
                return false;
            }
        }
        return true;
    }
}

if (!function_exists('leftSeconds')) {

    /**
     * 获得当前分钟内剩余的秒数
     * @return int
     */
    function leftSeconds()
    {
        $cur_sec = intval(date('s'));
        return 60 - $cur_sec;
    }
}


if (!function_exists('get_real_ip')) {
    function get_real_ip()
    {
        $ip = FALSE;
        //客户端IP 或 NONE
        if (!empty($_SERVER["HTTP_CLIENT_IP"])) {
            $ip = $_SERVER["HTTP_CLIENT_IP"];
        }

        //多重代理服务器下的客户端真实IP地址（可能伪造）,如果没有使用代理，此字段为空
        if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ips = explode(",", $_SERVER['HTTP_X_FORWARDED_FOR']);
            if ($ip) {
                array_unshift($ips, $ip);
                $ip = FALSE;
            }
            for ($i = 0; $i < count($ips); $i++) {
                if (!preg_match('/^(10│172.16│192.168)./', $ips[$i])) {
                    $ip = $ips[$i];
                    break;
                }
            }
        }

        //客户端IP 或 (最后一个)代理服务器 IP
        return ($ip ? $ip : $_SERVER['REMOTE_ADDR']);
    }
}

if (!function_exists('getCacheUser')) {
    function getCacheUser($user_id)
    {
        $key = 'user_cache_id' . $user_id;
        $user = redis($key);
        if(empty($user)){
            $user = DbUser::where(['id' => $user_id])->find();
            if (empty($user) || empty($user['plat_id'])) {
                $user = false;
                redis($key , $user, 60);
            } else {
                redis($key , $user, mt_rand(60, 300));
            }
        }

        return $user;
    }
}

if (!function_exists('arraySortOrder')) {
    function arraySortOrder($a, $b)
    {
        if ($a['sort_order'] == $b['sort_order']) {
            return 0;
        }
        return ($a['sort_order'] < $b['sort_order']) ? -1 : 1;
    }
}

if (!function_exists('getCacheBdpVin')) {
    function getCacheBdpVin($vin)
    {
        $dbp_key  = 'bdp_rec_vin' . $vin;
        $data_bdp = redis($dbp_key);
        if (empty($data_bdp)) {
            $data_bdp = (new DbBdpRecommend())->getOne(['where' => ['vin' => $vin]]);
            redis($dbp_key, $data_bdp, mt_rand(30, 60));
        }

        return $data_bdp;
    }
}

if (!function_exists('getCacheAbsMall')) {
    function getCacheAbsMall($vin = '', $member_id = '', $commodity_id = 0)
    {
        $abs_key  = 'abs_rec_vin1' . $vin . $member_id . $commodity_id;
        $data_abs = redis($abs_key);
        if (empty($data_abs)) {
            if (!empty($vin)) {
                $where = ['vin' => $vin];
            }elseif (!empty($member_id)){
                $where = ['member_id' => $member_id];
            }else{
                $where = ['commodity_id' => $commodity_id];
            }
            $data_abs = (new DbAdsMallGoodsRefereeD())->getOne(['where' => $where]);
            if (!empty($data_abs)){
                $data_list = (new DbAdsMallGoodsRefereeD())->where($where)->column('goods');
                $goods = [];
                foreach ($data_list as $v){
                    $goods = array_merge($goods, json_decode($v, true));
                }

                $data_abs['goods'] = json_encode($goods);
            }

            redis($abs_key, $data_abs, mt_rand(300, 600));
        }

        return $data_abs;
    }
}


if (!function_exists('getAbsDataNew')) {
    //数据组新推荐数据列表
    //这块暂时都是vin纬度
    function getAbsDataNew($vin = '', $member_id = '', $commodity_id = 0,$type=1)
    {
//        return [];//暂时不要返回千人千面

        if(!$vin){ //
            return  [];
        }
        $abs_key  = 'abs_data_new_vin' . $vin . $member_id . $commodity_id.$type;
        $data_abs = redis($abs_key);
        if (empty($data_abs)) {
            if (!empty($vin)) {
                $where = ['vin' => $vin];
            }elseif (!empty($member_id)){
                $where = ['member_id' => $member_id];
            }else{
                $where = ['commodity_id' => $commodity_id];
            }
            $where['recommend_type']=$type;
            $model = new \app\common\model\db\DbAdsRecommendedDatas();
            $data_abs = $model->getOne(['where' => $where]);
            if (!empty($data_abs)){
                $data_list = $model->where($where)->column('goods');
                $goods = [];
                foreach ($data_list as $v){
                    $goods_one=json_decode($v, true);
                    foreach ($goods_one as $v_o_v){
                        $goods[]=$v_o_v;
                    }
//                    $goods = array_merge($goods, json_decode($v, true));
                }

                $data_abs['goods'] = $goods;
            }

            redis($abs_key, $data_abs, mt_rand(300, 600));
        }

        return $data_abs;
    }
}

if (!function_exists('inBindVin')) {
    //数据组新推荐数据列表
    //index_in==1 需要刷新
    function inBindVin($nvi_vin, $user,$index_in=0)
    {
        if(!empty($nvi_vin)){
            $redis_name = config('cache_prefix.index_bind_vin') . $user['id'] . $nvi_vin;
            $redis=       redis($redis_name);
            if($redis){
                return  $redis;
            }

            $user_car_info['vin'] = $nvi_vin;
            // 判断vin是否能查到18位码
            $dbCommoditySetSkuObj = new DbCommoditySetSku();
            $code18n = $dbCommoditySetSkuObj->get18Code(['vin' => $nvi_vin]);
            $user_car_info['car_config_code'] = $code18n['car_config_code'];
            $user['18_oil_type']=$user_car_info['18_oil_type'] = $code18n['config_attr'][3] ?? 4;//增加一个vin获取机油信息.
            $user['car_offline_date']=$user_car_info['car_offline_date'] = $code18n['offline_date'];
            $series_model =  new \app\common\model\e3s\E3sCarSeries();
            $series_info =  $series_model->getOne(['where' =>['car_config_code'=>$code18n['car_config_code']]]);
            if($series_info){
                $user['car_series_id'] = $series_info['id'];
            }
            $re_data = ['user'=>$user,'user_car_info'=>$user_car_info];
            //更新默认车
            $net_user = new NetUser();
            $net_user->bindUserCar($user, $nvi_vin,$index_in);
            redis($redis_name,$re_data,30);
            return $re_data;

        }
    }
}



/**
 * 获取13位毫秒数
 */
if (!function_exists('getMicroTime')) {
    function getMicroTime()
    {
        $time_arr= explode(' ', microtime());
        return (float)sprintf('%.0f', (floatval($time_arr[1]) + floatval($time_arr[0])) * 1000);
    }
}


if (!function_exists('download')) {
    function download($path, $filename)
    {
        Header("Content-type: application/octet-stream");
        Header("Accept-Length:filesize($path)");
        Header("Content-Disposition: attachment; filename=$filename");
        $out = fopen('php://output','wb');
        $file = fopen($path,'rb');
        stream_copy_to_stream($file, $out);
        fclose($out);
        fclose($file);
        exit();
    }
}


if (!function_exists('custom_filter')) {
    function custom_filter($value)
    {
        if(is_json($value)){
            return  $value;
            return json_decode($value, true);
        }
        // 使用正则表达式过滤包含 javascript: 协议的链接（不区分大小写）
        $value = preg_replace('/href=["\']\s*javascript:/i', 'href="#"', $value);
//        // 使用正则表达式过滤常见的 JavaScript 关键字（不区分大小写）
//        $value = preg_replace('/\balERT\s*\(/i', 'alert(', $value);
//        $value = preg_replace('/\beVAL\s*\(/i', 'eval(', $value);
//        $value = preg_replace('/\bEXPRession\s*\(/i', 'expression(', $value);
//        $value = preg_replace('/\bON[A-Z][A-Z0-9]+\s*=/i', '', $value);
        return $value;
//        // 使用 htmlspecialchars 进行转义
//        return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');

    }
}
if (!function_exists('is_json')) {
    function is_json($string)
    {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }
}
if (!function_exists('formatNumber')) {
    function formatNumber($number) {
//        if (floor($number) == $number) {
//            // 整数
//            return (int)$number;
//        } elseif (number_format($number, 2) == number_format($number, 1)) {
//            // 小数点后只有一位有效数字
//            return number_format($number, 1);
//        } else {
//            // 显示两位小数
//            return number_format($number, 2);
//        }


        $str = sprintf("%.2f", $number);
        // 去除末尾的零和小数点
        $str = rtrim($str, '0');
        $str = rtrim($str, '.');
        return $str;
    }
}



/**
 * 判断索引数组是否有重复值
 */
if(!function_exists("hasDuplicateValues")) {
    function hasDuplicateValues($array)
    {
        return count($array) !== count(array_unique($array));
    }
}



