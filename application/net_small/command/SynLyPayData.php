<?php


namespace app\net_small\command;


use app\common\command\Base;
use app\common\net_service\PaymentSystem;
use think\Config;

class SynLyPayData extends Base
{

    public static function doIt()
    {
        trace('cron order notice start');
        static::loadConfig();
        Config::load(ROOT_PATH . 'config/net_small/config.php');
        Config::load(ROOT_PATH . 'config/net_small/' . config('app_status') . '.php');
        self::synTransOrderData();
        self::synSettleOrderData();
    }


    /**
     * 同步交易账单
     */
    public static function synTransOrderData()
    {
        $service = new PaymentSystem();
        // 前天和昨天
        $before_yesterday = date('Y-m-d', strtotime("-2 day"));
        $yesterday = date('Y-m-d', strtotime("-1 day"));
        $service->synTransOrderData($before_yesterday);

        $service->synTransOrderData($yesterday);
    }


    /**
     * 同步结算订单
     */
    public static function synSettleOrderData()
    {
        $service = new PaymentSystem();
        $three_days_ago = date('Y-m-d', strtotime("-3 day"));
        $before_yesterday = date('Y-m-d', strtotime("-2 day"));
        $yesterday = date('Y-m-d', strtotime("-1 day"));
        // 2025年8月20日15:43:06 罗骏：结算手续费是 T-3 的
        $service->synSettleOrderData($three_days_ago);
        $service->synSettleOrderData($before_yesterday);
        $service->synSettleOrderData($yesterday);
    }


    /**
     * 每月执行一次
     */
    public static function supplierOrder()
    {
        static::loadConfig();
        $service = new PaymentSystem();
        $service->supplier_order();
    }

}