<?php


namespace app\net_small\command;


use app\common\command\Base;
use app\common\model\bu\BuOrder;
use app\common\model\db\DbDlr;
use app\common\model\db\DbDlrInvoice;
use app\common\model\db\DbOrderInvoice;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbLyPaySettle;
use app\common\model\db\InvoiceApplyModel;
use app\common\net_service\LyPay;
use app\common\net_service\NetSupplier;
use app\common\return_data\JsonBuilder;
use app\common\return_data\MessageBag;
use think\Config;
use think\Exception;
use think\Queue;

class OrderSettle extends Base
{

    public static function doIt()
    {
        trace('cron order notice start');
        static::loadConfig();
        Config::load(ROOT_PATH . 'config/net_small/config.php');
        Config::load(ROOT_PATH . 'config/net_small/' . config('app_status') . '.php');

        self::orderList();
    }

    public static function doIt2($order_code = '')
    {
        trace('cron order notice start');
        static::loadConfig();
        Config::load(ROOT_PATH . 'config/net_small/config.php');
        Config::load(ROOT_PATH . 'config/net_small/' . config('app_status') . '.php');

        self::orderList($order_code);
    }


    // order_status => 7   ms_order_code || ms_order_code2 不为空   完成时间
    public static function orderList($order_code = '')
    {
//        $now  = date('Y-m-d') . ' 00:00:00';
        // 上个月
        $lastMonth = date('Y-m-d', strtotime('-1 month', time()));
        $map       = [
            'last_updated_date' => ['gt', $lastMonth],
            'settlement_state'  => 0,
            'parent_order_type' => ['in', [1, 2]],
            'money'             => ['gt', 0], // 支付现金大于0
            'order_status'      => ['in', [7, 9, 14]],

        ];

        if (!empty($order_code)) {
            $map['order_code'] = $order_code;
        }

        $order_model = new BuOrder();

        $orderIdArr = $order_model->where($map)->where(function ($query) {
            $query->where('ms_order_code', 'neq', '')
                ->whereOr('ms_order_code2', 'neq', '');
        })->column('id');
        self::pageList($orderIdArr);


    }


    public static function pageList($orderIdArr)
    {


        BuOrder::whereIn('id', $orderIdArr)->chunk(1000, function ($list) {
            foreach ($list as $key => $item) {
                // 查询售后
                $map     = [
                    'afs_status' => ['in', [1, 4, 7, 9]],
                    'order_id'   => $item['id']
                ];
                $aftList = DbAfterSaleOrders::where($map)->select();
                // 退款审核的不发起结算
                if (!empty($aftList)) {
                    continue;
                }
                $map     = [
                    'afs_status' => 6,
                    'order_id'   => $item['id']
                ];
                $aftInfo = DbAfterSaleOrders::where($map)->find();
                if (!empty($aftInfo)) {
                    // 判断退款金额是否等于支付金额
                    if ($aftInfo['refund_money'] == bcadd($item['money'], $item['pre_use_money'], 2)) {
                        continue;
                    }
                }

                if (in_array($item['order_status'], [7, 14])) {
                    // 11 车联CCS套餐
                    if ($item['order_source'] == 11) {
                        self::orderType2($item);
                    }
                    // 1:商品订单 2:拼团订单   15 到店备件  16，17，18，19 套餐   20 到店电子券   is_by_tc =2 N延保
                    if (in_array($item['order_source'], [1, 2, 15, 16, 17, 18, 19, 20, 22, 23, 40, 42, 43]) || ($item['is_by_tc'] == 2)) {
                        self::orderType3($item);
                    }
                    //   12 虚拟商品 13 电子卡券
                    if (in_array($item['order_source'], [12, 13, 21, 24])) {
                        self::orderType4($item);
                    }

                } else {
                    // 实物商品
                    self::orderType1($item);

                }
            }
        });


    }


    // 已收货第8天方法
    public static function orderType1($orderInfo)
    {
        // 日产充电桩不用判断收货时间 立即分账
        if ($orderInfo['order_source'] != 42) {
            // 收获时间  receive_time
            $now = date('Y-m-d H:i:s');
            $day = self::diffInDays($now, $orderInfo['receive_time']);
            if ($day < 8) {
                return false;
            }
        }

        // 结算
        self::settle($orderInfo);
    }


    // 充值第8天
    public static function orderType2($orderInfo)
    {
        // 充值时间  recharge_time
        $now = date('Y-m-d H:i:s');
        $day = self::diffInDays($now, $orderInfo['recharge_time']);
        if ($day < 8) {
            return false;
        }

        // 结算
        self::settle($orderInfo);
    }


    // 已完成  7
    public static function orderType3($orderInfo)
    {
        if ($orderInfo['order_status'] != 7) {
            return false;
        }

        // 结算
        self::settle($orderInfo);
    }


    // 已完成第8天
    public static function orderType4($orderInfo)
    {
        // last_updated_date  最后更新时间
        $now = date('Y-m-d H:i:s');
        $day = self::diffInDays($now, $orderInfo['last_updated_date']);
        if ($day < 8) {
            return false;
        }

        // 结算
        self::settle($orderInfo);
    }


    /**
     * 时间差 天
     * @param $bigTime
     * @param $smallTime
     * @return false|float
     */
    public static function diffInDays($bigTime, $smallTime)
    {
        if (empty($smallTime)) {
            return 0;
        }
        return floor((strtotime($bigTime) - strtotime($smallTime)) / 86400);
    }


    /**
     * 结算
     * @param $orderInfo
     */
    public static function settle($orderInfo)
    {

        // 尾款
        if (!empty($orderInfo['ms_order_code'])) {
            self::lySettle($orderInfo['id'], $orderInfo['order_code'], $orderInfo['ms_order_code']);
        }

        // 预付款
        if (!empty($orderInfo['ms_order_code2'])) {
            self::lySettle($orderInfo['id'], $orderInfo['order_code'], $orderInfo['ms_order_code2']);
        }
    }


    /**
     * 结算
     * @param $orderId
     * @param $orderCode
     * @param $msOrderCode
     * @return array
     */
    public static function lySettle($orderId, $orderCode, $msOrderCode)
    {
        $lyPay = new LyPay();
        $re    = getRedisLock('order_settle:' . 'subOrderCode_' . $orderCode . '_msOrderCode_' . $msOrderCode,300);
        if (!$re) {
            return false;
        }

        // 查询是否已发起
        $ly_pay_settle_model = new DbLyPaySettle();

        $where = ['sub_order_code' => $orderCode, 'is_enable' => 1];
        $settle_info = $ly_pay_settle_model->where($where)->find();
        if (!empty($settle_info)) {
            return false;
        }

        $data   = ['subOrderCode' => $orderCode, 'msOrderCode' => $msOrderCode];
        $return = $lyPay->settle($data);
        $result = [];
        $add    = [
            'sub_order_code' => $orderCode,
            'ms_order_code'  => $msOrderCode,
            'creator'        => 'crond'
        ];
        if ($return->isSuccess()) {
            // 添加结算记录
            $result = ['msg' => $return->getMessage(), 'data' => $return->getData()];
            $upd    = ['settlement_state' => 3, 'settlement_time' => date('Y-m-d H:i:s')];
        } else {
            $add['settle_status'] = 'S3';
            $add['is_enable']     = '0';
            $upd                  = ['settlement_state' => 2, 'settlement_time' => date('Y-m-d H:i:s')];
        }
        DbLyPaySettle::insertGetId($add);
        $order_model =  new BuOrder();
        BuOrder::where('id', $orderId)->update($upd);
        $order =  $order_model->getOneByPk($orderId);
        if($order['logistics_mode']==2){
            $net_spplier =  new NetSupplier();
            $net_spplier->updateSupplierOrderToSupplier($order['order_code'],2);
        }

        Queue::push('app\common\queue\PushOrder', json_encode(['order_id' => $orderId]), config('queue_type.push_order'));
        return $result;


    }


}
