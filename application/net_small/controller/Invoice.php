<?php
/**
 * Created by PhpStorm.
 * Date: 2020/12/22
 * Time: 3:11 PM
 */

namespace app\net_small\controller;

use api\wechat\Mail;
use app\admin_v2\service\InvoiceApplyService;
use app\admin_v2\service\InvoiceRecordService;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\db\DbDlr;
use app\common\model\db\DbDlrInvoice;
use app\common\model\db\DbOrderInvoice;
use app\common\model\db\InvoiceApplyDetailModel;
use app\common\model\db\InvoiceApplyModel;
use app\common\model\db\InvoiceRecordModel;
use app\common\net_service\SendMailer;
use ClassesWithParents\G;
use ForkModules\Traits\ResponseTrait;
use app\common\validate\Invoice as InvoiceValid;
use think\Db;


/**
 * @title 开票接口
 * @description 接口说明
 */
class Invoice extends Common
{
    use ResponseTrait;
    private $orderInvoiceObj;
    public function __construct()
    {
        parent::__construct();
        $this->orderInvoiceObj = new DbOrderInvoice();
    }

    /**
     * @title 获取发票抬头信息明细-更新时使用
     * @url /net-small/Invoice/detail
     * @method GET
     *
     */
    public function detail(InvoiceValid $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("detail")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $buOrderObj = new BuOrder();
        $spitOrder = $buOrderObj->where(['id'=>$requestData['order_id'],'parent_order_type'=>2])->find();

        if(!empty($spitOrder)){
            $order = $buOrderObj->where(['order_code'=>$spitOrder['parent_order_code']])->find();
            $requestData['order_id'] = $order['id'];
        }
        $invoiceInfo = $this->orderInvoiceObj->where(['order_id'=>$requestData['order_id']])->find();
        if(empty($invoiceInfo)){
            $invoiceInfo['order_id'] = $requestData['order_id'];
            $invoiceInfo['id'] = "";
            $invoiceInfo["order_code"] = "";
            $invoiceInfo["invoice_type"] = "1";
            $invoiceInfo["invoice_header_type"] = "1";
            $invoiceInfo["invoice_header"] = "个人";
            $invoiceInfo["email"] = "";
            $invoiceInfo["mobile"] = "";
            $invoiceInfo["tax_code"] = "";
            $invoiceInfo["register_address"] = "";
            $invoiceInfo["register_mobile"] = "";
            $invoiceInfo["bank"] = "";
            $invoiceInfo["bank_account"] = "";
            $invoiceInfo["invoice_materials_image"] = "";
            $invoiceInfo["is_enable"] = "";
            $invoiceInfo["creator"] = "";
            $invoiceInfo["created_date"] = "";
            $invoiceInfo["modifier"] = "";
            $invoiceInfo["invoice_status"] = "";
            $invoiceInfo["invoice_time"] = "";


        }
//        echo  $this->orderInvoiceObj->getLastSql();
//        dd($invoiceInfo);
        return $this->setResponseData($invoiceInfo)->send();
    }

    /**
     * @title 修改发票抬头
     * @url /net-small/Invoice/update-invoice
     * @method Post
     *
     */
    public function updateInvoice(InvoiceValid $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("save-invoice")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $invoiceData = [];
        $invoiceData['order_id'] = $requestData['order_id'];
        $buOrderObj = new BuOrder();
        $orderinfo  =$buOrderObj->where(['id'=>$requestData['order_id']])->find();
        $invoiceData['order_code'] = $orderinfo['order_code'] ?? "";
        $invoiceData['invoice_type'] = $requestData['invoice_type'] ?? "";
        $invoiceData['invoice_header_type'] = $requestData['invoice_header_type'] ?? "";
        $invoiceData['invoice_header'] = $requestData['invoice_header'] ?? "";
        $invoiceData['email'] = $requestData['email'] ?? "";
        $invoiceData['mobile'] = $requestData['mobile'] ?? "";
        $invoiceData['tax_code'] = $requestData['tax_code'] ?? "";
        $invoiceData['register_address'] = $requestData['register_address'] ?? "";
        $invoiceData['register_mobile'] = $requestData['register_mobile'] ?? "";
        $invoiceData['bank'] = $requestData['bank'] ?? "";
        $invoiceData['bank_account'] = $requestData['bank_account'] ?? "";
        $invoiceData['invoice_materials_image'] = $requestData['invoice_materials_image'] ?? "";
        if(empty($invoiceData['email'])){
            return $this->setResponseError("邮箱必填")->send();
        }
        if($invoiceData['invoice_type'] == 2){
            if(empty($invoiceData['register_address'])){
                return $this->setResponseError("注册地址必填")->send();
            }
            if(empty($invoiceData['register_mobile'])){
                return $this->setResponseError("注册电话必填")->send();
            }
            if(empty($invoiceData['bank'])){
                return $this->setResponseError("开户银行必填")->send();
            }
            if(empty($invoiceData['bank_account'])){
                return $this->setResponseError("银行账号必填")->send();
            }
            if(empty($invoiceData['invoice_materials_image'])){
                return $this->setResponseError("专票申请资料必填")->send();
            }
        }

        $existOrderInvoice = $this->orderInvoiceObj->where(['order_id'=>$requestData['order_id']])->find();
        if(empty($existOrderInvoice)){
            $this->orderInvoiceObj->insertGetId($invoiceData);
        }else{
            $this->orderInvoiceObj->where(['order_id'=>$requestData['order_id']])->update($invoiceData);
        }


        $orderInfo = $buOrderObj->alias("a")
                    ->join("t_db_tax_invoice_apply_detail b","a.order_code=b.good_order_no")
                    ->join("t_db_tax_invoice_record c","b.apply_id = c.apply_id","left")->where(['a.id'=>$requestData['order_id']])->field("a.settlement_state,c.id as record_id")->find();

        if(empty($orderInfo['record_id'])){//没有开过票的并已分帐要开票
            if($orderInfo['settlement_state'] == 1){
                $intance = InvoiceApplyService::getInstance()->createInvoice($invoiceData);
                if ($intance['result'] == null) {
                    return $this->setResponseError($intance['message'])->send();
                }
            }
        }else{//开过票的要换开
            $intance = InvoiceApplyService::getInstance()->createInvoice($invoiceData);
            if ($intance['result'] == null) {
                return $this->setResponseError($intance['message'])->send();
            }
        }

        return $this->setResponseData("操作成功")->send();
    }

    /**
     * @title 专用发票确认书
     * @url /net-small/Invoice/desc
     * @method Get
     *
     */
    public function desc(){
        $data['content']="<p style=\"text-align:left;text-indent:2em;\">      根据国家税法及发票管理相关规定，任何单位和个人不得要求他人为自己开具与实际经营业务情况不符的专用发票[包括并不限于(1)在没有货物采购或者没有接受应税劳务的情况下要求他人为自己开具专用发票;(2)虽有货物采购或者接受应税劳务但要求他人为自己开具数量或金额与实际情况不符的专用发票]，否则属于“虚开专用发票”。</p ><p style=\"text-align:left;text-indent:2em;\"><br/></p ><p style=\"text-align:left;text-indent:2em;\">  我已充分了解上述各项相关国家税法和发票管理规定，并确认仅就我司实际购买商品或服务索取发票。如我司未按国家相关规定申请开具或使用专用发票，由我司自行承担相应法律后果。</p ><p></p >";
        return $this->setResponseData($data)->send();
    }

    /**
     * 创建开票/保存发票明细
     * @param InvoiceValid $validate
     * @return \think\Response
     *  @url /net-small/Invoice/save-invoice
     */
    public function saveInvoice(InvoiceValid $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("save-invoice")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $invoiceData['order_id'] = $requestData['order_id'];
        $orderObj = new BuOrder();
        $orderinfo =$orderObj->where(['id'=>$requestData['order_id']])->find();
        $invoiceData['order_code'] = $orderinfo['order_code'];
        $invoiceData['invoice_type'] = $requestData['invoice_type'] ?? "";
        $invoiceData['invoice_header_type'] = $requestData['invoice_header_type'] ?? "";
        $invoiceData['invoice_header'] = $requestData['invoice_header'] ?? "";
        $invoiceData['email'] = $requestData['email'] ?? "";
        $invoiceData['mobile'] = $requestData['mobile'] ?? "";
        $invoiceData['tax_code'] = $requestData['tax_code'] ?? "";
        $invoiceData['register_address'] = $requestData['register_address'] ?? "";
        $invoiceData['register_mobile'] = $requestData['register_mobile'] ?? "";
        $invoiceData['bank'] = $requestData['bank'] ?? "";
        $invoiceData['bank_account'] = $requestData['bank_account'] ?? "";
        $invoiceData['invoice_materials_image'] = $requestData['invoice_materials_image'] ?? "";
        $result =  $this->orderInvoiceObj->where(['order_id'=>$requestData['order_id'],'is_enable'=>1])->find();
        if(!empty($result)){
            $this->orderInvoiceObj->where(['id'=>$result['id']])->update($invoiceData);
        }else{
            $this->orderInvoiceObj->insertGetId($invoiceData);
        }

        $invoiceData['dlr_code'] = $requestData['dlr_code'] ?? "";//专营店编码
        $intance = InvoiceApplyModel::createInvoice($invoiceData);
        if ($intance['result'] == null) {
            return $this->setResponseError($intance['message'])->send();
        }
        return $this->setResponseData("操作成功")->send();
    }

    public function getMainOrderId($orderId){
        $orderObj = new BuOrder();
        $spitOrder = $orderObj->where(['id'=>$orderId,'parent_order_type'=>2])->find();
        if(!empty($spitOrder)){
            $suborder = $orderObj->where(['order_code'=>$spitOrder['parent_order_code']])->find();
            $data['order_id'] = $suborder['id'];
            $data['order_code'] = $suborder['order_code'];
        }else{
            $order = $orderObj->where(['id'=>$orderId])->find();
            $data['order_id'] = $order['id'];
            $data['order_code'] = $order['code'];
        }
        return $data;
    }

    /**
     * 发票详情
     * @param InvoiceValid $validate
     * @return \think\Response|void
     * @url /net-small/Invoice/get-invoice-info
     */
    public function getInvoiceInfo(InvoiceValid $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("invoice-info")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $orderObj = new BuOrder();
       // $mainOrder = $orderObj->getMainOrderId($requestData['order_id']);//拆单的拿主单
       // $requestData['order_id'] = $mainOrder['order_id'];

        $invoiceInfo =$orderObj->alias("a")
                                ->join("t_db_order_invoice b","a.order_code=b.order_code")
                                ->where(['a.id'=>$requestData['order_id']])
                                ->field("a.id,b.email,b.mobile,a.order_code,b.invoice_type,b.tax_code,b.register_address,b.register_mobile,b.bank,b.bank_account")
                                ->find();
//        echo $orderObj->getLastSql();exit;
        $invoiceApplyModel = new InvoiceApplyModel();
        $applyDetailObj = new InvoiceApplyDetailModel();
        $invoiceApplyList = $applyDetailObj->alias("a")
                                           ->join("t_db_tax_invoice_apply b","a.apply_id=b.id")
                                           ->where(['a.good_order_no'=>$invoiceInfo['order_code']])->order("a.id desc")->select();

      //  echo $applyDetailObj->getLastSql();exit;
        $all_apply_id = [];
      //  $invoiceApplyInfo = $invoiceApplyList[0] ?? [];
        $last_apply_id = 0;
        foreach($invoiceApplyList as $invoiceApplyitem){
            $all_apply_id[] = $invoiceApplyitem['apply_id'];
            $last_apply_id = $invoiceApplyList[0]['apply_id'];
        }

        $applyInfo = $invoiceApplyModel->where(['id'=>$last_apply_id])->find();

       // echo $invoiceApplyModel->getLastSql();exit;
     //   dd();
        if($applyInfo['apply_invoice_status'] == 107){//审核不通过
            if($applyInfo['parent_id']){
                $applyInfo = $invoiceApplyModel->where(['id'=>$applyInfo['parent_id']])->find();
            }
        }
       // echo $invoiceApplyModel->getLastSql();exit;
        $invoiceInfo['payeer'] = $applyInfo['seller_payee'];
        $invoiceInfo['invoicer'] = $applyInfo['seller_clerker'];
        $invoiceInfo['checker'] = $applyInfo['seller_checker'];
       // dd($invoiceApplyInfo);
        $invoiceInfo['invoice_header'] = $applyInfo['buyer_name'];
        $invoiceInfo['email'] =  $applyInfo['notify_email'];
        $invoiceInfo['mobile'] =  $applyInfo['notify_phone'];
      //  echo $invoiceApplyModel->getLastSql();exit;

        $isRepetitive = $applyInfo['is_repetitive'] ?? 0;
        $relation_good_order_no = $applyInfo['relation_good_order_no'];
        $relation_good_order_no_arr = array_unique(explode(',', $relation_good_order_no));
        $invoiceInfo['order_count'] = count($relation_good_order_no_arr);

        $taxRecordObj = new InvoiceRecordModel();
        $recordInfo = $invoiceApplyModel->alias("a")
                                        ->join("t_db_tax_invoice_record b","a.id = b.apply_id","left")
                                      //  ->where('a.apply_invoice_status','<>',"100")
                                        ->where(['a.id'=>$applyInfo['id']])
                                        ->field("a.invoice_type,b.id as record_id,b.pdf_url,b.image_url,a.public_message,a.apply_invoice_status,a.id as apply_id,a.created_date,b.fault_message,b.invoice_status,a.tax_amount,count(a.id) as invoice_count")
                                        ->group("a.id")->find();

        if($applyInfo['invoice_type'] == 2){//专票
            if($applyInfo['apply_invoice_status'] != 104 ){
                if($isRepetitive == 1 && $applyInfo['apply_invoice_status'] == 101){//换开待审
                    $invoiceInfo['invoice_status'] = 1011;
                    $invoiceInfo['message'] = $recordInfo['public_message'];
                }else{
                    $invoiceInfo['invoice_status'] = $recordInfo['apply_invoice_status'];
                    if($invoiceInfo['invoice_status'] == 107){
                        $haveRecordInfo = $taxRecordObj->whereIn('apply_id',$all_apply_id)->select();//开过票的驳回叫开票成功，没开过票的驳回叫开票驳回
                      // echo $taxRecordObj->getLastSql();exit;
                        if(!empty($haveRecordInfo)){
                            $invoiceInfo['invoice_status'] = 1004;
                        }
                    }
                    if($applyInfo['apply_invoice_status'] == 100 ){
                        $invoiceInfo['invoice_status'] = 1014;//充红中
                    }
                    $invoiceInfo['message'] = $recordInfo['public_message'];
                }
            }else{
                $invoiceInfo['invoice_status'] = InvoiceRecordService::getInstance()->getInvoiceStatusByStatus($recordInfo['invoice_status']);
                $invoiceInfo['message'] = $recordInfo['fault_message'];
            }
        }else{//普票
            if($applyInfo['apply_invoice_status'] == 100 ){
                $invoiceInfo['invoice_status'] = 1014;//充红中
            }else{
                $invoiceInfo['invoice_status'] = InvoiceRecordService::getInstance()->getInvoiceStatusByStatus($recordInfo['invoice_status']);
            }

            $invoiceInfo['message'] = $recordInfo['fault_message'];
        }

        if($invoiceInfo['invoice_status'] == 1007){//开票失败，如果有已充红的票那就显示已充红状态
            $redInvoice = $taxRecordObj->whereIn('apply_id',$all_apply_id)->where(['is_red_invoice'=>1])->order('id desc')->find();

            if(!empty($redInvoice)){
                $invoiceInfo['invoice_status'] = 1017;
            }
        }
        if($invoiceInfo['message'] == '""') $invoiceInfo['message'] = "";//处理陈汉的表
        $invoiceInfo['record_id'] = $recordInfo['record_id'];
        $invoiceInfo['invoice_type'] = $recordInfo['invoice_type'];
        $invoiceInfo['created_date'] = date("Y-m-d",strtotime($recordInfo['created_date']));
        $invoiceInfo['apply_id'] = $applyInfo['id'];
        $invoiceInfo['tax_amount'] = $applyInfo['tax_amount'];
        $invoiceInfo['invoice_count'] = $recordInfo['invoice_count'];
        $invoiceInfo['pdf_url'] = $recordInfo['pdf_url'];
        $invoiceInfo['image_url'] = $recordInfo['image_url'];

//        $applyInfo = $invoiceApplyModel->where(['id'=>$invoiceInfo['apply_id'],'is_enable'=>1])->find();
        //如果有两张蓝票时也不让换开
        $invoiceInfo['blue_invoice_count'] = $taxRecordObj->where(['invoice_data_status'=>2])->whereIn('apply_id',$all_apply_id)->count();

        // 换开次数不能超过3次
        $apply_model = new InvoiceApplyModel();
        $where = [
            'relation_good_order_no' =>$invoiceInfo['order_code'],
            'apply_invoice_status' => 104,
        ];
        $num = $apply_model->where($where)->count();
        if ($num >= 4) {
            $isRep = 1;
        } else{
            $isRep = 0;
        }
        $invoiceInfo['is_repetitive'] = $isRep;
        $invoiceInfo['buyer_materials_image'] = $applyInfo['buyer_materials_image'];
        if($invoiceInfo['buyer_materials_image'] == '[""]') $invoiceInfo['buyer_materials_image'] = [];
        $invoiceInfo['tax_code'] = $applyInfo['buyer_tax_num'];
        $invoiceInfo['register_address'] = $applyInfo['buyer_address'];
        $invoiceInfo['register_mobile'] = $applyInfo['buyer_phone'];
        $invoiceInfo['bank'] = $applyInfo['buyer_bank'];
        $invoiceInfo['bank_account'] = $applyInfo['buyer_account'];
        return $this->setResponseData($invoiceInfo)->send();
    }

    /**
     * 发票订单商品列表
     * @param InvoiceValid $validate
     * @return \think\Response|void
     * @url /net-small/Invoice/invoice-order-commoidty-list
     */
    public function invoiceOrderCommodityList(InvoiceValid $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("invoice-order-commoidty-list")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $applyDetailObj = new InvoiceApplyDetailModel();
        $buOrderCommodity = new BuOrderCommodity();
        $ApplyDeatilList = $applyDetailObj->alias("a")->join("t_bu_order b","a.good_order_no=b.order_code")->where(['a.apply_id'=>$requestData['apply_id'],'a.good_row_type'=>['in',[1,2]]])
                                                            ->group("a.good_order_no")->field("a.tax_amount,a.created_date,a.good_order_no,b.id as order_id")->select();
     // echo $applyDetailObj->getLastSql();exit;
        foreach($ApplyDeatilList as $k=>$ApplyDeatilItem){
            $ApplyDeatilList[$k]['commodity_list'] =  $buOrderCommodity->where(['order_code'=>$ApplyDeatilItem['good_order_no']])->field("commodity_name,commodity_pic,commodity_id")->select();
        }
        return $this->setResponseData($ApplyDeatilList)->send();
    }



    /**
     * 发送到邮箱
     * @param InvoiceValid $validate
     * @return \think\Response|void
     * @url /net-small/Invoice/send-email
     */
    public function sendEmail(InvoiceValid $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("send-email")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        //申请退款邮件
        $SendMailer = new SendMailer();
        $SendMailer->send_mail(['type'=>14,'apply_id'=>$requestData['apply_id'],'email'=>$requestData['email']]);
        return $this->setResponseData("加入队列成功")->send();
    }

    /**
     * 查看示例
     * @param InvoiceValid $validate
     * @return \think\Response|void
     * @url /net-small/Invoice/get-example
     */
    public function getExample(InvoiceValid $validate){
        $dbDlrObj = new DbDlr();
        $info = $dbDlrObj->alias("a")->join("t_db_dlr_invoice b","a.id=b.dlr_id","left")->where(['a.dlr_code'=>'GWSM','a.is_enable'=>1])->field('b.materials_image')->find();
        $materials_image = [];
        if(!empty($info['materials_image'])){
            $materials_image = json_decode($info['materials_image'],true);
        }
        return $this->setResponseData($materials_image)->send();
    }

    /**
     * 取消换开
     * @param InvoiceValid $validate
     * @return \think\Response|void
     * @url /net-small/Invoice/cancle-invoice
     */
    public function cancleInvoice(InvoiceValid $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("cancle-invoice")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $taxRecordObj = new InvoiceRecordModel();
        $message = "";
        $applyModel =new InvoiceApplyModel();
        $applyinfo = $applyModel->where(['id'=>$requestData['apply_id']])->find();
        $recordInfo = $taxRecordObj->where('apply_id',$applyinfo['parent_id'])->where(['is_red_invoice'=>0])->find();
        $ret = InvoiceRecordService::getInstance()->cancelRedoInvoice($recordInfo['id'],$message);

        if($ret){
            return $this->setResponseData("取消换开成功")->send();
        }else{
            return $this->setResponseError($message)->send();;
        }
    }

    /**
     * 换开
     * @url /net-small/Invoice/record-invoice
     */
    public function redoInvoice(InvoiceValid $validate){

        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("redo-invoice")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $invoiceData['id'] = $requestData['record_id'];
        $invoiceData['invoice_type'] = $requestData['invoice_type'] ?? "";
        $invoiceData['invoice_head_type'] = $requestData['invoice_header_type'] ?? "";
        $invoiceData['buyer_name'] = $requestData['invoice_header'] ?? "";
        $invoiceData['buyer_tax_num'] = $requestData['tax_code'] ?? "";
        $invoiceData['buyer_bank'] = $requestData['bank'] ?? "";
        $invoiceData['buyer_account'] = $requestData['bank_account'] ?? "";
        $invoiceData['buyer_address'] = $requestData['register_address'] ?? "";
        $invoiceData['buyer_phone'] = $requestData['register_mobile'] ?? "";
        $invoiceData['is_deducted'] = $requestData['is_deducted'] ?? 0;
        $invoiceData['notify_phone'] = $requestData['mobile'] ?? "";
        $invoiceData['notify_email'] = $requestData['email'] ?? "";
        $invoice_materials_image = $requestData['invoice_materials_image'] ?? "";
        if(!empty($invoice_materials_image)){
            $invoice_materials_image = json_decode($requestData['invoice_materials_image'],true) ?? "";
        }
        $invoiceData['buyer_materials_image'] = $invoice_materials_image;
        $invoiceRecordObj = new InvoiceRecordModel();
        $message = "";
        $result = InvoiceRecordService::getInstance()->redoInvoice($invoiceData,$message);

        if (!$result) {
            return $this->setResponseError($message)->send();
        }
        return $this->setResponseData("操作成功")->send();

    }

    /**
     * 重开
     * @param InvoiceValid $validate
     * @return \think\Response
     */
    public function reTryInvoice(InvoiceValid $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("redo-invoice")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $invoiceRecord  = new InvoiceRecordModel();
        $data['id'] = $requestData['record_id'] ?? "";

        $data['notify_phone'] = $requestData['mobile'] ?? ""; //通知手机号
        $data['notify_email'] = $requestData['email'] ?? ""; //通知邮箱
        $data['invoice_head_type'] = $requestData['invoice_header_type'] ?? "";; //发票抬头类型 1个人2公司
        $data['invoice_type'] = $requestData['invoice_type'] ?? ""; //发票类型:1普票2专票
        $data['buyer_name'] = $requestData['invoice_header'] ?? "";
        $data['buyer_tax_num'] = $requestData['tax_code'] ?? "";
        $data['buyer_bank'] = $requestData['bank'] ?? "";
        $data['buyer_account'] = $requestData['bank_account'] ?? "";
        $data['buyer_address'] = $requestData['register_address'] ?? "";
        $data['buyer_phone'] = $requestData['register_mobile'] ?? "";
        $data['buyer_materials_image'] = $requestData['buyer_materials_image'] ?? "";
        $data['dealer_code'] = $requestData['dlr_code'] ?? '';//专营店编码
        $data['company_invoice_type'] = '1';//公司开票类型
        $data['apply_way'] = InvoiceApplyModel::APPLY_WAY_CLIENT; //申请方式
        $data['is_repetitive'] = $requestData['is_repetitive'] ?? 0; //是否换开
        $data['parent_id'] = $requestData['parent_id'] ?? 0; //换开上一个申请 ID
        $data['merge_invoice'] = $requestData['merge_invoice'] ?? 0; //是否合并发票:0-否 1-是
        $data['invoice_materials_image'] = $requestData['invoice_materials_image'] ?? "";

        if($data['invoice_type'] == 2){
            if(empty($data['buyer_address'])){
                return $this->setResponseError("注册地址必填")->send();
            }
            if(empty($data['buyer_phone'])){
                return $this->setResponseError("注册电话必填")->send();
            }
            if(empty($data['buyer_bank'])){
                return $this->setResponseError("开户银行必填")->send();
            }
            if(empty($data['buyer_account'])){
                return $this->setResponseError("银行账号")->send();
            }
            if(empty($data['invoice_materials_image'])){
                return $this->setResponseError("专票申请资料")->send();
            }
        }

        $dbDlrInvoiceobj = new DbDlrInvoice();
        $dlrCode = $requestData['dlr_code'] ?? "";
        if (empty($dlrCode) || $dlrCode == "GWAPP" || $dlrCode == "QCSM" ||  $dlrCode = "QCAPP") {
            $dlrCode = "GWSM";
        }

        $dlrInfo = $dbDlrInvoiceobj->alias("a")->join("t_db_dlr b", "b.id=a.dlr_id")->where(['b.dlr_code' => $dlrCode, "b.is_enable" => 1])->field("a.*")->find();

        if (!empty($dlrInfo)) {
            $data['seller_name'] = $dlrInfo['dlr_name'];
            $data['seller_tax_num'] = $dlrInfo['tax_num'];
            $data['seller_address'] = $dlrInfo['register_address'];
            $data['seller_bank'] = $dlrInfo['bank'];
            $data['seller_phone'] = $dlrInfo['register_mobile'];
            $data['seller_account'] = $dlrInfo['bank_account'];
        }

        $buOrderObj = new BuOrder();
        $orderInfo = $buOrderObj->where(['id' => $requestData['order_id']])->find();

        $data['details'] = (new BuOrderCommodity())->getInvoiceDetailByOrderCode($orderInfo['order_code']);

        $array = InvoiceRecordService::getInstance()->reTryInvoice($data);
        if (!isset($array['code'])) {
            return $this->setResponseError($array['message'] ?? '未知错误，请重试')->send();
        }
        if ($array['code'] != 200) {
            return $this->setResponseError($array['message'] ?? '未知错误，请重试')->send();
        }
        return $this->setResponseData("操作成功")->send();
    }

}
