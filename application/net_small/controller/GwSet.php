<?php
/**
 * Created by PhpStorm.
 * Date: 2020/12/22
 * Time: 3:11 PM
 */

namespace app\net_small\controller;

use app\common\model\bu\BuGwFloor;
use app\common\model\bu\BuGwBannerSet;
use app\common\model\bu\BuGwType;
use app\common\model\bu\BuOrder;
use app\common\model\db\DbAdvertisement;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbPopupLog;
use app\common\net_service\NetGoods;
use ForkModules\Traits\ResponseTrait;
use app\common\model\bu\BuGwHotWord;
use app\common\model\bu\BuGwRecommendation;
use app\common\model\bu\BuGwFloorProduct;
use app\common\validate\GwSet as GwSetValid;
use app\common\model\wlz\WlzCrowdsLogs;
use think\Db;


/**
 * @title 官网首页接口
 * @description 接口说明
 */
class GwSet extends Common
{
    use ResponseTrait;

    private $_shelves_type = 5;

    public function __construct()
    {
        parent::__construct();
        if(in_array($this->channel_type, ['PZ1ASM','PZ1AAPP'])){
            $this->_shelves_type = 6;
        }else if(in_array($this->channel_type, ['QCSM','QCAPP'])){
            $this->_shelves_type = 7;
        }
    }

    /**
     * @title 楼层图片
     * @description 接口说明
     * @param name:page type:string require:0 default:1 other: desc:页码
     *
     * @return 200:成功
     * @return data:返回数据@!
     *
     * @data pic:banner sort:楼层排序 floor_id:层楼id pic:宣传图 total:总记录数 per_page:每页条数 current_page:当前页数
     * <AUTHOR>
     * @url /net-small/gwset/product-imgs
     * @method GET
     *
     */
    public function productImg(GwSetValid $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("product_img")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $page        = $requestData['page'] ?? 1;
        $pageSize    = $requestData['pageSize'] ?? 20;
        $key         = config('cache_prefix.gwset_product_imgs') . $page . "_" . $pageSize;
        $return_data = redis($key);
        if (empty($return_data)) {
            $return_data                 = [];
            $buGwFloorObj                = new  BuGwFloor();
            $return_data['total']        = $buGwFloorObj->where(['is_enable' => 1])->count();
            $return_data['data']         = $buGwFloorObj->where(['is_enable' => 1])->field("pic,sort,id as floor_id")->limit(($page - 1) * $pageSize, $pageSize)->order("sort desc")->select();
            $return_data['per_page']     = $pageSize;
            $return_data['current_page'] = $page;
            redis($key, $return_data, mt_rand(3600, 7200));
            $redis = \think\Cache::redisHandler();
            $redis->sadd(config('cache_prefix.gwset_product_imgs'), $key);
        }
        return $this->setResponseData($return_data)->send();
    }


    /**
     * @title 楼层商品
     * @description 接口说明
     * @param name:floor_id type:string require:1 default:1 other: desc:楼层id
     * @param name:page type:string require:1 default:0 other: desc:页码
     *
     *
     * @return 200:成功
     * @return data:返回数据@!
     *
     * @data pic:banner product_sort:商品排序 commodity_id:商品id commodity_set_id:商品上架id commodity_name:商品名 cover_image:商品图片 price:商品价格 total:总记录数 per_page:每页条数 current_page:当前页数
     * <AUTHOR>
     * @url /net-small/gwset/products
     * @method GET
     *
     */
    public function products(GwSetValid $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("products")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $page     = $requestData['page'] ?? 1;
        $pageSize = $requestData['pageSize'] ?? 20;
        $floor_id = $requestData['floor_id'] ?? 1;

        $key         = config('cache_prefix.gwset_products') . $floor_id . "_" . $page . "_" . $pageSize."_".$this->_shelves_type;
        $return_data = redis($key);
        if (empty($return_data)) {
            $BuGwFloorProductObj = new BuGwFloorProduct();
            $data                = $BuGwFloorProductObj->alias("a")
                ->join("t_db_commodity_flat c ", "c.commodity_id=a.commodity_id ")
               // ->join("t_db_commodity_set_sku b ", "b.commodity_id=a.commodity_id and b.commodity_set_id = a.commodity_set_id")
                ->where(['c.shelves_type' => $this->_shelves_type, 'c.is_enable' => 1, 'floor_id' => $floor_id])
                ->group("a.commodity_id")
                ->order("a.sort desc")
                ->field("c.commodity_name,c.cover_image,c.price,a.sort as product_sort,a.commodity_id,a.commodity_set_id")
                ->limit(($page - 1) * $pageSize, $pageSize)->select();
           // echo $BuGwFloorProductObj->getLastSql();exit;
            $return_data['total']        = $BuGwFloorProductObj->alias("a")
                ->join("t_db_commodity_flat c ", "c.commodity_id=a.commodity_id  ")
               // ->join("t_db_commodity_set_sku b ", "b.commodity_id=a.commodity_id and b.commodity_set_id = a.commodity_set_id")
                ->where(['c.shelves_type' => $this->_shelves_type, 'c.is_enable' => 1, 'floor_id' => $floor_id])
                ->group("a.commodity_id")
                ->count();
            $return_data['data']         = $data;
            $return_data['per_page']     = $pageSize;
            $return_data['current_page'] = $page;
            redis($key, $return_data, mt_rand(3600, 7200));
            $redis = \think\Cache::redisHandler();
            $redis->sadd(config('cache_prefix.gwset_products'), $key);
        }
        return $this->setResponseData($return_data)->send();
    }

    /**
     * @title 商品推荐
     * @description 接口说明
     * @param name:page type:string require:0 default:1 other: desc:页码
     * @param name:pageSize type:string require:0 default:20 desc: 默认20
     * @param name:position type int require:0  default:0 desc:1=>'搜索页',2=>'商品详情',3=>'购物车',4=>'我的订单',5=>'订单支付成功页
     *
     * @return 200:成功
     * @return data:返回数据@!
     * @data channel:渠道 position:位置 commodity_type:商品分类 sort:商品排序 commodity_set_id:商品上架id commodity_id:商品id commodity_name:商品名 cover_image:商品图片 final_price:商品价格 price:商品原价 total:总记录数 per_page:每页条数 current_page:当前页数
     * <AUTHOR>
     * @url /net-small/gwset/recommendations
     * @method GET
     *
     */
    public function recommendations(GwSetValid $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("recom")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $page         = $requestData['page'] ?? 1;
        $pageSize     = $requestData['pageSize'] ?? 20;
        $position     = $requestData['position'] ?? 0;
        $commodity_id = in_array($position, [2,10]) ? ($requestData['commodity_id'] ?? 0) : 0;

        if ($page > 1){
            return $this->setResponseData([])->send();
        }

        $buGwRecommendationObj = new BuGwRecommendation();
        $net_goods = new NetGoods();
        if (!empty($position)) {
            $where['a.positions'] = $position;
            if (in_array($position, [2,10]) && empty($commodity_id)) return $this->setResponseData([])->send();

        }
        $position_info = $buGwRecommendationObj->where(['positions' => $position, 'channels' => $this->channel_type,'is_enable'=>1])->order('sort desc')->find();
        if(!$position_info){
            return $this->setResponseData([])->send();
        }
        $where['a.channels'] = $this->channel_type;
        $where['a.is_enable'] = 1;

        $key         = config('cache_prefix.gwset_recommendations') . "_" . $position . "_" . $this->channel_type . "_" . $this->_shelves_type . $commodity_id;
        $return_data = redis($key);
        $return_data = [];
        if (empty($return_data)) {
            $return_data = [
                'total' => 0,
                'per_page' => 0,
                'current_page' => 1,
                'jump_type' => '',
                'jump_url' => '',
            ];

            $commodity_ids = [];
            $rec_sort_list = [];
            if (!empty($commodity_id)) { // 商品详情判断数据组推荐
                $commodity_type_key = 'commodity_type_by' . $commodity_id;
                $commodity_type = redis($commodity_type_key);
                if (empty($commodity_type)){
                    $commodity_type = (new DbCommodityFlat())->alias("b")
                        ->field('e.id')
                        ->join('t_db_commodity_type c', 'c.id=b.comm_type_id', 'left')
                        ->join('t_db_commodity_type d', 'c.comm_parent_id=d.id', 'left')
                        ->join('t_db_commodity_type e', 'd.comm_parent_id=e.id', 'left')
                        ->where(['b.commodity_id' => $commodity_id])
                        ->find();
                    redis($commodity_type_key, $commodity_type, mt_rand(3600, 7200) * 100);
                }

                $commodity_type_id = $commodity_type['id'] ?? 0;
                $where[] = ['exp', "a.commodity_type = $commodity_type_id || a.commodity_type = 0"];

                $position_info_1 = $buGwRecommendationObj->where(['positions' => $position, 'recommend_type' => 1,'is_enable'=>1, ['exp', " commodity_type = $commodity_type_id || commodity_type = 0"], 'channels' => $this->channel_type])->order('sort desc')->find();

                if (!empty($position_info_1)){ // 数据组推荐
                    $position_info = $position_info_1;
                    $data_ads = getCacheAbsMall('', '', $commodity_id);
                    if(!empty($data_ads)){
                        $goods = json_decode($data_ads['goods'], true);
                        if (!empty($goods)) {
                            //取这个是因为 要先匹配再取长度
                            $goods_list = $net_goods->goodsList(['page' => $page, 'pageSize' => $position_info_1['show_count'], 'commodity_ids' => implode(',', array_column($goods, 'commodity_id'))], $this->user, $this->channel_type);
                            $commodity_ids = array_column($goods_list['msg']['data'], 'commodity_id');
                            if ($position_info_1['show_count'] > 0){
                                // 数据组数据长度截取
                                $goods_list['msg']['data'] = array_slice($goods_list['msg']['data'], 0, $position_info_1['show_count']);
                                // 数据组数据长度截取
                                //
                                $commodity_ids = array_slice($commodity_ids, 0, $position_info_1['show_count']);
                            }
                            foreach ($commodity_ids as $v){
                                $rec_sort_list[$v] = $position_info_1;
                            }
                        }
                    }
                }

            }

            $rec_list = $buGwRecommendationObj->alias("a")
                ->join("t_bu_gw_recommendation_product b ", "a.id=b.recommendation_id", 'left')
                ->where($where)
                ->order('a.sort desc,b.sort asc')//改成正序-产品zlq 测试SQJ 运营SWS
                ->field('a.id,b.commodity_id,a.channels,b.sort,a.positions,b.id bid')
                ->select();

            foreach ($rec_list as $v){
                if (!isset($rec_sort_list[$v['commodity_id']])){
                    $rec_sort_list[$v['commodity_id']] = $v;
                }
            }
            //1-数据组推荐 2-后台配置
            if($position_info['show_logic']==1){
                $commodity_ids = array_unique(array_merge($commodity_ids, array_column($rec_list, 'commodity_id')));
            }else{
                $commodity_ids = array_unique(array_merge(array_column($rec_list, 'commodity_id'), $commodity_ids));

            }
            $s_key = array_search($commodity_id, $commodity_ids);
            if ($s_key > -1){
                unset($commodity_ids[$s_key]);
                $commodity_ids = array_values($commodity_ids);
            }
            if (!empty($commodity_ids)) {
                $commodity_ids_str = implode(',', $commodity_ids);
                $goods_list        = $net_goods->goodsList(['page' => $page, 'pageSize' => count($commodity_ids), 'commodity_ids' => $commodity_ids_str, 'order_by' => "field(a.commodity_id, $commodity_ids_str),"], $this->user, $this->channel_type);
            }

            $have_commodity = [];
            if (!empty($goods_list['msg']['data'])) {
                foreach ($commodity_ids as $val) {
                    foreach ($goods_list['msg']['data'] as $v) {
                        if ($v['commodity_id'] != $val || in_array($v['commodity_id'], $have_commodity)){
                            continue;
                        }
                        $point_js = $net_goods->goods_point_js(['commodity_id' => $val,'price'=>$v['final_price'],'channel_type'=>$this->channel_type]);
                        $money_point =$point_js['point'];
                        $money_m =sprintf("%.2f",$point_js['least_money']);
                        $tmp                   = [
                            'id'               => $rec_sort_list[$v['commodity_id']]['id'] ?? 0,
                            'channel'          => $rec_sort_list[$v['commodity_id']]['channels'] ?? '',
                            'position'         => $rec_sort_list[$v['commodity_id']]['positions'] ?? 0,
                            'sort'             => $rec_sort_list[$v['commodity_id']]['sort'] ?? 0,
                            'commodity_set_id' => $v['commodity_set_id'],
                            'commodity_type'   => $v['comm_type_id'],
                            'commodity_id'     => $v['commodity_id'],
                            'commodity_name'   => $v['commodity_name'],
                            'cover_image'      => $v['cover_image'],
                            'price'            => $v['price'],
                            'final_price'      => $v['final_price'],
                            'point'            => $money_point,
                            'final_point'      => $money_point,
                            'least_price'      => $money_m,

                        ];
                        $return_data['data'][] = $tmp;
                        $return_data['total']++;
                        $return_data['per_page']++;
                        $have_commodity[] = $v['commodity_id'];
                    }
                }
            }
            $return_data['jump_type'] = $position_info['jump_type'] ?? '';
            $return_data['jump_url']  = $position_info['jump_url'] ?? '';

            redis($key, $return_data, mt_rand(3600, 7200));
            $redis = \think\Cache::redisHandler();
            $redis->sadd(config('cache_prefix.gwset_recommendations'), $key);
        }
        return $this->setResponseData($return_data)->send();
    }


    /**
     * @title banner图
     * @description 接口说明
     *
     *
     * @return 200:成功
     * @return data:返回数据@!
     * @data pic:bannerpc图片 pic_wap:wap图片 sort:楼层排序 operation_type:1商品2分类3url operation_type_data:对应operation_type的数据
     * <AUTHOR>
     * @url /net-small/gwset/banners
     * @method GET
     *
     */
    public function banners()
    {
        $key  = config('cache_prefix.gwset_banners');
        $data = redis($key);
        if (empty($data)) {
            $buGwBannerObj = new  BuGwBannerSet();
            $data          = $buGwBannerObj->alias("a")
                ->where(['a.is_enable' => 1])
                ->order("a.sort desc")
                ->field("a.pic_wap,a.pic,a.sort,operation_type_data,operation_type")
                ->select();
            redis($key, $data, mt_rand(3600, 7200));
        }
        return $this->setResponseData($data)->send();
    }

    /**
     * @title 分类目录
     * @description 接口说明
     *
     *
     * @return 200:成功
     * @return data:返回数据@!
     * @data type_name:分类名 sort:楼层 commodity_type:商品分类 id:分类id
     * <AUTHOR>
     * @url /net-small/gwset/catalogs
     * @method GET
     *
     */
    public function catalogs()
    {
        $key  = config('cache_prefix.gwset_catalogs');
        $data = redis($key);
        if (empty($data)) {
            $buGwTypeObj = new  BuGwType();
            $data        = $buGwTypeObj->alias("a")
                ->where(['a.is_enable' => 1])
                ->order("a.sort desc")
                ->field("a.type_name,a.sort,commodity_type,id")
                ->select();
            redis($key, $data, mt_rand(3600, 7200));
        }
        return $this->setResponseData($data)->send();
    }

    /**
     * @title 搜索设置
     * @description 接口说明
     *
     *
     * @return 200:成功
     * @return data:返回数据@!
     * @data search_name:搜索名 sort:排序 channel:渠道 url:跳转链接
     * <AUTHOR>
     * @url /net-small/gwset/hots
     * @method GET
     *
     */
    public function hots()
    {
        $key  = config('cache_prefix.gwset_hots') . $this->channel_type;
        $data = redis($key);
        if (empty($data)) {
            $buGwHotWordObj = new BuGwHotWord();
            $data           = $buGwHotWordObj->alias("a")
                ->where(['a.is_enable' => 1, 'a.channels' => $this->channel_type])
                ->order("a.sort desc")
                ->field("search_name,sort,channels as channel,url,set_type")
                ->select();
            foreach ($data as $k => $v) {
                if ($v['set_type'] == 2) {
                    $dbCommodityType = new DbCommodityType();
                    $commodity_type  = $dbCommodityType->getOne(['field' => 'id,level', 'where' => ['id' => $v['url']]]);

                    if ($commodity_type['level'] == 2) {
                        $type_ids        = $dbCommodityType->getColumn(['column' => 'id', 'where' => ['comm_parent_id' => $commodity_type['id']]]);
                        $data[$k]['url'] = implode(',', $type_ids);
                    } elseif ($commodity_type['level'] == 1) {
                        $p_type_ids      = $dbCommodityType->getColumn(['column' => 'id', 'where' => ['comm_parent_id' => $commodity_type['id']]]);
                        $type_ids        = $dbCommodityType->getColumn(['column' => 'id', 'where' => ['comm_parent_id' => ['in', $p_type_ids]]]);
                        $data[$k]['url'] = implode(',', $type_ids);
                    }
                }
            }
            redis($key, $data, mt_rand(3600, 7200));
            $redis = \think\Cache::redisHandler();
            $redis->sadd(config('cache_prefix.gwset_hots'), $key);
        }
        return $this->setResponseData($data)->send();
    }

    /**
     * @title 订单接口
     * @description 接口说明
     *
     * @return 200:成功
     * @return data:返回数据@!
     * @data order_status:状态1:待付款7:待点评11:待安装12:待发货 count:数量
     * <AUTHOR>
     * @url /net-small/gwset/orders
     * @method GET
     *
     */
    public function orders()
    {
        if (!$this->user_id) {
            return $this->setResponseError('请登录!', 401)->send();
        }

        $BuOrderObj = new BuOrder();
        $data       = $BuOrderObj->alias("a")
            ->where(['user_id' => $this->user_id, 'order_status' => ['in', [1, 8]]])
            ->order("a.id desc")
            ->field("a.order_status,count(1) as count")
            ->group("a.order_status")
            ->select();
        //待安装
        $count11 = $BuOrderObj->alias("b")
            ->where("b.user_id = {$this->user_id} and b.logistics_mode =1  and ( b.order_status=11 or ( b.order_status=2 and  b.order_source!=2 and b.is_by_tc<>2)) ")
            ->count();

        //待点评
        $count7 = $BuOrderObj->alias("b")
            ->where("b.user_id = {$this->user_id} and b.order_status in (7,9,16) and b.is_all_comment=0")
            ->count();

        $return_data = ['1' => ['order_status' => 1, 'count' => 0], 7 => ['order_status' => 7, 'count' => 0], '11' => ['order_status' => 11, 'count' => 0], '12' => ['order_status' => 12, 'count' => 0]];
        foreach ($data as $k => $item) {
            if ($item['order_status'] == 1 or $item['order_status'] == 8) {
                $return_data['1']['count'] = $return_data['1']['count'] + $item['count'];
            } else if ($item['order_status'] == 12) {
                $return_data['12']['count'] = $item['count'];
            }
        }

        if ($count11 > 0) {
            $return_data['11']['count'] = $count11;
        }

        if ($count7 > 0) {
            $return_data['7']['count'] = $count7;
        }

        return $this->setResponseData($return_data)->send();
    }

    /**
     * @title 订单消息接口
     * @description 接口说明
     *
     * @return 200:成功
     * @return data:返回数据@!
     * @data message:订单消息
     * <AUTHOR>
     * @url /net-small/gwset/messages
     * @method GET
     *
     */
    public function messages()
    {
        if (!$this->user_id) {
            return $this->setResponseError('请登录!', 401)->send();
        }

        $order_arr   = [4 => '已发货', 5 => '已退款', 9 => '已收货', 13 => '安装中', 19 => '已核销'];
        $BuOrderObj  = new BuOrder();
        $data        = $BuOrderObj->where(['user_id' => $this->user_id, 'order_status' => ['in', [4, 5, 9, 13, 19]]])
            ->field("order_status,order_code")
            ->select();
        $return_data = [];
        foreach ($data as $item) {
            $return_data['message'][] = '您的订单' . $item['order_code'] . $order_arr[$item['order_status']];
        }

        return $this->setResponseData($return_data)->send();
    }

    /**
     * @title 官网首页分类接口
     * @description 接口说明
     *
     * @param name:catalog_id type:int require:1 default:0 other: desc:分类id
     * @param name:price_start type:int require:0 default:0 other: desc:现价开始价
     * @param name:price_end type:int require:0 default:0 other: desc:现价结束价
     * @param name:page type:int require:0 default:0 other: desc:页码
     * @param name:pagesize type:int require:0 default:0 other: desc:数量，默认20条
     *
     * @return 200: 成功
     * @return data:返回数据@!
     * @data total:总条数 per_page:页条数 current_page:当前页数 list:商品列表字段名data@
     * @list commodity_id:商品id commodity_name: comm_type_id:商品名称 cover_image:商品分类id discount_price_range_start:商品封面图 discount_price_range_end:现价开始价格 comm_type_name:现价结束价格
     *
     * <AUTHOR>
     * @url /net-small/gwset/type-product
     * @method GET
     *
     */
//    public function getTypeProduct(GwSetValid $validate)
//    {
//        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
//        $result      = $validate->scene("type_goods")->check($requestData);
//        //校验失败,返回异常
//        if (empty($result)) {
//            return $this->setResponseError($validate->getError())->send();
//        }
//        $catalog_id = $requestData['catalog_id'];
//        $buGwTypeObj = new BuGwType();
//        $buGwTypeInfo =  $buGwTypeObj->where(['id'=>$catalog_id])->find();
//        $where['a.is_enable']    = 1;
//        $where[] = ['exp', " (find_in_set('{$this->channel_type}',a.up_down_channel_dlr)) "];
//
//        $where['b.comm_type_id'] = ['in', implode(',',json_decode($buGwTypeInfo['commodity_type'],true))];
//        if (!empty($requestData['price_start'])) {
//            $where['b.discount_price_range_start'] = ['>=', $requestData['price_start']];
//        }
//        if (!empty($requestData['price_end'])) {
//            $where['b.discount_price_range_end'] = ['<=', $requestData['price_end']];
//        }
//        $query['page'] = $requestData['page'] ?? 1;
//        $params        = [
//            'where'    => $where,
//            'field'    => 'a.commodity_id,b.commodity_name,b.comm_type_id,b.cover_image,b.discount_price_range_start,b.discount_price_range_end,c.comm_type_name',
//            'order'    => '',
//            'pagesize' => $requestData['pageSize'] ?? 20,
//            'query'    => $query,
//            'group'    => 'a.commodity_id',
//            'gw'       => true,
//        ];
//        $db_com_set    = new DbCommoditySet();
//        $list          = $db_com_set->getCommoditySetList($params);
//        if ($list) {
//            return $this->setResponseData($list)->send();
//        } else {
//            return $this->setResponseError('没有符合条件的商品', 403)->send();
//        }
//    }
    public function getTypeProduct(GwSetValid $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("type_goods")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $where['a.is_enable']    = 1;
        $where[] = ['exp', " (find_in_set('{$this->channel_type}',a.up_down_channel_dlr)) "];
        $where['b.comm_type_id'] = ['in', $requestData['comm_type_ids']];
        if (!empty($requestData['price_start'])) {
            $where['a.discount_price_range_start'] = ['>=', $requestData['price_start']];
        }
        if (!empty($requestData['price_end'])) {
            $where['a.discount_price_range_end'] = ['<=', $requestData['price_end']];
        }
        $query['page'] = $requestData['page'] ?? 1;
        $params        = [
            'where'    => $where,
            'field'    => 'a.commodity_id,b.commodity_name,b.comm_type_id,b.cover_image,a.discount_price_range_start,a.discount_price_range_end,c.comm_type_name',
            'order'    => '',
            'pagesize' => $requestData['pageSize'] ?? 20,
            'query'    => $query,
            'group'    => 'a.commodity_id',
            'gw'       => true,
        ];
        $db_com_set    = new DbCommoditySet();
        $list          = $db_com_set->getCommoditySetList($params);
        if ($list) {
            return $this->setResponseData($list)->send();
        } else {
            return $this->setResponseError('没有符合条件的商品', 403)->send();
        }
    }

    /**
     * 首页弹窗
     * */
    public function getPopup(GwSetValid $validate)
    {
        if (!$this->user){
            return $this->setResponseError('请先登录', 403)->send();
        }
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("get_popup")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $position = $requestData['position'] ?? 2;
        $date_now = date('Y-m-d');
        $params   = [
            'where' => [
                'position'   => $position,
                'is_enable'  => 1,
                'date_start' => ['<=', $date_now],
                'date_end'   => ['>=', $date_now],
                ['exp', " (find_in_set('{$this->channel_type}',channels)) "],
            ],
            'order' => 'date_start desc, last_updated_date desc',
            'field' => '*',
        ];

        $db_model    = new DbAdvertisement();
//        $key         = config('cache_prefix.gwset_popup') . $this->channel_type;
//        $data        = redis($key);
        $return_data = [];
//        if ($data && $data['date_end'] >= $date_now) {
//            $return_data = $data;
//        } else {
            $info = $db_model->getOne($params);
            $info_user = new WlzCrowdsLogs();
            if ($info) {
                //屏蔽首页弹框屏蔽app
                if(in_array($this->channel_type,['GWSM','QCSM'])){
//                    if(in_array($this->channel_type,['GWSM','QCSM','GWAPP','QCAPP'])){
                    $user_tag_json = json_decode($info['user_tag_json'],true);
                    if(!empty($user_tag_json)){
                        $info_count = 0;
                        if(in_array($this->channel_type,['GWSM','QCSM'])){
                            $info_count = $info_user->where([
                                'crowd_id'=>array('in',$user_tag_json['user_sm']),
                                'user_id'=>$this->user_id,
                            ])->count();
                            if(empty($user_tag_json['user_sm'])){
                                $info_count = '-1';
                            }
                        }
//                        if(in_array($this->channel_type,['GWAPP','QCAPP'])){
//                            $info_count = $info_user->where([
//                                'crowd_id'=>array('in',$user_tag_json['user_app']),
//                                'user_id'=>$this->user_id,
//                            ])->count();
//                            if(empty($user_tag_json['user_app'])){
//                                $info_count = '-1';
//                            }
//                        }
                        $info                              = $info->toArray();
                        if($info_count !=0 || $info_count == '-1'){
                            $return_data['id']                 = $info['id'];
                            $return_data['advertisement_name'] = $info['advertisement_name'];
                            $return_data['pic']                = $info['pic'];
                            $return_data['date_end']           = $info['date_end'];
                            $set_data                          = json_decode($info['set_data_json'], true);
                            $return_data['popup_data']         = $set_data[$this->channel_type];
                            unset($return_data['popup_data']['set_data_name']);
                            if ($return_data['popup_data']['set_type'] == 2) {
                                $dbCommodityType = new DbCommodityType();
                                $commodity_type  = $dbCommodityType->getOne(['field' => 'id,level', 'where' => ['id' => $return_data['popup_data']['set_data']]]);

                                if ($commodity_type['level'] == 2) {
                                    $type_ids                              = $dbCommodityType->getColumn(['column' => 'id', 'where' => ['comm_parent_id' => $commodity_type['id']]]);
                                    $return_data['popup_data']['set_data'] = implode(',', $type_ids);
                                } elseif ($commodity_type['level'] == 1) {
                                    $p_type_ids                            = $dbCommodityType->getColumn(['column' => 'id', 'where' => ['comm_parent_id' => $commodity_type['id']]]);
                                    $type_ids                              = $dbCommodityType->getColumn(['column' => 'id', 'where' => ['comm_parent_id' => ['in', $p_type_ids]]]);
                                    $return_data['popup_data']['set_data'] = implode(',', $type_ids);
                                }
                            }
                        }
                    }
                }

            }
//            redis($key, $return_data, mt_rand(3600, 7200));
//            $redis = \think\Cache::redisHandler();
//            $redis->sadd(config('cache_prefix.gwset_popup'), $key);
//        }
        if (!empty($return_data)) {
            $popup_log = new DbPopupLog;
            $ck        = $popup_log->getOne(['where' => ['user_id' => $this->user_id, 'advertisement_id' => $return_data['id'], 'channel_type' => $this->channel_type]]);
            if ($ck) {
                $return_data = [];
            }
        }
        return $this->setResponseData($return_data)->send();
    }

    /**
     * 关闭弹窗
     * */
    public function closePopup(GwSetValid $validate){
        if (!$this->user){
            return $this->setResponseError('请先登录', 403)->send();
        }
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("close_popup")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $popup_log = new DbPopupLog;
        $popup_log->insert(['user_id'=>$this->user_id, 'advertisement_id'=>$requestData['popup_id'], 'channel_type'=>$this->channel_type]);
        return $this->setResponseData('ok')->send();
    }


    /**
     * 广告获取
     * */
    public function getAdvert(GwSetValid $validate)
    {
        if (!$this->user){
            return $this->setResponseError('请先登录', 403)->send();
        }
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("get_advert")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $position = $requestData['position'];
        $date_now = date('Y-m-d');
        $params   = [
            'where' => [
                'position'   => $position,
                'is_enable'  => 1,
                'date_start' => ['<=', $date_now],
                'date_end'   => ['>=', $date_now],
                ['exp', " (find_in_set('{$this->channel_type}',channels)) "],
            ],
            'order' => 'sort desc, last_updated_date desc',
            'field' => '*',
        ];

        $db_model    = new DbAdvertisement();
        $return_data = [];
        $list = $db_model->getList($params);
        $return_list = [];
        foreach ($list as $info){
            $return_data['id']                 = $info['id'];
            $return_data['advertisement_name'] = $info['advertisement_name'];
            $return_data['pic']                = $info['pic'];
            $return_data['date_end']           = $info['date_end'];
            $set_data                          = json_decode($info['set_data_json'], true);
            $return_data['popup_data']         = $set_data[$this->channel_type];
            unset($return_data['popup_data']['set_data_name']);
            if ($return_data['popup_data']['set_type'] == 2) {
                $dbCommodityType = new DbCommodityType();
                $commodity_type  = $dbCommodityType->getOne(['field' => 'id,level', 'where' => ['id' => $return_data['popup_data']['set_data']]]);

                if ($commodity_type['level'] == 2) {
                    $type_ids                              = $dbCommodityType->getColumn(['column' => 'id', 'where' => ['comm_parent_id' => $commodity_type['id']]]);
                    $return_data['popup_data']['set_data'] = implode(',', $type_ids);
                } elseif ($commodity_type['level'] == 1) {
                    $p_type_ids                            = $dbCommodityType->getColumn(['column' => 'id', 'where' => ['comm_parent_id' => $commodity_type['id']]]);
                    $type_ids                              = $dbCommodityType->getColumn(['column' => 'id', 'where' => ['comm_parent_id' => ['in', $p_type_ids]]]);
                    $return_data['popup_data']['set_data'] = implode(',', $type_ids);
                }
            }

            if (!empty($return_data)) {
                $return_list[] = $return_data;
            }
        }

        return $this->setResponseData($return_list)->send();
    }
}
