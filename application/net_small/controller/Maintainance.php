<?php
/**
 * Created by PhpStorm.
 * User: hjz
 * Date: 2022/5/16
 * Time: 8:51 AM
 */

namespace app\net_small\controller;
use app\admin_v2\controller\E3sCarSeries;
use app\common\model\act\AcByPrice;
use app\common\model\db\DbBdpRecommendLog;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySku;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbGroup;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbUserCarSeries;
use app\common\model\e3s\E3sPartCarSeries;
use app\common\model\jd\JdJifenEmployessLog;
use app\common\net_service\NetOrder;
use app\common\net_service\NetUser;
use app\common\net_service\NetGoods;
use app\common\port\connectors\Bdp;
use think\Db;
use think\Exception;
use think\exception\HttpResponseException;
use app\common\validate\Maintainance as MaintainanceValidate;
use app\common\model\db\DbBdpRecommend;
use app\common\model\db\DbBdpRecommendAi;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbSpecValue;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbLimitDiscount;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\db\DbCard;
use app\common\net_service\NetCart;
use app\common\model\db\DbDlr;
use app\common\model\db\DbSpec;
use app\common\model\inter\IRequestLog;
use app\common\model\inter\IResponesLog;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSeckillCommodity;
use think\Model;
use tool\Logger;

/***
 *
 * e87356490e9e41269007fdab4d1350fbBDP_REC_IND.commodity.page{"vin": "LGBG22E07BY043065", "mile": 3000000,"isInternetCar": 0}123167989989597fa512af6144bb6
 * e87356490e9e41269007fdab4d1350fbBDP_REC_IND.commodity.page{"vin":"LGBG22E07BY043065","mile":3000000,"isInternetCar":0}123167990203197fa512af6144bb6
 *
 *
 */



class Maintainance extends Common
{
    protected  $dlr_code;
    protected $is_redis;
    public function __construct()
    {
        parent::__construct();
        //false 为开启缓存 true 为关闭缓存
        $this->is_redis = true;
    }

    //要过滤的商品id
    protected $limit_product_list=[1569,5164,5162,5161,5160,5159,5158,5157,5156,3541,3540,3539,3538,1718,1717,1716,1715,1589,1588,1587,1586,1585,1584,1583,1582,1581,1580,1579,1578,1577,1575,1574,1573,1572,1571,1570,5416,5415,5414,5413,4285,4284,4283,4282,4281,4280,4279,4278,4277,4276,4275,4274,4273,4272,4271,4270,4269,4268,4267,4266,4265,4264,4263,4262,4261,4260,4259,4258,4256,4257,5371,5370,5369,5368,5365,5327,5326,5325,5324,5323,5322,5321,5387,5386,5385,5384,5383,5382,5381,5380,5379,5377,5376,5375,5388];

    /**
     * @title 保养推荐-优惠明细
     * @description 接口说明
     *
     * @return 200:成功
     * @return data:返回数据@!
     * <AUTHOR>
     * @url /net-small/maintainance/recommend-discount-detail
     * @method GET
     *
     */
    public function discountDetail(MaintainanceValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("discountDetail")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $sku_id_card_arr = [];
        $net_common_model = new \app\common\net_service\Common();
        $user_car_info = $net_common_model->getFriendBaseInfo($this->user);
        $test_vin = $requestData['test_vin'] ?? '';
        $test_18n = $requestData['test_18n'] ?? '';
        if(!empty($test_vin) && !empty($test_18n)){
            $user_car_info['vin'] = $test_vin;
            $user_car_info['car_config_code'] = $test_18n;
        }

        $default_dlr_code = $user_car_info['dlr_code'] ?? '';
        $default_vin = $user_car_info['vin'] ?? '';

        $requestData['dlr_code'] = $requestData['dlr_code'] ?? "";
        if(!empty($requestData['dlr_code'])) $default_dlr_code = $requestData['dlr_code'];
        if(is_array($requestData['set_sku_data'])){
            $set_sku_id_str = json_encode($requestData['set_sku_data']);
        }else{
            $set_sku_id_str = trim($requestData['set_sku_data']);
        }

        $dbFullDiscountObj = new DbFullDiscount();
        $dbNDiscountObj = new DbNDiscount();
        $dbLimitDiscountObj = new DbLimitDiscount();
        $dbCommoditySetSkuObj = new DbCommoditySetSku();
        $netCartObj = new NetCart();
        $net_goods = new NetGoods();
        $redis_key = md5($set_sku_id_str).$default_dlr_code.$default_vin.'_'.$this->brand;
        $data = redis($redis_key);
        $limit_dis_money = 0;
        $full_dis_money = 0;
        $ndis_dis_money = 0;
        $card_dis_money = 0;
        $work_time_price = 0;
        $all_maintain_dis=0;
        $kills_time_price = 0;
        $all_segment_membership_dis = 0;
        $all_segment_owner_dis = 0;
        //赠品券抵扣的商品金额纳入优惠券字段
        $sum_card_gift_price = 0;

        $dis_set_sku_id_arr = [];
        $disable_card_ids = [];
        $orign_set_sku_id = [];
        $sku_use_card_arr = [];//sku可使用卡券ID
        $disabled_card_sku_ids = [];//不可用券skuid
        if($this->is_redis || empty($data) || getRedisLock($redis_key.'-lock', mt_rand(500, 600))) {
            $set_sku_id_arr = json_decode($set_sku_id_str, true);
            foreach ($set_sku_id_arr as $key => $set_sku_id_item) {
                $orign_set_sku_id[] = $set_sku_id_item['set_sku_id'];
//                $sku_id_card_arr[$set_sku_id_item['set_sku_id']] = $set_sku_id_item['count'];
                $sku_id_card_arr[$set_sku_id_item['set_sku_id']] = ['count'=>$set_sku_id_item['count'],'sku_json'=>''];
                if(isset($set_sku_id_item['sku_json'])){
                    $sku_id_card_arr[$set_sku_id_item['set_sku_id']] = ['count'=>$set_sku_id_item['count'],'sku_json'=>$set_sku_id_item['sku_json']];

                }
            }
//            print_json($set_sku_id_arr);
            if(!empty($set_sku_id_arr)){
                $tmp_sum['full_sum_menoy'] = [];
                $tmp_sum['n_dis_count'] = [];
                $tmp_sum['n_dis_money'] = [];
                $commmodity_list_tmp = [];
                $gift_set_sku_id_arr = [];
                foreach($set_sku_id_arr as $set_sku_id_item){
                    //赠品券优惠券
                    if(isset($set_sku_id_item['gift_card_set_sku_ids']) && !empty($set_sku_id_item['gift_card_set_sku_ids'])){
                        foreach($set_sku_id_item['gift_card_set_sku_ids'] as $gift_item){
                            $gift_set_sku_id_arr[] = $gift_item['set_sku_id'];
                            $card_gift_price = $dbCommoditySetSkuObj->where("id in ({$gift_item['set_sku_id']})")->where(['is_enable'=>1])->field("sum(price) as gift_sum_prive")->select();
                            $item_gift_sum_prive =  $card_gift_price[0]['gift_sum_prive'] ?? 0;
                            $sum_card_gift_price += $item_gift_sum_prive * $gift_item['count'];
                        }
                    }

                    //$full_save_activity 用来判断满减是否同一活动，1为同一活动 2为商品，3为工时
                    $full_save_activity = 0;
                    $orign_set_sku_id[] = $set_sku_id_item['set_sku_id'];
                    $code18n = $user_car_info['car_config_code'];
                    $car_id =  $this->user['car_series_id'];
                    $min_price_info = $dbCommoditySetSkuObj->alias("a")
                        ->join("t_db_commodity commodity","commodity.id=a.commodity_id")
                        ->join("t_db_commodity_sku sku","sku.id=a.commodity_sku_id")
                        ->join("t_db_commodity_flat b","a.commodity_id=b.commodity_id")
                        ->where(['a.stock'=>['gt',0],'a.is_enable'=>1,'b.is_enable'=>1,"a.id"=>$set_sku_id_item['set_sku_id'],'b.shelves_type' => $this->shelves_type])
//                        ->where("(find_in_set('$car_id',sku.relate_car_ids) || sku.relate_car_ids='') ")
                        ->field("sku.sku_code,commodity.work_hour_type,b.commodity_id,a.id as set_sku_id,b.full_dis,b.full_wi_dis,b.limit_dis,b.limit_wi_dis,a.price as set_sku_price,sku.maintain_q maintain_dis,b.is_grouped")->find();
//                    if(!$min_price_info){
//                        print_json($dbCommoditySetSkuObj->getLastSql());
//                        continue;
//                    }
                    $min_price_info['do_dis_price'] = $min_price_info['set_sku_price'];
                    $min_price_info['dis_price'] = 0;
                    $min_price_info['commodity_dis_user_segment'] = 0;
                    $min_price_info['commodity_dis_act_user_segment'] = 0;
                    $min_price_info['commodity_dis_label'] = '';
                    $min_price_info['commodity_dis_label_cn'] = '';

                    if ($min_price_info['is_grouped'] == 1){
                        $min_price_info['set_sku_price'] = $set_sku_id_item['price'];
                    }

                    $min_price_info['old_price'] = $min_price_info['price'] = $min_price_info['set_sku_price'];
                    if($min_price_info['maintain_dis']){
                        $min_price_info['old_price'] = sprintf("%.2f",bcdiv($min_price_info['set_sku_price'],$min_price_info['maintain_dis']/10,0));
                        $all_maintain_dis+=bcsub($min_price_info['old_price']-$min_price_info['set_sku_price'],0);
                    }
                    if( $min_price_info['maintain_dis']==10){
                        $min_price_info['maintain_dis']='';
                    }

                    $use_discount = $set_sku_id_item['use_discount'] ?? 1;
                    if ($use_discount) {
                        $commodity_dis_info = $net_goods->getCommoditySegmentDiscount($min_price_info['commodity_id']);
                        if ($commodity_dis_info) {
                            $min_price_info['set_sku_price'] = $net_goods->getCommodityDisFinalPrice($commodity_dis_info, $min_price_info['price']);
                            $min_price_info['commodity_dis_user_segment'] = $commodity_dis_info['user_segment'];
                            $min_price_info['commodity_dis_label'] = get_user_segment_label($commodity_dis_info['user_segment']);
                            $min_price_info['commodity_dis_label_cn'] = get_user_segment_label_cn($min_price_info['commodity_dis_label']);
                            $commodity_dis_price = $min_price_info['price'] - $min_price_info['set_sku_price'];
                            if ($commodity_dis_info['user_segment'] == 1) {
                                $all_segment_membership_dis += round_bcmul($commodity_dis_price, $set_sku_id_item['count']);
                            } else {
                                $all_segment_owner_dis += round_bcmul($commodity_dis_price, $set_sku_id_item['count']);
                            }
                        }
                    }

                    //限时活动
                    if($set_sku_id_item['type'] == 1){
                        $min_price_info = $dbCommoditySetSkuObj->onlyPproduct($min_price_info,$set_sku_id_item['act_id'],$set_sku_id_item['count']);
//                        print_json($min_price_info);
                        $limit_dis_money += $min_price_info['dis_price'];
                        $min_price_info['do_dis_price'] = $min_price_info['set_sku_price'];
                        $sku_use_card_arr[$set_sku_id_item['set_sku_id']] = isset($min_price_info['rel_card_ids'])?$min_price_info['rel_card_ids']:'';
                    }

                    if($set_sku_id_item['type'] == 2){
                        //仅商品满减
                        if(empty($set_sku_id_item['have_work_time'])){
                            $min_price_info = $dbCommoditySetSkuObj->onlyFullPproduct($tmp_sum,$min_price_info,$set_sku_id_item['act_id'],$set_sku_id_item['count']);
                            $full_dis_money += $min_price_info['dis_price'];
                            $min_price_info['do_dis_price'] = $min_price_info['set_sku_price']-$min_price_info['dis_price'];
                            $sku_use_card_arr[$set_sku_id_item['set_sku_id']] = isset($min_price_info['rel_card_ids'])?$min_price_info['rel_card_ids']:'';
                        }
                        //商品+工时同一活动和不同活动
                        if(!empty($set_sku_id_item['have_work_time'])){
                            $full_wi_dis = json_decode($min_price_info['full_wi_dis'],true);
                            $full_wi_id = $full_wi_dis[$this->channel_type][0] ?? 0;

                            if($set_sku_id_item['act_id'] == $full_wi_id){//同一活动
                                $full_save_activity = 1;
                                $bj_data['code18n'] = $user_car_info['car_config_code'];
                                $bj_data['vin'] = $default_vin;
                                $bj_data['dlr_code'] = $default_dlr_code;
                                $min_price_info = $dbCommoditySetSkuObj->workTimePrice($min_price_info,$bj_data);
                                $min_price_info = $dbCommoditySetSkuObj->onlyFullWorkTime($tmp_sum,$min_price_info,$set_sku_id_item['act_id'],$full_save_activity,$set_sku_id_item['count']);
                                $productfull_money = round($min_price_info['dis_price'] * ($min_price_info['set_sku_price'] / ($min_price_info['set_sku_price'] + $min_price_info['work_time_price'])), 2);
                                $work_time_price += $min_price_info['dis_price'] - $productfull_money;
//                                $limit_dis_money += $productfull_money;//工时优惠
                                // $min_price_info['do_dis_price'] = $productfull_money;
                                $min_price_info['do_dis_price'] = $min_price_info['set_sku_price'] - $productfull_money;
                            }else{//不同活动
                                $min_price_info = $dbCommoditySetSkuObj->onlyFullPproduct($tmp_sum,$min_price_info,$set_sku_id_item['act_id'],$set_sku_id_item['count']);
                                $full_dis_money += $min_price_info['dis_price'];
                                $dis_set_sku_id_arr = $this->productDis($min_price_info,$dis_set_sku_id_arr);
                                $min_price_info['do_dis_price'] = $min_price_info['set_sku_price'] - $min_price_info['dis_price'];
                                $sku_use_card_arr[$set_sku_id_item['set_sku_id']] = isset($min_price_info['rel_card_ids'])?$min_price_info['rel_card_ids']:'';

                            }
                        }
                        $tmp_sum = $min_price_info['tmp_sum'];
                    }

                    //n件
                    if($set_sku_id_item['type'] == 3){
                        $now_date = time();
                        $tmp_sum['n_dis_count'][$set_sku_id_item['act_id']] = $tmp_sum['n_dis_count'][$set_sku_id_item['act_id']] ?? 0;
                        $tmp_sum['n_dis_money'][$set_sku_id_item['act_id']] = $tmp_sum['n_dis_money'][$set_sku_id_item['act_id']] ?? 0;
                        $commmodity_list_tmp['n_dis'][] = $set_sku_id_item['act_id'];
                        $tmp_sum['n_dis_count'][$set_sku_id_item['act_id']] += $set_sku_id_item['count'];
                        $tmp_sum['n_dis_money'][$set_sku_id_item['act_id']] += $min_price_info['set_sku_price'] * $set_sku_id_item['count'] ;
                        $min_price_info['do_dis_price'] = $min_price_info['set_sku_price'];

                    }
                    //工时
                    if($set_sku_id_item['have_work_time'] == 1){
                        //限时工时
                        if(!empty($min_price_info['limit_wi_dis'])){
                            $limit_wi_dis = json_decode($min_price_info['limit_wi_dis'],true);
                            $limit_wi_id = $limit_wi_dis[$this->channel_type][0] ?? 0;
                            if(!empty($limit_wi_id)) {
                                $bj_data['code18n'] = $user_car_info['car_config_code'];
                                $bj_data['vin'] = $default_vin;
                                $bj_data['dlr_code'] = $default_dlr_code;
                                $min_price_info = $dbCommoditySetSkuObj->workTimePrice($min_price_info,$bj_data);
                                $min_price_info = $dbCommoditySetSkuObj->onlyWorkTime($min_price_info,$limit_wi_id,$set_sku_id_item['count']);
                                $work_time_price += $min_price_info['work_time_dis_price'];
                            }
                        }
                        //满减工时
                        if(!empty($min_price_info['full_wi_dis']) && $full_save_activity == 0){
                            $full_wi_dis = json_decode($min_price_info['full_wi_dis'],true);
                            $full_wi_id = $full_wi_dis[$this->channel_type][0] ?? 0;
                            if(!empty($full_wi_id)) {
                                $bj_data['code18n'] = $user_car_info['car_config_code'];
                                $bj_data['vin'] = $default_vin;
                                $bj_data['dlr_code'] = $default_dlr_code;
                                $min_price_info = $dbCommoditySetSkuObj->workTimePrice($min_price_info,$bj_data);
                                $min_price_info = $dbCommoditySetSkuObj->onlyFullWorkTime($tmp_sum,$min_price_info,$full_wi_id,3,$set_sku_id_item['count']);
                                $work_time_price += $min_price_info['dis_price'];
                                $tmp_sum = $min_price_info['tmp_sum'];
                            }
                        }
                    }
                    $dis_set_sku_id_arr = $this->productDis($min_price_info,$dis_set_sku_id_arr);
                }
//                print_json($set_sku_id_item,$commmodity_list_tmp['n_dis']);
                //处理n件
                if(!empty($commmodity_list_tmp['n_dis'])){
                    $commmodity_list_tmp['n_dis'] = array_unique($commmodity_list_tmp['n_dis']);
                    foreach($commmodity_list_tmp['n_dis']  as $nn_v){
                        if(in_array($min_price_info['set_sku_id'],$gift_set_sku_id_arr))  continue;

                        $segment_info = get_user_segment_info();
                        $membership = $segment_info['membership_level'];
                        $owner = $segment_info['brand_owner_label'];

                        $activity_n_list = $dbNDiscountObj->alias("a")->join("t_db_n_discount_info b", "a.id=b.n_id")->whereIn('a.id',$nn_v)->where("unix_timestamp(a.start_time) <='{$now_date}' and '{$now_date}' <= unix_timestamp(a.end_time)")->where(['a.is_enable' => 1])->order("b.piece desc")->field("a.id,a.title,b.piece,b.discount,a.card_available,a.rel_card_ids,a.user_segment,a.user_segment_options,b.segment_label")->select();
                        foreach ($activity_n_list as $item) {
                            if ($item['user_segment']) {
                                $segment_option = explode(',', $item['user_segment_options']);
                                if ($item['user_segment'] == 1) {
                                    $segment_key = $membership;
                                } else {
                                    $segment_key = $owner;
                                }
                                if (!in_array($segment_key, $segment_option) || $item['segment_label'] != $segment_key) {
                                    continue;
                                }
                            }
                            if($tmp_sum['n_dis_count'][$nn_v] >= $item['piece']){
                                $ndis_tmp_money = ($tmp_sum['n_dis_money'][$nn_v] - round($tmp_sum['n_dis_money'][$nn_v] * $item['discount'] / 10,2));
                                $ndis_dis_money += $ndis_tmp_money;
                                $sku_use_card_arr[$set_sku_id_item['set_sku_id']] = $item['rel_card_ids'];
                                $min_price_info['do_dis_price'] = $min_price_info['set_sku_price'] - $ndis_tmp_money;
                                break;
                            }
                        }
                    }
                }
                $dis_set_sku_id_arr[$min_price_info['set_sku_id']] = $min_price_info['set_sku_price'];

            }
            //处理卡券
//            $netCartObj = new NetCart();
//            dd($dis_set_sku_id_arr);
            //卡券优惠还是要计算已领取的卡券
            $ret = $netCartObj->select_card_bset($orign_set_sku_id,$this->user_id ,$this->channel_type,$sku_id_card_arr,$dis_set_sku_id_arr,$disable_card_ids,$disabled_card_sku_ids,$sku_use_card_arr,$default_vin,$default_dlr_code);

            $card_dis_money = $ret['card_yh_all_money'] + $sum_card_gift_price;

            $data = [
                'seckill_dis_money' => $kills_time_price,
                'work_time_price' => round($work_time_price, 2),
                'limit_dis_money' => round($limit_dis_money, 2),
                'full_dis_money' => round($full_dis_money, 2),
                'ndis_dis_money' => round($ndis_dis_money, 2),
                'card_dis_money' => round($card_dis_money, 2),
                'all_maintain_dis' => round($all_maintain_dis, 2),
                'all_segment_membership_dis' => $all_segment_membership_dis,
                'all_segment_owner_dis' => $all_segment_owner_dis,

            ];
//            redis($redis_key, $data, 8640000);
        }
        return $this->setResponseData($data)->send();
    }

    /**
     * 优惠了多少钱
     * @return void
     */
    private function productDis($min_price_info,$dis_set_sku_id_arr){
//        print_json($min_price_info);
        $dis_set_sku_id_arr[$min_price_info['set_sku_id']] = $dis_set_sku_id_arr[$min_price_info['set_sku_id']] ?? 0;
//        var_dump($dis_set_sku_id_arr);

        $dis_price     = $min_price_info['do_dis_price'] ?? 0;
        //有优惠时候取优惠值，lzx 2023-02-03 18:21:53
        if($dis_price){
            $dis_set_sku_id_arr[$min_price_info['set_sku_id']] =  $dis_price;
        }
//        dd($dis_price);
//        $dis_set_sku_id_arr[$min_price_info['set_sku_id']] +=  $dis_price;
        return $dis_set_sku_id_arr;
    }

    /**
     * @title 保养推荐-卡券列表
     * @description 接口说明
     *
     * @return 200:成功
     * @return data:返回数据@!
     * @data carded:套餐名 commodity_type_id:套餐id image:图片 sku_price:原价 set_sku_price:现价 sp_value_name:规格值 sp_name:规格名
     * <AUTHOR>
     * @url /net-small/maintainance/recommend-base
     * @method GET
     *
     */
    public function redommendBaseAi(MaintainanceValidate $validate){
        $print_sql = false;
        $api_start_at = microtime(true);
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("recommendai")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $nvi_vin = $requestData['nvi_vin'] ?? '';
        $dlr_code = $requestData['dlr_code'] ?? '';
        $longitude = $requestData['longitude'] ?? 0;
        $latitude = $requestData['latitude'] ?? 0;
        //echo $nvi_vin;

        $cb_list = [];
        $all_limit_activity=[];
        $product_type = [];
        $activity_commodity_id_arr = [];
        $card_list = [];
        $all_set_sku_id_arr = [];
        $recommend_commodity_dd_commodity_type_arr = [];

        $dbBdpRecommendObj = new DbBdpRecommendAi();
        $dbCommoditySetSkuObj = new DbCommoditySetSku();
        $dbFullDiscountObj = new DbFullDiscount();
        $dbNDiscountObj = new DbNDiscount();
        $dbLimitDiscountObj = new DbLimitDiscount();
        $dbCommodityFlatObj = new DbCommodityFlat();
        $dbCommodityTypeObj = new DbCommodityType();
        $dbseckillObj = new DbSeckill();
        $dbGroupObj = new DbGroup();
        $dbCheapObj = new BuCheapSuitIndex();
        $dbPreSaleObj = new DbPreSale();
        if(!$nvi_vin){
            $nvi_vin = $this->user['vin'];
        }
        $recommend_info = $dbBdpRecommendObj->where(['vin' => $nvi_vin])->find();
        //没有商品数据就提前收工了

        if (empty($recommend_info)) {
            $cb_emp_list['dlr_code'] = '';
            $cb_emp_list['dlr_name'] = '';
            $cb_emp_list['is_vin_car'] = $this->user['user_status'];
            $cb_emp_list['card_list'] =[];
            $cb_emp_list['product_list'] =[];
            $cb_emp_list['ac_list'] =[];
            return $this->setResponseData($cb_emp_list)->send();
        }
        //取出所有商品备件和保养id
        $recommend_list_commodity_id_ori_arr = $recommend_list_commodity_id_arr = json_decode($recommend_info['goods'], true);
        $goods_ids = [];
        if($recommend_list_commodity_id_ori_arr){
            foreach ($recommend_list_commodity_id_ori_arr as $vv){
                if($vv['goods']){
                    foreach ($vv['goods'] as $goods_item) {
                        $goods_ids[]=$goods_item['commodityId'];
                    }
                }
            }
        }
        if($goods_ids){
            $this->user = $this->_goods_group($goods_ids,$this->user,$this->channel_type);
        }


        $net_common_model = new \app\common\net_service\Common();
        $user_car_info = $net_common_model->getFriendBaseInfo($this->user, $longitude, $latitude);
        if(empty($dlr_code)){
            $dlr_code = $user_car_info['dlr_code'];
        }

        $nev_where =  $this->user['nev_where'];
        $dlr_where = $nev_where;
        $dlr_where['dlr_code'] = $dlr_code;

        $dlr_model = new DbDlr();
        $dlr = $dlr_model->getOne(['where' => $dlr_where]);
        if (!$dlr) {
            $dlr_code = '';
        }

        if(!empty($nvi_vin)){
            $user_car_info['vin'] = $nvi_vin;
            // 判断vin是否能查到18位码
            $code18n = $dbCommoditySetSkuObj->get18Code(['vin' => $nvi_vin]);
            $user_car_info['car_config_code'] = $code18n['car_config_code'];
            $this->user['18_oil_type']=$user_car_info['18_oil_type'] = $code18n['config_attr'][3] ?? 4;//增加一个vin获取机油信息.
            $this->user['car_offline_date']=$user_car_info['car_offline_date'] = $code18n['offline_date'];
            $series_model =  new \app\common\model\e3s\E3sCarSeries();
            $series_info =  $series_model->getOne(['where' =>['car_config_code'=>$code18n['car_config_code']]]);
            if($series_info){
                $this->user['car_series_id'] = $series_info['id'];

            }

            //更新默认车
            $net_user = new NetUser();
            $net_user->bindUserCar($this->user, $nvi_vin);

        }
//        if(!empty($nvi_vin)){
//
//        }

        //echo $this->user_id;
        ///dd($user_car_info);
//        $IRequestLogObj = new IRequestLog();
//        $ridd = $IRequestLogObj->insertGetId(array('module'=>'net-small','action'=>'Maintainance','function'=>'redommendBaseAi','data'=>json_encode($requestData).'<====>'.json_encode($user_car_info)));





        //获取分类
        $product_type_list = $dbCommodityTypeObj->where(['is_enable' => 1, 'level' => 2])->field('id,comm_type_name')->select();
        foreach ($product_type_list as $product_type_item) {
            $product_type[$product_type_item['id']] = $product_type_item['comm_type_name'];
        }

        if(!empty($recommend_list_commodity_id_ori_arr)){
            foreach ($recommend_list_commodity_id_arr as $kkn=>$item) {
                if(!empty($item['goods'])) {
                    foreach ($item['goods'] as $goods_item) {

                        if ($goods_item['ddCommodityType'] == 9 || $goods_item['ddCommodityType'] == 7) {
                            //备件
                            $product_bj_id[] = $goods_item['commodityId'];
                            //                   $all_set_sku_id_arr['bj'][$goods_item['commodityId']] = $goods_item['commoditySetId'];
                            if (!empty($goods_item['skuList'])) {
                                foreach ($goods_item['skuList'] as $sku_item) {
                                    $all_set_sku_id_arr['bj'][$goods_item['commodityId']][] = $sku_item['setSkuId'];
                                }
                            }
//                            $recommend_commodity_id_arr[] = $goods_item['commodityId'];
                        }
                        $recommend_commodity_id_arr[] = $goods_item['commodityId'];

                        if ($goods_item['ddCommodityType'] == 1 || $goods_item['ddCommodityType'] == 3 || $goods_item['ddCommodityType'] == 41 || $goods_item['ddCommodityType'] == 4 || $goods_item['ddCommodityType'] == 12) {
                            //心悦老友惠
                            $product_by_id[] = $goods_item['commodityId'];
                            //                    $all_set_sku_id_arr['by_list'][$goods_item['commodityId']] = $goods_item['commoditySetId'];
                            if (!empty($goods_item['skuList'])) {
                                foreach ($goods_item['skuList'] as $sku_item) {
                                    $all_set_sku_id_arr['by_list'][$goods_item['commodityId']][] = $sku_item['setSkuId'];
                                    if($sku_item['isDefault']==1){
                                        $all_set_sku_id_arr['by_list'][$goods_item['commodityId'].'df'][] = $sku_item['setSkuId'];
                                    }else{
                                        $all_set_sku_id_arr['by_list'][$goods_item['commodityId'].'no_df'][] = $sku_item['setSkuId'];
                                    }
                                }
                                $recommend_commodity_id_arr[] = $goods_item['commodityId'];
                            }

                        }
//                        if ($goods_item['ddCommodityType'] == 41 || $goods_item['ddCommodityType'] == 4) {
//                            //五年
//                            $product_by_id[] = $goods_item['commodityId'];
//
//                            //                    $all_set_sku_id_arr['by_list'][$goods_item['commodityId']] = $goods_item['commoditySetId'];
//                            if (!empty($goods_item['skuList'])) {
//                                foreach ($goods_item['skuList'] as $sku_item) {
//                                    $all_set_sku_id_arr['by_list'][$goods_item['commodityId']][] = $sku_item['setSkuId'];
//                                }
//                            }
//                            $recommend_commodity_id_arr[] = $goods_item['commodityId'];
//                        }

                        $recommend_commodity_dd_commodity_type_arr[$goods_item['commodityId']] = $goods_item['ddCommodityType'];
                    }
                }

            }
        }
//        print_json($recommend_commodity_dd_commodity_type_arr);
        //dd($recommend_commodity_dd_commodity_type_arr);
        //没有商品数据就提前收工了
        if(empty($recommend_commodity_id_arr)) {
            return $this->setResponseData(['data'=>$recommend_list_commodity_id_ori_arr])->send();
        }

        $dlr_level='A';
        $dd_dlr_code = $dlr_code;//没有传值就获取默认店，在上面
        if(!empty($dd_dlr_code) && $dd_dlr_code!='V0000'){
            $dlrObj = new DbDlr();
            $dlr_info = $dlrObj->alias("a")->join("t_db_area b", "a.area_id=b.area_id")->where(['a.dlr_code' => $dd_dlr_code, 'a.is_enable' => 1])->field("b.city_type,a.dlr_name,b.brand_city_type")->find();
            if(in_array($this->channel_type,['QCSM','QCAPP'])){
                $dlr_level = $dlr_info['brand_city_type'];
            }else{
                $dlr_level = $dlr_info['city_type'];
            }
        }

        //取出所有商品的活动
        $recommend_commodity_id_arr = array_unique($recommend_commodity_id_arr);
        $commodity_id_str = implode(',',$recommend_commodity_id_arr);
        $sql = "select commodity_id,limit_dis,seckill_dis,group_dis,full_dis,n_dis,pre_dis,cheap_dis,limit_wi_dis,is_grouped,card_id from t_db_commodity_flat where commodity_id in($commodity_id_str) and is_enable=1 and shelves_type={$this->shelves_type} order by field(commodity_id,$commodity_id_str) ";
        $commodity_list = $dbCommodityFlatObj->query($sql);
        $set_sku_list_tmp = $dbCommodityFlatObj
            ->alias('a')
            ->field('a.commodity_id,b.id set_sku_id')
            ->join('t_db_commodity_set_sku b', 'a.commodity_id=b.commodity_id')
            ->where("a.commodity_id in($commodity_id_str) and a.is_enable=1 and a.shelves_type={$this->shelves_type} and b.is_enable=1 and (find_in_set('{$user_car_info['car_config_code']}', b.relate_car_18n) || b.relate_car_18n = '' || b.relate_car_18n is null)")
            ->select();
//        print_json($dbCommodityFlatObj->getLastSql());
        $set_sku_list = [];
        foreach ($set_sku_list_tmp as $v){
            $set_sku_list[$v['commodity_id']][] = $v['set_sku_id'];
        }
        //echo $dbCommodityFlatObj->getLastSql();exit;
        $all_seckill_activity=[];
        $all_group_activity=[];
        $all_full_activity = [];
        $all_ndis_activity = [];
        $all_predis_activity = [];
        $all_cheap_activity = [];
        $seckill_commodity_list = $this->getSeckillCommodity();
        //拿到活动
        foreach ($commodity_list as $i => $commodity_item) {
            //过滤秒杀商品
            if($this->removeSeckillCommodity($seckill_commodity_list,$commodity_item['commodity_id'])) continue;
            //商品活动分组
            //限时活动
            if (!empty($commodity_item['limit_dis'])) {
                $limit_data = json_decode($commodity_item['limit_dis'], true);
                $limit_data_wi = json_decode($commodity_item['limit_wi_dis'], true);
                $limit_id = $limit_data[$this->channel_type][0] ?? '';
                $limit_wi_id =$limit_data_wi[$this->channel_type][0] ?? '';
                if(!empty($limit_data) && !empty($limit_id)){
                    $limts_id_arr[$limit_id][] = $commodity_item['commodity_id'];
                    $activity_commodity_id_arr['limit'][$commodity_item['commodity_id']] = $limit_id;
                    $all_limit_activity[] = $limit_id;
                }
            }

            //秒杀活动
            if (!empty($commodity_item['seckill_dis'])) {
                $seckill_data = json_decode($commodity_item['seckill_dis'], true);
                $seckill_id = $seckill_data[$this->channel_type][0] ?? '';
                if(!empty($seckill_id)){
                    $seckill_id_arr[$seckill_id][] = $commodity_item['commodity_id'];
                    $activity_commodity_id_arr['seckill'][$commodity_item['commodity_id']] = $seckill_id;
                    $all_seckill_activity[] = $seckill_id;
                }
            }

            //拼团活动
            if (!empty($commodity_item['group_dis'])) {
                $group_data = json_decode($commodity_item['group_dis'], true);
                $group_id = $group_data[$this->channel_type][0] ?? '';
                if(!empty($group_id)){
                    $group_id_arr[$group_id][] = $commodity_item['commodity_id'];
                    $activity_commodity_id_arr['group'][$commodity_item['commodity_id']] = $group_id;
                    $all_group_activity[] = $group_id;
                }
            }
            //满减活动
            if (!empty($commodity_item['full_dis'])) {
                $full_data = json_decode($commodity_item['full_dis'], true);
                $full_id = $full_data[$this->channel_type][0] ?? '';
                if(!empty($full_id)){
                    $full_id_arr[$full_id][] = $commodity_item['commodity_id'];
                    $activity_commodity_id_arr['full'][$commodity_item['commodity_id']] = $full_id;
                    $all_full_activity[] = $full_id;
                }
            }
            //n件n折
            if (!empty($commodity_item['n_dis'])) {
                $ndis_data = json_decode($commodity_item['n_dis'], true);
                $ndis_id = $ndis_data[$this->channel_type][0] ?? '';
                if(!empty($ndis_id)){
                    $ndis_id_arr[$ndis_id][] = $commodity_item['commodity_id'];
                    $activity_commodity_id_arr['n'][$commodity_item['commodity_id']] = $ndis_id;
                    $all_ndis_activity[] = $ndis_id;
                }
            }
        }

        $all_group_activity = array_unique($all_group_activity);
        $all_cheap_activity = array_unique($all_cheap_activity);
        $all_predis_activity = array_unique($all_predis_activity);
        $all_ndis_activity = array_unique($all_ndis_activity);
        $all_full_activity = array_unique($all_full_activity);
        $all_seckill_activity = array_unique($all_seckill_activity);
        $all_limit_activity = array_unique($all_limit_activity);
        //处理活动
        $now_date = date("Y-m-d H:i:s", time());
        $activity_all = [];
        $kills_tmp = [];
        $segment_info = get_user_segment_info();
        $membership = $segment_info['membership_level'];
        $owner = $segment_info['brand_owner_label'];
        $segment_where = sprintf("user_segment=0 or (user_segment=1 and FIND_IN_SET('%s',user_segment_options)) or (user_segment=2 and FIND_IN_SET('%s',user_segment_options))", $membership, $owner);

        if(!empty($all_limit_activity)){
            $limit_tmps = [];
            $limit_tmp = [];
            $activity_limit_list = $dbLimitDiscountObj
                ->field("a.id,a.title,a.dis_type,a.discount,a.discount_type,a.purchase_number,b.commodity_id,b.sku_dis_select,a.activity_image")
                ->alias('a')
                ->join('t_db_limit_discount_commodity b', 'a.id=b.limit_discount_id')
                ->where(['a.id' => ['in', $all_limit_activity], 'a.is_enable' => 1])
                ->where("a.start_time <='{$now_date}' and '{$now_date}' <= a.end_time")
                ->where($segment_where)
                ->select();
            foreach ($activity_limit_list as $item) {
                $limit_tmp[$item['id']]['id'] = $item['id'];
                $limit_tmp[$item['id']]['title'] = $item['title'];
                $limit_tmp[$item['id']]['dis_type'] = $item['dis_type'];
                $limit_tmp[$item['id']]['rule'] = $item['discount'];
                $limit_tmp[$item['id']]['discount_type'] = $item['discount_type'];
                $limit_tmp[$item['id']]['type'] = 1;
                $limit_tmp[$item['id']]['purchase_number'] = 0;
                $limit_tmp[$item['id']]['sku_list'][$item['commodity_id']] = array_keys(json_decode($item['sku_dis_select'] ?? '', true));
                $limit_tmps[$item['id']]['id'] = $item['id'];
                $limit_tmps[$item['id']]['title'] = $item['title'];
                $limit_tmps[$item['id']]['dis_type'] = $item['dis_type'];
                $limit_tmps[$item['id']]['activity_image'] = $item['activity_image'];
            }
            $activity_all['limit'] = $limit_tmp;
        }

        if(!empty($all_full_activity)){
            $full_tmp = [];
            $activity_full_list = $dbFullDiscountObj->where($segment_where)->whereIn('id', $all_full_activity)->where("start_time <='{$now_date}' and '{$now_date}' <= end_time")->where(['is_enable' => 1])->field("id,activity_title,activity_image")->select();
            foreach ($activity_full_list as $item) {
                $full_tmp[$item['id']]['id'] = $item['id'];
                $full_tmp[$item['id']]['title'] = $item['activity_title'];
                $full_tmp[$item['id']]['type'] = 2;
                $full_tmp[$item['id']]['activity_image'] = $item['activity_image'];
            }
            $activity_all['full'] = $full_tmp;
        }
        if(!empty($all_ndis_activity)){
            $nids_tmp = [];
            $activity_ndis_list = $dbNDiscountObj->where($segment_where)->whereIn('id', $all_ndis_activity)->where("start_time <='{$now_date}' and '{$now_date}' <= end_time")->where(['is_enable' => 1])->field("id,title,activity_image")->select();
            foreach ($activity_ndis_list as $item) {
                $nids_tmp[$item['id']]['id'] = $item['id'];
                $nids_tmp[$item['id']]['title'] = $item['title'];
                $nids_tmp[$item['id']]['type'] = 3;
                $nids_tmp[$item['id']]['activity_image'] = $item['activity_image'];
            }
            $activity_all['ndis'] = $nids_tmp;
        }
        if(!empty($all_seckill_activity)){
            $seckill_tmp = [];
            $activity_seckill_list = $dbseckillObj->where($segment_where)->whereIn('id', $all_seckill_activity)->where("start_time <='{$now_date}' and '{$now_date}' <= end_time")->where(['is_enable' => 1])->field("id,title,activity_image")->select();
            foreach ($activity_seckill_list as $item) {
                $seckill_tmp[$item['id']]['id'] = $item['id'];
                $seckill_tmp[$item['id']]['title'] = $item['title'];
                $seckill_tmp[$item['id']]['type'] = 4;
                $seckill_tmp[$item['id']]['activity_image'] = $item['activity_image'];
            }
            $activity_all['seckill'] = $seckill_tmp;
        }
        if(!empty($all_group_activity)){
            $group_tmp = [];
            $activity_group_list = $dbGroupObj->whereIn('id', $all_group_activity)->where("start_time <='{$now_date}' and '{$now_date}' <= end_time")->where(['is_enable' => 1])->field("id,title,activity_image")->select();
            foreach ($activity_group_list as $item) {
                $group_tmp[$item['id']]['id'] = $item['id'];
                $group_tmp[$item['id']]['title'] = $item['title'];
                $group_tmp[$item['id']]['type'] = 5;
                $group_tmp[$item['id']]['activity_image'] = $item['activity_image'];
            }
            $activity_all['group'] = $group_tmp;
        }

//        $commodity_id_str = implode(',',$recommend_commodity_id_arr);
//        $sql = "select commodity_id,limit_dis,seckill_dis,group_dis,full_dis,n_dis,pre_dis,cheap_dis,card_id,is_grouped from t_db_commodity_flat where commodity_id in($commodity_id_str) and is_enable=1 and shelves_type={$this->shelves_type} order by field(commodity_id,$commodity_id_str) ";
//        $commodity_list = $dbCommodityFlatObj->query($sql);
        $product_tmp_arr = [];
        $info_arr = [];
        $goods_arr = [];
        $dbCardObj = new DbCard();
        $goods_tmp = (new NetGoods())->goodsList(['commodity_ids' => $commodity_id_str], $this->user, $this->channel_type);
        $goods_tmp_cc_arr = [];
        if($goods_tmp['msg']['data']){
            foreach ($goods_tmp['msg']['data'] as $goods_v){
                $goods_tmp_cc_arr[$goods_v['commodity_id']] = $goods_v;
            }
        }
//        print_json($set_sku_list);
        foreach ($commodity_list as $k => $commodity_item) {
            if (empty($set_sku_list[$commodity_item['commodity_id']])) continue;
            //保养商品
            $by_set_sku_id_arr = $all_set_sku_id_arr['by_list'][$commodity_item['commodity_id']] ?? '';
            if (!empty($by_set_sku_id_arr)) {
                $setskulist = $dbCommoditySetSkuObj->whereIn('id',  $by_set_sku_id_arr)->where(['is_enable'=>1])->order("price asc")->select();
                $by_set_sku_id_arr_df = $all_set_sku_id_arr['by_list'][$commodity_item['commodity_id'].'df'] ?? [];
                $by_set_sku_id_arr_no_df = $all_set_sku_id_arr['by_list'][$commodity_item['commodity_id'].'no_df'] ?? [];
                $by_set_sku_id_arr = [];
                if($by_set_sku_id_arr_df){
                    $by_set_sku_id_arr_df_res = $dbCommoditySetSkuObj->whereIn('id',  $by_set_sku_id_arr_df)->where(['is_enable'=>1])->order("price asc")->select();
                    if(!empty($by_set_sku_id_arr_df_res)){
                        foreach($by_set_sku_id_arr_df_res as $setskuitem){
                            $by_set_sku_id_arr[] = $setskuitem['id'];
                        }
                    }
                }
                if($by_set_sku_id_arr_no_df){
                    $by_set_sku_id_arr_no_df_res = $dbCommoditySetSkuObj->whereIn('id',  $by_set_sku_id_arr_no_df)->where(['is_enable'=>1])->order("price asc")->select();
                    if(!empty($by_set_sku_id_arr_no_df_res)){
                        foreach($by_set_sku_id_arr_no_df_res as $setskuitem){
                            $by_set_sku_id_arr[] = $setskuitem['id'];
                        }
                    }
                }

                if(in_array($recommend_commodity_dd_commodity_type_arr[$commodity_item['commodity_id']],[4,41])){
                    $dlr_level=[];
                }

                $by_set_sku_id_arr = implode(',',array_unique($by_set_sku_id_arr));
                $info = $dbCommoditySetSkuObj->getAllByProductList($print_sql,$this->user,$commodity_item['commodity_id'],$this->shelves_type, $this->channel_type, $activity_commodity_id_arr, $activity_all, $product_type, $by_set_sku_id_arr, $dlr_level,'ai');
                if(!empty($info))  $info_arr[$commodity_item['commodity_id']][] = $info;
            }

            //备件商品
            $bj_set_sku_id_arr = $all_set_sku_id_arr['bj'][$commodity_item['commodity_id']] ?? '';
            if (!empty($bj_set_sku_id_arr)) {

                $setskulist = $dbCommoditySetSkuObj->whereIn('id',  $bj_set_sku_id_arr)->where(['is_enable'=>1])->order("price asc")->select();
                if(!empty($setskulist)){
                    $bj_set_sku_id_arr = [];
                    foreach($setskulist as $setskuitem){
                        $bj_set_sku_id_arr[] = $setskuitem['id'];
                    }
                }
                $bj_set_sku_id_arr = array_unique($bj_set_sku_id_arr);


                foreach($bj_set_sku_id_arr as $bj_set_sku_id_item){
                    //所有商品参活动id,所有活动，商品分类，set_sku_id
                    $bj_data = ['dlr_code' => $dlr_code, 'vin' => $user_car_info['vin']];
                    $info = $dbCommoditySetSkuObj->getProductList($print_sql,$this->user,$user_car_info['car_config_code'],$this->shelves_type, $this->channel_type, $activity_commodity_id_arr, $activity_all, $product_type, $bj_set_sku_id_item, $bj_data,$user_car_info,"ai");
                    if(!empty($info)) {
                        if ($commodity_item['is_grouped'] == 1){
//                            $goods_tmp = (new NetGoods())->goodsList(['commodity_ids' => $commodity_item['commodity_id']], $this->user, $this->channel_type);
//                            $info['set_sku_price'] = end($goods_tmp['msg']['data'])['final_price'] ?? $info['set_sku_price'];
                            $info['set_sku_price'] = $goods_tmp_cc_arr[$commodity_item['commodity_id']]['final_price'] ?? $info['set_sku_price'];
                            $info['old_price'] = $goods_tmp_cc_arr[$commodity_item['commodity_id']]['price'] ?? $info['old_price'];
                        }
                        $info_arr[$commodity_item['commodity_id']][] = $info;
                    }
                }
            }

            $card_list_arr = [];
            $act_list_arr = [];
            if(!empty($commodity_item['card_id'])){
//                $card_id_arr = explode(',', $commodity_item['card_id']);
                $card_id_arr =$goods_tmp_cc_arr[$commodity_item['commodity_id']]['card_list']??[];  //goods_list做了商品对应卡券
                if($card_id_arr){
                    $card_where = ['a.id'=>['in',$card_id_arr],'b.is_can_receive'=>1];
                    $card_list = $dbCardObj->getCommodityCard(['where'=>$card_where,'field'=>'a.id,a.card_name,a.card_type,a.card_quota']);
//                $card_list = $dbCardObj->whereIn('id', $card_id_arr)->select();
                    $h_card_arr=[];
                    foreach($card_list as $card_item){
                        if(!in_array($card_item['id'],$h_card_arr)){
                            $item_tmp = [];
                            $item_tmp['id'] =  (string)$card_item['id'];
                            $item_tmp['card_name'] = $card_item['card_name'];
                            $item_tmp['card_type'] = $card_item['card_type'];
                            $item_tmp['card_quota'] = $card_item['card_quota'];
                            $card_list_arr[] = $item_tmp;
                            $h_card_arr[] = $card_item['id'];
                        }

                    }
                }

            }

            if(!empty($commodity_item['limit_dis'])){
                $limit_dis_arr = json_decode($commodity_item['limit_dis'],true);
                $limit_id = $limit_dis_arr[$this->channel_type][0] ?? 0;
                $this_commodity_limit = $activity_all['limit'][$limit_id] ?? '';
                $limit_sku_list = $this_commodity_limit['sku_list'][$commodity_item['commodity_id']] ?? [];
                if (!empty($limit_sku_list)){
                    $this_commodity_sku = $set_sku_list[$commodity_item['commodity_id']];
                    foreach ($this_commodity_sku as $commodity_sku){
                        if (in_array($commodity_sku, $limit_sku_list)){
                            unset($this_commodity_limit['sku_list']);
                            $act_list_arr[] = $this_commodity_limit;
                            break;
                        }
                    }
                }
            }

            if(!empty($commodity_item['seckill_dis'])){
                $seckill_dis_arr = json_decode($commodity_item['seckill_dis'],true);
                $seckill_id = $seckill_dis_arr[$this->channel_type][0] ?? 0;
                if(isset($activity_all['seckill'][$seckill_id])){
                    $act_list_arr[] = $activity_all['seckill'][$seckill_id] ;
                }

            }
            if(!empty($commodity_item['group_dis'])){
                $group_dis_arr = json_decode($commodity_item['group_dis'],true);
                $group_id = $group_dis_arr[$this->channel_type][0] ?? 0;
                if(isset($activity_all['group'][$group_id])){
                    $act_list_arr[] = $activity_all['group'][$group_id] ;
                }
            }
            if(!empty($commodity_item['full_dis'])){
                $full_dis_arr = json_decode($commodity_item['full_dis'],true);
                $full_id = $full_dis_arr[$this->channel_type][0] ?? 0;
                if(isset( $activity_all['full'][$full_id])){
                    $act_list_arr[] = $activity_all['full'][$full_id] ;
                }
            }
            if(!empty($commodity_item['n_dis'])){
                $n_dis_arr = json_decode($commodity_item['n_dis'],true);
                $n_id = $n_dis_arr[$this->channel_type][0] ?? 0;
                if(isset( $activity_all['ndis'][$n_id])){
                    $act_list_arr[] = $activity_all['ndis'][$n_id];
                }
            }
            foreach($info_arr as $kk=>$goodsitem){
                $info_items = [];
                foreach($goodsitem as $kv=>$kitem){
                    $sp_id_arr = explode(',',$kitem['sp_value_list']);
                    $valuename_arr = $dbCommoditySetSkuObj->getSpValueName($sp_id_arr);
                    $info_item = [];

                    $info_item['maintain_dis']=$kitem['maintain_dis'] ?? 0;
                    $info_item['ac_dis_count']=$kitem['ac_dis_count'] ?? 0;
                    $info_item['ac_dis_type']=$kitem['ac_dis_type'] ?? 0;
                    $info_item['commodity_id']=$kitem['commodity_id'];
                    $info_item['commodity_name']=$kitem['commodity_name'];
                    $info_item['dd_commodity_type']=$kitem['dd_commodity_type'];
                    $info_item['sku_code']=$kitem['sku_code'];
                    $info_item['set_sku_id']=$kitem['set_sku_id'];
                    $info_item['cover_image']=$kitem['cover_image'];
                    $info_item['old_price']=sprintf('%.2f', $kitem['old_price']);
                    $info_item['set_sku_price']=sprintf('%.2f', $kitem['set_sku_price']);
                    $info_item['value_ids']=implode(',',$sp_id_arr);
                    $info_item['value_namnes']=implode(',',$valuename_arr);
                    $info_item['act_list'] =$act_list_arr;
                    $info_item['card_list'] = $card_list_arr;
                    $info_items[] = $info_item;
                }
                $goods_arr = $info_items;
            }
            $product_tmp_arr[$commodity_item['commodity_id']] = $goods_arr;
        }


        $unque = [];
        //把商城字段加到推荐里
        foreach($recommend_list_commodity_id_ori_arr as $k=>$recommend_list_commodity_id_ori_one){
            $arr_tmp = [];
            $recommend_list_commodity_id_ori_arr[$k]['new_goods'] = [];
            if(!empty($recommend_list_commodity_id_ori_one['goods'])){
                foreach($recommend_list_commodity_id_ori_one['goods'] as $kk=>$recommend_list_commodity_id_ori_two){
                    $arr_info = $product_tmp_arr[$recommend_list_commodity_id_ori_two['commodityId']] ?? [];
                    if(!empty($arr_info)){
                        if(!in_array($arr_info[0]['commodity_id'],$unque)){
                            array_push($recommend_list_commodity_id_ori_arr[$k]['new_goods'], $arr_info[0]);
                            $unque[] = $arr_info[0]['commodity_id'];
                        }

                    }
                }
            }
        }





        foreach($recommend_list_commodity_id_ori_arr as $k=>$recommend_list_commodity_id_ori_one){
            $recommend_list_commodity_id_ori_arr[$k]['goods'] = [];
            $recommend_list_commodity_id_ori_arr[$k]['goods'] = $recommend_list_commodity_id_ori_one['new_goods'];
            unset($recommend_list_commodity_id_ori_arr[$k]['new_goods']);
        }

        $recommend_data['data'] = $recommend_list_commodity_id_ori_arr;
        $recommend_data['nvi_vin'] = $nvi_vin;
        $recommend_data['dlr_code'] = $dlr_code;
        $recommend_data['longitude'] = $longitude;
        $recommend_data['latitude'] = $latitude;
        return $this->setResponseData($recommend_data)->send();
    }

    /**
     * @title 保养推荐-卡券列表
     * @description 接口说明
     *
     * @return 200:成功
     * @return data:返回数据@!
     * @data carded:套餐名 commodity_type_id:套餐id image:图片 sku_price:原价 set_sku_price:现价 sp_value_name:规格值 sp_name:规格名
     * <AUTHOR>
     * @url /net-small/maintainance/recommend-base
     * @method GET
     *
     */
    public function redommendBase(MaintainanceValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("recommend")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        if(isset($requestData['is_dfs'])){
            //南方商城 保留24小时
            $redis_dfs_key = 'is_dfs_'.$this->channel_type.'_'.$this->user_id;
            if($requestData['is_dfs'] == 1){
                if(!redis($redis_dfs_key)){
                    redis($redis_dfs_key,1,1*24*60*60);
                }
            }
        }
        $current_page = $page  = $requestData['page'] ?? 1;
        $page = intval($page);
        $current_page = intval($current_page);
        $page = $page - 1 ;
        $pageSize = $requestData['page_size'] ?? 5;
        $pageSize = intval($pageSize);
        $longitude = $requestData['longitude'] ?? 0;
        $latitude = $requestData['latitude'] ?? 0;
        // $kilometer = $requestData['kilometer'] ?? 0;
        // dd($requestData);
        $pageCount = $pageSize * $page;
        $out_put_commodity_id=0;
        // dd($pageSize);
        //返回列表
        $cb_list = [];
        //卡券列表
        $card_list = [];
        //保养商品列表
        $product_by_list = [];
        //保养商品id
        $product_by_id = [];
        //备件商品列表
        $product_bj_list_tmp = [];
        //所有备件id
        $product_bj_id = [];
        //限时活动
        $limts_id_arr = [];
        //满减活动
        $full_id_arr = [];
        //n件活动
        $ndis_id_arr = [];
        //秒杀
        $kills_id_arr = [];
        //大数据的所有商品id
        $recommend_commodity_id_arr = [];
        //所有set_sku_id数据
        $all_set_sku_id_arr = [];
        //商品分类
        $product_type = [];
        //活动用的商品id
        $activity_commodity_id_arr = [];
        //所有的限时活动
        $all_limit_activity = [];
        //所有的满减活动
        $all_full_activity = [];
        //所有的n件活动
        $all_n_disn = [];
        //所有秒杀活动
        $all_kills_activity = [];
        //所有活动
        $all_acityvity_tmp = [];
        //二级分类名.
        $type_name_list = [];
        //赠品券列表
        $all_gift_card_id_arr = [];
        $card_list_tmp = [];
        $group_can_card_id = [];
        $all_set_sku_id_arr['by_list'] = [];
        $dd_commodity_type_tmp = [];
        $dbBdpRecommendObj = new DbBdpRecommend();
        $dbCommoditySetSkuObj = new DbCommoditySetSku();
        $dbFullDiscountObj = new DbFullDiscount();
        $dbNDiscountObj = new DbNDiscount();
        $dbLimitDiscountObj = new DbLimitDiscount();
        $dbCommodityFlatObj = new DbCommodityFlat();
        $dbCommodityTypeObj = new DbCommodityType();
        $buCardReceiveRecord = new BuCardReceiveRecord();
        $dbseckillObj = new DbSeckill();
        $dlrObj = new DbDlr();
        $request_type_id = $requestData['type_id'] ?? '';
        $request_type_id = intval($request_type_id);
        $requestData['dlr_code'] = $requestData['dlr_code'] ?? "";
        $test_vin = $requestData['test_vin'] ?? '';
        $test_18n = $requestData['test_18n'] ?? '';
        $print_sql = false;
        $user_car_info['vin']  =  $this->user['vin'];
        if(!empty($test_vin) && !empty($test_18n)){
            $user_car_info['vin'] = $test_vin;
            $user_car_info['car_config_code'] = $test_18n;
            $print_sql = true;
        }
        //取出大数据推荐的商品
        $recommend_info = $dbBdpRecommendObj->where(['vin' => $user_car_info['vin']])->find();
        $cb_emp_list['user_info'] = [];
        $cb_emp_list['request_data'] = $requestData;
        $cb_emp_list['dlr_code'] = '';
        $cb_emp_list['dlr_name'] = '';
        $cb_emp_list['is_vin_car'] = $this->user['user_status'];
        $cb_emp_list['card_list'] =[];
        $cb_emp_list['product_bj_list'] =['data'=>[],'total'=>0,'page_size'=>$pageSize,'page'=>$page];
        $cb_emp_list['product_by_list'] =[];
        if (empty($recommend_info)) {
            //没有商品数据就提前收工了

            return $this->setResponseData($cb_emp_list)->send();
        }
        //取出所有商品备件和保养id
        $recommend_list_commodity_id_arr = json_decode($recommend_info['goods'], true);
        $have_jx_goods_arr =  array_column($recommend_list_commodity_id_arr,'dd_commodity_type');

        //如果有接续套餐，就去掉心悦
        $goods_ids = [];
        foreach ($recommend_list_commodity_id_arr as $k=>$ii){
            $goods_ids[]=$ii['commodity_id'];
            if($ii['dd_commodity_type']==3 && in_array(12,$have_jx_goods_arr)){
                unset($recommend_list_commodity_id_arr[$k]);
            }
        }
        if($goods_ids){
            $this->user = $this->_goods_group($goods_ids,$this->user,$this->channel_type);
        }

        //获取专营店
        $net_common_model = new \app\common\net_service\Common();
        $user_car_info = $net_common_model->getFriendBaseInfo($this->user, $longitude, $latitude);

 //       $IRequestLogObj = new IRequestLog();
//        $ridd = $IRequestLogObj->insertGetId(array('module'=>'net-small','action'=>'Maintainance','function'=>'redommendBase','data'=>json_encode($requestData).'<====>'.json_encode($user_car_info)));
        $dlr_code =  $user_car_info['dlr_code'];
        if(!empty($requestData['dlr_code'])) $dlr_code = $requestData['dlr_code'];


        $redis_key = $user_car_info['vin'].$current_page.$pageSize.$request_type_id.$dlr_code.$this->channel_type.'_'.$this->brand;
        $cb_list = redis($redis_key);
        if($this->is_redis || empty($cb_list) || getRedisLock($redis_key.'-lock', mt_rand(10, 15))) {
            $nev_where =  $this->user['nev_where'];
            $dlr_where = $nev_where;
            $dlr_where['dlr_code'] = $dlr_code;

            $dlr_model = new DbDlr();
            $dlr = $dlr_model->getOne(['where' => $dlr_where]);
            if (!$dlr) {
                $dlr_code = '';
                $dlr_name='';
            } else {
                $dlr_name = $dlr['dlr_name'];
            }
            $cb_list['dlr_code'] =$dlr_code;
            $cb_list['dlr_name'] = $dlr_name;

            $cb_list['is_vin_car'] = $user_car_info['user_status'];


            //获取分类
            $product_type_list = $dbCommodityTypeObj->where(['comm_parent_id' => ['gt', 0], 'is_enable' => 1, 'level' => 2])->field('id,comm_type_name')->select();
            foreach ($product_type_list as $product_type_item) {
                $product_type[$product_type_item['id']] = $product_type_item['comm_type_name'];
            }
            $commodity_id_str=[];

            foreach ($recommend_list_commodity_id_arr as $item) {
                //所有的set_sku_id
                $all_set_sku_id_arr['all'] = $item['set_sku_id'];
                if ($item['dd_commodity_type'] == 9 || $item['dd_commodity_type'] == 7) {
                    //备件
                    $product_bj_id[] = $item['commodity_id'];
                    $all_set_sku_id_arr['bj'][$item['commodity_id']] = $item['set_sku_id'];
                    $recommend_commodity_id_arr[] = $item['commodity_id'];
                }
                if ($item['dd_commodity_type'] == 1 || $item['dd_commodity_type'] == 3 ||  $item['dd_commodity_type'] == 12) {
                    //心悦老友惠+接续
                    $product_by_id[] = $item['commodity_id'];
                    $all_set_sku_id_arr['by_list'][$item['commodity_id']] = $item['set_sku_id'];
                    $recommend_commodity_id_arr[] = $item['commodity_id'];

                }

                if ($item['dd_commodity_type'] == 4 || $item['dd_commodity_type'] == 41) {
                    //五年
                    $product_by_id[] = $item['commodity_id'];
                    $all_set_sku_id_arr['by_list'][$item['commodity_id']] = $item['set_sku_id'];
                    $recommend_commodity_id_arr[] = $item['commodity_id'];
                }
                $dd_commodity_type_tmp[$item['commodity_id']] = $item['dd_commodity_type'];
                $commodity_id_str[]=$item['commodity_id'];
            }
            $goods_tmp_cc_arr = [];
            $card_id_arr = [];
            $commodity_set_id_arr =[];
            $rel_goods_id = [];
            $net_goods = new NetGoods();
            $get_card_list=[];
            if($commodity_id_str){
                $goods_tmp = $net_goods->goodsList(['commodity_ids' => implode(',',$commodity_id_str),'use_gift_card'=>3], $this->user, $this->channel_type);

                if($goods_tmp['msg']['data']){
                    foreach ($goods_tmp['msg']['data'] as $goods_v){
                        $rel_goods_id[]=$goods_v['commodity_id'];
                        $goods_tmp_cc_arr[$goods_v['commodity_id']] = $goods_v;
                    }
                    $get_card_list = $goods_tmp['msg']['get_card_list'];//可领卡券列表，下面对不可领的再进行处理

                    $goods_card_rule = $goods_tmp['msg']['goods_card_rule'];
                }
            }

            $dlr_level='A';
            $dd_dlr_code = $dlr_code;//没有传值就获取默认店，在上面
            if(!empty($dd_dlr_code) && $dd_dlr_code!='V0000'){
                $dlrObj = new DbDlr();
                $dlr_info = $dlrObj->alias("a")->join("t_db_area b", "a.area_id=b.area_id")->where(['a.dlr_code' => $dd_dlr_code, 'a.is_enable' => 1])->field("b.city_type,a.dlr_name,b.brand_city_type")->find();
                if(in_array($this->channel_type,['QCSM','QCAPP'])){
                    $dlr_level = $dlr_info['brand_city_type'];
                }else{
                    $dlr_level = $dlr_info['city_type'];
                }
            }
            //没有商品数据就提前收工了
            if(empty($recommend_commodity_id_arr)) {
                return $this->setResponseData($cb_emp_list)->send();
            }
            //所有卡券
            $card_id_tmp = [];
            $card_commodity_id_arr = [];
            unset($cb_list['card_list_tmp']);

            //取出所有商品的活动
            $commodity_id_str = implode(',',$recommend_commodity_id_arr);
            $sql = "select commodity_id,limit_dis,full_dis,n_dis,seckill_dis,limit_wi_dis,full_wi_dis,is_grouped,commodity_set_id from t_db_commodity_flat where commodity_id in($commodity_id_str) and is_enable=1 and shelves_type={$this->shelves_type} order by field(commodity_id,$commodity_id_str) ";
            $commodity_list = $dbCommodityFlatObj->query($sql);

            foreach ($commodity_list as $i => $commodity_item) {
                //商品活动分组
                //限时活动
                if (!empty($commodity_item['limit_dis']) || !empty($commodity_item['limit_wi_dis'])) {
                    $limit_data = json_decode($commodity_item['limit_dis'], true);
                    $limit_data_wi = json_decode($commodity_item['limit_wi_dis'], true);
                    $limit_id = $limit_data[$this->channel_type][0] ?? '';
                    $limit_wi_id =$limit_data_wi[$this->channel_type][0] ?? '';
                    if(!empty($limit_data) && !empty($limit_id)){
                        $limts_id_arr[$limit_id][] = $commodity_item['commodity_id'];
                        $activity_commodity_id_arr['limit'][$commodity_item['commodity_id']] = $limit_id;
                        $all_limit_activity[] = $limit_id;
                    }

                    if(!empty($limit_data_wi) && !empty($limit_wi_id)){
                        $limts_id_arr[$limit_wi_id][] = $commodity_item['commodity_id'];
                        // $activity_commodity_id_arr['limit'][$commodity_item['commodity_id']] = $limit_data_wi[$this->channel_type][0];
                        $all_limit_activity[] = $limit_wi_id;
                    }
                }

                //满减活动
                if (!empty($commodity_item['full_dis']) || !empty($commodity_item['full_wi_dis'])) {
                    $full_data = json_decode($commodity_item['full_dis'], true);
                    $full_data_wi = json_decode($commodity_item['full_wi_dis'], true);
                    $full_id = $full_data[$this->channel_type][0] ?? 0;
                    $full_wi_id = $full_data_wi[$this->channel_type][0] ?? 0 ;
                    if(!empty($full_data) && !empty($full_id)){
                        $full_id_arr[$full_id][] = $commodity_item['commodity_id'];
                        $activity_commodity_id_arr['full'][$commodity_item['commodity_id']] = $full_id;
                        $all_full_activity[] = $full_id;
                    }
                    if(!empty($full_data_wi)){
                        $full_id_arr[$full_wi_id][] = $commodity_item['commodity_id'];
                        // $activity_commodity_id_arr['full'][$commodity_item['commodity_id']] = $full_wi_id;
                        $all_full_activity[] = $full_wi_id;
                    }
                }
                //n件活动
                if (!empty($commodity_item['n_dis'])) {
                    $n_data = json_decode($commodity_item['n_dis'], true);
                    $n_id = $n_data[$this->channel_type][0] ?? 0;
                    $ndis_id_arr[$n_id][] = $commodity_item['commodity_id'];
                    $activity_commodity_id_arr['ndis'][$commodity_item['commodity_id']] = $n_id;
                    $all_n_disn[] = $n_id;
                }
            }

            $all_limit_activity = array_unique($all_limit_activity);
            $all_full_activity  = array_unique($all_full_activity);
            //dd($all_full_activity);
            //所有商品活动
            $now_date = date("Y-m-d H:i:s", time());
            $limit_tmp = [];
            $full_tmp = [];
            $n_tmp = [];
            $activity_all = [];
            $kills_tmp = [];
            $segment_info = get_user_segment_info();
            $membership = $segment_info['membership_level'];
            $owner = $segment_info['brand_owner_label'];
            $segment_where = sprintf("user_segment=0 or (user_segment=1 and FIND_IN_SET('%s',user_segment_options)) or (user_segment=2 and FIND_IN_SET('%s',user_segment_options))", $membership, $owner);
            $activity_limit_list = $dbLimitDiscountObj->whereIn('id', $all_limit_activity)->where("start_time <='{$now_date}' and '{$now_date}' <= end_time")->where($segment_where)->where(['is_enable' => 1])->field("id,title,dis_type,discount,discount_type,purchase_number,user_segment,user_segment_options,activity_image")->select();
            foreach ($activity_limit_list as $item) {
                $limit_tmp[$item['id']]['id'] = $item['id'];
                $limit_tmp[$item['id']]['title'] = $item['title'];
                $limit_tmp[$item['id']]['dis_type'] = $item['dis_type'];
                $limit_tmp[$item['id']]['rule'] = $item['discount'];
                $limit_tmp[$item['id']]['discount_type'] = $item['discount_type'];
                $limit_tmp[$item['id']]['type'] = 1;
                $limit_tmp[$item['id']]['purchase_number'] = 0;

                $limit_tmp[$item['id']]['commodity_dis_act_user_segment'] = $item['user_segment'];
                $limit_tmp[$item['id']]['commodity_dis_label'] = get_user_segment_label($item['user_segment']);
                $limit_tmp[$item['id']]['commodity_dis_label_cn'] = get_user_segment_label_cn($limit_tmp[$item['id']]['commodity_dis_label']);
                $limit_tmp[$item['id']]['activity_image'] = $item['activity_image'];
            }
            //所有商品满减活动
            $activity_full_list = $dbFullDiscountObj->whereIn('id', $all_full_activity)->where("start_time <='{$now_date}' and '{$now_date}' <= end_time")->where($segment_where)->where(['is_enable' => 1])->field('id,activity_title title,full_discount_rules,purchase_number,user_segment,user_segment_options,activity_image')->select();
            foreach ($activity_full_list as $item) {
                $full_tmp[$item['id']]['type'] = 2;
                $full_tmp[$item['id']]['id'] = $item['id'];
                $full_tmp[$item['id']]['title'] = $item['title'];
                $full_tmp[$item['id']]['purchase_number'] = $item['purchase_number'];

                $full_tmp[$item['id']]['commodity_dis_act_user_segment'] = $item['user_segment'];
                $full_tmp[$item['id']]['commodity_dis_label'] = get_user_segment_label($item['user_segment']);
                $full_tmp[$item['id']]['commodity_dis_label_cn'] = get_user_segment_label_cn($full_tmp[$item['id']]['commodity_dis_label']);
                $full_tmp[$item['id']]['activity_image'] = $item['activity_image'];
            }
            //所有商品n件活动
            $activity_n_list = $dbNDiscountObj->alias("a")->join("t_db_n_discount_info b", "a.id=b.n_id")->whereIn('a.id', $all_n_disn)->where("a.start_time <='{$now_date}' and '{$now_date}' <= a.end_time")->where($segment_where)->where(['a.is_enable' => 1])->field("a.id,a.title,b.piece,b.discount,user_segment,user_segment_options,activity_image")->select();
            foreach ($activity_n_list as $item) {
                $n_tmp[$item['id']]['type'] = 3;
                $n_tmp[$item['id']]['id'] = $item['id'];
                $n_tmp[$item['id']]['title'] = $item['title'];
                $n_tmp[$item['id']]['purchase_number'] = 0;

                $n_tmp[$item['id']]['commodity_dis_act_user_segment'] = $item['user_segment'];
                $n_tmp[$item['id']]['commodity_dis_label'] = get_user_segment_label($item['user_segment']);
                $n_tmp[$item['id']]['commodity_dis_label_cn'] = get_user_segment_label_cn($n_tmp[$item['id']]['commodity_dis_label']);
                $n_tmp[$item['id']]['activity_image'] = $item['activity_image'];
            }

            $activity_all['limit'] = $limit_tmp;
            $activity_all['full'] = $full_tmp;
            $activity_all['ndis'] = $n_tmp;
            $seckill_commodity_list = $this->getSeckillCommodity();
            $tmp_commtype_id_arr = [];
            $can_use_card_goods_id = [];
            $can_not_card_arr = [];
            foreach ($commodity_list as $ki => $commodity_item) {
                //过滤秒杀商品
                if($this->removeSeckillCommodity($seckill_commodity_list,$commodity_item['commodity_id'])) continue;
                //汤总说要过滤套餐id
//                if(!in_array($commodity_item['commodity_id'],$this->limit_product_list) && in_array($dd_commodity_type_tmp[$commodity_item['commodity_id']],[1,3,4,41])){
//                    continue;
//                }
                //保养商品
//                echo "(".$commodity_item['is_grouped'].'-'.$commodity_item['commodity_id'].")";
                $by_set_sku_id = $all_set_sku_id_arr['by_list'][$commodity_item['commodity_id']] ?? '';
                $bj_set_sku_id = $all_set_sku_id_arr['bj'][$commodity_item['commodity_id']] ?? '';

                $now_sku_id = $bj_set_sku_id;
                if($by_set_sku_id){
                    $now_sku_id = $by_set_sku_id;
                }
                $card_can_use_status = 0;//0没卡券1当前规格可用券2其他规格可用券
                $card_commodity=[];
                $can_use_card_sku_id= [];
                $card_to_sku_arr = [];
                if(isset($goods_card_rule[$commodity_item['commodity_set_id']])){
                    $card_goods_one = $goods_card_rule[$commodity_item['commodity_set_id']];
                    $now_sku_id_arr = explode(',', $now_sku_id);
                    foreach ($card_goods_one as $card_goods_info){
                        $card_commodity[]=[
                            'card_id'=>$card_goods_info['card_id'],
                            'group_card_type'=>$card_goods_info['group_card_type']
                        ];
                        if($card_goods_info['group_card_type']!=2){
                            $card_can_use_status = 2;
                            $card_to_sku_arr[$commodity_item['commodity_set_id']][$card_goods_info['card_id']]=$card_goods_info['set_sku_arr'];
                            //can_get 可领+可用  can_use_card 可用
                            if($card_goods_info['can_get']>0 || $card_goods_info['can_use_card']>0){
                                if(empty($card_goods_info['set_sku_arr'])){
                                    $card_can_use_status=1;
                                }else{
                                    $g_s_sid_arr =  $card_goods_info['set_sku_arr'];
                                    $can_use_card_sku_id =  array_merge($g_s_sid_arr,$can_use_card_sku_id);
                                    if($by_set_sku_id){
                                        if(!array_intersect(explode(",",$by_set_sku_id),$g_s_sid_arr)){
                                            $card_can_use_status=0;
                                        }else{
                                            if(array_intersect($now_sku_id_arr,$g_s_sid_arr)){
                                                $card_can_use_status=1;
                                            }
                                        }
                                    }else{
                                        $card_can_use_status=1;//备件强制==1 不会是2，在下面根据匹配的sku再进行处理
                                    }
                                }
                            }
                        }
                        if(in_array($card_can_use_status,[1,2])){
                            if($can_not_card_arr){
                                if(in_array($card_goods_info['card_id'],$can_use_card_sku_id)){
                                    array_diff($can_not_card_arr,[$card_goods_info['card_id']]);
                                }
                            }
                        }else{
                            $can_not_card_arr[]=$card_goods_info['card_id'];
                        }
                    }
                }

                if (!empty($by_set_sku_id)) {
                    if($dd_commodity_type_tmp[$commodity_item['commodity_id']] == 4 || $dd_commodity_type_tmp[$commodity_item['commodity_id']] == 41){
                        $dlr_level=[];
                    }

                    $by_info = $dbCommoditySetSkuObj->getAllByProductList($print_sql,$this->user,$commodity_item['commodity_id'],$this->shelves_type, $this->channel_type, $activity_commodity_id_arr, $activity_all, $product_type, $by_set_sku_id, $dlr_level);
                    //重新判断是否匹配...
                    if(isset($card_to_sku_arr[$commodity_item['commodity_set_id']])){
                        foreach($card_to_sku_arr[$commodity_item['commodity_set_id']] as $cc_k=>$cc_v){
                            if($cc_v){
                                if(!array_intersect(explode(',',$by_info['set_sku_ids']),$cc_v)){
                                    $can_not_card_arr[]=$cc_k;
                                }
                            }
                        }
                    }
                    if($card_can_use_status==1){
                        if(!in_array($by_info['set_sku_id'],$can_use_card_sku_id) && $can_use_card_sku_id){
                            $card_can_use_status=2;
                        }
                        if(!array_intersect(explode(',',$by_info['set_sku_ids']),$can_use_card_sku_id)  && $can_use_card_sku_id){
                            $card_can_use_status=0;
                        }
                    }
                    if($card_can_use_status>0){
                        $can_use_card_goods_id[]=$commodity_item['commodity_id'];
                    }
                    $by_info['can_use_card'] = $card_can_use_status;
                    array_walk($card_commodity, function(&$item) {
                        // 移除 'price' 字段
                        unset($item['g_s_sid']);
                    });
                    $by_info['card_commodity_list'] = $card_commodity;
//                    $by_info['gift_card_ids'] = "";
//                    if(!empty($by_info['sku_code']) && !empty($card_gif_sku_arr) && in_array($by_info['sku_code'],$card_gif_sku_arr)){
//                        $by_info['gift_card_type'] = 1;
//                        if(!empty($by_info['card_id']) && !empty($card_gif_id_arr)){
//                           $card_gift_arr_list = explode(',',$by_info['card_id']);
//                           foreach($card_gift_arr_list as $card_gift_item){
//                               if(in_array($card_gift_item,$card_gif_id_arr)){
//                                   $by_info['gift_card_ids'] .= $card_gift_item.',';
//                               }
//                           }
//
//                        }
//                    }
                    if(isset($by_info['set_sku_id'])){
                        $by_info['gift_card_count'] =  $net_goods->checkGiftCard(json_encode([['sku_id'=>$by_info['set_sku_id']]]), $this->user,[], $this->channel_type);
                    }
                    if(!empty($by_info['comm_type_id'])) $product_by_list[] = $by_info;
                }

                //备件商品

                if (!empty($bj_set_sku_id)) {
                    //所有商品参活动id,所有活动，商品分类，set_sku_id
                    $bj_data = ['dlr_code' => $dlr_code, 'vin' => $user_car_info['vin']];
                    $re_data = $dbCommoditySetSkuObj->getProductList($print_sql,$this->user,$user_car_info['car_config_code'],$this->shelves_type, $this->channel_type, $activity_commodity_id_arr, $activity_all, $product_type, $bj_set_sku_id, $bj_data,$user_car_info,'',$commodity_item['commodity_id']);

                    if(!empty($re_data)){
                        if($card_can_use_status==1){
                            if(!in_array($re_data['set_sku_id'],$can_use_card_sku_id)  && $can_use_card_sku_id){
                                $card_can_use_status=2;
                            }
                            if(!array_intersect(explode(',',$re_data['set_sku_ids']),$can_use_card_sku_id)  && $can_use_card_sku_id){
                                $card_can_use_status=0;
                            }
                        }

                        $re_data['can_use_card'] = $card_can_use_status;
                        array_walk($card_commodity, function(&$item) {
                            // 移除 'price' 字段
                            unset($item['g_s_sid']);
                        });
                        $re_data['card_commodity_list'] = $card_commodity;

                        if ($commodity_item['is_grouped'] == 1){
                            $commodity_item_sub = (new NetGoods())->detail([
                                'goods_id'    => $commodity_item['commodity_id'],
                                'lng'         => $longitude,
                                'lat'         => $latitude,
                                'dd_dlr_code' => $dd_dlr_code,
                            ], $this->user, $this->channel_type, $this->brand,9);
                            if (empty($commodity_item_sub['msg']['goods']['groups_data']) || empty($commodity_item_sub['msg']['goods']['maintain_can_buy']) || empty($commodity_item_sub['msg']['goods']['count_stock']) || $commodity_item_sub['msg']['goods']['is_mate'] != 1 || $commodity_item_sub['msg']['canot_buy']['status'] == 1){
                                continue;
                            }


                            $re_data['groups_data'] = $commodity_item_sub['msg']['goods']['groups_data'] ?? [];
                            if(!empty($re_data['groups_data'])){
                                foreach($re_data['groups_data']['sub_commodity_list'] as $gk=>$item){
                                   // dd($item['commodity_sku_id']);
                                  //  dd($re_data['groups_data']['sub_commodity_list'][$gk]['gift_card_count']);

                                    $gift_card_count = $net_goods->checkGiftCard(json_encode([['sku_id'=>implode(',',$item['all_sku_ids'])]]), $this->user, [],$this->channel_type);
                                    $item['gift_card_count'] =  $gift_card_count;
                                    if(isset($gift_card_count['card_id']) && empty($gift_card_count['card_id'])){
                                        $all_gift_card_id_arr[] =$gift_card_count['card_id'];
                                    }
                                   // if(!empty($item['sku_code']) && !empty($card_gif_sku_arr) && in_array($item['sku_code'],$card_gif_sku_arr)){
                                        //$re_data['groups_data']['sub_commodity_list'][$gk]['gift_card_type'] = 1;
                                       // if(!empty($item['card_id']) && !empty($card_gif_id_arr)){
//                                            $card_gift_arr_list = explode(',',$item['card_id']);
//                                            foreach($card_gift_arr_list as $card_gift_item){
//                                                if(in_array($card_gift_item,$card_gif_id_arr)){
//                                                    $re_data['groups_data']['sub_commodity_list'][$gk]['gift_card_ids'] .= $card_gift_item.',';
//                                                }
//                                            }
//                                            if(!empty($re_data['groups_data']['sub_commodity_list'][$gk]['gift_card_ids'])){
//                                                $re_data['groups_data']['sub_commodity_list'][$gk]['gift_card_ids'] =  rtrim($re_data['groups_data'][$gk]['gift_card_ids'],',');
//                                            }
                                          //  $re_data['groups_data']['sub_commodity_list'][$gk]['gift_card_count'] =  $net_goods->checkGiftCard(json_encode([$re_data['sku_id']]), $this->user, [],$this->channel_type);
                                       // }
                                 //   }
                                }
                            }

                            $re_data['h_card_list'] = $commodity_item_sub['msg']['h_card_list'] ?? [];
                            $can_user_card_list =$commodity_item_sub['msg']['card_list'] ?? [];
                            $re_data['sp_associated_data'] = $commodity_item_sub['msg']['sp_associated_data'] ?? [];
                            $re_data['is_sp_associated'] = $commodity_item_sub['msg']['is_sp_associated'] ?? '';
                            $re_data['full_list'] = $commodity_item_sub['msg']['full_list'] ?? '';
                            $re_data['n_dis_info'] = $commodity_item_sub['msg']['n_dis_info'] ?? [];
                            $re_data['limit_info'] = $commodity_item_sub['msg']['limit_info'] ?? [];
                            $re_data['seckill_info'] = $commodity_item_sub['msg']['seckill_info'] ?? [];
                            $re_data['limit_wi_info'] = $commodity_item_sub['msg']['limit_wi_info'] ?? [];
                            $re_data['full_wi_list'] = $commodity_item_sub['msg']['full_wi_list'] ?? [];

                            $re_data['g_info'] = $commodity_item_sub['msg']['g_info'] ?? [];
                            $re_data['gift_info'] = $commodity_item_sub['msg']['gift_info'] ?? [];
                            if($can_user_card_list){
                                $group_can_card_id =  array_column($can_user_card_list,'card_id');
                                $can_use_card_goods_id[]=$commodity_item['commodity_id'];
                            }
                        }else{
                            if($card_can_use_status>0){
                                $can_use_card_goods_id[]=$commodity_item['commodity_id'];
                            }
//                            echo "sku:".$re_data['sku_code'];
//                            var_dump($card_gif_sku_arr);echo "==";
//                            if(!empty($re_data['sku_code']) && !empty($card_gif_sku_arr) && in_array($re_data['sku_code'],$card_gif_sku_arr)){
//                                $re_data['gift_card_type'] = 1;
//
//                                if(!empty($re_data['card_id']) && !empty($card_gif_id_arr)){
//                                    $card_gift_arr_list = explode(',',$re_data['card_id']);
//                                    foreach($card_gift_arr_list as $card_gift_item){
//                                        if(in_array($card_gift_item,$card_gif_id_arr)){
//                                            $re_data['gift_card_ids'] .= $card_gift_item.',';
//                                        }
//                                    }
//                                }
//                            }
                        }

                        $re_data['gift_card_count'] =  $net_goods->checkGiftCard(json_encode([['sku_id'=>$re_data['set_sku_id']]]), $this->user,[], $this->channel_type);
                        if(isset($re_data['gift_card_count']['card_id']) && empty($re_data['gift_card_count']['card_id'])){
                            $all_gift_card_id_arr[] = $re_data['gift_card_count']['card_id'];
                        }
                        $tmp_commtype_id_arr[] = $re_data['comm_type_id'];
                        $product_bj_list_tmp[] = $re_data;

                    }
                }
                $out_put_commodity_id =$commodity_item['commodity_id'];
//                if($bj_set_sku_id == 83495){
//
//                    dd($re_data);
//                }
            }


            if(!empty($tmp_commtype_id_arr)){
                $tmp_commtype_id_arr = array_unique($tmp_commtype_id_arr);
                $type_list = $dbCommodityTypeObj->alias("a")->whereIn('a.id',$tmp_commtype_id_arr)->field('a.comm_type_name,a.id')->order("field(a.id, " . implode(',', $tmp_commtype_id_arr) . ")")->select();

                foreach($type_list as $type_info){
                    $type_name_list[] = ['comm_type_id' =>$type_info['id'], 'comm_type_name' => $type_info['comm_type_name']];
                }

            }

            if (!empty($type_name_list)) {
                $type_name_list_tmp = [];
                foreach ($type_name_list as $item) {
                    $type_name_list_tmp[$item['comm_type_id']] = $item;
                }
                $type_name_list = [];
                foreach ($type_name_list_tmp as $itemt) {
                    $type_name_list[] = $itemt;
                }
            }

            $type_id_arr = [];
            $type_name_arr = [];

            foreach ($type_name_list as $bj_item) {
                $type_id_arr[] = $bj_item['comm_type_id'];
                $type_name_arr[$bj_item['comm_type_id']] = $bj_item['comm_type_name'];
            }
            if (!empty($type_id_arr)) {
                $type_id_list = array_unique($type_id_arr);
                foreach ($type_id_list as $type_item_id) {
                    foreach ($product_bj_list_tmp as $recommend_item) {
                        $comm_type_arr = explode(',',$recommend_item['comm_type_id_str']);
                        $parent_comm_type_id = $comm_type_arr[1] ?? '';

                        if(empty($parent_comm_type_id)) {
//                            print_json($comm_type_arr);
                            continue;
                        }
                        if ($type_item_id == $parent_comm_type_id) {
                            $product_bj_list_data_tmp[$type_item_id][] = $recommend_item;
                        }
                    }
                }
                $type_id = empty($request_type_id) ? $type_id_arr[0] : $request_type_id;
                $product_bj_list_tmp = $product_bj_list_data_tmp[$type_id];
            }

            $count = count($product_bj_list_tmp);
            $product_bj_list['data'] = array_slice($product_bj_list_tmp, $pageCount, $pageSize);
            $product_bj_list['current_page'] = $current_page;
            $product_bj_list['page_size'] = $pageSize;
            $product_bj_list['total'] = $count;
            $product_bj_list['all_page'] = ceil($count / $pageSize);

            $cb_list['product_by_list'] = $product_by_list;
            $cb_list['product_bj_list'] = $product_bj_list;
            $cb_list['type_name_list'] = $type_name_list;
            $user_car_info['user_id']=$this->user_id;
            $cb_list['user_info'] = $user_car_info;
            $cb_list['card_list'] = [];
//            print_json($get_card_list);
            $act_code="maintainace_g7pw6ir3r";
            //.get_rand_str(9)
            if($get_card_list){
                foreach ($get_card_list as $citem) {
                    $citem['act_code'] = $act_code;
                    unset($citem['set_sku_ids']);
                    if(!in_array($citem['card_id'],$can_not_card_arr) || in_array($citem['card_id'],$group_can_card_id)){
                        $cb_list['card_list'][] = $citem;
                    }
                }
            }
//            redis($redis_key, $cb_list, 8640000);
        }
        $can_card_id = [];
        $can_user_card_goods_redis_name = $this->user_id.'can_user_card_goods_redis_name-';
        foreach($card_commodity_id_arr as $ca_k=> $ca_v){
            if(in_array($ca_k,$can_use_card_goods_id)){
                foreach($ca_v as $ca_vv){
                    $can_card_id[]= $ca_vv['card_id'];
                }
                redis($can_user_card_goods_redis_name.$ca_k,implode(',',$can_card_id),7200);
            }
        }
        //所有赠品券
        $cb_list['gift_card_dlr'] = 0;
        if(!empty($all_gift_card_id_arr)){
            $where3 = " a.status = 1 and a.is_enable=1  
                    and (a.use_vin='".$this->user['vin']."' or a.use_vin='' or a.use_vin is null)
                    and ((a.receive_vin = '".$user_car_info['vin']."' and (a.user_id=".$this->user['id'] ." or a.user_id=0)) or (a.user_id=".$this->user['id'] ."  and (a.receive_vin='' or a.receive_vin is null)) )";
            $giftCardReceiveRecordList = $buCardReceiveRecord->alias("a")
                                                             ->join("t_db_dlr b","a.intention_store=b.dlr_code and b.is_enable =1","left")
                                                             ->whereIn("a.card_id",$all_gift_card_id_arr)
                                                             ->where($where3)->field("GROUP_CONCAT(intention_store) dlr_code_str,b.dlr_name,a.intention_store,count(1) as count")
                                                             ->group("a.intention_store")->order("count desc")->find();

            if(!empty($giftCardReceiveRecordList)){
                $cb_list['dlr_code'] = $giftCardReceiveRecordList['intention_store'];
                $cb_list['dlr_name'] = $giftCardReceiveRecordList['dlr_name'];
                if($giftCardReceiveRecordList['dlr_code_str']){
                    $gift_dlr_code_arr = explode(',',$giftCardReceiveRecordList['dlr_code_str']);
                    if(in_array($dlr_code,$gift_dlr_code_arr)){
                        $cb_list['gift_card_dlr'] = 1;
                    }
                }
            }
        }

        $cb_list['request_data'] = $requestData;
        $cb_list['act_code'] = $act_code;
        $cb_list['commodity_id'] = $out_put_commodity_id;
        $cb_list['goods_ids'] = implode(',',$goods_ids);
//        $IResponesLogObj = new IResponesLog();
//        $IResponesLogObj->insertGetId(array('request_id'=>$ridd,'module'=>'net-small','action'=>'Maintainance','function'=>'redommendBase','data'=>json_encode($cb_list)));
        return $this->setResponseData($cb_list)->send();
    }

    /**
     * 去掉秒杀的商品
     * @return void
     */
    private function removeSeckillCommodity($seckill_commodity_list,$commodity_id){

        $commodity_arr = [];
        foreach($seckill_commodity_list as $seckill_commodity_item){
            $commodity_arr[] = $seckill_commodity_item['commodity_id'];
        }
        if(!empty($commodity_arr)){
            $commodity_arr = array_unique($commodity_arr);
            if(in_array($commodity_id,$commodity_arr)) return true;
        }
        return false;
    }

    /**
     * 获取所有未开始进行中的商品
     * @return bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private function getSeckillCommodity(){
        $dbSeckillObj = new DbSeckill();
        return $dbSeckillObj->alias("a")->join("t_db_seckill_commodity b","a.id = b.seckill_id")
            ->where(['a.is_enable'=>1])
            ->whereIn("act_status",[1,2])->group("b.commodity_id")->group("b.commodity_id")->select();
    }


    public function activityChange(MaintainanceValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("activityChange")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_common_model = new \app\common\net_service\Common();
        $user_car_info = $net_common_model->getFriendBaseInfo($this->user);
        $dlr_code =  $user_car_info['dlr_code'];
        $requestData['dlr_code'] = $requestData['dlr_code'] ?? "";
        $num = $requestData['num'] ?? 1;
        if(!empty($requestData['dlr_code'])) $dlr_code = $requestData['dlr_code'];
        $test_vin = $requestData['test_vin'] ?? '';
        $test_18n = $requestData['test_18n'] ?? '';
        if(!empty($test_vin) && !empty($test_18n)){
            $user_car_info['vin'] = $test_vin;
            $user_car_info['car_config_code'] = $test_18n;
        }


        $dd_commodity_type = $requestData['dd_commodity_type'];
        $type = $requestData['type'];
        $use_discount = $requestData['use_discount'] ?? 1;
        $set_sku_id = $requestData['set_sku_id'];
        $commodity_id = $requestData['commodity_id'];
        $rise = 4;
        if($dd_commodity_type == 4 || $dd_commodity_type == 41){
            if($this->brand == 2){
                $rise = 4;
            }else{
                $rise = 4;
                $oil_type = $car_package['body'][0]['MAINTAIN_OIL_TYPE'] ?? '';
                if ($oil_type == 'ASOIL') {
                    $rise = 41;
                }
            }
        }
        $shelves_type = $this->shelves_type;
        $channel = $this->channel_type;
        $user_info = $this->user;
        $dbCommoditySetSkuObj = new DbCommoditySetSku();
        $bj_data = ['dlr_code' => $dlr_code, 'vin' => $user_car_info['vin'],'code18n'=>$user_car_info['car_config_code']];
        $commodity_price_info = $dbCommoditySetSkuObj->activityChange($type,$set_sku_id,$commodity_id,$shelves_type,$user_info,$channel,$dd_commodity_type,$rise,$bj_data,$dlr_code,$use_discount);//$rise 用不到了
        if ($use_discount) {
            $cb['set_sku_price'] = $commodity_price_info['set_sku_price'];
            $cb['dis_price'] = $commodity_price_info['dis_price'];
            $cb['price'] = $commodity_price_info['price'];
        } else {
            $cb['set_sku_price'] = $commodity_price_info['commodity_dis_act_original_price'];
            $cb['dis_price'] = round($commodity_price_info['commodity_dis_original_price'] - $commodity_price_info['commodity_dis_act_original_price'], 2);
            $cb['price'] = $commodity_price_info['commodity_dis_original_price'];
        }
        $cb['old_price'] = $commodity_price_info['old_price'];
        $cb['wi_act_id'] = $commodity_price_info['wi_act_id'] ;
        $cb['wi_act_type_id'] = $commodity_price_info['wi_act_type_id'] ;


        $cb['work_time_price'] = round($commodity_price_info['work_time_price'] * $num,2);
        $cb['old_work_time_price'] = round($commodity_price_info['old_work_time_price'] * $num, 2);

        $cb['commodity_dis_act_original_price'] = $commodity_price_info['commodity_dis_act_original_price'];
        $cb['commodity_dis_act_segment_price'] = $commodity_price_info['commodity_dis_act_segment_price'];
        $cb['commodity_dis_use_discount'] = $use_discount;

        $cb['commodity_dis_user_segment'] = $commodity_price_info['commodity_dis_user_segment'];
        $cb['commodity_dis_act_user_segment'] = $commodity_price_info['commodity_dis_act_user_segment'];
        $cb['commodity_dis_label'] = $commodity_price_info['commodity_dis_label'];
        $cb['commodity_dis_label_cn'] = $commodity_price_info['commodity_dis_label_cn'];

        $cb['ac_dis_type'] = $commodity_price_info['ac_dis_type'] ?? 0;
        $cb['ac_dis_count'] = $commodity_price_info['ac_dis_count'] ?? 0;

        $cb['ac_origin_dis_type'] = $commodity_price_info['ac_origin_dis_type'] ?? 0;
        $cb['ac_origin_dis_count'] = $commodity_price_info['ac_origin_dis_count'] ?? 0;

        if ($cb['commodity_dis_user_segment']) {
            $cb['ac_dis_type'] = 2;
            if ($type == 1) {
                $cb['ac_dis_count'] = $cb['dis_price'];
            } else {
                if ($use_discount) {
                    $cb['ac_dis_count'] = round($commodity_price_info['commodity_dis_original_price'] - $commodity_price_info['commodity_dis_segment_price'], 2);
                }
            }
        }

        return $this->setResponseData($cb)->send();
    }

    /**
     * @return
     * 确认更改规格
     */
    public function selectedSku(MaintainanceValidate $validate){
        $tmp_sum['full_sum_menoy'] = [];
        $tmp_sum['n_dis_count'] = [];
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("selectedSku")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $commodity_id = $requestData['commodity_id'] ?? 0;
        $commodity_set_id = $requestData['commodity_set_id'] ?? 0;
        $sku_id = $requestData['sku_id'] ?? 0;
        $num = $requestData['num'] ?? 1;
        //  $type = $requestData['type'] ?? 1;
        $num = intval($num);
        $dd_commodity_type = $requestData['dd_commodity_type'] ?? 1;
        $net_common_model = new \app\common\net_service\Common();
        $user_car_info = $net_common_model->getFriendBaseInfo($this->user);
        $dlr_code =  $user_car_info['dlr_code'];
        $test_vin = $requestData['test_vin'] ?? '';
        $test_18n = $requestData['test_18n'] ?? '';
        if(!empty($test_vin) && !empty($test_18n)){
            $user_car_info['vin'] = $test_vin;
            $user_car_info['car_config_code'] = $test_18n;
        }



        // $IRequestLogObj = new IRequestLog();
        // $ridd = $IRequestLogObj->insertGetId(array('module'=>'net-small','action'=>'Maintainance','function'=>'selectedSku','data'=>json_encode($requestData).'<====>'.json_encode($user_car_info)));
        $redis_key = $commodity_id.$commodity_set_id.$dd_commodity_type.$sku_id.$num.'_'.$this->brand;
        $data = redis($redis_key);
        if($this->is_redis ||  empty($data) || getRedisLock($redis_key.'-lock', mt_rand(500, 600))) {
            $dbCommoditySetSkuObj = new DbCommoditySetSku();
            $dbCommodityTypeObj = new DbCommodityType();
            $sku_list = $dbCommoditySetSkuObj->alias("a")
                ->join("t_db_commodity commodity", "commodity.id = a.commodity_id")
                ->join("t_db_commodity_flat flat", "flat.commodity_id = a.commodity_id  and flat.shelves_type = {$this->shelves_type}")
                ->join("t_db_commodity_sku b", "a.commodity_sku_id = b.id")
                // ->join("t_db_spec_value c", "c.is_enable=1 and find_in_set(c.id,b.sp_value_list) ","left")
                // ->join("t_db_spec d", "c.sp_id=d.id","left")
                ->join("t_e3s_part_car_series e", "find_in_set(e.part_no,b.sku_code) and e.car_config_code = '{$user_car_info['car_config_code']}'", "left")
                ->field("flat.full_wi_dis,flat.limit_wi_dis,a.price old_price,commodity.cover_image,commodity.work_hour_type,a.id set_sku_id,a.price set_sku_price,commodity.dd_commodity_type,flat.comm_type_id_str,flat.full_dis,flat.n_dis,flat.limit_dis,commodity.commodity_name,b.commodity_id,a.commodity_set_id,b.stock,b.id sku_id,a.commodity_id,e.wi_code,e.wi_qty,b.image,b.sp_value_list,a.price,b.maintain_q maintain_dis")
                ->where("a.id", $sku_id)->where(['a.is_enable' => 1, 'b.is_enable' => 1,  'flat.is_enable' => 1])
                ->find();
           // echo $dbCommoditySetSkuObj->getLastSql();exit;
            $spec_list = [];
            if(!empty($sku_list['sp_value_list'])){
                $spValueObj = new DbSpecValue();
                $value_list = $spValueObj->alias("a")->join("t_db_spec b","a.sp_id=b.id")->where("a.id in ({$sku_list['sp_value_list']})")->field("a.id value_id,a.sp_id,a.sp_value_name,b.sp_name")->select();
                foreach($value_list as $item){
                    $spec_list[] = ['sp_id' => $item['sp_id'], 'sp_name' => $item['sp_name'], 'value_id' => $item['value_id'], 'sp_value_name' => $item['sp_value_name']];
                }
            }
            if(!empty($test_vin) && !empty($test_18n)){
                $IRequestLogObj = new IRequestLog();
                $IRequestLogObj->insertGetId(array('module'=>'net-small','action'=>'Maintainance','function'=>'selectedSku','data'=>$dbCommoditySetSkuObj->getLastSql()));
            }
           // $spec_list = [];
            $data = [];
            $item = $sku_list;
           // foreach ($sku_list as $k => $item) {

            $data['wi_act_id'] = "";
            $data['wi_act_type_id'] = "";

            if(!empty($item['limit_wi_dis'])){
                $limit_wi_dis_arr = json_decode($item['limit_wi_dis'],true);
                $data['wi_act_id'] = $limit_wi_dis_arr[$this->channel_type][0];
                $data['wi_act_type_id'] = 1;
            }

            if(!empty($item['full_wi_dis'])){
                $full_wi_dis_arr = json_decode($item['full_wi_dis'],true);
                $data['wi_act_id'] = $full_wi_dis_arr[$this->channel_type][0];
                $data['wi_act_type_id'] = 3;
            }

            if(empty($item['image'])) $item['image'] = $item['cover_image'];
            $data['commodity_id'] = $item['commodity_id'];
            $data['commodity_name'] = $item['commodity_name'];
            $data['commodity_set_id'] = $item['commodity_set_id'];
            $data['work_hour_type'] = $item['work_hour_type'];
            $data['set_sku_price'] = $item['set_sku_price'];
            $data['price'] = $item['set_sku_price'];
            if($item['maintain_dis']){
                $data['old_price'] =  sprintf("%.2f",bcdiv($item['old_price'],$item['maintain_dis']/10,0));

            }else{
                $data['old_price'] =  $item['old_price'];
            }

            $data['stock'] = $item['stock'];
            $data['sku_id'] = $item['sku_id'];
            if(!empty($item['set_sku_id'])){
                $net_goods = new NetGoods();
                $data['gift_card_count'] =  $net_goods->checkGiftCard(json_encode([['sku_id'=>$item['set_sku_id']]]), $this->user,[], $this->channel_type);
            }
            $data['wi_code'] = empty($item['wi_code']) ? '' : $item['wi_code'];
            $data['wi_qty'] = empty($item['wi_qty']) ? 0 : $item['wi_qty'];
            $data['image'] = $item['image'];
            $data['n_dis'] = $item['n_dis'];
            $data['limit_dis'] = $item['limit_dis'];
            $data['full_dis'] = $item['full_dis'];
            $data['sp_value_list'] = $item['sp_value_list'];
            $data['dd_commodity_type'] = $item['dd_commodity_type'];
            $data['maintain_dis'] = $item['maintain_dis']==10?'':$item['maintain_dis'];
//                $spec_list[] = ['sp_id' => $item['sp_id'], 'sp_name' => $item['sp_name'], 'value_id' => $item['value_id'], 'sp_value_name' => $item['sp_value_name']];
           // }

            $data['spec_list'] = $spec_list;
            $product_type_list = $dbCommodityTypeObj->where(['is_enable' => 1, 'level' => 2])->field('id,comm_type_name')->select();
            foreach ($product_type_list as $product_type_item) {
                $product_type[$product_type_item['id']] = $product_type_item['comm_type_name'];
            }
            //取title
            $data['dd_commodity_type_name'] = '';
            if (isset($data['dd_commodity_type']) && $data['dd_commodity_type'] > 0) {
                $data['dd_commodity_type_name'] = $dbCommoditySetSkuObj->getDdType($data['dd_commodity_type']);
            }
            $data['set_sku_id'] = $sku_id;
            $data['count'] = $num;
            $data['dis_price'] = 0;
            $data['selected'] = 0;
            $data['work_time_price'] = 0;
            $data['old_work_time_price'] = 0;
            $data['work_time_dis_price'] = 0;

            $data['commodity_dis_user_segment'] = 0;
            $data['commodity_dis_act_user_segment'] = 0;
            $data['commodity_dis_label'] = '';
            $data['commodity_dis_label_cn'] = '';
            $data['set_sku_price'] = $data['set_sku_price'] ?? 0;
            $data['price'] = $data['price'] ?? 0;

            $data['commodity_dis_segment_price'] = $data['set_sku_price'];
            $data['commodity_dis_original_price'] = $data['set_sku_price'];
            $data['commodity_dis_act_segment_price'] = $data['set_sku_price'];
            $data['commodity_dis_act_original_price'] = $data['set_sku_price'];

            $net_goods = new NetGoods();
            $commodity_dis_info = $net_goods->getCommoditySegmentDiscount($commodity_id);
            //跟购物车一样
            $can_use_card = 0;

            $card_list = $net_goods->card_get_use($item['commodity_set_id'],[],$this->user,$this->channel_type);
//            $card_list =  $net_goods->user_can_card($item['commodity_set_id'],$this->user,$this->channel_type,[]);
            if($card_list){
                $card_list = $card_list['all_card'];
                foreach ($card_list as $v1){
                    if($v1['can_card']==1){
                        $can_use_card=2;
                    }
                }
                foreach ($card_list as $v){
                    if($v['can_card']==1){
                        if($v['set_sku_arr']){
                            if(in_array($sku_id,$v['set_sku_arr'])){
                                $can_use_card=1;
                            }
                        }else{
                            $can_use_card=1;
                        }
                    }

                }
            }
            $data['can_use_card'] = $can_use_card;

            if ($commodity_dis_info) {
                $data['set_sku_price'] = $net_goods->getCommodityDisFinalPrice($commodity_dis_info, $data['price']);
                $data['commodity_dis_user_segment'] = $commodity_dis_info['user_segment'];
                $data['commodity_dis_label'] = get_user_segment_label($commodity_dis_info['user_segment']);
                $data['commodity_dis_label_cn'] = get_user_segment_label_cn($data['commodity_dis_label']);
                $data['dis_price'] += $data['price'] - $data['set_sku_price'];

                $data['commodity_dis_segment_price'] = $data['set_sku_price'];
                $data['commodity_dis_act_segment_price'] = $data['set_sku_price'];
            }

            $now_date = date("Y-m-d H:i:s", time());
            $bj_data = [];
            if (!empty($data['wi_code']) && !empty($data['work_hour_type'])) {
                $bj_data['work_hour_type'] = $data['work_hour_type'];
                $bj_data['dlr_code'] = $dlr_code;
                $bj_data['vin'] = $user_car_info['vin'];
                $price = $dbCommoditySetSkuObj->getTimePrice($bj_data);
                $data['work_time_price'] = $data['old_work_time_price'] = $price * $data['wi_qty'] ;
            }
            $ac_list = [];
            //限时折扣工显示划线价
            if (!empty($data['limit_dis'])) {
                $limit_data = json_decode($data['limit_dis'], true);

                $limit_id = $limit_data[$this->channel_type][0];
                $dbLimitDiscountObj = new DbLimitDiscount();
                //所有商品活动
                $limit_tmp = [];
                $activity_limit_info = $dbLimitDiscountObj->alias('a')
                    ->join('t_db_limit_discount_commodity b','a.id = b.limit_discount_id')
                    ->where(['a.id'=>$limit_data[$this->channel_type][0],'b.commodity_id'=>$commodity_id])
                    ->where("a.start_time <='{$now_date}' and '{$now_date}' <= a.end_time")
                    ->where(['a.is_enable'=>1])->field("a.id,a.title,a.dis_type,a.discount,b.sku_price,a.discount_type,b.sku_dis,a.user_segment,a.user_segment_options")
                    ->find();
                $sku_id_list = array_keys(json_decode_assoc($activity_limit_info['sku_dis']));
                if(in_array($sku_id,$sku_id_list)){
                    $limit_tmp['id'] = $activity_limit_info['id'];
                    $limit_tmp['title'] = $activity_limit_info['title'];
                    $limit_tmp['dis_type'] = $activity_limit_info['dis_type'];
                    $limit_tmp['rule'] = $activity_limit_info['discount'];
                    $limit_tmp['discount_type'] = $activity_limit_info['discount_type'];
                    $limit_tmp['type'] = 1;
//                    $data['old_price'] = $data['set_sku_price'];
                    $is_limit = $dbCommoditySetSkuObj->isinlimit($limit_id,$this->user,$commodity_id);
                    $limit_tmp['is_limit'] = $is_limit['is_limit'];
                    $limit_tmp['purchase_number'] = $is_limit['purchase_number'];
                    $limit_tmp['purchase_limit_number'] = $is_limit['purchase_limit_number'];

                    $limit_tmp['commodity_dis_act_user_segment'] = $activity_limit_info['user_segment'];
                    $limit_tmp['commodity_dis_label'] = get_user_segment_label($activity_limit_info['user_segment']);
                    $limit_tmp['commodity_dis_label_cn'] = get_user_segment_label_cn($limit_tmp['commodity_dis_label']);

                    if(in_array($limit_tmp['discount_type'],[1,3])){
                        $limit_tmp['ac_dis_type'] = $activity_limit_info['dis_type'];
                        $all_limit_dis_arr = json_decode($activity_limit_info['sku_dis'],true);
                        $limit_tmp['ac_dis_count'] = isset($all_limit_dis_arr[$data['set_sku_id']])?$all_limit_dis_arr[$data['set_sku_id']]:0;
                    }

                    //只有限时是这样
                    if($limit_tmp['is_limit'] == 1){
                        if(!empty($activity_limit_info)){
                            $data = $dbCommoditySetSkuObj->activity($this->user, $data,$this->shelves_type, $this->channel_type,1, $bj_data,"selectedSku");
                        }
                        $ac_list[] = $limit_tmp;
                    }
                }
            }

            if (!empty($data['full_dis'])) {
                $dbFullDiscountObj = new DbFullDiscount();
                $full_tmp = [];
                $full_data = json_decode($data['full_dis'], true);
                $full_id = $full_data[$this->channel_type][0] ?? 0;
                //所有商品满减活动
                if(!empty($full_id)){
                    $activity_full_info = $dbFullDiscountObj->where('id', $full_id)->where("start_time <='{$now_date}' and '{$now_date}' <= end_time")->where(['is_enable' => 1])->field('id,activity_title title,full_discount_rules,user_segment,user_segment_options')->find();
                    $full_tmp['type'] = 2;
                    $full_tmp['id'] = $activity_full_info['id'];
                    $full_tmp['title'] = $activity_full_info['title'];
                    $is_limit = $dbCommoditySetSkuObj->isin($full_id,$this->user,1);
                    $full_tmp['is_limit'] = $is_limit['is_limit'];
                    $full_tmp['purchase_number'] = $is_limit['purchase_number'];
                    $full_tmp['purchase_limit_number'] = $is_limit['purchase_limit_number'];

                    $full_tmp['commodity_dis_act_user_segment'] = $activity_full_info['user_segment'];
                    $full_tmp['commodity_dis_label'] = get_user_segment_label($activity_full_info['user_segment']);
                    $full_tmp['commodity_dis_label_cn'] = get_user_segment_label_cn($full_tmp['commodity_dis_label']);

                    $ac_list[] = $full_tmp;
                }
            }

            if (!empty($data['n_dis'])) {
                $ndis_data = json_decode($data['n_dis'], true);
                $dbNDiscountObj = new DbNDiscount();
                $ndis_id = $ndis_data[$this->channel_type][0] ?? 0;
                $n_tmp = [];
                if(!empty($ndis_id)) {
                    $activity_n_info = $dbNDiscountObj->alias("a")->join("t_db_n_discount_info b", "a.id=b.n_id")->where('a.id', $ndis_id)->where("a.start_time <='{$now_date}' and '{$now_date}' <= a.end_time")->where(['a.is_enable' => 1])->field("a.id,a.title,b.piece,b.discount,a.user_segment,a.user_segment_options")->find();
                    $n_tmp['type'] = 3;
                    $n_tmp['id'] = $activity_n_info['id'];
                    $n_tmp['title'] = $activity_n_info['title'];
                    $is_limit = $dbCommoditySetSkuObj->isin($ndis_id,$this->user,3);
                    $n_tmp['is_limit'] = $is_limit['is_limit'];
                    $n_tmp['purchase_number'] = $is_limit['purchase_number'];
                    $n_tmp['purchase_limit_number'] = $is_limit['purchase_limit_number'];

                    $n_tmp['commodity_dis_act_user_segment'] = $activity_n_info['user_segment'];
                    $n_tmp['commodity_dis_label'] = get_user_segment_label($activity_n_info['user_segment']);
                    $n_tmp['commodity_dis_label_cn'] = get_user_segment_label_cn($n_tmp['commodity_dis_label']);
                    $ac_list[] = $n_tmp;
                }
            }

            if (!empty($data['wi_act_id']) && $data['wi_act_type_id'] == 1){
                $data = $dbCommoditySetSkuObj->onlyWorkTime($data,$data['wi_act_id'],$num);
            }

            if (!empty($data['wi_act_id']) && $data['wi_act_type_id'] == 3){
                $full_wi_id = $data['wi_act_id'] ?? 0;
                $full_id = $data['full_dis'] ?? 0;
                if( $full_id > 0 && $full_wi_id >0  && $full_id == $full_wi_id ){
                    $full_save_activity = 1;
                }
                if($full_id >0 && $full_wi_id ==0){
                    $full_save_activity = 2;
                }
                if($full_id ==0 && $full_wi_id  > 0){
                    $full_save_activity = 3;
                }
                $data = $dbCommoditySetSkuObj->onlyFullWorkTime($tmp_sum,$data,$data['wi_act_id'],$full_save_activity,$num);
            }

            $data['work_time_price'] = round($data['work_time_price'] * $num , 2);
            $data['old_work_time_price'] = round($data['old_work_time_price'] * $num, 2);
            $data['work_time_dis_price'] = round($data['work_time_dis_price'] * $num, 2);
            $data['ac_list'] = $ac_list;
//            redis($redis_key, $data, 8640000);
            if ($data['commodity_dis_user_segment']) {
                $data['ac_dis_type'] = 2;
                $data['ac_dis_count'] = $data['dis_price'];
            }

        }
        $data['user_id'] = $this->user_id;
        return $this->setResponseData($data)->send();
    }


    public function work_time($activity_id){
        $dbFullDiscountObj = new DbFullDiscount();
        $dbNDiscountObj = new DbNDiscount();
        $dbLimitDiscountObj = new DbLimitDiscount();
        $activity_all = [];
        $now_date = date("Y-m-d H:i:s", time());
        $limit_tmp = [];
        $full_tmp = [];
        $n_tmp = [];
        $activity_limit_list = $dbLimitDiscountObj->whereIn('id', $activity_id)->where("start_time <='{$now_date}' and '{$now_date}' <= end_time")->where(['is_enable' => 1])->field("id,title,dis_type,discount,discount_type")->select();
        foreach ($activity_limit_list as $item) {
            $limit_tmp[$item['id']]['id'] = $item['id'];
            $limit_tmp[$item['id']]['title'] = $item['title'];
            $limit_tmp[$item['id']]['dis_type'] = $item['dis_type'];
            $limit_tmp[$item['id']]['rule'] = $item['discount'];
            $limit_tmp[$item['id']]['discount_type'] = $item['discount_type'];
            $limit_tmp[$item['id']]['type'] = 1;
        }
        //所有商品满减活动
        $activity_full_list = $dbFullDiscountObj->whereIn('id', $activity_id)->where("start_time <='{$now_date}' and '{$now_date}' <= end_time")->where(['is_enable' => 1])->field('id,activity_title title,full_discount_rules')->select();
        foreach ($activity_full_list as $item) {
            $full_tmp[$item['id']]['type'] = 2;
            $full_tmp[$item['id']]['id'] = $item['id'];
            $full_tmp[$item['id']]['title'] = $item['title'];
        }
        //所有商品n件活动
        $activity_n_list = $dbNDiscountObj->alias("a")->join("t_db_n_discount_info b", "a.id=b.n_id")->whereIn('a.id', $activity_id)->where("a.start_time <='{$now_date}' and '{$now_date}' <= a.end_time")->where(['a.is_enable' => 1])->field("a.id,a.title,b.piece,b.discount")->select();
        foreach ($activity_n_list as $item) {
            $n_tmp[$item['id']]['type'] = 3;
            $n_tmp[$item['id']]['id'] = $item['id'];
            $n_tmp[$item['id']]['title'] = $item['title'];
        }
        $activity_all=['ndis'=>$n_tmp,'full'=>$full_tmp,'limit'=>$limit_tmp];
        return $activity_all;
    }
    /**
     *获取备件sku列表-- 配件规格弹窗 get-sku-part
     */
    public function getPartSkuByCommodityid(MaintainanceValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("getSkuPart")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $commodity_id = $requestData['commodity_id'] ?? 0;
        $dd_commodity_type = $requestData['dd_commodity_type'] ?? 0;

        $net_common_model = new \app\common\net_service\Common();
        $user_car_info = $net_common_model->getFriendBaseInfo($this->user);
        $test_vin = $requestData['test_vin'] ?? '';
        $test_18n = $requestData['test_18n'] ?? '';
        if(!empty($test_vin) && !empty($test_18n)){
            $user_car_info['vin'] = $test_vin;
            $user_car_info['car_config_code'] = $test_18n;
        }
        // $IRequestLogObj = new IRequestLog();
        // $ridd = $IRequestLogObj->insertGetId(array('module'=>'net-small','action'=>'Maintainance','function'=>'getPartSkuByCommodityid','data'=>json_encode($requestData).'<====>'.json_encode($user_car_info)));

        $type = $requestData['type'] ?? 1;
        $use_discount = $requestData['use_discount'] ?? 1;

        $default_num = $requestData['num'] ?? 1;
        $default_num = intval($default_num);
        $default_dlr_code = $requestData['dlr_code'] ?? $user_car_info['dlr_code'];
        $default_dlr_name = $requestData['dlr_name'] ?? $user_car_info['dlr_name'];
        $redis_key = $user_car_info['vin'].$commodity_id.$dd_commodity_type.$default_num.$default_dlr_code.'6_'.$this->brand;
        $sku_cb_list = redis($redis_key);

        if($this->is_redis || empty($sku_cb_list) || getRedisLock($redis_key.'-lock', mt_rand(500, 600))) {
            $dbBdpRecommendObj = new DbBdpRecommend();
            $dbCommodityObj = new DbCommodity();
            $dbCommoditySetSkuObj = new DbCommoditySetSku();
            $e3sPartObj = new E3sPartCarSeries();
            $recommend_info = $dbBdpRecommendObj->where(['vin' => $user_car_info['vin']])->find();
            $set_sku_id_str = ''; //找出推荐过来的可用的set_sku_id
            $sku_cb_list = [];
            $sku_cb_list_tmp = [];
            $price_image_arr = [];
            $bj_data = [];
            $sku_val_list_tmp = [];
            if (!empty($recommend_info)) {
                //取出所有商品id
                $recommend_list = json_decode($recommend_info['goods'], true);
                foreach ($recommend_list as $item) {
                    if ($item['dd_commodity_type'] == $dd_commodity_type && $item['commodity_id'] == $commodity_id) {
                        $set_sku_id_str = $item['set_sku_id'];
                    }
                }
                //这是备件
                $is_store = 1;
                if (!empty($set_sku_id_str)) {
                    $set_sku_id_arr = explode(',', $set_sku_id_str);
                    ///////////////////////////////////
                    $where_data = ['commodity.is_enable'=>1,'b.is_enable'=>1,'set.is_enable'=>1,'flat.is_enable'=>1,'commodity.id'=>$commodity_id];
                    $where_data[]=['exp',"(find_in_set('{$this->channel_type}',flat.up_down_channel_dlr))"];
                    if($default_dlr_code && $default_dlr_code<>'V0000'){
                        $where_data[] = ['exp', " (find_in_set('{$default_dlr_code}',b.relate_dlr_code) || b.relate_dlr_code='' || b.relate_dlr_code is null ) "];//KV 关联了价格
                    }
                    // ->whereIn("a.id", $set_sku_id_arr)
                    $sp_arr_list = $dbCommoditySetSkuObj->alias("a")
                        ->join("t_db_commodity commodity", "commodity.id = a.commodity_id")
                        ->join("t_db_commodity_sku b", "a.commodity_sku_id = b.id")
                        ->join("t_db_commodity_set set", "a.commodity_set_id = set.id")
                        ->join("t_db_commodity_flat flat", "flat.commodity_id = a.commodity_id and flat.shelves_type = {$this->shelves_type}")
                        ->field("a.id as set_sku_id,a.price as set_sku_price,flat.limit_wi_dis,b.sku_code,flat.limit_dis,set.is_store,commodity.work_hour_type,commodity.cover_image,commodity.commodity_name,a.commodity_set_id,b.stock,a.id sku_id,a.commodity_id,b.image,b.sp_value_list,a.price")
                        ->where($where_data)
                        ->where("find_in_set('{$user_car_info['car_config_code']}',b.relate_car_18n)")
                       ->order("a.price")->group("b.sp_value_list")->select();
//                    print_json($sp_arr_list);
//                    echo $dbCommoditySetSkuObj->getLastSql();exit;
                    if(!empty($test_vin) && !empty($test_18n)){
                        $IRequestLogObj = new IRequestLog();
                        $IRequestLogObj->insertGetId(array('module'=>'net-small','action'=>'Maintainance','function'=>'getPartSkuByCommodityid','data'=>$dbCommoditySetSkuObj->getLastSql()));
                    }
                    $net_goods = new NetGoods();
                    $commodity_dis_info = $net_goods->getCommoditySegmentDiscount($commodity_id);
                    $goods_info = $net_goods->getCommodityInfo($commodity_id, $this->channel_type, 0, 0, 0, 3, 0, $this->user, $default_dlr_code, '', 0, 0, 0, 1, 2, 1, '');// 备件不用vin , $user_car_info['vin']
                    $sku_list = $goods_info['sku_list'];
                    foreach ($sp_arr_list as $sp_arr_item) {

                        $is_store = $sp_arr_item['is_store'];
                        $e3sPartime =  $e3sPartObj->where(["car_config_code" =>$user_car_info['car_config_code'],"part_no"=>$sp_arr_item['sku_code']])->field("wi_qty,wi_code")->find();
                        $wi_code =  '';
                        $wi_qty =  0;
                        $sp_arr_item['work_time_price'] = 0;
                        $sp_arr_item['old_work_time_price'] = 0;

                        $sp_arr_item['commodity_dis_user_segment'] = 0;
                        $sp_arr_item['commodity_dis_act_user_segment'] = 0;

                        $sp_arr_item['commodity_dis_original_price'] = $sp_arr_item['set_sku_price'];
                        $sp_arr_item['commodity_dis_segment_price'] = $sp_arr_item['set_sku_price'];

                        $sp_arr_item['commodity_dis_label'] = '';
                        $sp_arr_item['commodity_dis_label_cn'] = '';
                        $sp_arr_item['dis_price'] = 0;

                        if ($use_discount && $commodity_dis_info) {
                            $sp_arr_item['set_sku_price'] = $net_goods->getCommodityDisFinalPrice($commodity_dis_info, $sp_arr_item['set_sku_price']);
                            $sp_arr_item['commodity_dis_user_segment'] = $commodity_dis_info['user_segment'];
                            $sp_arr_item['commodity_dis_label'] = get_user_segment_label($commodity_dis_info['user_segment']);
                            $sp_arr_item['commodity_dis_label_cn'] = get_user_segment_label_cn($sp_arr_item['commodity_dis_label']);

                            $sp_arr_item['commodity_dis_segment_price'] = $sp_arr_item['set_sku_price'];
                            $sp_arr_item['dis_price'] += $sp_arr_item['price'] - $sp_arr_item['set_sku_price'];
                        }

                        if(!empty($sp_arr_item['work_hour_type'])){
                            $wi_code = $e3sPartime['wi_code'] ?? '';
                            $wi_qty = $e3sPartime['wi_qty'] ?? 0;
                            $bj_data = ['dlr_code' => $default_dlr_code, 'vin' => $user_car_info['vin']];
                            $bj_data['work_hour_type'] = $sp_arr_item['work_hour_type'];
                            if($wi_qty > 0){
                                $price = $dbCommoditySetSkuObj->getTimePrice($bj_data);
                                $sp_arr_item['work_time_price'] = $sp_arr_item['old_work_time_price'] = round($price * $wi_qty,2);
                            }
                        }
                        $sp_arr_item = $dbCommoditySetSkuObj->activity($this->user, $sp_arr_item, $this->shelves_type, $this->channel_type, $type, $bj_data);
                        if (empty($sp_arr_item['image'])) $sp_arr_item['image'] = $sp_arr_item['cover_image'];
                        $card_can_get=0;
                        $card_can_use=0;
                        if(isset($sku_list[$sp_arr_item['sp_value_list']])){
                            $sku_list_arr = $sku_list[$sp_arr_item['sp_value_list']];
                            $card_can_get = $sku_list_arr['card_can_get'];
                            $card_can_use = $sku_list_arr['card_can_use'];
                        }
                        $price_image_arr[$sp_arr_item['sp_value_list']] = [
                            'commodity_name' => $sp_arr_item['commodity_name'],
                            'image' => $sp_arr_item['image'],
                            'price' => $sp_arr_item['set_sku_price'],
                            'old_price'=>$sp_arr_item['price'],
                            'sku_id' => $sp_arr_item['sku_id'],
                            'stock' => $sp_arr_item['stock'],
                            'commodity_set_id' => $sp_arr_item['commodity_set_id'],
                            'commodity_id' => $sp_arr_item['commodity_id'],
                            'work_time_price' => $sp_arr_item['work_time_price'],
                            'work_time_code' => $wi_code,
                            'work_time_number' => $wi_qty,

                            'commodity_dis_user_segment' => $sp_arr_item['commodity_dis_user_segment'],
                            'commodity_dis_act_user_segment' => $sp_arr_item['commodity_dis_act_user_segment'],
                            'commodity_dis_label' => $sp_arr_item['commodity_dis_label'],
                            'commodity_dis_label_cn' => $sp_arr_item['commodity_dis_label_cn'],
                            'card_can_get' => $card_can_get,
                            'card_can_use' => $card_can_use
                        ];
                        $price_sku_str_arr[] = $sp_arr_item['sp_value_list'];
                    }
                }

                if(!empty($price_sku_str_arr)){
                    $value_str = implode(',', $price_sku_str_arr);
                    $sp_ec_obj = new DbSpecValue();
                    $sp_list = $sp_ec_obj->alias("a")->join("t_db_spec b", "a.sp_id=b.id")->whereIn("a.id", $value_str)->field("a.id value_id,a.sp_value_name,b.id sp_id,b.sp_name")->order("b.sort,a.id")->select();
                    $sku_cb_list_tmp['sp_value_arr'] = [];
                    $sku_cb_list['sp_value_arr'] = [];
                    foreach ($sp_list as $sku_item) {
                        $sku_cb_list_tmp['sp_value_arr'][$sku_item['sp_id']] = [
                            'sp_id' => $sku_item['sp_id'],
                            'sp_name' => $sku_item['sp_name'],
                            'val' => []
                        ];
                    }

                    foreach ($sp_list as $sku_item) {
                        $sku_cb_list_tmp['sp_value_arr'][$sku_item['sp_id']]['val'][] = ['sp_id' => $sku_item['sp_id'], 'value_name' => $sku_item['sp_value_name'], 'value_id' => $sku_item['value_id']];
                    }

                    foreach ($sku_cb_list_tmp['sp_value_arr'] as $key => $item) {
                        $sku_cb_list['sp_value_arr'][] = [
                            'sp_id' => $item['sp_id'],
                            'sp_name' => $item['sp_name'],
                            'val' => $item['val'],
                        ];
                    }
                }


                $sku_cb_list['price_image'] = $price_image_arr;
                $sku_cb_list['num'] = $default_num;
                $sku_cb_list['is_mate'] = 1;
                if($is_store == 1){
                    $sku_cb_list['mail_method'] = 1;
                }else{
                    $sku_cb_list['mail_method'] =2 ;
                }

                $sku_cb_list['commodity_id'] = $commodity_id;
                $sku_cb_list['dlr_code'] = $default_dlr_code;
                $sku_cb_list['dlr_name'] = $default_dlr_name;
//                redis($redis_key, $sku_cb_list, 8640000);
                return $this->setResponseData($sku_cb_list)->send();
            }
        }else{
            return $this->setResponseData($sku_cb_list)->send();
        }
        return $this->setResponseData([])->send();
    }


    /**
     * @title 新版获取车龄接口
     * @description 根据传的vin获取用户的车龄
     * @description <br />返回结果例子：{"car_age": 5.85}
     * @param name:vin type:string require:1 default: other: desc:vin码
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据@!
     * @data car_age:车龄
     *
     * <AUTHOR>
     * @url /net-small/friend/vehicle-age2
     * @method GET
     */
    public function vehicleAge2($vin){
        $url = config('friend.vehicle_age').'/postdata/DNDC_ONLINESHOP/DNDC_GET_CAR_AGE';
        $car_age = json_decode($this->_get_maintain($url, $vin), true);
        $me_car_age = 0;
        if ($car_age['result'] == 0) {
            $me_car_age = $car_age['body'][0]['CAR_AGE'] ?? 0;
        }
        return $me_car_age;
    }


    /**
     *获取保养sku列表--规格弹窗
     */
    public function getComboSkuByCommodityid(MaintainanceValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("getSkuCombo")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $commodity_id = $requestData['commodity_id'];
        $dd_commodity_type = $requestData['dd_commodity_type'];
        $type = $requestData['type'] ?? 0;
        $dbBdpRecommendObj = new DbBdpRecommend();
        $dbCommoditySetSkuObj = new DbCommoditySetSku();
        $net_common_model = new \app\common\net_service\Common();
        $user_car_info = $net_common_model->getFriendBaseInfo($this->user);
        // $IRequestLogObj = new IRequestLog();
        // $ridd = $IRequestLogObj->insertGetId(array('module'=>'net-small','action'=>'Maintainance','function'=>'getComboSkuByCommodityid','data'=>json_encode($requestData).'<====>'.json_encode($user_car_info)));
        $use_discount = $requestData['use_discount'] ?? 1;
        $test_vin = $requestData['test_vin'] ?? '';
        $test_18n = $requestData['test_18n'] ?? '';
        if(!empty($test_vin) && !empty($test_18n)){
            $user_car_info['vin'] = $test_vin;
            $user_car_info['car_config_code'] = $test_18n;
        }
        $default_dlr_code = $requestData['dlr_code'] ?? $user_car_info['dlr_code'];
        $default_dlr_name = $requestData['dlr_name'] ?? $user_car_info['dlr_name'];
        $code18n = $user_car_info['car_config_code'];
        $redis_key = $user_car_info['vin'].$commodity_id.$dd_commodity_type.$default_dlr_code.'_'.$this->brand;
        $sku_cb_list = redis($redis_key);
        if($this->is_redis || empty($sku_cb_list) || getRedisLock($redis_key.'-lock', mt_rand(500, 600))) {
            $limit_id = 0;
            $price_image_arr = [];
            $price_sku_str_arr = [];
            $recommend_info = $dbBdpRecommendObj->where(['vin' => $user_car_info['vin']])->find();
//            dd($dbBdpRecommendObj->getLastSql());
            $set_sku_id_str = ''; //找出推荐过来的可用的set_sku_id
            $sku_cb_list = [];
            $sku_cb_list['sp_value_arr'] = [];
            $sku_cb_list_tmp = [];
            $sku_val_list_tmp = [];
            $sp_arr_list = [];
            $val = [];
            if (!empty($recommend_info)) {
                //取出所有商品id
                $recommend_list = json_decode($recommend_info['goods'], true);
                foreach ($recommend_list as $item) {
                    if ($item['dd_commodity_type'] == $dd_commodity_type && $item['commodity_id'] == $commodity_id) {
                        $set_sku_id_str = $item['set_sku_id'];
                    }
                }

                if (!empty($set_sku_id_str)) {
                    $set_sku_id_arr = explode(',', $set_sku_id_str);
                    $dlr_level='';
                    if(in_array($dd_commodity_type,[1,3])){
                        $dlr_level='A';
                        $dd_dlr_code = $default_dlr_code;
                        if(!empty($dd_dlr_code) && $dd_dlr_code!='V0000'){
                            $dlrObj = new DbDlr();
                            $dlr_info = $dlrObj->alias("a")->join("t_db_area b", "a.area_id=b.area_id")->where(['a.dlr_code' => $dd_dlr_code, 'a.is_enable' => 1])->field("b.city_type,a.dlr_name,b.brand_city_type")->find();
                            if(in_array($this->channel_type,['QCSM','QCAPP'])){
                                $dlr_level = $dlr_info['brand_city_type'];
                            }else{
                                $dlr_level = $dlr_info['city_type'];
                            }
                            $is_by_pp=1;
                        }
                    }
                    if(in_array($dd_commodity_type,[1,3,4,41,12])){
                        $data_car_where = ['b.is_enable' => 1, 'flat.is_enable' => 1, 'sku.is_enable' => 1];
                        if ($dlr_level) {
                            $data_car_where['b.city_type']= $dlr_level;
                        }
                        $user_oil_type = $this->user['18_oil_type'];
                        if(in_array($this->channel_type,['QCSM','QCAPP'])){
                            if($this->user['18_oil_type']==4){
                                $user_oil_type = '3.5,4';
                            }
                        }
                        $data_car_where['sku.oli_liters']= ['in',$user_oil_type];;//必须匹配油型升数
//                        $data_car_where .= sprintf(" and ( b.oli_liters in(%s) )",$user_oil_type);//必须匹配油型升数
                        $sp_arr_list = $dbCommoditySetSkuObj->alias("b")
                            ->join("t_db_commodity c", "c.id=b.commodity_id")
                            ->join("t_db_commodity_sku sku", "sku.id=b.commodity_sku_id")
                            ->join("t_db_commodity_flat flat", "flat.commodity_id = b.commodity_id and flat.shelves_type = {$this->shelves_type}")
                            ->where($data_car_where)
                            ->where("b.stock >0")
                            ->whereIn('b.id', $set_sku_id_arr)
                            ->field("b.price set_sku_price,b.id as set_sku_id,flat.limit_dis,b.stock,sku.sp_value_list sku_str,c.cover_image,sku.sp_value_list,sku.image,c.commodity_name,sku.price price,b.id as sku_id,c.id commodity_id,b.commodity_set_id,sku.maintain_q maintain_dis")
                            ->order("b.price")->select();
                    }
//                    //专题类型--old
//                    if ($dd_commodity_type == 1) { //老友惠
//                        $ac_by_price_obj = new AcByPrice();
//                        $sp_arr_list = $ac_by_price_obj->alias("p")
//                            ->join("t_db_commodity_set_sku b", "p.set_sku_id=b.id")
//                            ->join("t_db_commodity c", "c.id=p.goods_id")
//                            ->join("t_db_commodity_sku sku", "sku.id=b.commodity_sku_id")
//                            ->join("t_db_commodity_flat flat", "flat.commodity_id = p.goods_id and flat.shelves_type = {$this->shelves_type}")
//                            ->where(['b.is_enable' => 1, 'flat.is_enable' => 1, 'sku.is_enable' => 1])
//                            ->where("b.stock >0")
//                            ->whereIn('set_sku_id', $set_sku_id_arr)
//                            ->field("p.sale_price set_sku_price,b.id as set_sku_id,flat.limit_dis,b.stock,p.sku_str,c.cover_image,sku.image,c.commodity_name,p.sale_price price,p.set_sku_id as sku_id,c.id commodity_id,b.commodity_set_id,sku.maintain_q maintain_dis")
//                            ->order("p.sale_price")->select();
//                    }
//                    //心悦
//                    if ($dd_commodity_type == 3) {
//                        $where = [
//                            'c.id' => $commodity_id,
//                            'c.dd_commodity_type' => 3,
//                            'p.litre' => $this->user['18_oil_type'] . 'L',
//                        ];
//                        $ac_by_price_obj = new AcByPrice();
//                        $sp_arr_list = $ac_by_price_obj->alias("p")
//                            ->join("t_db_commodity_set_sku b", "p.set_sku_id=b.id")
//                            ->join("t_db_commodity c", "c.id=p.goods_id")
//                            ->join("t_db_commodity_sku sku", "sku.id=b.commodity_sku_id")
//                            ->join("t_db_commodity_flat flat", "flat.commodity_id = p.goods_id and flat.shelves_type = {$this->shelves_type}")
//                            ->where(['b.is_enable' => 1, 'flat.is_enable' => 1, 'sku.is_enable' => 1])
//                            ->where("b.stock >0")
//                            ->where($where)
//                            ->whereIn('set_sku_id', $set_sku_id_arr)
//                            ->field("p.sale_price set_sku_price,b.id as set_sku_id,flat.limit_dis,b.stock,p.sku_str,c.cover_image,sku.image,c.commodity_name,p.sale_price price,p.set_sku_id as sku_id,c.id commodity_id,b.commodity_set_id,sku.maintain_q maintain_dis")
//                            ->order("p.sale_price")->select();
//                        //  echo $ac_by_price_obj->getLastSql();exit;
//                    }
//                    //五年双保
//                    if ($dd_commodity_type == 4 || $dd_commodity_type == 41) {
//                        $ac_by_price_obj = new AcByPrice();
//                        $sp_arr_list = $ac_by_price_obj->alias("p")
//                            ->join("t_db_commodity_set_sku b", "p.set_sku_id=b.id")
//                            ->join("t_db_commodity c", "c.id=p.goods_id")
//                            ->join("t_db_commodity_sku sku", "sku.id=b.commodity_sku_id")
//                            ->join("t_db_commodity_flat flat", "flat.commodity_id = p.goods_id and flat.shelves_type = {$this->shelves_type}")
//                            ->where(['b.is_enable' => 1, 'flat.is_enable' => 1,])
//                            ->where("b.stock >0")
//                            ->where(['p.litre' =>$this->user['18_oil_type'].'L'])
//                            ->whereIn('set_sku_id', $set_sku_id_arr)
//                            ->field("p.sale_price set_sku_price,b.id as set_sku_id,flat.limit_dis,b.stock,p.sku_str,c.cover_image,sku.image,c.commodity_name,p.sale_price price,p.set_sku_id as sku_id,c.id commodity_id,b.commodity_set_id,sku.maintain_q maintain_dis")
//                            ->order("p.sale_price")->select();
//                    }

                    if(!empty($test_vin) && !empty($test_18n)){
                        $IRequestLogObj = new IRequestLog();
                        $IRequestLogObj->insertGetId(array('module'=>'net-small','action'=>'Maintainance','function'=>'getComboSkuByCommodityid','data'=>$dbCommoditySetSkuObj->getLastSql()));
                    }

                    $net_goods = new NetGoods();
                    $commodity_dis_info = $net_goods->getCommoditySegmentDiscount($commodity_id);
                    $goods_info = $net_goods->getCommodityInfo($commodity_id, $this->channel_type, 0, 0, 0, 3, 0, $this->user, '', '', 0, 0, 0, 1, 2, 1, '', $user_car_info['vin']);
                    $sku_list = $goods_info['sku_list'];

                    // dd($sp_arr_list);
                    //////////////////////////////////////////////////////////////
                    foreach ($sp_arr_list as $sp_arr_item) {
                        $card_can_get=0;
                        $card_can_use=0;
                        if(isset($sku_list[$sp_arr_item['sp_value_list']])){
                            $sku_list_arr = $sku_list[$sp_arr_item['sp_value_list']];
                            $card_can_get = $sku_list_arr['card_can_get'];
                            $card_can_use = $sku_list_arr['card_can_use'];
                        }
                        $sp_arr_item['old_price'] = $sp_arr_item['price'];

                        $sp_arr_item['commodity_dis_user_segment'] = 0;
                        $sp_arr_item['commodity_dis_act_user_segment'] = 0;

                        $sp_arr_item['commodity_dis_original_price'] = $sp_arr_item['set_sku_price'];
                        $sp_arr_item['commodity_dis_segment_price'] = $sp_arr_item['set_sku_price'];

                        $sp_arr_item['commodity_dis_label'] = '';
                        $sp_arr_item['commodity_dis_label_cn'] = '';
                        $sp_arr_item['dis_price'] = 0;

                        if ($use_discount && $commodity_dis_info) {
                            $sp_arr_item['set_sku_price'] = $net_goods->getCommodityDisFinalPrice($commodity_dis_info, $sp_arr_item['set_sku_price']);
                            $sp_arr_item['commodity_dis_user_segment'] = $commodity_dis_info['user_segment'];
                            $sp_arr_item['commodity_dis_label'] = get_user_segment_label($commodity_dis_info['user_segment']);
                            $sp_arr_item['commodity_dis_label_cn'] = get_user_segment_label_cn($sp_arr_item['commodity_dis_label']);

                            $sp_arr_item['commodity_dis_segment_price'] = $sp_arr_item['set_sku_price'];
                            $sp_arr_item['dis_price'] += $sp_arr_item['price'] - $sp_arr_item['set_sku_price'];
                        }

                        $sp_arr_item = $dbCommoditySetSkuObj->activity($this->user, $sp_arr_item, $this->shelves_type, $this->channel_type, 1, []);
                        if(!empty($sp_arr_item['set_sku_price'])){
                            $sp_arr_item['price'] = $sp_arr_item['set_sku_price'];
                        }
                        if ($sp_arr_item['commodity_dis_user_segment']) {
                            $sp_arr_item['ac_dis_type'] = 2;
                            $sp_arr_item['ac_dis_count'] = $sp_arr_item['dis_price'];
                        }
                        if (empty($sp_arr_item['image'])) $sp_arr_item['image'] = $sp_arr_item['cover_image'];
                        $price_image_arr[$sp_arr_item['sku_str']] = [
                            'commodity_name' => $sp_arr_item['commodity_name'],
                            'image' => $sp_arr_item['image'],
                            'price' => $sp_arr_item['price'],
                            'old_price'=> sprintf("%.2f",bcdiv($sp_arr_item['old_price'],$sp_arr_item['maintain_dis']/10,0)),
                            'sku_id' => $sp_arr_item['sku_id'],
                            'stock' => $sp_arr_item['stock'],
                            'commodity_set_id' => $sp_arr_item['commodity_set_id'],
                            'commodity_id' => $sp_arr_item['commodity_id'],
                            'work_time_price' => 0,
                            'work_time_code' => '',
                            'work_time_number' => 0,
                            'maintain_dis' => $sp_arr_item['maintain_dis']==10?'':$sp_arr_item['maintain_dis'],
                            'ac_dis_count' => isset($sp_arr_item['ac_dis_count'])?$sp_arr_item['ac_dis_count']:0,//暂时没数据
                            'ac_dis_type' => isset($sp_arr_item['ac_dis_type'])?$sp_arr_item['ac_dis_type']:0,

                            'commodity_dis_user_segment' => $sp_arr_item['commodity_dis_user_segment'],
                            'commodity_dis_act_user_segment' => $sp_arr_item['commodity_dis_act_user_segment'],
                            'commodity_dis_label' => $sp_arr_item['commodity_dis_label'],
                            'commodity_dis_label_cn' => $sp_arr_item['commodity_dis_label_cn'],
                            'card_can_get' => $card_can_get,
                            'card_can_use' => $card_can_use
                        ];
                        $price_sku_str_arr[] = $sp_arr_item['sku_str'];
                        $limit_data = json_decode($sp_arr_item['limit_dis'], true);
                        $limit_id = $limit_data[$this->channel_type][0] ?? 0;
                    }
                    if(!empty($price_sku_str_arr)){
                        $value_str = implode(',', $price_sku_str_arr);
                        $sp_ec_obj = new DbSpecValue();
                        $sp_list = $sp_ec_obj->alias("a")->join("t_db_spec b", "a.sp_id=b.id")->whereIn("a.id", $value_str)->field("a.id value_id,a.sp_value_name,b.id sp_id,b.sp_name")->order("b.sort,a.id")->select();

                        foreach ($sp_list as $sku_item) {
                            $sku_cb_list_tmp['sp_value_arr'][$sku_item['sp_id']] = [
                                'sp_id' => $sku_item['sp_id'],
                                'sp_name' => $sku_item['sp_name'],
                                'val' => []
                            ];
                        }

                        foreach ($sp_list as $sku_item) {
                            $sku_cb_list_tmp['sp_value_arr'][$sku_item['sp_id']]['val'][] = ['sp_id' => $sku_item['sp_id'], 'value_name' => $sku_item['sp_value_name'], 'value_id' => $sku_item['value_id']];
                        }

                        foreach ($sku_cb_list_tmp['sp_value_arr'] as $key => $item) {
                            $sku_cb_list['sp_value_arr'][] = [
                                'sp_id' => $item['sp_id'],
                                'sp_name' => $item['sp_name'],
                                'val' => $item['val'],
                            ];
                        }
                    }

//                    if ($limit_id > 0 && $type == 1 ) {
//                        //限时折扣工显示划线价
//                        $now_date = date("Y-m-d H:i:s", time());;
//                        $dbLimitDiscountObj = new DbLimitDiscount();
//                        $activity = $dbLimitDiscountObj->alias("a")
//                            ->join('t_db_limit_discount_commodity b','a.id = b.limit_discount_id')
//                            ->where(['a.id'=>$limit_id,'b.commodity_id'=>$commodity_id])
//                            ->where("a.start_time <='{$now_date}' and '{$now_date}' <= a.end_time")
//                            ->where(['a.is_enable' => 1])
//                            ->field("a.id,a.title,a.dis_type,a.discount,a.discount_type,b.sku_price")->find();
//
//                        $sku_id = array_keys(json_decode($activity['sku_price'],true));
//
//                        foreach ($price_image_arr as $k => $item) {
//                            if(in_array($item['sku_id'],$sku_id)){
//                                $price = $price_image_arr[$k]['price'];
//                                $work_price = $price_image_arr[$k]['work_time_price'];
//                                if ($activity['dis_type'] == 1) {
//                                    if ($activity['discount_type'] == 1 || $activity['discount_type'] == 3) { //仅商品
//                                        $price_image_arr[$k]['price'] = round($price * $activity['discount'] / 10, 2);
//                                    }
////                                //备件活动计算工进价
////                                if($activity['discount_type'] == 2 || $activity['discount_type'] == 3){//仅工时
////                                    $price_image_arr[$sku_item['sp_value_list']]['work_time_price'] = round($work_price * $activity['discount'] / 10,2);
////                                }
//                                }
//                                if ($activity['dis_type'] == 2) {
//                                    if ($activity['discount_type'] == 1) { //仅商品
//                                        $price_image_arr[$k]['price'] = round($price - $activity['discount'], 2);
//                                    }
//
//                                    //备件活动计算工进价
//                                    if ($activity['discount_type'] == 2) {//仅工时
//                                        $price_image_arr[$k]['work_time_price'] = round($work_price * $activity['discount'] / 10, 2);
//                                    }
//                                }
//                            }
//                        }
//
//                    }
                    $sku_cb_list['price_image'] = $price_image_arr;
                    $sku_cb_list['dlr_code'] = $default_dlr_code;
                    $sku_cb_list['dlr_name'] = $default_dlr_name;
                    $sku_cb_list['is_mate'] = 1;
                    $sku_cb_list['mail_method'] = 1;
//                    redis($redis_key, $sku_cb_list, 8640000);
                    return $this->setResponseData($sku_cb_list)->send();
                }
            }
        }else{
            return $this->setResponseData($sku_cb_list)->send();
        }
        return $this->setResponseData([])->send();
    }



    public function actualRecommend(MaintainanceValidate $validate)
    {
//        $res = $this->actualRecommendAi($validate);
//        if ($res) {
//            return $this->setResponseData('ok')->send();
//        } else {
//            return $this->setResponseError('刷新失败')->send();
//        }

        //===发新版本时候注释这个start只能推荐====
//        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
//        $result      = $validate->scene("actual_recommend")->check($requestData);
//        if (empty($result)) {
//            return $this->setResponseError($validate->getError())->send();
//        }
//
//        $res = Bdp::create('bdp')->recommend($requestData);
//
//        if ($res) {
//            return $this->setResponseData('ok')->send();
//        } else {
//            return $this->setResponseError('刷新失败')->send();
//        }
//
//        exit;
        //===新版本注释end====
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("actual_recommend_ai")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $http_uri = 'BDP_REC_IND.commodity.page';
        $miles = $requestData['miles']  ?? 1;
        $json_data = ['vin'=>$requestData['vin'],'mile'=>$miles,'isInternetCar'=>0];
//        $http_uri = 'ly.bdp.ds.ind.commodity.rec.page';
//        $json_data = ['vin'=>$requestData['vin'],'mile'=>$requestData['miles']];
        $res = Bdp::create('bdpai')->recommendAi('POST',$http_uri,$json_data,[]);
        if ($res) {
            return $this->setResponseData('ok')->send();
        } else {
            return $this->setResponseError('刷新失败')->send();
        }
        exit;
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("actual_recommend")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $res = Bdp::create('bdp')->recommend($requestData);

        if ($res) {
            return $this->setResponseData('ok')->send();
        } else {
            return $this->setResponseError('刷新失败')->send();
        }
    }

    public function actualRecommendAi(MaintainanceValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("actual_recommend_ai")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $http_uri = 'BDP_REC_IND.commodity.page';
        $miles = $requestData['miles']  ?? 1;
        $json_data = ['vin'=>$requestData['vin'],'mile'=>$miles,'isInternetCar'=>0];
//        $http_uri = 'ly.bdp.ds.ind.commodity.rec.page';
//        $json_data = ['vin'=>$requestData['vin'],'mile'=>$requestData['miles']];
        $res = Bdp::create('bdpai')->recommendAi('POST',$http_uri,$json_data,[]);

        if ($res) {
            return $this->setResponseData('ok')->send();
        } else {
            return $this->setResponseError('刷新失败')->send();
        }
    }

    //检查组合商品是否可用券
    public function checkGroupCard(MaintainanceValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("check_group_card")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_goods =  new NetGoods();
        $can_use_card =$net_goods->_checkGroupCardByGoodsId($requestData['sku_json'],$this->user,$this->channel_type);
        if ($can_use_card) {
            return $this->setResponseData($can_use_card)->send();
        } else {
            return $this->setResponseError('刷新失败')->send();
        }

    }





    private function activity($commodity_id, $commodity_list){
        $dbCommodityFlatObj = new DbCommodityFlat();
        $activity_list = $dbCommodityFlatObj->where(['is_enable'=>1,'commodity_id'=>$commodity_id,'shelves_type'=>$this->shelves_type])->field("limit_dis,full_dis,n_dis")->find();
        $act['act_type_id'] = 0;
        $act['act_id'] = 0;
        $now_date = date("Y-m-d H:i:s", time());
        //限时
        if(!empty($activity_list['limit_dis'])){
            $dbLimitDiscountObj = new DbLimitDiscount();
            $limit_data = json_decode($activity_list['limit_dis'], true);
            $activity_limit_info = $dbLimitDiscountObj->where(['id'=>$limit_data[$this->channel_type][0]])->where("start_time <='{$now_date}' and '{$now_date}' <= end_time")->where(['is_enable'=>1])->field("id,title,dis_type,discount")->find();

            if(!empty($activity_limit_info)){
                if($activity_limit_info['dis_type'] == 1){//折扣
                    foreach($commodity_list as $k=>$commodity_item){
                        $commodity_list[$k]['old_price'] = $commodity_item['price'];
                        $commodity_list[$k]['price'] = $commodity_item['price'] * $activity_limit_info['discount'] / 10 ;
                    }
                }
                if($activity_limit_info['dis_type'] == 2){//立减
                    foreach($commodity_list as $k=>$commodity_item){
                        $commodity_list[$k]['old_price'] = $commodity_item['price'];
                        $commodity_list[$k]['price'] = $commodity_item['price'] - $activity_limit_info['discount']  ;
                    }
                }
                $act['act_type_id'] = 1;
                $act['act_id'] = $activity_limit_info['id'];
            }

        }
        return ['commodity_list'=>$commodity_list,'act_list'=>$act];
    }


    public function fixed_spec($id){
        $array = [
            896 => ['val' => '一年期(2年有效)', "discount" => 8],
            897 => ['val' => '二年期(3年有效)', "discount" => 7.5],
            898 => ['val' => '三年期(4年有效)', "discount" => 7],
            2883 => ['val' => '3次', "discount" => 8.5],
            2884 => ['val' => '6次', "discount" => 8],
            2885 => ['val' => '8次', "discount" => 7.5],
            2886 => ['val' => '9次', "discount" => 7],
            2900 => ['val' => '2次', "discount" => 7],
            2901 => ['val' => '2次', "discount" => 7.5],
            3174 => ['val' => '2次', "discount" => 8]
        ];
        return $array[$id] ?? 0;

    }

    /**
     * 机油类型 英文转中文
     * @param string $string
     * @return string
     */
    private function oil($string = '')
    {
        $array = [
            "NOIL"   => "普通油",
            "SOIL"   => "合成机油",
            "ASOIL"  => "全合成机油",
            "SUPOIL" => "超级全合成机油",
        ];
        return $array[$string];
    }

    private function _goods_group($goods_ids,$user,$channel_type)
    {
        if($goods_ids){
            $set_model =  new DbCommoditySet();
            $goods_where = ['is_enable'=>1,'commodity_id'=>['in',$goods_ids]];
            $goods_where[] = ['exp', " (find_in_set('{$channel_type}',up_down_channel_dlr)) "];
            $goods_set =  $set_model->getList(['where'=>$goods_where]);
            $user_ggg = [];
            if($goods_set){
                foreach ($goods_set as $v){
                    if(!$v['dlr_groups']){
                        $v['dlr_groups_arr'] = $user['group_id'];
                    }else{
                        $v['dlr_groups_arr'] = explode(',',$v['dlr_groups']);
                    }
                    if($user_ggg){
                        $user_ggg = array_merge($user_ggg,$v['dlr_groups_arr']);
                    }else{
                        $user_ggg = $v['dlr_groups_arr'];
                    }
                }
                $user['group_id'] =  $user_ggg;


            }
            return $user;
        }
    }


}
