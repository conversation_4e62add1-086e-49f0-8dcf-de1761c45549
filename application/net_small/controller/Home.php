<?php

/**
 * Created by PhpStorm.
 * User: zxtdcyy
 * Date: 2021/4/07
 * Time: 10:57 AM
 */

namespace app\net_small\controller;

use api\wechat\Carer;
use app\common\model\act\AcByDlrCode;
use app\common\model\act\AcInsuranceSpecialist;
use app\common\model\bu\BuOrder;
use app\common\model\db\DbArea;
use app\common\model\db\DbBdpRecommend;
use app\common\model\db\DbBdpRecommendLog;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbDlr;
use app\common\model\db\DbDlrGroup;
use app\common\model\db\DbLog;
use app\common\model\db\DbLyPaySettle;
use app\common\model\db\DbSection;
use app\common\model\db\DbUser;
use app\common\model\db\DbSmExpand;
use app\common\model\db\DbUserCarSeries;
use app\common\net_service\GoodsCustomize;
use app\common\net_service\LyPay;
use app\common\net_service\NetOrder;
use app\common\port\connectors\Member;
use app\common\port\connectors\QuickWin;
use app\common\service\OrderService;
use app\common\validate\BdpValidate;
use app\common\validate\Cart as CartValidate;
use Firebase\JWT\JWT;
use ForkModules\Traits\ResponseTrait;
use app\common\model\db\DbHomeSm;
use app\common\validate\Home as HomeValidate;
use think\Controller;
use think\exception\HttpResponseException;
use think\Model;
use think\Queue;
use think\Request;
use think\Validate;
use tool\Logger;

/**
 * 首页 (尚未登录，通用接口)
 * Class Home
 * @package app\net_small\controller
 * @title 首页
 * @description 接口说明
 */
class Home extends Controller
{
    use ResponseTrait;

    private $user = [
        'id'            => 0,
        'phone'         => '',
        'name'          => '',
        'car_series_id' => '',
        'address_id'    => '',
        'openid'        => '',
        'plat_id'       => '',
        'bind_unionid'  => '',
        'unionid'       => '',
        'member_id'     => '',
        'one_id'        => '',
        'car_18n'       => '',
        'vin'           => '',
        'brand'         => '',
        '18_oil_type'   => 4,// 烂口汤说没有油量就默认4L
        'channel_type'  => '',
    ];

    public function test(HomeValidate $validate)
    {
        Logger::error('testgversionzxtdcyy');
        return $this->setResponseData(['res' => 'ok666'])->send();
    }

    /**
     * @title 小程序首页
     * @description 接口说明
     * @param name:page_type type:int require:0 default:1 other: desc:主页页面类型，1-车生活(默认),2-OneApp,3Pz1a-小程序&APP,5启辰小程序,6启辰App
     * @return 200: 成功
     *
     * <AUTHOR>
     * @url /net-small/home
     * @method GET
     */
    public function index(HomeValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $pageType  = $requestData['page_type'] ?? DbHomeSm::DEFAULT_PAGE;
        $key       = config('cache_prefix.home') . $pageType;
        $home_info = redis($key);
        $home_info = false;
        if (empty($home_info)) {
            //1.获取数据,2.写入redis
            $home_info            = DbHomeSm::field('data_json')->find($pageType);
            $home_info->data_json = (new GoodsCustomize())->filerNormal($home_info->data_json, $pageType);# 过滤第一个商品模块

            redis($key, $home_info, mt_rand(3600, 7200) * 10);
        }
        $this->user['sm_type'] = 1;
        $this->user['set_id']  = $pageType;
        $home_info->data_json = (new GoodsCustomize())->activity($home_info->data_json, $this->user,(new DbHomeSm())->getPageTypeChannel($pageType));
        $home_info->ask_at = time();

        return $this->setResponseData($home_info)->send();
    }


    /**
     * @title 地区列表
     * @description 接口说明
     * @return 200:成功
     * @return msg:提示信息
     * @return data:返回数据地区列表
     * @data name:名称 code:编码 city:城市列表 area:区列表
     * 专营店的省市也是拿的营销平台 wechat/point_get_city
     * 快递类的  manager/base_city?
     * 然后再根据地区拿的专营店列表
     *
     * <AUTHOR>
     * @url /net-small/cart/area
     * @method GET
     *
     */
    public function area()
    {
        $redis_name = config('cache_prefix.ch_area');
        $js         = redis($redis_name);
        if (empty($js)) {
            $area_model = new DbArea();
            $where      = ['is_enable' => 1, 'area_layer' => 1, 'base_province_id' => ['gt', 0]];
            $where_city = ['is_enable' => 1, 'area_layer' => 2, 'base_city_id' => ['gt', 0]];
            $where_area = ['is_enable' => 1, 'area_layer' => 3, 'base_county_id' => ['gt', 0]];
            $p_arr      = $area_model->getList(['where' => $where, 'field' => "area_name name, base_province_id code,area_layer,area_parent_id"]);
            $city_arr   = $area_model->getList(['where' => $where_city, 'field' => "area_name name, base_city_id code,area_layer,base_province_id area_parent_id"]);
            $area       = $area_model->getList(['where' => $where_area, 'field' => "area_name name, base_county_id code,area_layer,base_city_id area_parent_id"]);  //区域做整理映射
            $area_av    = [];
            foreach ($area as $a_v) {
                $area_av[$a_v['area_parent_id']][] = ['name' => $a_v['name'], 'code' => $a_v['code']];
            }
            $area_arr = [];
            if ($p_arr) {
                foreach ($p_arr as $k => $v) {
//                    $area_arr = $v;
                    $area_arr[$k] = ['name' => $v['name'], 'code' => $v['code']];
                    foreach ($city_arr as $kk => $vv) {
                        if ($vv['area_parent_id'] == $v['code']) {
                            $area_arr[$k]['city'][$kk] = ['name' => $vv['name'], 'code' => $vv['code']];
                            if (isset($area_av[$vv['code']])) {
                                $area_arr[$k]['city'][$kk]['area'] = $area_av[$vv['code']];
                            }
                        }
                    }
                }
            }

            foreach ($area_arr as &$item) {
                if (isset($item['city'])) {
                    $item['city'] = array_values($item['city']);
                    foreach ($item['city'] as &$child) {
                        if (isset($child['area'])) {
                            $child['area'] = array_values($child['area']);
                        }
                    }
                }
            }
            $js = json_encode_cn($area_arr);
            redis($redis_name, $js, 86400);
        } else {
            $area_arr = json_decode($js, true);
        }
        return $this->setResponseData($area_arr)->send();
    }

    
    /**
     * 获取价格区间
     * */
    public function getSection()
    {
        $brand_channel = DbDlr::$brand_channel;
        $channel_type  = input('channel_sm');
        if (empty($channel_type) || !isset($brand_channel[$channel_type])) {
            return $this->setResponseError('渠道参数错误')->send();
        }

        $where['is_enable'] = 1;
        $where[]            = ['exp', " (find_in_set('{$channel_type}',channels)) "];
        $model              = new DbSection();
        $params             = [
            'field' => 'section_name,section_price_start,section_price_end',
            'where' => $where,
            'order' => 'sort_order desc',
        ];
        $key                = config('cache_prefix.goods_section') . $channel_type;
        $data               = redis($key);
        if ($data) {
            $list = $data;
        } else {
            $list = $model->getList($params);
            redis($key, $list, mt_rand(3600, 7200));
            $redis = \think\Cache::redisHandler();
            $redis->sadd(config('cache_prefix.goods_section'), $key);
        }
        return $this->setResponseData($list)->send();
    }

    public function getDlrSupportNeedMsg()
    {
        $token = input('token');
        $uuid  = input('uuid');
        if (!empty($token) && !empty($uuid)) {
            $dlr_list = DbDlr::where(['dlr_get_source' => 2])->column('*', 'dlr_code');
            $city_ids = array_unique(array_column($dlr_list, 'area_id'));
            foreach ($city_ids as $v) {
                if (redis('e3s_venucia_city' . $v)) continue;
                Queue::push('app\common\queue\E3sVenuciaCity', json_encode(['token' => $token, 'uuid' => $uuid, 'city_id' => $v, 'dlr_list' => $dlr_list]), config('queue_type.growth'));
            }
        }

        return $this->setResponseData([])->send();
    }

    public function combinationMsg()
    {
        $list = DbCommoditySet::where(['commodity_id' => ['in', [4781, 4790, 4783, 4784]]])->select();
        $data = [
            [
                'commodity_type'     => 1, // 1、CCS流量&养修套餐
                'commodity_ids'      => '4781,4784', // 商品编码，多个用,隔开
                'commodity_name'     => '音乐上网1年包&3次双保升级套餐', // 商品名称
                'commodity_discount' => 6.2, // 商品折扣
                'is_enable'          => 1,// 商品上架状态
                'last_updated_date'  => '', // 最后修改时间
                'commodity_source'   => 5, // 5、NI+商城
            ],
            [
                'commodity_type'     => 1, // 1、CCS流量&养修套餐
                'commodity_ids'      => '4781,4783', // 商品编码，多个用,隔开
                'commodity_name'     => '音乐上网1年包&单次基础保养套餐', // 商品名称
                'commodity_discount' => 5.8, // 商品折扣
                'is_enable'          => 1,// 商品上架状态
                'last_updated_date'  => '', // 最后修改时间
                'commodity_source'   => 5, // 5、NI+商城
            ],
            [
                'commodity_type'     => 1, // 1、CCS流量&养修套餐
                'commodity_ids'      => '4781,4790', // 商品编码，多个用,隔开
                'commodity_name'     => '音乐上网1年包&空调清新套装', // 商品名称
                'commodity_discount' => 6.6, // 商品折扣
                'is_enable'          => 1,// 商品上架状态
                'last_updated_date'  => '', // 最后修改时间
                'commodity_source'   => 5, // 5、NI+商城
            ],
        ];
        foreach ($list as $v) {
            foreach ($data as $k => $l) {
                if (is_numeric(strpos($l['commodity_ids'], (string)$v['commodity_id']))) {
                    if ($v['is_enable'] == 0) $data[$k]['is_enable'] = 0;
                    if (empty($l['last_updated_date']) || $l['last_updated_date'] < $v['last_updated_date']) $data[$k]['last_updated_date'] = $v['last_updated_date'];
                }
            }
        }
        return $this->setResponseData($data)->send();
    }


    /**
     * @Apidoc\Title("BDP更新车主喜好数据")
     * @Apidoc\Author("zxtdcyy")
     * @Apidoc\Url("/net-small/admin/bdp-info")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("BDP")
     *
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("vin",type="string", require=true,default="",desc="vin")
     * @Apidoc\Param("goods",type="string", require=true,default="",desc="推荐信息")
     *
     * @Apidoc\Returned("message", type="string",desc="数据描述")
     */
    public function bdpInfo(BdpValidate $validate)
    {
        $ack = $this->goCheckToken();
        if (empty($ack)) {
            return $this->setResponseError('Bad Auth', 401)->send();
        }

        $requestData = $this->request->only(['vin', 'goods']);

        if (empty($requestData['vin'])) {
            return $this->setResponseError('vin码必传')->send();
        }
        Logger::debug('BDP-DATA', ['vin' => $requestData['vin'], 'goods' => $requestData['goods'] ?? ''], "bdp/bdp-" . date("Y-m-d", time()));
        //有则更新，无则创建
        $bdp   = new DbBdpRecommend();
        $where = ['vin' => $requestData['vin']];
        $data  = $bdp->getOne(['where' => $where, 'db-read' => true]);
        $res   = 1;
        if (empty($data) && !empty($requestData['goods'])) {
            $requestData['goods'] = json_encode($requestData['goods']);
            $res                  = $bdp->insertGetId($requestData);
        } else {
            //如果goods是空，那就删除数据
            if (empty($requestData['goods'])) {
                DbBdpRecommend::where($where)->delete();
            } else {
                $res = $bdp->saveData([
                    'goods'             => json_encode($requestData['goods']),
                    'last_updated_date' => date("Y-m-d H:i:s")
                ], ['id' => $data['id']]);
            }

            $goodService = new GoodsCustomize(); # 清理对应首页的缓存数据
//            $goodService->cacheClear($requestData['vin']);
            $goodService->bdpCacheClear($requestData, ['vin' => $requestData['vin']]);
        }

        if (empty($res)) {
            return $this->setResponseError('error')->send();
        } else {
            return $this->setResponseData('ok')->send();
        }
    }

    /**
     * BDP获取套餐/备件的订单数据
     * @param BdpValidate $validate
     * @return \think\Response
     */
    public function bdpOrder(BdpValidate $validate)
    {
        $ack = $this->goCheckToken();
        if (empty($ack)) {
            return $this->setResponseError('Bad Auth', 401)->send();
        }

        //参数 is_by_tc , 1:老友惠 2:N延保 3:心悦套餐 4:五年双保 41:五年双保(全合成)  5: 车联服务包  6: 保养套餐-其他  9: 到店备件
        // 验证这几个选项值
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));

        if (isset($requestData['vin'])) {
            $result = $validate->scene("bdp_vin")->check($requestData);
        } else {
            $result = $validate->scene("bdp")->check($requestData);
        }

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $net_order = new NetOrder();

        if (isset($requestData['vin'])) {
            $res = $net_order->bdpVinOrder($requestData);
        } else {
            $res = $net_order->bdpOrder($requestData);
        }

        return $this->setResponseData($res['msg'])->send();
    }


    private function goCheckToken()
    {
        if (input('test') == '123456') {
            return true;#test
        }

        try {
            $token = Request::instance()->header('access-token');

            if (empty($token)) {
                return false;
            }
            $token_arr = explode(".", $token);
            if (count($token_arr) != 3) {
                return false;
            }
            $key  = md5(config('nissan_en_key'));
            $info = JWT::decode($token, $key, ['HS256']); //解密jwt

            if (!empty($info) && $info->exp > time()) {
                return true;
            }
            return false;
        } catch (\Exception $exception) {
            Logger::error('validate ack');
            return false;
        }
    }


    /**
     * 结算通知
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function settNotice()
    {
        $ack = $this->goCheckToken();
        if (empty($ack)) {
            return $this->setResponseError('Bad Auth', 401)->send();
        }

        $callback = $this->request->post();
        Logger::info('pay-ment-settNotice-callback', $callback);


        //参数 ms_order_code 支付单号  sett_status 结算状态 1:成功 2:失败
        $rules = [
            'orderId'         => 'require', # 联友支付单号
            'subOrderId'      => 'require', # 订单code
            'settleState'     => 'require', # 结算状态
            'settleStateName' => 'require', # 结算状态名称
            'settleAmt'       => 'require', # 结算金额
            'feeAmt'          => 'require', # 手续费
            'settleTime'      => 'require|date', # 结算时间
            'bankCode'        => 'require', # 银行类型
            'bankName'        => 'require', # 银行类型
        ];

        $validate = new Validate($rules);
        if (!$validate->check($callback)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $map = [
            'sub_order_code' => $callback['subOrderId'],
            'is_enable'      => 1
        ];
        $settleInfo  = DbLyPaySettle::where($map)->find();
        $service = new OrderService();

        $re      = $service->settCallback($callback,$settleInfo);
        $appId   = config("port.ly_payment")['appId'];
        $lyPay   = new LyPay();
        $data    = ['version' => '1.0', 'appId' => $appId];
        if ($re->isSuccess()) {
            $data['returnCode'] = 'SUCCESS';
        } else {
            $data['returnCode'] = 'ERROR';
        }
        $sign = $lyPay->getSign($data);
        $data['sign'] = $sign;
        echo json_encode($data);
        exit();
    }

    /**
     * 乐改-手机号获取认证车vin
     * */
    public function getUserVinList(HomeValidate $validate){
        $ack = $this->goCheckToken();
        if (empty($ack)) {
            return $this->setResponseError('Bad Auth', 401)->send();
        }

        $phone = input('phone', '');
        if (empty($phone)){
            return $this->setResponseError('手机号必传', 403)->send();
        }

        $member = Member::create('member')->search(['phone' => $phone]);
        if (empty($member['member_id'])) {
            $re_data['msg'] = '用户不存在';
            return $this->setResponseError($re_data, 403)->send();
        }

//        if (empty($user_info)){
//            return $this->setResponseError('用户不存在', 403)->send();
//        }

        // 营销平台获取vin车
        $user_cards = Member::create('member')->crmCards(['member_id' => $member['member_id'], 'ignore_e3s' => 1]);
        $car_infos = QuickWin::create('quick_win')->getCarInfos(['appCode' => 'nissan', 'oneId' => $member['oneid'], 'appSkin' => 1]);

        $n_vin_list = []; // N认证车
        foreach ($car_infos as $v){
            if ($v['accountType'] == 0){
                $n_vin_list[$v['vin']] = $v['vin'];
            }
        }
        foreach ($user_cards as $v) {
            // from=digital 营销平台不取统一认证车 --- 先去掉，万一网联接口挂了就没车了
            if ($v['brandcode'] == 1 && !in_array($v['vin'], $n_vin_list)) {
                $n_vin_list[$v['vin']] = $v['vin'];
            }
        }

        $v_vin_list = []; // V认证车
        if (!empty($member['oauth'][0]['unionid'])) {
            $Car   = new Carer();
            $carer = $Car->vin(array('unionid' => $member['oauth'][0]['unionid']));
            if (!empty($carer['vin']) && $carer['car_brand_code'] == 2) {
                $v_vin_list[] = $carer['vin'];
            }
        }
        if (!empty($member['oneid'])) {
            $car_list = QuickWin::create('e3s_dlr')->postQcCarInfoList(['oneid' => $member['oneid'], 'appCode' => 'venucia', 'clientid' => 'venuciaapp']);// 日产：nissan  启辰：venucia
            if (!empty($car_list['rows'])) {
                foreach ($car_list['rows'] as $v) {
                    if ($v['accountType'] == 1) continue;
                    if (!in_array($v['vin'], $v_vin_list)){
                        $v_vin_list[] = $v['vin'];
                    }
                }
            }
        }

        $a_vin_list = []; // A认证车
        if (!empty($member['oneid'])) {
            $car_list = QuickWin::create('e3s_dlr')->postQcCarInfoList(['oneid' => $member['oneid'], 'appCode' => 'nissan', 'clientid' => 'nissanapp']);// 日产：nissan  启辰：venucia
            if (!empty($car_list['rows'])) {
                foreach ($car_list['rows'] as $v) {
                    if ($v['accountType'] == 1) continue;
                    $vin_new_list[$v['vin']] = $v['vin'];
                }
            }
        }

        $vin_list = array_unique(array_merge($n_vin_list, $v_vin_list, $a_vin_list));
        $return_data = [];
        foreach ($vin_list as $vin){
            $car_info = (new \app\common\net_service\Common())->get_car_msg($vin);
            $car_info['vin'] = $vin;
            $return_data[] = $car_info;
        }

        return $this->setResponseData($return_data)->send();
    }


    /**
     * 首页/专题页瀑布流商品组件分页 -- 无权
     * */
    public function getWaterfallGoods(HomeValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("getWaterfallGoods")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $page      = $requestData['page'];
        $sm_type   = $requestData['sm_type'];
        $page_type = $requestData['page_type'] ?? '';
        $id        = $requestData['id'] ?? '';

        if ($sm_type == 1) {
            if (empty($page_type)) $this->setResponseError('首页page_type必传')->send();
            $id = $page_type;
        } elseif ($sm_type == 2) {
            if (empty($id)) $this->setResponseError('专题页id必传')->send();
        }

        $redis_key = 'cWaterfallGoods' . $sm_type . '-' . $id; // 缓存瀑布流商品
        $sm_data = redis($redis_key);
        $data = json_decode($sm_data, true) ?? [];

        $return_data['data']         = array_slice($data, ($page - 1) * 12, 12);
        $return_data['total']        = count($data);
        $return_data['per_page']     = 12;
        $return_data['current_page'] = $page;
        $return_data['all_page']     = ceil($return_data['total'] / 12);
        return $this->setResponseData($return_data)->send();

    }

    /**
     * 企微获取大数据推荐接口
     */
    public function weChatGetBdp(HomeValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("we_chat_bdp")->check($requestData);
        $start_time = $requestData['start_time'];
        $end_time   = $requestData['end_time'];

        $bdp_log_model = new DbBdpRecommendLog();
        $user_car_model = new DbUserCarSeries();
        $commodity_model = new DbCommodity();

        $log_date_start = date('Ym', strtotime($start_time));
        $log_date_end   = date('Ym', strtotime($end_time));

        $list = $bdp_log_model->where(['log_date' => ['between', [$log_date_start, $log_date_end]]])->select();

        $return_data = [];
        foreach ($list as $v){
            $json_data = json_decode($v['msg_list'], true) ?? [];
            $commodity_ids = [];
            foreach ($json_data as $data){
                if ($data['date_time'] >= $start_time && $data['date_time'] <= $end_time){
                    $commodity_ids = array_merge($commodity_ids, array_column($data['goods'], 'commodity_id'));
                }
            }
            $goods = $commodity_model->where(['id' => ['in', $commodity_ids]])->field('id commodity_id,commodity_name,cover_image')->select();
            if (!empty($goods)) {
                $union_ids = $user_car_model
                    ->alias('a')
                    ->join('t_db_user_sub b', 'a.user_id=b.user_id')
                    ->where(['a.vin' => $v['vin'], 'a.channel_type' => 'GWSM', 'b.channel_type' => 'GWSM', 'a.is_bind' => 1, 'b.unionid' => ['not in', ['', 'GWSMtmpUser', 'GWAPPtmpUser']]])
                    ->column('b.unionid');
                $return_data[$v['vin']] = array_merge($return_data[$v['vin']] ?? [], [
                    'vin'       => $v['vin'],
                    'goods'     => $goods,
                    'union_ids' => $union_ids,
                ]);
            }
        }

        return $this->setResponseData(array_values($return_data))->send();
    }

    /**
     * 企微用户下单数据
     * */
    public function weChatHaveOrder(HomeValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("we-chat-have-order")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $start_time    = $requestData['start_time'];
        $end_time      = $requestData['end_time'];
        $member_ids    = $requestData['member_ids'];
        $commodity_ids = $requestData['commodity_ids'] ?? '';
        $sourcecode    = $requestData['sourcecode'] ?? '';

        $user_model = new DbUser();
        $order_model = new BuOrder();

        $user_ids = $user_model->where(['plat_id' => ['in', $member_ids]])->column('id uid,plat_id', 'id');

        $where = ['a.user_id' => ['in', array_keys($user_ids)], 'a.order_status' => ['not in', [1, 3, 8]], 'a.pay_time' => ['between', [$start_time, $end_time]], 'a.dlr_code' => 'GWSM', 'c.channel_type' => 'GWSM'];
        if (!empty($commodity_ids)){
            $where['b.commodity_id'] = ['in', $commodity_ids];
        }

        // 数据状态类型：1创建任务前判断用户是否已经买过对应商品；2定时查询统计数
        if (!empty($sourcecode)){
            $where['a.source'] = $sourcecode;
        }

        $list = $order_model
            ->alias('a')
            ->join('t_bu_order_commodity b', 'a.order_code = b.order_code')
            ->join('t_db_user_sub c', 'a.user_id = c.user_id')
            ->where($where)->column('a.id order_id,a.order_code,a.order_status,a.user_id,a.pay_time,b.commodity_id,c.unionid', 'b.id');

        $data = [];
        $return_data = [];
        foreach ($list as $v){
            $data[$v['user_id']]['unionid']   = $v['unionid'];
            $data[$v['user_id']]['member_id'] = $user_ids[$v['user_id']]['plat_id'];

            $data[$v['user_id']]['order_list'][$v['order_code']]['order_id']     = $v['order_id'];
            $data[$v['user_id']]['order_list'][$v['order_code']]['order_code']   = $v['order_code'];
            $data[$v['user_id']]['order_list'][$v['order_code']]['order_status'] = $v['order_status'];
            $data[$v['user_id']]['order_list'][$v['order_code']]['pay_time']     = $v['pay_time'];
            $data[$v['user_id']]['order_list'][$v['order_code']]['goods_ids'][]  = $v['commodity_id'];
        }
        foreach ($data as $v){
            $v['order_list'] = array_values($v['order_list']);
            $return_data[] = $v;
        }

        return $this->setResponseData($return_data)->send();
    }

    /**
     * 接收充电服务单状态
     * */
    public function chargeServiceStatus(HomeValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("service_status")->check($requestData);
        $order_code = $requestData['order_code'];
        $service_status = $requestData['service_status'];

        $order_model = new BuOrder();
        $order_info = $order_model->getOne(['where' => ['order_code' => $order_code]]);
        if (empty($order_info)){
            return $this->setResponseError('订单号错误')->send();
        }

        $order_status = 0;
        if ($service_status == 1){
            $order_status = 4;
        }elseif ($service_status == 2){
            $order_status = 9;
        }

        if (!empty($order_status)) {
            $order_model->saveData(['order_status' => $order_status,'modifier'=>'ly-cs'], ['order_code' => $order_code]);
            (new NetOrder())->orderChange($order_code);
        }
        return $this->setResponseData('ok')->send();
    }

}
