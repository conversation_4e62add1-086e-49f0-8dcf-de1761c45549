<?php
/**
 * 合作伙伴回调控制器
 * @author: AI Assistant
 * @time: 2025-01-21
 */

namespace app\net_small\controller;

use app\common\service\PartnerCallbackService;
use ForkModules\Traits\ResponseTrait;
use think\Controller;
use think\Response;
use tool\Logger;

class PartnerCallback extends Controller
{
    use ResponseTrait;

    /**
     * 合作伙伴发货回调接口
     * @return Response
     */
    public function deliveryCallback()
    {
        try {
            // 获取请求参数
            $params = input();
            
            // 记录请求日志
            Logger::info('收到合作伙伴发货回调请求', [
                'params' => $params,
                'request_time' => date('Y-m-d H:i:s')
            ]);

            // 验证请求方法
            if (!$this->request->isPost()) {
                return $this->error('仅支持POST请求', [], 405);
            }

            // 验证Content-Type
            $contentType = $this->request->header('content-type');
            if (strpos($contentType, 'application/json') === false) {
                // 如果不是JSON格式，尝试获取原始输入
                $rawInput = file_get_contents('php://input');
                if (!empty($rawInput)) {
                    $params = json_decode($rawInput, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        return $this->error('请求数据格式错误，请使用JSON格式', [], 400);
                    }
                }
            }

            // 验证基本参数
            if (empty($params)) {
                return $this->error('请求参数不能为空', [], 400);
            }

            // 调用服务处理回调
            $service = new PartnerCallbackService();
            $result = $service->handleDeliveryCallback($params);

            if ($result['success']) {
                return $this->successResponse($result['message'], $result['data']);
            } else {
                return $this->errorResponse($result['message'], [], 400);
            }

        } catch (\Exception $e) {
            Logger::error('合作伙伴发货回调处理异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->errorResponse('系统异常，请稍后重试', [], 500);
        }
    }

    /**
     * 健康检查接口
     * @return Response
     */
    public function health()
    {
        return $this->successResponse('合作伙伴回调服务正常运行');
    }

    /**
     * 返回成功响应
     * @param string $message
     * @param array $data
     * @return Response
     */
    private function successResponse($message = 'success', $data = [])
    {
        $response = [
            'result' => 1,
            'msg' => $message,
            'extInfo' => '',
            'pageindex' => 0,
            'pages' => 0,
            'records' => 0,
            'rows' => $data,
            'success' => true
        ];

        return Response::create($response, 'json')->code(200);
    }

    /**
     * 返回错误响应
     * @param string $message
     * @param array $data
     * @param int $code
     * @return Response
     */
    private function errorResponse($message = 'error', $data = [], $code = 400)
    {
        $response = [
            'result' => 0,
            'msg' => $message,
            'extInfo' => '',
            'pageindex' => 0,
            'pages' => 0,
            'records' => 0,
            'rows' => $data,
            'success' => false
        ];

        return Response::create($response, 'json')->code($code);
    }
}
