<?php

/*** Created by demo.
 * PROJECT：php-wxmp-dealers
 * User: <EMAIL>
 * Date: 2025/2/19
 * Time: 14:54
 * remark:
 */

namespace app\net_small\controller;

use app\common\model\db\InvoiceLogModel;
use app\common\model\db\InvoiceRecordModel;
use app\common\port\connectors\InvoiceHX;
use app\common\service\InvoiceRecordCallbackService;
use ForkModules\Traits\ResponseTrait;
use think\Controller;
use think\Response;

class InvoiceCallback extends Controller
{
    use ResponseTrait;

    /**
     * 发票回调
     * @return Response
     */
    public function sendInvoiceCallback(): Response
    {
        // todo
//        $return = [
//            'code' => '0000',
//            'message' => 'success',
//        ];
//        return $this->setResponseData($return)->send();

        $params = input();
        $log = new InvoiceLogModel();
        $service = new InvoiceRecordCallbackService();

        $log->request_url = '/net_small/invoice_callback/sendInvoiceCallback';
        $log->request_params = json_encode_cn($params);
        if ($params['invoiceType'] == 1) {
            // 蓝票
            $log->order_no = $params['orderNo'];
        } else {
            // 红票
            $log->order_no = $params['oriOrderNo'];
        }

        $messageBag = $service->sendInvoiceCallbackProcess($params);
        $msg = $messageBag->getMessage();

        $log->response = $msg;

        $log->save();
        $return = ['message'=>$msg];
        if ($messageBag->isSuccess()) {
            $return['code'] = '0000';
        } else {
            $return['code'] = '9999';
        }
        return $this->setResponseData($return)->send();
    }


    /**
     * 确认红字发票回调
     * @return Response
     */
    public function sendRedConfirmInvoiceCallback(): Response
    {
//        // todo
//        $return = [
//            'code' => '0000',
//            'message' => 'success',
//        ];
//        return $this->setResponseData($return)->send();

        $params = input();
        $log = new InvoiceLogModel();
        $service = new InvoiceRecordCallbackService();
        $log->request_url = '/net_small/invoice_callback/sendRedConfirmInvoiceCallback';
        $log->request_params = json_encode_cn($params);
        $return = ['code'=>'', 'message'=>''];
        if (isset($params['backType']) && ($params['backType'] == 2)) {
            $messageBag = $service->runSendRedInvoiceBackType2($params);
            $msg = $messageBag->getMessage();
            $log->response = $msg;
            if ($messageBag->isSuccess()) {
                $return['code'] = '0000';
            } else {
                $return['code'] = '9999';
            }

        } else {
            $log->response = '回传类型不正确';
        }

        $log->save();
        return $this->setResponseData($return)->send();
    }

//    /**
//     * 确认红字发票回调处理
//     * @return string[]
//     */
//    protected function sendRedConfirmInvoiceCallbackProcess($params): array
//    {
//        if (!in_array($params['backType'] ?? 0, [1, 2])) {
//            return [
//                'code' => '9999',
//                'message' => '回传类型不能不正确',
//            ];
//        }
//
//        // 2025年4月8日 10:05:58  产品：日产商城只有数电票，就只有红字确认单
//        if ($params['backType'] == 1) {
//            //回传红字信息表结果
//            return $this->runSendRedInvoiceBackType1($params);
//        }
//        if ($params['backType'] == 2) {
//            //红字确认单结果
//            return $this->runSendRedInvoiceBackType2($params);
//        }
//
//        return [
//            'code' => '9999',
//            'message' => '回传类型不能不正确',
//        ];
//    }

//    protected function runSendRedInvoiceBackType1($params): array
//    {
//        $_where = [
//            'red_invoice_id' => $params['billNo']
//        ];
//        $record = InvoiceRecordModel::where($_where)->find();
//        if (empty($record)) {
//            return [
//                'code' => '9999',
//                'message' => '发票不存在',
//            ];
//        }
//        $data_update = [];
//        //		信息表类型(0:正常 1:逾期(仅销方开具)，2:机动车专票-退货和开具错误，3:机动车专票-销售折让和合格证不退回
//        //		4、矿产品类专用信息表（涉及销售数量和金额变更）,5、矿产品类专用信息表（仅涉及销售金额变更，不涉及数量变动
//        if ($params['billType'] != 0) {
//            $data_update['red_message'] = "信息表类型非正常状态:" . $params['billType'];
//            $record->where('id', '=', $record->id)->update($data_update);
//            return [
//                'code' => '9999',
//                'message' => '信息表类型非正常状态',
//            ];
//        }
//        //		信息表状态(-1:未提交 0:申请中 1:审核成功 2:审核失败 3:申请成功 4:申请失败
//        //		5:已开具 6:撤销中 7:撤销失败 8:已撤销 -2:删除成功)
//        if (!in_array($params['billStatus'], ["1", "3", "5"])) {
//            $data_update['red_message'] = "信息表状态非正常状态:" . $params['billStatus'];
//            $record->where('id', '=', $record->id)->update($data_update);
//            return [
//                'code' => '9999',
//                'message' => '信息表状态非正常状态',
//            ];
//        }
//        //如果是 1:审核成功 3:申请成功 5:已开具
//        //请求快速开票
//        $this->fastRepeatedRedSingle($params);
//
//        return [
//            'code' => '0000',
//            'message' => '业务方接收同步成功',
//        ];
//    }


    /**
     * 红字确认单
     * @param $params
     * @return string[]
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
//    protected function runSendRedInvoiceBackType2($params): array
//    {
//        $_where = [
//            'red_invoice_id' => $params['billId']
//        ];
//        $record = InvoiceRecordModel::where($_where)->find();
//        if (empty($record)) {
//            return [
//                'code' => '9999',
//                'message' => '发票不存在',
//            ];
//        }
//        $data_update = [];
//        //确认单状态:-1 提交失败,00 提交中,01 无需确认02 销方录入待购方确认,03 购方录入待销方确认,04 购销双方已确认,
//        // 05 作废（销方录入购方否认）,06 作废（购方录入销方否认）,07 作废（超72小时未确认）,
//        // 08 作废（发起方撤销）,09 作废（确认方撤销）,10作废（异常凭证）,11作废（纳税人状态异常阻断）,15 提交税局中
//        // 16 提交税局失败
//        if (in_array($params['billStatus'], ["01", "04"])) {
//            $data_update['red_message'] = "确认单状态:" . $params['billStatus'];
//
//            $data_update['red_bill_status'] = $params['billStatus'];
//            $data_update['red_billinfo_no'] = $params['billInfoNo'] ?? '';
//
//            //		开具状态0：未开具,1：已开具
//            if ($params['openStatus'] == 0) {
//                $record->where('id', '=', $record->id)->update($data_update);
//                //未开时，请求开
//                $this->fastRepeatedRedSingle($params);
//            }
//        } elseif ($params['billStatus'] == '16') {
//            $data_update = [
//                'red_invoice_data_status' => InvoiceRecordModel::INVOICE_REQUEST_FAIL,
//                'red_bill_status' => 4,
//                'red_message' => $params['billMessage'],
//                'invoice_status' => InvoiceRecordModel::INVOICE_RED_FAIL,
//            ];
//            $record->where('id', '=', $record->id)->update($data_update);
//
//        }
//
//        return [
//            'code' => '0000',
//            'message' => '业务方接收同步成功',
//        ];
//    }


}