<?php


namespace app\net_small\controller;


use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityCard;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommoditySku;
use app\common\model\db\DbSpecialSm;
use app\common\model\db\DbSpecValue;
use app\common\net_service\NetGoods;
use app\common\net_service\NetOrder;
use app\common\net_service\NetUser;
use app\common\port\connectors\JavaNewMedia;
use app\common\service\CommodityService;
use app\common\model\db\DbBdpRecommend;
use app\common\net_service\GoodsCustomize;
use app\common\validate\Admin as AdminValidate;
use app\common\validate\BdpValidate;
use think\Exception;
use think\Request;
use tool\Logger;

/**
 * 后台需要
 * @Apidoc\Group("mall")
 */
class Admin extends Common
{
    protected $order_status = [2, 4, 7, 9, 11, 12, 13, 14];

    public function __construct(Request $request)
    {
        parent::__construct($request);
    }

    /**
     * @Apidoc\Title("商品列表")
     * @Apidoc\Url("/net-small/admin/goods-list")
     * @Apidoc\Tag("商品 详情")
     * @Apidoc\Method("GET")
     * @Apidoc\Author("llj")
     *
     * @Apidoc\Param("id", type="int",require=false, desc="商品ID" )
     * @Apidoc\Param("search", type="string",require=false, desc="商品名称搜索" )
     * @Apidoc\Param("sale_num", type="int",require=false, desc="销量，返回大于参数的数据" )
     * @Apidoc\Param("page", type="int",require=false, desc="页码" )
     * @Apidoc\Param("pageSize", type="int",require=false, desc="数量，默认15条" )
     *
     * @Apidoc\Returned("total", type="int",desc="总数" ),
     * @Apidoc\Returned("current_page", type="int",desc="页码" ),
     * @Apidoc\Returned("per_page", type="int",desc="分页数量" ),
     * @Apidoc\Returned("first_page_url", type="string",desc="第一页链接" ),
     * @Apidoc\Returned("prev_page_url", type="string",desc="上一页链接" ),
     * @Apidoc\Returned("next_page_url", type="string",desc="下一页链接" ),
     * @Apidoc\Returned("last_page_url", type="string",desc="最后一页链接" ),
     * @Apidoc\Returned("last_page", type="string",desc="最后一页页码" ),
     * @Apidoc\Returned("path", type="string",desc="链接地址" ),
     * @Apidoc\Returned("data", type="object",desc="商品列表",
     *     @Apidoc\Returned("commodity_id", type="int(10)",desc="商品id" ),
     *     @Apidoc\Returned("commodity_name", type="varchar(200)",desc="商品名称" ),
     *     @Apidoc\Returned("tag", type="varchar(128)",desc="标签（多个用,隔开）：1热销;2推荐;3新品;4促销;5预售;10:优惠套装;11:满优惠;12:限时优惠;13:多人拼团;14:N件N折;15:预售活动" ),
     *     @Apidoc\Returned ("tag_name",type="array/json",desc="标签数组"),
     *     @Apidoc\Returned("original_price_range_start", type="decimal(10,2)",desc="原价范围开始价格" ),
     *     @Apidoc\Returned("original_price_range_end", type="decimal(10,2)",desc="原价范围结束价格" ),
     *     @Apidoc\Returned("discount_price_range_start", type="decimal(10,2)",desc="现价范围开始价格" ),
     *     @Apidoc\Returned("discount_price_range_end", type="decimal(10,2)",desc="现价范围结束价格" ),
     *     @Apidoc\Returned("cover_image", type="varchar(200)",desc="封面图" ),
     *     @Apidoc\Returned("creator", type="varchar(20)",desc="创建人" ),
     *     @Apidoc\Returned("sale_num", type="int(10)",desc="销量" ),
     *     @Apidoc\Returned("created_date", type="datetime",desc="创建时间" )
     * )
     */
    public function getGoodsList(AdminValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("goods_list")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $buOrderCommodity = new BuOrderCommodity();
        $dbCommodityFlat  = new DbCommodityFlat();

        $query['page'] = empty($requestData['page']) ? 1 : $requestData['page'];
        $pagesize      = empty($requestData['pageSize']) ? 15 : $requestData['pageSize'];

        $where['a.is_enable'] = 1;

        if (!empty($requestData['id'])) {
            $where['a.commodity_id'] = $requestData['id'];
        }
        if (!empty($requestData['search'])) {
            $where['a.commodity_name'] = ['like', "%{$requestData['search']}%"];
        }
        if (!empty($requestData['sale_num'])) {
            $having = 'sum(a.count)>=' . $requestData['sale_num'];
        }

        if (isset($having)) {
            $where['a.dlr_code']     = $this->channel_type;
            $where['b.order_status'] = ['in', $this->order_status];
            $where['d.is_enable']    = 1;
            $where['c.shelves_type'] = 5;
            #销售渠道
            $where[] = ['EXP', "FIND_IN_SET(1,d.sales_channel)"];
            $params  = [
                'field'    => 'a.commodity_id,d.commodity_name,d.tag_gwapp as tag,c.original_price_range_start,c.original_price_range_end,c.discount_price_range_start,c.discount_price_range_end,d.cover_image,d.creator,d.created_date,sum(a.count) as sale_num',
                'where'    => $where,
                'order'    => 'a.created_date DESC',
                'pagesize' => $pagesize,
                'query'    => $query,
                'group'    => 'a.commodity_id',
                'having'   => $having
            ];
            $list    = $buOrderCommodity->getOrderMsg($params);
        } else {
            #销售渠道
            $where[]                 = ['EXP', "FIND_IN_SET(1,a.sales_channel)"];
            $where[]                 = ['EXP', "FIND_IN_SET('{$this->channel_type}', a.up_down_channel_dlr)"];
            $where['a.is_enable']    = 1;
//            $where['b.shelves_type'] = 5;
            $params                  = [
                'field'    => 'a.commodity_id,a.commodity_name,a.tag_gwapp as tag,b.original_price_range_start,b.original_price_range_end,b.discount_price_range_start,b.discount_price_range_end,a.cover_image,a.creator,a.created_date,b.front_sale_num sale_num',
                'where'    => $where,
                'order'    => 'a.created_date DESC',
                'pagesize' => $pagesize,
                'query'    => $query,
                'group'    => 'a.commodity_id'
            ];

            $list = $dbCommodityFlat->getCommodity($params);
            $keys = array_column($list['data'], 'commodity_id');
//这里会影响sql性能，去掉
//            $params = [
//                'where' => [
//                    'a.commodity_id' => ['in', $keys],
//                    'a.dlr_code'     => $this->channel_type,
//                    'b.order_status' => ['in', $this->order_status]
//                ],
//                'group' => 'a.commodity_id',
//            ];
//
//
//            $sale_list = $buOrderCommodity->getSaleNum($params);

            foreach ($list['data'] as $k => $v) {
//                if (isset($sale_list[$v['commodity_id']])) {
//                    $list['data'][$k]['sale_num'] = $sale_list[$v['commodity_id']];
//                } else {
//                    $list['data'][$k]['sale_num'] = 0;
//                }

                $tag_name = [];
                if (!empty($v['tag'])) {
                    $tag_gwapp = explode(',', $v['tag']);
                    foreach ($tag_gwapp as $vl) {
                        $tag_name[] = DbCommodityFlat::tagArr($vl);
                    }
                }
                $list['data'][$k]['tag_name'] = $tag_name;
            }
        }

        if ($list) {
            $last_page = ceil($list['total'] / $pagesize);
            $path      = $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];

            $first_page_url = empty($requestData['page']) ? $path . '&page=1' : str_replace('page=' . $query['page'], 'page=1', $path);
            if ($query['page'] == 1) {
                $prev_page_url = '';
            } else {
                $prev_page_url = str_replace('page=' . $query['page'], 'page=' . ($query['page'] - 1), $first_page_url);
            }
            if ($query['page'] >= $last_page) {
                $next_page_url = '';
            } else {
                $next_page_url = str_replace('page=' . $query['page'], 'page=' . ($query['page'] + 1), $first_page_url);
            }
            $last_page_url          = str_replace('page=' . $query['page'], 'page=' . $last_page, $first_page_url);
            $list["first_page_url"] = $first_page_url;
            $list["prev_page_url"]  = $prev_page_url;
            $list["next_page_url"]  = $next_page_url;
            $list["last_page_url"]  = $last_page_url;
            $list["last_page"]      = $last_page;
            $list["test"]      = 111;
            $list["path"]           = $path;
            return $this->setResponseData($list)->send();
        } else {
            return $this->setResponseError([])->send();
        }
    }

    /**
     * @Apidoc\Title("根据id获取商品信息")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-small/admin/getGoodsForIds")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("商品")
     *
     * @Apidoc\Param("goods_ids", type="string",require=true, desc="商品id，多个用,隔开" )
     *
     * @Apidoc\Returned ("commodity_name",type="varchar(200)",desc="商品名称")
     * @Apidoc\Returned ("tag",type="varchar(128)",desc="商品标签（多个用,隔开）: 1热销;2推荐;3新品;4促销;5满减;6团购;7限时购")
     * @Apidoc\Returned ("tag_name",type="array/json",desc="标签数组"),
     * @Apidoc\Returned("original_price_range_start", type="decimal(10,2)",desc="原价范围开始价格" )
     * @Apidoc\Returned("original_price_range_end", type="decimal(10,2)",desc="原价范围结束价格" )
     * @Apidoc\Returned("discount_price_range_start", type="decimal(10,2)",desc="现价范围开始价格" )
     * @Apidoc\Returned("discount_price_range_end", type="decimal(10,2)",desc="现价范围结束价格" )
     * @Apidoc\Returned ("cover_image",type="varchar(255)",desc="封面图")
     * @Apidoc\Returned ("created_date",type="datetime",desc="创建时间")
     */
    public function getGoodsForIds(AdminValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("for_ids")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $goods_ids = $requestData['goods_ids'];

        $list = (new NetGoods())->goodsList(['commodity_ids' => $goods_ids], ['id'=>0,'bind_unionid'=>'','brand'=>'','vin'=>''], 'GWAPP');

        $key_data = explode(',', $goods_ids);
        $appVersion = $requestData['appVersion'] ?? 0;
        $data     = [];
        foreach ($key_data as $vl) {
            foreach ($list['msg']['data'] as $k => $v) {
                if ($vl == $v['commodity_id']) {
                    $card_list = [];
                    if (!empty($v['card_id'])){
                        $card_list = (new DbCommodityCard())
                            ->alias('a')
                            ->join('t_db_card b', 'a.card_id = b.id')
                            ->where(['a.commodity_set_id' => $v['commodity_set_id'], 'a.card_id' => ['in', $v['card_id']], 'a.is_can_receive' => 1, 'a.is_enable' => 1, 'b.act_status' => 2, 'b.available_count' => ['gt', 0], ['exp', "find_in_set('GWAPP',b.up_down_channel_dlr)"]])
                            ->field('b.id,b.card_name')
                            ->select();
                    }

                    $tag_name = [];
                    if (!empty($v['tag_gwapp'])){
                        $tag = explode(',', $v['tag_gwapp']);
                        foreach ($tag as $val){
                            if (in_array($val, [10,12,14])){
                                $tag_name[] = '折扣';
                            }elseif ($val == 11){
                                $tag_name[] = '满减';
                            }elseif ($val == 16){
                                $tag_name[] = '秒杀';
                            }elseif ($val == 15){
                                $tag_name[] = '预售';
                            }elseif ($val == 17){
                                $tag_name[] = '买赠';
                            }elseif ($val == 13){
                                $tag_name[] = '拼团';
                            }
                        }
                    }

                    $price = $v['price'];
                    $final_price = $v['final_price'];
                    if ($appVersion < '3.3.0') {
                        $price = number_format($v['price'], 2, '.', '');
                        $final_price = number_format($v['final_price'], 2, '.', '');
                    }

                    $data[] = [
                        'commodity_id'               => $v['commodity_id'],
                        'commodity_name'             => $v['commodity_name'],
                        'tag'                        => $v['tag_gwapp'],
                        'tag_name'                   => array_unique($tag_name),//子云说要过滤重复
//                        'original_price_range_start' => $v['price'],
//                        'original_price_range_end'   => $v['price'],
//                        'discount_price_range_start' => $v['final_price'],
//                        'discount_price_range_end'   => $v['final_price'],
                        'original_price_range_start' => $price,
                        'original_price_range_end'   => $price,
                        'discount_price_range_start' => $final_price,
                        'discount_price_range_end'   => $final_price,
                        'cover_image'                => $v['cover_image'],
                        'created_date'               => '',
                        'card_list'                  => $card_list,
                    ];
                }

            }
        }
        return $this->setResponseData($data)->send();
//        }else{
//            return $this->setResponseError([])->send();
//        }
    }

    /**
     * @Apidoc\Title("自定义专题列表")
     * @Apidoc\Url("/net-small/admin/special-custom")
     * @Apidoc\Tag("专题")
     * @Apidoc\Method("GET")
     * @Apidoc\Author("zxtdcyy")
     *
     * @Apidoc\Param("id", type="string",require=false, desc="专题ID" )
     * @Apidoc\Param("title", type="int",require=false, desc="专题名称" )
     * @Apidoc\Param("page", type="int",require=false, desc="页码" )
     * @Apidoc\Param("pageSize", type="int",require=false, desc="数量，默认15条" )
     *
     * @Apidoc\Returned("id", type="int",desc="总数" ),
     * @Apidoc\Returned("name", type="int",desc="页码" ),
     *
     */
    public function specialCustom(AdminValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("special_list")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $special = new DbSpecialSm();
        $where   = [];

        $where['page_type'] = ['in', [2, 7,6]];

        if (!empty($requestData['id'])) {
            $where['id'] = $requestData['id'];
        }
        if (!empty($requestData['title'])) {
            $where['title'] = ['like', "%{$requestData['title']}%"];
        }

        $params = [
            'field'    => 'id,title',
            'where'    => $where,
            'order'    => 'id desc',
            'pagesize' => $requestData['pageSize'],
        ];
        $res    = $special->getListPaginate($params);
        return $this->setResponseData($res)->send();
    }

    /**
     * @Apidoc\Title("定制专题列表")
     * @Apidoc\Url("/net-small/admin/special-fixed")
     * @Apidoc\Tag("专题")
     * @Apidoc\Method("GET")
     * @Apidoc\Author("zxtdcyy")
     *
     * @Apidoc\Returned("id", type="int",desc="总数" ),
     * @Apidoc\Returned("title", type="int",desc="名称" ),
     * @Apidoc\Returned("routes", type="string",desc="专题路由地址" ),
     *
     */
    public function specialFixed()
    {
        $data = config('special_fix');
        return $this->setResponseData($data)->send();
    }

    /**
     * 获取卡券关联商品列表
     * */
    public function getCardGoodsList(AdminValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("card_goods")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $requestData['page']     = $requestData['page'] ?? 1;
        $requestData['pageSize'] = $requestData['pageSize'] ?? 15;

        $where = [];
        if (!empty($requestData['search'])) {
            $where['a.commodity_name'] = ['like', "%{$requestData['search']}%"];
        }
        $card_model = new DbCard();
        $card_info  = $card_model->where(['quick_win_card_id' => $requestData['card_id']])->find();
        if (!$card_info) {
            return $this->setResponseError('卡券不存在')->send();
        }
        $where[] = ['exp', " (find_in_set({$card_info['id']},a.card_id) ) "];

//        $where['a.shelves_type'] = 5;
        $field = "a.commodity_set_id,a.comm_type_id_str,a.commodity_class,a.commodity_id,a.commodity_name,a.cover_image,b.count_stock,a.card_id,c.is_mail,c.is_shop,c.is_pure,c.original_price_range_start,c.original_price_range_end,c.discount_price_range_start,c.discount_price_range_end,c.unit,c.sort,c.created_date,b.commodity_attr";

        $params = array(
            'query'    => $requestData,
            'where'    => $where,
            'pageSize' => $requestData['pageSize'],
            'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
            'field'    => $field
        );

        $flat = new DbCommodityFlat();
        $list = $flat->getCommodityCardList($params);

        $commodity_class_arr = DbCommodity::commodityClass();

        foreach ($list as $key => $val) {
            $list[$key]['commodity_class_name']  = $commodity_class_arr[$val['commodity_class']] ?? '';
            $list[$key]['comm_type_id_str']      = getCommTypeIdInfo($list[$key]['comm_type_id_str']);
            $list[$key]['commodity_attr_name']   = DbCommodity::attribute($val['commodity_attr']);
            $list[$key]['settlement_rule_id']    = $card_info['settlement_rule_id'];
            $list[$key]['settlement_rule_name']  = $card_info['settlement_rule_name'];
            $list[$key]['settlement_rule_type']  = $card_info['settlement_rule_type'];
            $list[$key]['settlement_rule_value'] = $card_info['settlement_rule_value'];
            $list[$key]['e3s_activity_id']       = $card_info['e3s_activity_id'];
            $list[$key]['e3s_activity_name']     = $card_info['e3s_activity_name'];
        }
        $card_model = new DbCard();
        $card_row   = $card_model->getOneByPk($requestData['card_id']);
        $goods_list = $list->toArray();
        $data       = [
            'msg'       => '获取成功',
            'result'    => '1',
            'rows'      => ['goods_list' => $goods_list['data'], 'card_row' => $card_row],
            'extInfo'   => '',
            'pageindex' => $requestData['page'],
            'pages'     => (int)ceil($goods_list['total'] / $requestData['pageSize']),
            'records'   => $goods_list['total'],
        ];
        return $this->setResponseData($data)->send();
    }

    /**
     * 获取商品规格列表
     * */
    public function getGoodsSku(AdminValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("goods_sku")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $commodity_set_id = $requestData['commodity_set_id'];

        $commodity_set = new DbCommoditySet();
        $commodity_sku = new DbCommoditySku();
//        $commodity_sku = new CommodityService();
        $comm_info = $commodity_set->getSetCommodity(['a.id' => $commodity_set_id]);
        $params    = [
            'where'    => [
                'b.commodity_id'     => $comm_info['commodity_id'],
                'a.is_enable'        => 1,
                'b.is_enable'        => 1,
                'b.commodity_set_id' => $commodity_set_id,
            ],
            'group'    => 'a.id',
            'order'    => 'price asc',
            'field'    => 'a.sp_value_list,a.id as commodity_sku_id,b.divided_into,b.install_fee,b.cost_price, a.sku_code,b.id,b.set_type,IFNULL(b.stock,0) stock,IFNULL(b.price,a.price) as price,b.price old_price,a.image,a.relate_car_work_hour,a.relate_car_ids,b.commodity_id,a.commodity_id sub_commodity_id,a.relate_car_18n,a.relate_car_ids',
            'pageSize' => $requestData['pageSize'] ?? 15,
            'page'     => $requestData['page'] ?? 1,
            'query'    => $requestData,
        ];

        $list = $commodity_sku->getSetSkuByCommoditySetIdPage($params);
//        dd($list);
//        $list          = $commodity_sku->getSetSku($commodity_set_id);
        $data    = [];
        $val_arr = [];
        foreach ($list as $k => $v) {
            if (!empty($val['sp_value_list'])) {
                $val_arr                  = array_merge($val_arr, explode(',', $val['sp_value_list']));
                $list[$k]['sp_value_arr'] = explode(',', $val['sp_value_list']);
            } else {
                $list[$k]['sp_value_arr'] = [];
            }
        }
        //规则值列表
        $sp_model    = new DbSpecValue();
        $sp_list     = $sp_model->getAllList(['a.id' => ['in', $val_arr]]);
        $sp_list     = collection($sp_list)->toArray();
        $sp_title    = [];
        $sp_list_val = [];
        foreach ($sp_list as $key => $val) {
            $sp_title[$val['sp_id']] = $val['sp_name'];
            $sp_list_val[$val['id']] = $val;
        }

        foreach ($list as $key => $val) {
            foreach ($val['sp_value_arr'] as $key2 => $val2) {
                if (!isset($sp_list_val[$val2])) {
                    unset($list[$key]);
                    break;
                }
            }
        }
        foreach ($list as $k => $v) {
            $data[$k]['id']           = $v['id'];
            $data[$k]['sku_code']     = $v['sku_code'];
            $data[$k]['price']        = $v['price'];
            $data[$k]['stock']        = $v['stock'];
            $data[$k]['sku_image']    = $v['sku_image'] ?? $comm_info['cover_image'];
            $data[$k]['sp_value_msg'] = '';
            if (isset($v['sp_value_arr'])) {
                foreach ($v['sp_value_arr'] as $vl) {
                    $data[$k]['sp_value_msg'] .= $list['sp_list'][$vl]['sp_name'] . ':' . $list['sp_list'][$vl]['sp_value_name'] . ' ';
                }
            }
        }
        $list   = $list->toArray();
        $return = [
            'msg'       => '获取成功',
            'result'    => '1',
            'rows'      => $data,
            'extInfo'   => '',
            'pageindex' => $params['page'],
            'pages'     => ceil($list['total'] / $params['pageSize']),
            'records'   => $list['total'],
        ];
        return $this->setResponseData($return)->send();
    }

    /**
     * @return void
     * 卡券订单
     */
    public function order_card(AdminValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("order_card")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order_model   = new BuOrder();
        $db_card_model = new DbCard();
        $dlr_code      = $requestData['dlr_code'];
        $vin           = $requestData['vin'] ?? '';
        $order_code    = $requestData['order_code'] ?? '';
        if (!$vin && !$order_code) {
            return $this->setResponseError('参数缺失！')->send();
        }
        $order_info = [];
        $thirdReturn = [];
        // 第三方商城订单
        if ((!empty($order_code) && !containsSpecificPattern($order_code)) || !empty($vin)) {
            $thirdReturn = JavaNewMedia::create('java_new_media')->orderCardList($dlr_code, $order_code, $vin);
        }

        // 日产商城订单
        if ((!empty($order_code) && containsSpecificPattern($order_code)) || !empty($vin)) {
            // NI+商城订单
            $check_order = $this->order_card_info($order_code, $dlr_code, $vin);
            if ($check_order['error'] == 1) {
//                return $this->setResponseData([])->send();
                $list = [];
            } else {
                $list = $check_order['list'];
            }
            foreach ($list as $k => $v) {
                $order_info[$v['order_code']]['card_all_money'] = 0;
            }
            foreach ($list as $k => $v) {
                //card_quota
                $order_card = $db_card_model->getList(['where' => ['id' => ['in', $v['goods_card_ids']]], "field" => "e3s_ids,card_name,card_quota,id,e3s_activity_id"]);
//            echo $db_card_model->getLastSql();
                $order_card_m = 0;
                $card_att     = '';
                $card_ids = '';
                $card_names = '';
                $e3s_ids = '';
                $e3s_type_ids = '';
                if ($order_card) {
                    foreach ($order_card as $ccc) {
                        $order_card_m += $ccc['card_quota'] * $v['count'];
                        $card_att     .= $ccc['e3s_activity_id'] . ',';
                        $card_ids .= $ccc['id']. ',';
                        $card_names .= $ccc['card_name']. ',';
                        $e3s_ids .= $ccc['e3s_ids']. ',';
                        //$e3s_type_ids .= $ccc['e3s_type_ids']. ',';
                    }
                }
                $card_part_no = $e3s_type_ids;
                if(!empty($e3s_ids)){
                    $card_part_no = $e3s_ids;
                }


                $order_info[$v['order_code']]['order_code']         = $v['order_code'];
                $order_info[$v['order_code']]['order_status']       = $v['order_status'];
                $order_info[$v['order_code']]['order_status_name']  = $order_model::orderStatus($v['order_status']);
                $order_info[$v['order_code']]['order_name']         = "商城订单";
                $order_info[$v['order_code']]['order_channel']      = 0;
                $order_info[$v['order_code']]['order_channel_name'] = "微信";
                $order_info[$v['order_code']]['card_all_money']     += $order_card_m;
                $order_info[$v['order_code']]['order_total_money']  = $v['total_money'];
                $order_info[$v['order_code']]['goods_card_ids']     = $v['goods_card_ids'];
                $order_info[$v['order_code']]['yh_money']           = $v['yh_money'];
                $order_info[$v['order_code']]['order_money']        = $v['money'] + $v['integral'] / 10 + $v['pre_point'] / 10 + $v['pre_use_money'];
                $order_info[$v['order_code']]['pay_money']          = $v['money'] + $v['pre_use_money'];
                $order_info[$v['order_code']]['point']              = $v['integral'] + $v['pre_point'];
                $order_info[$v['order_code']]['order_goods'][]      = [
                    'order_code'     => $v['order_code'],
                    'commodity_name' => $v['commodity_name'],
                    // 'act_id'         => trim($card_att, ','),
                    'act_name'       => $v['act_name'],
                    'count'          => $v['count'],
                    'actual_price'   => sprintf("%.2f", ($v['actual_point'] / 10 + $v['actual_use_money'])),
                    'price'          => $v['price'],
                    'card_ids'         => trim($card_ids, ','),
                    'card_names'         => trim($card_names, ','),
                    'card_part_no'         => trim($card_part_no, ','),

                ];
            }
        }
        $result = [];
        if (!empty($order_info)) {
            $result = array_merge($result, $order_info);
        }
        if (isset($thirdReturn['result']) && $thirdReturn['result'] == 1) {
            $data = $thirdReturn['rows'];
            if (!empty($data)) {
                $result = array_merge($result, $data);
            }
        }

        return $this->setResponseData(array_values($result))->send();
    }

    /**
     * @param BdpValidate $validate
     * @return \think\Response|void
     * 核销
     */
    public function order_card_consume(AdminValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("order_card_consume")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $dlr_code   = $requestData['dlr_code'];
        $order_code = $requestData['order_code'];
        if (!$order_code) {
            return $this->setResponseError('参数缺失！')->send();
        }
        // 判断是否是NI+商城订单
        if (!containsSpecificPattern($order_code)) {
            //  第三方商城订单
            $return = JavaNewMedia::create('java_new_media')->cardConsume($dlr_code, $order_code);
            if ($return['result'] == 1) {
                return $this->setResponseData('ok')->send();
            } else {
                return $this->setResponseError('发货失败！')->send();
            }

        } else {
            // NI+商城订单
            $order_model = new BuOrder();
            $check_order = $this->order_card_info($order_code, $dlr_code);
            if ($check_order['error'] == 1) {
                return $this->setResponseError($check_order['msg'])->send();
            } else {
                $list                = $check_order['list'];
                $order_card_code_arr = array_column($list, 'goods_card_codes');
                $order_id_arr        = array_column($list, 'order_id');
                $order_ids           = implode(',', $order_id_arr);
                $order               = $list[0];
                if ($order) {
                    $userId = $order['user_id'];
                    if ($order['order_status'] == 9) {
                        return $this->setResponseError('卡券已被核销！')->send();
//                    print_json(0,'卡券已被核销');
                    }
                    $card_r_model = new BuCardReceiveRecord();
                    $data         = array(
                        'order_status'      => 19,//cong19->7 2022-08-10 17:21:32 tzl   2024-12-07 7->19
                        'verification_user' => 'e3s_c',
                        'last_updated_date' => date('Y-m-d H:i:s'),
                        'delivery_time'     => date('Y-m-d H:i:s')
                    );
                    $res          = $order_model->saveData($data, ['id' => $order['order_id']]);
                    $data         = array(
                        'status'             => 3,
                        'consume_date'       => date('Y-m-d H:i:s'),
                        'consume_dlr_code'   => $dlr_code,
                        'consume_order_code' => $order_code,
                    );
                    $where        = ['user_id' => $userId, 'act_id' => ['in', $order_ids]];
                    $card_res     = $card_r_model->saveData($data, $where);//把卡券做成 已核销
                    // 实时核销
                    $order_card_code_arr = explode(',', $order['goods_card_codes']);

                    $res = (new NetUser())->couponReceive($order_card_code_arr, [], 2, 2);
                    //要通过接口去核销--
                    if ($res) {
                        $net_order = new NetOrder();
                        $net_order->orderChange($order['order_code']);
                        return $this->setResponseData('ok')->send();
                    } else {
                        return $this->setResponseError('发货失败！')->send();
//                    print_json(1, '发货失败');
                    }

                }
            }
        }

    }

    public function card_redirect_url()
    {
        # center_card_id 卡券中心创券id  （card 的id）
        # 卡券中心领取的卡券记录id
        # 核销码
        $requestData = $this->request->only(['center_card_id', 'id', 'coupon_code', 'shop_card_id', 'received_card_id']);

        if (!empty($requestData['center_card_id'])) {
            $cardInfo = (new DbCard())->getOne(['where' => ['quick_win_card_id' => $requestData['center_card_id']]]);
        } else {
            $cardInfo = (new DbCard())->getOneByPk($requestData['shop_card_id'] ?? 0);
        }

        if (empty($cardInfo)) {
            return $this->setResponseError('获取失败！')->send();
        } else {

            $brandConfig = config('card_redirect_url');

            //根据 brand
            //根据coupon_code查询订单记录

            if (!empty($requestData['coupon_code'])) {
                $buRecord = (new BuCardReceiveRecord())->getOne(['where' => ['coupon_code' => $requestData['coupon_code']]]);
            } else {
                $buRecord = (new BuCardReceiveRecord())->getOneByPk($requestData['received_card_id'] ?? 0);
            }

            if (empty($buRecord)) {
                return $this->setResponseError('获取失败！')->send();
            }

            $info = $brandConfig[$this->brand];
            $url  = '';

            if ($cardInfo['card_type'] == 1 || $cardInfo['card_type'] == 2) {
                #商品列表
                if (strpos($this->channel_type, 'SM') !== false) {
                    #小程序
                    $url = $info['coupon_list']['mini'] . $cardInfo['id'];
                } else {
                    #app
                    $url = $info['coupon_list']['app'] . $cardInfo['id'];
                }
            } else if ($cardInfo['card_type'] == 6) {
                #订单详情
                if (strpos($this->channel_type, 'SM') !== false) {
                    #小程序
                    $url = $info['order_info']['mini'] . $buRecord['act_id'];
                } else {
                    #app
                    $url = $info['order_info']['app'] . $buRecord['act_id'];
                }
            }

            return $this->setResponseData(['url' => $url])->send();
        }
    }

    /**
     * @return void
     * E3S--核销码对应卡券状态+订单退款状态
     */
    public function user_card_status(AdminValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("user_card_status")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $card_r_model =  new BuCardReceiveRecord();
        $order_model   = new BuOrder();

        $card_code      = $requestData['card_code'];
        $card_r =  $card_r_model->getOne(['where'=>['coupon_code'=>$card_code]]);
        if(!$card_r){
            return $this->setResponseError("卡券领券记录不存在")->send();
        }
        $re_data = [];
        $where = ['a.order_status' => ['not in', [1,3,8]],'a.is_enable'=>1,'b.is_enable'=>1];
        $where[]=['exp',sprintf("FIND_IN_SET('%s',b.card_codes)",$card_r['card_code'])];
        if($card_r['receive_vin']){
            $where['order_vin'] = $card_r['receive_vin'];
        }

        if($card_r['user_id']){
            $where['user_id'] = $card_r['user_id'];
        }

        $field = ' a.order_code,a.order_status,c.afs_status';
        $list  = $order_model->alias('a')->join('t_bu_order_commodity b', "a.order_code=b.order_code")
            ->join('t_db_after_sale_orders c', 'c.order_id=a.id and c.afs_status not in (2, 3, 6, 8, 11)', 'left')
            ->where($where)->field($field)->select();
        $statusData = BuCardReceiveRecord::STATUS_LIST;
        $re_data['card_status'] = $card_r['status'];
        $re_data['card_status_name'] = $statusData[$card_r['status']];
        if($list){
            $order =  $list[0];
            $re_data['order_status'] = $order['order_status'];
            $re_data['order_status_name'] = $order_model::orderStatus($re_data['order_status']);
            $re_data['order_after'] = 0;
            if($order['afs_status']){
                $re_data['order_after'] = 1;
            }
        }
        return $this->setResponseData($re_data)->send();


    }

    private function order_card_info($order_code, $dlr_code, $vin = '')
    {
        $order_model    = new BuOrder();
        $af_order_model = new DbAfterSaleOrders();
        $where          = ['a.dd_dlr_code' => $dlr_code, 'a.order_status' => ['in', [2, 19, 7]], 'b.dd_commodity_type' => 7];
        if ($order_code) {
            $where['a.order_code'] = $order_code;
        }
        if ($vin) {
            $where['a.order_vin'] = $vin;//汤说改的
        }
        $field = 'a.order_code,a.user_id,a.order_status,a.b_act_goods_price  total_money,a.money,a.all_act_yh+a.all_card_yh  yh_money,a.money,a.integral,b.commodity_name,b.e3s_activity_id,b.count,b.actual_price,b.price,a.id order_id,b.goods_card_ids,b.goods_card_codes,c.id cid,a.money pay_money,a.pre_point,a.pre_use_money,b.card_all_dis,b.actual_point,b.actual_use_money,b.act_name';
        $list  = $order_model->alias('a')
            ->join('t_bu_order_commodity b', "a.order_code=b.order_code")
            ->join('t_db_after_sale_orders c', 'c.order_id=a.id and c.afs_status not in (2, 3, 6, 8, 11)', 'left')
            ->where($where)
            ->field($field)->select();
//        echo $order_model->getLastSql();die();
        if (!$list) {
            return ['error' => 1, 'msg' => '订单异常'];
        }
        foreach ($list as $k => $v) {
            if ($v['cid']) {
                unset($list[$k]);
            }
        }
        return ['error' => 0, 'list' => $list];
    }


}
