<?php
namespace app\api\controller;



use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\db\DbActivity;
use app\common\model\db\DbActivityCenterLog;
use app\common\model\db\DbCard;
use app\common\model\db\DbUser;
use app\common\net_service\NetCardKafKa;
use app\common\port\connectors\Member;
use ForkModules\Traits\ResponseTrait;
use think\Controller;

class CardKafka extends Controller
{
    use ResponseTrait;
    /**
     * 核销卡券
     * @return void
     */
    public function consume_card(){
        $data = trim(file_get_contents('php://input'));//一个JSON
        //$data = "{\"id\":558995715,\"enable\":true,\"created_date\":\"2024-10-08T15:42:26.675 08:00\",\"creator\":\"SYSTEM\",\"modified_date\":\"2024-10-08T15:42:23.788 08:00\",\"modifier\":\"SYSTEM\",\"coupon_code\":\"2000050717\",\"coupon_id\":305903,\"coupon_log_id\":882408,\"reserve_id\":null,\"coupon_title\":\"线上线下核销10801\",\"consume_type\":5,\"receive_scene_id\":59,\"activity_id\":\"\",\"coupon_type\":1,\"reserve_type\":null,\"created_role_type\":1,\"created_role\":\"\",\"receive_date\":\"2024-10-08 15:42:23\",\"reveive_status\":0,\"reserve_status\":0,\"reserve_date\":null,\"one_id\":null,\"user_name\":\"王子威\",\"user_phone\":\"13876318931\",\"vin\":\"LGBF5AE03HR272035\",\"receive_type\":null,\"receive_channel\":4,\"receive_source\":\"activitycenter\",\"intention_car_series_id\":null,\"intention_car_type_id\":null,\"intention_brand_id\":\"1\",\"intention_store_id\":null,\"intention_store\":null,\"used_store_id\":null,\"used_store\":null,\"effective_date\":\"2024-10-01 00:00:00\",\"deadline_date\":\"2024-10-31 23:59:59\",\"consume_date\":null,\"is_sync\":0,\"effective_beg_date\":null,\"effective_end_date\":null,\"receive_coupon_status\":null,\"receive_coupon_scene\":null,\"repair_coupon_id\":null,\"activate_begin_date\":null,\"activate_end_date\":null,\"msg_id\":null,\"campaign_id\":\"1843557393167659010\",\"campaign_title\":\"商城用券10.08\",\"association_type\":1}";
        $card_info = json_decode($data, true);

        $logData=[
            'activity_id'=>'kafka-consume',
            'request_id'=>$card_info['coupon_id'],
            'oneid'=> $card_info['coupon_code'] ?? '', // 卡券核销码
            'request_url'=>"test",
            'request_info'=>$data,
        ];
        $dbActivityCenterLog =  new DbActivityCenterLog();
        $logid = $dbActivityCenterLog->insertGetId($logData);

        $buCardReceiveRecordObj = new BuCardReceiveRecord();
        if(!empty($card_info)){
           $receiveInfo = $buCardReceiveRecordObj->where(['is_enable'=>1,'coupon_code'=>$card_info['coupon_code'],'status'=>['in', [1,5]]])->find();
           if(!empty($receiveInfo)){
               $upd = [
                   'status'            => 3,
                   'is_get_card'       => 3,
                   'consume_date'      => $card_info['consume_date'],
                   'use_vin'           => $card_info['vin'],
                   'consume_dlr_code'  => $card_info['used_store'],
                   'coupon_receive_id' => $card_info['id'],
                   'modifier'          => 'consume_card'
               ];
               $buCardReceiveRecordObj->where(['id' => $receiveInfo['id']])->update($upd);
           }
        }
        return $this->setResponseData('ok')->send();
    }


    /**
     * 241118 会议决定冻结方法不再使用此方法
     * 冻结卡券
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function freeze_card(){
//        $data = trim(file_get_contents('php://input'));//一个JSON
//        // $data = '{"reveive_status":0,"freeze_status":0,"coupon_code":"4001051810"}';
//        $card_data = json_decode($data, true);
//        $logData=[
//            'activity_id'=>'kafka',
//            'request_id'=>'freeze_card',
//            'oneid'=>"",
//            'request_url'=>"test",
//            'request_info'=>$data,
//        ];
//        $dbActivityCenterLog =  new DbActivityCenterLog();
//        $logid = $dbActivityCenterLog->insertGetId($logData);
//        $buCardReceiveRecordObj = new BuCardReceiveRecord();
//        if(!empty($card_data)) {
//            if (!empty($card_data['coupon_code'])) {
//                $status = '';
//                if($card_data['freeze_status'] == 1 ){
//                    $status = 5;
//                }else{
//                    $status = 7;
//                }
////                if(strlen($card_data['reveive_status']) > 0 && $card_data['reveive_status'] == 0 && $card_data['freeze_status'] == 1){ //冻结
////                    $status = 5;
////                }else if(strlen($card_data['reveive_status']) > 0 && $card_data['reveive_status'] == 0 && $card_data['freeze_status'] == 0) { //未冻结
////                    $status = 1;
////                }else{
////                    $status = 7;
////                }
//               // dd($status);
//                if(!empty($status)){
//                    $buCardReceiveRecordObj->where(['is_enable' => 1, 'coupon_code' => $card_data['coupon_code']])->update(['status'=>$status]);
//                }
//
//            }
//        }
//        return $this->setResponseData('ok')->send();
    }

    /**
     * 领取卡券
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function update_card_receive(){
        $data = trim(file_get_contents('php://input'));//一个JSON
        $card_data = json_decode($data, true);
        $logData=[
            'activity_id'=>'kafka-update',
            'request_id'=>$card_data['coupon_id'],
            'oneid'=> $card_data['coupon_code'] ?? '', // 卡券核销码
            'request_url'=>"test",
            'request_info'=>$data,
        ];
        $dbActivityCenterLog =  new DbActivityCenterLog();
        $logid = $dbActivityCenterLog->insertGetId($logData);
        $dbUserObj = new DbUser();
        $buCardReceiveRecordObj = new BuCardReceiveRecord();
        $net_card_kafka = new NetCardKafKa();
        //先查卡券是否存在，再查是否有活动，如果没有活动，跟人跟车就读卡券即可如果是跟车的，那么unset($card_data['one_id']);
        if(!empty($card_data)){
            $card_data['freeze_status'] = $card_data['freeze_status'] ?? "";
            $businessOrderNo = $card_data['business_order_no'] ?? ''; // 业务单号

            if(!empty($card_data['one_id'])){
                //是否商城用户
                $userInfo = $dbUserObj->where(['one_id'=>$card_data['one_id'],'is_enable'=>1])->order("id asc")->find();

                $card_data['user_id'] = $userInfo['id'] ?? '';
                if(!empty($userInfo)){
                    //有就更新没有就新增
                    $receiveInfo = $buCardReceiveRecordObj->where(['is_enable'=>1,'coupon_code'=>$card_data['coupon_code']])->find();
                    if(!empty($receiveInfo)){
                        $status = $this->getReceiveCouponStatus($card_data['reveive_status'],$card_data['reserve_status'],$card_data['freeze_status']);
                        $upd = [
                            'status'=>$status,
                            'coupon_receive_id'=>$card_data['id'],
                            'receive_vin'=>trim($card_data['vin']),
                            'last_updated_date' => $card_data['modified_date']
                        ];
                        // 待激活
                        if ($status == 7) {
                            $upd['intention_store'] = '';
                            $upd['get_dlr_code'] = '';
                        }
                        if (in_array($status, [1,7])) {
                            $upd['consume_order_code'] = '';
                        }
                        // 冻结
                        if ($status == 5) {
                            // 判断是否需要冻结卡券
                            $re = $net_card_kafka->freezeCoupon($businessOrderNo, $card_data['coupon_code']);
                            if ($re) {
                                $upd['consume_order_code'] = $businessOrderNo; // 冻结单号
                                $buCardReceiveRecordObj->where(['is_enable'=>1,'coupon_code'=>$card_data['coupon_code']])->update($upd);
                            }
                        } else {
                            $buCardReceiveRecordObj->where(['is_enable'=>1,'coupon_code'=>$card_data['coupon_code']])->update($upd);
                        }
                    }else{
                        $this->createCard($card_data);
                    }
                }else{ //不是商城用户
                    $memberInfo = Member::create('member')->oneIdToMemberId($card_data['one_id']);

                    $createUserData = [
                        'openid'=>$memberInfo['oauth']['openid'] ?? '',
                        'name'=>$memberInfo['user_name'] ?? "",
                        'phone'=>$memberInfo['phone'] ?? "",
                        'unionid'=>$memberInfo['oauth']['unionid']?? '',
                        'one_id'=>$memberInfo['oneid'] ?? "",
                        'nickname_app'=>$memberInfo['app_nickname'] ?? "",
                        'headimg_app'=>$memberInfo['app_avatar'] ?? "",
                        'plat_id'=>$memberInfo['member_id'] ?? "",
                        'creator' => 'kafka_user',
                    ];
//                    $userInfo = $dbUserObj->where(['one_id'=>$card_data['one_id'],'is_enable'=>1])->order("id asc")->find();
//                    $card_data['user_id'] = $userInfo['id'] ?? '';
                    if(empty($userInfo)){
                        $userId =  $dbUserObj->insertGetId($createUserData);
                        $card_data['user_id'] = $userId;
                    }
                    $this->createCard($card_data);

                }
            }else if(!empty($card_data['vin'])){
                $receiveInfo = $buCardReceiveRecordObj->where(['is_enable'=>1,'coupon_code'=>$card_data['coupon_code']])->find();
                if(!empty($receiveInfo)){
                    $status = $this->getReceiveCouponStatus($card_data['reveive_status'],$card_data['reserve_status'],$card_data['freeze_status']);
                    $upd = [
                        'status'=>$status,
                        'coupon_receive_id'=>$card_data['id'],
                        'receive_vin'=>trim($card_data['vin']),
                    ];
                    // 待激活
                    if ($status == 7) {
                        $upd['intention_store'] = '';
                        $upd['get_dlr_code'] = '';
                    }
                    if (in_array($status, [1,7])) {
                        $upd['consume_order_code'] = '';
                    }
                    // 冻结
                    if ($status == 5) {
                        // 判断是否需要冻结卡券
                        $re = $net_card_kafka->freezeCoupon($businessOrderNo, $card_data['coupon_code']);
                        if ($re) {
                            $upd['consume_order_code'] = $businessOrderNo; // 冻结单号
                            $buCardReceiveRecordObj->where(['is_enable'=>1,'coupon_code'=>$card_data['coupon_code']])->update($upd);
                        }
                    } else {
                        $buCardReceiveRecordObj->where(['is_enable'=>1,'coupon_code'=>$card_data['coupon_code']])->update($upd);
                    }
                }else{
                    $this->createCard($card_data);
                }

            }
        }
        return $this->setResponseData('ok')->send();
    }


    public function createCard($card_data){
        $buCardReceiveRecordObj = new BuCardReceiveRecord();
        $dbCardObj = new DbCard();
        $cardInfo = $dbCardObj->where(['quick_win_card_id'=>$card_data['coupon_id'],'is_enable'=>1])->find();
        if(!empty($cardInfo)){
            $dbActivityObj      = new DbActivity();
            $record_vin = 1;
            $vin ='';
            //卡券领取，
            //1如果有活动，取当前活动  “活动属性: 1-选人 2-选车 3-选人+车” 如果 是2 3的，才记录卡券中心记录的vin，
            //2如果没有活动，卡券中心传了vin就记录vin
            //产品LR
            if($cardInfo['activity_id']>0){
                $dbActivityCardInfo = $dbActivityObj->where(['activity_id' => $cardInfo['activity_id'], 'is_enable' => 1])->find();
                if ($dbActivityCardInfo['select_obj'] == 1) {
                   $record_vin = 0;
                }
            }
            if($record_vin){
                $vin = !empty($card_data['vin']) ? $card_data['vin'] : "";
                $vin = trim($vin);
            }



            if (isset($card_data['effective_date']) && $card_data['deadline_date']) {
                $date_start = $card_data['effective_date'];
                $date_end   = $card_data['deadline_date'];
            } else {
                if ($cardInfo['date_type'] == 1) {
                    $date_start = $cardInfo['validity_date_start'];
                    $date_end   = $cardInfo['validity_date_end'] . ' 23:59:59';
                } else {
                    //固定时长专用，领取后多少天内有效，单位为天(有效天数)
                    //  `fixed_begin_term` '固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天',
                    $date_start = date('Y-m-d H:i:s', strtotime(sprintf(" +%s day", $cardInfo['fixed_begin_term'])));
                    $date_end   = date('Y-m-d H:i:s', strtotime(sprintf(" +%s day", $cardInfo['fixed_term'])));
                }
            }


            $data                = array(
                'user_id'       => $card_data['user_id'] ?? '',
                'vin'           => $vin,
                'license_plate' => isset($car['car_no']) ? $car['car_no'] : '',
                'name'          => $card_data['user_name'],
                'source'        => 26,
                'act_id'        => "kafka_act",
                'phone'         =>  $card_data['user_phone'],
                'card_id'       => $cardInfo['id'],
                'dlr_code'      => 'kafka',
                'openid'        => '',
                'creator'       => "kafka_creator",
                'car_18n'       => '',
                //'created_date'     => date('Y-m-d H:i:s',time()+mt_rand(1,10)),
                'coupon_code'      => $card_data['coupon_code'] ?? '',
                'validity_date_start' =>  $date_start,
                'validity_date_end'   =>  $date_end,
                'is_get_card'       =>1,
                'status'   =>$this->getReceiveCouponStatus($card_data['reveive_status'],$card_data['reserve_status'],$card_data['freeze_status']),
                'one_id' => $card_data['one_id'],
                'coupon_receive_id'=>$card_data['id'],
                'receive_vin'=>$vin,//20241023 产品说不判断活动
                'activity_id'=> $cardInfo['activity_id'] ?? '',
                'created_date' => $card_data['created_date']
            );
//            if (!empty($cardInfo['activity_id'])) {
//                $dbActivityObj      = new DbActivity();
//                $dbActivityCardInfo = $dbActivityObj->where(['activity_id' => $cardInfo['activity_id'], 'is_enable' => 1])->find();
//
//                if ($dbActivityCardInfo['select_obj'] == 2 || $dbActivityCardInfo['select_obj'] == 3) {
//                    $data['receive_vin'] = $vin;
//                }
//            }

            $card_code           = $this->_getOrderNo('kafkac' . date('YmdHis'), 3);
            $data['card_code']   = $card_code;
            $buCardReceiveRecordObj->insertGetId($data);
        }

    }

    private function getReceiveCouponStatus($reveive_status,$reserve_status,$freeze_status){
        //240920变更 当$reserve_status == 1 表示未激活就直接返回未激活，返之则判断 reveive_status卡卷状态
        if($reserve_status == 1){//待激活
            return 7;
        }else{
            if($freeze_status == 2){ //冻结默认不处理
                $data = [
                    0=>1,
                    1=>3,
                    4=>4
                ];
                return $data[$reveive_status];
            }else{
                if($freeze_status == 1){//冻结
                    return 5;
                }else{//解冻
                    return 1;
                }
            }
        }

        //商城状态，1领取，2,未领取,3核销,4已失效
        //reserve_status预占状态 0非预占 1预占(未激活)
        //reveive_status卡卷状态;0-未核销 1-已核销 2-已过期 3-未生效 4-已失效

    }

    /**
     * 生成订单号
     */
    protected function _getOrderNo($preStr = '', $length = 7)
    {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        $str   = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }

        return $preStr . $str;
    }

}
