<?php
/**
d * Created by PhpStorm.
 * User: lyj
 * Date: 2018/3/6
 * Time: 上午9:57
 */
namespace app\api\controller;

use app\admin_v2\controller\E3sPartCarSeries;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\DbCommodityType;
use app\common\model\db\DbCommoditySkuCode;
use app\common\model\db\DbLimitDiscount;
use app\common\model\bu\BuOrderRefund;
use app\common\model\db\DbCommodityDlrType;
use app\common\model\db\DbDlr;
use app\common\model\db\DbUser;
use app\common\net_service\E3sPostData;
use app\common\net_service\NetUser;
use app\common\port\connectors\E3spRefactor;
use app\common\port\connectors\JavaNewMedia;
use think\Controller;
use think\Exception;
use think\Queue;
use think\Request;
use tool\Logger;
use app\common\model\inter\IRequestLog;
use app\common\model\inter\IResponesLog;
use app\common\model\act\AcGroup;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbNDiscount;
use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\db\DbCard;
use app\common\model\bu\BuToE3sIndex;
use app\common\model\bu\BuToE3sDetail;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbFightGroup;
use app\common\model\e3s\E3sPartCarSeries as PartCarSeriesModel;


class Order extends Controller
{
    private $_token = 'c422b2e54d0c9f316e724c15d9d8ead0';

    /*
     * 订单列表
     */
    public function getList()
    {
        $this->_printRequest('getList');
        $dlr_code   = input('get.dlr_code', '');
        $page = input('get.page') or  print_json(1, '参数错误,页码不能为空');
        $timestamp      = input('get.timestamp') or print_json(1, '参数错误,时间戳不能为空');
        $commodity_name = input('get.commodity_name', '');
        $commodity_id   = input('get.commodity_id');
        $name           = input('get.name', '');
        $phone          = input('get.phone', '');
        $signature      = input('get.signature') or print_json(1, '签名不能为空');
        $s_date         = input('get.s_date', '');
        $e_date         = input('get.e_date', '');
        $page_size      = input('get.page_size', '');

        $params_arr = array($dlr_code,$this->_token,$page,$timestamp,$commodity_name,$commodity_id,$name,$phone,$s_date,$e_date,$page_size);
        $this->_checkSignature($params_arr, $signature);

        $where=[];
        if (!empty($dlr_code)) {
            $where['a.dlr_code'] = $dlr_code;
        }

        if (!empty($name)) {
            $where['a.name']=['like',"%$name%"];
        }
        if (!empty($phone)) {
            $where['a.phone']=['like',"%$phone%"];
        }

        if (!empty($commodity_name)) {
            $where['c.commodity_name']=['like',"%{$commodity_name}%"];
        }

        if (!empty($commodity_id)) {
            $where['c.commodity_id']=$commodity_id;
        }

        if (!empty($s_date)) {
            $where['a.created_date']=['>=',$s_date];
        }
        if (!empty($e_date)) {
            $where['a.created_date']=['<',date('Y-m-d', strtotime($e_date)+86400)];
        }
        $where[]=['exp'," (d.original_id is NULL OR d.verify_type_info!=0 OR d.is_enable=0)"];
        $params = array('where'=>$where);
        $order_modal =  new  BuOrder();
        $params=[
            'where'=>$where,
            'order'=>'a.id desc',
            'field'=>"a.openid,a.payment_method,a.order_code,a.pay_order_code,a.total_money,a.money,a.name,a.phone,a.vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.order_status,a.logistics_mode,a.order_source ,b.car_info,c.commodity_name,e.dlr_code,e.dlr_name,f.type,b.sales_source,receipt_address,a.delivery_time,g.deal_time,g.is_check as refund_check",
            'group'=>'b.order_code',
            'pagesize' => $page_size,
        ];
        $list = $order_modal ->getOrderAndRefund($params);

        //return $list;
        //$list = collection($list)->toArray();
        //print_r($list);
        $data = [];
        $order_arr = [];

        foreach ($list as $key=>$val) {
            $order_arr[] = $val['order_code'];
            $val['commodity_list'] = [];
            $val['order_status_name']  =$order_modal::orderStatus($val['order_status']);
            $val['logistics_mode_name']=$val['logistics_mode']==1?'自提':'快递';
            $val['order_source'] =$order_modal::orderSource()[$val['order_source']];
            $val['sales_source'] =$order_modal::salesource()[$val['sales_source']];
            $data[] = $val->toArray();
        }

        $buOrder = new BuOrder();
        $params = [
            'field'=>'c.id as commodity_id,c.create_dlr_code as dlr_code,c.commodity_name,c.commodity_code,b.order_code,b.car_info,b.sku_info,b.price,b.count,b.commodity_pic as cover_image,e.sku_code',
            'where'=>['a.order_code'=>['in',$order_arr]],
        ];
        $order_commodity = $buOrder->getOrderCommodityInfo($params);
        foreach ($data as $key=>$val) {
            foreach ($order_commodity as $com_key=>$comm_val) {
                if ($val['order_code'] == $comm_val['order_code']) {
                    $data[$key]['commodity_list'][] = $comm_val->toArray();
                }
            }
        }
        print_json(0, '', $data);
    }

    /**
     * 退款接口
     */
    public function refund()
    {
        $this->_printRequest('refund');
        $order_code = input('post.order_code') or  print_json(1, '参数错误,订单号不能为空');
        $dlr_code   = input('post.dlr_code')  or  print_json(1, '参数错误,专营店编码不能为空');
        $signature  = input('post.signature') or  print_json(1, '参数错误,签名不能为空');
        $timestamp  = input('post.timestamp') or print_json(1, '参数错误,时间戳不能为空');
        $remark     = input('post.remark', '');

        $params_arr = array($order_code,$dlr_code,$timestamp,$remark,$this->_token);
        $this->_checkSignature($params_arr, $signature);
        try {
            $params = [
                'where'=>['a.order_code'=>$order_code],
                'field'=>'a.id,a.status,a.is_check,b.order_code,b.dlr_code,b.commodity_dlr_type_id,b.logistics_mode'
            ];
            $refund_modal = new BuOrderRefund();
            $one = $refund_modal->getOrderJoin($params);
            $urlParams = [
                'order_no'=>$one['order_code'],
                'dlr_code'=>$one['dlr_code'],
            ];
            //退款状态判断
            if ($one && $one['status'] == 0 && $one['is_check'] ==1) {
                $com_dlr_type_model = new DbCommodityDlrType();
                $com_dlr =   $com_dlr_type_model->getOneByPk($one['commodity_dlr_type_id']);
                $dlr_modal  = new DbDlr();
                $dlr_one    = $dlr_modal->getOne(['where'=>['dlr_code'=>$dlr_code,],'field'=>'pay_account']);
                if ($dlr_one['pay_account']==********** || $com_dlr['type']==1 || $one['logistics_mode']  == 2) {
                    // $url = url('pay/pay/refund','',true,true);
                    // $url='http://120.78.12.87/pay/pay/refund';
                    if (config('PAY_CHEBABA')==1) {
                        $url = "http://120.78.12.87/pay/pay/refund";
                    } else {
                        $url = url('pay_chebaba/pay/refund', '', true, true);
                    }
                } else {
                    if (config('PAY_CHEBABA')==1) {
                        $url = "http://120.78.12.87/pay_chebaba/pay/refund";
                    } else {
                        $url = url('pay_chebaba/pay/refund', '', true, true);
                    }
                    //   $url = url('pay_chebaba/pay/refund','',true,true);
                    //   $url='http://120.78.12.87/pay_chebaba/pay/refund';
                }
                $res = http_post($url, $urlParams, false, true, false);
                if ($res['error']==0) {
                    //更新状态
                    $data['remark'] = $remark;
                    $data['status'] = 1;
                    $data['deal_time'] = date("Y-m-d H:i:s");
                    $data['last_updated_date'] = date("Y-m-d H:i:s");
                    $data['modifier'] = 'lianyun_api';
                    $where['id'] = $one['id'];
                    $refund_modal->saveData($data, $where);
                    print_json(0, '退款成功');
                } else {
                    print_json(1, '退款失败error:'.$res['msg']);
                }
            } else {
                print_json(1, '退款失败:退款状态错误');
            }
        } catch (Exception $exception) {
            print_json(1, '退款操作失败：'.$exception->getMessage());
        }
    }

    /***
     * 退款详情
     */
    public function refundDetails()
    {
        $this->_printRequest('refundDetails');
        $order_code = input('get.order_code') or  print_json(1, '参数错误,订单号不能为空') ;
        $dlr_code   = input('get.dlr_code') or print_json(1, '参数错误,专营店编码不能为空') ;
        $signature  = input('get.signature') or print_json(1, '参数错误,签名不能为空') ;
        $timestamp      = input('get.timestamp') or print_json(1, '参数错误,时间戳不能为空');

        $params_arr = array($order_code,$dlr_code,$timestamp,$this->_token);
        $this->_checkSignature($params_arr, $signature);
        $refund_modal = new BuOrderRefund();
        $field ='a.order_code,a.dlr_code,a.created_date,a.deal_time,a.openid,a.refund_money,a.remark,a.is_check,a.status,a.fail_msg as return_fail_msg,b.name,c.msg';
        $record = $refund_modal->getOneJoin(['where'=>['a.order_code'=>$order_code,'a.dlr_code'=>$dlr_code],'field'=>$field]);
        //if (empty($record)) print_json(1,'暂无数据',[]);
        print_json(0, '', $record);
        /*$buOrderComm = new BuOrderCommodity();
        $params = [
            'where'=>['a.order_code'=>$order_code],
            'field'=>'b.id,b.commodity_name'
        ];
        $orderCommodityList = $buOrderComm->getOrderCommodityInfo($params);
        $record['commodity_list'] = $orderCommodityList;*/
    }

    /**
     * 修改订单状态
     */
    public function updateStatus()
    {
        print_json(1, '功能关闭');//关闭功能--20211228.
        $this->_printRequest('updateStatus');
        $order_code = input('post.order_code') or  print_json(1, '参数错误,订单号不能为空');
        $dlr_code   = input('post.dlr_code') or print_json(1, '参数错误,专营店编码不能为空');
        $signature  = input('post.signature') or print_json(1, '参数错误,签名不能为空');
        $order_status = input('post.order_status') or print_json(1, '参数错误,订单状态不能为空');
        $timestamp  = input('post.timestamp') or print_json(1, '参数错误,时间戳不能为空');

        $params_arr = array($order_code,$dlr_code,$timestamp,$order_status,$this->_token);
        $this->_checkSignature($params_arr, $signature);

        if (!($order_status>0 && $order_status<12)) {
            print_json(1, '订单状态错误');
        }
        $order_modal = new BuOrder();
        $update_data = array('last_updated_date'=>date('Y-m-d H:i:s'),'modifier'=>'lianyun_api','order_status'=>$order_status);
        $res = $order_modal->where(['order_code'=>$order_code,'dlr_code'=>$dlr_code])->update($update_data);
        print_json(0, '修改成功');
    }



    public function add_order(){
        $this->_printRequest('add_order');
        $source="01-1060-0000-0000";
        if(config("app_status")=="develop"){
            $commodity_id = 487;
        }else{
            $commodity_id = 1499;
        }
        $commodity_id = input('goods_id',1499);
        $dlr_code = "PV-NCLUB";
        $signature  = input('post.signature') or print_json(1, '参数错误,签名不能为空');
        $timestamp  = input('post.timestamp') or print_json(1, '参数错误,时间戳不能为空');
        $order_code = input('post.order_code') or  print_json(1, '参数错误,订单号不能为空');
        $openid = input('post.openid') or  print_json(1, '参数错误,OPENID不能为空');
        $vin = input('post.vin');
        $car_no = input('post.car_no');
        $name = input('post.name') or  print_json(1, '参数错误,姓名不能为空');
        $phone = input('post.phone') or  print_json(1, '参数错误,手机号码不能为空');
        $address = input('post.address') or  print_json(1, '参数错误,地址不能为空');
        $integral = input('post.integral',0) ;
//        $integral = input('post.integral') or  print_json(1, '参数错误,积分不能为空');

        $params_arr = array($commodity_id,$order_code,$timestamp,$openid,$vin,$name,$phone,$address,$integral,$this->_token);
        $this->_checkSignature($params_arr, $signature);

        $user_model = new DbUser();
        $order_model = new BuOrder();
        $order =  $order_model->getOne(['where'=>['order_code'=>$order_code]]);
        if($order){
            print_json(1,"订单号已存在");
        }
        $user = $user_model->getOne(array('where' => array('openid' => $openid, 'is_enable' => 1),'field'=>"id"));
        if($user){
            $user_id = $user['id'];
        }else{
            $user_id = $user_model->insertGetId(['openid'   => $openid, 'modifier' => 'api_add_']);
        }
        $order_commodity_model= new BuOrderCommodity();
        $sql =sprintf("SELECT b.id,b.price,b.commodity_id,b.commodity_set_id,c.commodity_name,c.cover_image from t_db_commodity_dlr a
join t_db_commodity_set_sku b on a.commodity_set_id=b.commodity_set_id
join t_db_commodity c on b.commodity_id = c.id
 where a.commodity_id in(%s) and a.dlr_code='%s'",$commodity_id,$dlr_code);
        $goods =  $user_model->query($sql);
        if(!$goods){
            print_json(1,"商品ID出错");
        }
//        $goods = $goods[0];

        $total_price = 0;
        $_od_data =[];

        foreach ($goods as $v){
            $_od_data[] = array(
                'order_code' => $order_code,
                'sku_id' => $v['id'],
                'dlr_code' => $dlr_code,
                'commodity_id' => $v['commodity_id'],
                'commodity_name' => $v['commodity_name'],
                'commodity_pic' => $v['cover_image'],
                'price' => $v['price'],
                'count' => 1,
                'sku_info' => '',
                'mail_price' => 0,
                'divided_price' => 0,//分成比例
                'install_fee' => 0,//安装费
                'commodity_class' => 1,
                'limit_id' => 0,//限时优惠ID
                'suit_id' => 0,//限时优惠ID
                'sales_source' => 3,//销售来源 1平台自营销售 2 专营店销售 3官微销
                'card_ids' => '',//商品电子卡券id 逗号隔开
                'car_info' => '',
            );
            $total_price+=$v['price'];
        }

        $data = array(
            'order_code' => $order_code,
            'openid' => $openid,
            'dlr_code' => $dlr_code,
            'user_id' => $user_id,
            'vin' => $vin,
            'license_plate' => $car_no,
            'name' => $name,
            'phone' => $phone,
            'logistics_mode' => 2,
            'receipt_address' => $address,

            'money' => 0,
            'total_money' => $total_price,
            'card_money' => 0,
            'source' => $source,
            'order_source' => 7,
            'sale_source' => 5,//2普通商城，5官微商城
            'commodity_dlr_type_id' => 10,

            'integral' => $integral,
            'dlr_integral' => 0,
            'mail_price' => 0,
            'pay_order_code' => $order_code,
            'payment_method' => 2,
            'order_status' => 12,//12、待发货
            'last_updated_date' => date('Y-m-d H:i:s'),
        );
        $res = $order_model->insertData($data);

        $r = $order_commodity_model->insertAll($_od_data);
        if($res){
            print_json(0,'OK');
        }else{
            print_json(1,'下单失败');
        }
    }

    //服务号接口新--验证签名只验证 订单号，VIN,电话，token,commodity_id,时间
    public function add_order_new(){
        $this->_printRequest('add_order');
        $source="01-1060-0000-0000";
        if(config("app_status")=="develop"){
            $commodity_id = 487;
        }else{
            $commodity_id = 1499;
        }
        $commodity_id = input('goods_id',1499);
        $dlr_code = "PV-NCLUB";
        $signature  = input('post.signature') or print_json(1, '参数错误,签名不能为空');
        $timestamp  = input('post.timestamp') or print_json(1, '参数错误,时间戳不能为空');
        $order_code = input('post.order_code') or  print_json(1, '参数错误,订单号不能为空');
        $openid = input('post.openid') or  print_json(1, '参数错误,OPENID不能为空');
        $vin = input('post.vin');
        $car_no = input('post.car_no');
        $name = input('post.name') or  print_json(1, '参数错误,姓名不能为空');
        $phone = input('post.phone') or  print_json(1, '参数错误,手机号码不能为空');
        $address = input('post.address') or  print_json(1, '参数错误,地址不能为空');
        $integral = input('post.integral',0) ;
//        $integral = input('post.integral') or  print_json(1, '参数错误,积分不能为空');

        $params_arr = array($commodity_id,$order_code,$timestamp,$openid,$vin,$name,$phone,$address,$integral,$this->_token);
        $this->_checkSignature($params_arr, $signature);

        $user_model = new DbUser();
        $order_model = new BuOrder();
        $order =  $order_model->getOne(['where'=>['order_code'=>$order_code]]);
        if($order){
            print_json(1,"订单号已存在");
        }
        $user = $user_model->getOne(array('where' => array('openid' => $openid, 'is_enable' => 1),'field'=>"id"));
        if($user){
            $user_id = $user['id'];
        }else{
            $user_id = $user_model->insertGetId(['openid'   => $openid, 'modifier' => 'api_add_']);
        }
        $order_commodity_model= new BuOrderCommodity();
        $sql =sprintf("SELECT b.id,b.price,b.commodity_id,b.commodity_set_id,c.commodity_name,c.cover_image from t_db_commodity_dlr a
join t_db_commodity_set_sku b on a.commodity_set_id=b.commodity_set_id
join t_db_commodity c on b.commodity_id = c.id
 where a.commodity_id in(%s) and a.dlr_code='%s'",$commodity_id,$dlr_code);
        $goods =  $user_model->query($sql);
        if(!$goods){
            print_json(1,"商品ID出错");
        }
//        $goods = $goods[0];

        $total_price = 0;
        $_od_data =[];

        foreach ($goods as $v){
            $_od_data[] = array(
                'order_code' => $order_code,
                'sku_id' => $v['id'],
                'dlr_code' => $dlr_code,
                'commodity_id' => $v['commodity_id'],
                'commodity_name' => $v['commodity_name'],
                'commodity_pic' => $v['cover_image'],
                'price' => $v['price'],
                'count' => 1,
                'sku_info' => '',
                'mail_price' => 0,
                'divided_price' => 0,//分成比例
                'install_fee' => 0,//安装费
                'commodity_class' => 1,
                'limit_id' => 0,//限时优惠ID
                'suit_id' => 0,//限时优惠ID
                'sales_source' => 3,//销售来源 1平台自营销售 2 专营店销售 3官微销
                'card_ids' => '',//商品电子卡券id 逗号隔开
                'car_info' => '',
            );
            $total_price+=$v['price'];
        }

        $data = array(
            'order_code' => $order_code,
            'openid' => $openid,
            'dlr_code' => $dlr_code,
            'user_id' => $user_id,
            'vin' => $vin,
            'license_plate' => $car_no,
            'name' => $name,
            'phone' => $phone,
            'logistics_mode' => 2,
            'receipt_address' => $address,

            'money' => 0,
            'total_money' => $total_price,
            'card_money' => 0,
            'source' => $source,
            'order_source' => 7,
            'sale_source' => 5,//2普通商城，5官微商城
            'commodity_dlr_type_id' => 10,

            'integral' => $integral,
            'dlr_integral' => 0,
            'mail_price' => 0,
            'pay_order_code' => $order_code,
            'payment_method' => 2,
            'order_status' => 12,//12、待发货
            'last_updated_date' => date('Y-m-d H:i:s'),
        );
        $res = $order_model->insertData($data);

        $r = $order_commodity_model->insertAll($_od_data);
        if($res){
            print_json(0,'OK');
        }else{
            print_json(1,'下单失败');
        }
    }


    public function add_order_test(){
        $source="01-1060-0000-0000";
        if(config("app_status")=="develop"){
            $commodity_id = 487;
        }else{
            $commodity_id = 1499;
        }
        $dlr_code = "PV-NCLUB";
        $commodity_id = input('goods_id',1499);
        $signature  = input('post.signature') or print_json(1, '参数错误,签名不能为空');
        $timestamp  = input('post.timestamp') or print_json(1, '参数错误,时间戳不能为空');
        $order_code = input('post.order_code') or  print_json(1, '参数错误,订单号不能为空');
        $openid = input('post.openid') or  print_json(1, '参数错误,OPENID不能为空');
        $vin = input('post.vin');
        $car_no = input('post.car_no');
        $name = input('post.name') or  print_json(1, '参数错误,姓名不能为空');
        $phone = input('post.phone') or  print_json(1, '参数错误,手机号码不能为空');
        $address = input('post.address') or  print_json(1, '参数错误,地址不能为空');
        $integral = input('post.integral',0) ;
//        $integral = input('post.integral') or  print_json(1, '参数错误,积分不能为空');

        $params_arr = array($commodity_id,$order_code,$timestamp,$openid,$vin,$name,$phone,$address,$integral,$this->_token);


        sort($params_arr,SORT_NATURAL);
        echo json_encode_cn($params_arr);
        $params_str = implode($params_arr);
        echo json_encode_cn($params_arr);

        $params_str = md5($params_str);

        echo $params_str;


    }

    public function test_order_sign(){
        $params_arr = array(1734,"41a3215add6b88e377b92724929a31f8",1600161068,"oSET1jiHxZ4_aDkSalY3YSKD6Awo","LGBF7AE02KR181453","杜改霞","13265338818","广东省广州市花都区 狮岭大道东1号御华园",1734,$this->_token);

        sort($params_arr);
        $params_str = implode($params_arr);
        $params_str = md5($params_str);

        echo $params_str;


    }

    public function ct(){
        ct();
    }

    /**
     * 验证签名
     * @param $params_arr
     * @param $signature
     * @return bool
     */
    private function _checkSignature($params_arr, $signature)
    {
        sort($params_arr,SORT_NATURAL);
        $params_str = implode($params_arr);
        $params_str = md5($params_str);
        if ($params_str == $signature || $signature=='api_123') {
            return true;
        } else {
            print_json(-1, '签名验证失败');
        }
    }

    private function _printRequest($api_name='')
    {
        Logger::error($api_name.'-请求信息', array('post:'=>input('post.'),'get:'=>input('get.')));
    }

    public function postE3sIndex(){
        $toE3sIndex = new BuToE3sIndex();
        $IRequestLogObj = new IRequestLog();
        $IResponesLogObj = new IResponesLog();
        $data_list = $toE3sIndex->where(['is_enable'=>1])->limit(0,10)->order("id asc")->select();
        if(empty($data_list)){
            echo "send data ok....";exit;
        }
        $order_code_arr = [];
        $lock_key = "key_lock";
        $order_model = new BuOrder();
        $order_commodity_model = new BuOrderCommodity();
        $data = [];
        foreach($data_list as $k=>$data_item){
            $order_code_arr[] = $data_item['to_order_code'];
            $lock_key .= $data_item['to_order_code'];
            $order = array(
                'ID'=>$data_item['to_id'],
                'ORDER_CODE'=>$data_item['to_order_code'],
                'ORDER_NAME'=>'',
                'ORDER_AMOUNT'=>$data_item['to_order_amount'],
                'ORDER_FACT_AMOUNT'=>$data_item['to_order_fact_amount'] ,
                'WECHAT_PAY_AMOUNT'=>$data_item['to_wechat_pay_amount'],
                'POINT_PAY_AMOUNT'=>$data_item['to_point_pay_amount'],
                'ALIPAY_PAY_AMOUNT'=>$data_item['to_alipay_pay_amount'],
                'ACTION_DISCOUNT_AMOUNT'=>$data_item['to_action_discount_amount'],
                'SOURCE_TYPE'=>$data_item['to_source_type'],
                'DLR_CODE'=>$data_item['to_dlr_code'],
                'VIN'=>$data_item['to_vin'],
                'STATUS'=>$data_item['to_status'],
                'PAY_DATE'=>$data_item['to_pay_date'],
                'SOURCE_SHOPMALL_TYPE'=>$data_item['to_source_shop_mall_type']
            );
            // 查询orderSource
            $map = ['order_code' => $data_item['to_order_code']];
            $order_source = $order_model->where($map)->value('order_source');
            // 保养套餐-五年双保专享心悦套餐
            if ($order_source == 40) {
                // IS_WNSB ：是否双保用户(1/0)
                // REMAINQTY：双保剩余次数(如0、1、2、)
                $field = 'is_sb,re_time';
                $order_commodity = $order_commodity_model->where($map)->field($field)->find();
                $order['IS_WNSB'] = $order_commodity['is_sb'];
                $order['REMAINQTY'] = $order_commodity['re_time'];
            }


            $data['BODY'][] = $order;
        }
        $order_str = "'".implode("','", $order_code_arr)."'";
        if (!getRedisLock($lock_key, 10)) {
            return [];
        }

        $toE3sIndex->where("to_order_code in ({$order_str})")->update(['is_enable'=>2]);
        $ridd = $IRequestLogObj->insertGetId(array('module'=>'api','action'=>'order','function'=>'postE3sIndex','data'=>json_encode_cn($data)));
//        $headers = ['Content-Type: application/json'];
//        $res = http_post(config("order_to_ly.index"), $data, true,true,true,$headers);
        try {
            $res = E3spRefactor::create('e3sp_refactor')->toIndex($data);
            $msg = json_encode_cn($res);
        } catch (Exception $e) {
            $msg = $e->getMessage();
        }

        $IResponesLogObj->insertGetId(array('request_id'=>$ridd,'module'=>'api','action'=>'order','function'=>'postE3sIndex','data'=>$msg));
        if(isset($res['code']) && $res['code'] == 0){
            $toE3sIndex->where("to_order_code in ({$order_str})")->update(['is_enable'=>3,'msg'=>$msg]);
        }else{
            $toE3sIndex->where("to_order_code in ({$order_str})")->update(['is_enable'=>4,'msg'=>$msg]);
        }
        return $order_code_arr;
    }


    public function postE3sDetial($order_code_arr){
        $IRequestLogObj = new IRequestLog();
        $IResponesLogObj = new IResponesLog();
        $toE3sDetialObj = new BuToE3sDetail();
        $order_str = "'".implode("','", $order_code_arr)."'";
        $data_list = $toE3sDetialObj->where("to_order_code in ({$order_str}) and is_enable=1")->order("id asc")->select();
        if(empty($data_list)){
            echo "send data ok....";exit;
        }
        $order_model = new BuOrder();
        $order_commodity_model = new BuOrderCommodity();
        $data = [];
        foreach($data_list as $k=>$order_info){
            $datas = array(
                'ID'=>$order_info['to_id']."_".($k +1),
                'ORDER_CODE' => $order_info['to_order_code'],
                'MAINTAIN_GROUP_CODE' => $order_info['to_maintain_group_code'],
                'MAINTAIN_GROUP_AMOUNT' => empty($order_info['to_maintain_group_amount']) ? 0 : $order_info['to_maintain_group_amount'],
                'PART_NO' => $order_info['to_part_no'],
                'PART_NAME' =>$order_info['to_part_name'],
                'PART_QTY' =>$order_info['to_part_qty'],
                'PART_PRICE' =>  empty($order_info['to_part_price']) ? 0 : $order_info['to_part_price'],
                'PART_PRE_AMOUNT'=>$order_info['to_part_pre_amount'],
                'ACTION_NAME' => $order_info['to_action_name'],
                'DISCOUNT'=>$order_info['to_discount'],
                'DETAIL_TYPE' => $order_info['to_detail_type'],
                'SOURCE_TYPE' => $order_info['to_source_type'],
                'MAINTAIN_GROUP_TYPE'=>$order_info['to_maintain_group_type'],
                'VEHICLE_MILEAGE'=>empty($order_info['to_vehice_mileage']) ? 0 : $order_info['to_vehice_mileage'],
//                'ACTION_DISCOUNT'=> 1,//
                'ACTION_DISCOUNT' => $order_info['to_action_discount'], // 活动折扣
                'COMMODITY_TYPE' => $order_info['to_commodity_type'], // 商品类型
            );

            // 查询orderSource
            $map = ['order_code' => $order_info['to_order_code']];
            $order_source = $order_model->where($map)->value('order_source');
            // 保养套餐-五年双保专享心悦套餐
            if ($order_source == 40) {
                // IS_WNSB ：是否双保用户(1/0)
                // REMAINQTY：双保剩余次数(如0、1、2、)
                $field = 'is_sb,re_time';
                $order_commodity = $order_commodity_model->where($map)->field($field)->find();
                $datas['IS_WNSB'] = $order_commodity['is_sb'];
                $datas['REMAINQTY'] = $order_commodity['re_time'];
            }

            $data['BODY'][] = $datas;
        }
        $toE3sDetialObj->where("to_order_code in ({$order_str})")->update(['is_enable'=>2]);
        $ridd = $IRequestLogObj->insertGetId(array('module'=>'api','action'=>'order','function'=>'postE3sDetial','data'=>json_encode_cn($data)));
//        $headers = ['Content-Type: application/json'];
//        $res = http_post(config("order_to_ly.detail"), $data, true,true,true,$headers );
        try {
            $res = E3spRefactor::create('e3sp_refactor')->toDetail($data);
            $msg = json_encode_cn($res);
        } catch (Exception $e) {
            $msg = $e->getMessage();
        }

        $IResponesLogObj->insertGetId(array('request_id'=>$ridd,'module'=>'api','action'=>'order','function'=>'postE3sDetial','data'=>$msg));
        if(isset($res['code']) && $res['code'] == 0){
            $toE3sDetialObj->where("to_order_code in ({$order_str})")->update(['is_enable'=>3,'msg'=>$msg]);
        }else{
            $toE3sDetialObj->where("to_order_code in ({$order_str})")->update(['is_enable'=>4,'msg'=>$msg]);
        }
        // echo json_encode($data);
    }


    /**
     *选50条数据s
     */
    public function oldSelectOrder(){
        $order_code_arr =  $this->postE3sIndex();
        if(!empty($order_code_arr)){
            $this->postE3sDetial($order_code_arr);
        }
    }


    /**
     * 定时任务补漏
     */
    public function selectOrder(){
        $toE3sIndex = new BuToE3sIndex();
        $map = ['is_enable'=>1];
        $orderCodeArr = $toE3sIndex->where($map)->limit(0,10)->column('to_order_code');
        $e3sPostData = new E3sPostData();
        $e3sPostData->pushOrder($orderCodeArr);
    }


    public function t(){
        $order_code[] = $_REQUEST['order_code'];
        $this->postE3sDetial($order_code);
    }

    public function test(){

        $order_code = $_REQUEST['order_code'];
        $r =  postOrderIndexToly($order_code);
        echo "<br/><br/><br/><br/><br/><br/><br/>------------------------------------------------------------------------------<br/><br/><br/><br/><br/><br/><br/><br/>";
        $r =  postOrderDetailToly($order_code);

    }

    public function resendm(){
        $this->selectOrder();
    }

    public function resendsub($order_code){
        $this->postE3sDetial($order_code);
    }

    public function test_local(){

        $order_code = $_REQUEST['order_code'];
        $test =  isset($_REQUEST['test']) ? $_REQUEST['test'] : 0;
        $r =  $this->postOrderIndexToly($order_code,0,$test);
        echo "<br/><br/><br/><br/><br/><br/><br/>------------------------------------------------------------------------------<br/><br/><br/><br/><br/><br/><br/><br/>";

        $r =  $this->postOrderDetailToly($order_code);

    }

    /** 电商订单主表接口表
     * http://172.26.130.168:8096/postdata/DNDC_ONLINESHOP/DNDC_RECEIVE_MAIN
     */
    function postOrderIndexToly($order_code,$status = 0,$test = 0){
        if(empty($order_code)) {
            return false;
        }
        $BuOrderObj = new BuOrder();
        $toE3sIndex = new BuToE3sIndex();
        $BuOrderCommodityObj = new BuOrderCommodity();

        //传过的不传
        $is_have = $toE3sIndex->where("to_order_code =  '{$order_code}'")->find();
        if(!empty($is_have)){
            return false;
        }

//        //有轮胎不传
//        $found_info = $BuOrderCommodityObj->where("order_code = '{$order_code}' and commodity_id in (1800,1801)")->find();
//        if(!empty($found_info)){
//            return false;
//        }

        if($test == 0){
            // $where = "a.sale_source= 5 and ( a.order_status=2 or a.order_status = 12 )";
            $where = "a.sale_source= 5 and ( a.order_status=19 )";
        }else{
            $where = "1 ";
        }


        $order_info = $BuOrderObj->alias("a")
            ->join("t_bu_order_commodity b","a.order_code = b.order_code")
            ->join("t_db_commodity g","g.id=b.commodity_id")
            ->where("a.sale_source= 5 and  a.order_status=19 and a.logistics_mode=1 and a.order_code = '{$order_code}'  or (a.is_by_tc >=1 and  a.order_code = '{$order_code}' and a.order_status=19)")
            ->field("a.b_act_goods_price,a.pre_point,a.pre_use_money,a.all_act_yh,a.all_card_yh,a.order_vin,sum(b.work_time_actual_money) work_time_actual_money,a.n_dis_id,a.total_money,a.order_code,b.dd_dlr_code dlr_code,a.id,b.b_act_price,b.count,a.money,a.integral,a.mail_price,a.dlr_integral,a.card_money,a.vin")
            ->group("order_code")
            ->find();
        echo 'BuOrderObj:==>'.$BuOrderObj->getLastSql();echo "<br/>";
        if(!empty($order_info)){
//            $sql = "SELECT a.count,sc.name as scname,sc.price as price_item,sc.sku_code as sku_code_item,`e`.`sku_code`,`c`.`commodity_set_id`
//    FROM `t_bu_order_commodity` `a`
//    INNER JOIN `t_db_commodity` `g` ON `g`.`id`=`a`.`commodity_id`
//    INNER JOIN `t_bu_order` `b` ON `a`.`order_code`=`b`.`order_code`
//    INNER JOIN `t_db_commodity_set_sku` `c` ON `a`.`commodity_id`=c.commodity_id and c.id=a.sku_id
//    INNER JOIN `t_db_commodity_sku` `e` ON `e`.`id`=`c`.`commodity_sku_id`
//    INNER JOIN `t_db_commodity_sku_code` `sc` ON FIND_IN_SET(sc.sku_code,e.sku_code)
//    WHERE ( a.order_code = '{$order_code}' ) order by a.id asc";
//            $commodity_list_tmp = $BuOrderCommodityObj->query($sql);
//            echo $BuOrderCommodityObj->getLastSql();echo "<br/>";
//            $true_all_price = 0;
//            foreach($commodity_list_tmp as $item){
//                $true_all_price = $true_all_price+$item['price_item'] * $item['count'];//商品原价
//            }
//
//            $true_all_price = $true_all_price + $order_info['mail_price'];//加运费
//            $data = [];
//            $true_dis_price = 0;
//
//            $money_one = $order_info['total_money']- $order_info['money'];
//            $money_two = $order_info['integral'] / 10;
//            $true_dis_price = bcsub($true_all_price,$order_info['total_money'],2);

            $data = array(
                'to_id'=>$order_info['id'],
                'to_order_code'=> $order_info['order_code'] ,
                'to_order_name'=>'',
                'to_order_amount'=>$order_info['b_act_goods_price'],
                'to_order_fact_amount'=>$order_info['money'] + ($order_info['integral'] /10) + ($order_info['pre_point'] /10) + $order_info['pre_use_money'],
                'to_wechat_pay_amount'=>$order_info['money'] +  $order_info['pre_use_money'],
                'to_point_pay_amount'=>$order_info['integral'] + $order_info['pre_point'],
                'to_alipay_pay_amount'=>0,
                'to_action_discount_amount'=>$order_info['all_act_yh'] + $order_info['all_card_yh'],
                'to_source_type'=>0,
                'to_dlr_code'=>$order_info['dlr_code'],
                'to_vin'=>$order_info['order_vin'],
                'to_status'=>$status,
            );
            echo json_encode($data);
            //    $toE3sIndex->insertGetId($data);
            return true;
        }
        return false;
    }

    /**
     * 电商订单明细表接口表
     */
    function postOrderDetailToly($order_code){
        if(empty($order_code)) {
            print_json(-1,'订单号不能为空');
        }
        $buOrderCommodityObj = new BuOrderCommodity();
        $buOrderObj = new BuOrder();
        $dbCommoditySkuCodeObj  = new DbCommoditySkuCode();

        $order_info_index_info = $buOrderObj->where(['order_code'=>$order_code])->field("order_source,is_by_tc,distance")->find();
        $is_by_tc = $order_info_index_info['is_by_tc'];
        $order_source = $order_info_index_info['order_source'];
        $distance =  $order_info_index_info['distance'];
        $order_commodity_list = $buOrderCommodityObj->alias('a')
            // ->join("t_db_commodity_set_sku c","c.id=a.sku_id ")
            // ->join("t_db_commodity_sku e","e.id=c.commodity_sku_id ")
            ->where("a.order_code = '$order_code' and a.mo_id = 0")->field("a.dd_commodity_type,a.third_sku_code sku_code,a.*")->select();

        $arr = [];
        echo "buOrderCommodityObj:"; echo "<br/>";echo $buOrderCommodityObj->getLastSql();echo "<br/>";echo "<br/>";
        foreach($order_commodity_list as $k=>$order_commodity_item){

            $commodity_sum = 0;//商品总价
            if(!empty($order_commodity_item['sku_code'])){
                $action_name = '';
                if(in_array($order_source,[16,17,18,19,22]) ){
                    //保养套餐
                    $to_maintain_group_type = 0;
                    if($order_source == 17) $to_maintain_group_type = 1;
                    if($order_source == 18) $to_maintain_group_type = 2;
                    if($order_source == 22) $to_maintain_group_type = 3;
                    $commodity_sku_tmp = [];
                    $sku_code_arr = [];
                    $commodity_sku_tmp = [];
                    $sku_code_arr = explode(',',$order_commodity_item['sku_code']);
                    $commodity_sum_list =  $dbCommoditySkuCodeObj->whereIn("sku_code",$sku_code_arr)->field('price,sku_code,name')->order("price desc")->select();

                    if($order_commodity_item['act_type'] == 1 || $order_commodity_item['act_type'] == 3){
                        $action_name = $order_commodity_item['act_name'];
                    }

                    //处理两个一样的sku_code
                    foreach($sku_code_arr  as $kk=>$sku_code){
                        $commodity_sum += $commodity_sku_tmp[$sku_code]['price'] ?? 0;
                    }

                    $data = array(
                        'to_id'=>$order_commodity_item['id']."_".($k +1),
                        'to_order_code'=>$order_commodity_item['order_code'],
                        'to_maintain_group_code'=>$order_commodity_item['sku_code'],
                        'to_maintain_group_amount'=> $order_commodity_item['price'] ?? 0,
                        'to_part_no'=>"",
                        'to_part_name'=>"",
                        'to_part_qty'=>"",
                        'to_part_price'=>"",
//                    'to_part_pre_amount'=>round($order_commodity_item['price'] * $order_commodity_item['count'],2) ,
                        'to_part_pre_amount'=>round($order_commodity_item['price'] * $order_commodity_item['count'],2) ,
                        'to_action_name'=>$action_name,
                        'to_discount'=>round($order_commodity_item['actual_price'] / ($order_commodity_item['price'] * $order_commodity_item['count']), 4),
                        'to_detail_type'=>1,
                        'to_source_type'=>0,
                        'to_maintain_group_type'=>$to_maintain_group_type,
                        'to_vehice_mileage'=>$distance
                    );
                    $arr[] = $data;
                    if($order_commodity_item['work_time_money'] > 0 && !empty($order_commodity_item['work_time_json']) && $order_commodity_item['work_time_json'] != '""'){
                        // var_dump($order_commodity_item['work_time_json']);
                        $ret = workTime($order_commodity_item['work_time_json'],$order_commodity_item);
                        if(!empty($ret)) $arr[] = $ret;
                    }
                    break;
                }else{

                    // 备件
                    if($order_commodity_item['mo_sub_id'] ==0){ //普通商品
                        $to_part_pre_amount =  bcsub(bcmul($order_commodity_item['actual_price'], $order_commodity_item['count'], 2), $order_commodity_item['card_all_dis'], 2);

                    }else{ // >0 子商品
                        $map = ['order_code'=>$order_commodity_item['order_code'], 'mo_id'=>$order_commodity_item['mo_sub_id']];
                        $zCount = $buOrderCommodityObj->where($map)->value('count');
                        $to_part_pre_amount = bcsub(bcmul($order_commodity_item['actual_price'], $zCount, 2), $order_commodity_item['card_all_dis'], 2);
                    }

                    if ($to_part_pre_amount < 0) {
                        $to_part_pre_amount = 0;
                    }

                    // 普通商品
                    if ($order_commodity_item['mo_sub_id'] == 0) {
                        $count = $order_commodity_item['count'];
                    } else {
                        // 子商品
                        $map = ['order_code'=>$order_commodity_item['order_code'], 'mo_id'=>$order_commodity_item['mo_sub_id']];
                        $zCount = $buOrderCommodityObj->where($map)->value('count');
                        $count = bcmul($zCount, $order_commodity_item['count']); // 主商品数量*子商品数量
                    }

                    // 活动商品
                    if (in_array($order_commodity_item['act_type'], [1,3])) {
                        $action_name = $order_commodity_item['act_name'];
                        if ($order_commodity_item['card_all_dis'] > 0) {
                            $action_name = $action_name.';'.'优惠券金额:'.$order_commodity_item['card_all_dis'];
                        }
                    }

                    // 折扣
                    $discount = bcdiv($to_part_pre_amount, bcmul($order_commodity_item['price'], $count, 2), 4);
                    $to_detail_type = 0;
                    if($order_commodity_item['dd_commodity_type'] == 11){
                        $to_detail_type = 2;
                        if(!empty($order_commodity_item['third_sku_code'])){
                            $partCartobj = new PartCarSeriesModel();
                            $part_time_info = $partCartobj->alias("a")->join("t_e3s_spare_part b","a.part_no = b.part_no")->where(['a.wi_code'=>$order_commodity_item['third_sku_code']])->group("a.wi_code")->find();
                            $order_commodity_item['third_sku_name'] = $part_time_info['wi_name'];
                        }
                    }
                    $data = array(
                        'to_id'=>$order_commodity_item['id']."_".($k +1),
                        'to_order_code'=>$order_commodity_item['order_code'],
                        'to_maintain_group_code'=>"",
                        'to_maintain_group_amount'=>0,
                        'to_part_no'=>$order_commodity_item['third_sku_code'],
                        'to_part_name'=>$order_commodity_item['third_sku_name'],
                        'to_part_qty'=>$count, // 备件数量/工时数量
                        'to_part_price'=>$order_commodity_item['price'],
                        'to_part_pre_amount'=> $to_part_pre_amount,
                        'to_action_name'=>$action_name,
                        'to_discount'=> $discount,
                        'to_detail_type'=>$to_detail_type,
                        'to_source_type'=>0,
                        'to_maintain_group_type'=>0,
                        'to_vehice_mileage'=>0
                    );
                    $arr[] = $data;
                    if($order_commodity_item['work_time_money'] > 0 && !empty($order_commodity_item['work_time_json']) && $order_commodity_item['work_time_json'] != '""'){
                        $ret = workTime($order_commodity_item['work_time_json'],$order_commodity_item);
                        if(!empty($ret)) $arr[] = $ret;
                    }
                }

            }
        }
        echo json_encode($arr);
    }

    public function workTime($work_data,$order_commodity_item){
        $work_data_arr = json_decode($work_data, true);
        if(empty($work_data_arr)) return [];
        $to_action_name = '';
        $to_discount = 1;
//    if($work_data_arr['work_time_price'] > 0 && $work_data_arr['work_time_number'] >0 ){
//        $to_discount = round(1 - $order_commodity_item['work_time_dis'] / ( $work_data_arr['work_time_price'] * $work_data_arr['work_time_number'] ), 2);
//    }
//
//    if(!empty($order_commodity_item['full_id'])){
//        $dbFullDiscountObj = new DbFullDiscount();
//        $full_info =  $dbFullDiscountObj->where(array('id'=>$order_commodity_item['full_id']))->whereIn('discount_type',[2,3])->field("activity_title,preferential_money")->find();
//        if(!empty($full_info)) {
//            $to_action_name = $full_info['activity_title'] ;
//        }
//    }
//
//    if(!empty($order_commodity_item['limit_id'])){
//        $dbLimitDiscountObj = new DbLimitDiscount();
//        $limit_info =  $dbLimitDiscountObj->where(array('id'=>$order_commodity_item['limit_id']))->whereIn('discount_type',[2,3])->field("title")->find();
//        if(!empty($limit_info)){
//            $to_action_name = $limit_info['title'];
//        }
//    }

        if($order_commodity_item['mo_sub_id'] ==0){ //普通商品
            $count = bcmul($work_data_arr['work_time_number'], $order_commodity_item['count'], 1);
            $to_part_pre_amount = bcmul($order_commodity_item['work_time_actual_money'], $order_commodity_item['count'], 2);
        }else{ // >0 子商品
            $map = ['order_code'=>$order_commodity_item['order_code'], 'mo_id'=>$order_commodity_item['mo_sub_id']];
            $zCount = BuOrderCommodity::where($map)->value('count');
            $count = bcmul($zCount, bcmul($work_data_arr['work_time_number'], $order_commodity_item['count'], 1),1);
            $to_part_pre_amount = $order_commodity_item['work_time_actual_money'];
        }

        if ($to_part_pre_amount < 0) {
            $to_part_pre_amount = 0;
        }

        $to_action_name = "";
        if(in_array($order_commodity_item['act_type'], [2,3])){
            $to_action_name = $order_commodity_item['act_name'];
        }


        $discount = bcdiv($to_part_pre_amount, bcmul($work_data_arr['work_time_price'], $count, 2), 4);
        return array(
            'to_id'=>$order_commodity_item['id']."_work_time",
            'to_order_code'=>$order_commodity_item['order_code'],
            'to_maintain_group_code'=>"",
            'to_maintain_group_amount'=>0,
            'to_part_no'=>$work_data_arr['work_time_code'],
            'to_part_name'=>$work_data_arr['work_time_name'],
//        'to_part_qty'=>$work_data_arr['work_time_number'] * $order_commodity_item['count'],
            'to_part_qty'=>$count,
            'to_part_price'=>$work_data_arr['work_time_price'],
            'to_part_pre_amount'=>$to_part_pre_amount,
            // 'to_action_name'=>$to_action_name,
            'to_action_name'=>'商品数量'.$order_commodity_item['count'].'/'.$order_commodity_item['third_sku_code'].';'.$to_action_name,
            'to_discount'=>$discount,
            'to_detail_type'=>2,
            'to_source_type'=>0,
            'to_maintain_group_type'=>0,
            'to_vehice_mileage'=>0
        );
    }

    public function discount($is_last_one,$all_price,$money_type,$commodity_sku_price,$sum_product_price,$dis_money){
        if($is_last_one == 1){
            $discount_price =  $all_price -  $dis_money[$money_type];
        }else{
            //商品占比
            $sku_discount = round($commodity_sku_price  / $sum_product_price ,2);
            //真正优惠
            $discount_price = $all_price * $sku_discount;
        }
        return ['discount_price'=>$discount_price];
    }

    public function activeList1($order_info){
        $action_name = '';
        $sum_discount_price = 0;
        if(!empty($order_info['full_id'])){ //满减
            $dbFullDiscountObj = new DbFullDiscount();
            $full_info =  $dbFullDiscountObj->where(array('id'=>$order_info['full_id']))->whereIn('discount_type',[1,3])->field("activity_title,preferential_money,discount_type")->find();
            if(!empty($full_info)){
             //   $ret = $this->discount($is_last_one,$order_info['full_dis_money'],'full_money',$commodity_sku_price,$sum_product_price,$dis_money);
              //  $dis_money['full_money'] = $ret['discount_price'];
                $action_name .= $full_info['activity_title']." ";
              //  $sum_discount_price += $ret['discount_price'];

            }
        }

        if(!empty($order_info['limit_id'])){ //限时折扣表
            $dbLimitDiscountObj = new DbLimitDiscount();
            $limit_info =  $dbLimitDiscountObj->where(array('id'=>$order_info['limit_id']))->whereIn('discount_type',[1,3])->field("title,discount_type")->find();
            if(!empty($limit_info)){
              //  $ret = $this->discount($is_last_one, $order_info['limit_dis_money'], 'limit_money', $commodity_sku_price, $sum_product_price, $dis_money);
              //  $dis_money['limit_money'] = $ret['discount_price'];
                $action_name .= $limit_info['title'] . " ";
              //  $sum_discount_price += $ret['discount_price'];
            }
        }

        if(!empty($order_info['n_dis_id'])){ //N件N折
            $dbNDiscountObj = new DbNDiscount();
            $cheap_info  = $dbNDiscountObj->alias("a")->where("a.id = '{$order_info['n_dis_id']}'")->field('a.title')->find();
           // $ret = $this->discount($is_last_one,$order_info['n_dis_money'],'n_money',$commodity_sku_price,$sum_product_price,$dis_money);
          //  $dis_money['n_money'] = $ret['discount_price'];
            $action_name .= $cheap_info['title']. " ";
          //  $sum_discount_price += $ret['discount_price'];
        }

        if(!empty($order_info['suit_id'])){ //套装
            $buSuitObj = new BuCheapSuitIndex();
            $suit_info  = $buSuitObj->alias("a")->where("a.id = '{$order_info['suit_id']}'")->field('a.name')->find();
          //  $ret = $this->discount($is_last_one,$order_info['suit_dis_money'],'suit_money',$commodity_sku_price,$sum_product_price,$dis_money);
           // $dis_money['suit_money'] = $ret['discount_price'];
            $action_name .= $suit_info['name']. " ";
           // $sum_discount_price += $ret['discount_price'];
        }

        if(!empty($order_info['group_id'])){ //团购
            $dbFightGroupObj = new DbFightGroup();
            $acgroup_info = $dbFightGroupObj->alias("a")->where("a.id = '{$order_info['group_id']}'")->field('a.title')->find();
           // $ret = $this->discount($is_last_one,$order_info['group_dis_money'],'group_money',$commodity_sku_price,$sum_product_price,$dis_money);
           // $dis_money['group_money'] = $ret['discount_price'];
            $action_name .= $acgroup_info['title']. " ";
           // $sum_discount_price += $ret['discount_price'];
        }

        if(!empty($order_info['pre_sale_id'])){ //预售
            $dbPreSaleObj = new DbPreSale();
            $pre_info = $dbPreSaleObj->where(['id'=>$order_info['pre_sale_id']])->find();
           // $ret = $this->discount($is_last_one,$order_info['pre_sale_dis_money'],'sale_money',$commodity_sku_price,$sum_product_price,$dis_money);
          //  $dis_money['sale_money'] = $ret['discount_price'];
            $action_name .= $pre_info['title']. " ";
          //  $sum_discount_price += $ret['discount_price'];
        }

        if(!empty($order_info['card_ids'])){ //卡券
            $dbCardObj = new DbCard();
            $card_tmp = [];
            $card_list = $dbCardObj->where("id in ({$order_info['card_ids']}) and card_type < 6 ")->select();
            foreach($card_list as $card_item){
                $card_tmp[$card_item['id']] = $card_item['card_name'];
            }
            $card_arr =  explode(',',$order_info['card_ids']);
           // $card_yh_arr =  explode(',',$order_info['card_yh']);
          //  $card_discount_price = 0;
            foreach($card_arr as $card_k=>$card_item_id){
//                $all_price =  $card_yh_arr[$card_k];
//                if($is_last_one == 1){
//                    $discount_price =  $all_price -  $dis_money[$card_k]['card_money'];
//                }else{
//                    //商品占比
//                    $sku_discount = round($commodity_sku_price  / $sum_product_price ,2);
//                    //真正优惠
//                    $discount_price = $all_price * $sku_discount;
//                    $dis_money[$card_k]['card_money'] = $discount_price;
//                }
              //  $card_discount_price +=$discount_price;
                $action_name .= $card_tmp[$card_item_id]." ";
              //  $sum_discount_price += $discount_price;
            }
        }

//        //真正折扣
//        $discount =  round((($commodity_sku_price - $sum_discount_price) / $commodity_sku_price) ,2) ;
//        if($discount != 1){
//            $discount =  $discount * 100 % 100 / 100;
//        }
        return ['action_name'=>$action_name];
    }

    /**
     * 电商订单状态接口表
     * 联友状态：0已付款，1已使用，2已结算，3已作废，4.已销退
     *  商城状态：   '1'  => '已下单', '2' => '已支付', '3' => '已取消', '4' => '已发货', '5' => '已退款',
    '6'  => '已过期', '7' => '已完成', '8' => '未支付', '9' => '已收货', '10' => '退款中',
    '11' => '发货中', '12' => '待发货', '13' => '安装中', '14' => '充值成功', '15' => "已付定金",
    '16'   => "审核通过", '17' => "审核不通过", '18' => "交易关闭", '19' => '已核销'
     *
     */
    public function oldUpdateOrderFromly(){
        $data_json = request()->getContent();
        //$data_json = trim(file_get_contents('php://input'));
       // Logger::error('ly-order-updateorder',['js'=>file_get_contents('php://input'),'d-j'=>request()->getContent()]);
        $IRequestLogObj = new IRequestLog();
        $IResponesLogObj = new IResponesLog();
        $buOrderObj = new BuOrder();
        $rid = $IRequestLogObj->insertGetId(array('module'=>'api','action'=>'order','function'=>'updateOrderFromly','data'=>$data_json));
        if(empty($data_json)) {
            $IResponesLogObj->insertGetId(array('request_id'=>$rid,'module'=>'api','action'=>'order','function'=>'updateOrderFromly','data'=>'参数错误'));
            print_json(1,'参数错误');
        }
        $status_arr = array(1=>13, 2=>7);
        $BuOrderObj = new BuOrder();
        $data_arr = json_decode($data_json,true);
        $sqlwhere = '';
        $inwhere = [];
        $update_inwhere = [];
        if(!empty($data_arr)){
            foreach($data_arr as $order_item){
                $order_status_7_info = $buOrderObj->where(['order_code'=>$order_item['ORDER_CODE'],'order_status'=>7])->find();
                if(!empty($order_status_7_info)) continue;
                if($order_item['STATUS'] == 1 || $order_item['STATUS'] == 2){
                    $sqlwhere .= " WHEN '".$order_item['ORDER_CODE']."' THEN  ".$status_arr[$order_item['STATUS']]."   ";
                    $inwhere[] = $order_item['ORDER_CODE'];
                }
                if($order_item['STATUS'] == 2){
                    $update_inwhere[] = $order_item['ORDER_CODE'];
                }
            }
            $order_code = '';
            if(!empty($inwhere)){
                $order_code = "'".implode("','", $inwhere)."'";
            }
            if(!empty($order_code)){
                $sql = "UPDATE t_bu_order SET order_status = CASE order_code ".$sqlwhere."  end where order_code in({$order_code})";
                $BuOrderObj->query($sql);
                if(!empty($update_inwhere)){
                    $BuOrderObj->whereIn('order_code',$update_inwhere)->update(['last_updated_date'=>date("Y-m-d H:i:s",time())]);
                }
                foreach($data_arr as $item) {
                    $data = ['order_code'=>$item['ORDER_CODE'], 'order_status'=>$item['STATUS']];
                    Queue::push('app\common\queue\OrderChangeQueue', json_encode($data), config('queue_type.order_change'));
                }
                $IResponesLogObj->insertGetId(array('request_id'=>$rid,'module'=>'api','action'=>'order','function'=>'updateOrderFromly','data'=>"update success"));
                 print_json(0,'修改成功');
            }else{
                print_json(1,'修改失败');
            }


        }
    }


    /**
     * 电商订单状态
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function updateOrderFromly()
    {
        $data_json = request()->getContent();
        $i_request_log_model = new IRequestLog();
        $add = ['module' => 'api', 'action' => 'order', 'function' => 'updateOrderFromly', 'data' => $data_json];
        $rid = $i_request_log_model->insertGetId($add);
        $i_respones_log_model = new IResponesLog();
        if (empty($data_json)) {
            $add = ['request_id' => $rid, 'module' => 'api', 'action' => 'order', 'function' => 'updateOrderFromly', 'data' => '参数错误'];
            $i_respones_log_model->insertGetId($add);
            print_json(1, '参数错误');
        }
        // 联友状态：0已付款，1已使用，2已结算，3已作废，4.已销退 5受理中
        // 商城状态：7已完成, 13安装中
        $status_arr = [1 => 13, 2 => 7, 5 => 24];
        $bu_order_model = new BuOrder();
        $order_commodity_model = new BuOrderCommodity();

        $data_arr = json_decode($data_json, true);
        foreach ($data_arr as $key => $item) {

            // 新媒体订单
            if (!containsSpecificPattern($item['ORDER_CODE'])) {
                JavaNewMedia::create('java_new_media')->cardConsumeStatus($item);
            } else {
                // NI+商城订单
                $map = ['order_code' => $item['ORDER_CODE']];
                $order_info = $bu_order_model->where($map)->find();
                // 订单状态为已完成
                if ($order_info['order_status'] == 7) {
                    continue;
                }
                $status = $status_arr[$item['STATUS']] ?? 0;

                // 到店代金券
                if ($order_info['order_source'] == 20) {
                    // 已作废
                    if ($item['STATUS'] == 3) {
                        // 卡券反核销
                        $field = 'goods_card_ids';
                        $order_commodity = $order_commodity_model->where($map)->field($field)->find();
                        $status = 2;
                        $this->cardCouponReviveV3($order_info, $order_commodity);
                    }
                }
                // 43 延保服务包商品
                if ($order_info['order_source'] == 43) {
                    // 已作废
                    if ($item['STATUS'] == 3) {
                        $status = 2; // 进度状态是待受理
                    }
                }
//                // 状态不存在
//                if ($status == 0) {
//                    continue ;
//                }
                $upd = [];
                if ($status != 0) {
                    $upd['order_status'] = $status;
                }
                if (isset($item['E3S_VIN']) && !empty($order_info['E3S_VIN']) && !empty($order_info['order_vin'])) {
                    $upd['e3s_vin'] = $item['E3S_VIN'];
                    $upd['order_vin'] = $item['E3S_VIN'];
                }
                if (!empty($upd)) {
                    $upd['last_updated_date'] = date('Y-m-d H:i:s');
                    $upd['modifier'] = 'updateOrderFromly';
                    $bu_order_model->where('order_code', $item['ORDER_CODE'])->update($upd);
                }

                if ($status != 0) {
                    $data = ['order_code' => $item['ORDER_CODE'], 'order_status' => $item['STATUS']];
                    Queue::push('app\common\queue\OrderChangeQueue', json_encode($data), config('queue_type.order_change'));
                }
                $add = ['request_id' => $rid, 'module' => 'api', 'action' => $item['ORDER_CODE'], 'function' => 'updateOrderFromly', 'data' => "update success"];
                $i_respones_log_model->insertGetId($add);
            }
        }
        print_json(0, '修改成功');
    }


    // 卡券反核销
    private function cardCouponReviveV3($orderInfo, $orderCommodity)
    {

        $card_r_model = new BuCardReceiveRecord();
        $map = [
            'card_id' => $orderCommodity['goods_card_ids'],
            'user_id' => $orderInfo['user_id'],
            'act_id' =>  $orderInfo['id'],
        ];
        $card_r_list = $card_r_model->where($map)->select();
        $net_user = new NetUser();
        foreach ($card_r_list as $item) {
            $user = ['id' => $orderInfo['user_id']];
            $re = $net_user->postCouponReviveV3($user, ['coupon_code'=>$item['coupon_code']]);
            if ($re['code'] <> 200) {
                return $re;
            }
        }
        return ['msg' => '修改成功', 'code' => 200];

    }

}
