
{$jssdk}

<script type="text/javascript">
    wx.ready(function(){
        wx.scanQRCode({
            desc: 'scanQRCode desc',
            needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
            scanType: ["qrCode"], // 可以指定扫二维码还是一维码，默认二者都有
            success: function (res) {
                // alert(JSON.stringify(res));
                var result = res.resultStr; // 当needResult 为 1 时，扫码返回的结果
                var url = "{$url}?from={$from}&dy_code="+result+"&user_id={$user_id}";
                // alert(url);
                window.location.href=url;
                // // if(result.indexOf('{$url}')>=0){
                // //     window.location.href="{$url}?code="+result;
                // // }else{
                // //     alert('请扫描正确的二维码');
                // //     window.location.reload();
                // // }

            }
        });
    });
</script>


{include file="active@common:footer" /}