<!-------------商城卡券step1-------------------->

<fieldset>
    <div class="row col-md-12">

        <!-- begin 基础信息 -->
        <div class="table-content ">
            <legend class="pull-left width-full">基本信息</legend>
            <!--							<h5> 基本信息 </h5> <hr>-->
            <div class="form-group">
                <label class="col-md-2 control-label">优惠券类型<i class="text-danger">*</i>:</label>
                <div class="col-md-6">

                    <?php foreach($card_type_arr as $key => $val){?>
                        <label class="radio-inline">
                            {if condition="$key==1"}
                            <input type="radio" class="card_type" name="card_type" value="{$key}" data-parsley-group="wizard-step-1" required  checked/>{$val}
                            {else/}
                            <input type="radio" class="card_type" name="card_type" value="{$key}" data-parsley-group="wizard-step-1" required />{$val}
                            {/if}
                        </label>
                    <?php }?>
                    <span id="what-text">
                        <img style="margin-left:10px;width: 18px;" src="__STATIC__admin_v2/img/what.jpg">
                        <div>
                            <dd>立减券：可为用户提供抵扣现金服务，可设置成为“满*元，减*元”。</dd>
                            <dd>折扣券：可为用户提供消费折扣。</dd>
                            <dd>代金券：可为用户提供代金服务。</dd>
                        </div>
                    </span>
                </div>
            </div>
            <div class="form-group ">
                <label class="col-md-2 control-label" id="t_title">立减券标题<i class="text-danger">*</i>:</label>
                <div class="col-md-6">
                    <input type="text" class="form-control" name="card_name" placeholder="请输入立减券标题" data-parsley-group="wizard-step-1" data-parsley-maxlengthstr="9" required data-parsley-maxlengthstr-message="最多9个汉字或18个字母">
                    <dd>不能为空，最多9个汉字或18个字母。建议涵盖卡券服务及金额，如：100元立减券</dd>
                </div>
            </div>
            <div class="form-group" id="card-quota-group">
                <label class="col-md-2 control-label" id="t_card_quota">减免金额<i class="text-danger">*</i>:</label>
                <div class="col-md-6" >
                    <div id="mi_price">
                        <div class="input-group" id="div-c-daterange">
                            <input type="text" class="form-control" id="card_quota" data-parsley-errors-container="#mi_price" name="card_quota" placeholder="请输入优惠券减免金额" data-parsley-group="wizard-step-1" data-parsley-type="number" data-parsley-min="0.1" data-parsley-max="100000" required >
                            <span class="input-group-addon">元</span>

                        </div>
                        <dd>不能为空，减免金额数大于0.1 元且小于100000元，精确到角</dd>

                    </div>

                </div>

            </div>
            <div class="form-group ">
                <label class="col-md-2 control-label">库存<i class="text-danger">*</i>:</label>
                <div class="col-md-8">
                    <div id="p-count">
                        <div class="input-group" >
                            <input type="text" name="available_count" data-parsley-errors-container="#p-count" class="form-control" placeholder="请输入库存的数量" data-parsley-group="wizard-step-1" data-parsley-type="number" data-parsley-min="1" data-parsley-max="100000000" required >
                            <span class="input-group-addon">张</span>
                        </div>
                        <dd>用于设置优惠券生成的库存数量。不能为空，大于0且上限为100000000张</dd>
                    </div>
                </div>
            </div>
            <div class="form-group ">
                <label class="col-md-2 control-label">有效期<i class="text-danger">*</i>:</label>
                <div class="col-md-6" >
                    <div id="range-date">
                        <div class="input-group "  id="default-daterange">
                            <input type="text" name="validity_date"  class="form-control" value="" data-parsley-errors-container="#range-date" data-parsley-group="wizard-step-1" required placeholder="请选择时间" />
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                            </span>
                        </div>
                    </div>
                    <dd>有效期必选。用于设置优惠券可有效使用的时间范围</dd>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">业务归属:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <select name="gather_id" class="form-control input-sm ">
                            <option value=""> 全部 </option>
                            {volist name="$gather_list" id="val" key="key"}
                            <option value="{$val['id']}" {if($Think.get.gather_id == $val['id'])}selected{/if} > {$val['name']} </option>
                            {/volist}
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">主题活动:</label>
                <div  class="col-md-5" id="is_enable">
                    <input type="text" name="theme_name" id="theme_name" placeholder="请输入主题活动" value="" class="form-control" data-parsley-length="[0, 100]">
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">是否PV补贴:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_pv_subsidy" type="checkbox" data-input-name="is_pv_subsidy" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭" />
                        </label>
                    </div>
                </div>
            </div>


            <div class="form-group"  >
                <label class="control-label col-md-2">卡券描述:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <textarea type="text" id="" name="card_desc" class="form-control" placeholder="请输入卡券描述" data-parsley-group="wizard-step-1" data-parsley-maxlengthstr="50"  style="height: 100px;"></textarea>
                        <dd>显示在商城领券列表和订单券列表的提示文案，最多50个字</dd>
                    </div>
                </div>
            </div>

            {if condition="$Think.get.live_type!=1"}
<!--            <div class="form-group ">-->
<!--                <label class="col-md-2 control-label">每日限量:</label>-->
<!--                <div class="col-md-8">-->
<!--                    <div id="p-count">-->
<!--                        <div class="input-group" >-->
<!--                            <input type="text" name="limit_count" data-parsley-errors-container="#p-count" class="form-control" placeholder="请输入每日限量的数量" data-parsley-group="wizard-step-1" data-parsley-type="number" data-parsley-min="0" data-parsley-max="100000000" >-->
<!--                            <span class="input-group-addon">张</span>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!---->
<!--            <div class="form-group ">-->
<!--                <label class="col-md-2 control-label">每日开始领取时间段:</label>-->
<!--                <div class="col-md-6" >-->
<!--                    <div id="range-date">-->
<!--                        <div class="col-md-3 p-l-0" style="">-->
<!--                            <input type="text" name="start_time" id="start_time"  placeholder="请选择开始领取时间点" class="form-control timepicker3">-->
<!--                        </div>-->
<!--                        <div class="col-md-3">-->
<!--                            <input type="text" name="end_time" id="end_time"  placeholder="请选择结束领取时间点" class="form-control timepicker3" >-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
            {/if}


        </div>
        <!-- end 基础信息 -->

        <!-- begin 优惠详情 -->
        <div class="table-content table-content-6">
            <legend class="pull-left width-full">优惠详情</legend>
            <div class="form-group ">
                <label class="col-md-2 control-label">领券限制:</label>
                <div class="col-md-6">
                    <div id="mi_get_limit">
                        <div class="input-group" id="div-c-daterange">
                            <input type="text" class="form-control" id="get_limit" name="get_limit"
                                   placeholder="请输入领取数量上限"
                                   value="1"
                                   data-parsley-errors-container="#mi_get_limit" data-parsley-group="wizard-step-1" data-parsley-type="integer" data-parsley-min="1" data-parsley-max="50">
                            <span class="input-group-addon">张</span>
                        </div>
                    </div>
                    <dd>用于设置每个用户的领券上限，最高50。如不填，则默认为1</dd>
                </div>
            </div>
            <div class="form-group ">
                <label class="col-md-2 control-label">使用条件:</label>
                <div class="col-md-8">

                    <div class="checkbox zdxf">
                        <label> <input type="checkbox" id="text-checkbox-zdxf" value=""> 最低消费 </label>
                        <span id="text-checkbox-sp">
                            满 <input type="text" id="text-checkbox-zdxf-text" class="form-control" name="least_cost" readonly="readonly" data-parsley-group="wizard-step-1" data-parsley-errors-container="#valid-zdxf"> 元可用
                        </span>
                        <dd id="valid-zdxf">用于设置满减的消费限额。最低消费金额必须大于减免金额。大于0且只能到百分位</dd>
                    </div>

                    <div class="checkbox apply_use">
                        <label> <input type="checkbox" id="text-checkbox-syfw" value=""> 适用范围 </label>
                        <span>（至少填写一项） </span>

                        <div class="form-group col-md-12" style="margin-top: 7px;">
                            <label class="col-md-3 control-label" style="padding-left: 0px;padding-right: 5px; width:15%;">适用商品</label>
                            <div class="">
                                <input type="text" name="apply_des" class="form-control text-checkbox-syfw-text" placeholder="请输入本券适用的商品、类目或服务" data-parsley-group="wizard-step-1" data-parsley-maxlengthstr="15" readonly="readonly" style="width: 350px;"  data-parsley-maxlengthstr-message="最多15个汉字或30个字母">
                                <dd>展示于优惠券使用说明。不能为空，长度在15个汉字或30个英文字母内</dd>
                            </div>
                        </div>
                        <div class="form-group col-md-12" style="margin-top: 7px;">
                            <label class="col-md-3 control-label" style="padding-left: 0px;padding-right: 5px; width:15%;">不适用商品</label>
                            <div class="">
                                <input type="text" name="not_apply_des" class="form-control text-checkbox-syfw-text" placeholder="请输入本券适用的商品、类目或服务" data-parsley-group="wizard-step-1" data-parsley-maxlengthstr="15" readonly="readonly" style="width: 350px;" data-parsley-maxlengthstr-message="最多15个汉字或30个字母">
                                <dd>展示于优惠券使用说明。不能为空，长度在15个汉字或30个英文字母内</dd>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        {if $live_type != 1}
        <div class="form-group">
            <label class="control-label col-md-2">适用用户群体:</label>
            <div class="col-md-4" id="valid-can-user">
                {if $live_type != 2}
                <label> <input type="radio" class="can_user" name="can_user_type" value="1" data-parsley-group="wizard-step-1"/>用户群体 </label>
                <select class="multiple-select2 form-control can_user_group" multiple="multiple" name="can_user_group[]"  data-parsley-errors-container="#valid-can-user-group" disabled>
                    <?php foreach ($crowds_list as $key => $val):?>
                        <option value="{$val['id']}">{$val['crowd_name']}</option>
                    <?php endforeach;?>
                </select>
                {/if}


                <?php if (!empty($level_list)){ ?>
                    <label> <input type="radio" class="can_user" name="can_user_type" value="2" data-parsley-group="wizard-step-1" />用户等级 </label>
                    <select class="multiple-select2 form-control can_user_level" multiple="multiple" name="can_user_level[]"  data-parsley-errors-container="#valid-can-user-level" disabled>
                        <?php foreach ($level_list as $key => $val):?>
                            <option value="{$val['value_code']}">{$val['county_name']}</option>
                        <?php endforeach;?>
                    </select>
                <?php } ?>
            </div>
        </div>
        {/if}


        <div class="form-group ">
            <label class="col-md-2 control-label">使用须知<i class="text-danger">*</i>:</label>
            <div class="col-md-8">
                <textarea type="text" id="" name="use_des" class="form-control" placeholder="请输入优惠券的使用须知" data-parsley-group="wizard-step-1" data-parsley-maxlengthstr="300" required style="height: 100px;"></textarea>
                <dd>展示与优惠券详情中的使用须知。不能为空且长度不超过300个字</dd>
            </div>
        </div>
        <div class="form-group ">
            <label class="col-md-2 control-label" id="e3s_activity">关联E3S活动:</label>
            <div class="col-md-6">
                <input type="text" class="form-control e3s_activity" name="e3s_activity" placeholder="请选择活动" data-parsley-group="wizard-step-1" id="text-a" data-id="" autocomplete="off">
                <input type="hidden" id="e3s_activity_id" name="e3s_activity_id">
            </div>
        </div>

        <div class="form-group ">
            <label class="col-md-2 control-label" id="activity_type">活动设置类型:</label>
            <div class="col-md-6">
                <select class="form-control width-300" name="activity_type" >
                    <option value="0">请选择</option>
                    <option value="1">备件</option>
                    <option value="2">工时</option>
                    <option value="3">赠品</option>
                </select>
            </div>
        </div>

        <div class="form-group ">
            <label class="col-md-2 control-label" id="settlement_rule">关联结算规则:</label>
            <div class="col-md-6">
                <input type="text" class="form-control settlement_rule" name="settlement_rule" placeholder="请选择选择结算规则" data-parsley-group="wizard-step-1" autocomplete="off">
                <input type="hidden" id="settlement_rule_id" name="settlement_rule_id">
                <input type="hidden" id="settlement_rule_value" name="settlement_rule_value">
                <input type="hidden" id="settlement_rule_type" name="settlement_rule_type">
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-2 control-label">领券场景:</label>
            <div class="col-md-6">
                <input type="text" class="form-control" id="get_card_scene" value="会员商城" disabled>
            </div>
        </div>
        <!-- end 优惠详情 -->

    </div>

</fieldset>