{extend name="public:base_layout" /}

{block name="content"/}

<div class="panel-body">


    <div class="alert alert-info fade in m-b-15">
        <h4>操作提示</h4>
        <p>设置商城使用的优惠券,<span style="color: red;">操作过程请走完4步流程</span>。</p>
    </div>
    <ul class="nav nav-tabs dbyy">
        <li <?php if($live_type==0):?>class="active" <?php endif;?>><a href="{:url('live',['live_type'=>0])}" >NI+商城</a></li>
        <li <?php if($live_type==1):?>class="active" <?php endif;?>><a href="{:url('live',['live_type'=>1])}" >PZ1A商城</a></li>
        <li <?php if($live_type==2):?>class="active" <?php endif;?>><a href="{:url('live',['live_type'=>2])}" >启辰商城</a></li>
    </ul>
    <div class="margin-bottom-10 p-r-0">

        <form class="form search-form" action="" method="get">
            <input type="hidden" value="{$live_type}" name="live_type">
            <div class="dndc-form-mod1" id="dndc-form-mod1">
                <div class="form-main">
                    <div class="form-item">
                        <em>优惠券名称：</em>
                        <div class="form-wp">
                            <input type="text" name="card_name" value="{$card_name}" class="form-control input-sm element" placeholder="请输入优惠劵名称">
                        </div>
                    </div>
                    <div class="form-item" style="">
                        <em>活动状态：</em>
                        <div class="form-wp" id="act_status_select">
                        </div>
                    </div>
                    <div class="btn-more" id="dndc-btn-more1">
                        更多<i class="fa fa-plus-circle"></i><span class="arrow-spread"></span>
                    </div>
                    <div class="btn-wp">
                        <button type="submit" class=" btn btn-sm btn-success"><i class="fa fa-search"></i> 搜索</button>
                    </div>
                    <div class="btn-wp">
                        <button type="button" data-type="more-card"  class="btn btn-sm btn-primary m-r-3 export-card-commodity"><i class="fa fa-1x fa-download"></i>导出关联商品</button>
                    </div>
                    <div class="btn-left" style="position:absolute;left:0;top:0;">
                        <a href="#addModal" id="a-add-modal" class="btn btn-success btn-sm pull-left m-r-2" data-toggle="modal"> <i class="fa fa-lg fa-plus"></i> 新增优惠券 </a>
                    </div>
                </div>

                <div class="form-spread">
                    <ul>
                        <li>
                            <div class="form-item">
                                <em>业务归属：</em>
                                <div class="form-wp">
                                    <select name="gather_id" class="form-control input-sm ">
                                        <option value=""> 全部 </option>
                                        {volist name="$gather_list" id="val" key="key"}
                                        <option value="{$val['id']}" {if($Think.get.gather_id == $val['id'])}selected{/if} > {$val['name']} </option>
                                        {/volist}
                                    </select>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>是否PV补贴：</em>
                                <div class="form-wp">
                                    <select name="is_pv_subsidy" class="form-control input-sm ">
                                        <option value=""> 全部 </option>
                                        <option value="1" {if($Think.get.is_pv_subsidy == 1)}selected{/if} > 是 </option>
                                        <option value="2" {if($Think.get.is_pv_subsidy == 2)}selected{/if} > 否 </option>
                                    </select>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>主题活动：</em>
                                <div class="form-wp">
                                    <input type="text" name="theme_name" value="{$Think.get.theme_name}" class="form-control input-sm element" placeholder="请输入主题活动">
                                </div>
                            </div>
                        </li>
                        <li></li>
                    </ul>
                </div>
            </div>
        </form>

    </div>

    <div class="table-scrollable">
    <table class="table table-hover table-bordered">
        <thead>
        <tr class="active">
            <th><input class="allcheck-order"  type="checkbox"/></th>
<!--            <th>序号</th>-->
            <th>优惠券ID</th>
            <th>优惠券名称</th>
            <th>业务归属</th>
            <th>主题活动</th>
            <th>是否PV补贴</th>
            <th>优惠劵类型</th>
            <th>库存</th>
            <th>有效期</th>
            <th>发放渠道</th>
            <th>状态</th>
            <th class="text-center">操作</th>
        </tr>
        </thead>
        <tbody>
        {volist name="$list" id="val" key="key"}
            <tr>
                <td><input class="check-order"  data-card-id="{$val.id}" type="checkbox"/></td>
                <td>{$val.id}</td>
                <td>{$val.card_name}</td>
<!--                <td>{$val.card_id}</td>-->
                <td>{$val['gather_name']}</td>
                <td>{$val['theme_name']}</td>
                <td>{$val['is_pv_subsidy_status']}</td>
                <td>{$val.card_type_name}</td>
                <td>{$val.available_count}</td>
                <td>{$val.validity_date}</td>
                <td>{$val.up_down_channel_name}</td>
                <td>{$val.status_name}</td>
                <td class="text-left">
                    <a href="{:url('createStep')}?id={$val['id']}&live_type={$live_type}" data-id="{$val['card_id']}" id="updateModal" class="btn btn-primary btn-sm m-r-5 m-t-0"><i class="fa fa-edit"></i>编辑</a>
                    {if(!in_array($val.card_type, [6,7]))}
                    <a href="{:url('commodityCardLive',['card_id'=>$val['id']])}&live_type={$live_type}" data-id="{$val['card_id']}"
                       class="btn btn-primary btn-sm m-r-5 m-t-0"><i class="fa fa-cube"></i>关联商品</a>
                    <a href="javascript:" data-type="one-card" data-card-id="{$val['id']}"
                       class="btn btn-primary btn-sm m-r-5 m-t-0 export-card-commodity"><i class="fa fa-1x fa-download"></i>导出关联商品</a>
                    {/if}
                </td>
            </tr>
        {/volist}
        </tbody>
    </table>
    </div>

    <div class="row">
        <div class="col-sm-5">
            <div class="dataTables_info" id="data-table_info" role="status" aria-live="polite">
                共查询到 {$list->total()} 条数据
            </div>
        </div>
        <div class="col-sm-7">
            <div class="dataTables_paginate paging_simple_numbers" id="data-table_paginate">
                {$page}
            </div>
        </div>
    </div>

</div>

{/block}

{block name="css"}
<style type="text/css">
    #add-card-radio dd{
        color: #7b7f8f;
        margin-left: 15px;
    }
    /*table平铺*/
    td,th{ white-space: nowrap; }
</style>
{/block}

{block name="script"/}
<script src="__STATIC__admin_v2/js/xm-select.js"></script>
<script>

    // 清除所有localStorage的项
    localStorage.clear();

    $(function(){
        $(".default-select2").select2();
        $(".modal-select2").select2({'width':'100%'});
        //新增优惠券
        $("#add-confirm").on('click',function(e){
            var $form=$(this).parent().parents('form');
            var validate=$form.psly().validate();  //表单验证
            if(!validate) return false;
            var data =$form.serialize();
            window.location.href="{:url('createStep')}?"+data;
        });

        $("#a-add-modal").on('click',function(e){
            window.location.href="{:url('createStep')}?live_type={$live_type}";
        });

        //新增前隐藏 提示框
        $("#a-add-modal").on('click',function(){
            //表单初始化
            $("#add-form")[0].reset();
            $("#add-form").psly().reset();
            $("#addModal").find(".alert-danger").hide();
            ue.ready(function() {
                ue.setContent('');
            });
        });
        //同步 搜索
        $("#synchronous-btn-search").on('click',function(){
            var modal_obj = $(this).parent();
            var card_id = modal_obj.find("#card_id").val();
            $.get("{:url('getOne')}",{card_id:card_id},function(resData){
                if(resData.error==0){
                    var data=resData.data;
//                    modal_obj.find("input[name='card_id']").val(data.id);
                    modal_obj.find("#table-tr-1").text(data.id);
                    modal_obj.find("#table-tr-2").text(data.title);
                    modal_obj.find("#table-tr-3").html("<lable><input type='checkbox' data-id='"+data.id+"' id='tong-bu' name='tong-bu'>同步</lable>");
                    $('#synchronous-confirm').parents(".modal-content").find(".alert-danger").hide();
                }else{
                    modal_obj.find("input[name='card_id']").val('');
                    modal_obj.find("#table-tr-1").text('');
                    modal_obj.find("#table-tr-2").text('');
                    modal_obj.find("#table-tr-3").html('');
                    $('#synchronous-confirm').parents(".modal-content").find(".alert-danger").hide();
                    layer.msg(resData.msg)
                }
            },'json');
        });
        //取消
        $("#synchronous-qx").on('click',function(){
            $('#card_id').val('');
            var modal_obj = $('#synchronous-btn-search').parent();
            modal_obj.find("input[name='card_id']").val('');
            modal_obj.find("#table-tr-1").text('');
            modal_obj.find("#table-tr-2").text('');
            modal_obj.find("#table-tr-3").html('');
            $('#synchronous-confirm').parents(".modal-content").find(".alert-danger").hide();
            $("#synchronousModal").click();
        });
        //同步复选框
        $("body").on('click','#tong-bu',function(){
            var modal_obj = $('#synchronous-btn-search').parent();
            var this_val = $(this).attr('data-id');
            if($(this).is(':checked')){
                modal_obj.find("input[name='card_id']").val(this_val);
            }else{
                modal_obj.find("input[name='card_id']").val('');
            }
        });
        //同步 表单序列化提交 提交数据
        $("#synchronous-confirm").on('click',function(e){
            var modal_obj = $('#synchronous-btn-search').parent();
            var card_id = modal_obj.find("input[name='card_id']").val();
            if(card_id=='') {
                layer.msg('请勾选需要同步的微信优惠券');
                return false;
            }
            var alert_obj=$(this).parents(".modal-content").find(".alert-danger");
            Custom.ajaxPost("{:url('synchronousCard')}",{card_id:card_id},alert_obj);
        });
    });

    $('.export-card-commodity').on('click', function (){
        var export_type = $(this).attr('data-type');
        console.log('export_type',export_type)
        var card_ids = [];
        if (export_type === 'more-card'){
            $.each($(".check-order"), function (i, v) {
                if ($(v).is(':checked') == true) {
                    var card_id = $(v).attr('data-card-id');
                    card_ids.push(card_id);
                }
            })
            if (card_ids.length === 0){
                layer.msg('请选择优惠券', {icon: 5, time: 3000});
                return false;
            }
        }else if (export_type === 'one-card'){
             var card_id = $(this).attr('data-card-id');
             card_ids.push(card_id);
        }
        console.log('card_ids',card_ids)
        var data = $.param({'card_ids':card_ids,'export_type':1});
        Custom.ajaxPost("{:url('exportCardCommodity')}", data);
    })

    $(".allcheck-order").change(function(){
        $(".check-order").attr('checked',$(this).is(':checked'))
        if($(this).is(':checked') == false){
            $("#allcheck-order").attr('checked',false);
        }
    })


    $(".check-order").change(function(){
        if($(this).is(':checked') == false){
            $(".allcheck-order").attr('checked',false);
            $("#allcheck-order").attr('checked',false);
        }
    })
</script>
<script>
    // console.log(getUrlParam('act_status'))

    if(getUrlParam('act_status') != null){
        var act_status_select = [
            {name: '未开始', value: 1,selected:false},
            {name: '进行中', value: 2,selected:false},
            {name: '已结束', value: 3,selected:false},
            {name: '已关闭', value: 4,selected:false},
        ];
        var act_status = getUrlParam('act_status').split(",")
        $.each(act_status,function (key,value){
            $.each(act_status_select,function (k,v){
                if(value == v.value){
                    v.selected = true
                }
            })
        })
        console.log(act_status_select)
    }else{
        var act_status_select = [
            {name: '未开始', value: 1,selected:true},
            {name: '进行中', value: 2,selected:true},
            {name: '已结束', value: 3,selected:false},
            {name: '已关闭', value: 4,selected:false},
        ];
    }

    xmSelect.render({
        el: '#act_status_select',
        name: "act_status",
        autoRow: true,
        cascader: {
            show: true,
            indent: 300,
        },
        height: '180px',
        data(){
            return act_status_select
        }
    })
</script>

{/block}
