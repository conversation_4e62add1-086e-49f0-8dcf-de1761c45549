{extend name="public:base_layout" /}

{block name="content"/}

<div class="panel-body">
    <div class="alert alert-danger m-b-8" style="display: none;">
        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
        <p></p>
    </div>
    <div class="well m-t-10">
        <form id="add_form" class="form-horizontal form-bordered" data-parsley-trigger="change">
            <input type="text" class="hidden" name="id" value="{$info.id}">
            <div class="form-group">
                <label class="control-label col-md-2">订单编码:</label>
                <div class="col-md-10">
                    {$info.order_code}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">商户订单号:</label>
                <div class="col-md-10">
                    {$info.pay_order_code}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">订单金额:</label>
                <div class="col-md-10">
                    {$info.total_money}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">专营店:</label>
                <div class="col-md-10">
                    {$info.dlr}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">姓名:</label>
                <div class="col-md-10">
                    <input class="form-control input-sm width-300" name="name" value="{$info.name}">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">手机号:</label>
                <div class="col-md-10">
                    <input class="form-control input-sm width-300" name="phone" value="{$info.phone}">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">VIN:</label>
                <div class="col-md-10">
                    {$info.vin}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">车牌:</label>
                <div class="col-md-10">
                    {$info.license_plate}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">厂家积分:</label>
                <div class="col-md-10">
                    {$info.integral}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">专营店积分:</label>
                <div class="col-md-10">
                    {$info.dlr_integral}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">卡劵名称:</label>
                <div class="col-md-10">
                    {$info.card_name}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">卡劵CODE:</label>
                <div class="col-md-10">
                    {$info.card_code}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">下单时间:</label>
                <div class="col-md-10">
                    {$info.created_date}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">订单状态:</label>
                <div class="col-md-10">
                    <?php $selected = false;?>
                    <select class="form-control input-sm  width-200 default-select2" name="order_status" >
                        <?php foreach($order_status_list as $k=>$order_status){?>
                            <option value="{$k}" <?php if($info['order_status']==$k) {echo 'selected';$selected = true;}?>>
                                {$order_status}
                            </option>
                        <?php } ?>
                        <?php if(empty($selected)){?>
                            <option value="{$info['order_status']}" selected>
                                {$hidden_status[$info['order_status']]}
                            </option>
                        <?php } ?>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">物流方式:</label>
                <div class="col-md-10">
                    <select class="form-control input-sm width-200 default-select2" name="logistics_mode" >
                        {volist name="logistics_mode_list" id="vo" key="k"}
                        <option value="{$k}" <?php if($info['logistics_mode']==$k) echo 'selected'?>>{$vo}</option>
                        {/volist}
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">销售来源:</label>
                <div class="col-md-10">
                    {$info.sales_source}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">订单来源:</label>
                <div class="col-md-10">
                    {$info.order_source}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">物流地址:</label>
                <div class="col-md-10">
                    <input type="text" class="form-control input-sm width-300" name="receipt_address" value="{$info.receipt_address}" placeholder="">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">商品明细:</label>
                <div class="col-md-10">
                    {volist name="comm_list" id="comm"}
                    <div>
                        <img class="comm-img" src="{$comm.cover_image}">
                        <a class="init-commodity-preview" data-dlr-code='{$comm.dlr_code}' data-commodity-id='{$comm.commodity_id}'>{$comm.commodity_name}</a>
                        <span class="separator">{$comm.sku_info}</span>
                        <span class="separator">车型:{$comm.car_info}</span>
                        <span class="separator">单价:{$comm.price}</span>
                        <span class="separator">数量:{$comm.count}</span>
                        <span class="separator">总价:{$comm.total_price}</span>
                        <span class="separator">商品编码:{$comm.commodity_code}</span>
                        <span class="separator">规格编码:{$comm.sku_code}</span>
                    </div>
                    {/volist}
                </div>
            </div>
            <div class="text-center" style="padding: 10px">
                <a type="button" id="bt-submit"  class="btn btn-primary btn-sm m-l-200" data-toggle="modal" href="#order_detail_modal" data-order-code="{$info.order_code}">确定提交</a>
            </div>
        </form>
    </div>


    <!-- BEGIN 订单详情-->
    <div class="modal fade" id="order_detail_modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">操作提醒</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <h4>订单操作</h4>
                        <p></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <a id="submit" class="btn btn-sm btn-primary">确定</a>
                    <a class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
                </div>
            </div>
        </div>
    </div>
    <!-- END -->
</div>
{/block}
{block name="css"/}
<style>
.separator{
    margin-left: 10px;
}
.comm-img{
    width: 50px;
}
.form-horizontal.form-bordered .form-group>.control-label {
    padding: 22px 15px;
    border-right: 1px solid #eee;
    margin-right: -1px;
}
.form-horizontal.form-bordered .form-group>.col-md-10 {
    padding: 22px 15px;!important;
    border-left: 1px solid #eee;
}
</style>
{/block}
{block name="script"/}
<script>
    $(".default-select2").select2();
    $("#bt-submit").on("click",function () {
        var order_code = $(this).data('order-code');
        var current_status = "{$info.order_status}"; // 原始订单状态
        var new_status = $("select[name='order_status']").val(); // 新选择的订单状态

        var message = "您确定要修改订单“"+order_code+"”吗？";

        // 如果原来状态为11，修改为12时显示特殊提示
        if (current_status == "11" && new_status == "2") {
            message = "修改后状态可发起退款，请与供应商沟通暂缓发货，否则可能导致货物多发。是否确认修改为已支付？";
        }

        $("#order_detail_modal").find(".alert-danger>p").text(message);

        // $("#order_detail_modal").find(".alert-danger>p").text("您确定要修改订单“"+order_code+"”吗？")
    })
    $("#submit").on("click",function () {
        var form = $("#add_form");
        var data = form.serialize();
        var alert_obj = $(this).closest(".modal").find('.alert-danger');
        Custom.ajaxPost("{:url('ajaxDeal')}",data,alert_obj,"{:url('index')}");
    })
</script>
{/block}