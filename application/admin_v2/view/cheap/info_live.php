{extend name="public:base_layout" /}
{block name="css"/}
<link href="__STATIC__admin_v2/plugins/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/css/bootstrap-datetimepicker.min.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/css/style_commodity_type.css" rel="stylesheet"/>
<link href="__STATIC__admin_v2/css/jBootsrapPage.css" rel="stylesheet"/>
<style>
    .form-group dd{
        margin-top: 5px;
    }
    .cover-image{
        width: 40px;
        height: 40px;
        margin-right: 10px;
        margin-left: 20px;
    }
    /*添加规则230423*/
    .ruleEdit {
        font-size: 14px;
        padding:0 15px 15px;
    }
    .ruleEdit .form-control {
        width:80px;
        display: inline-block;
        margin:0 10px;
    }
    .ruleEdit .row {
        margin-bottom:10px;
        overflow: hidden;
    }
    .ruleEdit .row1 .fa-minus-circle {
        float: right;
        position: relative;
        top:3px;
        margin-right:10px;
    }
    .ruleEdit .row .fa.fa-2x {
        vertical-align: -5px;
        margin:0 10px;
    }
    .ruleEdit .items-wp {
        margin-bottom:20px;
    }
    .ruleEdit .item {
        padding:15px 0;
        border-bottom:1px dashed #ccc;
    }
    .ruleEdit .btn-wp {
        margin-right:10px;
    }

    .sku-confirm-all-active{
        background: #929ba1;
        border-color: #929ba1
    }

</style>
{/block}
{block name="content"/}
<div class="panel-body">
    <div class="alert alert-danger m-b-8 m-t-10" style="display: none;">
        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
        <p></p>
    </div>
    <div class="well m-t-10">
        <legend class="pull-left width-full">新增-优惠套装</legend>
        <form id="fight-form"class="form-horizontal form-bordered" data-parsley-trigger="change">
            <div class="form-group">
                <label class="control-label col-md-2">活动名称<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" name="name" placeholder="请输入活动名称,不能超出10个字符" class="form-control " data-parsley-required="true" data-parsley-length="[1, 10]">

                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">起止时间<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">
                    <div class="col-md-3 p-l-0" >
                        <input type="text" name="s_time" placeholder="请输入开始时间" class="form-control datetimepicker3"  data-parsley-required="true" >
                    </div>

                    <div class="col-md-3 p-l-0">
                        <input type="text" name="e_time" placeholder="请输入结束时间" class="form-control datetimepicker3"  data-parsley-required="true" >
                    </div>
                </div>
                <input type="hidden" value="" id="dlrs"/>
                <input type="hidden" name="type" value="{$set_type}" id="type"/>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">促销标签:</label>
                <div class="col-md-5" style="width: 70%;">
                    <input type="text" name="tag"  style="width:200px;background-color: #fff;background-image: none;
    border: 1px solid #ccc;border-radius: 4px;box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset; color: #555;font-size: 14px;height: 34px;line-height: 1.42857;padding: 6px 12px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;border-radius: 3px;box-shadow: none; font-size: 12px;" maxlength="4"/>
                    (促销标签是商家对促销动作的别名操作，用于前台显示，最多可输入4个字符，非必填选项）
                </div>
            </div>

            {include file="activity:activity_img" /}

            <div class="form-group"  >
                <label class="control-label col-md-2">业务归属:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <select name="gather_id" class="form-control input-sm ">
                            <option value=""> 全部 </option>
                            {volist name="$gather_list" id="val" key="key"}
                            <option value="{$val['id']}" {if($Think.get.gather_id == $val['id'])}selected{/if} > {$val['name']} </option>
                            {/volist}
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">主题活动:</label>
                <div  class="col-md-5" id="is_enable">
                    <input type="text" name="theme_name" id="theme_name" placeholder="请输入主题活动" value="" class="form-control" data-parsley-length="[0, 100]">
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">是否PV补贴:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_pv_subsidy" type="checkbox" data-input-name="is_pv_subsidy" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭" />
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">是否叠加券:</label>
                <div  class="col-md-5" id="is_card_change">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch col-md-9" id="card_available" type="checkbox" name="switch" data-input-name="card_available"
                                   data-input-true="1" data-input-false="0"
                                   data-size="small" data-on-text="开启" data-off-text="关闭" checked />
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group no_card">
                <label class="col-md-2 control-label" id="card_activity">选择可叠加的券:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control card_activity" name="card_activity" placeholder="点击选择" data-parsley-group="wizard-step-1" id="text-a" data-id="" value="">
                    <input type="hidden" class="form-control hide_card_ids" name="rel_card_ids" >
                </div>
                <div class="bottom-wp">
                    <div class="bottom-left">
                        已选<input type="" class="num_card_ids" style="width: 20px;outline:none;border: 0; readonly " value="0">张
                    </div>
                </div>
            </div>
<!--            <div class="form-group ">-->
<!--                <label class="col-md-2 control-label" id="e3s_activity">关联E3S活动:</label>-->
<!--                <div class="col-md-6">-->
<!--                    <input type="text" autocomplete="off" class="form-control e3s_activity" name="e3s_activity" placeholder="请选择活动" data-parsley-group="wizard-step-1" id="text-a" data-id="">-->
<!--                    <input type="hidden" id="e3s_activity_id" name="e3s_activity_id">-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="form-group ">-->
<!--                <label class="col-md-2 control-label" id="activity_type">活动设置类型:</label>-->
<!--                <div class="col-md-6">-->
<!--                    <select class="form-control width-300" name="activity_type" >-->
<!--                        <option value="0">请选择</option>-->
<!--                        <option value="1">备件</option>-->
<!--                        <option value="2">工时</option>-->
<!--                        <option value="3">赠品</option>-->
<!--                    </select>-->
<!--                </div>-->
<!--            </div>-->
            <div class="form-group ">
                <label class="col-md-2 control-label" id="settlement_rule">关联结算规则:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control settlement_rule" name="settlement_rule" id="template_name" placeholder="请选择选择结算规则" data-parsley-group="wizard-step-1" >
                    <input type="hidden" id="settlement_rule_id" name="settlement_rule_id">
                    <input type="hidden" id="settlement_rule_value" name="settlement_rule_value">
                    <input type="hidden" id="settlement_rule_type" name="settlement_rule_type">
                    <input type="hidden" id="act_sett_standard" name="act_sett_standard">
                </div>
            </div>

            <div class="form-group" id="up_down">
                <label class="control-label col-md-2">活动渠道:<i class="m-r-3 text-danger">*</i></label>
                <div class="col-md-10">
                    {volist name="up_down_channel" id="val"}
                        {if condition="$live_type==1"}
                            {in name="$key" value="PZ1AAPP,PZ1ASM"}
                                <label class="checkbox-inline">
                                    <input type="checkbox"
                                           name="up_down_channel[]" data-parsley-required="true"
                                           value="{$key}" id="{$key}" data-name="{$val}">{$val}
                                </label>
                            {/in}
                        {elseif condition="$live_type==2" /}
                            {in name="$key" value="QCSM,QCAPP"}
                                <label class="checkbox-inline">
                                    <input type="checkbox"
                                           name="up_down_channel[]" data-parsley-required="true"
                                           value="{$key}" id="{$key}" data-name="{$val}">{$val}
                                </label>
                            {/in}
                        {elseif condition="$live_type==0" /}
                            {in name="$key" value="GWSM,GWAPP"}
                                <label class="checkbox-inline">
                                    <input type="checkbox"
                                           name="up_down_channel[]" data-parsley-required="true"
                                           value="{$key}" id="{$key}" data-name="{$val}">{$val}
                                </label>
                            {/in}
                        {else /}
                        {/if}
                    {/volist}
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-2 control-label" id="dis_type">优惠类型:</label>
                <div class="col-md-6">
                    <select class="form-control width-300 dis_type" name="dis_type" >
                        <option value="1">自由设置价格</option>
                        <option value="5">一口价</option>
                        <option value="2">满减</option>
                        <option value="3">限时折扣</option>
                        <option value="4">限时立减</option>
                    </select>
                </div>
            </div>

            <div class="form-group full_decrement" style="display: none">
                <label class="control-label col-md-2">
                    满减<i class="m-r-3 text-danger">*</i>:
                </label>
                <div class="col-md-10 ruleEdit" id="ruleEdit">
                    <div class="row row2">
                        满<input type="text" disabled class="form-control piece form-j" >元减<input disabled type="text" class="form-control
                    discount form-z">元<i class="fa fa-2x fa-plus-circle text-success"></i>
                    </div>
                </div>
            </div>

            <div class="form-group discount_price" style="display: none">
                <label class="control-label col-md-2">
                    限时折扣<i class="m-r-3 text-danger">*</i>:
                </label>
                <div class="input-group col-md-4" style="float:left;">
                    <input type="text" disabled placeholder="请输入" data-parsley-errors-container="#discount" name="discount" class="form-control set-discount col-md-3" maxlength="3" data-parsley-required="true" data-parsley-type="number"  data-parsley-range="[0.1,9.9]" >
                    <span class="input-group-addon" >折</span>
                </div>
            </div>

            <div class="form-group reduction_price" style="display: none">
                <label class="control-label col-md-2">
                    限时立减<i class="m-r-3 text-danger">*</i>:
                </label>
                <div class="input-group col-md-4" style="float:left;">
                        <input  type="text" disabled placeholder="请输入" class="form-control set-price default-price" name="discount" value="" data-parsley-required="true" data-parsley-errors-container="#valid-dis_money" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">
                        <span class="input-group-addon" >元</span>
                </div>
            </div>

            <div class="form-group regular_price" style="display: none">
                <label class="control-label col-md-2">
                    一口价<i class="m-r-3 text-danger">*</i>:
                </label>
                <div class="input-group col-md-4" style="float:left;">
                    <input  type="text" disabled placeholder="请输入" class="form-control set-price default-price" name="discount" value="" data-parsley-required="true" data-parsley-errors-container="#valid-dis_money" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">
                    <span class="input-group-addon" >元</span>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">允许退:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="can_refund" type="checkbox" data-input-name="can_refund" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭" checked />
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">活动状态:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_enable" type="checkbox" data-input-name="is_enable" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭" checked />
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">活动商品<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10" >
                    <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                        <div style="float:left;width:60%">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品分类:</label>
                            <select class="form-control input-sm element default-select2 col-md-3" name="comm_parent_id" id="slt_comm_type_id" style="width: 25%">
                                <option value="0">
                                    请选择
                                </option>
                                {volist name="comm_parent_list" id="val"}
                                <option value="{$val['id']}" >{$val['comm_type_name']}</option>
                                {/volist}
                            </select>
                            <select name="sub_comm_type_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id" style="width: 25%">
                                <option value="0">
                                    请选择
                                </option>
                            </select>
                            <select name="three_comm_type_id" class="form-control input-sm element default-select2" id="slt_three_comm_type_id" style="width: 25%">
                                <option value="0">
                                    请选择
                                </option>
                            </select>
                        </div>
                        <div style="float:left;width:30%">
                            <label class="control-label col-md-2" style="padding:  5px 5px;  width: auto;">商品名称:</label>
                            <input type="text" class="form-control input-sm element col-md-3 m-r-2 " style=" width: 78%"
                                   name="commodity_name" placeholder="请输入商品名称">
                        </div>
                    </div>
                    <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                        <div   id="btn-sm-div" style="float:left;width:40%">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品类型:</label>
                            <select class="form-control input-sm element default-select2 col-md-3 comm-type-search2"
                                    name="commodity_dlr_type_id" onchange="commTypeSearch1()" id="slt_comm_type_id" style="width: 58%">
                                <option value="0">
                                    请选择
                                </option>
                                {volist name="comm_dlr_type_list" id="val"}
                                <option value="{$val['id']}" >{$val['inner_name']}</option>
                                {/volist}
                            </select>
                            <button id="comm-type-search" type="button" class="btn btn-sm btn-success comm-type-search2"><i class="fa fa-search"></i>搜索</button>
                        </div>
                    </div>
                    <div>
                        <div class="table-scrollable">
                            <table id="" class="table table-hover">
                                <thead>
                                <th class="text-center">商品规格</th>
                                <th class="text-center">上架渠道</th>
                                <th class="text-center">原价</th>
                                <th class="text-center">库存</th>
                                <th class="text-center">
                                    <a data-current-page="1" data-is-sku-confirm-all="1"
                                       data-comm-id="all" home="all" data-comm-set-id="all" commodity_class=""
                                       data-dlr-code="all"
                                       class="btn btn-sm btn-default btn-white"
                                       id="sku-confirm-all"
                                    >批量添加</a>
                                </th>
                                </thead>
                                <tbody id="add-comm-tbody">

                                </tbody>
                            </table>
                        </div>
                        <div>
                            <ul class="pagination" id="comm-pagination"></ul>
                        </div>
                    </div>

                    <legend class="pull-left width-full" style="font-size: 18px;">
                        <div class="col-md-10">
                            已选活动商品
                        </div>
                        <div class="col-md-2">
                            <a class="btn btn-danger btn-sm m-r-5 m-b-5 del-commodity">删除全部商品</a>
                        </div>
                    </legend>

                    <div>
                        <div class="table">
                            <table id="commodity_select" class="table">
                                <thead></thead>

                                <tbody id="haved-commodtiy">

                                </tbody>
                            </table>
                            <div id="pagiDiv" style="width:100%;text-align: center;display: none">
                                <span id="spanFirst">首页</span>
                                <span id="spanPre">上一页</span>
                                <span id="spanNext">下一页</span>
                                <span id="spanLast">尾页</span>
                                第 <span id="spanPageNum"></span> 页/共 <span id="spanTotalPage"></span> 页
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">套餐详情:</label>
                <div class="col-md-9 ">
                    <script id="content" name="detail_content" type="text/plain" style="width:100%;height:300px;z-index:999;"></script>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2"></label>
                <div class="col-md-5">
                    <a href="javascript:;" class="btn btn-sm btn-primary btn-sm"  id="put-form">确认</a>
                </div>
            </div>
        </form>
    </div>


    <!----begin 查看规格---->
    <div class="modal fade" id="sku-modal" data-comm-id="" data-comm-set-id="" data-commodity-class="" data-type="" data-dd-commodity-type="">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <input type="hidden" id="comm-id"/>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择商品: <span id="comm-name-header"></span></h4>
                </div>
                <input id="modal_com_type_id" type="hidden" value="" />
                <input id="modal_home"  type="hidden" value="" />
                <input id="modal_commodity_class"  type="hidden" value="" />
                <div class="modal-body" style="">

                    <div class="hide">
                        <div class="sku-image" style="display: inline-block;">
                            <img class=" cover-image" src="">
                        </div>
                        <div style="display: inline-block;">
                            <div  class="sku-comm"></div>
                        </div>
                    </div>

                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th class="col-md-6" >商品规格</th>

                            <th >原价</th>
                            <th id="all_price_th"><a href="#" id="all_price" data-type="text" class="btn btn-primary btn-sm editable editable-click" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i class="fa fa-edit m-l-5"></i></a></th>
                            <th>库存</th>
                        </tr>
                        </thead>
                        <tbody class="sku-tb">

                        </tbody>
                    </table>

                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >关闭</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary" id="sku-confirm" >确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 删除设置---->
    <div class="modal fade" id="del-sku-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">操作提醒</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger m-b-0">
                        <h4><i class="fa fa-info-circle"></i>确定删除该商品?</h4>
                        <p></p>
                    </div>
                </div>
                <input type="hidden" id="del-data-id" data-comm-set-id="" value="">
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="del-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>
</div>

{/block}
{block name="script"/}
{include  file="public/baidu_ueditor_js" /}
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/moment-with-locales.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/bootstrap-datetimepicker.min.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/fileinput.min.js"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js"></script>
<script>
    var urlCardparam    ="{:url('ajaxGetCard')}" + "?set_type=" + "{$set_type}";
    $("#slt_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        $.get("{:url('ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">选择</option>';
            $("#slt_three_comm_type_id").html(html);
            if(comm_parent_id == 0){
                $("#slt_sub_comm_type_id").html(html);return;
            }
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_sub_comm_type_id").html(html);
        },'json');

    }) ;

    $("#slt_sub_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        if(comm_parent_id == 0){
            var html='<option value="0">请选择</option>';
            $("#slt_three_comm_type_id").html(html);return;
        }
        $.get("{:url('ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">请选择</option>';
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_three_comm_type_id").html(html);
        },'json');
    }) ;
    var admin_type="{$admin_type}";
    var commodity_arr = {};
    var getSkuUrl ="{:url('getSkuLive')}";
    var fight_group_id=0;
    var getDlr_url="{:url('ajaxGetDlr')}";
    var ajaxCommUrl = "{:url('ajaxGetLiveCommodityList')}?wxtag=1&pagesize=10&live_type={$live_type}";
    var ajaxCommUrl2 = ajaxCommUrl;
    var save_url    ="{:url('saveLive')}?wxtag=1";
    var index_url   ="{:url('live')}";
    var getSetIdUrl =   "{:url('ajaxGetSetId')}";

    var oneCommodityDlrTypeId = 0;
    var commDlrTypeJson = {$comm_dlr_type_json};

     var ajaxData= {$dlr_data};
    $("#ptype").change(function(){
        var id = $(this).val();
        if(id == 0){
            var str = '<select class="form-control" name="subtype" id="subtype">';
            str= str + '<option  value="0">请选择</option>';
            str = str +'</select>';
            $("#subtype_box").empty().append(str);
            $("#qrCode").empty();
        }else{
            $.post(
                "{:url('selectType')}",
                {id:id},
                function(cb){
                    if(cb.error == 0 ){
                        var str = '<select class="form-control" name="subtype" id="subtype">';
                        $(cb.data).each(function(i,v){
                            str= str + '<option  value="'+v.id+'">'+v.comm_type_name+'</option>';
                        })
                        str = str +'</select>';
                        $("#subtype_box").empty().append(str);
                        var url  = cb.src;
                        $("#qrCode").empty().qrcode(url)
                    }else{
                        layer.msg('操作失败')
                    }
                },'json'
            );
        }
    });

    var comm_parent_list = <?php echo json_encode_cn($comm_parent_list);?>;

    $("body").on('click','#GWDLR',function(){
        if($("#GWDLR").is(':checked')){
            $('#dealer_select').removeClass('hidden');
        }else{
            $('#dealer_select').addClass('hidden');
        }
    });

    var dlr_data = <?= $dlr_data ?>;

    $("body").on("click",'#dlr_show',function () {
        var select_data = $("#dlr_hide").val().split(',');
        Custom.selectDlr(dlr_data,select_data,function (dlr_code,dlr_name) {
            $("#dlr_show").val(dlr_name.join(','));
            $("#dlr_hide").val(dlr_code.join(','));
        });
    })
    // 添加小规则
    $(document).on('click','#ruleEdit .fa-plus-circle',function(){
        var l=$(this).parents('.ruleEdit').find('.row').length;
        if(l>2){
            layer.msg('最多设置3个层级');
            return false;
        }
        $(this).parents('.ruleEdit').find('.row:last').after('<div class="row row2">满<input type="text"'
            +' class="form-control piece '
            +' form-j" >元减<input type="text" class="form-control discount form-z">元<i class="fa fa-2x fa-minus-circle'
            +' text-danger"></i></div>');
    })

    // 删除小规则
    $(document).on('click','#ruleEdit .row2 .fa-minus-circle',function(){
        $(this).closest('.row').remove();
    })

    var get_data=function(){
        data_l=[];
        $('#ruleEdit').find('.form-control').each(function(){
            if($(this).val()!==''){
                data_l.push($(this).val());
            }
        })
        if(data_l.length!=$('#ruleEdit').find('.form-control').length){
            alert('请填写完整的满减信息')
            return false;
        }else{
            var data=[];
            $('.ruleEdit .row2').each(function(){
                var data2=[];
                data2[0]=$(this).find('.form-j').val();
                data2[1]=$(this).find('.form-z').val();
                data.push(data2);
            })

            return data;
        }
    }

    $(document).on('change','.dis_type',function (){
        if($(this).val() == 5){
            $('.regular_price').show();
            $('.full_decrement').hide();
            $('.discount_price').hide();
            $('.reduction_price').hide();
            $('.regular_price input').attr('disabled',false)
            $('.reduction_price input').attr('disabled',true)
            $('.full_decrement input').attr('disabled',true)
            $('.discount_price input').attr('disabled',true)
        }else if($(this).val() == 4){
            $('.regular_price').hide();
            $('.full_decrement').hide();
            $('.discount_price').hide();
            $('.reduction_price').show();
            $('.reduction_price input').attr('disabled',false)
            $('.regular_price input').attr('disabled',true)
            $('.full_decrement input').attr('disabled',true)
            $('.discount_price input').attr('disabled',true)
        }else if($(this).val() == 3){
            $('.regular_price').hide();
            $('.full_decrement').hide();
            $('.discount_price').show();
            $('.reduction_price').hide();

            $('.regular_price input').attr('disabled',true)
            $('.reduction_price input').attr('disabled',true)
            $('.full_decrement input').attr('disabled',true)
            $('.discount_price input').attr('disabled',false)
        }else if($(this).val() == 2){
            $('.regular_price').hide();
            $('.full_decrement').show();
            $('.discount_price').hide();
            $('.reduction_price').hide();
            $('.full_decrement input').attr('disabled',false)
            $('.regular_price input').attr('disabled',true)
            $('.reduction_price input').attr('disabled',true)
            $('.discount_price input').attr('disabled',true)
        }else{
            $('.regular_price').hide();
            $('.full_decrement').hide();
            $('.discount_price').hide();
            $('.reduction_price').hide();
        }
        // alert($(this).val())
    })

    //活动商品列表搜索
    function commTypeSearch1(isSkuConfirmAll = 0){
        var commodity_class = $("select[name='commodity_class']").val();
        var commodity_dlr_type_id = $("select[name='commodity_dlr_type_id']").val();
        var comm_parent_id = $("select[name='comm_parent_id']").val();
        var page = $("#sku-confirm-all").data("current-page");
        ajaxCommUrl = ajaxCommUrl2 + '&commodity_dlr_type_id='+commodity_dlr_type_id+ '&page='+page;
        //请求公示接口数据
        commTypeSearch(isSkuConfirmAll)
    }
    //已全选的分页
    var skuConfirmAllCurrentPage = [];
    //当前页所有的commodity_id值
    currentPageCommSetIds = [];
    var comm_set_id_arr = [];
    $("#sku-confirm-all").on('click',function(){
        var isSkuConfirmAll = $(this).data('is-sku-confirm-all');

        if(isSkuConfirmAll == 0){
            layer.msg('当前页已全部添加，请不要重复操作');
            return false;
        }
        var obj = $(this).parents('.form-group');
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();

        // if(oneCommodityDlrTypeId != commodity_dlr_type_id){
        //     $("#sku-modal").modal('hide');
        //     var msg2 = "只能添加同种类型的商品，该商品为“"+commDlrTypeJson[oneCommodityDlrTypeId]+"“";
        //     layer.msg(msg2);
        //     return;
        // }
        //先请求列表数据，同时进行批量数据操作
        commTypeSearch2(1);
    });

    //活动商品列表搜索
    function commTypeSearch2(isSkuConfirmAll = 0){
        var commodity_class = $("select[name='commodity_class']").val();
        var commodity_dlr_type_id = $("select[name='commodity_dlr_type_id']").val();
        var comm_parent_id = $("select[name='comm_parent_id']").val();
        var page = $("#sku-confirm-all").data("current-page");
        var user_segment = $('input:radio[name=user_segment]:checked').val()
        if(user_segment == 'undefined') user_segment = 0;
        ajaxCommUrl = ajaxCommUrl2 + '&commodity_dlr_type_id='+commodity_dlr_type_id+ '&page='+page + '&user_segment='+user_segment;

        //请求公示接口数据
        commTypeSearch(isSkuConfirmAll)
    }

    $('.del-commodity').on('click',function (){
        if($("table #haved-commodtiy tr:visible").length > 0){
            var index = layer.open({
                title: ['操作提醒'],
                btn: ['确认', '取消'],
                content:"<div style='font-size: 15px'>您选择对商品列表中全部商品进行移除操作吗?</div>",
                yes: function (res) {
                    $("#haved-commodtiy").empty();
                    $("#sku-confirm-all").data('is-sku-confirm-all',1)
                    initComm(ajaxCommUrl2);
                    commodity_select()
                    comm_id = [];
                    comm_set_id_arr = [];
                    skuConfirmAllCurrentPage = []
                    layer.close(index);
                }
            })
        }

    })
</script>
<script src="__STATIC__admin_v2/js/cheap_live.js?rand=2.0.6"></script>
<script src="__STATIC__admin_v2/js/commodity_page.js"></script>
{include file="card:act_rule" /}
{include file="card:act_card_rule" /}
{include file="activity:activity_js" /}
{/block}