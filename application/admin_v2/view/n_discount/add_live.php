{extend name="public:base_layout" /}

{block name="css"/}

<style>
    .form-group dd{
        margin-top: 5px;
    }
    .cover-image{
        width: 40px;
        height: 40px;
        margin-right: 10px;
        margin-left: 20px;
    }

    /*添加规则200515*/
    .ruleEdit {
        font-size: 14px;
        padding:0 15px 15px;
    }
    .ruleEdit .form-control {
        width:80px;
        display: inline-block;
        margin:0 10px;
    }
    .ruleEdit .row {
        margin-bottom:10px;
        overflow: hidden;
    }
    .ruleEdit .row1 .fa-minus-circle {
        float: right;
        position: relative;
        top:3px;
        margin-right:10px;
    }
    .ruleEdit .row .fa.fa-2x {
        vertical-align: -5px;
        margin:0 10px;
    }
    .ruleEdit .items-wp {
        margin-bottom:20px;
    }
    .ruleEdit .item {
        padding:15px 0;
        border-bottom:1px dashed #ccc;
    }
    .ruleEdit .btn-wp {
        margin-right:10px;
    }

    .level_name_box{
        width: 90px;float: left;text-align: center;padding-top: 5px;
        margin-left:20px;
    }
    .level_name_item_box{
        float:left;width: 70px
    }
    .btnc{
        text-align: center;padding: 10px 0 0 10px;float:left;
    }
    .stylelt{
        /*width: 74px;*/
        float: left;text-align: center;
    }
    .level_name_box_1{
        height:100px;
    }
    .level_name_box_2{
        height:100px;
    }
    .radio, .radio-inline{
        line-height:unset !important;
    }
    .level_item_level .user_level{
        margin-left:0 !important;
    }
    .level_zhe_box >div{
        width:65px;
        margin-right:10px;
        float:left;
    }
    .level_zhe_car >div{
        width:65px;
        margin-right:10px;
        float:left;
    }
    .level_jian{width:6%}
    .level_pi{ width:15%}
    .level_zhe_box{width:60%}
    .level_zhe_car{width: 25%}

</style>
{/block}

{block name="content"/}

<div class="panel-body">

    <div class="alert alert-danger m-b-8 m-t-10" style="display: none;">
        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
        <p></p>
    </div>
    <div class="well m-t-10">
        <legend class="pull-left width-full">新增-N件N折</legend>
        <form id="add-form"class="form-horizontal form-bordered" data-parsley-trigger="change">
            <div class="form-group">
                <label class="control-label col-md-2">活动名称<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" name="title" placeholder="请输入活动名称" class="form-control " data-parsley-required="true" data-parsley-length="[1, 15]">

                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">起止时间<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">
                    <div class="col-md-3 p-l-0" style="">
                        <input type="text" name="start_time" placeholder="请输入开始时间" class="form-control datetimepicker3" data-parsley-required="true" >
                    </div>

                    <div class="col-md-3">
                        <input type="text" name="end_time" placeholder="请输入结束时间" class="form-control datetimepicker3" data-parsley-required="true" >
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">促销标签:</label>
                <div class="col-md-5" style="width: 70%;">
                    <input type="text" name="tag"  style="width:200px;background-color: #fff;background-image: none;
    border: 1px solid #ccc;border-radius: 4px;box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset; color: #555;font-size: 14px;height: 34px;line-height: 1.42857;padding: 6px 12px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;border-radius: 3px;box-shadow: none; font-size: 12px;" maxlength="4"/>
                    (促销标签是商家对促销动作的别名操作，用于前台显示，最多可输入4个字符，非必填选项）
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">定向人群:</label>
                <div class="col-md-9">
                    <dd>未选择定向人群则代表所有用户可参与活动。选择定向人群后若更改人群类型，已添加的商品列表会被清空！活动开始后不可修改</dd>
                    <?php if ($set_type != 6) :?>
                    <label class="radio-inline">
                        <input type="radio" name="user_segment" class="user_segment" value="1"   data-parsley-multiple="dis_type" /> 会员
                    </label>
                    <?php endif;?>

                    <label class="radio-inline">
                        <input type="radio" name="user_segment" class="user_segment" value="2"  data-parsley-multiple="dis_type"/> 车主
                    </label>


                    <div style="margin-top: 30px;display: none;" id="user_level_box">
                        <dd>可参与的会员等级，若选择了某个会员等级后，该等级及更高等级的会员都有活动资格，高等级用户优惠力度需大于等于低等级用户。</dd>
                        <?php foreach($user_level as $key=>$val):?>
                            <label class="radio-inline">
                                <?php if ($val['value_code'] == 'NONE'):?>
                                <input type="radio" name="user_segment_options" class="user_segment_options" checked value="{$val['value_code']}" id="{$val['order_no']}" data-parsley-required="true" title="{$val['county_name']}" data-parsley-multiple="dis_type"/>{$val['county_name']}
                                <?php else:?>
                                    <input type="radio" name="user_segment_options" class="user_segment_options"  value="{$val['value_code']}" id="{$val['order_no']}" data-parsley-required="true" title="{$val['county_name']}" data-parsley-multiple="dis_type"/>{$val['county_name']}
                                <?php endif;?>
                            </label>
                        <?php endforeach;?>
                    </div>
                </div>
            </div>

            <div class="form-group"  id="rule_old">
                <label class="control-label col-md-2">活动金额设定<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10 ruleEdit" id="ruleEdit">
                    <div class="items-wp">
                        <div class="item" id="item-original">

                            <div class="row row2">
                                <input type="text"  class="form-control piece form-j" data-parsley-required="true">件<input type="text" class="form-control discount form-z" maxlength="3" data-parsley-type="number" data-parsley-range="[0.1,9.9]" data-parsley-required="true">折<i class="fa fa-2x fa-plus-circle text-success"></i>
<!--                                <span>（如填写9则表示九折，9.9则表示九九折，每条规则最多添加3种折扣）</span>-->
                            </div>
                        </div>
                        <dd>活动开始后不可修改，请慎重选择！最多可添加三个折扣等级</dd>
                    </div>
                </div>
            </div>

            <div class="form-group"  id="rule_new" style="display: none;">
                <label class="control-label col-md-2">活动金额设定<i class="m-r-3 text-danger">*</i>:</label>
                <div  class="col-md-8" id="discount">
                    <dd>活动开始后不可修改，请慎重选择！最多可添加三个折扣等级</dd>
                    <div class="input-group col-md-12">
                        <!--会员折扣-->
                        <div id="member_box" class="limit_boxs">
                            <div class="row dis_1 slope-dis"  style="margin-top: 12px;">
                                <div class="rule_new_box">
                                    <div class="level_item_level" style="height:60px">
                                        <div class="stylelt level_jian" style="color:transparent">占</div>
                                        <div class="level_name_box stylelt level_pi" style="color:transparent">占</div>
                                        <div class="level_zhe_box stylelt" id="county_name">

                                        </div>
                                    </div>
                                    <div class="level_item level_name_box_1">
                                        <div class="stylelt level_jian">
                                            <input size="4"  class=" piece form-j" data-parsley-required="true"><span class="c_dis_type">件</span>
                                        </div>
                                        <div class="level_name_box stylelt level_pi">
                                            <div class="level_name_item_box" ><a href="#" data-type="text" class="btn btn-primary btn-sm editable editable-click batch_price" data-value="" data-placeholder="批量设置" data-title="批量设置">批量设置<i class="fa fa-edit m-l-5"></i></a></div>
                                        </div>
                                        <div class="level_zhe_box stylelt" id="value_code">

                                        </div>

                                        <div class="btnc level_btn">
                                            <i class="fa fa-2x fa-plus-circle text-success adbtn"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--车主折扣-->
                        <div id="car_onwer_box" class="limit_boxs ">
                            <div class="row dis_1 slope-dis"  style="margin-top: 12px;">
                                <div class="rule_new_box">
                                    <div class="level_item_level" style="height:60px">
                                        <div class="stylelt level_jian" style="color:transparent">占</div>
                                        <div class="level_name_box stylelt level_pi" style="color:transparent">占</div>
                                        <div class="level_zhe_car stylelt">
                                            <div class="stylelt" >非车主</div>
                                            <?php if ($set_type == 6):?>
                                                <div  class="stylelt ">日产车主</div>
                                                <div  class="stylelt ">Ariya车主</div>

                                            <?php else:?>
                                                <div  class="stylelt ">车主</div>
                                            <?php endif;?>
                                        </div>
                                    </div>
                                    <div class="level_item level_name_box_2">
                                        <div class="stylelt level_jian">
                                            <input size="4"  class=" piece form-j" data-parsley-required="true"><span class="c_dis_type">件</span>
                                        </div>
                                        <div class="level_name_box stylelt level_pi">
                                            <div class="level_name_item_box" ><a href="#" data-type="text" class="btn btn-primary btn-sm editable editable-click car_batch_price" data-value="" data-placeholder="批量设置" data-title="批量设置">批量设置<i class="fa fa-edit m-l-5"></i></a></div>
                                        </div>
                                        <div class="level_zhe_car stylelt">
                                            <?php if (in_array($set_type,[5,7])): ?>
                                            <div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_1" /><span class="c_dis_type">折</span></div>
                                            <div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_2" data-parsley-required="true"/><span class="c_dis_type">折</span></div>
                                            <?php else:?>
                                                <div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_1" /><span class="c_dis_type">折</span></div>
                                                <div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_2" /><span class="c_dis_type">折</span></div>
                                                <div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_3" data-parsley-required="true"/><span class="c_dis_type">折</span></div>
                                            <?php endif;?>
                                        </div>
                                        <div class="btnc"><i class="fa fa-2x fa-plus-circle text-success adbtn2"></i></div>
                                    </div>
                                </div>
                                <?php if (in_array($set_type,[5,7])):?>
                                <dd>若未设置非车主价则非车主无参与资格，如需设置多个梯度，请保证多个梯度资格一致，车主的优惠力度需大于等于非车主</dd>
                                <?php else:?>
                                    <dd>若未设置非车主价则非车主无参与资格，如需设置多个梯度，请保证多个梯度资格一致，三类人群的优惠力度需排序为：Ariya车主≥日产车主≥非车主</dd>
                                <?php endif;?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">活动描述:</label>
                <div class="col-md-5">
                    <input type="text" name="des" id="des" placeholder="请输入活动描述" value="" class="form-control" data-parsley-length="[0, 30]">
                    <dd>活动描述是商家对促销活动的补充说明文字，用于前台显示，最多可输入30个字符，非必填选项</dd>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">业务归属:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <select name="gather_id" class="form-control input-sm ">
                            <option value=""> 全部 </option>
                            {volist name="$gather_list" id="val" key="key"}
                            <option value="{$val['id']}" {if($Think.get.gather_id == $val['id'])}selected{/if} > {$val['name']} </option>
                            {/volist}
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">主题活动:</label>
                <div  class="col-md-5" id="is_enable">
                    <input type="text" name="theme_name" id="theme_name" placeholder="请输入主题活动" value="" class="form-control" data-parsley-length="[0, 100]">
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">是否PV补贴:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_pv_subsidy" type="checkbox" data-input-name="is_pv_subsidy" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭" />
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">是否叠加券:</label>
                <div  class="col-md-5" id="card_available">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_card_change" type="checkbox" name="switch"  data-input-name="card_available"
                                   data-input-true="1" data-input-false="0"
                                   data-size="small" data-on-text="可用" data-off-text="不可用" checked />
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group no_card">
                <label class="col-md-2 control-label" id="card_activity">选择可叠加的券:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control card_activity" name="card_activity" placeholder="点击选择" data-parsley-group="wizard-step-1" id="text-a" data-id="" value="">
                    <input type="hidden" class="form-control hide_card_ids" name="rel_card_ids" >
                </div>
                <div class="bottom-wp">
                    <div class="bottom-left">
                        已选<input type="" class="num_card_ids" style="width: 20px;outline:none;border: 0; readonly " value="0">
                        张
                    </div>
                </div>
            </div>

            {include file="activity:activity_img" /}

            <div class="form-group ">
                <label class="col-md-2 control-label" id="e3s_activity">关联E3S活动:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control e3s_activity" name="e3s_activity" placeholder="请选择活动" data-parsley-group="wizard-step-1" id="text-a" data-id="" value="">
                    <input type="hidden" id="e3s_activity_id" name="e3s_activity_id" value="">
                </div>
            </div>
            <div class="form-group ">
                <label class="col-md-2 control-label" id="activity_type">活动设置类型:</label>
                <div class="col-md-6">
                    <select class="form-control width-300" name="activity_type" >
                        <option value="0">请选择</option>
                        <option value="1">备件</option>
                        <option value="2">工时</option>
                        <option value="3">赠品</option>
                    </select>
                </div>
            </div>
            <div class="form-group ">
                <label class="col-md-2 control-label" id="settlement_rule">关联结算规则:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control settlement_rule" name="settlement_rule" id="template_name" placeholder="请选择选择结算规则" data-parsley-group="wizard-step-1"  value="">
                    <input type="hidden" id="settlement_rule_id" name="settlement_rule_id" value="">
                    <input type="hidden" id="settlement_rule_value" name="settlement_rule_value">
                    <input type="hidden" id="settlement_rule_type" name="settlement_rule_type">
                    <input type="hidden" id="act_sett_standard" name="act_sett_standard">

                </div>
            </div>

            <div class="form-group" id="up_down">
                <label class="control-label col-md-2">活动渠道:<i class="m-r-3 text-danger">*</i></label>
                <div class="col-md-10">
                    <div>
                        <?php foreach ($up_down_channel as $key => $val): ?>
                            <?php if($live_type == 1):  if (in_array($key,['PZ1AAPP','PZ1ASM'])):?>
                                <label class="checkbox-inline">
                                    <input type="checkbox"
                                           name="up_down_channel[]" data-parsley-required="true"
                                           value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                </label>
                            <?php endif; else: if ($live_type == 2):  if (in_array($key,['QCAPP','QCSM'])):?>
                                <label class="checkbox-inline">
                                    <input type="checkbox"
                                           name="up_down_channel[]" data-parsley-required="true"
                                           value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                </label>
                            <?php endif; else:?>
                                <?php if(!in_array($key,['PZ1AAPP','PZ1ASM','TOBPC','QCAPP','QCSM'])):?>
                                    <label class="checkbox-inline">
                                        <input type="checkbox"
                                               name="up_down_channel[]" data-parsley-required="true"
                                               value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                    </label>
                                <?php endif; endif; endif; endforeach; ?>
                    </div>
                    <div style="margin-top:20px;" class="hidden" id="dealer_select">
                        <input id="dlr_show" type="text" class="form-control width-300" value="" placeholder="请点击选择经销商"
                               data-parsley-required="true">
                        <input id="dlr_hide" type="text" name="dlr_code" class="hidden" value="">
                    </div>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">活动状态:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_enable" type="checkbox" data-input-name="is_enable" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭" checked />
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">活动商品<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10">
                    <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                        <div style="float:left;width:60%">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品分类:</label>
                            <select class="form-control input-sm element default-select2 col-md-3" name="comm_parent_id" id="slt_comm_type_id" style="width: 25%">
                                <option value="0">
                                    请选择
                                </option>
                                <?php foreach($comm_parent_list as $key=>$val):?>
                                    <option value="{$val['id']}" >{$val['comm_type_name']}</option>
                                <?php endforeach;?>
                            </select>
                            <select name="sub_comm_type_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id" style="width: 25%">
                                <option value="0">
                                    请选择
                                </option>
                            </select>
                            <select name="three_comm_type_id" class="form-control input-sm element default-select2" id="slt_three_comm_type_id" style="width: 25%">
                                <option value="0">
                                    请选择
                                </option>
                            </select>
                        </div>
                        <div style="float:left;width:30%">
                            <label class="control-label col-md-2" style="padding:  5px 5px;  width: auto;">商品名称:</label>
                            <input type="text" class="form-control input-sm element col-md-3 m-r-2 " style=" width: 78%"
                                   name="commodity_name" placeholder="请输入商品名称">
                        </div>
                    </div>
                    <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                        <div style="float:left;width:60%">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品种类:</label>
                            <select class="form-control input-sm element default-select2 col-md-3 comm-type-search2"
                                    name="commodity_class" onchange="commTypeSearch2()"  id="slt_comm_type_id" style="width: 76%">
                                <option value="0">
                                    请选择
                                </option>
                                <?php foreach($commodity_class as $key=>$val):?>
                                    <option value="{$key}" >{$val}</option>
                                <?php endforeach;?>
                            </select>
                        </div>

                        <div   id="btn-sm-div" style="float:left;width:40%">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品类型:</label>
                            <select class="form-control input-sm element default-select2 col-md-3 comm-type-search2"
                                    name="commodity_dlr_type_id" onchange="commTypeSearch2()" id="slt_comm_type_id" style="width: 58%">
                                <option value="0">
                                    请选择
                                </option>
                                <?php foreach($comm_dlr_type_list as $key=>$val):?>
                                    <option value="{$val['id']}" >{$val['inner_name']}</option>
                                <?php endforeach;?>
                            </select>
                            <button id="comm-type-search" type="button" class="btn btn-sm btn-success comm-type-search2"><i class="fa fa-search"></i>搜索</button>
                        </div>
                    </div>
                    <div>
                        <div class="table-scrollable">
                            <table id="" class="table table-hover">
                                <thead>
                                <th class="text-center">商品规格</th>
                                <th class="text-center">上架渠道</th>
                                <th class="text-center">原价</th>
                                <th class="text-center">库存</th>
                                <th class="text-center">
                                    <a
                                            data-current-page="1"
                                            data-is-sku-confirm-all="1"
                                            data-comm-id="all"
                                            home="all"
                                            data-comm-set-id="all"
                                            commodity_class=""
                                            data-dlr-code="all"
                                            class="btn btn-sm btn-default btn-white"
                                            id="sku-confirm-all"
                                    >批量添加</a>
                                </th>
                                </thead>
                                <tbody id="add-comm-tbody">

                                </tbody>
                            </table>
                        </div>
                        <div>
                            <ul class="pagination" id="comm-pagination"></ul>
                        </div>
                    </div>

                    <legend class="pull-left width-full" style="font-size: 18px;">
                        <div class="col-md-10">
                            已选活动商品
                        </div>
                        <div class="col-md-2">
                            <a class="btn btn-danger btn-sm m-r-5 m-b-5 del-commodity">删除全部商品</a>
                        </div>
                    </legend>

                    <div>
                        <div class="table">
                            <table id="commodity_select" class="table">
                                <thead></thead>

                                <tbody id="haved-commodtiy">

                                </tbody>
                            </table>
                            <div id="pagiDiv" style="width:100%;text-align: center;display: none">
                                <span id="spanFirst">首页</span>
                                <span id="spanPre">上一页</span>
                                <span id="spanNext">下一页</span>
                                <span id="spanLast">尾页</span>
                                第 <span id="spanPageNum"></span> 页/共 <span id="spanTotalPage"></span> 页
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2"></label>
                <div class="col-md-5">
                    <input type="hidden" name="set_type" value="{$set_type}">
                    <a href="javascript:;" class="btn btn-sm btn-primary btn-sm"  id="put-form">确认</a>
                 </div>
            </div>
        </form>

    </div>

    <!----begin 查看规格---->
    <div class="modal fade" id="sku-modal" data-per-page="" data-comm-id="" data-comm-set-id="" data-type="">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择商品: <span id="comm-name-header"></span></h4>
                </div>
                <input id="modal_home"  type="hidden" value="" />
                <input id="modal_commodity_class"  type="hidden" value="" />
                <input id="commodity_dlr_type_id"  type="hidden" value="" />

                <div class="modal-body" style="">
                    <div class="alert alert-info m-b-0" style="padding: 8px;padding-top: 5px;">
                        <h6>操作提示</h6>
                        <p>默认该商品全部规格选中参加该项促销活动,如某个规格不想参与该项活动,可通过取消选择排除掉;被排除的规格将保持原价</p>
                    </div>
                    <d></d><br>

                    <div class="form-group">
                        <label class="control-label col-md-3" style="padding-left: 11px; padding-top: 10px; width: 100px;">折扣价设置:</label>
                        <div class="col-md-5" id="comm_discount">
                            <div class="input-group col-md-12" >
                                <input type="text" data-parsley-errors-container="#comm_discount" name="comm_discount" readonly="readonly" class="form-control col-md-3" maxlength="3" data-parsley-type="number"  data-parsley-range="[0.1,9.9]">
                                <span class="input-group-addon" >折</span>
                            </div>
                        </div>
                    </div>

                    <div class="hide">
                        <div class="sku-image" style="display: inline-block;">
                            <img class=" cover-image" src="">
                        </div>
                        <div style="display: inline-block;">
                            <div  class="sku-comm">
                            </div>
                        </div>
                    </div>

                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th class="col-md-6" >商品规格</th>
                            <th >原价</th>
                            <th style="text-align: center">
                                价格
                            </th>
                            <th>库存</th>
                        </tr>
                        </thead>
                        <tbody class="sku-tb">

                        </tbody>
                    </table>

                </div>

                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >关闭</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary  " id="sku-confirm" >确定</a>
                </div>
            </div>
        </div>
    </div>
    <!-- 删除提示 -->
    <div class="modal fade" id="modal-del">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" id="btn-pop-del-cancel2" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">提示</h4>
                </div>
                <div class="modal-body">
                    是否删除该规则
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" id="btn-pop-del-cancel" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-success" id="btn-pop-del" data-dismiss="modal">确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 删除设置---->
    <div class="modal fade" id="del-sku-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">操作提醒</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger m-b-0">
                        <h4><i class="fa fa-info-circle"></i>确定删除该商品?</h4>
                        <p></p>
                    </div>
                </div>
                <input type="hidden" id="del-data-id" data-comm-set-id="" value="">
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="del-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>


</div>
{/block}
{block name="script"/}

<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/moment-with-locales.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/bootstrap-datetimepicker.min.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/fileinput.min.js"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js"></script>

<script>
    var admin_type="{$admin_type}";
    var getSkuUrl ="{:url('getSku')}";
    var limit_discount_id=0;
    var getDlr_url="{:url('ajaxGetDlr')}";
    //异步请求数据地址
    var ajaxCommUrl = "{:url('ajaxGetLiveCommodityList',['pagesize'=>10,'set_type'=>$set_type,'live_type'=>$live_type])}";      //商品
    var save_url    ="{:url('saveLive')}";
    var index_url   ="{:url('live',['set_type'=>$set_type,'live_type'=>$live_type])}";
    var set_type    ="{$set_type}";
    //已经添加过的商品
    var comm_set_id_arr = [];
    var comm_id = [];
    var action = "add";
    var commodity_dlr_type_selected = 0;

    var ajaxGetCommTypeIdUrl ="{:url('ajaxGetCommTypeId')}";
    var urlCardparam    ="{:url('ajaxGetCard')}" + "?set_type=" + "{$set_type}";
    var user_level_list = {:json_encode($user_level)}


    /**
     * 新增参数验证
     * @type {string}
     */
    var ajaxCommUrl2 = ajaxCommUrl;
    var isInitComm = 1;
    var isInitCommMsg = '';
    //已全选的分页
    var skuConfirmAllCurrentPage = [];
    //当前页所有的comm_set_id值
    currentPageCommSetIds = [];
    //第一个选中的【商品种类】
    var oneCommodityClass = 0;
    var commodityClassJson = {$commodity_class_json};
    //第一个选中的【商品类型】
    var oneCommodityDlrTypeId = 0;
    var commDlrTypeJson = {$comm_dlr_type_json};

    var start_time = '';
    var end_time = '';

    $(".comm-type-search2").on('click',function(){
        var obj = $(this).parents('.form-group');
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();

        ajaxCommUrl = ajaxCommUrl2 + '&commodity_dlr_type_id='+commodity_dlr_type_id;

    });
    $("#sku-confirm-all").on('click',function(){
        var isSkuConfirmAll = $(this).data('is-sku-confirm-all');
        if(isSkuConfirmAll == 0){
            layer.msg('当前页已全部添加，请不要重复操作');
            return false;
        }

        var obj = $(this).parents('.form-group');
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();

        //验证【商品种类】和【商品类型】是否已选择
        if(commodity_class==0){
            layer.msg('请选择商品种类');
            return;
        }
        // if(commodity_dlr_type_id==0){
        //     layer.msg('请选择商品类型');
        //     return;
        // }

        if(oneCommodityClass==0){
            oneCommodityClass = commodity_class;
        }
        if(oneCommodityClass != commodity_class){
            $("#sku-modal").modal('hide');
            var msg1 = "只能添加“"+commodityClassJson[oneCommodityClass]+"“的商品，该商品为“"+commodityClassJson[commodity_class]+"”";
            layer.msg(msg1);
            return;
        }

        // if(oneCommodityDlrTypeId==0){
        //     oneCommodityDlrTypeId = commodity_dlr_type_id;
        // }
        // if(oneCommodityDlrTypeId != commodity_dlr_type_id){
        //     $("#sku-modal").modal('hide');
        //     var msg2 = "只能添加同种类型的商品，该商品为“"+commDlrTypeJson[oneCommodityDlrTypeId]+"“";
        //     layer.msg(msg2);
        //     return;
        // }

        //先请求列表数据，同时进行批量数据操作
        commTypeSearch2(1);
    });
    //活动商品列表搜索
    function commTypeSearch2(isSkuConfirmAll = 0){
        var commodity_class = $("select[name='commodity_class']").val();
        var commodity_dlr_type_id = $("select[name='commodity_dlr_type_id']").val();
        var comm_parent_id = $("select[name='comm_parent_id']").val();
        var page = $("#sku-confirm-all").data("current-page");
        ajaxCommUrl = ajaxCommUrl2 + '&commodity_dlr_type_id='+commodity_dlr_type_id+ '&page='+page;

        //请求公示接口数据
        commTypeSearch(isSkuConfirmAll)
    }

    $(function(){
        // 添加大规则
        $('#btn-addRule2').on('click',function(){
            var l=$('#ruleEdit .item').length;
            if(l<2){
                $('#item-original').after('<div class="item" id="item-original"><div class="row row1"><input type="text" class="form-control">人下单购买享折扣优惠<i href="#modal-del" data-toggle="modal" class="fa fa-2x fa-minus-circle text-danger"></i></div><div class="row row2"><input type="text" class="form-control form-j">件<input type="text" class="form-control form-z" maxlength="3" data-parsley-type="number" data-parsley-range="[0.1,9.9]">折<i class="fa fa-2x fa-plus-circle text-success"></i><span>（如填写9则表示九折，9.9则表示九九折）</span></div></div>')
            }
            return false;
        })
        // 添加小规则
        $(document).on('click','#ruleEdit .fa-plus-circle',function(){
            var l=$(this).closest('.item').find('.row2').length;
            if(l<3){
                $(this).closest('.row').after('<div class="row row2"><input type="text" class="form-control form-j">件<input type="text" class="form-control form-z" maxlength="3" data-parsley-type="number" data-parsley-range="[0.1,9.9]">折<i class="fa fa-2x fa-minus-circle text-danger"></i></div>');
            }
        })
        // 删除大规则
        $(document).on('click','#ruleEdit .row1 .fa-minus-circle',function(){
            $(this).closest('.item').addClass('active_del');
        })
        // 删除小规则
        $(document).on('click','#ruleEdit .row2 .fa-minus-circle',function(){
            $(this).closest('.row').remove();
        })
        // 是否删除弹窗取消按钮
        $('#btn-pop-del-cancel,#btn-pop-del-cancel2').on('click',function(){
            $('.item.active_del').removeClass('active_del')
        })
        // 是否删除弹窗确认按钮
        $('#btn-pop-del').on('click',function(){
            $('.item.active_del').remove()
        })
    })
</script>

<script type="text/javascript">

    var get_data=function(){
        data_l=[];
        $('#ruleEdit').find('.form-control').each(function(){
            if($(this).val()!==''){
                data_l.push($(this).val());
            }
        })
        if(data_l.length!=$('#ruleEdit').find('.form-control').length){
            alert('请填写完整的折扣信息')
        }else{
            var data=[];
            $('#ruleEdit').find('.item').each(function(){
                var data0=[];
                var data1=[];
                data0.push($(this).find('.row1 .form-control').val());
                $(this).find('.row2').each(function(){
                    var data2=[];
                    data2[0]=$(this).find('.form-j').val();
                    data2[1]=$(this).find('.form-z').val();
                    data1.push(data2);
                })
                data0.push(data1);
                data.push(data0);
            })
            return data;
        }
    }


    $("#slt_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        $.get("{:url('ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">选择</option>';
            $("#slt_three_comm_type_id").html(html);
            if(comm_parent_id == 0){
                $("#slt_sub_comm_type_id").html(html);return;
            }
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_sub_comm_type_id").html(html);
        },'json');

    }) ;

    $("#slt_sub_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        if(comm_parent_id == 0){
            var html='<option value="0">请选择</option>';
            $("#slt_three_comm_type_id").html(html);return;
        }
        $.get("{:url('ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">请选择</option>';
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_three_comm_type_id").html(html);
        },'json');
    }) ;

    $("body").on('click','#GWDLR',function(){
        if($("#GWDLR").is(':checked')){
            $('#dealer_select').removeClass('hidden');
        }else{
            $('#dealer_select').addClass('hidden');
        }
    });

    var dlr_data = <?= $dlr_data ?>;

    $("body").on("click",'#dlr_show',function () {
        var select_data = $("#dlr_hide").val().split(',');
        Custom.selectDlr(dlr_data,select_data,function (dlr_code,dlr_name) {
            $("#dlr_show").val(dlr_name.join(','));
            $("#dlr_hide").val(dlr_code.join(','));
        });
    })

    $('.del-commodity').on('click',function (){
        if($("table #haved-commodtiy tr:visible").length > 0){
            var index = layer.open({
                title: ['操作提醒'],
                btn: ['确认', '取消'],
                content:"<div style='font-size: 15px'>您选择对商品列表中全部商品进行移除操作吗?</div>",
                yes: function (res) {
                    $("#haved-commodtiy").empty();
                    $("#sku-confirm-all").data('is-sku-confirm-all',1)
                    initComm(ajaxCommUrl2);
                    commodity_select()
                    comm_id = [];
                    comm_set_id_arr = [];
                    skuConfirmAllCurrentPage = []
                    layer.close(index);
                }
            })
        }

    })

</script>

<script src="__STATIC__admin_v2/js/n_discount_live.js?rand=*******"></script>
<script src="__STATIC__admin_v2/js/commodity_page.js?rand=*******"></script>
{include file="card:act_rule" /}
{include file="card:act_card_rule" /}
{include file="activity:activity_js" /}
{/block}