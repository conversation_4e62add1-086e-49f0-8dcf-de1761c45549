{extend name="public:base_layout" /}

{block name="css"}
    <link href="__STATIC__admin_v2/dndc_file/style.css" rel="stylesheet" />
    <link href="__STATIC__admin_v2/plugins/lightbox/css/lightbox.css" rel="stylesheet" />

    <style>

        .dndc-pop1 {

            z-index: 9999;
        }
    </style>
{/block}

{block name="content"/}

      
<div class="panel-body">
     <div class="alert alert-info fade in m-b-15">
            <h4>操作提示</h4>
            <p>
                •用于发券页的管理与维护；<br/>
                •可通过使用发券页模板，创建个性化发券页；<br/>
                •在创建发券页过程中，可对模板的图片、文案、优惠券等信息进行个性化修改，实现发券页的自助创建与发布。

            </p>
        </div>
    <div class="margin-bottom-10 p-r-0">

        <form class="form search-form" action="" method="get">
            <div class="dndc-form-mod1" id="dndc-form-mod1">
                <div class="form-main">
                   <div class="form-item">
                                <em>发券页名称：</em>
                                <div class="form-wp">
                                    <input type="text" name="title" value="" class="form-control input-sm element" placeholder="请输入发券页名称">
                                </div>
                            </div>
                    <div class="btn-more" id="dndc-btn-more1">
                        更多<i class="fa fa-plus-circle"></i><span class="arrow-spread"></span>
                    </div>
                    <div class="btn-wp">
                        <button type="submit" class=" btn btn-sm btn-success"><i class="fa fa-search"></i> 搜索</button>
                        <input type="hidden" value="0" id="is_down">
                        <div class="btn-left" style="position:absolute;left:0;top:0;">
                        <a href="#addModal" id="a-add-modal" class="btn btn-success btn-sm pull-left m-r-2" data-toggle="modal"> <i class="fa fa-lg fa-plus"></i> 新增发券页 </a>
                    </div>
                    </div>
                </div>

                <div class="form-spread">
                    <ul>
                        <li>
                            <div class="form-item">
                                <em>发券模式：</em>
                                <div class="form-wp">
                                    <select name="send_type" class="form-control input-sm element default-select2">
                                       <?php foreach($send_type_list as $key=>$val):?>
                                           <option value="{$key}" <?php if($key==$send_type) echo 'selected';?>>{$val}</option>
                                       <?php endforeach;?>
                                    </select>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>状态：</em>
                                <div class="form-wp">
                                    <select name="is_enable" class="form-control input-sm element default-select2">
                                        <?php foreach($is_enable_list as $key=>$val):?>
                                            <option value="{$key}" <?php if($key==$is_enable) echo 'selected';?>>{$val}</option>
                                       <?php endforeach;?>
                                    </select>
                                </div>
                            </div>
                        </li>
                        
                        <li></li>
                    </ul>
                </div>
            </div>
        </form>

    </div>

    <div class="table-scrollable">
    <table class="table table-hover table-bordered">
        <thead>
        <tr class="active">
            <th  >序号</th>
            <th>页面名称</th>
            <th>发券模式</th>
            <th>有效期</th>
            <th>可参与专营店</th>
            <th>状态</th>
            <th class="text-center">操作</th>
        </tr>
        </thead>
        <tbody>
            <?php foreach($list as $key=>$vo):?>
                <tr>
                    <td style="width:20px;"><?php echo  get_number($key) ?></td>
                    <td>{$vo.title}</td>
                    <td>{$vo.send_type_name}</td>
                    <td><?php if(!empty($vo['start_time'])):?>{$vo.start_time|date="Y-m-d",###}~{$vo.end_time|date="Y-m-d",###}<?php endif;?></td>
<!--                    <td  style="max-width:200px;word-wrap:break-word;">{$vo.dlr_code}</td>-->
                    <td  style="max-width:200px;word-wrap:break-word;" class="look" data-dlr-code="{$vo.dlr_code}">点击查看</td>
                    <td>{if condition="$vo.is_enable eq 1"}可用{else}不可用{/if}</td>
                    <td  style="width:300px;">
                        <a class="btn btn-primary btn-sm m-r-5 m-t-0" href="{:url('post',array('id'=>$vo['id']))}">编辑</a>
                    <?php if($vo['is_enable']==1 && $vo['is_send']==1 && $vo['send_type']==1):?>
                        <a data-id="{$vo.id}" data-url="{$vo.fixation_url}" class="btn btn-primary qr-code btn-sm m-r-5 m-t-0" href="#">生成二维码</a>
                        <a data-id="{$vo.id}" data-clipboard-text="{$vo.fixation_url}" class="btn btn-primary copy-url btn-sm m-r-5 m-t-0" href="#">复制链接</a>
                    <?php elseif($vo['is_enable']==1 && $vo['send_type']==2):?>
                        <!--<a data-id="$vo.id" data-url="{$vo.send_card_url}" class="btn btn-primary  scan_qr btn-sm m-r-5 m-t-0" href="#">扫码发券</a>-->
                    <?php endif;?>
                    <?php if($vo['send_type'] == 3): ?>
                        <a data-id="show_url" data-url="__STATIC__admin_v2/img/share.png" class="btn btn-primary preview btn-sm m-r-5 m-t-0" href="#">预览</a>
                    <?php else: ?>
                        <a data-id="{$vo.id}" data-url="{:url('active/marketing_card/cards_prv',['card_page_id'=>$vo['id'],'dlr_code'=>$dlr_code],'',true)}" class="btn btn-primary preview btn-sm m-r-5 m-t-0" href="#">预览</a>
                    <?php endif;?>
                    </td>
                </tr>
           <?php endforeach;?>
        </tbody>
    </table>
    </div>

    <div class="row">
        <div class="col-sm-5">
            <div class="dataTables_info" id="data-table_info" role="status" aria-live="polite">
                共查询到{$list->total()} 条数据
            </div>
        </div>
        <div class="col-sm-7">
            <div class="dataTables_paginate paging_simple_numbers" id="data-table_paginate">
               {$page}
            </div>
        </div>
    </div>

<div class="dndc-pop1 dndc-pop2" style="display:none;">
        <div class="shade"></div>
        <div class="main1">
            <div class="tlt">选择发券页模板</div>
            <div class="mod-list">
               <?php foreach($template_list as $key=>$val):?>
                <div class="mod-item">
                    <img src="__STATIC__admin_v2/dndc_file/fq_mod_{$val['value_code']}.jpg">
                    <div class="name">{$val['county_name']}</div>
                    <div class="btn-wp">
                        <div class="p1">
                            <a href="{:url('post',array('theme'=>$val['value_code']))}" class="btn btn-success btn-sm">使用模板</a>
                            <a href="__STATIC__admin_v2/dndc_file/fq_mod_{$val['value_code']}.jpg" data-lightbox="example-{$val['value_code']}" class="btn btn-primary btn-sm btn-view">预览模板</a>
                        </div>
                        <div class="p2">
                            <img src="__STATIC__admin_v2/dndc_file/eg_code.jpg">
                            <span>使用手机微信扫一扫预览</span>
                        </div>
                    </div>
                </div>
                <?php endforeach;?>


            </div>
            <del></del>
        </div>
    </div>

    <div class="dndc-pop1 dndc-pop3" style="display:none;">
        <div class="shade"></div>
        <div class="main1">
            <div class="tlt">专营店列表</div>
<!--            <div class="mod-list">-->
                <textarea class="mod-list" disabled></textarea>
<!--            </div>-->
            <del></del>
        </div>
    </div>



</div>

{/block}

{block name="script"/}

<script src="__STATIC__admin_v2/plugins/lightbox/js/lightbox.min.js?v=2019"></script>
<script src="__STATIC__admin_v2/js/clipboard.min.js?v=2019"></script>
<script>
    $(function(){
        $(".default-select2").select2();
        $(".modal-select2").select2({'width':'100%'});

        //扫码发券
        //$(".scan_qr")

        //复制链接 生成二维码
        $(".qr-code,.scan_qr,.preview").on('click',function(){
           var url=$(this).data('url');
           var id=$(this).data('id');
            $('#output').empty();
            if (id == 'show_url'){
                $('#output').html('<img src="'+url+'" alt="">');
            }else{
                $('#output').qrcode(url);
            }
            if($(this).hasClass("qr-code")){
                $(" #commodity-qrc-modal .modal-title").html("生成二维码");

            }else  if($(this).hasClass("preview")){
                $(" #commodity-qrc-modal .modal-title").html("预览效果");
            }else{
                $(" #commodity-qrc-modal .modal-title").html("扫码发券");
            }
            $("#commodity-qrc-modal").modal("show");

        })
        var clipboard = new Clipboard('.copy-url');
        clipboard.on('success', function(e) {
            console.log(e);
            layer.msg('复制成功')
        });
        clipboard.on('error', function(e) {
            console.log(e);
            layer.msg('复制失败，请联系管理员');
        });





        $('#a-add-modal').on('click',function(){
            $('.dndc-pop2').show();
        });
        $('del').on('click',function(){
            $(this).closest('.dndc-pop2').fadeOut();
        })

        $('.look').on('click',function(){
            var dlr_code = $(this).attr('data-dlr-code');
            $('.dndc-pop3 .main1 .mod-list').text(dlr_code);
            $('.dndc-pop3').show();
        });


    });
</script>
<script>

    $("[data-toggle='tooltip']").tooltip({'placement':'bottom'});

</script>

{/block}
