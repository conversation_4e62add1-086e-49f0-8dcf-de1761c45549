{extend name="public:base_layout" /}

{block name="css"/}
<link href="__STATIC__admin_v2/plugins/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/css/bootstrap-datetimepicker.min.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/css/style_commodity_type.css" rel="stylesheet"/>
<link href="__STATIC__admin_v2/css/jBootsrapPage.css" rel="stylesheet"/>
<style>
    .form-group dd{
        margin-top: 5px;
    }

    .form-group .col-md-10 .col-md-9  dd{
        margin-left: 500px;
    }

    .cover-image{
        width: 40px;
        height: 40px;
        margin-right: 10px;
        margin-left: 20px;
    }

    .level_name_box{
        width: 90px;float: left;text-align: center;padding-top: 5px;
        margin-left:20px;
    }
    .level_name_item_box{
        float:left;width: 70px
    }
    .btnc{
        text-align: center;padding: 10px 0 0 10px;float:left;
    }
    .stylelt{
        /*width: 74px;*/
        float: left;text-align: center;
    }
    .level_name_box_1{
        height:60px;
        width:155%;
    }
    .level_name_box_2{
        height:60px;
        width:134%;
    }
    .radio, .radio-inline{
        line-height:unset !important;
    }
    .level_item_level .user_level{
        margin-left:0 !important;
    }
    .level_item_level{
        width: 155%;
    }
    .level_zhe_box >div{
        width:65px;
        margin-right:10px;
        float:left;
    }
    .level_zhe_car >div{
        width:65px;
        margin-right:10px;
        float:left;
    }
    .level_jian{width:6%}
    .level_pi{ width:15%}
    .level_zhe_box{width:60%}
    .level_zhe_car{width: 30%}

</style>
{/block}

{block name="content"/}

<div class="panel-body">

    <div class="alert alert-danger m-b-8 m-t-10" style="display: none;">
        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
        <p></p>
    </div>
    <div class="well m-t-10">
        <legend class="pull-left width-full">新增-秒杀</legend>
        <form id="fight-form"class="form-horizontal form-bordered" data-parsley-trigger="change">
            <div class="form-group">
                <label class="control-label col-md-2">活动名称<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" name="title" placeholder="请输入活动名称" class="form-control " data-parsley-required="true" data-parsley-length="[1, 15]">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">起止时间<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10">
                    <div class="row  col-md-12">
                        <label class="radio-inline">
                            <input type="radio"  checked name="seckill_type" id="set-time"  value="1" data-parsley-required="true" data-parsley-multiple="discount_type" /> 固定时间
                        </label>

                        <label class="radio-inline">
                            <input type="radio" name="seckill_type" id="repetition-time" value="2" data-parsley-required="true" data-parsley-multiple="discount_type"/> 每天重复
                        </label>
                    </div>

                    <div class="col-md-9 set-time" >
                        <div style="float:left">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">活动时间段:</label>
                            <div class="col-md-3 p-l-0" style="">
                                <input type="text" name="start_time" id="start_time" placeholder="请输入开始时间" class="form-control datetimepicker3" data-parsley-required="true" >
                            </div>

                            <div class="col-md-3">
                                <input type="text" name="end_time" id="end_time" placeholder="请输入结束时间" class="form-control datetimepicker3" data-parsley-required="true" >
                            </div>
                            <dd style="color:red">*操作提示：请选择活动时间再添加商品，若更改活动时间，已添加的商品列表会被清空！</dd>
                        </div>
                    </div>

                    <div class="col-md-9 repetition-time" style="display: none">
                        <div style="float:left">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">持续时间段:</label>
                            <div class="col-md-3 p-l-0" style="">
                                <input type="text" name="repetition_start_time" id="repetition_start_time" placeholder="请输入开始时间" class="form-control daypicker3" data-parsley-required="true" >
                            </div>

                            <div class="col-md-3">
                                <input type="text" name="repetition_end_time" id="repetition_end_time"  placeholder="请输入结束时间" class="form-control daypicker3" data-parsley-required="true" >
                            </div>
                            <dd style="color:red">*操作提示：请选择活动时间再添加商品，若更改活动时间，已添加的商品列表会被清空！</dd>
                        </div>
                    </div>

                    <div class="col-md-9 repetition-time" style="display: none">
                        <div style="float:left">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">每天开始结束时间段:</label>
                            <div class="col-md-3 p-l-0" style="">
                                <input type="text" name="day_start_time"  placeholder="请输入开始时间" class="form-control timepicker3" data-parsley-required="true" >
                            </div>

                            <div class="col-md-3">
                                <input type="text" name="day_end_time"  placeholder="请输入结束时间" class="form-control timepicker3" data-parsley-required="true" >
                            </div>
                            <dd style="color:red">*操作提示：请选择活动时间再添加商品，若更改活动时间，已添加的商品列表会被清空！</dd>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">促销标签:</label>
                <div class="col-md-5" style="width: 70%;">
                    <input type="text" name="tag"  style="width:200px;background-color: #fff;background-image: none;
    border: 1px solid #ccc;border-radius: 4px;box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset; color: #555;font-size: 14px;height: 34px;line-height: 1.42857;padding: 6px 12px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;border-radius: 3px;box-shadow: none; font-size: 12px;" maxlength="4"/>
                    (促销标签是商家对促销动作的别名操作，用于前台显示，最多可输入4个字符，非必填选项）
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">定向人群:</label>
                <div class="col-md-9">
                    <dd>未选择定向人群则代表所有用户可参与活动。选择定向人群后若更改人群类型，已添加的商品列表会被清空！活动开始后不可修改</dd>
                    <?php if ($set_type != 6) :?>
                        <label class="radio-inline">
                            <input type="radio" name="user_segment" class="user_segment" value="1"   data-parsley-multiple="dis_type" /> 会员
                        </label>
                    <?php endif;?>

                    <label class="radio-inline">
                        <input type="radio" name="user_segment" class="user_segment" value="2"  data-parsley-multiple="dis_type"/> 车主
                    </label>
                    <div style="margin-top: 30px;display: none;" id="user_level_box">
                        <dd>可参与的会员等级，若选择了某个会员等级后，该等级及更高等级的会员都有活动资格，高等级用户优惠力度需大于等于低等级用户。</dd>
                        <?php foreach($user_level as $key=>$val):?>
                            <label class="radio-inline">
                                <?php if ($val['value_code'] == 'NONE'):?>
                                    <input type="radio" name="user_segment_options" class="user_segment_options" checked  value="{$val['value_code']}" id="{$val['order_no']}" data-parsley-required="true" title="{$val['county_name']}" data-parsley-multiple="dis_type"/>{$val['county_name']}
                                <?php else:?>
                                <input type="radio" name="user_segment_options" class="user_segment_options"  value="{$val['value_code']}" id="{$val['order_no']}" data-parsley-required="true" title="{$val['county_name']}" data-parsley-multiple="dis_type"/>{$val['county_name']}
                                <?php endif;?>

                            </label>
                        <?php endforeach;?>

                    </div>
                </div>
            </div>



            <div class="form-group"  >
                <label class="control-label col-md-2">限购数量:</label>
                <div  class="col-md-5" id="purchase_number">
                    <div class="input-group col-md-12" >
                        <input type="text" data-parsley-errors-container="#purchase_number" name="purchase_number" class="form-control  col-md-3"   data-parsley-type="integer"  data-parsley-min="0"  >
                        <span class="input-group-addon" >件／人</span>
                    </div>
                    <dd>如果设置为0或不设置则被视为不限制购买数量</dd>
                </div>
            </div>


            <div class="form-group" style="display: none" id="purchase">
                <label class="control-label col-md-2">限购范围<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10">
                    <div class="row col-md-9">
                        <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品范围:</label>
                        <div class="row  col-md-12" >
                            <label class="radio-inline">
                                <input type="radio"  checked name="purchase_commodity_scope" id="purchase_commodity_scope_1"  value="1" data-parsley-required="true" /> 参加活动的单个商品
                            </label>

                            <label class="radio-inline">
                                <input type="radio" name="purchase_commodity_scope" value="2" id="purchase_commodity_scope_2" data-parsley-required="true" /> 参加活动的所有商品
                            </label>
                        </div>
                    </div>

                    <div class="row col-md-9">
                        <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">活动范围:</label>
                        <div class="row  col-md-12" >
                            <label class="radio-inline">
                                <input type="radio"  checked name="purchase_activity_scope" id="purchase_activity_scope_1"  value="1" data-parsley-required="true"  /> 单场
                            </label>

                            <label class="radio-inline">
                                <input type="radio" name="purchase_activity_scope" id="purchase_activity_scope_2"  value="2" data-parsley-required="true" /> 本活动的所有场次
                            </label>
                        </div>
                    </div>
                </div>

            </div>

            <div class="form-group">
                <label class="control-label col-md-2">秒杀价规则:</label>
                <div  class="col-md-8" id="discount">
                    <div class="input-group col-md-12" >
                        <div class="row  col-md-12">
                            <label class="radio-inline">
                                <input type="radio" name="dis_type" value="1" checked id="radio-required1"  data-parsley-multiple="dis_type" /> 限时折扣
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="dis_type" id="radio-required2" value="2" data-parsley-multiple="dis_type"/> 限时立减
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="dis_type" id="radio-required3" value="3" data-parsley-multiple="dis_type"/> 固定秒杀价
                            </label>
                        </div>
                        <div id="rule_old">
                            <div class="row" style="margin-top: 12px;" id="dis_1">
                                <div class="input-group col-md-4" style="float:left;">
                                    <input type="text" data-parsley-errors-container="#discount" name="discount" class="form-control col-md-3" maxlength="3" data-parsley-required="true" data-parsley-type="number"  data-parsley-range="[0.1,9.9]" >
                                    <span class="input-group-addon" >折</span>
                                </div>
                                <div class="tip col-md-8" style="float: right;height: 32px; line-height: 32px">如填写9则表示九折，9.9则表示九九折</div>
                            </div>
                            <div class="row" style="margin-top: 12px;display: none" id="dis_2">
                                <div class="input-group col-md-4" style="float:left;">
                                    <input  type="text" class="form-control set-price default-price" name="dis_money" value="" data-parsley-required="true" data-parsley-errors-container="#valid-dis_money" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">
                                    <span class="input-group-addon" >元</span>
                                </div>
                                <div class="tip col-md-8" style="float: right;height: 32px; line-height: 32px">立减金额</div>
                            </div>
                            <div class="row" style="margin-top: 12px;display: none " id="dis_3">
                                <div class="input-group col-md-4" style="float:left;">
                                    <input  type="text" class="form-control set-price default-price" name="seckill_money" value="" data-parsley-required="true" data-parsley-errors-container="#valid-dis_money" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">
                                    <span class="input-group-addon" >元</span>
                                </div>
                                <div class="tip col-md-8" style="float: right;height: 32px; line-height: 32px;color:red">*操作提示：您选择的是固定秒杀价，所有sku都统一价格，请确保价格是否合理</div>
                            </div>
                        </div>
                        <div id="rule_new" style="display: none">
                            <div  class="col-md-8" id="discount">
                                <div class="input-group col-md-12">
                                    <!--会员折扣-->
                                    <div id="member_box" class="limit_boxs">
                                        <div class="row dis_1 slope-dis"  style="margin-top: 12px;">
                                            <div class="rule_new_box">
                                                <div class="level_item_level" style="height:60px">
                                                    <div class="level_name_box stylelt level_pi" style="color:transparent">占</div>
                                                    <div class="level_zhe_box stylelt " id="county_name">
                                                        <?php foreach($user_level as $key=>$val):?>
                                                            <div class="stylelt user_level county_name" data-title="{$val['county_name']}" data-code="{$val['value_code']}">{$val['county_name']}</div>
                                                        <?php endforeach;?>
                                                    </div>
                                                </div>
                                                <div class="level_item level_name_box_1">
                                                    <div class="level_name_box stylelt level_pi">
                                                        <div class="level_name_item_box" ><a href="#" data-type="text" class="btn btn-primary btn-sm editable editable-click batch_price" data-value="" data-placeholder="批量设置" data-title="批量设置">批量设置<i class="fa fa-edit m-l-5"></i></a></div>
                                                    </div>
                                                    <div class="level_zhe_box stylelt" id="value_code">
                                                        <?php foreach($user_level as $key=>$val):?>
                                                            <div class="stylelt level_jian form-z value_code">
                                                                <span class="c_dis_type_1"></span><input size="4" class="v_dis_type" data-parsley-required="true" value_code="{$val['value_code']}"  /><span class="c_dis_type">折</span>
                                                            </div>
                                                        <?php endforeach;?>
                                                    </div>

<!--                                                    <div class="btnc level_btn"><i class="fa fa-2x fa-plus-circle text-success adbtn"></i></div>-->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--车主折扣-->
                                    <div id="car_onwer_box" class="limit_boxs ">
                                        <div class="row dis_1 slope-dis"  style="margin-top: 12px;">
                                            <div class="rule_new_box">
                                                <div class="level_item_level" style="height:60px">
                                                    <div class="level_name_box stylelt level_pi" style="color:transparent">占</div>
                                                    <div class="level_zhe_car stylelt">
                                                        <div class="stylelt" >非车主</div>
                                                        <?php if ($set_type == 6):?>
                                                            <div  class="stylelt ">日产车主</div>
                                                            <div  class="stylelt ">Ariya车主</div>

                                                        <?php else:?>
                                                            <div  class="stylelt ">车主</div>
                                                        <?php endif;?>
                                                    </div>
                                                </div>
                                                <div class="level_item level_name_box_2">
                                                    <div class="level_name_box stylelt level_pi">
                                                        <div class="level_name_item_box" ><a href="#" data-type="text" class="btn btn-primary btn-sm editable editable-click car_batch_price" data-value="" data-placeholder="批量设置" data-title="批量设置">批量设置<i class="fa fa-edit m-l-5"></i></a></div>
                                                    </div>
                                                    <div class="level_zhe_car stylelt">
                                                        <div class="stylelt"><span class="c_dis_type_1"></span><input size="4"  class="carv_dis_type carv_dis_type_1"/><span class="c_dis_type">折</span></div>
                                                        <div class="stylelt"><span class="c_dis_type_1"></span><input size="4" data-parsley-required="true" class="carv_dis_type carv_dis_type_2"/><span class="c_dis_type">折</span></div>

                                                        <?php if ($set_type == 6):?>
                                                            <div class="stylelt"><span class="c_dis_type_1"></span><input size="4" data-parsley-required="true" class="carv_dis_type carv_dis_type_3"/><span class="c_dis_type">折</span></div>
                                                        <?php endif;?>
                                                    </div>
<!--                                                    <div class="btnc"><i class="fa fa-2x fa-plus-circle text-success adbtn2"></i></div>-->
                                                </div>
                                                <dd>若未设置非车主价则非车主无参与资格，车主的优惠力度需大于等于非车主</dd>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
            {include file="activity:activity_img" /}
            <div class="form-group"  >
                <label class="control-label col-md-2">业务归属:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <select name="gather_id" class="form-control input-sm ">
                            <option value=""> 全部 </option>
                            {volist name="$gather_list" id="val" key="key"}
                            <option value="{$val['id']}" {if($Think.get.gather_id == $val['id'])}selected{/if} > {$val['name']} </option>
                            {/volist}
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">主题活动:</label>
                <div  class="col-md-5" id="is_enable">
                    <input type="text" name="theme_name" id="theme_name" placeholder="请输入主题活动" value="" class="form-control" data-parsley-length="[0, 100]">
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">是否PV补贴:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_pv_subsidy" type="checkbox" data-input-name="is_pv_subsidy" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭" />
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group" id="new_discount">
                <label class="control-label col-md-2">优惠类型<i class="m-r-3 text-danger">*</i>:</label>
                <div  class="col-md-8" id="discount_type">
                    <div class="input-group col-md-12" >
                        <div class="row  col-md-12">
                            <label class="radio-inline">
                                <input type="radio" checked name="discount_type" id="discount_type1" value="1" data-parsley-required="true" data-parsley-multiple="discount_type" /> 仅商品
                            </label>
                            <!--                            <label class="radio-inline">-->
                            <!--                                <input type="radio" name="discount_type" id="discount_type2" value="2" data-parsley-required="true" data-parsley-multiple="discount_type"/> 仅商品工时-->
                            <!--                            </label>-->
                            <label class="radio-inline" id="discount_type3">
                                <input type="radio" name="discount_type" value="3" data-parsley-required="true" data-parsley-multiple="discount_type"/> 商品+工时
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group" style="display: none">
                <label class="control-label col-md-2">活动描述:</label>
                <div class="col-md-5">
                    <input type="text" name="des" id="des" placeholder="请输入活动描述" value="" class="form-control" data-parsley-length="[0, 30]">
                    <dd>活动描述是商家对秒杀活动的补充说明文字，用于前台显示，最多可输入30个字符，非必填选项</dd>
                </div>
            </div>

            <div class="form-group" id="up_down">
                <label class="control-label col-md-2">活动渠道:<i class="m-r-3 text-danger">*</i></label>
                <div class="col-md-10">
                    <div>
                        <?php foreach ($up_down_channel as $key => $val): ?>
                            <?php if($live_type == 1):  if (in_array($key,['PZ1AAPP','PZ1ASM'])):?>
                                <label class="checkbox-inline">
                                    <input type="checkbox"
                                           name="up_down_channel[]" data-parsley-required="true"
                                           value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                </label>
                            <?php endif; else:?>
                                <?php if($live_type == 2):  if (in_array($key,['QCAPP','QCSM'])):?>
                                    <label class="checkbox-inline">
                                        <input type="checkbox"
                                               name="up_down_channel[]" data-parsley-required="true"
                                               value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                    </label>
                                <?php endif; else:?>
                                    <?php if(!in_array($key,['PZ1AAPP','PZ1ASM','QCAPP','QCSM','TOBPC','GWNET','GWDLR'])):?>
                                        <label class="checkbox-inline">
                                            <input type="checkbox"
                                                   name="up_down_channel[]" data-parsley-required="true"
                                                   value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                        </label>
                                    <?php endif; endif; endif; endforeach; ?>
                    </div>
                    <div style="margin-top:20px;" class="hidden" id="dealer_select">
                        <input id="dlr_show" type="text" class="form-control width-300" value="" placeholder="请点击选择经销商"
                               data-parsley-required="true">
                        <input id="dlr_hide" type="text" name="dlr_code" class="hidden" value="">
                    </div>
                </div>
            </div>


            <div class="form-group ">
                <label class="col-md-2 control-label" id="settlement_rule">关联结算规则:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control settlement_rule" name="settlement_rule" id="template_name" placeholder="请选择选择结算规则" data-parsley-group="wizard-step-1" >
                    <input type="hidden" id="settlement_rule_id" name="settlement_rule_id">
                    <input type="hidden" id="settlement_rule_value" name="settlement_rule_value">
                    <input type="hidden" id="settlement_rule_type" name="settlement_rule_type">
                    <input type="hidden" id="act_sett_standard" name="act_sett_standard">
                </div>
            </div>




            <div class="form-group"  >
                <label class="control-label col-md-2">活动状态:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_enable" type="checkbox" data-input-name="is_enable" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭" checked />
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">是否叠加券:</label>
                <div  class="col-md-5" id="card_available">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_card_change" type="checkbox"  name="switch" data-input-name="card_available"
                                   data-input-true="1" data-input-false="0"
                                   data-size="small" data-on-text="可用" data-off-text="不可用" checked />
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group no_card ">
                <label class="col-md-2 control-label" id="card_activity">选择可叠加的券:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control card_activity" name="card_activity" placeholder="点击选择" data-parsley-group="wizard-step-1" id="text-a" data-id="" value="">
                    <input type="hidden" class="form-control hide_card_ids" name="rel_card_ids" >
                </div>
                <div class="bottom-wp">
                    <div class="bottom-left">
                        已选<input type="" class="num_card_ids" style="width: 20px;outline:none;border: 0; readonly " value="0">
                        张
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">活动商品<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10">
                    <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                        <div style="float:left">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品分类:</label>
                            <select class="form-control input-sm element default-select2 col-md-2" name="comm_parent_id" id="slt_comm_type_id">
                                <option value="0">
                                    请选择
                                </option>
                                <?php foreach($comm_parent_list as $key=>$val):?>
                                    <option value="{$val['id']}" >{$val['comm_type_name']}</option>
                                <?php endforeach;?>
                            </select>
                            <select name="sub_comm_type_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id">
                                <option value="0">
                                    请选择
                                </option>
                            </select>
                            <select name="three_comm_type_id" class="form-control input-sm element default-select2" id="slt_three_comm_type_id">
                                <option value="0">
                                    请选择
                                </option>
                            </select>
                        </div>

                    </div>
                    <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                        <div style="float:left">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品种类:</label>
                            <select class="form-control input-sm element default-select2 col-md-2"
                                    name="commodity_class" id="slt_comm_type_id">
                                <option value="0">
                                    请选择
                                </option>
                                <?php foreach($commodity_class as $key=>$val):?>
                                    <option value="{$key}" >{$val}</option>
                                <?php endforeach;?>
                            </select>
                        </div>
                        <div style="float:right;">
                            <label class="control-label col-md-2" style="padding:  5px 5px;  width: 90px;">商品名称:</label>
                            <input type="text" class="form-control input-sm element col-md-3 m-r-2 width-200" name="commodity_name" placeholder="请输入商品名称">
                            <button id="comm-type-search" type="button" class="btn btn-sm btn-success"><i class="fa fa-search"></i>搜索</button>
                        </div>

                    </div>
                    <div>
                        <div class="table-scrollable" >
                            <table id="" class="table table-hover">
                                <thead>
                                <th class="text-center">商品规格</th>
                                <th class="text-center">上架渠道</th>
                                <th class="text-center">原价</th>
                                <th class="text-center">库存(组合商品以实际库存为准)</th>
                                <th class="text-center">操作</th>
                                </thead>
                                <tbody id="add-comm-tbody">

                                </tbody>
                            </table>
                        </div>
                        <div>
                            <ul class="pagination" id="comm-pagination"></ul>
                        </div>
                    </div>

                    <legend class="pull-left width-full" style="font-size: 18px;">已选活动商品</legend>

                    <div>
                        <div class="table">
                            <table id="" class="table">
                                <thead></thead>

                                <tbody id="haved-commodtiy">

                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2"></label>
                <div class="col-md-5">
                    <input type="hidden" name="set_type" value="{$set_type}">
                    <a href="javascript:;" class="btn btn-sm btn-primary btn-sm"  id="put-form">确认</a>
                </div>
            </div>
        </form>

    </div>

    <!----begin 查看规格---->
    <div class="modal fade" id="sku-modal" data-comm-id="" data-comm-set-id="" data-type="">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close look-close" data-dismiss="modal"  aria-hidden="true">×</button>
                    <h4 class="modal-title">选择商品: <span id="comm-name-header"></span></h4>
                </div>
                <input id="modal_home"  type="hidden" value="" />
                <div class="modal-body" style="">
<!--                    <div class="alert alert-info m-b-0" style="padding: 8px;padding-top: 5px;">-->
<!--                        <h6>操作提示</h6>-->
<!--                        <p>默认该商品全部规格选中参加该项促销活动,如某个规格不想参与该项活动,可通过取消选择排除掉;被排除的规格将保持原价</p>-->
<!--                    </div>-->
<!--                    <d></d><br>-->

                    <div class="form-group col-md-12">
                        <div class="col-md-4" style="display:none">
                            <label class="control-label col-md-3" style="padding-left: 11px; padding-top: 10px; width: 500px;" id="dis_t_word">折扣价设置:</label>
                            <div class="col-md-12" id="comm_discount">
                                <div class="input-group col-md-12" >
                                    <input type="text" data-parsley-errors-container="#comm_discount" name="comm_discount" readonly="readonly" class="form-control col-md-3" maxlength="3" data-parsley-type="number"  data-parsley-range="[0.1,9.9]">
                                    <span class="input-group-addon" id="dis_con_word">折</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-md-10">
                            <label class="control-label col-md-3" style="padding-left: 11px; padding-top: 10px; width: 500px;" id="dis_t_word">秒杀库存:</label>
                            <div class="col-md-12" id="sku_stock">
                                <div class="input-group col-md-10" >
                                    <input type="number" data-parsley-errors-container="#sku_stock" name="sku_stock" class="form-control col-md-3" data-parsley-type="number" placeholder="只能填写1-99999的正整数,不得大于原始商品库存"  data-parsley-required="true" data-parsley-range="[1,99999]">
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="hide">
                        <div class="sku-image" style="display: inline-block;">
                            <img class=" cover-image" src="">
                        </div>
                        <div style="display: inline-block;">
                            <div  class="sku-comm">
                            </div>
                        </div>
                    </div>

                    <table class="table table-hover" style="display:">
                        <thead>
                        <tr>
                            <th class="col-md-6" >商品规格</th>
                            <th >原价</th>
<!--                            <th style="text-align: center">-->
<!--                                价格-->
<!--                            </th>-->
                            <th>库存</th>
                        </tr>
                        </thead>
                        <tbody class="sku-tb">

                        </tbody>
                    </table>
                </div>

                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white look-close"  data-dismiss="modal" >关闭</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary  " id="sku-confirm" >确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 删除设置---->
    <div class="modal fade" id="del-sku-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">操作提醒</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger m-b-0">
                        <h4><i class="fa fa-info-circle"></i>确定删除该商品?</h4>
                        <p></p>
                    </div>
                </div>
                <input type="hidden" id="del-data-id" data-comm-set-id="" value="">
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="del-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>


</div>
{/block}
{block name="script"/}

<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/moment-with-locales.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/bootstrap-datetimepicker.min.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/fileinput.min.js"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js"></script>
<script src="__STATIC__admin_v2/js/seckill.js?rand=2.0.28"></script>


<script>

    var urlCardparam    ="{:url('ajaxGetCard')}" + "?set_type=" + "{$set_type}";
    var setType = "{$set_type}"
    var user_level_list = {:json_encode($user_level)}


    $("#slt_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        $.get("ajaxGetCommTypeId",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">选择</option>';
            $("#slt_three_comm_type_id").html(html);
            if(comm_parent_id == 0){
                $("#slt_sub_comm_type_id").html(html);return;
            }
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_sub_comm_type_id").html(html);
        },'json');
    }) ;
    var dis_type=1;
    var discount_type=1;
    var dis_t_word = '秒杀设置:';
    $("#radio-required2").on('change',function () {
        var user_segment = $("*[name='user_segment']:checked").val();
        console.log('user_segment:',user_segment)
        $("#new_discount").show();
        var check = $(this).attr("checked");
        if (user_segment == undefined) {
            if(check){
                dis_type=2;
                dis_t_word = "立减：";
                $("#dis_t_word").text(dis_t_word);
                $("#dis_con_word").text("元");
                $("#dis_3").hide();
                $("#dis_2").show();
                $("#dis_1").hide();
                $('#discount_type3').hide();
                $('#discount_type1').attr('checked', 'checked');
            }else{
                $("#dis_3").hide();
                $("#dis_2").hide();
                $("#dis_1").show();
            }
            $("#dis_t_word").show();
            $("#comm_discount").show();
        } else {
            if(check){
                $('.c_dis_type_1').text('立减');
                $('.c_dis_type').text('元');
            }
        }

    })
    $("#radio-required1").on('change',function () {
        var user_segment = $("*[name='user_segment']:checked").val();
        console.log('user_segment:',user_segment)
        $("#new_discount").show();
        var check = $(this).attr("checked");
        if (user_segment == undefined) {
            if(check){
                dis_type=1;
                dis_t_word = "折扣价设置:";
                $("#dis_t_word").text(dis_t_word);
                $("#dis_con_word").text("折");
                $("#dis_3").hide();
                $("#dis_2").hide();
                $("#dis_1").show();
                $("#dis_t_word").show();
            }else{
                $("#dis_2").show();
                $("#dis_1").hide();
                $("#dis_3").hide();
            }
            $("#comm_discount").show();
            $('#discount_type3').show();
        } else {
            if(check){
                $('.c_dis_type_1').text('');
                $('.c_dis_type').text('折');
            }
        }

    });
    $("#radio-required3").on('change',function () {
        var user_segment = $("*[name='user_segment']:checked").val();
        console.log('user_segment:',user_segment)
        $("#new_discount").show();
        var check = $(this).attr("checked");
        if (user_segment == undefined) {
            if(check){
                dis_type=3;
                dis_t_word = "固定秒杀价:";
                $("#dis_t_word").text(dis_t_word);
                $("#dis_con_word").text("元");
                $("#dis_2").hide();
                $("#dis_1").hide();
                $("#dis_3").show();
                $('#discount_type3').hide();
                $("#comm_discount").show();
            }
        } else {
            if (check) {
                $('.c_dis_type_1').text('');
                $('.c_dis_type').text('元');
            }
        }
    })

    $("#discount_type1").on('change',function () {discount_type=1})
    $("#discount_type2").on('change',function () {discount_type=2})
    $("#discount_type3").on('change',function () {discount_type=3})

    $("#slt_sub_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        if(comm_parent_id == 0){
            var html='<option value="0">请选择</option>';
            $("#slt_three_comm_type_id").html(html);return;
        }
        $.get("ajaxGetCommTypeId",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">请选择</option>';
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_three_comm_type_id").html(html);
        },'json');
    }) ;
    var admin_type="{$admin_type}";
    var getSkuUrl ="{:url('getSkuLive')}";
    var limit_discount_id=0;
    var getDlr_url="{:url('ajaxGetDlr')}";
    //异步请求数据地址
    var ajaxCommUrl = "{:url('ajaxGetLiveCommodityList',['pagesize'=>5,'set_type'=>$set_type,'live_type'=>$live_type])}";      //商品
    var save_url    ="{:url('saveLive')}";
    var index_url   ="{:url('live',['set_type'=>$set_type,'live_type'=>$live_type])}";
    var set_type    ="{$set_type}";

    $("body").on('click','#GWDLR',function(){
        if($("#GWDLR").is(':checked')){
            $('#dealer_select').removeClass('hidden');
        }else{
            $('#dealer_select').addClass('hidden');
        }
    });

    var dlr_data = <?= $dlr_data ?>;

    $("body").on("click",'#dlr_show',function () {
        var select_data = $("#dlr_hide").val().split(',');
        Custom.selectDlr(dlr_data,select_data,function (dlr_code,dlr_name) {
            $("#dlr_show").val(dlr_name.join(','));
            $("#dlr_hide").val(dlr_code.join(','));
        });
    })

    var flag = false;
    $("#is_card_change").on('click',function(){
        flag = !flag;
        if(flag) {
            $('.no_card').addClass('hidden');
        } else {
            $('.no_card').removeClass('hidden');
        }
    });

    $('#set-time').click(function () {
        $('.repetition-time').hide();
        $('.set-time').show();
    })

    $('#repetition-time').click(function () {
        $('.repetition-time').show();
        $('.set-time').hide();
    })

    $('#purchase_number').change(function () {
        var purchase_number = $("*[name='purchase_number']").val();;
        if (purchase_number > 0) {
            $('#purchase').show();
        } else {
            $('#purchase').hide();
        }
    })

    $('#purchase_commodity_scope_1').click(function(){
        layer.msg('每个商品分别都可购买X件');
    });
    $('#purchase_commodity_scope_2').click(function(){
        layer.msg('购买任意商品达到X件后不可再买');
    });

    $('#purchase_activity_scope_1').click(function () {
        layer.msg('针对单场活动生效');
    });
    $('#purchase_activity_scope_2').click(function () {
        layer.msg('如果设置了每天重复开始的活动，那么在持续时间短内，购买达到X件，视为达到限购数量，不可在后续的每一场活动中购买');
    });

    // 会员批量设置
    $('.batch_price').editable({
        success: function (response, newValue) {
            $(this).parent().parent().parent().find(".v_dis_type").val(newValue)
            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^[0-9]+(.[0-9]{1,2})?$/).test(value)) {
                return '格式有误';
            }
        }
    });

    // 车主批量设置
    $('.car_batch_price').editable({
        success: function (response, newValue) {
            $(this).parent().parent().parent().find(".carv_dis_type").val(newValue)
            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^[0-9]+(.[0-9]{1,2})?$/).test(value)) {
                return '格式有误';
            }
        }
    });
    initComm(ajaxCommUrl);





</script>
{include file="card:act_rule" /}
{include file="card:act_card_rule" /}
{include file="activity:activity_js" /}
{/block}