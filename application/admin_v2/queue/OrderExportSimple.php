<?php

namespace app\admin_v2\queue;

use app\common\model\bu\BuOrder;
use app\common\model\db\DbExports;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbCrowdfund;
use app\common\model\db\DbSystemValue;
use app\common\model\act\AcHaveTradeList;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\db\DbCard;
use think\queue\Job;
use tool\Logger;

class OrderExportSimple extends Base
{
    public function fire(Job $job, $data)
    {
        try {
            ini_set('memory_limit', '2200M');
            set_time_limit(0);
            $info = json_decode($data, true);
            $id = $info['id'];
            $params = $info['params'];
            $fields = $info['fields'] ?? '';
            $titles = $info['titles'] ?? '';
            $order_source = $info['order_source'] ?? 0;
            $titles = '序号,订单编码,总订单号,老支付单号,新支付单号,结算单号,订单商户号,订单金额,订单总价,实付金额,订单实付积分,订单运费,卡券总优惠金额,活动总优惠金额,退款金额,专营店,姓名,手机号码,vin,下单vin,车牌,专营店积分,商城积分,卡券名称,卡劵code,下单时间,支付时间,核销时间,到期时间,订单状态,购买卡券状态,物流方式,商品图片,组合商品名称,商品名称,一级分类,二级分类,三级分类,是否纯正,车辆信息,sku信息,商品编码,规格编码,商品属性,销售来源,渠道来源,订单来源,收货地址,赠送积分,支付方式,积分卡券状态,原价,活动价,数量,总价,活动优惠总金额,商品活动优惠金额,商品卡券优惠金额,商品实付金额,商品实付积分,商品运费,是否线上支付工时,商品工时编码,商品工时原价,商品工时实付金额,商品工时优惠金额,供应商订单号,供应商运费,供应商,税务编码,税率,成本价,分账比例,预售活动,限时优惠,N件N折,满减,买赠活动,是否赠品,秒杀活动,用户ID,扣积分单号,首单立减,移动积分,商品状态,承运公司,运单编号,发货时间,前台一级分类,前台二级分类,前台三级分类,会员价,会员等级,取送车数量,众筹活动,支付前积分余额,工会手机号,卡券结算规则,活动结算规则,卡券结算价格标准值,活动结算价格标准值,订单厂家卡券补贴金额,商品厂家卡券补贴金额,订单厂家活动补贴金额,商品厂家活动补贴金额,订单厂家总补贴金额,商品厂家总补贴金额,开票状态,开票时间';
            $params['field'] = "a.id,a.payment_method,a.parent_order_code,a.user_id, channel,a.gift_score,a.refund_money,a.shop_integral,t1.comm_type_name,t2.comm_type_name as parent_comm_type_name,t3.comm_type_name as three_comm_type_name,c.is_pure,a.id,a.payment_method,a.cashier_trade_no,a.front_money,a.pre_use_money,a.front_pay_time,a.cashier_trade_no2,a.order_code,a.pay_order_code,a.total_money,a.money,a.name,a.phone,a.vin,a.order_vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.order_status,a.logistics_mode,a.order_source,f.dlr_code,f.dlr_name,b.b_act_price,b.count,b.all_dis,b.card_all_dis,b.pre_sale_id,b.commodity_pic,b.commodity_name,b.car_info,b.sku_info,b.price,b.count,b.price*b.count as total_price,b.actual_price,b.actual_point,b.actual_use_money,c.commodity_code,b.third_sku_code sku_code,(case h.type when 1 then '平台' when 2 then '专营店' when 3 then '官微销售' end) as type,(case b.sales_source when 0 then '旧数据' when 1 then '平台自营销售' when 2 then '专营店销售' when 3 then '官微销售' end) as sales_source,a.receipt_address,a.pay_time,a.mail_price,k.money hmoney,k.preferential_money,k.activity_title full_title,a.is_cc_ok,b.price as old_price,b.limit_id,b.n_dis_id,card_money,n.rule_info,ps.title as presale,b.mail_price goods_mail_price,a.pay_order_code2,a.cashier_trade_no,a.cashier_trade_no2,b.third_sku_code,b.third_order_id,b.tax_code,b.tax,b.supplier,b.cost_price,b.third_mail_price,a.ic_card_no,b.full_id,b.full_dis_money,b.gift_act_id,b.is_gift,g.title as gift_title,se.title as seckill_name";
            $params['field'] .= ',a.ms_order_code,a.ms_order_code2,b.mo_sub_id,a.pre_point,b.work_time_dis,b.work_time_json,b.work_time_money,b.act_sett_money,b.card_sett_money,b.work_time_actual_money,b.mo_id
                        ,(a.b_act_goods_price+a.b_work_time_price+a.mail_price) as total_order_price
                        ,(a.money+a.pre_use_money) as pay_money
                        ,(a.integral+a.pre_point) as total_integral
                        ,point.point_order_code,b.first_order_price,yd.point as yd_point
                        ,b.common_carrier,b.waybill_number,b.delivery_time,b.order_commodity_status,b.id order_commodity_id
                        ,a.card_degree_code,b.commodity_segment_dis_money,b.qsc_num,c.comm_type_id,b.commodity_id,b.crowd_id,a.current_points,b.source_special
                        ,b.card_sett_rule,b.act_sett_rule,b.card_sett_standard,b.act_sett_standard
                         ,invoice.invoice_status,invoice.invoice_time
                        ';
            $params['field'] .= ',b.limit_dis_money,a.delivery_time as verification_time';
            // 多选不处理
            if ($order_source == 6) {
                $params['order_s_q'] = 1;
                $params['field']     .= ",m.emp_name,m.mobile ca_phone,m.emp_no,o.service_charge,(case o.uses when 1 then '自己用' when 2 then '转赠' end) as uses";
            }

            $file_name = 'public/uploads/exports/simple_order_export_' . $id . '.csv';
            DbExports::where('id', $id)->update(['export_status' => 1, 'file_address' => $file_name]);
            $fp = fopen(ROOT_PATH . $file_name, 'a');
            fwrite($fp, chr(0xEF) . chr(0xBB) . chr(0xBF));
            if ($titles) {
                $title_arr = explode(',', $titles);
                fputcsv($fp, $title_arr);
            }
            $page = 1;
            $pageSize = 1000;
            do {
                $orders = $this->getOrdersmPage($params, $page, $pageSize);
                if (empty($orders)) break;
                // 依赖表批量查询
                $limit_id = [];
                $n_dis_id = [];
                $n_dis_id_arr = [];
                $limit_id_arr = [];
                $user_level_arr = [];
                $limit = [];
                $source_special = [];
                $trade_list = [];
                $crowd_id = [];
                $crowd_id_arr = [];
                $crowd_title = [];
                foreach ($orders as $k => $vvv) {
                    if ($vvv['n_dis_id'] && !in_array($vvv['n_dis_id'], $n_dis_id_arr)) {
                        $n_dis_id[]     = $vvv['n_dis_id'];
                        $n_dis_id_arr[] = $vvv['n_dis_id'];
                    }
                    if ($vvv['limit_id'] && !in_array($vvv['limit_id'], $limit_id_arr) && $vvv['limit_dis_money'] != '0.00') {
                        $limit_id[]     = $vvv['limit_id'];
                        $limit_id_arr[] = $vvv['limit_id'];
                    }
                    if (!empty($vvv['crowd_id']) && !in_array($vvv['crowd_id'], $crowd_id_arr)) {
                        $crowd_id[] = $vvv['crowd_id'];
                        $crowd_id_arr[] = $vvv['crowd_id'];
                    }
                    if (!empty($vvv['source_special'])) {
                        $source_special[] = $vvv['source_special'];
                    }
                }
                // N件N折
                $n_dis_arr = [];
                if ($n_dis_id) {
                    $n_dis_model = new DbNDiscount();
                    $n_dis_list  = $n_dis_model->whereIn('id', $n_dis_id)->select();
                    if ($n_dis_list) {
                        foreach ($n_dis_list as $nnn) {
                            $n_dis_arr[$nnn['id']] = "N件N折-" . $nnn['title'];
                        }
                    }
                }
                // 限时优惠
                if($limit_id){
                    $limit_model = new DbLimitDiscount();
                    $limit       = $limit_model->whereIn('id', $limit_id)->column('title', 'id');
                }
                // 众筹
                if (!empty($crowd_id)) {
                    $crowdfund_model = new DbCrowdfund();
                    $crowd_title = $crowdfund_model->whereIn('id', $crowd_id)->column('title','id');
                }
                // 工会手机号
                if (!empty($source_special)) {
                    $trade_model = new AcHaveTradeList();
                    $map = [
                        'sp_id' => ['in',array_unique($source_special)],
                        'is_enable' => 1,
                    ];
                    $field = 'id,phone,sp_id,user_id';
                    $trade_list = $trade_model->where($map)->field($field)->select();
                }
                // 会员等级
                $map = ['is_enable'=>1,'value_type'=>25, 'parent_value_code'=>'N'];
                $user_level = DbSystemValue::where($map)->field('value_code,county_name')->order('order_no')->select();
                foreach ($user_level as $key => $val) {
                    $user_level_arr[$val['value_code']] = $val['county_name'];
                }
                // 业务字段组装
                foreach ($orders as $kkk => &$vvv) {
                    $vvv['limit'] = '';
                    $vvv['n_dis'] = '';
                    $vvv['crowd_title'] = '';
                    $vvv['trade_phone'] = '';
                    $vvv['user_level_price'] = $vvv['price'] - $vvv['commodity_segment_dis_money'];
                    $vvv['card_degree_code'] = $user_level_arr[$vvv['card_degree_code']] ?? '';
                    $vvv['mj_info'] = $vvv['full_title'] ? '满减活动-'.$vvv['full_title'] : '';
                    $vvv['is_gift_name'] = '';
                    if($vvv['gift_act_id'] != 0){
                        $vvv['is_gift_name'] = $vvv['is_gift'] == 1 ? '是' : '否';
                    }
                    if (!empty($vvv['crowd_id'])) {
                        $vvv['crowd_title'] = $crowd_title[$vvv['crowd_id']] ?? '';
                    }
                    if(!empty($vvv['source_special'])) {
                        foreach ($trade_list as $datum) {
                            if ($datum['sp_id'] == $vvv['source_special'] && $vvv['user_id'] == $datum['user_id']) {
                                $vvv['trade_phone'] = $datum['phone'];
                            }
                        }
                    }
                    if ($vvv['limit_id'] && $vvv['limit_dis_money'] != '0.00') {
                        $vvv['limit'] = $limit[$vvv['limit_id']] ?? "活动未明";
                    }
                    if (!empty($vvv['n_dis_id'])) {
                        $vvv['n_dis'] = $n_dis_arr[$vvv['n_dis_id']] ?? "活动未明";
                    }
                    $vvv['web_comm_type_name'] = '';
                    $vvv['web_parent_comm_type_name'] = '';
                    $vvv['web_top_comm_type_name'] = '';
                }
                // 组合商品子商品信息批量查询优化
                $combo_keys = [];
                foreach ($orders as $item) {
                    if ($item['mo_sub_id'] != 0) {
                        $combo_keys[] = [
                            'order_code' => $item['order_code'],
                            'mo_id' => $item['mo_sub_id']
                        ];
                    }
                }
                $combo_map = [];
                if (!empty($combo_keys)) {
                    // 只用 order_code 查询所有子商品
                    $order_codes = array_unique(array_column($combo_keys, 'order_code'));
                    $order_commodity_model = new \app\common\model\bu\BuOrderCommodity();
                    $comboList = $order_commodity_model->where(['order_code'=>['in',$order_codes],'mo_id'=>['<>',0]])->field('order_code,mo_id,count,commodity_name')->select();
                    foreach ($comboList as $row) {
                        $combo_map[$row['order_code'].'_'.$row['mo_id']] = $row;
                    }
                }

                // 组合商品、卡券、金额、状态等组装
                $order_codes = [];
                $card_id_arr = [];
                $act_ids = [];
                $act_user_id = [];
                $card_receive_arr = [];
                foreach ($orders as $key => $val) {
                    $orders[$key]['cashier_settlement_no'] = '';
                    $orders[$key]['cashier_settlement_no2'] = '';
                    $orders[$key]['card_name'] = '';
                    $orders[$key]['card_status'] = '';
                    if (!empty($val['card_id'])) {
                        $cardIdArr = explode(',', $val['card_id']);
                        $card_id_arr = array_merge($card_id_arr, $cardIdArr);
                    }
                    if (in_array($val['order_status'], [2, 7]) && in_array($val['order_source'], [24])){
                        $act_ids[] = $val['id'];
                        $act_user_id[] = $val['user_id'];
                    }
                    $is_first_order = '否';
                    if ($val['first_order_price'] != 0) {
                        $is_first_order = '是';
                    }
                    $orders[$key]['is_first_order'] = $is_first_order;
                    $orders[$key]['dlr'] = $val['dlr_code'] . ' - ' . $val['dlr_name'];
                    $orders[$key]['order_status_name'] = BuOrder::orderStatus($val['order_status']);
                    $orders[$key]['order_commodity_status_name'] = \app\common\model\bu\BuOrderCommodity::$order_commodity_status[$val['order_commodity_status']] ?? $orders[$key]['order_status_name'];
                    $orders[$key]['logistics_mode_name'] = $val['logistics_mode'] == 1 ? '自提' : '快递';
                    $orders[$key]['is_pure'] = empty($val['is_pure']) ? '否' : '是';
                    $workTimeDis =  ($val['mo_sub_id'] != 0) ? $val['work_time_dis'] : ($val['work_time_dis'] * $val['count']);
                    $orders[$key]['yh_total_money'] = $val['all_dis'] - $workTimeDis;
                    if (in_array($val['order_status'], [1, 3, 5, 6, 8])) {
                        $orders[$key]['money'] = '0.00';
                    }
                    if ($val['payment_method'] == 1) {
                        $orders[$key]['payment_method'] = '现金';
                    } elseif ($val['payment_method'] == 2) {
                        $orders[$key]['payment_method'] = '积分';
                    } elseif ($val['payment_method'] == 3) {
                        $orders[$key]['payment_method'] = '卡劵';
                    } elseif ($val['payment_method'] == 4) {
                        $orders[$key]['payment_method'] = '现金+积分';
                    } elseif ($val['payment_method'] == 5) {
                        $orders[$key]['payment_method'] = '现金+优惠券';
                    } elseif ($val['payment_method'] == 6) {
                        $orders[$key]['payment_method'] = '积分+优惠券';
                    } elseif ($val['payment_method'] == 7) {
                        $orders[$key]['payment_method'] = '现金+积分+卡劵';
                    }
                    $orders[$key]['mj_info'] = $val['full_title'] ? '满减活动-'.$val['full_title'] : '';
                    $orders[$key]['is_gift_name'] = '';
                    if($val['gift_act_id'] != 0){
                        $orders[$key]['is_gift_name'] = $val['is_gift'] == 1 ? '是' : '否';
                    }
                    if ($val['order_status'] == 2 && $val['logistics_mode'] == 1) {
                        $orders[$key]['dq_time'] = date('Y-m-d H:i:s', strtotime(sprintf("%s +1 month", $val['pay_time'])));
                    } else {
                        $orders[$key]['dq_time'] = '';
                    }
                    if (isset($val['rule_info']) && !is_null($val['rule_info'])) {
                        $rule_info = json_decode($val['rule_info'], true);
                        $orders[$key]['rule_info'] = "平台:" . $rule_info['platform'] . '%' . ',' . "经销商:" . $rule_info['dlr'] . '%';
                    } else {
                        $orders[$key]['rule_info'] = "";
                    }
                    // 订单金额
                    $orders[$key]['order_money'] = $val['money'] + $val['integral']/10 + $val['pre_point']/10 + $val['pre_use_money'];
                    $orders[$key]['pre_order_money'] = $val['pre_point']/10 + $val['pre_use_money'];
                    $orders[$key]['order_money'] = round($orders[$key]['order_money'], 2);
                    $orders[$key]['pre_order_money'] = round($orders[$key]['pre_order_money'], 2);
                    $order_codes[] = $val['order_code'];
                }
                // 结算信息预处理（假设有结算信息模型/方法，需根据实际情况调整）
                $sett_arr = [];
                foreach ($orders as $item) {
                    $sett_arr[$item['order_code']][] = $item;
                }

                $newList = [];
                $api_start_at = microtime(true);
                $invoiceStatusArr = [0 => '', 1 => '未开票', 2 => '已开票'];
                $buOrder = new BuOrder();
                foreach ($orders as $k => $item) {
                    // 发票状态
                    $item['invoice_status'] = $invoiceStatusArr[$item['invoice_status']] ?? "";
                    $item['channel_source'] = $buOrder::$channel[$item['channel']];
                    // 组合商品处理（优化后）
                    if ($item['mo_sub_id'] != 0) {
                        $comboKey = $item['order_code'].'_'.$item['mo_sub_id'];
                        $zCommodity = $combo_map[$comboKey] ?? null;
                        if ($zCommodity) {
                            $item['count'] = bcmul($zCommodity['count'], $item['count']);
                            $item['mo_commodity_name'] = $zCommodity['commodity_name'];
                            $item['total_price']       = $item['price'] * $item['count'];
                        } else {
                            $item['mo_commodity_name'] = '';
                        }
                    } else {
                        $item['work_time_money'] *= $item['count'];
                        $item['work_time_actual_money'] *= $item['count'];
                        $item['work_time_actual_money'] < 0 ? $item['work_time_actual_money'] = 0 : '';
                        $item['work_time_dis'] *= $item['count'];
                        $item['mo_commodity_name'] = '';
                    }
                    // 工时信息
                    $workTimeJson = !empty($item['work_time_json']) ? json_decode($item['work_time_json'], true) : [];
                    $item['work_time_number'] = $workTimeJson['work_time_number'] ?? 0;
                    $item['work_time_code'] = $workTimeJson['work_time_code'] ?? 0;
                    $item['is_work_time'] = !empty($item['work_time_number']) ? '是' : '否';
                    // 结算信息
                    foreach ($sett_arr as $order_code_index => $datum) {
                        if ($item['order_code'] == $order_code_index) {
                            $act_sett_money_arr = array_column($datum, 'act_sett_money','commodity_id');
                            $card_sett_money_arr = array_column($datum, 'card_sett_money','commodity_id');
                            $order_card_subsidy_money = array_sum($card_sett_money_arr);
                            $order_act_subsidy_money = array_sum($act_sett_money_arr);
                            $commodity_card_subsidy_money = $card_sett_money_arr[$item['commodity_id']] ?? 0;
                            $commodity_act_subsidy_money = $act_sett_money_arr[$item['commodity_id']] ?? 0;
                            $order_total_subsidy_money = $order_card_subsidy_money + $order_act_subsidy_money;
                            $commodity_total_subsidy_money = $commodity_card_subsidy_money + $commodity_act_subsidy_money;
                            $card_sett_rule = [];
                            if (!empty($item['card_sett_rule'])) {
                                $card_sett_rule_arr = json_decode($item['card_sett_rule'], true);
                                foreach ($card_sett_rule_arr as $key => $value) {
                                    $str = '';
                                    foreach ($value as $k => $v) {
                                        if ($k == 1) {
                                            $str = '固定金额-'.$v.'元';
                                        } else {
                                            $str = '比例结算-'.$v.'%';
                                        }
                                    }
                                    $card_sett_rule[] = $str;
                                }
                            }
                            $act_sett_rule = [];
                            if (!empty($item['act_sett_rule'])) {
                                $card_sett_rule_arr = json_decode($item['act_sett_rule'], true) ?? [];
                                foreach ($card_sett_rule_arr as $key => $value) {
                                    if ($key == 1) {
                                        $str = '固定金额-'.$value.'元';
                                    } else {
                                        $str = '比例结算-'.$value.'%';
                                    }
                                    $act_sett_rule[] = $str;
                                }
                            }
                            $item['card_sett_rule'] = implode(',', $card_sett_rule);
                            $item['act_sett_rule'] = implode(',', $act_sett_rule);
                            $card_sett_standard_arr = json_decode($item['card_sett_standard'], true) ?? [];
                            $sett_standard = [];
                            foreach ($card_sett_standard_arr as $key => $val) {
                                $sett_standard[] = \app\common\model\db\DbCard::sett_standard($val);
                            }
                            $item['card_sett_standard'] = implode(',', $sett_standard);
                            $item['act_sett_standard'] = \app\common\model\db\DbCard::sett_standard($item['act_sett_standard']);
                            $item['order_card_subsidy_money'] = $order_card_subsidy_money;
                            $item['commodity_card_subsidy_money'] = $commodity_card_subsidy_money;
                            $item['order_act_subsidy_money'] = $order_act_subsidy_money;
                            $item['commodity_act_subsidy_money'] = $commodity_act_subsidy_money;
                            $item['order_total_subsidy_money'] =  $order_total_subsidy_money;
                            $item['commodity_total_subsidy_money'] = $commodity_total_subsidy_money;
                        }
                    }
                    // 预售订单分拆
                    if ($item['pre_sale_id'] > 0) {
                        $finalAdd                   = $preAdd = $item;  // 预售订单信息  尾款订单信息
                        $preAdd['pay_order_code']   = $item['cashier_trade_no2']; // 支付单号
                        $finalAdd['pay_order_code'] = $item['cashier_trade_no']; // 支付单号
                        $preAdd['cashier_settlement_no']   = $item['cashier_settlement_no2']; // 结算单号
                        $finalAdd['cashier_settlement_no'] = $item['cashier_settlement_no']; // 结算单号
                        $preAdd['cashier_trade_no']   = empty($item['ms_order_code2'])?$item['cashier_trade_no2']:'';
                        $finalAdd['cashier_trade_no'] = empty($item['ms_order_code'])?$item['cashier_trade_no']:'';
                        $preAdd['ms_order_code']   = $item['ms_order_code2'];
                        $finalAdd['ms_order_code'] = $item['ms_order_code'];
                        $preAdd['pay_money'] = $item['pre_use_money'];
                        $finalAdd['pay_money'] = $item['money'];
                        $preAdd['total_integral'] = $item['pre_point'];
                        $finalAdd['total_integral'] = $item['integral'];
                        $preAdd['pay_time']   = $item['front_pay_time'];
                        $finalAdd['pay_time'] = $item['pay_time'];
                        $preAdd['count'] = 0;
                        array_push($newList, $preAdd);
                        if ($item['order_status'] != 15) {
                            array_push($newList, $finalAdd);
                        }
                    } else {
                        $item['cashier_trade_no'] = empty($item['ms_order_code'])?$item['cashier_trade_no']:'';
                        array_push($newList, $item);
                    }
                }
                // 卡券名
                $card_list = [];
                if (!empty($card_id_arr)) {
                    $c_model = new DbCard();
                    $card_list = $c_model->where(['id' => ['in', $card_id_arr]])->column('card_name,card_quota', 'id');
                }
                // 取送车券
                if (!empty($act_ids)){
                    $card_r_r = new BuCardReceiveRecord();
                    $act_map = ['act_id' => ['in', $act_ids], 'user_id' => ['in', $act_user_id]];
                    $field = 'act_id,status';
                    $card_receive_list = $card_r_r->where($act_map)->field($field)->select();
                    $statusData = BuCardReceiveRecord::STATUS_LIST;
                    foreach ($card_receive_list as $key => $item) {
                        $card_receive_arr[$item['act_id']]['card_status'] = $statusData[$item['status']] ?? '';
                    }
                }
                foreach ($newList as $order) {
                    // 如果 $order 不是一维数组，强制转为一维
                    if (is_object($order)) {
                        $order = (array)$order;
                    }
                    foreach ($order as $k => $v) {
                        if (is_array($v) || is_object($v)) {
                            $order[$k] = json_encode($v, JSON_UNESCAPED_UNICODE);
                        }
                    }
                    $row = $this->formatRow($order, $fields);
                    fputcsv($fp, $row);
                }
                $page++;
            } while (count($orders) == $pageSize);
            fclose($fp);
            DbExports::where('id', $id)->update(['export_status' => 2]);
            $job->delete();
        } catch (\Throwable $e) {
            echo ('OrderExportFast error: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
            DbExports::where('id', $id)->update([
                'export_status' => 3,
                'remarks' => $e->getMessage() . "\n" . $e->getFile() . ':' . $e->getLine()
            ]);
            $job->delete();
        }
    }

    private function getOrdersmPage($params, $page, $pageSize)
    {
        $orderModel = new BuOrder();
        $params['page'] = $page;
        $params['limit'] = $pageSize;
        $list = $orderModel->getOrdersm($params, false);
        if (is_object($list)) $list = $list->toArray();
        return $list;
    }

    private function formatRow($order, $fields)
    {
        // 确保 $order 是数组
        if (is_object($order)) {
            $order = (array)$order;
        }
        if (!$fields) return array_values($order);
        $fieldsArr = explode(',', $fields);
        $row = [];
        foreach ($fieldsArr as $field) {
            $row[] = $order[$field] ?? '';
        }
        return $row;
    }
}
