<?php


namespace app\admin_v2\queue;


use app\common\model\db\DbExports;
use app\common\model\new_media\NewMediaDlr;
use think\Exception;
use think\queue\Job;
use tool\Logger;
use tool\PhpExcelPage;

class NewMediaDlrQueue extends Base
{
    /**
     *
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data)
    {
        ini_set('memory_limit', '1024M');
        $info         = json_decode($data, true);
        $map          = $info['map'];
        $exportId     = $info['id'];
        $export_model = new DbExports();
        $export_model->where('id', $exportId)->update(['export_status' => 1]);

        try {

            $pageExcel = new PhpExcelPage();
            $pageExcel->setSheetTitle('新媒体门店导出');
            $title = [
                'A' => '新媒体平台', 'B' => '新媒体门店ID', 'C' => 'DFN门店编码'
            ];
            $pageExcel->setTitle($title);
            $widthArr = [
                'A' => '30', 'B' => '30', 'C' => '30'
            ];
            $pageExcel->setWidth($widthArr);
            $totalPage = 10;
            $limit     = 1000;
            $dlr_model = new NewMediaDlr();
            for ($i = 0; $i < $totalPage; $i++) {
                $list = $dlr_model->where($map)->limit($i * $limit, $limit)->select();
                if (empty($list)) {
                    break;
                }
                $data = [];
                foreach ($list as $datum) {
                    $data[] = [
                        'A' => $datum['platform'],
                        'B' => $datum['dlr_id'],
                        'C' => $datum['dfn_dlr_code'],
                    ];
                }
                $pageExcel->setData($data, $i + 1, $limit);
            }

            $pageExcel->saveFile('新媒体门店导出_' . $exportId . '.xls');
            $path = $pageExcel->getFilePath();
            $upd  = [
                'export_status' => 2,
                'file_address'  => $path
            ];
            $export_model->where('id', $exportId)->update($upd);

        } catch (Exception $e) {
            $msg = $e->getMessage();
            Logger::error('NewMediaCommodityQueue: ' . $msg);

            $upd = [
                'export_status' => 3,
                'modifier'      => $msg
            ];
            $export_model->where('id', $exportId)->update($upd);
        }

        $job->delete();

    }
}