<?php

namespace app\admin_v2\queue;

use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\db\DbExports;
use think\queue\Job;

class OrderExportFast extends Base
{
    /**
     * 高效订单导出队列，分页分批导出，极大提升大数据量导出效率
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data)
    {
        ini_set('memory_limit', '1024M');
        set_time_limit(0);

        $info = json_decode($data, true);
        $id = $info['id'];
        $params = $info['params'];
        $brand = $info['brand']; // 品牌
        $titles = $info['titles'] ?? '';
        $order_source = $info['order_source'] ?? 0;
        $field = "a.payment_method,b.full_id,b.full_dis_money,a.parent_order_code,a.user_id,channel,a.gift_score,
        a.refund_money,a.shop_integral,c.comm_type_id,c.is_pure,a.id,a.payment_method,a.cashier_trade_no,a.front_money,
        a.pre_use_money,a.front_pay_time,a.cashier_trade_no2,a.order_code,a.pay_order_code,a.total_money,a.money,a.name,
        a.phone,a.vin,a.order_vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.order_status,
        a.logistics_mode,a.order_source,f.dlr_code,f.dlr_name,b.spr_id,b.b_act_price,b.count,b.all_dis,b.card_all_dis,
        b.pre_sale_id,b.commodity_pic,b.commodity_name,b.car_info,b.sku_info,b.price,b.count,b.price*b.count as total_price,
        b.actual_price,b.actual_point,b.actual_use_money,c.commodity_code,b.third_sku_code sku_code,(case h.type when 1 then '平台' when 2 then '专营店' when 3 then '官微销售' end) as type,
        (case b.sales_source when 0 then '旧数据' when 1 then '平台自营销售' when 2 then '专营店销售' when 3 then '官微销售' end) as sales_source,
        a.receipt_address,a.pay_time,a.delivery_time as verification_time,a.mail_price,a.is_cc_ok,b.price old_price,a.delivery_time,b.limit_id,b.n_dis_id,card_money,
        b.mail_price goods_mail_price,a.pay_order_code2,a.cashier_trade_no,a.cashier_trade_no2,b.third_sku_code,b.third_order_id,
        b.tax_code,b.tax,b.supplier,b.cost_price,b.third_mail_price,a.ic_card_no,a.ms_order_code,a.ms_order_code2,b.mo_sub_id,
        b.work_time_json,b.act_sett_money,b.card_sett_money,b.work_time_money,b.work_time_actual_money,b.work_time_dis,b.mo_id,
        b.order_commodity_status,b.common_carrier,b.waybill_number,b.delivery_time,b.id order_commodity_id,ps.title as presale,
        point.point_order_code,b.give_integral,a.pre_point,b.qsc_num,b.commodity_id,a.source,b.gift_act_id,b.is_gift,
        g.title as gift_title,se.title as seckill_name,b.suit_id,a.card_degree_code,b.commodity_segment_dis_money,b.crowd_id,b.limit_dis_money,b.source_special,
        b.card_sett_rule,b.act_sett_rule,b.card_sett_standard,b.act_sett_standard,invoice.invoice_status,invoice.invoice_time";
        if ($brand == 1) {
            $field .= ",b.first_order_price,yd.point as yd_point,a.current_points";
        }
        if ($brand == 2) {
            $field .= ",a.current_points";
        }
        // 多选不处理
        if ($order_source == 6) {
            $field .= ",m.mobile ca_phone,m.emp_no,m.emp_name,o.service_charge,(case o.uses when 1 then '自己用' when 2 then '转赠' end) as uses";
        }
        $params['field'] = $field;
        try {
            $file_name = 'public/uploads/exports/fast_order_export_' . $id . '.csv';
            DbExports::where('id', $id)->update(['export_status' => 1, 'file_address' => $file_name]);
            $fp = fopen(ROOT_PATH . $file_name, 'a');
            // 添加BOM头
            fwrite($fp, chr(0xEF) . chr(0xBB) . chr(0xBF));
            $titles = '序号,订单编码,总订单号,老支付单号,新支付单号,结算单号,订单商户号,订单金额,订单总价,实付金额,订单实付积分,订单运费,卡券总优惠金额,活动总优惠金额,退款金额,专营店,姓名,手机号码,vin,下单vin,车牌,专营店积分,商城积分,卡券名称,卡劵code,满减,下单时间,支付时间,核销时间,到期时间,订单状态,购买卡券状态,物流方式,商品图片,组合商品名称,商品名称,一级分类,二级分类,三级分类,是否纯正,车辆信息,sku信息,商品编码,规格编码,商品属性,销售来源,渠道来源,订单来源,收货地址,支付方式,积分卡券状态,原价,活动价,数量,总价,活动优惠总金额,商品卡券优惠金额,商品实付金额,商品实付积分,商品运费,是否线上支付工时,商品工时编码,商品工时原价,商品工时实付金额,商品工时优惠金额,供应商订单号,供应商运费,供应商,税务编码,税率,成本价,分账比例,预售活动,限时优惠,N件N折,用户ID,source_code,商品状态,承运公司,运单编号,发货时间,商品赠送积分';
            if ($order_source == 6) {
                $titles .= ",ca手机号,工号,姓名,服务费,用途";
            }
            if ($brand == 1) {
                $titles .= ',商品活动优惠金额,首单立减,移动积分,买赠活动,是否赠品,秒杀活动,前台一级分类,前台二级分类,前台三级分类,优惠套装,会员等级价,会员等级,支付前积分余额,卡券结算规则,活动结算规则,卡券结算价格标准值,活动结算价格标准值,订单厂家卡券补贴金额,商品厂家卡券补贴金额,订单厂家活动补贴金额,商品厂家活动补贴金额,订单厂家总补贴金额,商品厂家总补贴金额,开票状态,开票时间';
            }
            if ($brand == 2) {
                $titles .= ',买赠活动,是否赠品,秒杀活动,前台一级分类,前台二级分类,前台三级分类,优惠套装,会员等级价,会员等级,支付前积分余额,卡券结算规则,活动结算规则,卡券结算价格标准值,活动结算价格标准值,订单厂家卡券补贴金额,商品厂家卡券补贴金额,订单厂家活动补贴金额,商品厂家活动补贴金额,订单厂家总补贴金额,商品厂家总补贴金额,开票状态,开票时间';
            }

            if ($brand == 3) {
                $titles .= ",会员手机号,电车积分,油车积分,油车vin集合,前台一级分类,前台二级分类,前台三级分类";
            }
            $titles .= ',扣积分单号,取送车数,众筹活动,工会手机号';

            if ($titles) {
                $title_arr = explode(',', $titles);
                fputcsv($fp, $title_arr);
            }
            $page = 1;
            $pageSize = 1000;
            $total = 0;
            do {
                $orders = $this->getOrderPage($params, $page, $pageSize);
                if (empty($orders)) break;
                $orderCodes = array_column($orders, 'order_code');
                // 批量查商品
                $goods = $this->getOrderGoods($orderCodes);
                // 组装数据
                foreach ($orders as $order) {
                    $row = $this->formatRow($order, $goods[$order['order_code']] ?? []);
                    fputcsv($fp, $row);
                    $total++;
                }
                $page++;
            } while (count($orders) == $pageSize);
            fclose($fp);
            DbExports::where('id', $id)->update(['export_status' => 2]);
            $job->delete();
        } catch (\Throwable $e) {
            \think\Log::error('OrderExportFast error: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
            DbExports::where('id', $id)->update([
                'export_status' => 3,
                'file_address' => '',
                'remarks' => $e->getMessage()
            ]);
            $job->delete();
        }
    }

    /**
     * 分页获取订单（完全参照 OrderExport.php 的 getOrders 逻辑）
     */
    private function getOrderPage($params, $page, $pageSize)
    {
        $orderModel = new \app\common\model\bu\BuOrder();
        $params['page'] = $page;
        $params['limit'] = $pageSize;
        $list = $orderModel->getOrderLimitAjax($params);
        if (is_object($list)) $list = $list->toArray();
        if (empty($list)) return [];

        // 确保 $list 为数组
        if (is_object($list)) {
            $list = $list->toArray();
        } elseif (!is_array($list)) {
            $list = (array)$list;
        }

        // 依赖表批量查询
        $orderCodes = array_column($list, 'order_code');
        $commodity_type_model = new \app\common\model\db\DbCommodityType();
        $commodity_type_list = $commodity_type_model->where('is_enable', 1)->column('id,comm_type_name,comm_parent_id', 'id');
        $crowdfund_model = new \app\common\model\db\DbCrowdfund();
        $crowd_title_arr = $crowdfund_model->where('is_enable', 1)->column('title', 'id');
        $cheap_index_model = new \app\common\model\bu\BuCheapSuitIndex();
        $cheap_index_list = $cheap_index_model->where('is_enable', 1)->column('name', 'id');
        $export_rule_list = \app\common\model\db\DbSeparateAccountRules::where('is_enable', 1)->column('*', 'id');
        $full_discount_list = \app\common\model\db\DbFullDiscount::where('is_enable', 1)->column('*', 'id');
        $map = ['is_enable' => 1, 'value_type' => 25];
        $brand = $params['brand'] ?? 1;
        if ($brand == 1) {
            $map['parent_value_code'] = 'N';
        } else {
            $map['parent_value_code'] = 'V';
        }
        $user_level_arr = \app\common\model\db\DbSystemValue::where($map)->order('order_no')->column('county_name', 'value_code');
        $limit = [];
        $limit_id = [];
        $n_dis_id = [];
        $n_dis_id_arr = [];
        $card_list = [];
        $card_id = [];
        $goods_info = [];
        $source_special = [];
        $trade_list = [];
        $sett_arr = [];
        if (is_array($list)) {
            foreach ($list as $kkk => $vvv) {
                $orderCodes[] = $vvv['order_code'];
                if (!empty($vvv['source_special'])) {
                    $source_special[] = $vvv['source_special'];
                }
                $one = $this->getCommTypeName($commodity_type_list, $vvv['comm_type_id'], 1);
                $two = $this->getCommTypeName($commodity_type_list, $vvv['comm_type_id'], 2);
                $three = $this->getCommTypeName($commodity_type_list, $vvv['comm_type_id'], 3);
                $list[$kkk]['top_comm_type_name'] = $one;
                $list[$kkk]['parent_comm_type_name'] = $two;
                $list[$kkk]['comm_type_name'] = $three;
                $crowd_title = '';
                if (!empty($vvv['crowd_id'])) {
                    $crowd_title = $crowd_title_arr[$vvv['crowd_id']] ?? '';
                }
                $list[$kkk]['crowd_title'] = $crowd_title;
                $list[$kkk]['web_comm_type_name'] = '';
                $list[$kkk]['web_parent_comm_type_name'] = '';
                $list[$kkk]['web_top_comm_type_name'] = '';
                $list[$kkk]['suit_name'] = $cheap_index_list[$vvv['suit_id']] ?? '';
                $export_rule_info = $export_rule_list[$vvv['spr_id']] ?? '';
                if (empty($export_rule_info)) {
                    $list[$kkk]['rule_info'] = '';
                } else {
                    $rule_info = json_decode($export_rule_info['rule_info'], true);
                    $list[$kkk]['rule_info'] = "平台:" . $rule_info['platform'] . '%' . ',' . "经销商:" . $rule_info['dlr'] . '%';
                }
                $full_discount_info = $full_discount_list[$vvv['full_id']] ?? '';
                if (empty($full_discount_info)) {
                    $list[$kkk]['mj_info'] = '';
                } else {
                    $list[$kkk]['mj_info'] = sprintf("满%s-%s", $full_discount_info['money'], $full_discount_info['preferential_money']);
                }
                if (in_array($brand, [1, 2])) {
                    $list[$kkk]['is_gift_name'] = '';
                    if ($vvv['gift_act_id'] != 0) {
                        if ($vvv['is_gift'] == 0) {
                            $list[$kkk]['is_gift_name'] = '否';
                        } elseif ($vvv['is_gift'] == 1) {
                            $list[$kkk]['is_gift_name'] = '是';
                        }
                    }
                }
                if ($vvv['n_dis_id'] && !in_array($vvv['n_dis_id'], $n_dis_id_arr)) {
                    $n_dis_id[] = $vvv['n_dis_id'];
                    $n_dis_id_arr[] = $vvv['n_dis_id'];
                }
                $list[$kkk]['n_dis'] = '';
                if ($vvv['n_dis_id']) {
                    $n_dis_id[] = $vvv['n_dis_id'];
                }
                $list[$kkk]['limit'] = '';
                if ($vvv['limit_dis_money'] != '0.00' && $vvv['limit_id']) {
                    $limit_id[] = $vvv['limit_id'];
                }
                $list[$kkk]['card_name'] = '';
                if (!empty($vvv['card_id'])) {
                    $arr = explode(',', $vvv['card_id']);
                    $card_id = array_merge($card_id, $arr);
                }
            }
        }
        if (!empty($source_special)) {
            $trade_model = new \app\common\model\act\AcHaveTradeList();
            $map = [
                'sp_id' => ['in', array_unique($source_special)],
                'is_enable' => 1,
            ];
            $field = 'id,phone,sp_id,user_id';
            $trade_list = $trade_model->where($map)->field($field)->select();
        }
        $limit_id = array_unique($limit_id);
        if (!empty($limit_id)) {
            $limit_model = new \app\common\model\db\DbLimitDiscount();
            $limit = $limit_model->where(['id' => ['in', $limit_id]])->column('title', 'id');
        }
        $n_dis_id = array_unique($n_dis_id);
        if (!empty($n_dis_id)) {
            $n_dis_model = new \app\common\model\db\DbNDiscount();
            $n_dis_list = $n_dis_model->where(['id' => ['in', $n_dis_id]])->column('title', 'id');
        } else {
            $n_dis_list = [];
        }
        $card_id = array_unique($card_id);
        if (!empty($card_id)) {
            $c_model = new \app\common\model\db\DbCard();
            $field = 'card_id,card_name,card_quota';
            $card_list = $c_model->where(['id' => ['in', $card_id]])->column($field, 'id');
        }
        $order_commodity_model = new \app\common\model\bu\BuOrderCommodity();
        $params_goods = [
            'where' => ['order_code' => ['in', $orderCodes], 'mo_id' => 0, 'is_enable' => 1],
            'group' => 'order_code',
            'field' => 'order_code,sum(b_act_price*count) sum_total,sum(all_dis) sum_all_dis'
        ];
        $goods_arr = $order_commodity_model->getList($params_goods);
        foreach ($goods_arr as $v) {
            $goods_info[$v['order_code']] = $v;
        }
        $field = 'order_code,commodity_id,act_sett_money,card_sett_money,card_sett_rule,act_sett_rule,card_sett_standard,act_sett_standard';
        $where = ['order_code' => ['in', $orderCodes], 'mo_id' => 0, 'is_enable' => 1];
        $params_sett = ['where' => $where, 'field' => $field];
        $goods_sett_list = $order_commodity_model->getList($params_sett);
        $sett_arr = [];
        foreach ($goods_sett_list as $item) {
            $sett_arr[$item['order_code']][] = $item;
        }
        $order_source_list = \app\common\model\bu\BuOrder::orderSource();
        unset($order_source_list['5']);
        $act_ids = [];
        $user_ids = [];
        if (is_array($list)) {
            foreach ($list as $key => $val) {
                if ($val['limit_id']) {
                    $list[$key]['limit'] = $limit[$val['limit_id']] ?? "活动未明";
                }
                if ($val['n_dis_id']) {
                    $list[$key]['n_dis'] = $n_dis_list[$val['n_dis_id']] ?? "活动未明";
                }
                $card_mon = 0;
                if ($val['card_id']) {
                    $card_named_list = '';
                    $arr = explode(',', $val['card_id']);
                    foreach ($arr as $cardId) {
                        if (isset($card_list[$cardId])) {
                            $card_name = $card_list[$cardId]['card_name'] ?? '';
                            $card_quota = $card_list[$cardId]['card_quota'] ?? 0;
                            $card_named_list .= $card_name . ',';
                            $card_mon += $card_quota;
                        }
                    }
                    $list[$key]['card_name'] = rtrim($card_named_list, ',');
                }
                $trade_phone = '';
                if ($val['source_special']) {
                    foreach ($trade_list as $datum) {
                        if ($datum['sp_id'] == $val['source_special'] && $val['user_id'] == $datum['user_id']) {
                            $trade_phone = $datum['phone'];
                        }
                    }
                }
                $list[$key]['trade_phone'] = $trade_phone;
                $list[$key]['cashier_settlement_no'] = '';
                $list[$key]['dlr'] = $val['dlr_code'] . ' - ' . $val['dlr_name'];
                $list[$key]['order_status_name'] = $orderModel::orderStatus($val['order_status']);
                $list[$key]['order_commodity_status_name'] = \app\common\model\bu\BuOrderCommodity::$order_commodity_status[$val['order_commodity_status']] ?? $list[$key]['order_status_name'];
                $list[$key]['logistics_mode_name'] = $val['logistics_mode'] == 1 ? '自提' : '快递';
                $list[$key]['order_source'] = isset($order_source_list[$val['order_source']])?$order_source_list[$val['order_source']]:''
                ;
                $list[$key]['is_pure'] = empty($val['is_pure']) ? '否' : '是';
                $workTimeDis = ($val['mo_sub_id'] != 0) ? $val['work_time_dis'] : ($val['work_time_dis'] * $val['count']);
                $list[$key]['yh_total_money'] = $val['all_dis'] - $workTimeDis;
                if (in_array($val['order_status'], [1, 3, 5, 6, 8])) {
                    $list[$key]['money'] = '0.00';
                }
                if ($val['is_cc_ok'] == 0) {
                    if ($val['card_money'] <> $card_mon && $val['card_money'] > 0) {
                        $list[$key]['is_cc'] = '卡券使用有点异常';
                    } else {
                        $list[$key]['is_cc'] = '正常';
                    }
                } elseif ($val['is_cc_ok'] == 1) {
                    $list[$key]['is_cc'] = '积分扣除失败';
                } elseif ($val['is_cc_ok'] == 2) {
                    $list[$key]['is_cc'] = '卡券核销失败';
                }
                if ($val['payment_method'] == 1) {
                    $list[$key]['payment_method'] = '现金';
                } elseif ($val['payment_method'] == 2) {
                    $list[$key]['payment_method'] = '积分';
                } elseif ($val['payment_method'] == 3) {
                    $list[$key]['payment_method'] = '卡劵';
                } elseif ($val['payment_method'] == 4) {
                    $list[$key]['payment_method'] = '现金+积分';
                } elseif ($val['payment_method'] == 5) {
                    $list[$key]['payment_method'] = '现金+优惠券';
                } elseif ($val['payment_method'] == 6) {
                    $list[$key]['payment_method'] = '积分+优惠券';
                } elseif ($val['payment_method'] == 7) {
                    $list[$key]['payment_method'] = '现金+积分+卡劵';
                }
                if ($val['order_status'] == 2 && $val['logistics_mode'] == 1) {
                    $list[$key]['dq_time'] = date('Y-m-d H:i:s', strtotime(sprintf("%s +1 month", $val['pay_time'])));
                } else {
                    $list[$key]['dq_time'] = '';
                }
                $goods_info_one = $goods_info[$val['order_code']] ?? ['sum_total' => '', 'sum_all_dis' => ''];
                $list[$key]['order_total_money'] = $goods_info_one['sum_total'];
                $list[$key]['order_all_dis'] = $goods_info_one['sum_all_dis'];
                $list[$key]['order_money'] = $val['money'] + $val['integral'] / 10 + $val['pre_point'] / 10 + $val['pre_use_money'];
                if (in_array($val['order_status'], [2, 7]) && in_array($val['order_source'], [24])) {
                    $act_ids[] = $val['id'];
                    $user_ids[] = $val['user_id'];
                }
                foreach ($sett_arr as $order_code => $item) {
                    if ($val['order_code'] == $order_code) {
                        $act_sett_money_arr = array_column($item, 'act_sett_money', 'commodity_id');
                        $card_sett_money_arr = array_column($item, 'card_sett_money', 'commodity_id');
                        $order_card_subsidy_money = array_sum($card_sett_money_arr);
                        $order_act_subsidy_money = array_sum($act_sett_money_arr);
                        $commodity_card_subsidy_money = $card_sett_money_arr[$val['commodity_id']] ?? 0;
                        $commodity_act_subsidy_money = $act_sett_money_arr[$val['commodity_id']] ?? 0;
                        $order_total_subsidy_money = $order_card_subsidy_money + $order_act_subsidy_money;
                        $commodity_total_subsidy_money = $commodity_card_subsidy_money + $commodity_act_subsidy_money;
                        $card_sett_rule = [];
                        if (!empty($val['card_sett_rule'])) {
                            $card_sett_rule_arr = json_decode($val['card_sett_rule'], true);
                            foreach ($card_sett_rule_arr as $index => $value) {
                                $str = '';
                                foreach ($value as $k => $v) {
                                    if ($k == 1) {
                                        $str = '固定金额-' . $v . '元';
                                    } else {
                                        $str = '比例结算-' . $v . '%';
                                    }
                                }
                                $card_sett_rule[] = $str;
                            }
                        }
                        $act_sett_rule = [];
                        if (!empty($val['act_sett_rule'])) {
                            $card_sett_rule_arr = json_decode($val['act_sett_rule'], true) ?? [];
                            foreach ($card_sett_rule_arr as $k_index => $value) {
                                if ($k_index == 1) {
                                    $str = '固定金额-' . $value . '元';
                                } else {
                                    $str = '比例结算-' . $value . '%';
                                }
                                $act_sett_rule[] = $str;
                            }
                        }
                        $list[$key]['card_sett_rule'] = implode(',', $card_sett_rule);
                        $list[$key]['act_sett_rule'] = implode(',', $act_sett_rule);
                        $card_sett_standard_arr = json_decode($val['card_sett_standard'], true) ?? [];
                        $sett_standard = [];
                        foreach ($card_sett_standard_arr as $v) {
                            $sett_standard[] = \app\common\model\db\DbCard::sett_standard($v);
                        }
                        $list[$key]['card_sett_standard'] = implode(',', $sett_standard);
                        $list[$key]['act_sett_standard'] = \app\common\model\db\DbCard::sett_standard($val['act_sett_standard']);
                        $list[$key]['order_card_subsidy_money'] = $order_card_subsidy_money;
                        $list[$key]['commodity_card_subsidy_money'] = $commodity_card_subsidy_money;
                        $list[$key]['order_act_subsidy_money'] = $order_act_subsidy_money;
                        $list[$key]['commodity_act_subsidy_money'] = $commodity_act_subsidy_money;
                        $list[$key]['order_total_subsidy_money'] = $order_total_subsidy_money;
                        $list[$key]['commodity_total_subsidy_money'] = $commodity_total_subsidy_money;
                    }
                }
            }
        }
        $card_receive_list = [];
        if (!empty($act_ids)) {
            $field = "act_id,(case status when 1 then '（未使用）' when 3 then '（已使用）' when 4 then '（已失效）' when 5 then '（已冻结）' else '' end) as card_status";
            $map = [
                'user_id' => ['in', $user_ids],
                'act_id' => ['in', $act_ids]
            ];
            $card_receive_list = (new \app\common\model\bu\BuCardReceiveRecord())->where($map)->field($field)->select();
        }
        if (is_array($list)) {
            foreach ($list as $key => &$item) {
                $item = is_object($item) ? $item->toArray() : $item;
                $item['card_status'] = '';
                foreach ($card_receive_list as $v) {
                    if ($v['act_id'] == $item['id']) {
                        $item['card_status'] = $v['card_status'];
                    }
                }
                if ($item['mo_sub_id'] != 0) {
                    $map = ['order_code' => $item['order_code'], 'mo_id' => $item['mo_sub_id']];
                    $zCommodity = \app\common\model\bu\BuOrderCommodity::where($map)->field('id,order_code,count,commodity_name')->find();
                    $item['count'] = $zCommodity['count'] * $item['count'];
                    $item['mo_commodity_name'] = $zCommodity['commodity_name'];
                    $item['total_price'] = $item['price'] * $item['count'];
                } else {
                    $item['mo_commodity_name'] = '';
                    $item['work_time_money'] *= $item['count'];
                    $item['work_time_actual_money'] *= $item['count'];
                    if ($item['work_time_actual_money'] < 0) {
                        $item['work_time_actual_money'] = 0;
                    }
                    $item['work_time_dis'] *= $item['count'];
                }
                $workTimeJson = !empty($item['work_time_json']) ? json_decode($item['work_time_json'], true) : [];
                $item['work_time_number'] = $workTimeJson['work_time_number'] ?? 0;
                $item['work_time_code'] = $workTimeJson['work_time_code'] ?? 0;
                $item['is_work_time'] = !empty($item['work_time_number']) ? '是' : '否';
                $item['work_time_subsidy_money'] = $item['act_sett_money'] + $item['card_sett_money'];
            }
        }
        return $list;
    }

    /**
     * 批量获取订单商品
     */
    private function getOrderGoods($orderCodes)
    {
        if (empty($orderCodes)) return [];
        $goodsModel = new BuOrderCommodity();
        $where = ['order_code' => ['in', $orderCodes], 'mo_id' => 0];
        $goodsList = $goodsModel->where($where)->select();
        $goodsArr = [];
        foreach ($goodsList as $g) {
            $goodsArr[$g['order_code']][] = $g->toArray();
        }
        return $goodsArr;
    }

    /**
     * 格式化导出行（参考 OrderExport.php 字段组装）
     */
    private function formatRow($order, $goodsList)
    {
        // 这里假设 $order 已经包含了所有需要的字段（如需补充，需在 getOrderPage 里 join 或查出）
        $row = [
            $order['id'] ?? '',
            $order['order_code'] ?? '',
            $order['parent_order_code'] ?? '',
            $order['cashier_trade_no'] ?? '',
            $order['ms_order_code'] ?? '',
            $order['cashier_settlement_no'] ?? '',
            $order['pay_order_code'] ?? '',
            $order['order_money'] ?? '',
            $order['order_total_money'] ?? '',
            $order['money'] ?? '',
            $order['integral'] ?? '',
            $order['mail_price'] ?? '',
            $order['card_money'] ?? '',
            $order['order_all_dis'] ?? '',
            $order['refund_money'] ?? '',
            $order['dlr'] ?? '',
            $order['name'] ?? '',
            $order['phone'] ?? '',
            $order['vin'] ?? '',
            $order['order_vin'] ?? '',
            $order['license_plate'] ?? '',
            $order['dlr_integral'] ?? '',
            $order['shop_integral'] ?? '',
            $order['card_name'] ?? '',
            $order['card_code'] ?? '',
            $order['mj_info'] ?? '',
            $order['created_date'] ?? '',
            $order['pay_time'] ?? '',
            $order['verification_time'] ?? '',
            $order['dq_time'] ?? '',
            $order['order_status_name'] ?? '',
            $order['card_status'] ?? '',
            $order['logistics_mode_name'] ?? '',
            $order['commodity_pic'] ?? '',
            $order['mo_commodity_name'] ?? '',
            $order['commodity_name'] ?? '',
            $order['comm_type_name'] ?? '',
            $order['parent_comm_type_name'] ?? '',
            $order['three_comm_type_name'] ?? '',
            $order['is_pure'] ?? '',
            $order['car_info'] ?? '',
            $order['sku_info'] ?? '',
            $order['commodity_code'] ?? '',
            $order['sku_code'] ?? '',
            $order['type'] ?? '',
            $order['sales_source'] ?? '',
            $order['channel_source'] ?? '',
            $order['order_source'] ?? '',
            $order['receipt_address'] ?? '',
            $order['payment_method'] ?? '',
            $order['is_cc'] ?? '',
            $order['old_price'] ?? '',
            $order['actual_price'] ?? '',
            $order['count'] ?? '',
            $order['total_price'] ?? '',
            $order['yh_total_money'] ?? '',
            $order['card_all_dis'] ?? '',
            $order['actual_use_money'] ?? '',
            $order['actual_point'] ?? '',
            $order['goods_mail_price'] ?? '',
            $order['is_work_time'] ?? '',
            $order['work_time_code'] ?? '',
            $order['work_time_money'] ?? '',
            $order['work_time_actual_money'] ?? '',
            $order['work_time_dis'] ?? '',
            $order['third_order_id'] ?? '',
            $order['third_mail_price'] ?? '',
            $order['supplier'] ?? '',
            $order['tax_code'] ?? '',
            $order['tax'] ?? '',
            $order['cost_price'] ?? '',
            $order['rule_info'] ?? '',
            $order['presale'] ?? '',
            $order['limit'] ?? '',
            $order['n_dis'] ?? '',
            $order['user_id'] ?? '',
            $order['source_code'] ?? '',
            $order['order_commodity_status_name'] ?? '',
            $order['common_carrier'] ?? '',
            $order['waybill_number'] ?? '',
            $order['delivery_time'] ?? '',
            $order['give_integral'] ?? '',
            // 以下为 brand==1/2/3 时的扩展字段，实际可根据需要补充
            $order['first_order_price'] ?? '',
            $order['is_first_order'] ?? '',
            $order['yd_point'] ?? '',
            $order['gift_title'] ?? '',
            $order['is_gift'] ?? '',
            $order['seckill_name'] ?? '',
            $order['web_comm_type_name'] ?? '',
            $order['web_parent_comm_type_name'] ?? '',
            $order['web_top_comm_type_name'] ?? '',
            $order['suit_name'] ?? '',
            $order['user_level_price'] ?? '',
            $order['card_degree_code'] ?? '',
            $order['current_points'] ?? '',
            $order['card_sett_rule'] ?? '',
            $order['act_sett_rule'] ?? '',
            $order['card_sett_standard'] ?? '',
            $order['act_sett_standard'] ?? '',
            $order['order_card_subsidy_money'] ?? '',
            $order['commodity_card_subsidy_money'] ?? '',
            $order['order_act_subsidy_money'] ?? '',
            $order['commodity_act_subsidy_money'] ?? '',
            $order['order_total_subsidy_money'] ?? '',
            $order['commodity_total_subsidy_money'] ?? '',
            $order['invoice_status'] ?? '',
            $order['invoice_time'] ?? '',
            $order['mid_phone'] ?? '',
            $order['tram_point'] ?? '',
            $order['oil_point'] ?? '',
            $order['oil_vin'] ?? '',
            $order['point_num'] ?? '',
            $order['qsc_num'] ?? '',
            $order['crowd_title'] ?? '',
            $order['trade_phone'] ?? '',
        ];
        return $row;
    }

    /**
     * 获取商品分类名称（递归）
     */
    private function getCommTypeName($typeList, $typeId, $level)
    {
        if ($level == 3) {
            return $typeList[$typeId]['comm_type_name'] ?? '';
        }
        if ($level == 2) {
            $parentId = $typeList[$typeId]['comm_parent_id'] ?? 0;
            if ($parentId != 0) {
                return $typeList[$parentId]['comm_type_name'] ?? '';
            } else {
                return '';
            }
        }
        if ($level == 1) {
            $twoParentId = $typeList[$typeId]['comm_parent_id'] ?? 0;
            if ($twoParentId != 0) {
                $oneParentId = $typeList[$twoParentId]['comm_parent_id'] ?? 0;
                if ($oneParentId != 0) {
                    return $typeList[$oneParentId]['comm_type_name'] ?? '';
                }
            }
            return '';
        }
    }
}
