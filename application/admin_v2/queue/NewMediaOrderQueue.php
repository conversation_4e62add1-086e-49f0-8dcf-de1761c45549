<?php


namespace app\admin_v2\queue;


use app\common\model\db\DbExports;
use app\common\model\new_media\NewMediaOrderCommodity;
use app\common\model\new_media\NewMediaOrderCommodityStore;
use app\common\model\new_media\NewMediaOrderDetail;
use app\common\model\new_media\NewMediaOrderMain;
use think\Exception;
use think\Model;
use think\queue\Job;
use tool\Logger;
use tool\PhpExcelPage;

class NewMediaOrderQueue extends Base
{
    /**
     *
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data)
    {
        ini_set('memory_limit', '1024M');
        $info         = json_decode($data, true);
        $map          = $info['map'];
        $exportId     = $info['id'];
        $export_model = new DbExports();
        $export_model->where('id', $exportId)->update(['export_status' => 1]);

        try {
            $pageExcel = new PhpExcelPage();
            $pageExcel->setSheetTitle('新媒体订单表');
            $title = [
                'A'  => '新媒体订单号', 'B' => '商城总单号', 'C' => '商城子单号', 'D' => '新媒体订单类型', 'E' => '姓名',
                'F'  => '手机号码', 'G' => '渠道来源', 'H' => '订单来源', 'I' => '订单状态', 'J' => '次数', 'K' => '次卡顺序',
                'L'  => '次卡原价', 'M' => '代金券面值', 'N' => '订单总价', 'O' => '订单实收金额', 'P' => '订单优惠金额',
                'Q'  => '订单平台优惠金额', 'R' => '订单厂家优惠金额', 'S' => '订单厂家补贴金额', 'T' => '专营店', 'U' => '适用专营店',
                'V'  => '下单时间', 'W' => '支付时间', 'X' => '核销时间', 'Y' => '物流方式', 'Z' => '下单VIN', 'AA' => '支付方式',
                'AB' => '新媒体SKUID', 'AC' => '新媒体商品名称', 'AD' => '新媒体商品ID', 'AE' => '新媒体SKU名称', 'AF' => '新媒体商品分类',
                'AG' => '新媒体备件活动', 'AH' => '新媒体工时活动', 'AI' => '备件/工时', 'AJ' => '类别', 'AK' => '商品编码',
                'AL' => '商品数量', 'AM' => '商品原价', 'AN' => '商品活动价', 'AO' => '商品原价总价',
                'AP' => '商品活动总价', 'AQ' => '商品实付金额', 'AR' => '商品优惠金额'
            ];

            $pageExcel->setTitle($title);

            // 设置金额格式 保留2位小数
            $pageExcel->setFormatCode('N');

            $widthArr = [
                'A'  => '30', 'B' => '30', 'C' => '30', 'D' => '30', 'E' => '50', 'F' => '30',
                'G'  => '30', 'H' => '30', 'I' => '30', 'J' => '50', 'K' => '30', 'L' => '30',
                'M'  => '30', 'N' => '30', 'O' => '30', 'P' => '30', 'Q' => '30', 'R' => '30',
                'S'  => '30', 'T' => '30', 'U' => '30', 'V' => '30', 'W' => '30', 'X' => '30',
                'Y'  => '30', 'Z' => '30', 'AA' => '30', 'AB' => '30', 'AC' => '30', 'AD' => '30',
                'AE' => '50', 'AF' => '30', 'AG' => '30', 'AH' => '30', 'AI' => '30', 'AJ' => '50',
                'AK' => '30', 'AL' => '30', 'AM' => '30', 'AN' => '30', 'AO' => '30', 'AP' => '30',
                'AQ' => '30', 'AR' => '30',
            ];

            $pageExcel->setWidth($widthArr);

            $totalPage = 10;
            $limit     = 5000;
            for ($i = 0; $i < $totalPage; $i++) {
                $list = $this->getData($map, $i, $limit);
                if (empty($list)) {
                    break;
                }
                $data = [];
                foreach ($list as $key => $datum) {
                    $data[$key] = [
                        'A'  => $datum['third_order_id'], // 新媒体订单号
                        'B'  => $datum['order_main_no'], // 商城总单号
                        'C'  => $datum['order_detail_no'], // 商城子单号
                        'D'  => $datum['order_type_text'], // 新媒体订单类型
                        'E'  => $datum['user_name'], // 姓名
                        'F'  => $datum['user_phone'], // 手机号码
                        'G'  => $datum['channel_source'], // 渠道来源
                        'H'  => $datum['order_source_text'], // 订单来源
                        'I'  => $datum['order_status_text'], // 订单状态
                        'J'  => $datum['card_count'], // 次数
                        'K'  => $datum['card_sort'], // 次卡顺序
                        'L'  => $datum['card_price'], // 次卡原价
                        'M'  => $datum['voucher_value'], // 代金券面值
                        'N'  => $datum['total_amount'], // 订单总价
                        'O'  => $datum['actual_payment_amount'], // 订单实收金额
                        'P'  => $datum['total_discount_amount'], // 订单优惠金额
                        'Q'  => $datum['total_platform_discount_amount'], // 订单平台优惠金额
                        'R'  => $datum['total_mfrs_discount_amount'], // 订单厂家优惠金额
                        'S'  => $datum['total_mfrs_subsidy_amount'], // 订单厂家补贴金额
                        'T'  => $datum['dlr_code'] . '-' . $datum['dlr_name'], //  专营店
                        'U'  => $datum['apply_store_num'] .'个', // 适用专营店
                        'V'  => $datum['order_created_date'], // 下单时间
                        'W'  => $datum['order_paid_time'], // 支付时间
                        'X'  => $datum['consume_time'], //  核销时间
                        'Y'  => $datum['order_delivery_type_text'], // 物流方式
                        'Z'  => $datum['vin'], // 下单VIN
                        'AA' => $datum['payment_way_text'], // 支付方式
                        'AB' => $datum['media_commodity_sku_id'], // 新媒体SKUID
                        'AC' => $datum['media_commodity_name'], // 新媒体商品名称
                        'AD' => $datum['media_commodity_id'], // 新媒体商品ID
                        'AE' => $datum['media_commodity_sku_name'], // 新媒体SKU名称
                        'AF' => $datum['media_commodity_classify'], // 新媒体商品分类
                        'AG' => $datum['media_part_activity_name'], // 新媒体备件活动
                        'AH' => $datum['media_part_wi_name'], // 新媒体工时活动
                        'AI' => $datum['type_text'], // 工时/备件
                        'AJ' => $datum['part_type_text'], // 类别
                    ];
                    if ($datum['type_text'] == '备件') {
                        $data[$key]['AK']  = $datum['part_code']; // 商品编码
                        $data[$key]['AL']  = $datum['part_quantity']; // 商品数量
                        $data[$key]['AM']  = $datum['part_price']; // 商品原价
                        $data[$key]['AN']  = $datum['part_activity_price']; // 商品活动价
                        $data[$key]['AO']  = $datum['total_part_price']; // 商品原价总价
                        $data[$key]['AP']  = $datum['total_part_activity_price']; // 商品活动总价
                        $data[$key]['AQ']  = $datum['total_part_actual_payment']; // 商品实付金额
                        $data[$key]['AR']  = $datum['total_part_discount']; // 商品优惠金额
                    } else {
                        // 工时
                        $data[$key]['AK']  = $datum['wi_code']; // 商品编码
                        $data[$key]['AL']  = $datum['wi_quantity']; // 商品数量
                        $data[$key]['AM']  = $datum['wi_price']; // 商品原价
                        $data[$key]['AN']  = $datum['wi_activity_price']; // 商品活动价
                        $data[$key]['AO']  = $datum['total_wi_price']; // 商品原价总价
                        $data[$key]['AP']  = $datum['total_wi_activity_price']; // 商品活动总价
                        $data[$key]['AQ']  = $datum['total_wi_actual_payment']; // 商品实付金额
                        $data[$key]['AR']  = $datum['total_wi_discount']; // 商品优惠金额
                    }

                }
                $pageExcel->setData($data, $i + 1, $limit);
            }

            $pageExcel->saveFile('新媒体订单表_' . $exportId . '.xls');
            $path = $pageExcel->getFilePath();
            $upd  = [
                'export_status' => 2,
                'file_address'  => $path
            ];
            $export_model->where('id', $exportId)->update($upd);

        } catch (Exception $e) {

            $msg = $e->getMessage();
            Logger::error('NewMediaOrderQueue:' . $msg);

            $upd = [
                'export_status' => 3,
                'modifier'      => $msg
            ];
            $export_model->where('id', $exportId)->update($upd);

        }
        $job->delete();

    }

    public function getData($map, $page, $limit)
    {
        $field = 'a.*,b.user_name,b.user_phone,b.third_order_id,b.order_created_date,b.order_paid_time,b.payment_way,
        b.vin,c.*,d.dlr_code,dlr_name';

        $order_detail_model    = new NewMediaOrderDetail();
        $list                  = $order_detail_model->alias('a')
            ->join('t_new_media_order_main b', 'a.order_main_id=b.id')
            ->join('t_new_media_order_commodity c', 'a.id=c.order_detail_id and c.is_enable=1')
            ->join('t_db_dlr d', 'a.store_code=d.dlr_code and d.is_enable=1 and brand_type=1', 'left')
            ->where($map)
            ->field($field)
            ->limit($page * $limit, $limit)
            ->select();
        $orderType             = NewMediaOrderMain::$orderType;
        $clientId              = NewMediaOrderMain::$clientId;
        $orderSource           = NewMediaOrderMain::$orderSource;
        $paymentWay            = NewMediaOrderMain::$paymentWay;
        $orderStatus           = NewMediaOrderDetail::$orderStatus;
        $orderDeliveryType     = NewMediaOrderDetail::$orderDeliveryType;
        $partType              = NewMediaOrderCommodity::$partType;
        $wiType                = NewMediaOrderCommodity::$wiType;
        $commodity_store_model = new NewMediaOrderCommodityStore();
        foreach ($list as $key =>  $item) {
            $item['order_type_text']          = $orderType[$item['order_type']] ?? '';
            $item['channel_source']           = $clientId[$item['client_id']] ?? '';
            $item['order_source_text']        = $orderSource[$item['order_source']] ?? '';
            $item['order_status_text']        = $orderStatus[$item['order_status']] ?? '';
            $item['order_delivery_type_text'] = $orderDeliveryType[$item['order_delivery_type']] ?? '';
            $item['payment_way_text']         = $paymentWay[$item['payment_way']] ?? '';

            // 工时和备件
            if (!empty($item['part_code'])) {
                $type_text      = '备件';
                $part_type_text = $partType[$item['part_type']] ?? '';
            } else {
                $type_text      = '工时';
                $part_type_text = $wiType[$item['wi_type']] ?? '';
                $list[$key]['part_code'] = $item['wi_code'];
                $list[$key]['part_quantity'] = $item['wi_quantity'];
                $list[$key]['part_price'] = $item['wi_price'];
                $list[$key]['part_activity_price'] = $item['wi_activity_price'];
                $list[$key]['total_part_price'] = $item['total_wi_price'];
                $list[$key]['total_part_activity_price'] = $item['total_wi_activity_price'];
                $list[$key]['total_part_actual_payment'] = $item['total_wi_actual_payment'];
                $list[$key]['total_part_discount'] = $item['total_wi_discount'];

            }
            $list[$key]['type_text']      = $type_text;  // 备件/工时
            $list[$key]['part_type_text'] = $part_type_text; // 类别

            $where = ['order_main_id' => $item['order_main_id']];

            $num                     = $commodity_store_model->where($where)->count();
            $list[$key]['apply_store_num'] = $num;

            // 非次卡不展示次卡信息
            if (in_array($item['order_type'], ['GROUP', 'VOUCHER'])) {
                $list[$key]['card_count'] = ''; // 次卡次数
                $list[$key]['card_sort'] = ''; // 次卡排序
                $list[$key]['card_price'] = '0'; // 次卡原价
            }

        }
        return $list;
    }
}