<?php
/**
 * Created by PhpStorm.
 * User: www
 * Date: 17/8/2
 * Time: 15:08
 */

namespace app\admin_v2\controller;

use api\jd_sdk\JdOrderN;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuOrderCommodityJd;
use app\common\model\bu\BuOrderDetailed;
use app\common\model\bu\BuOrderInsure;
use app\common\model\bu\BuOrderJd;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbArea;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbExports;
use app\common\model\db\DbJdWarehouse;
use app\common\model\db\DbJdWarehouseUpd;
use app\common\model\db\DbSystemValue;
use app\common\net_service\Inventory;
use app\common\net_service\JdWarehouse;
use app\common\net_service\NetOrder;
use app\common\port\connectors\JdCloudWarehouse;
use app\common\port\connectors\QuickWin;
use think\Db;
use think\Exception;
use think\Queue;
use think\Request;
use tool\PhpExcel;

class OrderDeliveryStatic extends Common
{
    private $buOrder;
    private $buOrderDetail;

    public function __construct()
    {
        parent::__construct();
        $this->buOrder       = new BuOrder();
//        $this->buOrderDetail = new BuOrderDetailed();
    }

    public function index($set_channel = '1,2,3')
    {
        ini_set("max_execution_time", 120);

        $order_code          = trim(input('get.order_code'));
        $dlr_name            = trim(input('get.dlr_name'));
        $pay_order_code      = trim(input('get.pay_order_code'));
        $order_source        = input('get.order_source');
        $sales_source        = input('get.sales_source');
        $is_down             = input('get.is_down');
        $pay_time            = input('get.pay_time');
        $order_status        = input('get.order_status');
        $channel             = input('get.channel') ?? $set_channel;
        $order_commodity_ids = input('get.order_commodity_ids');
        $is_ajax_down        = input('get.is_ajax_down');

        if (empty($order_status)) $order_status = '2,4,11,12';

        //渠道来源
        $channelList = BuOrder::$channel;

        if (empty($sales_source) && $this->admin_info['type'] == 1) {
            $sales_source = 1;
        }
        $brand = input('brand', 1);
        //增加一个取送车的过滤~
        $where = [
            'a.logistics_mode'     => 2,
            'b.mo_id'              => 0,
            'a.pick_up_order_type' => 0,
            'a.order_source'       => ['neq', 25],
            'b.waybill_number'     => '',
            'c.commodity_class'    => ['not in', [DbCommodity::COMMODITY_CLASS_KEY8,DbCommodity::COMMODITY_CLASS_KEY9]],
        ];

        $_where = 'a.is_enable=1';
        $query  = [
            'dlr_name'         => $dlr_name,
            'order_code'       => $order_code,
            'pay_order_code'   => $pay_order_code,
            'order_source'     => $order_source,
            'sales_source'     => $sales_source,
            'pay_time'         => $pay_time,
            'order_status'     => $order_status,
            //渠道来源
            'channel'          => $channel,
            'selected_channel' => explode(",", $channel),
            'brand'            => $brand,
        ];
        if (!empty($dlr_name)) {
            $where['e.dlr_name'] = $dlr_name;
        }

        if (!empty($channel)) {
            $where['a.channel'] = ['in', explode(",", $channel)];
        }

        if (!empty($order_code)) {
            $where['a.order_code'] = $order_code;
        }
        if (!empty($pay_order_code)) {
            $where['a.pay_order_code'] = $pay_order_code;
        }

        if ($this->admin_info['type'] == 2) {
            $where['a.dd_dlr_code'] = $this->admin_info['dlr_code'];
            if (!empty($sales_source)) {
                $where['b.sales_source'] = $sales_source;
            }
        } else {
            //            $where['f.type'] = 1;
            if ($sales_source == 2) {
                $_where = "and (b.sales_source =2 OR b.sales_source =3)  ";
            }
        }
//        if ($sales_source == 1) {
//            $supplier_arr        = array_merge(['省广', 'JD'], BuOrderCommodity::$jd_warehouse_supplier);
//            $where['b.supplier'] = ['not in', $supplier_arr];
//        }
        if ($sales_source == 3) {
            $where['b.sales_source'] = 3;
            $where['b.supplier']     = 'JD';
            $where['a.dlr_code']     = ['<>', 'P33A'];
        }
        if ($sales_source == 4) {
            $where['a.dlr_code'] = 'P33A';
        }
        if ($sales_source == 5) {
            $where['b.supplier'] = '省广';
            $where['a.dlr_code'] = ['<>', 'P33A'];
        }
        if ($sales_source == 6) {
            unset($where['b.waybill_number']); // 云仓查询有物流单号的
            $where['b.supplier'] = ['in', BuOrderCommodity::$jd_warehouse_supplier];
        }
        if (!empty($pay_time)) {
            list($pay_time_start, $pay_time_end) = explode(' ~ ', $pay_time);
//            $where['i.created_date|a.pay_time'] = ['between', [date('Y-m-d H:i:s', strtotime($pay_time_start)), date('Y-m-d H:i:s', strtotime($pay_time_end) + 86400)]];//后续再改成a.pay_time
            $where['a.pay_time'] = ['between', [date('Y-m-d H:i:s', strtotime($pay_time_start)), date('Y-m-d H:i:s', strtotime($pay_time_end) + 86400)]];
        }
        if (!empty($order_status)) {
            $where['a.order_status'] = ['in', $order_status];
        } else {
            $_where .= ' and  ((a.order_source != 2 and a.order_status in (2,4,11,12)) or (a.order_source=2 and a.order_status in (4,11,12))) ';
        }
        $where['a.logistics_mode'] = 2;

        if (!empty($order_source)) {
            if ($order_source == 2) {
                $_where .= ' and a.order_source=2 and a.order_status=12 ';
            } else {
                $_where .= ' and a.order_source = ' . $order_source . ' and a.order_status in (2,4,11,12)';
            }
        }
        $where['a.is_cc_ok'] = 0;

        //增加售后
        $af_model = new DbAfterSaleOrders();
        //已取消、退款拒绝、退货拒绝、换货拒绝状态。
        $af_where = ['afs_status' => ['not in', [2, 3, 6, 8, 11]]];
//        $max_af_id      = $af_model->group('order_id')->column('max(id) as maxid');
//        $af_where['id'] = ['in', $max_af_id];
        $af_orders = $af_model->getColumn(['where' => $af_where, 'column' => 'order_id']);
        if ($af_orders) {
            $where['a.id'] = ['not in', $af_orders];
        }
        //增加售后--end

        if (empty(input('get.'))) {
            unset($where);
            $where['a.id'] = 0;
        }

        $params = [
            'where'     => $where,
            'where_or'  => $_where,
            'query'     => $query,
            'log_order' => 1,
            'order'     => 'a.created_date desc',
            'field'     => '(case b.sales_source when 0 then \'旧数据\' when 1 then \'平台自营销售\' when 2 then \'专营店销售\' when 3 then \'官微销售\' end) as sales_source,a.id,a.order_code,a.pay_order_code,a.total_money,a.money,a.name,a.phone,a.vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.pay_time,a.order_status,a.logistics_mode,a.order_source,a.common_carrier,a.receipt_address,b.waybill_number,b.delivery_time,b.car_info,b.commodity_class,c.commodity_name,e.dlr_code,e.dlr_name,a.is_cc_ok,a.card_money,a.payment_method,b.supplier,a.channel,b.order_commodity_status,b.id order_commodity_id,b.jd_warehouse_send_id,b.price,b.count,b.price*b.count as total_price,b.mo_sub_id',
            'group'     => 'b.id'  //2021-08-30 14:57:57 用id才能显示同时买多个商品的
        ];


        if ($is_down == 1) {
            ini_set('memory_limit', '1000M');

            $params['field'] = "a.id,a.channel,a.order_code,a.pay_order_code,a.total_money,a.money,CONCAT(f.dlr_code,'-',f.dlr_name) as dlr,a.name,a.phone,a.vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.receipt_address,a.order_status,b.common_carrier,b.waybill_number,b.delivery_time,a.order_source,b.commodity_pic,c.commodity_name,b.sku_info,b.car_info,b.price,b.count,b.price*b.count as total_price,c.commodity_code,e.sku_code,(case b.sales_source when 0 then '旧数据' when 1 then '平台自营销售' when 2 then '专营店销售' when 3 then '官微销售' end) as sales_source,a.pay_time,a.is_cc_ok,a.card_money,a.payment_method,b.supplier,b.order_commodity_status,b.id order_commodity_id,b.mo_sub_id";
            if ($this->admin_info['type'] == 2) {
                $params['where']['a.dlr_code'] = $this->admin_info['dlr_code'];
                if (!empty($sales_source)) {
                    $where['b.sales_source'] = $sales_source;
                }
            } else {
                //                $params['where']['h.type'] = 1;
                $where['b.sales_source'] = $sales_source;
            }
            if (!empty($order_commodity_ids)) {
                $params['where']['b.id'] = ['in', $order_commodity_ids];
            }
            $list = $this->buOrder->getOrderLimit($params, false);
        } else {
            $params['group'] = 'b.order_code';
            $list            = $this->buOrder->getOrderPaginate($params);
            if (input('test') == 1) {
                echo $this->buOrder->getLastSql();
                die;
            }
        }


        if ($is_ajax_down == 1) {
            $params['field'] = "a.id,a.channel,a.order_code,a.pay_order_code,a.total_money,a.money,CONCAT(f.dlr_code,'-',f.dlr_name) as dlr,a.name,a.phone,a.vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.receipt_address,a.order_status,a.common_carrier,a.waybill_number,a.delivery_time,a.order_source,b.commodity_pic,c.commodity_name,b.sku_info,b.car_info,b.price,b.count,b.price*b.count as total_price,c.commodity_code,e.sku_code,(case b.sales_source when 0 then '旧数据' when 1 then '平台自营销售' when 2 then '专营店销售' when 3 then '官微销售' end) as sales_source,a.pay_time,a.is_cc_ok,a.card_money,a.payment_method,b.supplier,b.mo_sub_id,b.order_commodity_status";
            if (empty($pay_time)) {
                print_json(1, '支付时间不能为空');
            }
            list($pay_time_start, $pay_time_end) = explode(' ~ ', $pay_time);
            $days = date_diff(date_create($pay_time_start), date_create($pay_time_end))->days;
            if ($days > 31) {
                print_json(1, '支付时间请小于1个月');
            }
            if (!getRedisLock('delivery_is_ajax_down', 300)) {
                print_json(1, '限制5分钟一次');
            }

            $params_key = md5(json_encode($params));

            $export = DbExports::create([
                'export_type'   => 'delivery',
                'filter_params' => json_encode($params['where']),
                'export_key'    => $params_key,
                'creator'       => $this->admin_info['username']
            ]);

            Queue::push('app\admin_v2\queue\DeliveryExport', json_encode([
                'params' => $params,
                'time'   => $pay_time,
                'id'     => $export->id,
                'type'   => 2, // 订单发货
            ]), config('queue_type.export'));
            print_json(0, '已加入异步下载列表');
        }


//        if ($is_down == 1) {
//            ini_set('memory_limit', '1000M');
//
//            $params['field'] = "a.id,a.channel,a.order_code,a.pay_order_code,a.total_money,a.money,CONCAT(f.dlr_code,'-',f.dlr_name) as dlr,a.name,a.phone,a.vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.receipt_address,a.order_status,b.common_carrier,b.waybill_number,b.delivery_time,a.order_source,b.commodity_pic,c.commodity_name,b.sku_info,b.car_info,b.price,b.count,b.price*b.count as total_price,c.commodity_code,e.sku_code,(case b.sales_source when 0 then '旧数据' when 1 then '平台自营销售' when 2 then '专营店销售' when 3 then '官微销售' end) as sales_source,a.pay_time,a.is_cc_ok,a.card_money,a.payment_method,b.supplier,b.order_commodity_status,b.id order_commodity_id,b.mo_sub_id";
//
//            if ($this->admin_info['type'] == 2) {
//                $params['where']['a.dlr_code'] = $this->admin_info['dlr_code'];
//                if (!empty($sales_source)) {
//                    $where['b.sales_source'] = $sales_source;
//                }
//            } else {
//                //                $params['where']['h.type'] = 1;
//                $where['b.sales_source'] = $sales_source;
//            }
//            if (!empty($order_commodity_ids)) {
//                $params['where']['b.id'] = ['in', $order_commodity_ids];
//            }
//            $list = $this->buOrder->getOrderLimit($params, false);
//            foreach ($list as $key => $val) {
//                // 子商品
//                if ($val['mo_sub_id'] != 0) {
//                    $map                = ['order_code' => $val['order_code'], 'mo_id' => $val['mo_sub_id']];
//                    $zCommodity         = BuOrderCommodity::where($map)->field('id,order_code,count,commodity_name')->find();
//                    $val['count']       = $zCommodity['count'] * $val['count']; // 主商品数量*子商品数量
//                    $val['total_price'] = $val['price'] * $val['count'];
//                }
//            }
//        } else {
//            $params['group'] = 'b.order_code';
//            $list            = $this->buOrder->getOrderPaginate($params);
//            if (input('test') == 1) {
//                echo $this->buOrder->getLastSql();
//                die;
//            }
//        }



        $c_model           = new DbCard();
        $sys_model         = new DbSystemValue();
        $order_source_list = BuOrder::orderSource();
        $check_list        = $list;
        if (!is_array($check_list)) {
            $check_list = (array)$check_list;
        }
        $card_ids              = array_filter(array_unique(array_column($check_list, 'card_id')));
        $c_params              = [
            'where' => ['id' => ['in', $card_ids]],
            'field' => 'id,card_id,card_name,card_quota'
        ];
        $card_list             = $c_model->getList($c_params);
        $order_codes           = [];
        $order_commodity_model = new BuOrderCommodity();
        $jd_warehouse_model    = new DbJdWarehouse();
        foreach ($list as $key => $val) {
            // 组合商品支付
            $orderCommodities         = $val->orderCommodities;
            $moCommodityName          = '';
            $commodity_count          = 0;
            $list[$key]['have_no_f']  = 0; // 是否存在未发货商品
            $is_jd_cloud_order        = 0; // 是否是京东云仓订单
            $jd_warehouse_send_id_arr = [];
            foreach ($orderCommodities as $value) {
                $jd_warehouse_send_id_arr[] = $value['jd_warehouse_send_id'];
                if (in_array($value['supplier'], BuOrderCommodity::$jd_warehouse_supplier)) {
                    $is_jd_cloud_order = 1;
                }


                if ($val['mo_sub_id'] != 0 && $value['mo_id'] != 0) {
                    $moCommodityName     = $value['commodity_name'];
                    $count = bcmul($value['count'] ?? 0, $val['count'] ?? 0); // 主商品数量*子商品数量
                    $list[$key]['count'] = $count;
                    $list[$key]['total_price'] = $count * $val['price'];

                }
                $order_commodity_status = $value['order_commodity_status'];
                /**** 旧订单商品状态处理-begin ****/
                if ($value['mo_id'] == 0 && empty($value['order_commodity_status'])) {
                    $order_commodity_status = $order_commodity_model->orderToCommodityStatus($val['order_status']);
                    $update_data            = ['order_commodity_status' => $order_commodity_status, 'common_carrier' => $val['common_carrier'], 'waybill_number' => $val['waybill_number'], 'delivery_time' => $val['delivery_time']];
                    if ($val['order_status'] == 4) {
                        $update_data['operate_list'] = json_encode_cn($update_data);
                    }
                    if (!empty($order_commodity_status)) {
                        $order_commodity_model->saveData($update_data, ['id' => $value['id']]);
                        if ($val['order_commodity_id'] == $value['id']) $val['order_commodity_status'] = $update_data['order_commodity_status'];
                    }
                }
                /**** 旧订单商品状态处理-end ****/
                if ($order_commodity_status == 2) $list[$key]['have_no_f'] = 1;
                $commodity_count++;
            }
            // 查询JD发货单号
            $jd_warehouse_send_id_arr = array_unique($jd_warehouse_send_id_arr);
            $map                      = ['id' => ['in', $jd_warehouse_send_id_arr], 'is_enable' => 1];
            $jd_warehouse             = $jd_warehouse_model->where($map)->select();

            $list[$key]['jd_warehouse']                = $jd_warehouse;
            $list[$key]['is_jd_cloud_order']           = $is_jd_cloud_order;
            $list[$key]['commodity_count']             = $commodity_count;

            $list[$key]['mo_commodity_name']           = $moCommodityName;
            $list[$key]['order_status_name']           = BuOrder::orderStatus($val['order_status']);
            $list[$key]['order_commodity_status_name'] = BuOrderCommodity::$order_commodity_status[$val['order_commodity_status']] ?? $list[$key]['order_status_name'];
            $list[$key]['order_source']                = $order_source_list[$val['order_source']];
            if (in_array($val['order_status'], [1, 3, 5, 6, 8])) {
                $list[$key]['money'] = '0.00';
            }
            $list[$key]['card_name'] = '';
            $card_mon                = 0;
            if (!empty($val['card_id'])) {
                $card_id_s       = explode(',', $val['card_id']);
                $card_named_list = '';
                foreach ($card_list as $key_1 => $value) {
                    if (in_array($value['id'], $card_id_s)) {
                        $card_named_list .= $value['card_name'] . ',';
                        $card_mon        += $value['card_quota'];
                    }
                }
                $list[$key]['card_name'] = rtrim($card_named_list, ',');
            }
            if ($val['is_cc_ok'] == 0) {
                if ($val['card_money'] <> $card_mon && $val['card_money'] > 0) {
//                    $list[$key]['is_cc_ok'] = '卡券使用有点异常' . $val['card_money'] . '--' . $card_mon;
                } else {
                    $list[$key]['is_cc_ok'] = '正常';
                }
            } elseif ($val['is_cc_ok'] == 1) {
                $list[$key]['is_cc_ok'] = '积分扣除失败';
            } elseif ($val['is_cc_ok'] == 2) {
                $list[$key]['is_cc_ok'] = '卡券核销失败';
            }


            if ($val['payment_method'] == 1) {
                $list[$key]['payment_method'] = '现金';
            } elseif ($val['payment_method'] == 2) {
                $list[$key]['payment_method'] = '积分';
            } elseif ($val['payment_method'] == 3) {
                $list[$key]['payment_method'] = '卡劵';
            } elseif ($val['payment_method'] == 4) {
                $list[$key]['payment_method'] = '现金+积分';
            } elseif ($val['payment_method'] == 5) {
                $list[$key]['payment_method'] = '现金+优惠券';
            } elseif ($val['payment_method'] == 6) {
                $list[$key]['payment_method'] = '积分+优惠券';
            } elseif ($val['payment_method'] == 7) {
                $list[$key]['payment_method'] = '现金+积分+卡劵';
            }


            //渠道来源
            $list[$key]['channel_name'] = $channelList[$val['channel']] ?? '未知';
            $order_codes[]              = $val['order_code'];
        }

        if ($is_down == 1) {
            $fields = 'id,order_code,pay_order_code,total_money,money,dlr,name,phone,vin,license_plate,integral,dlr_integral,card_name,card_code,created_date,pay_time,receipt_address,order_status_name,order_commodity_status_name,common_carrier,waybill_number,delivery_time,order_source,sales_source,commodity_pic,mo_commodity_name,commodity_name,car_info,sku_info,price,count,total_price,commodity_code,supplier,sku_code,is_cc_ok';
            $titles = '序号,订单编码,商户订单号,订单金额,实付金额,专营店,姓名,手机号码,vin,车牌,厂家积分,专营店积分,卡券名称,卡劵code,下单时间,支付时间,发货地址,订单状态,商品状态,承运公司,运单编号,发货时间,订单来源,销售来源,商品图片,组合商品名称,商品名称,车辆信息,sku信息,原价,数量,总价,商品编码,供应商,规格编码,积分卡券状态';

//            dd([
//                explode(',', $titles),
//                explode(',', $fields),
//            ]);
//            print_json(1,'',$list);
            PhpExcel::export_csv2($list, $fields, $titles, '订单发货.csv');
        } else {
            $page   = $list->render();
            $where  = [
                'value_type' => 13,
                'is_enable'  => 1
            ];
            $params = [
                'where' => $where,
                'field' => 'value_code,county_name',
                'order' => 'order_no asc'
            ];

            $waybill_where['a.order_code']     = ['in', $order_codes];
            $waybill_where['a.waybill_number'] = ['neq', ''];
            $waybill_where['a.mo_id']          = 0;
            if (!empty($where['b.supplier'])) {
                $waybill_where['a.supplier'] = $where['b.supplier'];
            }
            $waybill_params = [
                'where' => $waybill_where,
                'group' => 'a.order_code,a.waybill_number',
            ];
            $waybill_list   = $order_commodity_model->getOrderCommodityWaybill($waybill_params);
            foreach ($list as $k => $v) {
                $list[$k]['waybill_msg']    = '';
                $list[$k]['is_all_waybill'] = 0;
                $waybill_msg                = $v['common_carrier'] . ' ' . $v['waybill_number'];
                $count                      = $order_commodity_model->where(['order_code' => $v['order_code'], 'mo_id' => 0])->group('waybill_number')->count();
                if (!empty($waybill_list[$v['order_code']])) {
                    $waybill = $waybill_list[$v['order_code']];
                    if (count($waybill) == 1) {
                        $waybill_msg = end($waybill)['common_carrier'] . ' ' . end($waybill)['waybill_number'];
                    } else {
                        $waybill_msg = '共' . count($waybill) . '个包裹';
                    }
                }

                if ($v['is_jd_cloud_order'] == 1) {
                    // 京东云仓订单
                    $jd_warehouse_send_id_arr = [];
                    foreach ($v['orderCommodities'] as $item) {
                        if ($item['mo_id'] == 0) {
                            // 子商品
                            $jd_warehouse_send_id_arr[] = $item['jd_warehouse_send_id'];
                        }
                    }
                    if (!in_array(0, $jd_warehouse_send_id_arr)) {
                        $list[$k]['is_all_waybill'] = 1;
                    }

                } else {
                    if (empty($v['waybill_number'])) continue;
                    if (!empty($waybill_list[$v['order_code']])) {
                        $waybill = $waybill_list[$v['order_code']];
                        if (count($waybill) == $count) {
                            $list[$k]['is_all_waybill'] = 1;
                        }
                    }
                }
                $list[$k]['waybill_msg'] = $waybill_msg;
            }
            $cy_list = $sys_model->getList($params);
            $params  = [
                'where' => ['value_type' => 29, 'is_enable' => 1],
                'field' => 'value_code,county_name',
                'order' => 'order_no asc'
            ];

            $pv_cy_list = $sys_model->getList($params);
            $this->assign('list', $list);
            $this->assign('add_t_jd_url', url('addressToJd'));
            $this->assign('jd_add_url', url('getArea'));
            $this->assign('check_jd_url', url('checkArea'));
            $this->assign('go_fill', url('go_fill_jd'));
            $this->assign('go_con', url('go_con_jd'));
            $this->assign('del_jd_order', url('del_jd_order'));
            $this->assign('go_cancel', url('cancel_jd'));
            $this->assign('cy_list', $cy_list);
            $this->assign('pv_cy_list', $pv_cy_list);
            $this->assign('channel_list', $channelList);
            $this->assign('page', $page);
            $this->assign('role', $this->admin_info['type']);
            $this->assign('query', $query);
            $this->assign('sales_source', $sales_source);
            $this->assign('order_source_list', $order_source_list);
            $this->assign('waybill_list', $waybill_list);
            if ($sales_source == 3) {
                return $this->fetch('order_delivery_static/index_jd');
            } elseif ($sales_source == 4) {
                return $this->fetch('order_delivery_static/index_p33a');

            } elseif ($sales_source == 5) {
                return $this->fetch('order_delivery_static/index_sg');
            } elseif ($sales_source == 6) {
                return $this->fetch('order_delivery_static/index_pv_yc'); // pv云仓
            } else {
                return $this->fetch('order_delivery_static/index');
            }
        }
    }

    public function ajaxGetOrderCommodity()
    {
        $order_code = input('get.order_code');
        $supplier   = input('get.supplier', '');
        if (empty($order_code)) {
            print_json(1, '订单编号不能为空');
        }
        $buOrder = new BuOrder();
        $where   = ['a.order_code' => $order_code, 'b.mo_id' => 0];
        if (in_array($supplier, ['JD', '省广'])) $where['b.supplier'] = $supplier;
        $params                = [
            'field' => 'c.id as commodity_id,c.create_dlr_code as dlr_code,c.commodity_name,c.commodity_code,b.order_code,b.car_info,b.sku_info,b.price,b.count,b.commodity_pic as cover_image,e.sku_code,b.mo_id,b.mo_sub_id',
            'where' => $where,
        ];
        $detail                = $buOrder->getOrderCommodityInfo($params);
        $order_commodity_model = new BuOrderCommodity();

        $commentName = '';
        foreach ($detail as $k => $item) {
            if ($item['mo_sub_id'] != 0) {
                $commentName   = $item['commodity_name'];
                $map           = ['order_code' => $item['order_code'], 'mo_id' => $item['mo_sub_id']];
                $zCommodity    = $order_commodity_model->where($map)->field('id,order_code,count,commodity_name')->find();
                $item['count'] = bcmul($zCommodity['count'], $item['count']); // 主商品数量*子商品数量

            }
            if (!empty($commentName)) {
                $detail[$k]['commodity_name'] = '[' . $commentName . ']      ' . $item['commodity_name'];
            }
        }
        $detail = array_values($detail);
        print_json(0, '', $detail);
    }

    /**
     * 收货
     */
    public function receive()
    {
        $id = input('post.id');
        if (empty($id)) {
            print_json(1, '订单ID不能为空');
        }

        $info = $this->buOrder->getOneByPk($id);

        try {
            //更新订单信息
            $data                 = input('post.');
            $data['order_status'] = 9;
            $data['receive_time'] = date("Y-m-d H:i:s");
            $data['modifier']     = $this->admin_info['dlr_code'];//记录订单修改人
            $this->buOrder->allowField(true)->isUpdate(true)->save($data);

            $net_order = new NetOrder();
            $net_order->orderChange($info['order_code']);

//            //添加订单日志信息
//            $insert['order_code'] = $info['order_code'];
//            $insert['dlr_code'] = $info['dlr_code'];
//            $insert['openid'] = $info['openid'];
//            $insert['vin'] = $info['vin'];
//            $insert['license_plate'] = $info['license_plate'];
//            $insert['name'] = $info['name'];
//            $insert['phone'] = $info['phone'];
//            $insert['source'] = $info['source'];
//            $insert['total_money'] = $info['total_money'];
//            $insert['money'] = $info['money'];
//            $insert['card_id'] = $info['card_id'];
//            $insert['card_code'] = $info['card_code'];
//            $insert['card_money'] = $info['card_money'];
//            $insert['integral'] = $info['integral'];
//            $insert['order_status'] = 9;
//            $insert['payment_method'] = $info['payment_method'];
//            $insert['validity_date_start'] = $info['validity_date_start'];
//            $insert['validity_date_end'] = $info['validity_date_end'];
//            $insert['verification_user'] = $info['verification_user'];
//            $insert['settlement_user'] = $info['settlement_user'];
//            $insert['is_enable'] = $info['is_enable'];
//            $insert['created_date'] = $info['created_date'];
//            $insert['creator'] = $this->admin_info['dlr_code'];//记录订单操作日志的执行者
//            $insert['last_updated_date'] = $info['last_updated_date'];
//            $insert['modifier'] = $info['modifier'];
//            $insert['logistics_mode'] = $info['logistics_mode'];
//            $insert['common_carrier'] = $info['common_carrier'];
//            $insert['waybill_number'] = $info['waybill_number'];
//            $insert['delivery_time'] = $info['delivery_time'];
//            $insert['receipt_address'] = $info['receipt_address'];
//            $this->buOrderDetail->insertGetId($insert);
        } catch (Exception $exception) {
            print_json(1, '操作失败：' . $exception->getMessage());
        }
        print_json(0, '操作成功');
    }

    /**
     * 判断是否已存在物流信息
     */
    public function ajaxMakeSureDeliver()
    {
        $id         = input('get.id');
        $params     = [
            'where' => [
                'id' => $id
            ],
            'field' => 'waybill_number,common_carrier'
        ];
        $order_info = $this->buOrder->getOne($params);
        $result     = getExpressInfo($order_info['waybill_number'], $order_info['common_carrier'], $order_info['phone'] ?? '');

        if ($result['status'] == 0 && !empty($result['result']['list'])) {
            print_json(0, '该订单商品已出库，无法修改发货信息', ['is_change_enable' => 0]);
        }
        print_json(0, '', ['is_change_enable' => 1]);
    }


    //获取京东物流
    public function getTrack($exit = true)
    {
        $sdk_order   = new JdOrderN();
        $order_id    = input('get.id');
        $order_model = new BuOrder();
        $where       = [
            'a.id' => $order_id,
        ];
        $msg         = [];
        $last_stat   = '';
        $county_name = '';
        $wl_id_arr   = [];
        $row         = $order_model->alias("a")->join("t_bu_order_commodity b", "a.order_code=b.order_code")->where($where)->field("b.third_order_id")->find();
        if ($row) {
            $res = $sdk_order->orderTrack($row['third_order_id']);
            if (isset($res['result'])) {
                $res = $res['result'];
            }
            if (!$res) {
                print_json(1, '没有信息!');
            }

            if (isset($res['orderTrack'])) {
                foreach ($res['orderTrack'] as $key => $val) {
                    $msg[]     = [
                        'status' => $val['content'],
                        'time'   => $val['msgTime'],
                    ];
                    $last_stat = $val['content'];
                }
            }

            if (isset($res['waybillCode'])) {
                foreach ($res['waybillCode'] as $k => $v) {
                    $wl_id_arr[] = $v['deliveryOrderId'];
                    $county_name = $v['carrier'];
                }
            }
        }
        $wl_id = implode(',', $wl_id_arr);
        $data  = [
            'deliveryStatus' => $last_stat,
            'commonCarrier'  => $county_name,
            'waybill_number' => $wl_id,
            'tel'            => 'JD',
            'deliveryList'   => $msg
        ];
        print_json(0, '', $data, $exit);
        // sort($

    }

    /**
     * 获取物流信息
     */
    public function ajaxGetLogisticsList()
    {
        $order_commodity_model = new BuOrderCommodity();
        $logistics_type = input('get.logistics_type', 1); // 物流类型

        $supplier              = input('get.supplier', '');
        if (in_array($supplier, BuOrderCommodity::$jd_warehouse_supplier)) {
            $supplierArr = BuOrderCommodity::$jd_warehouse_supplier;
        } else {
            $supplierArr = [$supplier];
        }
        $order_code = input('get.order_code', '');
        $order_model = new BuOrder();
        $order_info = $order_model->where('order_code', $order_code)->field('phone,order_source')->find();
        if ($order_info['order_source'] == 43) {

            $order_commodity = $order_commodity_model->where('order_code', $order_code)->find();
            $order_insure_model = new BuOrderInsure();
            $insure = $order_insure_model->where('order_code', $order_code)->find();
            if ($logistics_type == 1) {
                $common_carrier = $insure['carrier_code'];
                $waybill_number = $insure['waybill_number'];
            } else {
                $common_carrier = $insure['return_carrier_code'];
                $waybill_number = $insure['return_waybill_number'];
            }

            $waybill_list[$order_code][] = [
                'common_carrier' => $common_carrier,
                'third_order_id' => $order_commodity['third_order_id'],
                'waybill_number' => $waybill_number,
                'jd_warehouse_send_id' =>0,
                'phone' => $order_info['phone'],
                'id' => $order_commodity['id'],
            ];
        } else {
            $where      = [
                'a.order_code'     => $order_code,
                'a.supplier'       => ['in', $supplierArr],
                'a.waybill_number' => ['neq', ''],
                'a.mo_id'          => 0
            ];
            if (empty($supplier)) {
                unset($where['a.supplier']);
            }

            $waybill_list     = $order_commodity_model->getOrderCommodityWaybill(['where' => $where, 'group' => 'a.waybill_number']);
        }
        $waybill_msg_list = [];
        if (!empty($waybill_list)) {
            foreach ($waybill_list[$order_code] as $order_info) {
                if ($order_info['common_carrier'] != 'JD' || !$order_info['third_order_id']) {
                    // 有京东订单号才查京东
                    $order_info['waybill_number'] = trim($order_info['waybill_number']);
                    $order_info['common_carrier'] = trim($order_info['common_carrier']);
                    if ($order_info['jd_warehouse_send_id']) {
                        // 云仓订单

                        if ($order_info['common_carrier'] == 'CYS0000010') {
                            // 京东发货
                            $result                         = JdCloudWarehouse::create('jd_cloud_warehouse')->commonQueryOrderTrace($order_info['jd_warehouse_send_id']);
                        } else {
                            // 第三方
                            $waybill_number_arr = explode(',', $order_info['waybill_number']);

                            $result                 = getExpressInfo($waybill_number_arr[0], '', $order_info['phone'] ?? '', $order_info['third_order_id']);

                        }
//                        $jd_list    = $result['result']['list'] ?? [];
//                        $third_list = $third_way_bill['result']['list'] ?? [];
//                        $old_list   = array_merge($jd_list, $third_list);
//                        usort($old_list, function ($a, $b) {
//                            $dateA = new \DateTime($a['time']);
//                            $dateB = new \DateTime($b['time']);
//                            // 返回一个整数，小于 0 表示 $a 应该排在 $b 前面，大于 0 表示 $a 应该排在 $b 后面
//                            return $dateB->getTimestamp() - $dateA->getTimestamp();
//                        });

                    } else {
                        $result = getExpressInfo($order_info['waybill_number'], $order_info['common_carrier'], $order_info['phone'] ?? '');
                    }

                    if ($result['status'] == 0 && isset($result['status'])) {

//                        $deliverystatusarray = [
//                            1 => '在途中',
//                            2 => '派件中',
//                            3 => '已签收',
//                            4 => '派件失败',
//                        ];
                        $deliverystatusarray = BuOrderCommodity::$delivery_status_array;
                        if (isset($result['result']['status_name'])) {
                            $deliverystatus = $result['result']['status_name'];
                        } else {
                            $deliverystatus = $deliverystatusarray[$result['result']['deliverystatus']];

                        }
                        $sys_model = new DbSystemValue();

                        $params = [
                            'where' => [
                                'value_code' => $order_info['common_carrier']
                            ],
                            'field' => 'county_name,remark'
                        ];
                        $cy     = $sys_model->getOne($params);

                        $data = [
                            'deliveryStatus' => $deliverystatus,
                            'commonCarrier'  => $cy['county_name'],
                            'waybill_number' => $order_info['waybill_number'],
                            'tel'            => $cy['remark'] ? $cy['remark'] : '暂无',
                            'deliveryList'   => $result['result']['list']
                        ];

                    }
                } else {
                    $msg         = [];
                    $last_stat   = '';
                    $county_name = '';
                    $wl_id_arr   = [];
                    $sdk_order   = new JdOrderN();
                    $res         = $sdk_order->orderTrack($order_info['third_order_id']);
                    if (isset($res['result'])) {
                        $res = $res['result'];
                    }
                    if (!$res) continue;

                    if (isset($res['orderTrack'])) {
                        foreach ($res['orderTrack'] as $key => $val) {
                            $msg[]     = [
                                'status' => $val['content'],
                                'time'   => $val['msgTime'],
                            ];
                            $last_stat = $val['content'];
                        }
                    }

                    if (isset($res['waybillCode'])) {
                        foreach ($res['waybillCode'] as $k => $v) {
                            $wl_id_arr[] = $v['deliveryOrderId'];
                            $county_name = $v['carrier'];
                        }
                    }
                    $wl_id = implode(',', $wl_id_arr);
                    $data  = [
                        'deliveryStatus' => $last_stat,
                        'commonCarrier'  => $county_name,
                        'waybill_number' => $wl_id,
                        'tel'            => 'JD',
                        'deliveryList'   => $msg
                    ];
                }

                if (!empty($data)) {
                    $where = ['where' => ['order_code' => $order_code, 'supplier' => ['in', $supplierArr], 'waybill_number' => ['in', $data['waybill_number']], 'mo_id' => 0]];
                    if (empty($supplier)) {
                        unset($where['where']['supplier']);
                    }
                    if ($logistics_type == 2) {
                        unset($where['where']['waybill_number']);
                    }
                    $goods = $order_commodity_model->getList($where);
                    foreach ($goods as $k => $v) {
                        $goods[$k]['sku_list'] = explode(',', $v['sku_info']);
                    }
                    $waybill_msg_list[$order_info['id']] = ['goods' => $goods, 'delivery' => $data];
                }
            }
        }
        $where = ['where' => ['order_code' => $order_code, 'supplier' => $supplier, 'waybill_number' => '', 'mo_id' => 0]];
        if (empty($supplier)) {
            unset($where['where']['supplier']);
        }

        $no_goods = $order_commodity_model->getList($where); // 未发货商品
        if (!empty($no_goods)) {
            foreach ($no_goods as $k => $v) {
                $no_goods[$k]['sku_list']                    = explode(',', $v['sku_info']);
                $no_goods[$k]['order_commodity_status_name'] = BuOrderCommodity::$order_commodity_status[$v['order_commodity_status']] ?? '';
            }
            $waybill_msg_list['no_goods'] = $no_goods;
        }

        if (empty($waybill_msg_list)) print_json(1, '暂无物流信息');
        print_json(0, '', $waybill_msg_list);
    }

    /**
     * 获取发货信息：物流公司，物流单号
     */
    public function ajaxGetDeliver()
    {
        $id = input('get.id');
        if (empty($id)) {
            print_json(1, '订单ID不能为空');
        }
        $params = [
            'field' => 'id,common_carrier,waybill_number,ticket_no',
            'where' => ['id' => $id]
        ];
        $info   = $this->buOrder->getOne($params);
        print_json(0, '', $info);
    }

    /**
     * 发货、编辑
     */
    public function deliver()
    {
        $id = input('post.id');
        if (empty($id)) {
            print_json(1, '订单ID不能为空');
        }

        $info = $this->buOrder->getOneByPk($id);
        if (!(in_array($info['order_status'], [2, 4, 11, 12]) && $info['logistics_mode'] == 2)) {
            print_json(1, '状态错误');
        }

        $post_data      = input('post.');
        $ids            = $post_data['order_commodity_id'] ?? [];
        $common_carrier = $post_data['common_carrier'] ?? '';
        $waybill_number = $post_data['waybill_number'] ?? '';
        if (empty($ids) || empty($common_carrier) || empty($waybill_number)) print_json(1, '无发货商品/物流信息');
        $order_commodity_model = new BuOrderCommodity();
        $in_stock              = new Inventory();
        try {
            $order_commodity_list = $order_commodity_model->where(['id' => ['in', $ids]])->select();
            $commodity_list       = [];
            foreach ($order_commodity_list as $key => $v) {
                if ($v['common_carrier'] == $common_carrier && $v['waybill_number'] == $waybill_number) continue;
                $update_data  = ['common_carrier' => $common_carrier, 'waybill_number' => $waybill_number, 'delivery_time' => date('Y-m-d H:i:s'), 'order_commodity_status' => 4]; // 取最后一条
                $operate_list = json_decode($v['operate_list'], true) ?? [];
                array_push($operate_list, $update_data);
                $update_data['operate_list'] = json_encode_cn($operate_list);
                $commodity_list[]            = $v;
                $order_commodity_model->saveData($update_data, ['id' => $v['id']]);
            }
            //发货判断是否更新流水 start
            $in_stock->outbound_stock($commodity_list, $this->admin_info['username']);
            //发货判断是否更新流水 end

            if ($info['order_status'] != 4) {
                //更新订单信息
                $where['id']            = input('post.id');
                $data['order_status']   = 4;
                $data['common_carrier'] = input('post.common_carrier');
                $data['waybill_number'] = input('post.waybill_number');
                $data['ticket_no']      = input('post.ticket_no');
                $data['delivery_time']  = date("Y-m-d H:i:s");
                $data['modifier']       = $this->admin_info['username'];//记录订单修改人
                $this->buOrder->saveData($data, $where);

                $net_order = new NetOrder();
                $net_order->orderChange($info['order_code']);
            }

            //添加订单日志信息
//            $insert['order_code'] = $info['order_code'];
//            $insert['dlr_code'] = $info['dlr_code'];
//            $insert['openid'] = $info['openid'];
//            $insert['vin'] = $info['vin'];
//            $insert['license_plate'] = $info['license_plate'];
//            $insert['name'] = $info['name'];
//            $insert['phone'] = $info['phone'];
//            $insert['source'] = $info['source'];
//            $insert['total_money'] = $info['total_money'];
//            $insert['money'] = $info['money'];
//            $insert['card_id'] = $info['card_id'];
//            $insert['card_code'] = $info['card_code'];
//            $insert['card_money'] = $info['card_money'];
//            $insert['integral'] = $info['integral'];
//            $insert['order_status'] = 4;
//            $insert['payment_method'] = $info['payment_method'];
//            $insert['validity_date_start'] = $info['validity_date_start'];
//            $insert['validity_date_end'] = $info['validity_date_end'];
//            $insert['verification_user'] = $info['verification_user'];
//            $insert['settlement_user'] = $info['settlement_user'];
//            $insert['is_enable'] = $info['is_enable'];
//            $insert['created_date'] = $info['created_date'];
//            $insert['creator'] = $this->admin_info['dlr_code'];//记录订单操作日志的执行者
//            $insert['last_updated_date'] = $info['last_updated_date'];
//            $insert['modifier'] = $info['modifier'];
//            $insert['logistics_mode'] = $info['logistics_mode'];
//            $insert['common_carrier'] = $info['common_carrier'];
//            $insert['waybill_number'] = $info['waybill_number'];
//            $insert['delivery_time'] = $info['delivery_time'];
//            $insert['receipt_address'] = $info['receipt_address'];
//            $this->buOrderDetail->insertGetId($insert);
        } catch (Exception $exception) {
            print_json(1, '操作失败：' . $exception->getMessage());
        }
        print_json(0, '操作成功');
    }

    // 导入批量发货-自营订单
    public function importLogistics()
    {
        $file = request()->file('excel_data');
        $res  = PhpExcel::import_csv($file);
        // var_dump($res);
        if ($res['error']) {
            print_json(1, $res['msg']);
        }

        $data = $res['data'];
        unset($data[0]);
        $carrier_list = (new DbSystemValue())->where(['value_type' => 13, 'is_enable' => 1])->column('county_name', 'value_code');
        $import_list  = [];
        $sku_codes    = [];
        foreach ($data as $key => $val) { // 按订单号整合导入数据
            if (empty($val[0]) || empty($val[2]) || empty($val[3]) || (!isset($carrier_list[$val[3]]) && !in_array($val[3], $carrier_list))) continue;
            if (empty($val[1])) {
                $import_list[trim($val[0])][] = [
                    'sku_code'       => trim($val[1]),
                    'waybill_number' => trim($val[2]),
                    'common_carrier' => trim($val[3]),
                ];
            } else {
                $import_list[trim($val[0])][trim($val[1])] = [
                    'sku_code'       => trim($val[1]),
                    'waybill_number' => trim($val[2]),
                    'common_carrier' => trim($val[3]),
                ];
            }
            $sku_codes[] = trim($val[1]);
        }
        $supplier_arr = array_merge(['省广', 'JD'], BuOrderCommodity::$jd_warehouse_supplier);

        $order_commodity_model = new BuOrderCommodity();
        $order_commodity_list  = $order_commodity_model
            ->where([
                'order_code'     => ['in', array_keys($import_list)],
                'third_sku_code' => ['in', $sku_codes],
                'supplier'       => ['not in', $supplier_arr],
                'mo_id'          => 0,
                'is_enable'      => 1
            ])->select();

        $commodity_list = [];
        foreach ($order_commodity_list as $key => $val) { // 按订单号整合订单商品
            $commodity_list[$val['order_code']][] = $val;
        }

        $net_order = new NetOrder();
        $in_stock  = new Inventory();
        foreach ($import_list as $order_code => $import) {
            if (empty($commodity_list[$order_code])) continue;
            $order_commodity = $commodity_list[$order_code];
            //发货判断是否更新流水 start
            $in_stock->outbound_stock($order_commodity, $this->admin_info['username']);
            //发货判断是否更新流水 end
            if (count($order_commodity) == 1) { // 只有一个订单商品直接更新物流
                $order_commodity_info = end($order_commodity);
                $update_data          = [
                    'common_carrier'         => end($import)['common_carrier'],
                    'waybill_number'         => end($import)['waybill_number'],
                    'order_commodity_status' => 4,
                    'delivery_time'          => date('Y-m-d H:i:s'),
                ]; // 取最后一条
                $operate_list         = json_decode($order_commodity_info['operate_list'], true) ?? [];
                array_push($operate_list, $update_data);
                $update_data['operate_list'] = json_encode_cn($operate_list);
                $order_commodity_model->saveData($update_data, ['id' => $order_commodity_info['id']]);
                $one_waybill = ['data' => $update_data];
            } else {// 多个订单商品处理
                $is_one_waybill = 1;
                $tmp            = [];
                foreach ($import as $imp) {
                    if (!empty($tmp) && ($tmp != $imp)) {
                        $is_one_waybill = 0;
                    }
                    $tmp = $imp;
                }
                if ($is_one_waybill == 1) { // 导入进来的信息都是同一个物流单号时
                    foreach ($order_commodity as $vl) {
                        $update_data  = [
                            'common_carrier'         => $tmp['common_carrier'],
                            'waybill_number'         => $tmp['waybill_number'],
                            'order_commodity_status' => 4,
                            'delivery_time'          => date('Y-m-d H:i:s'),
                        ];
                        $operate_list = json_decode($vl['operate_list'], true) ?? [];
                        array_push($operate_list, $update_data);
                        $update_data['operate_list'] = json_encode_cn($operate_list);
                        $order_commodity_model->saveData($update_data, ['id' => $vl['id']]);
                        $one_waybill = ['data' => $update_data];
                    }
                } else { // 存在不同物流单号
                    $update_data_all = [];
                    foreach ($import as $imp) {
                        foreach ($order_commodity as $vl) {
                            $update_data = [
                                'common_carrier'         => $imp['common_carrier'],
                                'waybill_number'         => $imp['waybill_number'],
                                'order_commodity_status' => 4,
                                'delivery_time'          => date('Y-m-d H:i:s'),
                            ];

                            $operate_list = json_decode($vl['operate_list'], true) ?? [];
                            array_push($operate_list, $update_data);
                            $update_data['operate_list'] = json_encode_cn($operate_list);

                            if (!empty($imp['sku_code'])) { // 相同规格编码取最后一条物流
                                if ($vl['third_sku_code'] != $imp['sku_code']) continue;
                                $update_data_all[$vl['third_sku_code']] = ['data' => $update_data, 'where' => ['order_code' => $vl['order_code'], 'third_sku_code' => $vl['third_sku_code'], 'mo_id' => 0]];
                            } else { // 空规格编码取最后一条
                                $update_no_code = ['data' => $update_data, 'where' => ['order_code' => $vl['order_code'], 'third_sku_code' => '', 'mo_id' => 0]];
                            }
                        }
                    }
                    if (!empty($update_no_code)) $update_data_all[] = $update_no_code;
                    foreach ($update_data_all as $v) {
                        $order_commodity_model->saveData($v['data'], $v['where']);
                    }
                    $one_waybill = array_slice($update_data_all, 0, 1) ?? [];
                    $one_waybill = end($one_waybill);
                }
            }
            $order = $this->buOrder->where(['order_code' => $order_code])->find();
            if (empty($order['waybill_number']) && !empty($one_waybill)) {
                $this->buOrder->where(['id' => $order['id']])->update(['waybill_number' => $one_waybill['data']['waybill_number'], 'common_carrier' => $one_waybill['data']['common_carrier'], 'delivery_time' => $one_waybill['data']['delivery_time'], 'order_status' => 4, 'last_updated_date' => date('Y-m-d H:i:s'), 'modifier' => $this->admin_info['username']]);
                $net_order->orderChange($order_code);
            }

        }
//        $net_order = new NetOrder();
//        foreach ($data as $key => $val) {
//            $order_code = trim($val[0]);
//            $this->buOrder->where(['order_code' => $order_code])->update(['waybill_number' => trim($val[1]), 'common_carrier' => trim($val[2]), 'delivery_time' => date('Y-m-d H:i:s'), 'order_status' => 4, 'last_updated_date' => date('Y-m-d H:i:s'), 'modifier' => $this->admin_info['username']]);
//            $net_order->orderChange($order_code);
//        }
        print_json(0, '导入成功');
    }

    public function addressToJd()
    {
        $address     = input('address');
        $id          = input('id');
        $jd_sdk      = new JdOrderN();
        $addArr      = $jd_sdk->jdAddressId($address);
        $order       = new BuOrder();
        $order_where = ['a.id' => $id, 'b.supplier' => 'JD', 'a.order_status' => ['in', [2, 11, 12]]];
//        $order_where = ['a.id'=>$id];
        $list     = $order->alias('a')->join('t_bu_order_commodity b', 'a.order_code=b.order_code')->where($order_where)->field('b.commodity_name,b.actual_price,b.mo_id,b.count,b.actual_point,b.actual_use_money,b.mail_price,a.order_code,a.parent_order_code')->select();
        $order_jd = [];

        if ($list) {
            $order_jd_model = new BuOrderJd();
            $where          = ['shop_order_id' => $list[0]['parent_order_code'], 'result_code' => ['<>', '0003'], 'error_code' => '0001', 'is_enable' => 1];
//            $where    = ['shop_order_id' => $list[0]['order_code'], 'result_code' => ['<>', '0003'], 'error_code' => '0001', 'is_enable' => 1];
            $order_jd = $order_jd_model->getOne(['where' => $where, 'order' => 'id desc']);
            if ($order_jd['shop_order_id'] != $list[0]['order_code']) {
                $order_jd['shop_order_id'] = $list[0]['order_code'];
                $order_jd_model->saveData(['shop_order_id' => $list[0]['order_code']], ['id' => $order_jd['id']]);
            }
        }
        $moCommodityName = '';
        foreach ($list as $key => $value) {
            if ($value['mo_id'] != 0) {
                $moCommodityName = $value['commodity_name'];
                unset($list[$key]);
                continue;
            }
            $list[$key]['mo_commodity_name'] = $moCommodityName;
        }
        $list = array_values($list);
        print_json(0, 'ok', ['add' => $addArr, 'goods_list' => $list, 'order_jd' => $order_jd]);
//        if($addArr){
//            print_json(0,'ok',$addArr);
//        }else{
//            print_json(1,'识别不到,手工选择');
//        }
    }

    //获取地址
    public function getArea()
    {
        $area_type = input('area_type');
        $area_id   = input('area_id');
        $jd_sdk    = new JdOrderN();
        switch ($area_type) {
            case 1:
                $res = $jd_sdk->getProvince();
                break;
            case 2:
                $res = $jd_sdk->getCity($area_id);
                break;
            case 3:
                $res = $jd_sdk->getCounty($area_id);
                break;
            case 4:
                $res = $jd_sdk->getTown($area_id);
                break;
            default:
                $res = [];
        }
        $data = [];
        if (isset($res['resultCode']) && $res['resultCode'] == '0000') {
            $data = array_flip($res['result']);
        }
        print_json(0, 'ok', $data);
    }

    public function checkArea()
    {
        $area_ids = input('area_ids');
        $area_arr = explode('_', $area_ids);
        $jd_sdk   = new JdOrderN();
        $params   = [
            'provinceId' => $area_arr[0],
            'cityId'     => $area_arr[1],
            'countyId'   => $area_arr[2],
            'townId'     => $area_arr[3],
        ];
        $res      = $jd_sdk->checkArea($params);
        if (isset($res['resultCode']) && $res['resultCode'] == '0000') {
            print_json(0, '地址可以');
        } else {
            print_json(1, $res['resultCode']);
        }

    }

    //下单+占库存
    public function go_fill_jd()
    {
        $id                = input('id');
        $name              = input('order_name');
        $phone             = input('order_phone');
        $address           = input('order_address');
        $area              = input('areas');
        $area_cn           = input('areas_cn');
        $area_arr          = explode('_', $area);
        $order_where       = ['a.id' => $id, 'b.supplier' => 'JD', 'a.order_status' => ['in', [2, 11, 12]]];
        $order_model       = new BuOrder();
        $order_goods_model = new BuOrderCommodity();
        $list              = $order_model->alias('a')->join('t_bu_order_commodity b', 'a.order_code=b.order_code')->where($order_where)->field('b.commodity_name,b.actual_price,b.count,a.order_code,b.id,b.third_sku_code,b.tax_code,b.third_order_id')->select();
        if (!$list) {
            print_json(1, '订单异常或者商品异常');
        }
//        if(config('app_status')=="develop"){
//            $pay_type='4';
//            $isUseBalance=1;
//        }else{
//            $pay_type='101';
//            $isUseBalance=0;
//        }
        $pay_type     = '101';
        $isUseBalance = 0;

        $order_price  = [];
        $order_code   = '';
        $sku          = [];
        $tax_code_arr = [];
        $all_count    = 0;
        foreach ($list as $val) {
            if ($val['third_order_id']) {
                print_json(1, '已下单请点库存');

            }
            $order_code = $val['order_code'];
            $need_gif   = true;
            if (isset($val['gift'])) {
                if ($val['gift'] == 2) {
                    $need_gif = false;
                }
            }
            $sku[]                                = [
                'skuId'      => $val['third_sku_code'],
                'num'        => $val['count'],
                'bNeedAnnex' => true,
                'bNeedGift'  => false,
            ];
            $tax_code_arr[$val['third_sku_code']] = $val['tax_code'];
            $all_count                            += $val['count'];

        }
        $sku_redis           = 'order_go_fill_jd-lock' . $order_code;
        $user_have_buy_redis = redis($sku_redis);
        $redis_lock          = getRedisLock($sku_redis, 60);
        if (!$redis_lock) {
            print_json(1, '不要重复下单');
        }
        if ($user_have_buy_redis) {
            print_json(1, '已经下单了');
        }

        $params = [
            'thirdOrder' => $order_code,
            'sku'        => json_encode($sku),
            'name'       => $name,
            'province'   => $area_arr[0],
            'city'       => $area_arr[1],
            'county'     => $area_arr[2],
            'town'       => $area_arr[3],
            'address'    => $address,
            'mobile'     => $phone,
            //剩下参数在基础数据里
        ];

        $sdk_order = new JdOrderN();
        $result    = $sdk_order->submitOrder($params);
//        var_dump(http_build_query($params));
//        var_dump($result);die();

        $order_jd_model           = new BuOrderJd();
        $order_commodity_jd_model = new BuOrderCommodityJd();
        $order_jd_data            = [
            'error_code'    => $result['resultCode'],
            'content'       => json_encode_cn($result),
            'sent_note'     => json_encode_cn($params),
            'ht_address'    => $area_cn,
            'ht_address_id' => $area,
            'shop_order_id' => $order_code,
            'order_id'      => isset($result['result']['jdOrderId']) ? $result['result']['jdOrderId'] : '',
        ];
        $res_id                   = $order_jd_model->insertGetId($order_jd_data);
//        var_dump($res_id);die();
        if ($result['resultCode'] == '0001') {
            redis($order_code, 1, 1800);//缓存半个小时记录用户购买过信息
            $order         = $result['result'];
            $insert_goods  = [];
            $goods_sku_arr = [];
            $toEnd         = count($order['sku']);
            $have_f_prive  = 0;
            $o_jd_price    = 0;
            foreach ($order['sku'] as $o_sku_val) {
                $o_jd_price += $o_sku_val['price'] * $o_sku_val['num'];
            }
            foreach ($order['sku'] as $val) {
                $f_price        = 0;
                $insert_goods[] = [
                    'shop_order_id' => $order_code,
                    'sku_id'        => $val['skuId'],
                    'category'      => $val['category'],
                    'goods_num'     => $val['num'],
                    'price'         => $val['price'],
                    'jd_goods_type' => $val['type'],
                    'oid'           => $val['oid'],
                    'goods_name'    => $val['name'],
                    'order_id'      => $order['jdOrderId'],
                    'sales_price'   => '000',
                    'image_path'    => '000',
                    'user_id'       => '000',
                    'tax_price'     => $val['taxPrice'],
                    'naked_price'   => $val['nakedPrice'],
                    'tax_code'      => $tax_code_arr[$val['skuId']] ?? '',
                ];
                if ($val['type'] == 0) {
                    if (0 === --$toEnd) {
                        $f_price = $order['freight'] - $have_f_prive;
                    } else {
                        $f_price      = round($val['num'] * $val['price'] * $order['freight'] / $o_jd_price, 2);
                        $have_f_prive += $f_price;
                    }
                }

                $order_goods_model->saveData(['tax' => $val['tax'], 'cost_price' => $val['price'], 'third_order_id' => $order['jdOrderId'], 'parent_third_order_id' => $order['jdOrderId'], 'third_mail_price' => $f_price], ['order_code' => $order_code, 'third_sku_code' => $val['skuId']]);
            }
            $order_commodity_jd_model->insertAll($insert_goods);
            $word = "下单成功!";

            $res = $sdk_order->confirmOrder($order['jdOrderId']);

            $order_jd_data = [
                'result_code'    => $res['resultCode'],
                'result_message' => json_encode($res)
            ];
//            $ww =['id'=>$res_id];
            $order_jd_model->saveData($order_jd_data, ['order_id' => $order['jdOrderId']]);
            if (isset($res['success']) && $res['success']) {
                $word .= "占库存成功!";

                $order_model->saveData(['order_status' => 4, 'modifier' => $this->admin_info['username'], 'delivery_time' => date('Y-m-d H:i:s')], ['order_code' => $order_code]);
                $net_order   = new NetOrder();
                $update_data = ['order_commodity_status' => 3, 'common_carrier' => '', 'waybill_number' => '', 'delivery_time' => date('Y-m-d H:i:s')];
                $order_goods_model->saveData(['modifier' => $this->admin_info['username'], 'order_commodity_status' => 3, 'operate_list' => json_encode_cn($update_data)], ['order_code' => $order_code, 'third_order_id' => ['<>', ''], 'supplier' => 'JD']);
                $net_order->orderChange($order_code);
            } else {
                $word .= "占库存失败!";
            }
            print_json(0, $word);
        } else {
            print_json(1, $result['resultMessage']);
        }
    }


    //预占库存之后占库存操作
    public function go_con_jd()
    {
        $order_id       = input('order_id');
        $sdk_order      = new JdOrderN();
        $order_jd_model = new BuOrderJd();
        $where          = ['order_id' => $order_id, 'result_code' => ['<>', '0003'], 'error_code' => '0001', 'is_enable' => 1];
        $order_jd       = $order_jd_model->getOne(['where' => $where, 'order' => 'id desc']);
        if (!$order_jd) {
            print_json(1, '没有要占的');
        }
        $res = $sdk_order->confirmOrder($order_id);

        $order_jd_data = [
            'result_code'    => $res['resultCode'],
            'result_message' => json_encode($res)
        ];
//            $ww =['id'=>$res_id];
        $order_jd_model->saveData($order_jd_data, ['id' => $order_jd['id']]);
        $word = '';
        if (isset($res['success']) && $res['success']) {
            $word        = "占库存成功!";
            $order_model = new BuOrder();
            $order_model->saveData(['order_status' => 4, 'modifier' => $this->admin_info['username'], 'delivery_time' => date('Y-m-d H:i:s')], ['order_code' => $order_jd['shop_order_id']]);
            $net_order = new NetOrder();
            $net_order->orderChange($order_jd['shop_order_id']);
            print_json(0, $word);
        } else {
            $word = "占库存失败!";
            print_json(1, $word);

        }
    }


    /**
     * 删除京东订单
     */
    public function del_jd_order()
    {
        $order_code        = input('order_code');
        $order_jd_model    = new BuOrderJd();
        $order_goods_model = new BuOrderCommodity();
        $order_jd          = $order_jd_model->getOne(['where' => ['shop_order_id' => $order_code, 'is_enable' => 1]]);
        if ($order_jd) {
            $re1 = $order_jd_model->saveData(['is_enable' => 0, 'modifier' => $this->admin_info['username']], ['id' => $order_jd['id']]);
            $re2 = $order_goods_model->saveData(['third_order_id' => '', 'modifier' => $this->admin_info['username']], ['third_order_id' => $order_jd['order_id']]);
            if ($re1 && $re2) {
                print_json(0, '删除成功');
            } else {
                print_json(1, '删除失败');
            }
        } else {
            print_json(1, '订单不存在');

        }
    }





    //取消京东订单
    public function cancel_jd()
    {
        $net_order  = new NetOrder();
        $order_code = input('order_code');
        $res        = $net_order->cancel_jd($order_code, $this->admin_info['username']);
        if ($res['code'] == '200') {
            print_json(0, '取消订单成功');
        } else {
            print_json(1, $res['msg']);
        }
//        $order_jd_model =  new BuOrderJd();
//        $where = ['shop_order_id'=>$order_code,'result_code'=>'','error_code'=>'0001','is_enable'=>1];
//        $order_jd = $order_jd_model->getOne(['where'=>$where,'order'=>'id desc']);
//        if(!$order_jd){
//            print_json(1,'订单状态异常');
//        }
//        $order_id = $order_jd['order_id'];
//        $sdk_order = new JdOrderN();
//        $res =  $sdk_order->cancel($order_id);
//        if (!$res['result']) {
//            Logger::error('jd-cancel-error',$res);
//            print_json(1,isset($res['resultMessage'])?$res['resultMessage']:'取消订单失败');
//        }else{
//            $order_jd_model->saveData(['is_enable'=>0,'modifier'=>$this->admin_info['username'],'last_updated_date'=>date('Y-m-d H:i:s')],['shop_order_id'=>$order_code]);
//
//            print_json(0,'取消订单成功');
//        }

    }

    public function jifenFailOrder()
    {
        $buOrder        = new BuOrder();
        $order_code     = trim(input('get.order_code'));
        $dlr_code       = trim(input('get.dlr_name'));
        $order_status   = input('get.order_status');
        $logistics_mode = input('get.logistics_mode');
        $phone          = trim(input('get.phone'));

        $where = [
            'is_cc_ok'       => ['neq', 0],
            'a.order_status' => ['not in', [1, 3, 8]]
        ];
        $query = [
            'dlr_name'           => $dlr_code,
            'order_status_array' => explode(",", input('get.order_status')),
            'order_status'       => $order_status,
            'logistics_mode'     => $logistics_mode,
            'order_code'         => $order_code,
            'phone'              => $phone,
        ];

        if (!empty($dlr_code)) {
            $where['a.dlr_code'] = $dlr_code;

        }
        if (!empty($order_code)) {
            $where['a.order_code'] = $order_code;

        }
        if (!empty($order_status)) {
            $where['a.order_status'] = ['in', explode(",", input('get.order_status'))];
        }
        if (!empty($logistics_mode)) {
            $where['a.logistics_mode'] = $logistics_mode;
        }
        if (!empty($phone)) {
            $where['a.phone'] = $phone;
        }
        if ($this->admin_info['type'] == 2) {
            $where['a.dlr_code'] = $this->admin_info['dlr_code'];
        }

        $params = [
            'where' => $where,
            'query' => $query,
            'order' => 'a.id desc',
            'field' => "a.id,a.payment_method,a.parent_order_code,a.user_id,a.gift_score,a.refund_money,a.shop_integral,a.payment_method,a.order_code,a.channel,a.pay_order_code,a.total_money,a.money,a.name,a.phone,a.vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.order_status,a.logistics_mode,a.order_source,a.cashier_trade_no2,a.cashier_trade_no,a.front_money,a.front_pay_time,a.pre_use_money,b.car_info,b.commodity_name,b.pre_sale_id,b.actual_price,e.dlr_code,e.dlr_name,(case f.type when 1 then '平台' when 2 then '专营店' when 3 then '官微' end) as type,(case b.sales_source when 0 then '旧数据' when 1 then '平台自营销售' when 2 then '专营店销售' when 3 then '官微销售' when 4 then '积分商城'  end) as sales_source,a.receipt_address,a.pay_time,a.mail_price,k.money hmoney,k.preferential_money,a.is_cc_ok,d.price old_price,b.b_act_price,b.count,b.all_dis,b.price,b.count,b.price*b.count as total_price,b.id bid,card_money,b.mail_price goods_mail_price,a.pay_order_code2,a.cashier_trade_no,a.cashier_trade_no2,b.third_sku_code,b.third_order_id,b.tax_code,b.tax,b.supplier,b.cost_price,b.third_mail_price",
            'group' => 'b.id'  //2020-12-21 10:12:23 用id才能显示同时买多个商品的
        ];

        $list = $buOrder->getOrderPaginate($params);

        foreach ($list as $key => $val) {
            // 正常订单
            if ($val['pre_sale_id'] == 0) {
                $orderSettlements                    = $val->orderSettlements;
                $cashier_settlement_no               = array_column($orderSettlements, 'cashier_settlement_no');
                $list[$key]['cashier_settlement_no'] = implode(',', $cashier_settlement_no);
            } else {
                // 预售订单
                if (!empty($val['cashier_trade_no2'])) {
                    $orderSettlement2                     = $val->orderSettlementByTradeNo2;
                    $list[$key]['cashier_settlement_no2'] = $orderSettlement2['cashier_settlement_no'] ?? '';
                } else {
                    $list[$key]['cashier_settlement_no2'] = '';
                }

                // 尾款
                if (!empty($val['cashier_trade_no'])) {
                    $orderSettlement                     = $val->orderSettlementByTradeNo;
                    $list[$key]['cashier_settlement_no'] = $orderSettlement['cashier_settlement_no'] ?? '';
                } else {
                    $list[$key]['cashier_settlement_no'] = '';
                }

            }

            $list[$key]['dlr']                 = $val['dlr_code'] . ' - ' . $val['dlr_name'];
            $list[$key]['order_status_name']   = $buOrder::orderStatus($val['order_status']);
            $list[$key]['logistics_mode_name'] = $val['logistics_mode'] == 1 ? '自提' : '快递';
            $list[$key]['yh_total_money']      = ($val['price'] - $val['actual_price']) * $val['count'];// 商品活动优惠金额
            if (in_array($val['order_status'], [1, 3, 5, 6, 8])) {
                $list[$key]['money'] = '0.00';
            }

            if ($val['order_status'] == 2 && $val['logistics_mode'] == 1) {
                $list[$key]['dq_time'] = date('Y-m-d H:i:s', strtotime(sprintf("%s +1 month", $val['pay_time'])));
            } else {
                $list[$key]['dq_time'] = '';
            }

            $order_total_money               = 0;  // 订单总价
            $order_all_dis                   = 0; // 订单活动总优惠金额
            $list[$key]['order_total_money'] = $order_total_money;
            $list[$key]['order_all_dis']     = $order_all_dis;
            // 订单金额 =订单总价 + 订单运费 - 订单卡券总优惠金额 - 订单活动总优惠金额
            $list[$key]['order_money'] = $order_total_money + $val['mail_price'] - $val['card_money'] - $order_all_dis;
        }

        $page = $list->render();
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('role', $this->admin_info['type']);
        $this->assign('query', $query);
        $this->assign('order_status_list', $buOrder::orderStatus());
        $this->assign('role', $this->admin_info['type']);
        return $this->fetch('jifen_fail_order');
    }


    //批量订单--订单列表
    public function batch_order_list()
    {
        $order_code     = input('ids');
        $order_jd_model = new BuOrderJd();
        $where          = ['b.order_code' => ['in', explode(',', $order_code)], 'a.result_code' => ['<>', '0003'], 'a.error_code' => '0001', 'a.is_enable' => 1];
        $list           = $order_jd_model->alias('a')->join('t_bu_order b ', 'a.shop_order_id=b.parent_order_code or a.shop_order_id = b.order_code')->where($where)->field('b.receipt_address,a.*')->select();
        if ($list) {
            print_json(0, $order_jd_model->getLastSql(), $list);
        } else {
            print_json(1, '没数据');
        }
    }

    //批量订单操作
    public function batch_order()
    {
        $order_code     = input('ids');
        $sdk_order      = new JdOrderN();
        $order_jd_model = new BuOrderJd();
        $order_model    = new BuOrder();
        $order_list     = $order_model->getList([
                'where' => ['order_code' => ['in', explode(',', $order_code)], 'is_enable' => 1]
            ]
        );
        $order_code_p   = [];
        $order_code_arr = [];
        foreach ($order_list as $v) {
            $order_code_p[]                          = $v['parent_order_code'];
            $order_code_arr[$v['parent_order_code']] = $v['order_code'];
        }

        $where          = ['shop_order_id' => ['in', $order_code_p], 'result_code' => ['<>', '0003'], 'error_code' => '0001', 'is_enable' => 1];

        $order_jds      = $order_jd_model->getList(['where' => $where, 'order' => 'id desc']);
        $rd_name        = 'ods-jd-batch' . $order_code;
        if (!getRedisLock($rd_name, 60)) {
            print_json(1, '一分钟只能点一次');
        }
        if (!$order_jds) {
            print_json(1, '没有要占的', $order_jd_model->getLastSql());
        }
        $fail_orders = '';
        $word        = '全部发货完成';
        foreach ($order_jds as $vv) {
            $vv['shop_order_id'] = $order_code_arr[$vv['shop_order_id']]??$vv['shop_order_id'];
            $res           = $sdk_order->confirmOrder($vv['order_id']);
            $order_jd_data = [
                'shop_order_id'    => $order_code_arr[$vv['shop_order_id']]??$vv['shop_order_id'],
                'result_code'    => $res['resultCode'],
                'result_message' => json_encode($res)
            ];
//            $ww =['id'=>$res_id];
            $order_jd_model->saveData($order_jd_data, ['id' => $vv['id']]);
            if (isset($res['success']) && $res['success']) {
                $word        = "占库存成功!";
                $order_model = new BuOrder();
                $order_model->saveData(['order_status' => 4, 'modifier' => $this->admin_info['username'], 'delivery_time' => date('Y-m-d H:i:s')], ['order_code' => $vv['shop_order_id']]);
                $net_order = new NetOrder();
                $net_order->orderChange($vv['shop_order_id']);
            } else {
                $word        = "以下订单发货失败：<br/>";
                $fail_orders .= $vv['shop_order_id'] . ',';
            }
        }
        print_json(0, $word . $fail_orders);

    }

    // 获取订单商品信息
    public function getOrderCommodity()
    {
        $order_code            = input('order_code', '');
        $order_commodity_model = new BuOrderCommodity();
        $list                  = $order_commodity_model
            ->alias('a')
            ->field('a.*,b.commodity_code,((a.count - a.after_commodity_num)* d.delivery_coefficient) as count_num')
            ->join('t_db_commodity b', 'a.commodity_id=b.id')
            ->join('t_db_commodity_set_sku c', 'a.sku_id=c.id')
            ->join('t_db_commodity_sku d', 'c.commodity_sku_id=d.id')
            ->where(['a.order_code' => $order_code, 'a.mo_id' => 0])
            ->select();
//        dd($order_commodity_model->getLastSql());
        $return_dta = [
            'jingdong'   => [],
            'shenguang'  => [],
            'other'      => [],
            'pv'         => [],
            'ot_no_send' => 0,
            'jd_no_send' => 0,
            'sg_no_send' => 0,
            'pv_no_send' => 0,
        ];
        foreach ($list as $k => $v) {
            if ($v['mo_sub_id'] != 0) {
                // 查询主商品
                $map = [
                    'order_code' => $v['order_code'],
                    'mo_id' => $v['mo_sub_id']
                ];
                $zCommodity = $order_commodity_model->where($map)->field('count')->find();
                $list[$k]['count_num'] = $zCommodity['count'] * $v['count_num'];
            }
            $common_carrier_name = '';
            if (!empty($v['common_carrier'])) {
                $system_model        = new DbSystemValue();
                $map                 = ['value_type' => 29, 'value_code' => $v['common_carrier'], 'is_enable' => 1];
                $common_carrier_name = $system_model->where($map)->value('county_name');
            }
            $v['common_carrier_name']         = $common_carrier_name;
            $v['order_commodity_status_name'] = BuOrderCommodity::$order_commodity_status[$v['order_commodity_status']] ?? '';
            if ($v['supplier'] == 'JD') {
                $return_dta['jingdong'][] = $v;
                if (in_array($v['order_commodity_status'], [1, 2])) $return_dta['jd_no_send'] = 1;
            } elseif ($v['supplier'] == '省广') {
                $return_dta['shenguang'][] = $v;
                if (in_array($v['order_commodity_status'], [1, 2])) $return_dta['sg_no_send'] = 1;
            } elseif (in_array($v['supplier'], BuOrderCommodity::$jd_warehouse_supplier)) {
                $return_dta['pv'][] = $v;
                if (in_array($v['order_commodity_status'], [1, 2])) $return_dta['pv_no_send'] = 1;
            } else {
                $return_dta['other'][] = $v;
                if (in_array($v['order_commodity_status'], [1, 2])) $return_dta['ot_no_send'] = 1;
            }
            // 发货数量不为零 展示发货数量
            if ($v['send_commodity_num'] != 0) {
                $v['count_num'] = $v['send_commodity_num'];
            }

            $sku_info             = explode(',', $v['sku_info']);
            $list[$k]['sku_info'] = '';
            foreach ($sku_info as $sku) {
                if (empty($sku)) continue;
                $list[$k]['sku_info'] .= $sku . '<br>';
            }
            if (!empty($v['third_sku_code'])) $list[$k]['sku_info'] .= "（{$v['third_sku_code']}）";
            if (!empty($v['commodity_code'])) $list[$k]['commodity_name'] .= "<br>（{$v['commodity_code']}）";
        }
        print_json(0, '', $return_dta);
    }

    // 省广下单
    public function deliverSg()
    {
        $data   = input('post.');
        $remark = $data['remark'];
        foreach ($data['order_codes'] as $order_code) {
            $remarks[$order_code] = $data[$order_code] ?? $remark;
        }
        $params = $this->tradePushData($data['order_codes'], $remarks ?? []);
        $re     = QuickWin::create('wang_dian')->wangDian($params);
        if (isset($re['code']) && $re['code'] == 0) {
            (new BuOrderCommodity())->saveData(['order_commodity_status' => 3,], ['order_code' => ['in', $data['order_codes']], 'supplier' => '省广', 'order_commodity_status' => ['in', [0, 2]]]);
            print_json(0, '省广下单成功');
        }
        print_json(1, '省广下单失败');
    }


    /**
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function deliverPv()
    {
        $data = input('post.');
        if (isset($data['order_code']) && empty($data['order_commodity_id'])) {
            print_json(1, '发货商品不能为空');
        }
        if (empty($data['common_carrier'])) {
            print_json(1, '承运公司不能为空');
        }
        if (isset($data['order_code']) && empty($data['count_num'])) {
            print_json(1, '发货数量不能为空');
        }
        // 判断订单是否有申请售后
        $aft_order_model = new DbAfterSaleOrders();
        $map             = [
            'order_id'    => ['in', $data['order_ids']],
            'afs_status'  => ['not in', [2, 3, 6, 8, 11]],
            'a.is_enable' => 1
        ];
        $aft_order_codes = $aft_order_model->alias('a')
            ->join('t_bu_order b', 'a.order_id=b.id', 'left')
            ->where($map)
            ->column('b.order_code');
        if (!empty($aft_order_codes)) {
            $aft_order_code = implode(',', $aft_order_codes);
            print_json(1, $aft_order_code . '订单商品售后中，无法发货');
        }

        $admin_name = $this->admin_info['username'] ?? 'system';

        $service = new JdWarehouse();
        if (!empty($data['order_code'])) {
            $re = $service->create($data['order_code'], $data['order_commodity_id'], $data['common_carrier'], $data['count_num'], $admin_name);
        } else {
            // 批量下单
            $re = $service->creates($data['order_codes'], $data['common_carrier'], $admin_name);
        }

        if ($re['code'] == 200) {

            print_json(0, 'pv云仓下单成功');
        } else {
            print_json(1, $re['msg']);

        }

    }


    public function getPvOrderCommodity()
    {
        $order_code            = input('order_code');
        $order_commodity_model = new BuOrderCommodity();
        $field                 = 'a.id,a.order_code,a.commodity_id, a.commodity_name,a.price,a.count,a.sku_info,a.send_commodity_num,b.cover_image,b.commodity_code,
        c.delivery_no,c.jd_delivery_no';
        $list                  = $order_commodity_model
            ->alias('a')
            ->field($field)
            ->join('t_db_commodity b', 'a.commodity_id=b.id')
            ->join('t_db_jd_warehouse c', 'a.order_code=c.order_code and a.jd_warehouse_send_id=c.id')
            ->with(['historyJdWarehouseUpd'])
            ->where(['a.order_code' => $order_code, 'a.mo_id' => 0])
            ->select();

        foreach ($list as $key => $item) {
            $sku_info               = explode(',', $item['sku_info']);
            $list[$key]['sku_info'] = '';
            foreach ($sku_info as $sku) {
                if (empty($sku)) continue;
                $list[$key]['sku_info'] .= $sku . '<br>';
            }
        }
        $params    = [
            'where' => ['value_type' => 29, 'is_enable' => 1],
            'field' => 'value_code,county_name',
            'order' => 'order_no asc'
        ];
        $sys_model = new DbSystemValue();

        $pv_cy_list = $sys_model->getList($params);
        $data       = ['list' => $list, 'cy_list' => $pv_cy_list];
        print_json(0, 'success', $data);

    }


    public function updJdWarehouse()
    {
        $input                  = input('post.')['data'];
        $order_commodity_model  = new BuOrderCommodity();
        $jd_warehouse_model     = new DbJdWarehouse();
        $jd_warehouse_upd_model = new DbJdWarehouseUpd();
        // 保存历史记录
        $jdDeliveryNoArr = [];
        $deliveryNoArr = [];
        $commonCarrier = [];
        $orderCommodityIdArr = [];
        // 保存历史记录
        foreach ($input as $key => $item) {
            $jdDeliveryNoArr[$item['jd_delivery_no']] = $item['jd_delivery_no'];
            $deliveryNoArr[$item['jd_delivery_no']] = $item['delivery_no'];
            $commonCarrier[$item['jd_delivery_no']] = $item['common_carrier'];
            $orderCommodityIdArr[$item['jd_delivery_no']][] = $item['order_commodity_id'];

            $field              = 'id,order_code,parent_order_code,waybill_number,common_carrier,jd_warehouse_send_id';
            $orderCommodityInfo = $order_commodity_model->where('id', $item['order_commodity_id'])->field($field)->find();
            $jd_warehouse       = $jd_warehouse_model->where('id', $orderCommodityInfo['jd_warehouse_send_id'])->find();

            $add = [
                'order_commodity_id'   => $item['order_commodity_id'],
                'order_code'           => $orderCommodityInfo['parent_order_code'],
                'delivery_no'          => $jd_warehouse['delivery_no'],
                'jd_delivery_no'       => $jd_warehouse['jd_delivery_no'],
                'waybill_number'       => $orderCommodityInfo['waybill_number'],
                'common_carrier'       => $orderCommodityInfo['common_carrier'],
                'third_waybill_number' => $jd_warehouse['third_waybill_number'],
                'third_common_carrier' => $jd_warehouse['third_common_carrier'],
                'creator'              => $this->admin_info['username'],
            ];
            $jd_warehouse_upd_model->insert($add);
        }
        // 更新数据
        foreach ($jdDeliveryNoArr as $jd_delivery_no) {
            //
            $parent_order_code = $order_commodity_model
                ->whereIn('id', $orderCommodityIdArr[$jd_delivery_no])
                ->value('parent_order_code');

            $add           = [
                'order_code'     => $parent_order_code,
                'delivery_no'    => $deliveryNoArr[$jd_delivery_no] ?? '',
                'jd_delivery_no' => $jd_delivery_no,
                'jd_type'        => 1,
                'creator'        => $this->admin_info['username'],
            ];
            $jdWarehouseId = $jd_warehouse_model->insertGetId($add);

            // 查询当前更细的发货单号是否已发货
            $result = JdCloudWarehouse::create('jd_cloud_warehouse')->querySoOrder($jdWarehouseId);
            $waybillNo = '';
            $delivery_time = '';
            if ($result['code'] == 1000) {
                $waybillNo     = $result['data']['carrierInfo']['waybillNo'] ?? '';
                $delivery_time = $result['data']['channelInfo']['salesPlatformCreateTime'] ?? '';
                $carrierNo = $result['data']['carrierInfo']['carrierNo'] ?? '';
                // 承运商不等于JD
                if ($carrierNo != 'CYS0000010') {
                    // 更新发货表
                    $upd = [
                        'third_waybill_number' => $waybillNo,
                        'third_common_carrier' => $carrierNo,
                    ];
                    $jd_warehouse_model->where('id', $jdWarehouseId)->update($upd);
                }
            }
            $waybillNoArr = explode(',', $waybillNo);
            // 更新订单商品表
            foreach ($orderCommodityIdArr[$jd_delivery_no] as $key => $orderCommodityId) {
                $map = ['id' => $orderCommodityId];
                $upd = [
                    'common_carrier'       => $commonCarrier[$jd_delivery_no] ?? '',
                    'waybill_number'       => $waybillNoArr[$key] ?? $waybillNoArr[0],
                    'delivery_time'        => $delivery_time,
                    'jd_warehouse_send_id' => $jdWarehouseId,
                    'last_updated_date'    => date('Y-m-d H:i:s'),
                ];
                $order_commodity_model->where($map)->update($upd);
            }
        }
        print_json(0, 'success');
    }

}
