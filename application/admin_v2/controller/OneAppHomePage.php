<?php


namespace app\admin_v2\controller;


use app\common\model\bu\BuCheapSuitCommodity;
use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbCrowdfund;
use app\common\model\db\DbDlr;
use app\common\model\db\DbDraw;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbHomeSmallPage;
use app\common\model\db\DbHomeSmallPageGroup;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbSeckill;
use app\common\port\connectors\Community;
use app\common\port\connectors\Tag;
use app\common\port\connectors\Website;
use app\common\service\BaseDataService;
use app\common\model\db\DbHomeSm;
use app\common\model\db\DbSpecialSm;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;
use think\Hook;
use think\Log;
use think\Model;

class OneAppHomePage extends Common
{
    //页面主键
    protected $pageType;

    public function __construct()
    {
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        header('Access-Control-Allow-Methods: GET,POST,PUT,DELETE,OPTIONS,PATCH');
        $this->admin_info = session('admin_info');
        $this->pageType   = DbHomeSm::ONE_APP_PAGE;
        parent::__construct();
        $this->admin_info         = session('admin_info');
        $this->homeSmallPageGroup = new DbHomeSmallPageGroup();
        $this->homeSmallPage      = new DbHomeSmallPage();
        $this->operation_type     = DbHomeSmallPageGroup::operationType();
        $this->base_service       = new BaseDataService();
        $this->commodity          = new DbCommodity();
        $this->commodityType      = new DbCommodityType();
        $this->dlr                = new DbDlr();
        $this->dlr_code           = $this->admin_info['dlr_code'];
        $this->set_type           = $this->admin_info['type'];
        $this->pageType           = input('page_type', DbHomeSm::ONE_APP_PAGE);#日产 or 启辰 app 的首页
        $this->shelves_type       = $this->pageType == DbHomeSm::ONE_APP_PAGE ? 5 : 7;
        $this->tag                = $this->pageType == DbHomeSm::ONE_APP_PAGE ? 'tag_gwapp' : 'tag_qcapp';
        $this->channel_type       = $this->pageType == DbHomeSm::ONE_APP_PAGE ? 'GWAPP' : 'QCAPP';
    }

    public function live()
    {
        return view('live');
    }

    public function get_find()
    {
        $home_info = DbHomeSm::field('id,data_json,draft_data_json,preview_data_json')->find($this->pageType);

        $data_json_arr = json_decode($home_info['data_json'], true);
        foreach ($data_json_arr as $key => $item) {
            if (in_array($item['type'], ['cAdvertising', 'cAdvertising1', 'cAdvertising2'])) {
                foreach ($item['attribute']['imgs'] as $k => $val) {
                    if (!isset($val['show_time'])) {
                        $data_json_arr[$key]['attribute']['imgs'][$k]['show_time']['start_time'] = '';
                        $data_json_arr[$key]['attribute']['imgs'][$k]['show_time']['end_time']   = '';
                    }
                }
            }
            if ($item['type'] == "cGoods") {
                if (!isset($item['attribute']['title'])) {
                    $data_json_arr[$key]['attribute']['title'] = '';
                }
                if (!isset($item['attribute']['show_time'])) {
                    $data_json_arr[$key]['attribute']['show_time']['start_time'] = '';
                    $data_json_arr[$key]['attribute']['show_time']['end_time']   = '';
                }

            }
        }
        $list['data_json']         = $data_json_arr;
        $list['draft_data_json']   = json_decode($home_info['draft_data_json'], true);
        $list['preview_data_json'] = json_decode($home_info['preview_data_json'], true);
        if (!empty($list)) {
            print_json(0, '获取成功', $list);
        } else {
            print_json(1, '获取失败');
        }
    }

    /**
     * 获取全部分类
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function ajax_get_class()
    {
        $list = Db::name('db_commodity_type')
            ->where(['is_enable' => 1, 'level' => 1])
            ->field('id,comm_parent_id as parent_id,comm_type_name as class_name')
            ->select();

        foreach ($list as $key => $value) {
            $list[$key]['subclass'] = Db::name('db_commodity_type')
                ->where(['is_enable' => 1, 'level' => 2])
                ->where('comm_parent_id', $value['id'])
                ->field('id,comm_parent_id as parent_id,comm_type_name as class_name')
                ->select();
            foreach ($list[$key]['subclass'] as $k => $val) {
                $list[$key]['subclass'][$k]['subclass'] = Db::name('db_commodity_type')
                    ->where(['is_enable' => 1, 'level' => 3])
                    ->where('comm_parent_id', $val['id'])
                    ->field('id,comm_parent_id as parent_id,comm_type_name as class_name')
                    ->select();
            }
        }

        return print_json('0', 'ok', $list);
    }


    public function edit()
    {
        $save  = [];
        $input = input();
        if (isset($input['data_json'])) {
            $data_json = $input['data_json'];
            Db::table('t_db_card_sm_log')->where(['log_type' => 1, 'page_type' => $this->pageType])->delete();

            foreach ($data_json as $key => $value) {
                if ($value['type'] == 'cCarousel') {
                    foreach ($value['attribute']['imgs'] as $k => $val) {
                        if (isset($val['tags'])) {
                            if (!empty($val['tags'])) {
                                $data                      = [];
                                $data['modelCode']         = $this->brandModelCode($input['page_type']);

                                $data['contentCreateTime'] = date("Y-m-d H:i:s");
                                $data['contentCreator']    = $this->admin_info['username'];
                                $data['contentDetail']     = $value['name'];
                                if ($val['contentId'] == 0) {
                                    $contentId                                             = date("YmH") . rand(1, 99);
                                    $data['contentId']                                     = $contentId;
                                    $data_json[$key]['attribute']['imgs'][$k]['contentId'] = $contentId;
                                } else {
                                    $data['contentId'] = $val['contentId'];
                                }
                                $data['contentPublishTime'] = date("Y-m-d H:i:s");
                                $data['contentType']        = '广告活动';
                                $data['departmentId']       = $val['department'][1];
                                $data['departmentName']     = empty($val['departmentLabel']) ? '' : $val['departmentLabel'][1];
                                $data['title']              = '广告活动';
                                $data['url']                = $val['src'];
                                $tags                       = [];
                                foreach ($val['tags'] as $v) {
                                    $tags[] = [
                                        'tagId'   => $v['id'],
                                        'tagName' => $v['name'],
                                    ];
                                }
                                $data['selectedTagList'] = $tags;
                                $res                     = Tag::create('tag')->addContentTag($data);
                                if ($res['code'] == 1) {
                                    Log::error('index tags error' . json_encode($res));
                                }
                            }

                        }
                    }
                }
                if ($value['type'] == 'cCoupon') {
                    foreach ($value['attribute']['cards_data'] as $v) {
                        $card_id = $v['card_data']['card_id'] ?? 0;
                        if ($card_id > 0) {
                            $data = [
                                'card_id'   => $card_id,
                                'act_code'  => $v['card_data']['act_code'] ?? '',
                                'log_type'  => 1,
                                'page_type' => $this->pageType,
                                'creator'   => $this->admin_info['username'],
                            ];
                            Db::table('t_db_card_sm_log')->insert($data);
                        }
                    }
                }
                if (in_array($value['type'], ['cAdvertising', 'cAdvertising1', 'cAdvertising2'])) {
                    foreach ($value['attribute']['imgs'] as $k => $val) {
                        if ($val['savePath']['type'] == 8) { // 卡券抽奖
                            $act_code = $val['couponList'][0]['act_code'] ?? '';
                            $data_json[$key]['attribute']['imgs'][$k]['act_code'] = $act_code;
                        }
                    }
                }
                if ($value['type'] == 'cCarousel') {
                    // 轮播广告
                    foreach ($value['attribute']['imgs'] as $k => $val) {
                        foreach ($val as $index => $datum) {
                            // 抽奖链接
                            if ($datum['savePath']['type'] == 8) {
                                $act_code = $datum['couponList'][0]['act_code'] ?? '';
                                $data_json[$key]['attribute']['imgs'][$k][$index]['act_code'] = $act_code;
                            }
                        }
                    }
                }
            }
            $save['data_json'] = json_encode($data_json);
        }
        if (isset($input['preview_data_json'])) {
            $save['preview_data_json'] = json_encode($input['preview_data_json']);
        }
        if (isset($input['draft_data_json'])) {
            $save['draft_data_json'] = json_encode($input['draft_data_json']);
        }

        if (empty($save)) {
            print_json('1', 'error info');
        }

        $commodity_ids = $this->getCommodityIds($this->pageType);
        if (!empty($commodity_ids)) {
            $save['commodity_id_str'] = $commodity_ids;
        }

        DbHomeSm::where('id', $this->pageType)->update($save);
        $params = [
            'key' => 'cache_prefix.home', 'suffix' => $this->pageType,
            'set' => 'cache_prefix.more_people_more_hm_set', 'set_suffix' => $this->pageType
        ];
        Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $params);

        print_json(0, '保存成功');
    }

    public function get_special()
    {
        $title = input('title', '');

        if ($this->pageType == DbHomeSm::ONE_APP_PAGE) {
            $page_type = DbSpecialSm::ONE_APP_PAGE_TYPE;
        } else {
            $page_type = DbSpecialSm::V_ONE_APP_PAGE_TYPE;
        }

        $params['field']              = 'id,title';
        $params['where']['is_enable'] = 1;
        $params['where']['page_type'] = $page_type;

        if (!empty($title)) {
            $params['where']['title'] = array('like', '%' . $title . '%');
        }

        $list = (new DbSpecialSm())->getListPaginate($params);

        print_json('0', 'ok', $list);
    }

    public function get_card()
    {
        $input = input('get.');

        $where = [
            'a.is_enable'           => 1,
            'a.act_status'          => ['in', [1, 2]],
            'a.shelves_type'        => $this->shelves_type,
            'a.up_down_channel_dlr' => ['like', '%' . $this->channel_type . '%'],
            'a.card_type'           => ['neq', 6],
            'a.activity_id'         => ['neq', 0],
            ['exp', 'FIND_IN_SET("LQDW-ShangChengYouHuiQuanZuJian",receive_coupon_points)']

        ];
        if (!empty($input['card_name'])) {
            $where['a.card_name'] = ['like', '%' . $input['card_name'] . '%'];
        }
        if (!empty($input['card_type'])) {
            $where['a.card_type'] = $input['card_type'];
        }

        $params['field'] = 'a.id as card_id,a.card_type,a.card_name,a.act_status,card_quota,card_discount,least_cost';
        $params['where'] = $where;
        $params['order'] = 'a.last_updated_date desc';


//        $list = (new DbCard())->getListPaginate($params);
        $card_model  = new DbCard();
        $type_list   = DbCard::CARD_TYPE_LIST;
        $card_status = DbCard::cardStatus();
        $list        = $card_model->getActivityCardPage($params);
        foreach ($list as $key => $item) {
            $list[$key]['card_id']        = (string)$item['card_id'];
            $list[$key]['card_type_name'] = $type_list[$item['card_type']] ?? '';
            $list[$key]['card_status']    = $card_status[$item['act_status']] ?? '';
        }

        print_json('0', 'ok', $list);
    }

    public function getJumpList()
    {
        $input  = input();
        $return = $this->jump_list($input);
        print_json($return[0], $return[1], $return[2] ?? null);
    }

    public function jump_list($input)
    {
        if (empty($input['type'])) {
            return ['1', '页面分类不可为空'];
        }
        $params                     = [
            'limit'   => $input['limit'] ?? 15,
            'page'    => $input['page'] ?? 1,
            'keyword' => $input['keyword'] ?? '',
            'id'      => $input['id'] ?? '',
        ];
        $return_data                = $params;
        $return_data['type']        = (string)$input['type'];
        $return_data['path']        = $this->path_list($input['type']);
        $return_data['data']        = [];
        $return_data['is_can_next'] = 1;
        if (in_array($input['type'], [1, 2, 3, 4])) {
            switch ($input['type']) {
                case 1:
                    $params['type'] = 'posts';
                    $ios            = [
                        'module' => 'oneDiscoverDetail',
                        'action' => 'pushToDiscoverDetailPost',
                    ];
                    $android        = [
                        'module' => 'discover',
                        'action' => 'jumpPostDetail',
                    ];
                    $title_name     = 'feed_title';
                    break;
                case 2:
                    $params['type'] = 'topics';
                    $ios            = [
                        'module' => 'oneDiscoverAndRegister',
                        'action' => 'circleDetailVC',
                    ];
                    $android        = [
                        'module' => 'discover',
                        'action' => 'jumpCircleDetail',
                    ];
                    $title_name     = 'name';
                    break;
                case 3:
                    $params['type'] = 'feed_themes';
                    $ios            = [
                        'module' => 'oneDiscoverAndRegister',
                        'action' => 'themeDetailVC',
                    ];
                    $android        = [
                        'module' => 'discover',
                        'action' => 'jumpTopicDetail',
                    ];
                    $title_name     = 'title';
                    break;
                case 4:
                    $params['type'] = 'activity';
                    $ios            = [
                        'module' => 'oneDiscoverDetail',
                        'action' => 'pushToDiscoverDetailActivit',
                    ];
                    $android        = [
                        'module' => 'discover',
                        'action' => 'jumpActivityDetail',
                    ];
                    $title_name     = 'name';
                    break;
            }

            $list = Community::create('community')->getGatherList($params);
            if ($list['code'] != 200) {
                return ['1', '获取失败'];
            }
            foreach ($list['data'][$params['type']]['data'] as $v) {
                $return_data['data'][] = [
                    'id'    => $v['id'],
                    'type'  => (string)$input['type'],
                    'title' => $v[$title_name],
                    'data'  => [
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'id'      => $v['id'],
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => $ios,
                                'android' => $android,
                                'params'  => ['id' => $v['id']],
                            ]
                        ]
                    ]
                ];
            }
        } elseif ($input['type'] == 5) {
            $data                       = Website::create('website')->getWebsiteList();
            $return_data['is_can_next'] = 0;
            foreach ($data as $v) {
                $return_data['data'][] = [
                    'id'    => $v['id'],
                    'type'  => (string)$input['type'],
                    'title' => $v['name'],
                    'data'  => [
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'id'      => $v['id'],
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => [
                                    'module' => 'nissanWeb',
                                    'action' => 'openURLWithWeb',
                                ],
                                'android' => [
                                    'module' => 'web',
                                    'action' => 'openURLWithWeb',
                                ],
                                'params'  => ['url' => config('NODE_WEBSITE_URL') . 'app/pudge/app_car_series_' . $v['name_en'] . '?model=pre&source=app&carseries_id=' . $v['id'] . '&carseries_name=' . $v['name'], 'title' => '购车福利详情页'],
                            ]
                        ]
                    ]
                ];
            }
        } elseif ($input['type'] == 6) {
            $special            = new DbSpecialSm();
            $where['page_type'] = 2;
            if (!empty($input['id'])) {
                $where['id'] = $input['id'];
            }
            if (!empty($input['keyword'])) {
                $where['title'] = ['like', "%{$input['keyword']}%"];
            }

            $paginate = [
                'field'    => 'id,title',
                'where'    => $where,
                'order'    => 'id desc',
                'pagesize' => $input['limit'] ?? 15,
                'page'     => $input['page'] ?? 1,
            ];
            $data     = $special->getListPaginate($paginate);
            if ($data->lastPage() <= $params['page']) {
                $return_data['is_can_next'] = 0;
            }
            foreach ($data as $v) {
                $return_data['data'][] = [
                    'id'    => $v['id'],
                    'type'  => (string)$input['type'],
                    'title' => $v['title'],
                    'data'  => [
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'id'      => $v['id'],
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => [
                                    'module' => 'nissanWeb',
                                    'action' => 'openShopUrl',
                                ],
                                'android' => [
                                    'module' => 'web',
                                    'action' => 'openShopUrl',
                                ],
                                'params'  => ['url' => '/oneapp/Special?id=' . $v['id']],
                            ]
                        ]
                    ]
                ];
            }
        } elseif ($input['type'] == 7) {
            $data = config('special_fix');
            foreach ($data as $v) {
                if (empty($input['keyword']) || is_numeric(strpos($v['title'], $input['keyword']))) {
                    $return_data['data'][] = [
                        'id'    => $v['id'],
                        'type'  => (string)$input['type'],
                        'title' => $v['title'],
                        'data'  => [
                            'src'      => '',
                            'path'     => $this->path_list($input['type']),
                            'savePath' => [
                                'id'      => $v['id'],
                                'type'    => '5',
                                'app_url' => [
                                    'iOS'     => [
                                        'module' => 'nissanWeb',
                                        'action' => 'openShopUrl',
                                    ],
                                    'android' => [
                                        'module' => 'web',
                                        'action' => 'openShopUrl',
                                    ],
                                    'params'  => ['url' => $v['routes']],
                                ]
                            ]
                        ]
                    ];
                }
            }
        } elseif ($input['type'] == 8) {
            $url  = config('app_status') <> "develop" ? 'h5vit' : 'h5zh';
            $data = [
                [
                    'id'    => '1',
                    'type'  => (string)$input['type'],
                    'title' => '智行无忧宝A介绍页',
                    'data'  => [
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'id'      => '1',
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'nissanWeb', 'action' => 'openURLWithWeb',],
                                'android' => ['module' => 'web', 'action' => 'openURLWithWeb',],
                                'params'  => ['url' => 'https://' . $url . '.venucia.com/oneapppage/sceneInsurance/insuranceDetail_nissan_new.html', 'title' => '智行无忧宝A介绍页'],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '2',
                    'type'  => (string)$input['type'],
                    'title' => '车联服务套餐页',
                    'data'  => [
                        'id'       => '2',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'nissanWeb', 'action' => 'openURLWithWeb',],
                                'android' => ['module' => 'web', 'action' => 'openURLWithWeb',],
                                'params'  => ['url' => 'https://' . $url . '.venucia.com/oneapppage/FlowPackageManageNewCycle/flowpackagemanage_nissan_new.html', 'title' => '车联服务套餐页'],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '3',
                    'type'  => (string)$input['type'],
                    'title' => '车联第三方会员页',
                    'data'  => [
                        'id'       => '3',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'nissanWeb', 'action' => 'openURLWithWeb',],
                                'android' => ['module' => 'web', 'action' => 'openURLWithWeb',],
                                'params'  => ['url' => 'https://' . $url . '.venucia.com/npage/appJump/jumpTicket.html?data=servicePackage&couponGenre=2', 'title' => '车联第三方会员页'],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '4',
                    'type'  => (string)$input['type'],
                    'title' => '车联组合套餐页',
                    'data'  => [
                        'id'       => '4',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'nissanWeb', 'action' => 'openURLWithWeb',],
                                'android' => ['module' => 'web', 'action' => 'openURLWithWeb',],
                                'params'  => ['url' => 'https://' . $url . '.venucia.com/npage/appJump/jumpTicket.html?data=servicePackage&couponGenre=4', 'title' => '车联组合套餐页'],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '5',
                    'type'  => (string)$input['type'],
                    'title' => '车机主题应用页',
                    'data'  => [
                        'id'       => '5',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'themeStore', 'action' => 'pushToThemeStoreHome',],
                                'android' => ['module' => 'gui', 'action' => 'jumpGUI',],
                                'params'  => (object)[],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '6',
                    'type'  => (string)$input['type'],
                    'title' => '违章服务应用页',
                    'data'  => [
                        'id'       => '6',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'nissanWeb', 'action' => 'openURLWithWeb',],
                                'android' => ['module' => 'web', 'action' => 'openURLWithWeb',],
                                'params'  => ['url' => config('app_status') <> "develop" ? 'https://h5vit.venucia.com/npage/appJump/jumpTicket.html?data=illegalService&link=https://nissan-lifeh5-live.jidouauto.com/#/' : 'https://h5zh.venucia.com/npage/appJump/jumpTicket.html?data=illegalService&link=https://nissan-lifeh5-test.aijidou.com/#/', 'title' => '违章服务应用页'],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '7',
                    'type'  => (string)$input['type'],
                    'title' => '道路救援',
                    'data'  => [
                        'id'       => '7',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'roadRescue', 'action' => 'pushToRoadRescueHome',],
                                'android' => ['module' => 'roadrescue', 'action' => 'jumpRoadRescue',],
                                'params'  => (object)[],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '8',
                    'type'  => (string)$input['type'],
                    'title' => '保险服务',
                    'data'  => [
                        'id'       => '8',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'nissanWeb', 'action' => 'openURLWithWeb',],
                                'android' => ['module' => 'web', 'action' => 'openURLWithWeb',],
                                'params'  => ['url' => 'https://' . $url . '.venucia.com/oneapppage/carInsurance/index.html', 'title' => '保险服务'],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '9',
                    'type'  => (string)$input['type'],
                    'title' => '预约维修',
                    'data'  => [
                        'id'       => '9',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'maintenance', 'action' => 'pushToReserve',],
                                'android' => ['module' => 'appointmentMaintain', 'action' => 'selectBoutique',],
                                'params'  => (object)[],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '10',
                    'type'  => (string)$input['type'],
                    'title' => '维修保养（更多）',
                    'data'  => [
                        'id'       => '10',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'maintenance', 'action' => 'pushToMaintenance',],
                                'android' => ['module' => 'appointmentMaintain', 'action' => 'appointmentMaintain',],
                                'params'  => (object)[],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '11',
                    'type'  => (string)$input['type'],
                    'title' => '二手车',
                    'data'  => [
                        'id'       => '11',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'nissanWeb', 'action' => 'openURLWithWeb',],
                                'android' => ['module' => 'web', 'action' => 'openURLWithWeb',],
                                'params'  => ['url' => config('app_status') <> "develop" ? 'https://weixin.dongfeng-nissan.com.cn/v2/h5/usedcar/index' : 'https://dfldata-dev.dongfeng-nissan.com.cn/v2/h5/usedcar/index', 'title' => '二手车'],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '12',
                    'type'  => (string)$input['type'],
                    'title' => '及新车',
                    'data'  => [
                        'id'       => '12',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'nissanWeb', 'action' => 'openURLWithWeb',],
                                'android' => ['module' => 'web', 'action' => 'openURLWithWeb',],
                                'params'  => ['url' => config('app_status') <> "develop" ? 'https://oneapph5.dongfeng-nissan.com.cn/oneapp/newCarRegion' : 'https://h5zh.venucia.com/oneapp/newCarRegion', 'title' => '及新车'],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '13',
                    'type'  => (string)$input['type'],
                    'title' => '贴心金融详情页',
                    'data'  => [
                        'id'       => '13',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'nissanWeb', 'action' => 'openURLWithWeb',],
                                'android' => ['module' => 'web', 'action' => 'openURLWithWeb',],
                                'params'  => ['url' => config('app_status') <> "develop" ? 'https://www.dongfeng-nissan.com.cn/finance/loan-calculator?SMARTCODE=C2021-50274-5490-542-2609135' : 'https://feat-nissan201126-uat-dndcfenge-node-site.nissan-website.dev.dndc.cloud/finance/loan-calculator', 'title' => '贴心金融详情页'],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '14',
                    'type'  => (string)$input['type'],
                    'title' => '小尼车间首页',
                    'data'  => [
                        'id'       => '14',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'nissanWeb', 'action' => 'openURLWithWeb',],
                                'android' => ['module' => 'web', 'action' => 'openURLWithWeb',],
                                'params'  => ['url' => config('app_status') <> "develop" ? 'https://xnchjdpvit.szlanyou.com/?froms=app_service_entry08' : 'https://xnchjdpvitvoqa.szlanyou.com/?apigroup=1.0&bucket=a&test=test', 'title' => '小尼车间首页'],
                            ]
                        ]
                    ],
                ],
                [
                    'id'    => '15',
                    'type'  => (string)$input['type'],
                    'title' => '路书应用页',
                    'data'  => [
                        'id'       => '15',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'nissanWeb', 'action' => 'openURLWithWeb',],
                                'android' => ['module' => 'web', 'action' => 'openURLWithWeb',],
                                'params'  => ['url' => config('app_status') <> "develop" ? 'https://h5vit.venucia.com/npage/appJump/jumpTicket.html?data=travelRoad&link=https://nissan-travelh5-live.jidouauto.com/#/' : 'https://h5zh.venucia.com/npage/appJump/jumpTicket.html?data=travelRoad&link=https://nissan-travelh5-test.aijidou.com/#/', 'title' => '路书应用页'],
                            ]
                        ]
                    ],
                ], [
                    'id'    => '16',
                    'type'  => (string)$input['type'],
                    'title' => '智慧加油应用页',
                    'data'  => [
                        'id'       => '16',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'nissanPour', 'action' => 'pushToPourOilHome',],
                                'android' => ['module' => 'addGasoline', 'action' => 'addGasoline',],
                                'params'  => (object)[],
                            ]
                        ]
                    ],
                ], [
                    'id'    => '17',
                    'type'  => (string)$input['type'],
                    'title' => '车辆健康检测页',
                    'data'  => [
                        'id'       => '17',
                        'src'      => '',
                        'path'     => $this->path_list($input['type']),
                        'savePath' => [
                            'type'    => '5',
                            'app_url' => [
                                'iOS'     => ['module' => 'NissanDetection', 'action' => 'pushToNissanDetection',],
                                'android' => ['module' => 'carstatus', 'action' => 'jumpCarHealth',],
                                'params'  => (object)[],
                            ]
                        ]
                    ],
                ],
            ];
            foreach ($data as $v) {
                if (empty($input['keyword']) || is_numeric(strpos($v['title'], $input['keyword']))) {
                    $return_data['data'][] = $v;
                }
            }
        }

        if (count($return_data['data']) < $params['limit']) {
            $return_data['is_can_next'] = 0;
        }
        return [0, '获取成功', $return_data];
    }

    public function path_list($type = 0)
    {
        $list = [
            1 => '帖子',
            2 => '圈子',
            3 => '话题',
            4 => '活动',
            5 => '购车福利详情页',
            6 => '商城配置专题',
            7 => '商城定制专题',
            8 => '其它',
        ];
        if ($type > 0) {
            return $list[$type];
        } else {
            print_json('0', 'ok', $list);
        }
    }

    public function adsType()
    {
        (new Special())->adsType();
    }

    //套装列表
    public function get_suit()
    {
        $title = input('title', '');
        if (!empty($title)) {
            $param['where']['name'] = array('like', '%' . $title . "%");
        }
        $model                                 = new BuCheapSuitIndex();
        $model_commodity                       = new BuCheapSuitCommodity();
        $param['where']['act_status']          = ['in', [1, 2]];
        $param['where']['up_down_channel_dlr'] = array('like', '%' . $this->channel_type . '%');
        $param['field']                        = "id,name as title,s_time,e_time";
        $param['order']                        = "id desc";
        $list                                  = $model->getIndexList($param);
        foreach ($list as $key => $value) {
            $value->time                  = date("Y-m-d", $value->s_time) . '~' . date('Y-m-d', $value->e_time);
            $params['where']['a.suit_id'] = $value->id;
            $params['field']              = 'c.cover_image';
            $img                          = $model_commodity->getCheapCommodity($params);
            $imgs                         = [];
            foreach ($img as $val) {
                $imgs[] = $val['cover_image'];
            }
            $value->imgs  = $imgs;
            $value->total = count($img);
            unset($value->s_time, $value->e_time);
        }
        print_json('0', 'ok', $list);
    }
}
