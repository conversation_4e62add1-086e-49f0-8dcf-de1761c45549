<?php
/**
 * 限时折扣
 * User: zhangjinpeng
 * Date: 2017/11/24
 * Time: 上午11:00
 */

namespace app\admin_v2\controller;

use app\admin_v2\service\RecommendActivityService;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityDlr;
use app\common\model\db\DbCommodityDlrType;
use app\common\model\db\DbCommoditySet;
use app\common\service\BaseDataService;
use app\common\model;
use app\common\service\CommodityService;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbDlr;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbSystemValue;
use app\common\model\db\DbFullDiscount;
use think\Hook;
use app\common\model\db\DbCard;

class LimitDiscount extends Common
{

    private $comm_type_model;
    private $dbCommodity;
    private $dbLimitDiscount;
    private $dbLimitDiscountCommodity;
    private $dbDlr;
    private $comm_service;
    private $_dbCommodityDlrTypeObj;
    private $_dbCommodityFlatObj;
    private $gather_list;

    public function __construct()
    {
        parent::__construct();
        $this->comm_type_model          = new model\db\DbCommodityType();
        $this->dbCommodity              = new model\db\DbCommodity();
        $this->dbLimitDiscount          = new model\db\DbLimitDiscount();
        $this->dbLimitDiscountCommodity = new model\db\DbLimitDiscountCommodity();
        $this->dbDlr                    = new model\db\DbDlr();
        $this->comm_service             = new CommodityService();
        $this->_dbCommodityDlrTypeObj   = new DbCommodityDlrType();
        $this->_dbCommodityFlatObj      = new DbCommodityFlat();
        $model                          = new DbSystemValue();
        $this->gather_list              = $model->where(['value_type' => 26, 'is_enable' => 1])->field('sys_value_id as id,county_name as name')->select();
        $this->assign('gather_list', $this->gather_list);
        $this->assign('admin_type', $this->admin_info['type']);
    }

    public function live()
    {
        $status        = input('get.status');  //1未开始 2 进行中 3已结束
        $title         = input('get.title');
        $date          = input('get.date');
        $set_type      = input('get.set_type');
        $gather_id     = input('get.gather_id', 0);
        $is_pv_subsidy = input('get.is_pv_subsidy');
        $theme_name    = input('get.theme_name');
        $this->assign('set_type', $set_type);
        $where     = [];
        $live_type = input('get.live_type');
        if (empty($live_type)) {
            $set_type                       = 5;
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif ($live_type == 2) {
            $set_type = 7;
            $where[]  = [['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], 'or'];
        } else {
            $set_type = 6;
            $where[]  = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        }

        if (!empty($date)) {
            $date = explode(' ~ ', $date);
            if (count($date) == 2) {
                $where['a.start_time'] = ['between', [date('Y-m-d H:i:s', strtotime($date[0])), date('Y-m-d H:i:s', strtotime($date[1]) + 86400)]];
            }
        }

        if (!empty($title)) {
            $where['a.title'] = ['like', "%{$title}%"];
        }

        $date = date('Y-m-d H:i:s');
        if ($status == 1) {
            $where['a.start_time'] = ['>', $date];
        } else if ($status == 2) {
            $where['a.start_time'] = ['<', $date];
            $where['a.end_time']   = ['>', $date];
        } else if ($status == 3) {
            $where['a.end_time'] = ['<', $date];
        } else if ($status == 4) {
            $where['a.is_enable'] = 0;
        }
        if (in_array($status, [1, 2, 3])) {
            $where['a.is_enable'] = 1;
        }
        if (!empty($gather_id)) {
            $where['a.gather_id'] = $gather_id;
        }
        if (!empty($is_pv_subsidy)) {
            if ($is_pv_subsidy != 2) {
                $where['a.is_pv_subsidy'] = $is_pv_subsidy;
            } else {
                $where['a.is_pv_subsidy'] = 0;
            }
        }
        if (!empty($theme_name)) {
            $where['a.theme_name'] = array('like', '%' . $theme_name . '%');
        }

        $field               = 'a.id,a.title,a.created_dlr_code,a.start_time,a.end_time,a.is_enable,a.up_down_channel_name,a.up_down_channel_dlr,a.gather_id,a.is_pv_subsidy,a.theme_name';
        $where['a.set_type'] = $set_type;
        $params              = [
            'where' => $where,
            'query' => input('get.'),
            'order' => 'a.id DESC',
            'field' => $field,
        ];
        $gather_array        = [];
        foreach ($this->gather_list as $value) {
            $gather_array[$value['id']] = $value['name'];
        }
        $list = $this->dbLimitDiscount->getLimitDiscountList($set_type, $params);
        foreach ($list as $key => $val) {
            $in = empty($val['is_enable']) || $status == 4;
            if (strtotime($val['start_time']) > time()) {
                $list[$key]['status'] = $in ? '已关闭' : '未开始';
            } else if (strtotime($val['start_time']) <= time() && strtotime($val['end_time']) >= time()) {
                $list[$key]['status'] = $in ? '已关闭' : '进行中';
            } else if (strtotime($val['end_time']) < time()) {
                $list[$key]['status'] = $in ? '已关闭' : '已结束';
            }
            if ($val['gather_id'] == 0 || empty($val['gather_id'])) {
                $list[$key]['gather_name'] = '-';
            } else {
                if (isset($gather_array[$val['gather_id']])) {
                    $list[$key]['gather_name'] = $gather_array[$val['gather_id']];
                } else {
                    $list[$key]['gather_name'] = '-';
                }
            }
            $list[$key]['is_pv_subsidy_status'] = $val['is_pv_subsidy'] == 1 ? '是' : '否';
        }

        $page = $list->render();
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('status', $status);
        $this->assign('set_type', $set_type);
        $this->assign('live_type', $live_type);
        return $this->fetch('live');
    }

    public function index()
    {
        $status         = input('get.status');  //1未开始 2 进行中 3已结束
        $belong         = input('get.belong');  //平台 2 专营店
        $title          = input('get.title');
        $date           = input('get.date');
        $dlr_code       = input('get.dlr_code');
        $set_type       = input('get.set_type', 1);
        $admin_type     = $this->admin_info['type'];
        $admin_dlr_code = $this->admin_info['dlr_code'];

        $where = [];
        if (!empty($date)) {
            $date = explode(' ~ ', $date);
            if (count($date) == 2) {
                $where['a.start_time'] = ['between', [date('Y-m-d H:i:s', strtotime($date[0])), date('Y-m-d H:i:s', strtotime($date[1]) + 86400)]];
            }
        }

        if ($belong == 1) {
            $where['a.created_dlr_code'] = 'NISSAN';
        } else if ($belong == 2) {
            $where['a.created_dlr_code'] = ['<>', 'NISSAN'];
        }
        if (!empty($title)) {
            $where['a.title'] = ['like', "%{$title}%"];
        }
        if (!empty($dlr_code)) {
            $where['a.created_dlr_code'] = $dlr_code;
        }
        $date = date('Y-m-d H:i:s');
        if ($status == 1) {
            $where['a.start_time'] = ['>', $date];
        } else if ($status == 2) {
            $where['a.start_time'] = ['<', $date];
            $where['a.end_time']   = ['>', $date];
        } else if ($status == 3) {
            $where['a.is_enable'] = '0';
        } else if ($status == 4) {
            $where['a.end_time'] = ['<', $date];
        }
        $field = 'a.id,a.title,a.created_dlr_code,a.start_time,a.end_time,a.is_enable';
        if ($admin_type == 1) {
            $field .= ", c.dlr_name";

            $where['a.set_type'] = $set_type == 3 ? $set_type : ['not in', [3, 5, 6]];

        } else {
            $set_type = 2;
            $where[]  = ['exp', "(find_in_set('{$admin_dlr_code}',b.dlr_code) AND set_type = 1 )OR a.created_dlr_code='{$this->admin_info['dlr_code']}'"];
        }
        $params = [
            'where' => $where,
            'query' => input('get.'),
            'order' => 'a.id DESC',
            'field' => $field,
        ];
        $list   = $this->dbLimitDiscount->getLimitDiscountList($admin_type, $params);
//        $list=$this->dbLimitDiscount->getLastSql();
//        print_json(1,$list);
        foreach ($list as $key => $val) {
            $list[$key]['edit'] = false;
            if (($val['created_dlr_code'] == 'NISSAN' && $admin_type == 1) or ($admin_type == 2 && $val['created_dlr_code'] == $admin_dlr_code)) {
                $list[$key]['edit'] = true;
            }

            if ($val['is_enable'] == 1) {
                if ($val['start_time'] > $date) {
                    $list[$key]['status_name'] = '未开始';
                } else if ($val['start_time'] < $date && $val['end_time'] > $date) {
                    $list[$key]['status_name'] = '进行中';
                } else {
                    $list[$key]['status_name'] = '已结束';
                }
            } else {
                $list[$key]['status_name'] = '';
                if ($val['end_time'] < $date) {
                    $list[$key]['status_name'] = '已结束，';
                }
                $list[$key]['status_name'] .= '已关闭';
            }
            if ($val['created_dlr_code'] == 'NISSAN') {
                $list[$key]['belong'] = '平台';
            } else {
                $list[$key]['belong'] = $admin_type == 1 ? '专营店' : '自建';
            }
        }

        $page = $list->render();
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('admin_type', $admin_type);
        $this->assign('belong', $belong);
        $this->assign('status', $status);
        $this->assign('set_type', $set_type);
        return $this->fetch('index');
    }

    public function addLive()
    {
        //角色类型
        $adminType = $this->admin_info['type'];
        $setType   = input('set_type', 5);

        /**
         * 商品类型列表 积分商城和官微相同
         */
        $type            = DbCommodityDlrType::getType($setType);
        $commDlrTypeList = (new DbCommodityDlrType())->getList([
            'field' => 'id,inner_name',
            'where' => ['is_enable' => 1, 'type' => $type],
        ]);
        $commDlrTypeData = array_column($commDlrTypeList, 'inner_name', 'id');

        $commodity_class = DbCommodity::commodityClass();
        $nvi_code        = '';
        if ($setType == 5) {
            $nvi_code = 'N';
        }
        if ($setType == 6) {
            $nvi_code = 'P';
        }
        if ($setType == 7) {
            $nvi_code = 'v';
        }
        $user_level = [];
        if ($setType == 5 || $setType == 7) {
            $sysvalueobj = new DbSystemValue();
            $user_level  = $sysvalueobj->getListByCode(25, $nvi_code);
            // $user_level = (new DbSystemValue())->getNameListByCode(25,$nvi_code);
        }


        $this->assign('user_level', $user_level);
        $this->assign('nvi_code', '');
        $this->assign('comm_dlr_type_list', $commDlrTypeList);
        $this->assign('comm_dlr_type_json', json_encode($commDlrTypeData));
        $this->assign('set_type', $setType);
        $this->assign('admin_type', $adminType);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', (new DbSystemValue())->getNameList(20));
        $this->assign('commodity_class', $commodity_class);
        $this->assign('commodity_class_json', json_encode($commodity_class));
        $this->assign('live_type', input('live_type'));
        return $this->fetch('add_live');
    }

    /**
     * 添加数据
     * @return mixed
     */
    public function add()
    {
        $set_type = input('get.set_type');

        $comm_parent_id     = input('get.comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $comm_parent_list   = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list     = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        if (!empty($order_code)) {
            $where['a.order_code'] = ['like', '%' . $order_code . '%'];
        }

        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('sub_comm_type_id', $sub_comm_type_id);
        $this->assign('three_comm_type_id', $three_comm_type_id);
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);
        $this->assign('set_type', $set_type);
        return $this->fetch('add');
    }

    /**
     * 更新数据
     * @return mixed
     */
    public function updateLive()
    {
        $id             = input('get.id');
        $limit_discount = $this->dbLimitDiscount->getOneByPk($id);
        if (!$limit_discount || $limit_discount['created_dlr_code'] != $this->admin_info['dlr_code']) print_json(1, '');
        $limit_discount_comm = $this->dbLimitDiscountCommodity->getLimitDiscountCommodity(['a.limit_discount_id' => $id]);
        $dbcommodityobj      = new DbCommodity();
        foreach ($limit_discount_comm as $k => $limit_discount_item) {
            $limit_discount_comm[$k]['segment_key'] = 0;
            $segmentinfo                            = $dbcommodityobj->alias("a")
                ->join("t_db_commodity_type_segment_discount b", "a.comm_type_id=b.commodity_type_id")
                ->join("t_db_commodity_segment_discount c", "b.segment_discount_id=c.id")
                ->where(['a.id' => $limit_discount_item['commodity_id']])->field("c.*")->find();
            if (!empty($segmentinfo)) {
                $limit_discount_comm[$k]['segment_key'] = $segmentinfo['user_segment'];
            }
        }

        $limit_discount_comm = collection($limit_discount_comm)->toArray();
        $row_comm            = [];
        foreach ($limit_discount_comm as $key => $val) {
            $dlr_name                    = $this->dbDlr->getColumn(['where' => ['dlr_code' => ['in', explode(',', $val['dlr_code'])]], 'column' => 'dlr_name']);
            $val['dlr_name']             = implode(',', $dlr_name);
            $val['sku_list']             = json_decode($val['sku_price'], true);
            $val['comm_discount']        = json_decode($val['sku_dis'], true);
            $val['dis_type']             = $limit_discount['dis_type'];
            $val['comm_discount_select'] = json_decode($val['sku_dis_select'], true);

            if ($id < 997 && (empty($val['sku_dis_select']) || $val['sku_dis_select'] == 'null')) {
                $val['sku_dis_select']       = $val['sku_dis'];
                $val['comm_discount_select'] = $val['sku_dis'];

            }
            unset($val['sku_price']);
            unset($val['cover_image']);
            //业务要显示定向人群以前的数据
            $commodity_name_one = $val['commodity_name'];
            unset($val['commodity_name']);
            $row_comm[] = [
                'commodity_name' => $commodity_name_one,
                'cover_image'    => $limit_discount_comm[$key]['cover_image'],
//                'sku_list'       => str_replace("\\", '',json_encode($val) ),
                'sku_list'       => json_encode($val),
                'sku_dis_select' => $val['sku_dis_select'],
                'segment_key'    => $val['segment_key'],
            ];
        }

        $up_down_channel_info = (new DbSystemValue())->getNameList(20);
//        $info_str             = implode(',', array_keys($up_down_channel_info));

        //角色类型
//        $adminType  = $this->admin_info['type'];
        $setType = $limit_discount['set_type'];

        /**
         * 商品类型列表 积分商城和官微相同
         */
        $type            = DbCommodityDlrType::getType($setType);
        $commDlrTypeList = (new DbCommodityDlrType())->getList([
            'field' => 'id,inner_name',
            'where' => ['is_enable' => 1, 'type' => $type],
        ]);
        $commDlrTypeData = array_column($commDlrTypeList, 'inner_name', 'id');

        $commodity_class       = DbCommodity::commodityClass();
        $oneSkuData            = $data['sku_list'][0] ?? [];
        $oneCommodityClass     = $oneSkuData['commodity_class'] ?? 0;
        $oneCommodityDlrTypeId = $oneSkuData['commodity_dlr_type_id'] ?? 0;

        $nvi_code = '';
        if ($setType == 5) {
            $nvi_code = 'N';
        }
        if ($setType == 7) {
            $nvi_code = 'V';
        }
        if ($setType == 6) {
            $nvi_code = 'P';
        }
        $user_level = [];
        if ($setType == 5 || $setType == 7) {
            $systemobj  = new DbSystemValue();
            $user_level = $systemobj->getListByCode(25, $nvi_code);
        }

        $user_segment_options     = '';
        $user_segment_options_arr = [];
        if (!empty($limit_discount['user_segment_options'])) {
            $user_segment_options_arr = explode(',', $limit_discount['user_segment_options']);
            $user_segment_options     = $user_segment_options_arr[0];
        }

        $this->assign('user_segment_options', $user_segment_options);
        $this->assign('user_segment_options_arr', $user_segment_options_arr);
        $this->assign('user_level', $user_level);
        $this->assign('nvi_code', '');

        //优惠券
        $card_arr            = [];
        $selected_data       = [];
        $commodity_card_name = [];
        if (!empty($limit_discount['rel_card_ids'])) {
            $commodity_card_name = (new DbCard())->getColumn(['where' => ['id' => ['in', $limit_discount['rel_card_ids']]], 'column' => 'card_name']);
            if (!empty($commodity_card_name)) {
                $selected_data = (new DbCard())->getColumn(['where' => ['id' => ['in', $limit_discount['rel_card_ids']]], 'column' => 'id,card_name']);
                $card_arr = explode(',', $limit_discount['rel_card_ids']);
            }
        }
        if (empty($card_arr)) {
            $limit_discount['rel_card_ids'] = '';
        }
        $limit_discount['commodity_card_name'] = implode(',', $commodity_card_name);
        $limit_discount['num_card']            = empty($limit_discount['rel_card_ids']) ? 0 : count(explode(',', $limit_discount['rel_card_ids']));
        $this->assign('selected', json_encode($card_arr));
        $this->assign('selected_data', $selected_data);
        $this->assign('selected_card_name', json_encode($commodity_card_name));

        $this->assign('oneCommodityClass', $oneCommodityClass);
        $this->assign('oneCommodityDlrTypeId', $oneCommodityDlrTypeId);
        $this->assign('comm_dlr_type_list', $commDlrTypeList);
        $this->assign('comm_dlr_type_json', json_encode($commDlrTypeData));
        $user_dis_desc     = [];
        $car_user_dis_desc = [];
        if ($limit_discount['user_segment'] == 1) {
            $user_dis_desc = json_decode($limit_discount['user_dis_desc'], true);
        }
        if ($limit_discount['user_segment'] == 2) {
            $car_user_dis_desc = json_decode($limit_discount['user_dis_desc'], true);
        }
        $this->assign('user_dis_desc', $user_dis_desc);
        $this->assign('car_user_dis_desc', $car_user_dis_desc);
        $this->assign('id', $id);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', $up_down_channel_info);
        $this->assign('dlr_hidden', (strpos($limit_discount['up_down_channel_dlr'], 'GWDLR') !== false) ? '' : 'hidden');
        $this->assign('dlr_str', $this->getDlrInInfo($limit_discount['up_down_channel_dlr']));
        $this->assign('row', $limit_discount);
        $this->assign('row_comm', $row_comm);
        $this->assign('set_type', $setType);
        $this->assign('commodity_class', $commodity_class);
        $this->assign('commodity_class_json', json_encode($commodity_class));
        $this->assign('live_type', input('live_type'));
        return $this->fetch('update_live');
    }

    /**
     * 更新数据
     * @return mixed
     */
    public function update()
    {
        $id             = input('get.id');
        $limit_discount = $this->dbLimitDiscount->getOneByPk($id);
        if ($limit_discount['created_dlr_code'] != $this->admin_info['dlr_code']) print_json(1, '');
        $limit_discount_comm = $this->dbLimitDiscountCommodity->getLimitDiscountCommodity(['a.limit_discount_id' => $id]);
        $limit_discount_comm = collection($limit_discount_comm)->toArray();
        $row_comm            = [];
        foreach ($limit_discount_comm as $key => $val) {
            $dlr_name        = $this->dbDlr->getColumn(['where' => ['dlr_code' => ['in', explode(',', $val['dlr_code'])]], 'column' => 'dlr_name']);
            $val['dlr_name'] = implode(',', $dlr_name);
            $val['sku_list'] = json_decode($val['sku_price'], true);
            unset($val['sku_price']);
            unset($val['cover_image']);
            $row_comm[] = [
                'commodity_name' => $val['commodity_name'],
                'cover_image'    => $limit_discount_comm[$key]['cover_image'],
                'sku_list'       => json_encode($val),
            ];
        }

        $comm_parent_id     = input('get.comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $comm_parent_list   = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list     = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        if (!empty($order_code)) {
            $where['a.order_code'] = ['like', '%' . $order_code . '%'];
        }

        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('sub_comm_type_id', $sub_comm_type_id);
        $this->assign('three_comm_type_id', $three_comm_type_id);
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);


        $this->assign('row', $limit_discount);

        $this->assign('row_comm', $row_comm);
        $this->assign('set_type', $limit_discount['set_type']);
        return $this->fetch('update');
    }

    /**
     * 查看
     */
    public function view()
    {
        $id                       = input('get.id');
        $limit_discount           = $this->dbLimitDiscount->getOneByPk($id);
        $limit_discount_comm_list = $this->dbLimitDiscountCommodity->getLimitDiscountCommodity(['a.limit_discount_id' => $id]);

        $dlr_name = '';
        if ($limit_discount !== 'NISSAN') {
            $dlr      = $this->dbDlr->getOne(['where' => ['dlr_code' => $limit_discount['created_dlr_code']], 'field' => 'dlr_name']);
            $dlr_name = $dlr['dlr_name'];
        }

        $date = date('Y-m-d H:i:s');
        if ($limit_discount['is_enable'] == 1) {
            if ($limit_discount['start_time'] > $date) {
                $limit_discount['status_name'] = '未开始';
            } else if ($limit_discount['start_time'] < $date && $limit_discount['end_time'] > $date) {
                $limit_discount['status_name'] = '进行中';
            } else {
                $limit_discount['status_name'] = '已结束';
            }
        } else {
            $limit_discount['status_name'] = '已关闭';
        }
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('row', $limit_discount);
        $this->assign('limit_discount_comm_list', $limit_discount_comm_list);
        $this->assign('dlr_name', $dlr_name);
        return $this->fetch('view');
    }

    /**
     * 保存数据
     */
    public function saveLive()
    {
        $post = input('post.');
        $title = input('post.title') or print_json(1, '活动名称不能为空');
        $start_time = input('post.start_time') or print_json(1, '开始时间不能为空');
        $end_time = input('post.end_time') or print_json(1, '结束时间不能为空');
        $discount            = $_POST['discount'];
        $dis_money           = $_POST['dis_money'];
        $dis_type            = input('post.dis_type') ?? 0;
        $home                = input('post.home');
        $card_available      = input('post.card_available', 0);
        $gather_id = input('gather_id',0);
        $is_pv_subsidy = input('is_pv_subsidy',0);
        $theme_name = input('theme_name');
        $new_level_str = '';
        $up_down_channel = $post['up_down_channel'] ?? [];
        $up_down_channel_dlr = getUpDownChannel(
            implode(',', $up_down_channel),
            input('post.dlr_code', '')
        );
        if ($dis_type == 2) {
            $discount = $dis_money;
        }
        if (!$discount && $dis_type != 3) {
            print_json(1, '折扣不能为空');
        }
        $purchase_number = input('post.purchase_number');      //限购数量
        $des             = input('post.des');
        $is_enable       = input('post.is_enable', '1');
        $id              = input('post.id');
        if ($start_time > $end_time) print_json(1, '结束时间不能小于开始时间');
        $sku_list = [];
        if (empty($_POST['sku_list'])) print_json(1, '商品规格不能为空');
        if (!empty($_POST['sku_list'])) {
            $sku_list = json_decode($_POST['sku_list'], true);
            $sku_list = str_replace("\\", '', $sku_list);
        }

        $user_segment_options = input('post.user_segment_options') ?? '';
        $user_segment         = input('post.user_segment', 0);
        $set_type             = input('post.set_type');
        $user_dis_desc        = '';
        $user_level_arr       = [];
        $v_dis_arr            = [];
        if ($user_segment > 0) {
            if ($set_type == 5) {
                $nvi_code = 'N';
            }
            if ($set_type == 7) {
                $nvi_code = 'V';
            }
            if ($set_type == 6) {
                $nvi_code = 'P';
            }
            $user_level = [];
            if ($user_segment == 1) {
                if ($set_type == 5 || $set_type == 7) {
                    $sysvalueobj = new DbSystemValue();
                    $user_level  = $sysvalueobj->getListByCode(25, $nvi_code);

                    foreach ($user_level as $user_level_item) {
                        $user_level_arr[] = $user_level_item['value_code'];
                    }

                    if (!empty($user_level_arr)) {
                        $user_level_str = implode(",", $user_level_arr);
                        $level_arr      = explode($user_segment_options, $user_level_str);
                        $new_level_str  = $user_segment_options . $level_arr[1];
                    }
                }


                //0的不入库
                $sku_list_tmp = [];
                $sku_list_arr = $sku_list;
                foreach ($sku_list_arr as $ko => $sku_item) {
                    foreach ($sku_item['comm_discount'] as $kt => $item) {
                        foreach ($item as $sku => $item_su) {
                            if ($item_su > 0) {
                                $sku_list_tmp[$sku] = $item_su;
                            }
                        }
                        $sku_list[$ko]['comm_discount'][$kt] = $sku_list_tmp;
                    }
                }
                // dd($sku_list);

                $v_dis_type = input('v_dis_type', '');
                $count      = count(explode(',', $new_level_str));
                //$count = count($user_level_arr);
                if (!empty($v_dis_type)) {
                    $v_dis_type      = rtrim($v_dis_type, ',');
                    $v_dis_type_arrs = explode(',', $v_dis_type);

                    foreach ($v_dis_type_arrs as $jk => $item) {
                        if (!empty($item)) {
                            $v_dis_type_arr[] = $item;
                        };
                    }
                    $n = 0;

                    foreach ($v_dis_type_arr as $k => $v_dis_type_item) {
                        $v_dis_arr[$n][] = $v_dis_type_item;
                        if (($k + 1) % $count == 0) {
                            $n = $n + 1;
                        }
                    }
                }
                // dd($v_dis_arr);
            }
            if ($user_segment == 2) {
                if ($set_type == 6) {
                    $count = 3;
                } else {
                    $count = 2;
                }

                $carv_dis_type = input('carv_dis_type', '');
                if (!empty($carv_dis_type)) {
                    $carv_dis_type = rtrim($carv_dis_type, ',');
                    $carv_dis_type = explode(',', $carv_dis_type);
                    $n             = 0;
                    foreach ($carv_dis_type as $k => $v_dis_type_item) {
                        $v_dis_arr[$n][] = $v_dis_type_item;
                        if (($k + 1) % $count == 0) {
                            $n = $n + 1;
                        }
                    }
                }
                $new_level_arr = [];
                if (!empty($v_dis_arr[0][0])) {
                    $new_level_arr[] = 'NONE';
                }

                if (!empty($v_dis_arr[0][1])) {
                    if ($set_type == 5 || $set_type == 6) {
                        $new_level_arr[] = 'N';
                    }
                    if ($set_type == 7) {
                        $new_level_arr[] = 'V';
                    }
                }
                if ($set_type == 6) {

                    $new_level_arr[] = 'P';
                }
                $new_level_str = implode(',', $new_level_arr);
            }
        }


        $act_status = $this->dbLimitDiscount->getActStatus($start_time, $end_time);
        $data = [
            'title'                 => $title,
            'start_time'            => $start_time,
            'end_time'              => $end_time,
            'act_status'            => $act_status,
            'tag'                   => $post['tag'],
            'discount'              => 0,
            'discount_multi'        => json_encode($discount),
            'purchase_number'       => $purchase_number,
            'is_enable'             => $is_enable,
            'des'                   => $des,
            'creator'               => $this->admin_info['username'],
            'created_dlr_code'      => $this->admin_info['dlr_code'],
            'set_type'              => $set_type,
            'dis_type'              => $dis_type,
            'card_available'        => $card_available,
            'up_down_channel_name'  => implode(',', $_POST['up_down_channel_name'] ?? []),
            'up_down_channel_dlr'   => $up_down_channel_dlr,
            'discount_type'         => $post['discount_type'] ?? 1,
            'e3s_activity_id'       => $post['e3s_activity_id'] ?? 0,
            'activity_type'         => $post['activity_type'] ?? 0,
            'settlement_rule_id'    => $post['settlement_rule_id'],
            'settlement_rule_name'  => $post['settlement_rule'],
            'settlement_rule_type'  => $post['settlement_rule_type'],
            'settlement_rule_value' => $post['settlement_rule_value'],
            'rel_card_ids'          => $post['rel_card_ids'] ?? '',
            'user_segment'          => $user_segment,
            'user_segment_options'  => $new_level_str,
            'user_dis_desc'         => json_encode($v_dis_arr),
            'gather_id'             => $gather_id,
            'is_pv_subsidy'         => $is_pv_subsidy,
            'theme_name'            => $theme_name,
        ];
        if (!empty($data['e3s_activity_id'])) {
            if (empty($data['activity_type'])) print_json(1, '存在e3s活动时活动设置类型必选');
        } else {
            $data['activity_type'] = 0;
        }
        $e3s_activity = input('e3s_activity', '');
        if (!empty($e3s_activity)) {
            $data['e3s_activity_name'] = explode(' | ', $e3s_activity)[1];
        }

        //开启事物
        $this->dbLimitDiscount->startTrans();
        $rm_commodity_id_arr = [];
        $where_time          = ['start_time' => $start_time, 'end_time' => $end_time];

        if ($is_enable == 1) {
//            $date = date('Y-m-d H:i:s');
            $disQuery = [
                'a.is_enable'  => ['=', 1],
//                'a.start_time' => ['<', $end_time],
//                'a.end_time'   => ['>', $start_time],
                'a.set_type'   => $set_type,
                'a.act_status' => ['in', [1, 2]],
            ];
            if (!empty($id)) $disQuery['a.id'] = ['<>', $id];
            if ($data['discount_type'] == 1) {
                $disQuery['a.discount_type'] = ['<>', 2];
            }
            if (in_array($data['discount_type'], [2, 3])) {
                $disQuery['a.discount_type'] = ['<>', 1];
                $full_where                  = $disQuery;
                unset($full_where['a.id']);
                $list = (new DbFullDiscount())->getIsFullDiscount(
                    $sku_list, $full_where
                );
                foreach ($list as $v) {
                    if ($v['is_full'] == 1) print_json(1, $v['commodity_name'] . '工时已参加满减活动');
                }
            }
            //是否 限时折扣
            $list = $this->dbLimitDiscount->getIsLimitProduct(
                $sku_list, $disQuery, $where_time
            );
            foreach ($list as $v) {
                if ($v['is_limit'] == 1) print_json(1, $v['commodity_name'] . '已参加其它限时折扣活动');
            }
            //判断是否已经参加了预售
            $list = (new DbPreSale())->getIsPreProduct(
                $sku_list, [
                    'a.is_enable'      => ['=', 1],
                    'a.front_s_time'   => ['<', $end_time],
                    'a.balance_e_time' => ['>', $start_time],
                    'a.set_type'       => $set_type,
                ]
            );
            foreach ($list as $v) {
                if ($v['is_pre'] == 1) print_json(1, $v['commodity_name'] . '已参加预售活动');
            }
            //判断是否已经参加了拼团
            $list = (new DbFightGroup())->getIsFightGroupProduct(
                $sku_list, [
                    'a.is_enable'  => ['=', 1],
                    'a.start_time' => ['<', $end_time],
                    'a.end_time'   => ['>', $start_time],
                    'a.set_type'   => $set_type,
                ]
            );
            foreach ($list as $v) {
                if ($v['is_fight'] == 1) print_json(1, $v['commodity_name'] . '已参加拼团活动');
            }

            //判断是否已经参加了秒杀
            $list = (new model\db\DbSeckill())->getIsSeckillProduct(
                $sku_list, [
                'a.act_status' => ['in', [1, 2]],
                'a.is_enable'  => ['=', 1],
//                    'a.start_time' => ['<', $end_time],
//                    'a.end_time'   => ['>', $start_time],
                'a.set_type'   => $set_type,
            ], $where_time
            );
            foreach ($list as $v) {
                if ($v['is_seckill'] == 1) print_json(1, $v['commodity_name'] . '已参加秒杀活动');
            }
        }

        if (empty($id)) {  //插入
            $last_id = $this->dbLimitDiscount->insertGetId($data);
            $res     = $this->insertCommodiySkuDataLive($sku_list, $last_id, 'add', $set_type, $home);
            if (!$res) {
                print_json(1, '保存失败');
            }
        } else {    //修改
            unset($data['dis_type']);
            unset($data['discount_type']);
            $row     = $this->dbLimitDiscount->getOneByPk($id);
            $last_id = $id;
            if ($row && $row['created_dlr_code'] == $this->admin_info['dlr_code']) {
                $data['modifier']          = $this->admin_info['username'];
                $data['last_updated_date'] = date('Y-m-d H:i:s');
                $res                       = $this->dbLimitDiscount->isUpdate(true)->saveData($data, ['id' => $id]);
                if ($res) {

                    $old_commodity_id_arr = $this->dbLimitDiscountCommodity->where(['limit_discount_id' => $id])->column('commodity_id');
                    $new_commodity_id_arr = array_column($sku_list, 'commodity_id');
                    $rm_commodity_id_arr  = array_diff($old_commodity_id_arr, $new_commodity_id_arr);

                    $this->dbLimitDiscountCommodity->where(['limit_discount_id' => $id])->delete();
                    $res_sku = $this->insertCommodiySkuDataLive($sku_list, $id, 'update', $set_type, $home, $id);
                    if (!$res_sku) {
                        print_json(1, '活动商品保存失败');
                    }
                }
            }
        }
        //提交
        $this->dbLimitDiscount->commit();
        if (($act_status == 2) && !empty($is_enable)) {
            $limit_discount_comm = $this->dbLimitDiscountCommodity->getLimitDiscountCommodity(['a.limit_discount_id' => $last_id]);
            $this->doHook('add', $limit_discount_comm, $last_id);
        } else if (empty($is_enable)) {
            $limit_discount_comm = $this->dbLimitDiscountCommodity->getLimitDiscountCommodity(['a.limit_discount_id' => $last_id]);
            $this->doHook('delete', $limit_discount_comm, $last_id);
        }

        if (!empty($rm_commodity_id_arr)) {
            $this->doHook('delete', $rm_commodity_id_arr, $last_id);
        }

        print_json(0, '保存成功');
    }

    /**
     * 保存数据
     */
    public function save()
    {
        $title = input('post.title') or print_json(1, '活动名称不能为空');
        $start_time = input('post.start_time') or print_json(1, '开始时间不能为空');
        $end_time = input('post.end_time') or print_json(1, '结束时间不能为空');
        $discount  = input('post.discount');
        $dis_money = input('post.dis_money');
        $dis_type  = input('post.dis_type');
        if ($dis_type == 2) {
            $discount = $dis_money;
        }
        if (!$discount) {
            print_json(1, '折扣不能为空');
        }
        $purchase_number = input('post.purchase_number');      //限购数量
        $des             = input('post.des');
        $is_enable       = input('post.is_enable', '1');
        $id              = input('post.id');
        if ($start_time > $end_time) print_json(1, '结束时间不能小于开始时间');
        if (empty($_POST['sku_list'])) print_json(1, '商品规格不能为空');
        $sku_list = $_POST['sku_list'];
        $set_type = input('post.set_type');
        $data     = [
            'title'            => $title,
            'start_time'       => $start_time,
            'end_time'         => $end_time,
            'discount'         => $discount,
            'purchase_number'  => $purchase_number,
            'is_enable'        => $is_enable,
            'des'              => $des,
            'creator'          => $this->admin_info['username'],
            'created_dlr_code' => $this->admin_info['dlr_code'],
            'set_type'         => $set_type,
            'dis_type'         => $dis_type,
        ];

        //开启事物
        $this->dbLimitDiscount->startTrans();
        if (empty($id)) {  //插入
            $last_id = $this->dbLimitDiscount->insertGetId($data);
            $res     = $this->insertCommodiySkuData($sku_list, $last_id, 'add', $set_type);
            if (!$res) {
                print_json(1, '保存失败');
            }

        } else {    //修改
            $row = $this->dbLimitDiscount->getOneByPk($id);
            if ($row && $row['created_dlr_code'] == $this->admin_info['dlr_code']) {
                $data['modifier']          = $this->admin_info['username'];
                $data['last_updated_date'] = date('Y-m-d H:i:s');
                $res                       = $this->dbLimitDiscount->isUpdate(true)->saveData($data, ['id' => $id]);
                if ($res) {
                    $this->dbLimitDiscountCommodity->where(['limit_discount_id' => $id])->delete();
                    $res_sku = $this->insertCommodiySkuData($sku_list, $id, 'update', $set_type);
                    if (!$res_sku) {
                        print_json(1, '活动商品保存失败');
                    }
                }
            }
        }
        //提交
        $this->dbLimitDiscount->commit();
        print_json(0, '保存成功');
    }

    public function delete()
    {
        $this->_checkAjax();
        $id  = input('post.id');
        $row = $this->dbLimitDiscount->getOneByPk($id);
        if ($row['set_type'] == 5) {
            $limit_discount_comm = $this->dbLimitDiscountCommodity->getLimitDiscountCommodity(['a.limit_discount_id' => $id]);
            $this->doHook('delete', $limit_discount_comm, $id);
        }
        print_json(0, '不可以！');
        if ($row['created_dlr_code'] == $this->admin_info['dlr_code']) {
            $this->dbLimitDiscountCommodity->where(['limit_discount_id' => $id])->delete();
            $this->dbLimitDiscount->where(['id' => $id])->delete();
            print_json(0, '删除成功');
        }
    }

    /**
     * 插入商品及规格
     * @param $sku_list
     * @param $fight_id
     * @param $action
     * @param $home
     * @return bool|int|string
     */
    public function insertCommodiySkuDataLive($sku_list, $fight_id, $action, $set_type = 5, $home = 0, $id = '')
    {
        if (empty($sku_list)) {
            return false;
        }
        $data     = [];
        $dlr_code = '';
        if ($set_type == 2) {
            $dlr_code = $this->admin_info['dlr_code'];
        } elseif ($set_type == 3) {
            $dlr_code = 'GWSC';
        }
        foreach ($sku_list as $key => $val) {
            $sku_price = $val['sku_list'];
            rsort($sku_price);
            $highest_price = reset($sku_price);
            $lowest_price  = end($sku_price);

            if (gettype($highest_price) == 'array') {
                $highest_price = max($highest_price);
                $lowest_price  = min($lowest_price);
            }

            $data[]              = [
                'commodity_id'      => $val['commodity_id'],
                'commodity_set_id'  => $val['commodity_set_id'],
                'comm_discount'     => 0,
                'dlr_code'          => $set_type == 1 ? $val['dlr_code'] : $dlr_code,
                'limit_discount_id' => $fight_id,
                'creator'           => $this->admin_info['username'],
                'sku_price'         => json_encode($val['sku_list']),
                'sku_dis'           => json_encode($val['comm_discount']),
                'highest_price'     => $highest_price > 0 ? $highest_price : 0,
                'lowest_price'      => $lowest_price > 0 ? $lowest_price : 0,
                'sku_dis_select'    => json_encode($val['comm_discount_select'])
                //'is_home' => $home
            ];
            $commodity_set_ids[] = $val['commodity_set_id'];
            // var_dump($data);
        }
        //删除已经添加的商品
        // if ($this->admin_info['type'] == 1) {
        //     $where = ['commodity_set_id' => ['in', $commodity_set_ids]];
        // } else {
        //     $where = ['commodity_set_id' => ['in', $commodity_set_ids], 'dlr_code' => $this->admin_info['dlr_code']];
        // }
        if ($id) {
            $this->dbLimitDiscountCommodity->where(['limit_discount_id' => $id])->delete();
        }

        $lastid = $this->dbLimitDiscountCommodity->insertAll($data);
        // 删除推荐活动不存在的商品
        $service = new RecommendActivityService();
        $service->delNotInActivityCommodity(1,$fight_id);
        return $lastid;
    }


    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetLiveCommodityList()
    {
        $commodity_name        = input('commodity_name');
        $top_type              = input('comm_parent_id');
        $second_type           = input('sub_comm_type_id');
        $third_type            = input('three_comm_type_id');
        $active_id             = input('active_id');
        $commodity_class       = input('commodity_class');
        $commodity_dlr_type_id = input('commodity_dlr_type_id');
        $live_type             = input('live_type');
        $start_time            = input('start_time');
        $end_time              = input('end_time');
        $discount_type         = input('discount_type');
        $user_segment          = input('user_segment') ?? 0;
        if (strtolower($user_segment) == 'undefined') $user_segment = 0;
        $set_type = input('set_type');
        $where    = ['b.listing_type' => 1];

        if (empty($set_type) || $set_type == 5) {
            $brand_id                       = 1;
            $where['b.shelves_type']        = 5;
            $where['b.qsc_group']           = '';  // 取送车服务包不能参加
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif ($set_type == 7) {
            $brand_id                = 2;
            $where['b.shelves_type'] = 7;
            $where[]                 = [['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], 'or'];
        } elseif ($set_type == 6) {
            $brand_id                = 3;
            $where['b.shelves_type'] = 6;
            $where[]                 = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        }
        if (!empty($commodity_name)) {
            $where['a.commodity_name'] = ['like', "%$commodity_name%"];
        }

        $type_id = 0;
        if (!empty($third_type)) {
            $type_id = $third_type;
        } else if (!empty($second_type)) {
            $type_id = $second_type;
        } else if (!empty($top_type)) {
            $type_id = $top_type;
        }

        if (!empty($commodity_class)) {
            $where['a.commodity_class'] = $commodity_class;
        }

        if (!empty($commodity_dlr_type_id)) {
            $where['b.commodity_dlr_type_id'] = $commodity_dlr_type_id;
        }

        if (!empty($type_id)) {
            $where[] = ['exp', " (find_in_set({$type_id},comm_type_id_str) ) "];
        }

        // $where['b.shelves_type'] = 5;//车生活id
        $field  = "b.commodity_dlr_type_id,a.commodity_class,comm_type_id,a.up_down_channel_name,a.commodity_id,a.commodity_set_id,commodity_name,cover_image,a.price,a.count_stock,a.is_grouped";
        $params = array(
            'qurey'    => input('get.'),
            'where'    => $where,
            'pagesize' => input('pagesize'),
            'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
            'field'    => $field,
        );

        $flat = new DbCommodityFlat();
        $list = $flat->getCommodityList($params);

        $date = date('Y-m-d H:i:s');

        $disQuery = [
            'a.id'         => ['<>', empty($active_id) ? 0 : $active_id],
            'a.is_enable'  => ['=', 1],
            'a.start_time' => ['<', $end_time],
            'a.end_time'   => ['>', $start_time],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]],
        ];
        if ($discount_type == 1) {
            $disQuery['a.discount_type'] = ['<>', 2];
        }
        $where_time = ['start_time' => $start_time, 'end_time' => $end_time];
        if (in_array($discount_type, [2, 3])) {
            $disQuery['a.discount_type'] = ['<>', 1];
            $full_where                  = $disQuery;
            unset($full_where['a.id']);
            $list = (new DbFullDiscount())->getIsFullDiscount(
                $list, $full_where
            );
        }

        //是否 定向人群不一至

        $dbcommoidtytypesegmentObj = new model\db\DbCommodityTypeSegmentDiscount();
        foreach ($list as $k => $v) {
            $list[$k]['is_segment'] = 0;
            if ($user_segment > 0) {
                $segment_info = $dbcommoidtytypesegmentObj->alias("a")->join("t_db_commodity_segment_discount b", "b.id=a.segment_discount_id")->where(['a.commodity_type_id' => $v['comm_type_id'], 'a.is_enable' => 1, 'a.brand' => $brand_id])->field('b.*')->find();
                if (!empty($segment_info)) {
                    if ($segment_info['user_segment'] != $user_segment) {
                        $list[$k]['is_segment'] = 1;
                    }
                }
            }
        }


        //是否 限时折扣
        $list = $this->dbLimitDiscount->getIsLimitProduct($list, $disQuery, $where_time);

        //判断是否已经参加了预售
        $list = (new DbPreSale())->getIsPreProduct($list, [
            'a.is_enable'      => ['=', 1],
            'a.front_s_time'   => ['<', $end_time],
            'a.balance_e_time' => ['>', $start_time],
            'a.set_type'       => $where['b.shelves_type'],
        ]);

        //判断是否已经参加了拼团
        $list = (new DbFightGroup())->getIsFightGroupProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.start_time' => ['<', $end_time],
            'a.end_time'   => ['>', $start_time],
            'a.set_type'   => $where['b.shelves_type'],
        ]);
        //判断是否已经参加秒杀
        $list        = (new model\db\DbSeckill())->getIsSeckillProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]],
        ], $where_time);
        $res         = [];
        $res['list'] = $list;
        print_json(0, '', $res);
    }


    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetCommodityList()
    {
        $commodity_name     = input('commodity_name');
        $comm_parent_id     = input('comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $params['where']    = ['a.commodity_class' => 1];
        $params['query']    = [];
        $set_type           = input('get.set_type');
        if (!empty($commodity_name)) {
            $params['where']['a.commodity_name'] = ['like', "%$commodity_name%"];
        }


        if (!empty($three_comm_type_id)) {
            $params['where']['a.comm_type_id'] = $three_comm_type_id;
        } else {
            if (!empty($sub_comm_type_id)) {
                $comm_type_column = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id], 'column' => 'id']);

                $params['where']['a.comm_type_id'] = ['in', $comm_type_column];
            } else {
                if (!empty($comm_parent_id)) {
                    $two_type_column                   = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                    $three_type_column                 = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => ['in', $two_type_column]], 'column' => 'id']);
                    $params['where']['a.comm_type_id'] = ['in', $three_type_column];
                }
            }
        }

//        $params['field']='a.id,a.commodity_name,a.cover_image,a.comm_type_id,a.set_type,a.create_dlr_code, b.comm_type_name,b.comm_parent_id,e.id as commodity_set_id,e.dlr_code,e.original_price_range_end,e.count_stock';
        $params['order']        = 'a.id desc';
        $params['query']        = input('get.');
        $params['pagesize']     = input('pagesize');
        $params['shelves_type'] = $set_type;
        $dlr_code               = $this->admin_info['dlr_code'];
        $set_type               = $this->admin_info['type'];
        $list                   = $this->dbCommodity->getShelvesListByDlr($set_type, $dlr_code, $params);

        $commodity_set_id_list = [];
        foreach ($list as $key => $val) {
            $commodity_set_id_list[] = $val['commodity_set_id'];
            $list[$key]['is_limit']  = 0;
            if (!empty($val['highest_price'])) $list[$key]['original_price_range_end'] = $val['highest_price'];
        }
        //处理是否已经折扣
        $comm_dl_model = new DbCommodityDlr();
        $dlr_list      = $comm_dl_model->getDlrBySetId($commodity_set_id_list);
        $is_limit_id   = [];
        foreach ($list as $key => $val) {
            if (in_array($val['commodity_set_id'], $is_limit_id)) $list[$key]['is_limit'] = 1;
        }

        $comm_parent_list2        = $this->comm_type_model->getCommTypeName();
        $res                      = [];
        $res['list']              = $list;
        $res['comm_parent_list2'] = $comm_parent_list2;
        print_json(0, '', $res);
    }

    public function ajaxGetCommTypeId()
    {
        $comm_parent_id = input('get.comm_parent_id');
        $list           = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        print_json(0, '', $list);

    }

    /**
     * 插入商品及规格
     * @param $sku_list
     * @param $fight_id
     * @param $action
     * @return bool|int|string
     */
    public function insertCommodiySkuData($sku_list, $fight_id, $action, $set_type)
    {
        if (empty($sku_list)) {
            return false;
        }
        $data = [];

        if ($set_type == 2) {
            $dlr_code = $this->admin_info['dlr_code'];
        } elseif ($set_type == 3) {
            $dlr_code = 'GWSC';
        }
        foreach ($sku_list as $key => $val) {
            $sku_price = $val['sku_list'];
            rsort($sku_price);
            $highest_price       = reset($sku_price);
            $lowest_price        = end($sku_price);
            $data[]              = [
                'commodity_id'      => $val['commodity_id'],
                'commodity_set_id'  => $val['commodity_set_id'],
                'comm_discount'     => $val['comm_discount'],
                'dlr_code'          => $set_type == 1 ? $val['dlr_code'] : $dlr_code,
                'limit_discount_id' => $fight_id,
                'creator'           => $this->admin_info['username'],
                'sku_price'         => json_encode($val['sku_list']),
                'highest_price'     => $highest_price,
                'lowest_price'      => $lowest_price,
            ];
            $commodity_set_ids[] = $val['commodity_set_id'];
            // var_dump($data);
        }
        //删除已经添加的商品
        if ($this->admin_info['type'] == 1) {
            $where = ['commodity_set_id' => ['in', $commodity_set_ids]];
        } else {
            $where = ['commodity_set_id' => ['in', $commodity_set_ids], 'dlr_code' => $this->admin_info['dlr_code']];
        }
        $this->dbLimitDiscountCommodity->where($where)->delete();
        $lastid = $this->dbLimitDiscountCommodity->insertAll($data);
        return $lastid;
    }

    public function getSkuList()
    {
        $limit_commodity_id = input('get.limit_commodity_id');
        $commodity_set_id   = input('get.commodity_set_id');
        $row                = $this->dbLimitDiscount->getGroupInfo(['where' => ['b.id' => $limit_commodity_id], 'field' => 'b.sku_price,b.commodity_id,a.created_dlr_code']);
        if (!$row) print_json(1, '', []);
        $sku_price    = json_decode($row['sku_price'], true);
        $commodity_id = input('commodity_id');
        // $list=$this->sku_model->getList(['where'=>['commodity_id'=>$commodity_id,'is_enable'=>1]]);
        $res = $this->comm_service->getSetSku($commodity_set_id);
        foreach ($res['sku_list'] as $key => $val) {
            if (empty($sku_price[$val['id']])) {
                unset($res['sku_list'][$key]);
            } else {
                $res['sku_list'][$key]['fight_price'] = $sku_price[$val['id']];
            }
        }
        $this->assign('sku_list', $res['sku_list']);
        $this->assign('sp_title', $res['sp_title']);
        $this->assign('sp_list', $res['sp_list']);
        return $this->fetch('get_index_sku');
    }

    public function getSkuLive()
    {
        $commodity_id     = input('get.commodity_id');
        $commodity_set_id = input('get.commodity_set_id');
        $live_type        = input('live_type');
        $set_type         = input('set_type');
        $dis_code         = 'NONE';
        if ($set_type == 5) {
            $brand_id     = 1;
            $shelves_type = 5;
            $dis_code     = 'N';
        } elseif ($set_type == 7) {
            $brand_id     = 2;
            $shelves_type = 7;
            $dis_code     = 'V';
        } elseif ($set_type == 6) {
            $brand_id     = 3;
            $shelves_type = 6;
            $dis_code     = 'N';
        }

        $comm_service = new CommodityService();
        $sku          = $comm_service->getSetSku($commodity_set_id);
        //echo $comm_service->getLastSql();exit;
        $sku_list      = $sku['sku_list'];
        $commodity_row = $this->_dbCommodityFlatObj->getOne([
            'where'        => ['commodity_id' => $commodity_id, 'commodity_set_id' => $commodity_set_id],
            'field'        => 'commodity_name,cover_image,comm_type_id,is_grouped',
            'shelves_type' => $shelves_type
        ]);

        //0517版本
        $user_level_name  = [];
        $keys             = [];
        $user_setect_keys = [];
        if ($shelves_type == 5 || $shelves_type == 7) {
            // $user_level_name_tmp = (new DbSystemValue())->getNameListByCode(25,$dis_code);
            $sysvalueobj         = new DbSystemValue();
            $user_level_name_tmp = $sysvalueobj->getListByCode(25, $dis_code);
            foreach ($user_level_name_tmp as $user_level_name_item) {
                $user_level_name[$user_level_name_item['value_code']] = $user_level_name_item['county_name'];
                $keys[]                                               = $user_level_name_item['value_code'];
            }
//            if(!empty($keys)){
//                $keys = array_reverse($keys);
//            }
        }


        $user_level_name_true        = [];
        $user_level_name_true_code   = [];
        $dbcommoidtytypesegmentObj   = new model\db\DbCommodityTypeSegmentDiscount();
        $segment_info                = $dbcommoidtytypesegmentObj->alias("a")->join("t_db_commodity_segment_discount b", "b.id=a.segment_discount_id")->where(['a.commodity_type_id' => $commodity_row['comm_type_id'], 'a.is_enable' => 1, 'a.brand' => $brand_id])->field('b.*')->find();
        $commodity_row['is_segment'] = 0;
        if (!empty($segment_info)) {
            $commodity_row['is_segment'] = $segment_info['user_segment'];
            $segment_info['disc']        = 1;
            if ($set_type == 6) {
                $segment_info['pdis'] = 1;
            }
            if ($segment_info['user_segment'] == 2) {//l车主
//                if($segment_info['is_segment'] == 1){
//                    $commodity_row['is_segment'] = 2;//前端js定位为2
//                }

                $segment_info_disc = json_decode($segment_info['discount_body'], true);

                $segment_info['disc'] = $segment_info_disc[$dis_code] ?? 0;
                if ($set_type == 6) {
                    $segment_info['pdisc'] = $segment_info_disc['P'] ?? 0;
                }
            }
            if ($segment_info['user_segment'] == 1) {//会员
                $body_list   = json_decode($segment_info['discount_body'], true);
                $keys_str    = implode(',', $keys);
                $explode_key = '';
                foreach ($body_list as $key => $user_level_name_item) {
                    $explode_key = $key;
                    break;
                }
                if (!empty($explode_key)) {
                    $explode_str = explode($explode_key, $keys_str);
                    $explode_arr = explode(',', $explode_str[0]);
                    foreach ($explode_arr as $item) {
                        if (!empty($item)) {
                            $user_level_name_true[$user_level_name[$item]] = '';
                            $user_level_name_true_code[$item]              = '';
                        }
                    }
                }

                foreach ($body_list as $code => $val) {
//                    if(!empty($val)){
//                        $user_level_name_true[$user_level_name[$code]] = $val /10 ;
//                    }else{
//                        $user_level_name_true[$user_level_name[$code]] = $val ;
//                    }
                    $user_level_name_true[$user_level_name[$code]] = $val;
                    $user_level_name_true_code[$code]              = $val;
                }
            }
            $segment_info['user_segment_name']         = $user_level_name_true;
            $segment_info['user_level_name_true_code'] = $user_level_name_true_code;
            $commodity_row['segment']                  = $segment_info;
            if ($set_type == 5 || $set_type == 6) {
                $dis_code = 'N';
            }
            if ($set_type == 7) {
                $dis_code = 'V';
            }

            $commodity_row['dis_code'] = $dis_code;
        }

        $lowest_price  = [];
        $highest_price = [];

        foreach ($sku_list as $key => $val) {
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'];
            if (empty($val['sp_value_arr'])) {
                continue;
            }
            foreach ($val['sp_value_arr'] as $key1 => $val1) {
                $sku_list[$key]['sku_val'] .= '/' . $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
            }
            if (!isset($lowest_price[$val['sub_commodity_id']]) || $lowest_price[$val['sub_commodity_id']] > $val['price']) {
                $lowest_price[$val['sub_commodity_id']] = $val['price'];
            }
            if (!isset($highest_price[$val['sub_commodity_id']]) || $highest_price[$val['sub_commodity_id']] < $val['price']) {
                $highest_price[$val['sub_commodity_id']] = $val['price'];
            }
        }
        $commodity_row['lowest_price']  = array_sum($lowest_price);
        $commodity_row['highest_price'] = array_sum($highest_price);

        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);
    }

    public function getSku()
    {
        $commodity_id     = input('get.commodity_id');
        $commodity_set_id = input('get.commodity_set_id');
        $comm_set_model   = new DbCommoditySet();
        $com_set_row      = $comm_set_model->getOne(['where' => ['id' => $commodity_set_id], 'field' => 'commodity_id,dlr_code,set_type']);
        // var_dump($commodity_set_id);
        $sku           = $this->comm_service->getSetSku($commodity_set_id);
        $sku_list      = $sku['sku_list'];
        $commodity_row = $this->dbCommodity->getOne(['where' => ['id' => $commodity_id], 'field' => 'commodity_name,cover_image']);
        // var_dump($sku);
        foreach ($sku_list as $key => $val) {
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'];
            if (!empty($val['sp_value_arr'])) {
                foreach ($val['sp_value_arr'] as $key1 => $val1) {
                    $sku_list[$key]['sku_val'] .= '/' . $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
                }
            }
        }

        //var_dump($sku_list);exit;
        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);
    }

    /**
     * 通过上架id 获取可以选专营店
     */
    public function ajaxGetDlr()
    {
        $commodity_set_id = input('get.commodity_set_id') or print_json(1, '', []);

        $baseService = new BaseDataService();
        $dlr_data    = $baseService->getDlrList($commodity_set_id);
        $this->assign('dlr_data', $dlr_data);
        print_json(0, '', $dlr_data);
    }

    public function test()
    {
        $dlr_list = ['cx222', '343', '3ooii', '34333'];
        $kk       = ['cx222', '343', '3ooii'];
        $ls       = array_diff($kk, $dlr_list);
        var_dump($ls);
    }

    public function test2()
    {
        $dd = $this->comm_service->getCommodityInfo(341, 'TEST', 0, '', 28);
        var_dump($dd);
    }

    public function test3()
    {
        $dd = $this->comm_service->getOneSku(186, 2);
        var_dump($dd);
    }

    private function doHook($type = 'delete', $commodity_arr = [], $act_id = 0)
    {
        Hook::import(require ROOT_PATH . 'config/tags.php');
        $result = true;

        //查询act_id对应的活动，查询up_down_channel_dlr ，做出判断 shelves_type
        $activity = model\db\DbLimitDiscount::where('id', $act_id)->find();

        if (!empty($activity['up_down_channel_dlr'])) {

            $up_down_arr = explode(',', $activity['up_down_channel_dlr']);
            $shelves_ni  = array_intersect(DbDlr::$ni_arr, $up_down_arr);
            $shelves_pz  = array_intersect(DbDlr::$pz1a_arr, $up_down_arr);
            $shelves_qc  = array_intersect(DbDlr::$qc_arr, $up_down_arr);

            if (!empty($shelves_ni)) {
                $shelves_type = DbDlr::$ni_shelves;
            } else if (!empty($shelves_pz)) {
                $shelves_type = DbDlr::$pz1a_shelves;
            } else if (!empty($shelves_qc)) {
                $shelves_type = DbDlr::$qc_shelves;
            } else {
                return false;
            }

            switch ($type) {
                case 'add':
                case 'update':
                    # 添加商品不需要刷活动信息
                    foreach ($commodity_arr as $item_one) {
                        $item_one['shelves_type'] = $shelves_type;
                        Hook::listen('flat_limit_discount', $item_one);
                    }
                    break;
                case 'delete':
                    # 删除就只需要将数据删除就ok
                    foreach ($commodity_arr as $item_one) {
                        $del_params = [
                            'shelves_type' => $shelves_type,
                            'del_dis'      => true,
                            'act_id'       => $act_id,
                            'commodity_id' => empty($item_one['commodity_id']) ? $item_one : $item_one['commodity_id']
                        ];
                        Hook::listen('flat_limit_discount', $del_params);
                    }
                    break;
            }

            $detail_param = ['key' => 'cache_prefix.commodity_detail', 'suffix' => '', 'set' => 'cache_prefix.commodity_detail_set'];
            Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $detail_param);

            return $result;
        }

        return false;
    }

    /**
     * 获取卡券
     */
    public function ajaxGetCard()
    {
        $this->_checkAjax();
        $model        = new DbCard();
        $card_name    = input('get.card_name');
        $set_type     = input('set_type', 0);
        $where        = [];
        $shelves_type = 5;
        if ($set_type == 5) {
            $where['up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], ['notlike', '%QCSM%'], ['notlike', '%QCAPP%'], 'and'];
        } elseif ($set_type == 6) {
            $shelves_type = 6;
            $where[]      = [['exp', 'FIND_IN_SET("PZ1ASM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",up_down_channel_dlr)'], 'or'];
        } elseif ($set_type == 7) {
            $shelves_type = 7;
            $where[]      = [['exp', 'FIND_IN_SET("QCSM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",up_down_channel_dlr)'], 'or'];
        }
        if (!empty($card_name))
            $where['card_name'] = ['like', '%' . $card_name . '%'];

        $where['shelves_type'] = $shelves_type;
        $where['is_enable']    = 1;
        $where['type']         = 2;
        $where['act_status']   = ['in', [1, 2, 3]];
        $params                = [
            'where'    => $where,
            'order'    => 'id desc',
            'field'    => 'id,is_enable,act_status,card_type,card_name,date_type,validity_date_start,validity_date_end,fixed_term,fixed_begin_term,(case dlr_code when "NISSAN" then "平台" else "自建" end) as belong_to,available_count,up_down_channel_name',
            'pagesize' => input('get.pagesize'),
            'query'    => input('get.')
        ];
        $list = $model->getListPaginate($params);
        $card_type_arr = $model->cardType();

        foreach ($list as $key=>$value){
            $list[$key]['id'] = (string)$value['id'];
            $list[$key]['card_type_name'] =  $card_type_arr[$value['card_type']] ?? '';

            switch ($value['date_type']){
                case 1:
                    $list[$key]['validity_date'] = $value['validity_date_start'] . '至' . $value['validity_date_end'];
                    break;
                case 2:
                    if ($value['fixed_begin_term'] == 0) {
                        $list[$key]['validity_date'] = "自领取当天有效，有效期" . $value['fixed_term'] . '天';
                    } elseif ($value['fixed_begin_term'] == 1) {
                        $list[$key]['validity_date'] = "领取后" . $value['fixed_term'] . '天后有效';
                    }
                    break;
                default:
                    break;
            }
            $value['status_name'] = empty($value['is_enable']) ? '已关闭' : $model->cardStatus()[$value['act_status']];
        }
        print_json(0, '', $list);
    }

}
