<?php


namespace app\admin_v2\controller;


use app\admin_v2\service\NewMediaOrderService;

class NewMediaOrder extends Common
{


    public function index()
    {
        $input = input('get.');
        $service = new NewMediaOrderService();
        $admin_name = $this->admin_info['username'];
        $list = $service->getList($input, $admin_name);
        print_json(0, 'success', $list);
    }


    /**
     * 搜索配置
     */
    public function searchConfig()
    {
        $service = new NewMediaOrderService();
        $list = $service->searchConfig();
        print_json(0, 'success', $list);
    }


    /**
     * 适用专营店
     */
    public function applyToDlr()
    {
        $order_main_id = input('get.order_main_id');
        if (empty($order_main_id)) {
            print_json(1,'主订单ID不能为空');
        }
        $service = new NewMediaOrderService();
        $list = $service->getApplyToDlr($order_main_id);
        print_json(0, 'success', $list);
    }


    /**
     * 订单商品
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function orderCommodities()
    {
        $order_detail_id = input('get.order_detail_id');
        if (empty($order_detail_id)) {
            print_json(1,'子订单ID不能为空');
        }
        $service = new NewMediaOrderService();
        $list = $service->orderCommodities($order_detail_id);
        print_json(0, 'success', $list);
    }


}