<?php


namespace app\admin_v2\controller;


use app\admin_v2\service\NewMediaDlrService;
use app\common\model\db\DbExports;
use think\Queue;
use think\Request;
use tool\PhpExcel;


class NewMediaDlr extends Common
{

    public function index()
    {
        $input = input('get.');
        $service = new NewMediaDlrService();
        $result = $service->getPageList($input);
        print_json(0, 'success',$result);

    }


    /**
     * 下载模板
     */
    public function download()
    {
        $files    = 'public/static/template/新媒体门店映射表.xlsx';
        $filename = '新媒体门店映射表.xlsx';
        download($files, $filename);
    }


    /**
     * 导入数据
     * @param Request $request
     */
    public function import(Request $request)
    {
        $file = $request->file('file');
        $res = PhpExcel::import_excel($file);

        if ($res['error']) {
            print_json(1, $res['msg']);
        }
        $service = new NewMediaDlrService();
        $result = $service->importData($res);
        if ($result->isSuccess()) {
            print_json(0, 'success');
        } else {
            print_json(1, $result->getMessage());
        }
    }


    /**
     * 导出
     */
    public function downloadDlr()
    {
        $input = input('get.');
        $map = ['is_enable'=>1];
        if (!empty($input['platform'])) {
            $map['platform'] = $input['platform'];
        }
        if (!empty($input['dlr_id'])) {
            $map['dlr_id'] = $input['dlr_id'];
        }
        if (!empty($input['dfn_dlr_code'])) {
            $map['dfn_dlr_code'] = $input['dfn_dlr_code'];
        }
        $params_key = md5(json_encode($input));

        $export = DbExports::create([
            'export_type'   => 'new_media_dlr',
            'filter_params' => json_encode($map),
            'export_key'    => $params_key,
            'creator'       => $this->admin_info['username']
        ]);

        Queue::push('app\admin_v2\queue\NewMediaDlrQueue', json_encode([
            'map'       => $map,
            'id'           => $export->id,
        ]), config('queue_type.new_media'));

        print_json(0, '已加入异步下载列表');
    }


    /**
     * 删除
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function delDlr()
    {
        $dlrId = input('dlr_id');
        if (empty($dlrId)) {
            print_json(1,'参数缺失');
        }
        $service = new NewMediaDlrService();
        $re = $service->delDlr($dlrId);
        if ($re->isSuccess()) {
            print_json(0,'删除成功');
        } else {
            print_json(1,$re->getMessage());
        }
    }
}