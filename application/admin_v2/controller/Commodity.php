<?php
/**
 * 商品管理
 * @author: liangyijian
 * @time: 2017-07-17
 */

namespace app\admin_v2\controller;


use api\jd_sdk\JdOrderN;
use app\common\model\db\DbBrands;
use app\common\model\db\DbCard;
use app\common\model\db\DbCarSeries;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityClassInfo;
use app\common\model\db\DbCommodityDlr;
use app\common\model\db\DbCommodityExpand;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommodityLog;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbCoupon;
use app\common\model\db\DbDlr;
use app\common\model\db\DbJdSkuInfo;
use app\common\model\db\DbSpecValue;
use app\common\model\db\DbCommoditySku;
use app\common\model\db\DbSystemValue;
use app\common\model\e3s\E3sMaintenanceProduct;
use app\common\model\e3s\E3sPackage;
use app\common\model\e3s\E3sPackagePart;
use app\common\model\e3s\E3sPz1aMaintenancePackage;
use app\common\model\e3s\E3sSpecificRelationPart;
use app\common\queue\Base;
use app\common\queue\CommoditySku;
use app\common\service\CommodityService;
use think\Db;
use app\common\model\e3s\E3sSparePart as SparePartModel;
use app\common\model\db\DbCommodityDes;
use app\common\model\db\DbCommodityDesType;
use think\Env;
use think\Exception;
use think\Hook;
use think\Model;
use think\Queue;
use tool\Logger;

class Commodity extends Common
{


    protected $sp_model;
    protected $comm_model;
    protected $comm_type_model;
    protected $sku_model;
    protected $db_card;
    protected $dbCommoditySet;
    protected $spare_part;
    protected $dbCommoditySetSku;
    protected $dbCommodityDes;
    protected $dbCommodityDesType;

    function __construct()
    {
        parent::__construct();
        $this->sp_model           = new DbSpecValue();
        $this->comm_model         = new DbCommodity();
        $this->comm_type_model    = new DbCommodityType();
        $this->sku_model          = new DbCommoditySku();
        $this->db_card            = new DbCard();
        $this->dbCommoditySet     = new DbCommoditySet();
        $this->spare_part         = new SparePartModel();
        $this->dbCommoditySetSku  = new DbCommoditySetSku();
        $this->dbCommodityDes     = new DbCommodityDes();
        $this->dbCommodityDesType = new DbCommodityDesType();
    }


    public function index()
    {
        if ($this->admin_info['type'] == 2) {
            return $this->CommodityShelves();
        }
        $commodity_name     = input('get.commodity_name');
        $comm_type_id       = input('get.comm_type_id');
        $comm_parent_id     = input('get.comm_parent_id');
        $commodity_type     = input('get.commodity_type');
        $commodity_class    = input('get.commodity_class');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $is_grouped         = input('get.is_grouped', 0);

        $params['where'] = [];
        $params['query'] = [];

        if (!empty($commodity_name)) {
            $params['where'] = ['commodity_name' => ['like', "%$commodity_name%"]];
        }

        if (!empty($three_comm_type_id)) {
            $params['where']['a.comm_type_id'] = $three_comm_type_id;
        } else {
            if (!empty($sub_comm_type_id)) {
                $comm_type_column                  = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id], 'column' => 'id']);
                $params['where']['a.comm_type_id'] = ['in', $comm_type_column];
            } else {
                if (!empty($comm_parent_id)) {
                    $two_type_column                   = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                    $three_type_column                 = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => ['in', $two_type_column]], 'column' => 'id']);
                    $params['where']['a.comm_type_id'] = ['in', $three_type_column];

                }
            }
        }

        $params['where']['is_grouped'] = $is_grouped;

        if (!empty($commodity_class)) {
            $params['where']['a.commodity_class'] = $commodity_class;
        }

        $comm_type_list = [];
        if (!empty($params['where']['a.comm_type_id'])) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }

        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        $params['field']                   = 'a.*,b.comm_type_name,b.comm_parent_id';
        $params['order']                   = 'a.id desc';
        $params['query']                   = input('get.');
        $params['query']['commodity_name'] = str_replace('+', '%2B', input('get.commodity_name'));
        if ($this->admin_info['type'] == 2) {
            $params['where']['a.create_dlr_code'] = $this->admin_info['dlr_code'];
        }
        $params['where']['a.is_enable'] = 1;
        $list                           = $this->comm_model->getCommodityList($params);

        $comm_parent_list    = $this->comm_type_model->getCommTypeName();
        $commodity_class_arr = DbCommodity::commodityClass();
        foreach ($list as $key => $val) {
            $list[$key]['is_shop_name']         = $val['is_shop'] == 1 ? '是' : '否';
            $list[$key]['is_pure_name']         = $val['is_pure'] == 1 ? '是' : '否';
            $list[$key]['is_mail_name']         = $val['is_mail'] == 1 ? '是' : '否';
            $list[$key]['commodity_attr_name']  = DbCommodity::attribute($val['commodity_attr']);
            $list[$key]['cover_image']          = config('upload.url') . $val['cover_image'];
            $list[$key]['commodity_class_name'] = $commodity_class_arr[$val['commodity_class']] ?? '';


            if ($val['comm_parent_id'] && isset($comm_parent_list[$val['comm_parent_id']])) {
                $type_info                    = $this->comm_type_model->where(['id' => $val['comm_parent_id']])->find();
                $list[$key]['comm_type_name'] = $comm_parent_list[$type_info['comm_parent_id']] . '>' . $comm_parent_list[$val['comm_parent_id']] . '>' . $val['comm_type_name'];
            }
        }

        $page = $list->render();
        $this->assign('commodity_attribute', DbCommodity::attribute());
        $this->assign('page', $page);
        $this->assign('list', $list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('comm_three_parent_id', $three_comm_type_id);
        $this->assign('comm_sub_parent_id', $sub_comm_type_id);
        $this->assign('comm_type_id', $comm_type_id);
        $this->assign('commodity_name', $commodity_name);
        $this->assign('commodity_type', $commodity_type);
        $this->assign('commodity_class', $commodity_class_arr);
        $this->assign('is_grouped', $is_grouped);

        if (empty($is_grouped)) {
            return $this->fetch('index');
        } else {
            return $this->fetch('group_index');
        }

    }


    public function CommodityShelves()
    {
        $commodity_name     = input('get.commodity_name');
        $is_grouped         = input('get.is_grouped', -1);
        $comm_type_id       = input('get.comm_type_id');
        $comm_parent_id     = input('get.comm_parent_id');
        $commodity_type     = input('get.commodity_type');
        $is_platform        = input('get.is_platform', 0);
        $commodity_status   = input('get.commodity_status', '');
        $shelf_commodity    = input('get.shelf_commodity');
        $admin_type         = $this->admin_info['type'];
        $dlr_code           = $this->admin_info['dlr_code'];
        $commodity_class    = input('get.commodity_class', 0);
        $shelves_type       = input('get.shelves_type'); //1 平台上架 2 专营店 3官微
        $is_pure            = input('get.is_pure');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $listing_type       = input('get.listing_type'); //上架类型 1普通 2众筹

        if (empty($shelves_type)) {
            $shelves_type = $admin_type == 1 ? 1 : 2;
        }

        $params['where'] = [];
        $params['query'] = [];
//        $params['where']['a.is_shop'] = 1;
        if (!empty($commodity_name)) {
            $params['where'] = ['commodity_name' => ['like', "%$commodity_name%"]];
        }
        if (!empty($three_comm_type_id)) {
            $params['where']['a.comm_type_id'] = $three_comm_type_id;
        } else {
            if (!empty($sub_comm_type_id)) {
                $comm_type_column                  = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id], 'column' => 'id']);
                $params['where']['a.comm_type_id'] = ['in', $comm_type_column];
            } else {
                if (!empty($comm_parent_id)) {
                    $two_type_column                   = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                    $three_type_column                 = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => ['in', $two_type_column]], 'column' => 'id']);
                    $params['where']['a.comm_type_id'] = ['in', $three_type_column];
                }
            }
        }
        if (!empty($commodity_class)) {
            $params['where']['a.commodity_class'] = $commodity_class;
        }

        if ($is_grouped != -1) {
            $params['where']['a.is_grouped'] = $is_grouped;
        }

        if (!empty($listing_type)) {
            $params['where']['e.listing_type'] = $listing_type;
        }

        $comm_type_list = [];
        if (!empty($params['where']['a.comm_type_id'])) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }

        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }

        if ($is_platform == 1) {
            $params['where']['a.set_type'] = 1;
            if (in_array($shelves_type, [1, 3])) {
                $params['where']['e.created_date'] = ['exp', 'is not null'];
            }
        } else {
            $params['where']['a.create_dlr_code'] = $this->admin_info['dlr_code'];
            //$params['e_join_on']          =" AND e.dlr_code='{$dlr_code}' AND e.set_type={$admin_type}";
            //$params['f_join_on']          =" AND f.dlr_code='{$dlr_code}'";
        }

        if ($commodity_status == 1) {   //已上架
            $params['where']['e.created_date'] = ['exp', 'is not null'];
        } else if ($commodity_status == 2) {  //未上架
            $params['where']['e.created_date'] = ['exp', 'is  null'];
        }

        if ($shelf_commodity == 1) {   //总部上架
            $params['where']['e.set_type'] = 1;
        } else if ($shelf_commodity == 2) {  //自己上架
            $params['where']['e.set_type'] = 2;
        }
        if (!empty($is_pure) || $is_pure === '0') {
            $params['where']['a.is_pure'] = $is_pure;
        }

        $params['where'][] = ['exp', "FIND_IN_SET('{$shelves_type}',a.shelves_sources)"];
        if ($shelves_type == 5 && $admin_type == 2) {
            $params['where'][] = ['exp', "FIND_IN_SET('{$dlr_code}',e.platform_up_down_channel_dlr)"];
        }

        $params['dlr_code'] = $dlr_code;

        $params['field']                = 'a.*,b.comm_type_name,b.comm_parent_id,  e.id as commodity_set_id,e.set_type as dlr_set_type,e.commodity_attr as commodity_set_attr,ee.latest_listing_time as shelves_date,ee.latest_end_time,e.original_price_range_start original_s_price,' .
            ' e.original_price_range_end original_e_price ,e.up_down_channel_name,e.up_down_channel_dlr,e.discount_price_range_start discount_s_price , e.discount_price_range_end as discount_e_price,e.is_mail set_is_mail,e.count_stock set_count_stock,e.listing_type,ee.id AS is_commodity_set_id';
        $params['order']                = 'a.id desc';
        $params['query']                = input('get.');
        $params['where']['a.is_enable'] = 1;
        $params['is_platform']          = $is_platform;
        $params['shelves_type']         = $shelves_type;

        //dd($params);
        $params['where']['a.is_update'] = 1;
        $list                           = $this->comm_model->getCommodityShelvesList($params, $shelves_type);
        $comm_parent_list               = $this->comm_type_model->getCommTypeName();
        $commodity_class_arr            = DbCommodity::commodityClass();
//        print_json(1,'',$comm_parent_list);
        $shelves_type_arr = DbCommodityDlr::shelvesType();
        $commodity_ids    = [];
        foreach ($list as $key => $val) {
            if (!empty($val['set_is_mail'])) $val['is_mail'] = $val['set_is_mail'];
            if (!empty($val['set_count_stock'])) $list[$key]['count_stock'] = $val['set_count_stock'];
            if (!empty($val['original_s_price'])) $list[$key]['original_price_range_start'] = $val['original_s_price'];
            if (!empty($val['original_e_price'])) $list[$key]['original_price_range_end'] = $val['original_e_price'];
            if (!empty($val['discount_s_price'])) $list[$key]['discount_price_range_start'] = $val['discount_s_price'];
            if (!empty($val['discount_e_price'])) $list[$key]['discount_price_range_end'] = $val['discount_e_price'];

            $list[$key]['is_shop_name']        = $val['is_shop'] == 1 ? '是' : '否';
            $list[$key]['is_pure_name']        = $val['is_pure'] == 1 ? '是' : '否';
            $list[$key]['is_mail_name']        = $val['is_mail'] == 1 ? '是' : '否';
            $list[$key]['commodity_attr_name'] = DbCommodity::attribute($val['commodity_set_attr']);
            $list[$key]['cover_image']         = config('upload.url') . $val['cover_image'];
            if (!empty($val['shelves_date'])) {
                $shelves_date = $val['shelves_date'];
                if (!empty($val['latest_end_time'])) {
                    $shelves_date .= '<br />至<br />' . $val['latest_end_time'];
                }
                $list[$key]['shelves_date'] = $shelves_date;
            } else {
                $list[$key]['shelves_date'] = '-';
            }
            $list[$key]['commodity_class_name'] = $commodity_class_arr[$val['commodity_class']] ?? '';
            if ($val['comm_parent_id'] && isset($comm_parent_list[$val['comm_parent_id']])) {
                $type_info                    = $this->comm_type_model->where(['id' => $val['comm_parent_id']])->find();
                $list[$key]['comm_type_name'] = $comm_parent_list[$type_info['comm_parent_id']] . '>' . $comm_parent_list[$val['comm_parent_id']] . '>' . $val['comm_type_name'];
            }
            if (empty($val['dlr_set_type'])) {
                $list[$key]['dlr_set_type_name'] = '_';
            } else if ($val['dlr_set_type'] == 1) {
                $list[$key]['dlr_set_type_name'] = '总部';
            } else {
                $list[$key]['dlr_set_type_name'] = '自己';
            }
            $commodity_ids[]              = $val['id'];
            $list[$key]['shelves_source'] = '';
            if (!empty($val['is_commodity_set_id'])) {
                $set_sku_count = DbCommoditySetSku::where(['commodity_set_id' => $val['is_commodity_set_id'], 'is_enable' => 1])->count();
                if ($set_sku_count == 0) {
                    $list[$key]['is_commodity_set_id'] = '';
                }
            }
        }

        //批量获取上架场景
        if ($admin_type == 1 || ($is_platform == 1 && $shelves_type == 2)) {
            //平台端
            if ($admin_type == 1) {
                $shelves_list = $this->dbCommoditySet->getList(['where' => ['set_type' => $admin_type, 'commodity_id' => ['in', $commodity_ids], 'dlr_code' => $dlr_code], 'field' => " commodity_id,shelves_type"]);
            } else { //专营店端
                $shelves_list = $this->dbCommoditySet->getCommoditySetType(['where' => ['b.commodity_id' => ['in', $commodity_ids], 'b.dlr_code' => $dlr_code], 'field' => 'a.commodity_id,a.shelves_type']);
            }

            foreach ($list as $key => $val) {
                $shelves_source = [];
                foreach ($shelves_list as $key_1 => $val_1) {
                    if ($val_1['commodity_id'] == $val['id']) {
                        if (!empty($shelves_type_arr[$val_1['shelves_type']])) {
                            $shelves_source[] = $shelves_type_arr[$val_1['shelves_type']];
                        }
                    }
                }
                $list[$key]['shelves_source'] = implode(',', $shelves_source);
            }
        }
        $page = $list->render();
        $this->assign('commodity_attribute', DbCommodity::attribute());
        $this->assign('page', $page);
        $this->assign('list', $list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('comm_three_parent_id', $three_comm_type_id);
        $this->assign('comm_sub_parent_id', $sub_comm_type_id);
        $this->assign('comm_type_id', $comm_type_id);
        $this->assign('commodity_name', $commodity_name);
        $this->assign('commodity_type', $commodity_type);
        $this->assign('admin_type', $admin_type);
        $this->assign('dlr_code', $dlr_code);
        $this->assign('is_platform', $is_platform);
        $this->assign('set_type', $shelves_type);
        $this->assign('shelves_type', $shelves_type);
        $this->assign('commodity_class', $commodity_class_arr);
        $this->assign('comm_three_type_list', $comm_three_type_list);
        return $this->fetch(in_array($shelves_type, [5, 6, 7]) ? 'commodity_channel' : 'commodity_shelves');
    }

    public function addStep1()
    {
        $t_model      = new DbCommodityType();
        $commodity_id = input('get.commodity_id', 0);
        $is_grouped   = input('get.is_grouped', 0);
        $this->assign('commodity_type', $t_model->getCommodityByParentId(0));
        $this->assign('commodity_id', $commodity_id);
        $this->assign('step', 1);
        $this->assign('is_grouped', $is_grouped);
        return $this->fetch('add_step_1');


    }

    /**
     * 替换富文本提交非法字符
     * @param $str
     * @return string
     */
    function DeleteHtml($str)
    {
        $str = trim($str); //清除字符串两边的空格
        $str = preg_replace("/\t/", "", $str); //使用正则表达式替换内容，如：空格，换行，并将替换为空。
        $str = preg_replace("/\r\n/", "", $str);
        $str = preg_replace("/\r/", "", $str);
        $str = preg_replace("/\n/", "", $str);

        return trim($str); //返回字符串
    }

    public function addStep2()
    {
        $commodity_id      = input('get.commodity_id');
        $is_grouped        = input('get.is_grouped', 0);
        $set_type          = input('get.set_type');
        $commodity_type_id = input('get.commodity_type_id');
        if (empty($commodity_id)) {  //新增
            $type_id = $commodity_type_id;

        }
        else {     //编辑
            $row                   = $this->comm_model->getOneByPk($commodity_id);
            $row                   = $row->toArray();
            $class_info_model = new DbCommodityClassInfo();
            $row['tips'] = $class_info_model->where(['class_id'=>$row['commodity_class']])->value('tips') ?? '';
            $row['detail_content'] = $this->DeleteHtml($row['detail_content']);
            if ($row['commodity_class'] == 3) {
                $commodity_card_name        = $this->db_card->getColumn(['where' => ['id' => ['in', explode(',', $row['commodity_card_ids'])]], 'column' => 'card_name']);
                $row['commodity_card_name'] = implode(',', $commodity_card_name);
            }
            //查询规格值
            $ser_com = new CommodityService();
            $res     = $ser_com->getDefaultSku($commodity_id);
            if(!empty($res)){
                if(!empty($res['sku_list'])){
                    foreach($res['sku_list'] as $ks=>$item){
                       $tax =  $item['tax'] ?? 0;
                        $res['sku_list'][$ks]['tax'] = intval($tax);
                    }
                }
            }


            $sku_card_ids   = array_column($res['sku_list'], 'card_id');
            $card_name_list = $this->db_card->where(['id' => ['in', $sku_card_ids]])->column('card_name', 'id');
            $tab_list       = [];
            $tab            = [];
            if ($row['dd_commodity_type'] == 9 || $row['dd_commodity_type'] == 10 ||
            /*延保服务包*/
                ($row['dd_commodity_type'] == 0 && $row['commodity_class'] == DbCommodity::COMMODITY_CLASS_KEY9)) {
                $sku_list     = [];
                $new_sku_list = [];
                foreach ($res['sku_list'] as $key => $value) {
                    $new_sku_list[$value['sp_value_list']]                 = $value;
                    $sku_list[$value['sp_value_list']]['sku_code'][$key]   = $value['sku_code'];
                    $sku_list[$value['sp_value_list']]['price'][$key]      = $value['price'];
                    $sku_list[$value['sp_value_list']]['cost_price'][$key] = $value['cost_price'];
                }
                foreach ($sku_list as $key => $val) {
                    $sku_code       = array_unique($val['sku_code']);
                    $new_sku_code   = [];
                    $new_price      = [];
                    $new_cost_price = [];
                    foreach ($sku_code as $k => $v) {
                        $new_sku_code[]   = $val['sku_code'][$k];
                        $new_price[]      = $val['price'][$k];
                        $new_cost_price[] = $val['cost_price'][$k];
                    }
                    $new_sku_list[$key]['sku_code']   = implode(',', $new_sku_code);
                    $new_sku_list[$key]['price']      = implode(',', $new_price);
                    $new_sku_list[$key]['cost_price'] = implode(',', $new_cost_price);
                }
                $this->assign('sku_list', array_values($new_sku_list));
                $this->assign('select_tab', []);
                $this->assign('tab_data', []);
            } elseif (in_array($row['dd_commodity_type'], [1, 3, 12])) {
                $tab_list     = [
                    'A1' => 'T0:上海',
                    'A'  => 'T1:A类',
                    'B'  => 'T2:准A类',
                    'C'  => 'T3:B类',
                    'D'  => 'T4:C类',
                ];
                $new_sku_list = [];
                $sku_list     = [];
                $tab_data     = [];
                foreach ($res['sku_list'] as $key => $value) {
                    if (!empty($value['city_type'])) {
                        $tab_data[$value['city_type']] = $tab_list[$value['city_type']];
                        $tab[]                         = $value['city_type'];
                        if (isset($new_sku_list[$value['city_type']][$value['sp_value_list']])) {
                            $sku_code       = explode(',', $value['sku_code']);
                            $new_sku_code   = [];
                            $new_price      = [];
                            $new_cost_price = [];
                            foreach ($sku_code as $v) {
                                $new_sku_code[]   = $v;
                                $new_price[]      = $value['price'];
                                $new_cost_price[] = $value['cost_price'];
                            }
//                            dump($new_sku_list[$value['city_type']][$value['sp_value_list']]['price']);
//                            dd($new_sku_list[$value['city_type']][$value['sp_value_list']]['sku_code']);
                            $new_sku_list[$value['city_type']][$value['sp_value_list']]['sku_code']   = $new_sku_list[$value['city_type']][$value['sp_value_list']]['sku_code'] . ',' . implode(',', $new_sku_code);
                            $new_sku_list[$value['city_type']][$value['sp_value_list']]['price']      = $new_sku_list[$value['city_type']][$value['sp_value_list']]['price'] . ',' . implode(',', $new_price);
                            $new_sku_list[$value['city_type']][$value['sp_value_list']]['cost_price'] = $new_sku_list[$value['city_type']][$value['sp_value_list']]['cost_price'] . ',' . implode(',', $new_cost_price);
                        } else {
                            $sku_code       = explode(',', $value['sku_code']);
                            $new_sku_code   = [];
                            $new_price      = [];
                            $new_cost_price = [];
                            foreach ($sku_code as $v) {
                                $new_sku_code[]   = $v;
                                $new_price[]      = $value['price'];
                                $new_cost_price[] = $value['cost_price'];
                            }
                            $new_sku_list[$value['city_type']][$value['sp_value_list']]               = $value;
                            $new_sku_list[$value['city_type']][$value['sp_value_list']]['sku_code']   = implode(',', $new_sku_code);
                            $new_sku_list[$value['city_type']][$value['sp_value_list']]['price']      = implode(',', $new_price);
                            $new_sku_list[$value['city_type']][$value['sp_value_list']]['cost_price'] = implode(',', $new_cost_price);
                        }
                        unset($new_sku_list[$value['city_type']][$value['sp_value_list']]['relate_car_18n'], $new_sku_list[$value['city_type']][$value['sp_value_list']]['relate_car_ids']);
                        $sku_list[$value['city_type']][$value['sp_value_list']]['sku_code'][$key]   = $value['sku_code'];
                        $sku_list[$value['city_type']][$value['sp_value_list']]['price'][$key]      = $value['price'];
                        $sku_list[$value['city_type']][$value['sp_value_list']]['cost_price'][$key] = $value['cost_price'];
                    }
                }
                $new_tab = [];
                foreach ($tab_data as $key => $value) {
                    $new_tab[] = [
                        'id'   => $key,
                        'name' => $value
                    ];
                }
                if (!empty($new_tab)) {
                    $res['sp_title'][] = '地区';
                }

                if (empty($new_tab)) {
                    foreach ($tab_list as $key => $value) {
                        if ($key == "A1") {
                            $new_tab[] = [
                                'id'   => $key,
                                'name' => $value
                            ];
                        }
                    }
                }
                $this->assign('tab_data', $new_tab);
                if (empty($new_sku_list)) {
                    $new_sku_list = [];
                    foreach ($res['sku_list'] as $key => $value) {
                        if (empty($value['city_type'])) {
                            $new_sku_list['A1'][$value['sp_value_list']] = $value;
                        }
                    }
                    $this->assign('sku_list', $new_sku_list);
                }
                $this->assign('sku_list', $new_sku_list);
                $this->assign('select_tab', []);
            } elseif ($row['dd_commodity_type'] == 4) {
                $tab_list     = ['0' => '0-普通', '1' => '1-全合成'];
                $sku_list     = [];
                $tab_data     = [];
                $new_sku_list = [];
                foreach ($res['sku_list'] as $key => $value) {
                    $tab_data[$value['upgrade_type']] = $tab_list[$value['upgrade_type']];
                    $tab[]                            = $value['upgrade_type'];
                    $sku_codes                        = explode(',', $value['sku_code']);
                    $price                            = [];
                    foreach ($sku_codes as $v) {
                        $price[] = $value['price'];
                    }
                    $new_sku_list[$value['upgrade_type']][$value['sp_value_list']]                 = $value;
                    $new_sku_list[$value['upgrade_type']][$value['sp_value_list']]['price']        = implode(',', $price);
                    $sku_list[$value['upgrade_type']][$value['sp_value_list']]['sku_code'][$key]   = $value['sku_code'];
                    $sku_list[$value['upgrade_type']][$value['sp_value_list']]['price'][$key]      = $value['price'];
                    $sku_list[$value['upgrade_type']][$value['sp_value_list']]['cost_price'][$key] = $value['cost_price'];
                }
                $new_tab    = [];
                $select_tab = [];
                foreach ($tab_data as $key => $value) {
                    $select_tab[] = $key;
                    $new_tab[]    = [
                        'id'   => $key,
                        'name' => $value
                    ];
                }
                $res['sp_title'][] = '地区';
                if (empty($new_tab)) {
                    $select_tab[]      = 0;
                    $res['sp_title'][] = '地区';
                    $new_tab[]         = ['id' => 0, 'name' => '0-普通'];
                    foreach ($res['sku_list'] as $value) {
                        $new_sku_list[0] = $value;
                    }
                }
                $this->assign('select_tab', $select_tab);
                $this->assign('tab_data', $new_tab);
                $this->assign('sku_list', $new_sku_list);
            } elseif ($row['dd_commodity_type'] == 7) {
                $sku_list     = [];
                $new_sku_list = [];
                foreach ($res['sku_list'] as $key => $value) {
                    $new_sku_list[$value['sp_value_list']]               = $value;
                    $sku_list[$value['sp_value_list']]['sku_code'][$key] = $value['sku_code'];
                }
                foreach ($sku_list as $key => $val) {
                    $sku_code     = array_unique($val['sku_code']);
                    $new_sku_code = [];
                    foreach ($sku_code as $k => $v) {
                        $new_sku_code[] = $val['sku_code'][$k];
                    }
                    $new_sku_list[$key]['sku_code'] = implode(',', $new_sku_code);
                }
                $this->assign('sku_list', array_values($new_sku_list));
                $this->assign('select_tab', []);
                $this->assign('tab_data', []);
            } else {
//                dump($res['sku_list']);
//                if (empty($res['sku_list'])){
//                    $res['sku_list'][0] = [
//                        'id' => '',
//                        'sp_value_id_1' => '',
//                        'sp_value_id_2' => '',
//                        'price' => '',
//                        'cost_price' => '',
//                        'tax' => '',
//                        'stock' => '',
//                        'sp_value_list' => '',
//                        'sku_code' => '',
//                        'image' => '',
//                        'tax_code' => '',
//                        'card_id' => '',
//                        'sp_value_arr' => [],
//                    ];
//                }
                $this->assign('tab_data', []);
                $this->assign('select_tab', []);
                $this->assign('sku_list', $res['sku_list']);
            }
            $this->assign('tabs_list', $tab_list);
            $this->assign('tab', array_values(array_unique($tab)));
            $this->assign('sp_title', $res['sp_title']);
            $this->assign('sp_list', $res['sp_list']);
            $this->assign('card_name_list', $card_name_list);
            $this->assign('sku_image', json_decode($row['sku_image'], true));

//            if($row['comm_type_id'] != input('get.commodity_type_id') && empty()){
//
//            }
            $commodity_count  = Db::name('db_commodity_set_sku')->alias('a')
                ->join('db_commodity b', 'a.commodity_id = b.id')
                ->join('db_commodity_set c', 'a.commodity_id = c.commodity_id and c.is_enable = 1')
                ->where(['a.commodity_id' => $commodity_id, 'a.is_enable' => 1, 'c.is_enable' => 1])
                ->group('a.commodity_id')
                ->field('b.commodity_name,b.id,a.group_sub_commodity_id,c.up_down_channel_name')
                ->count();
            $commodity_count1 = Db::name('db_commodity_set_sku')->alias('a')
                ->join('db_commodity b', 'a.commodity_id = b.id')
                ->join('db_commodity_set c', 'a.commodity_id = c.commodity_id and c.is_enable = 1')
                ->where(['a.group_sub_commodity_id' => $commodity_id, 'a.is_enable' => 1, 'c.is_enable' => 1])
                ->group('a.commodity_id')
                ->field('b.commodity_name,b.id,a.group_sub_commodity_id,c.up_down_channel_name')
                ->count();
            $is_show          = 0;
            if ($commodity_count != 0 || $commodity_count1 != 0) {
                $is_show = 1;
            }

            $this->assign('is_show', $is_show);
            $type_id = !empty($commodity_type_id) ? $commodity_type_id : $row['comm_type_id'];
            $this->assign('row', $row);
        }
        $sys_model = new DbSystemValue();
        $supp_list = $sys_model->getList(['where' => ['value_type' => 22]]);

        //获取商品分类
        $brands_model   = new DbBrands();
        $brands_list    = $brands_model->getList(['where' => ['is_enable' => 1], 'field' => 'id,brands_name']);
        $commodity_type = $this->comm_type_model->getSubParent($type_id);
        if (empty($commodity_type)) {
            $commodity_type['pparent_type_name'] = '';
            $commodity_type['parent_type_name']  = '';
            $commodity_type['comm_type_name']    = '';
            $commodity_type['spec_id']           = '';
            $commodity_type['id']                = '';
        }

        $car_model       = new DbCarSeries();
        $sp_model        = new DbSpecValue();
        $car_series_list = $car_model->getAllCarSeriesList();
        $this->assign('commodity_type', $commodity_type);
        $this->assign('car_series_list', $car_series_list);
        $this->assign('commodity_class', DbCommodity::commodityClass());
        //获取规格值
        $this->assign('commodity_id', $commodity_id);
        $this->assign('is_integral', $sp_model->getAllSpec(['b.id' => ['in', explode(',', '292')]]));
        $this->assign('sp_value', $sp_model->getAllSpec(['b.id' => ['in', explode(',', $commodity_type['spec_id'])]]));

        $this->assign('step', 2);
        $this->assign('set_type', $set_type);
        $this->assign('brand_s', DbDlr::brandType());
        $this->assign('shelves_sources', DbCommodity::shelvesSources());
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('brands_list', $brands_list);
        $this->assign('work_hour_type', DbCommodity::workHourType());
        $this->assign('dd_commodity_type', DbCommodity::commodityDdType());

        $this->assign('supp_list', $supp_list);
        $this->assign('is_grouped', $is_grouped);


        if (empty($commodity_id)) {
            if (empty($is_grouped)) {
                return $this->fetch('add_step_2');
            } else {
                return $this->fetch('add_step_group_2');
            }
        } else {
            if (empty($is_grouped)) {
                return $this->fetch('update_step_2');
            } else {
                return $this->fetch('update_step_group_2');
            }
        }
    }

    //查看上架商品
    public function shelf_commodity()
    {
        $commodity_id            = input('commodity_id');
        $pagesize                = 10;
        $query                   = input('get.');
        $where['a.commodity_id'] = $commodity_id;
        $where['a.is_enable']    = 1;
        $com_id                  = Db::name('db_commodity_set_sku')->alias('a')
            ->where($where)
            ->whereOr(['a.group_sub_commodity_id' => $commodity_id])->group('a.commodity_id')
            ->field('a.group_sub_commodity_id,a.commodity_id')
            ->select();
        $id                      = [];
        foreach ($com_id as $value) {
            $id[] = $value['commodity_id'];
            if ($value['group_sub_commodity_id'] != 0) {
                $id[] = $value['group_sub_commodity_id'];
            }
        }
        if (!empty($id)) {
            $list = Db::name('db_commodity_set_sku')->alias('a')
                ->join('db_commodity b', 'a.commodity_id = b.id')
                ->join('db_commodity_set c', 'a.commodity_id = c.commodity_id')
                ->where(['a.commodity_id' => array('in', $id), 'c.up_down_channel_name' => array('neq', '')])
                ->whereOr(['a.group_sub_commodity_id' => array('in', $id)])
                ->group('c.up_down_channel_name')
                ->field('b.commodity_name,b.id,a.group_sub_commodity_id,c.up_down_channel_name')
                ->paginate($pagesize, false, array('query' => $query));
            $data = $list->all();
            foreach ($data as $key => $value) {
                if ($value['group_sub_commodity_id'] != 0) {
                    $value['self_commodity_name'] = '组合商品';
                } else {
                    $value['self_commodity_name'] = '普通商品';
                }
                $list[$key] = $value;
            }
            $page = $list->render();
            $this->assign('list', $list);
            $this->assign('page', $page);//分页
            return $this->fetch('shelf_commodity');
        }

    }

    public function getIndexSku()
    {
        $commodity_id     = input('commodity_id', 30);
        $commodity_set_id = input('commodity_set_id', 0);
        // $list=$this->sku_model->getList(['where'=>['commodity_id'=>$commodity_id,'is_enable'=>1]]);
        $ser_com_model = new CommodityService();
        if (empty($commodity_set_id)) {
            $res = $ser_com_model->getDefaultSku($commodity_id, 1);
        } else {
            $res = $ser_com_model->getSetSku($commodity_set_id, 1);
        }

//          echo $ser_com_model->getLastSql();exit;
        $this->assign('sku_list', $res['sku_list']);
        $this->assign('sp_title', $res['sp_title']);
        $this->assign('sp_list', $res['sp_list']);
        return $this->fetch('get_index_sku');
        // print_r($res);
    }


    public function getCommodityType()
    {
        $this->_checkAjax();
        $parentid = input('get.parentid');
        if (empty($parentid)) print_json(1, '');
        $t_model = new DbCommodityType();
        $list    = $t_model->getCommodityByParentId($parentid);
        print_json(0, '', $list);
    }

    public function getBjTable()
    {
        $other_attr         = $this->spare_part->otherAttr();
        $variety_big        = $this->spare_part->varietyBig();
        $variety_small      = $this->spare_part->varietySmall();
        $variety_categories = $this->spare_part->categories();
        $brad               = $this->spare_part->partBrand();
        $this->assign('other_attr', $other_attr);
        $this->assign('variety_big', $variety_big);
        $this->assign('variety_small', $variety_small);
        $this->assign('variety_categories', $variety_categories);
        $this->assign('bj_type', $_GET['bj_type'] ?? '');
        $this->assign('appoint', $_GET['appoint'] ?? 0);
        $this->assign('part_brand', $brad);
        $this->assign('sku_code', $_GET['sku_code']);
        return $this->fetch('select_bj');
    }


    public function getYanbaoTable()
    {

        $this->assign('n_product_code', $_GET['n_product_code'] ?? '');
        $this->assign('appoint', $_GET['appoint'] ?? '');
        return $this->fetch('select_yan_bao');

    }

    public function selectInvoiceType()
    {


    }

    public function getBjCarTable()
    {
        $this->assign('bj_type', $_GET['bj_type'] ?? '');
        return $this->fetch('select_bj_car');
    }

    //工时
    public function getHoursTable()
    {
        $this->assign('appoint', $_GET['appoint'] ?? 0);
        return $this->fetch('select_hours');
    }

    //pz1a 套餐
    public function getPz1aTable()
    {
        $pz1a = new E3sPz1aMaintenancePackage();
        $this->assign('level', $pz1a->getLevel());
        $this->assign('appoint', $_GET['appoint'] ?? 0);
        return $this->fetch('select_pz1a');
    }

    public function getPz1aCarTable()
    {
        $this->assign('bj_type', $_GET['bj_type'] ?? '');
        return $this->fetch('select_pz1a_car');
    }

    public function getSkuTable()
    {
        $sku_data[] = [
            'sku_list' => 0,
            'group'    => [],
        ];
        $sp_title   = [];
        $package    = 0;
        $tab        = [];
        if (isset($_GET['sp_value_id_arr']) && !empty($_GET['sp_value_id_arr'])) {
            $sp_value_id_arr = $_GET['sp_value_id_arr'];
            $sp_model        = new DbSpecValue();
            $list            = $sp_model->getSpecByids($sp_value_id_arr);
            $data            = [];
            $sp_title        = [];
            $sku_data        = [];
            if (!empty($list)) {
                foreach ($list as $key1 => $val1) {
                    $data[]     = $val1['sp_value'];
                    $sp_title[] = $val1['sp_name'];
                }
                $sku_data = $sp_model->specDikaer($data);
            }
        }
        if (isset($_GET['bj_type'])) {
            if (($_GET['bj_type'] == 1 || $_GET['bj_type'] == 3 || $_GET['bj_type'] == 12)) {
                $package   = 1;
                $area_name = isset($_GET['area_value_name']) ? $_GET['area_value_name'] : [];
                $area_id   = isset($_GET['area_value_id']) ? $_GET['area_value_id'] : [];
                if (!empty($area_name)) {
                    $sp_title[] = '地区';
                }
                foreach ($area_name as $key => $value) {
                    $tab[] = [
                        'id'   => $area_id[$key],
                        'name' => $value
                    ];
                }
            } elseif ($_GET['bj_type'] == 4) {
                $package   = 1;
                $area_name = isset($_GET['area_type_value_name_arr']) ? $_GET['area_type_value_name_arr'] : [];
                $area_id   = isset($_GET['area_type_value_id_arr']) ? $_GET['area_type_value_id_arr'] : [];

                if (!empty($area_name)) {
                    $sp_title[] = '升级类型';
                }
                foreach ($area_name as $key => $value) {
                    $tab[] = [
                        'id'   => $area_id[$key],
                        'name' => $value
                    ];
                }
            }
        }
        $this->assign('package', $package);
        $this->assign('sku_data', $sku_data);
        $this->assign('sp_title', $sp_title);
        $this->assign('tab', $tab);
        $this->assign('bj_type', $_GET['bj_type'] ?? '');
        return $this->fetch('get_sku_table');
    }

    public function validateSpec()
    {
        $belong_zone_code        = input('belong_zone_code');
        $e3s_maintenance_package = new E3sPackage();
        $other_attr              = $this->spare_part->otherAttr();
        $variety_big             = $this->spare_part->varietyBig();
        $variety_small           = $this->spare_part->varietySmall();
        $variety_categories      = $this->spare_part->categories();
        $brad                    = $e3s_maintenance_package->package_brand();
        $part                    = new E3sPackagePart();
        $liters                  = $part->where(['maintain_parttype' => 'ENGINEOIL'])->group('part_name')->field('part_name,part_no')->select();
        $oil_supplier            = $part->where(['maintain_parttype' => 'ENGINEOIL'])->group('part_no')->field('part_no')->select();
        $oil                     = [];
        foreach ($oil_supplier as $value) {
            $res = $e3s_maintenance_package->oil_supplier(substr($value['part_no'], -4));
            if (!empty($res)) {
                $oil[substr($value['part_no'], -4)] = $res;
            }
        }
        $product_variety_name_arr = [
            ['name' => '心悦保养套餐'],
            ['name' => '双保升级套餐'],
            ['name' => '老友惠保养套餐'],
            ['name' => '五年双保专享心悦套餐'],
        ];
        $this->assign('other_attr', $other_attr);
        $this->assign('variety_big', $variety_big);
        $this->assign('variety_small', $variety_small);
        $this->assign('variety_categories', $variety_categories);
        $this->assign('bj_type', $_GET['bj_type'] ?? '');
        $this->assign('appoint', $_GET['appoint'] ?? 0);
        $this->assign('car_brand', $brad);
        $this->assign('sku_code', $_GET['validate_goods_spec'] ?? '');
        $this->assign('discount', $_GET['discount'] ?? ''); // 折扣
        $this->assign('belong_zone_code', $e3s_maintenance_package->area_list([]));
        $this->assign('liters', $liters);
        $this->assign('oil_supplier', array_unique($oil));
        $this->assign('address_key', $_GET['address_key'] ?? '');
        $this->assign('product_variety_name_arr', $product_variety_name_arr);
        return $this->fetch('select_taocan');
    }

    private function findCarType($sku_codes, $type = 0)
    {

        $error = [];
        if (empty($sku_codes)) {
            return $error;
        }
        $city_type = [];
        $part_time = [];
        if ($type == 0) {
            $spare_part    = new \app\common\model\e3s\E3sSparePart();
            $model         = new \app\common\model\e3s\E3sPartCarSeries();
            $CarSeries     = new \app\common\model\e3s\E3sCarSeries();
            $part_list     = $spare_part->where(['part_no' => $sku_codes, 'is_enable' => 1])
                ->field('part_no,sale_price,dlr_price,variety_code_mid_code,variety_code,variety_name')
                ->select();
            $dlr_code      = new E3sSpecificRelationPart();
            $part_del_code = $dlr_code->alias('a')->join('t_e3s_specific_relation_dlr b', 'a.spec_part_group_id = b.spec_part_group_id')
                ->where(['a.is_enable' => 1, 'b.is_enable' => 1, 'a.part_no' => $sku_codes])->column('b.dlr_code');
            $data          = [];
            foreach ($part_list as $value) {
                // 1120版本不上 start
                $fit_beg_time = $model->alias('a')
                    ->join('t_e3s_spare_part b', 'a.part_no = b.part_no')
                    ->where('a.part_no', $value['part_no'])
                    ->where('a.fit_beg_time', '<>', '')
                    ->where('a.is_enable', '=', 1)
                    ->field('a.part_no,b.sale_price,a.fit_beg_time,a.fit_end_time,b.variety_code_mid_code')
                    ->group('a.part_no,a.fit_beg_time,a.fit_end_time')
                    ->buildSql();
                $rep_part_no  = $model->alias('a')
                    ->join('t_e3s_spare_part b', 'a.part_no = b.part_no')
                    ->where(['b.rep_part_no' => array('like', '%' . $value['part_no'] . '%')])
                    ->where('a.fit_beg_time', '<>', '')
                    ->where('a.is_enable', '=', 1)
                    ->field('a.part_no,b.sale_price,a.fit_beg_time,a.fit_end_time,b.variety_code_mid_code')
                    ->union($fit_beg_time, false)
                    ->group('a.part_no,a.fit_beg_time,a.fit_end_time')
                    ->select();//查询原件是否是被人的替换件
                if (!empty($rep_part_no)) {
                    foreach ($rep_part_no as $k=>$val){
                        $n18 = $model->where(['part_no' => $val['part_no'], 'fit_beg_time' => $val['fit_beg_time'],
                            'fit_end_time' => $val['fit_end_time'], 'is_enable' => 1,
                        ])->column('car_config_code');
                        $work = $this->part_work_hour($val['part_no'],$val['fit_beg_time'],$val['fit_end_time']);
                        if(in_array($val['variety_code_mid_code'],[3,4,5,6,7])){
                            $val['fit_beg_time'] = $val['fit_end_time']='';// 只过滤备件上下架时间
                        }
                        $part_time = $val['fit_beg_time'].'_'.$val['fit_end_time'];
                        if(isset($data[$part_time])){
                            $array_18n = array_unique(array_merge(explode(',',$data[$part_time]['n_18']),$n18));
                            $data[$part_time]['n_18'] = implode(',',$array_18n);
                            $data[$part_time]['relate_car_work_hour'] = $data[$part_time]['relate_car_work_hour'] + $work['part_no_array'];//暂时没有这个工时的东西，后续要了再打开
//                            $data[$part_time]['relate_car_work_hour'] = [];

                        }else{
                            $data[$part_time] = [
                                'n_18' => implode(',',$n18),
                                'fit_beg_time' => $val['fit_beg_time'],
                                'fit_end_time' => $val['fit_end_time'],
                                'sku_code' => $value['part_no'],
                                'price' => $value['sale_price'],
                                'cost_price' => $value['dlr_price'],
                                'relate_car_work_hour' => [],//$work['part_no_array']
                                'e3s_bj_type_name' => implode(',', $work['bj_type_name']),
                                'rep_part_no' => '',
                                'relate_dlr_code' => implode(',',$part_del_code),
                            ];
                        }
                        $data[$part_time]['variety_code'] = $value['variety_code'];
                        $data[$part_time]['variety_name'] = $value['variety_name'];
                    }
                    $data = array_values($data);
                    foreach ($data as $key=>$item){
                        $relate_cars = $CarSeries->whereIn('car_config_code', $item['n_18'])
                            ->where('is_enable','=',1)->column('id');
                        $data[$key]['car_ids'] = implode(',',$relate_cars);
                        $data[$key]['relate_car_work_hour'] = json_encode($item['relate_car_work_hour']);

                    }
                }
            }
            return $data;
//            return ['n_18' => $relate_n18, 'car_ids' => $relate_cars,'part_time'=>$part_time];
        } elseif ($type == 2) {
            //pz1a套餐
            $where['a.sp_basic_code']     = array('in', $sku_codes);
            $city_type                    = Db::name('e3s_pz1a_maintenance_package')->alias('a')
                ->where($where)
                ->field('level_id')
                ->find();
            $city_type                    = $city_type['level_id'];
            $relate_n18                   = Db::name('e3s_pz1a_maintenance_package')->alias('a')
                ->join('t_e3s_pz1a_maintenance_car_series b', 'a.sp_basic_id = b.sp_basic_id')
                ->join('t_e3s_car_series c', 'b.service_car_type = c.service_car_type')
                ->where($where)
                ->group('c.car_config_code')
                ->column('c.car_config_code');
            $res_where['car_config_code'] = array('in', array_values(array_unique($relate_n18)));
            $res_where['is_enable']       = 1;
            $relate_cars                  = \app\common\model\e3s\E3sCarSeries::where($res_where)
                ->column('id');
        } elseif ($type == 1) {
            //老友惠和心悦获取18为码
            $where['a.maintain_group_code'] = array('in', $sku_codes);
            $where['a.is_enable']           = 1;
            $list                           = Db::name('e3s_maintenance_package')->alias('a')
                ->join('t_e3s_maintenance_product_car_series b', 'a.product_type_id = b.product_type_id')
                ->where($where)
                ->group('a.product_type_id')
                ->column('a.product_type_id');
            if (empty($list)) {
                $relate_n18  = [];
                $relate_cars = [];
            } else {
                $relate_n18                   = Db::name('e3s_maintenance_product_car_series')->alias('a')
                    ->join('t_e3s_car_series b', 'a.service_car_type = b.service_car_type')
                    ->where(['a.product_type_id' => array('in', $list), 'a.is_enable' => 1])
                    ->group('b.car_config_code')
                    ->column('b.car_config_code');
                $res_where['car_config_code'] = array('in', array_values(array_unique($relate_n18)));
                $res_where['is_enable']       = 1;
                $relate_cars                  = \app\common\model\e3s\E3sCarSeries::where($res_where)
                    ->column('id');
            }
        } else {
            $where['a.maintain_group_code'] = array('in', $sku_codes);
            $where['b.service_car_type']    = 'COMMOM_CARTYPE';
            $where['a.is_enable']           = 1;
            $where['b.is_enable']           = 1;
            $list                           = Db::name('e3s_maintenance_package')->alias('a')
                ->join('t_e3s_maintenance_product_car_series b', 'a.product_type_id = b.product_type_id')
                ->where($where)
                ->group('a.maintain_group_code')
                ->select();
            if (empty($list)) {
                $relate_n18 = Db::name('e3s_maintenance_package')->alias('a')
                    ->join('t_e3s_maintenance_product_car_series b', 'a.product_type_id = b.product_type_id')
                    ->join('t_e3s_car_series c', 'b.service_car_type = c.service_car_type')
                    ->where([
                        'a.maintain_group_code' => array('in', $sku_codes),
                        'a.is_enable'           => 1,
                        'b.is_enable'           => 1,
                        'c.is_enable'           => 1,
                    ])
                    ->column('c.car_config_code');
            } else {
                $relate_n18 = \app\common\model\e3s\E3sCarSeries::where('car_brand_code', $list[0]['dlr_brand_code'])
                    ->where('is_enable', '=', 1)
                    ->column('car_config_code');
            }
            $res_where['car_config_code'] = array('in', array_values(array_unique($relate_n18)));
            $res_where['is_enable']       = 1;
            $relate_cars                  = \app\common\model\e3s\E3sCarSeries::where($res_where)
                ->column('id');
        }
        return ['error' => $error, 'n_18' => array_values(array_unique($relate_n18)), 'car_ids' => array_unique($relate_cars), 'city_type' => $city_type, 'part_time' => $part_time];
    }

    private function work_hour($sku_code)
    {
        $error = [];
        if (empty($sku_code)) {
            return $error;
        }
        $where['a.part_no']   = array('in', $sku_code);
        $where['a.is_enable'] = 1;
        $where['b.is_enable'] = 1;
        //计算备件是否有重复的数据
        $query         = Db::name('e3s_part_car_series')->alias('a')
            ->join('t_e3s_spare_part b', 'a.part_no = b.part_no', 'left')
            ->where($where)
            ->field(' a.id,a.car_config_code,a.part_no,b.dlr_price,a.wi_code,a.wi_qty,b.variety_code_big_name,b.variety_code_small_name')
            ->order('b.dlr_price asc')
            ->buildSql();
        $res           = Db::table($query . 'c')
            ->group('car_config_code')
            ->select();
        $part_no_array = [];
        $array         = [];
        foreach ($res as $value) {
            $part_no_array[$value['id']][] = $value['car_config_code'];
            if ($value['variety_code_big_name'] != '养护品') {
                $array[] = $value['variety_code_small_name'];
            }
        }
        return ['part_no_array' => $part_no_array, 'bj_type_name' => array_unique($array)];
    }

    //备件新增时间查询车型工时和备件类型名称
    private function part_work_hour($part_no, $start_time, $end_time)
    {
        $list          = Db::name('e3s_part_car_series')->alias('a')
            ->join('t_e3s_spare_part b', 'a.part_no = b.part_no', 'left')
            ->where(['a.part_no' => $part_no, 'a.fit_beg_time' => $start_time, 'a.fit_end_time' => $end_time])//备件时间
            ->field('a.id,a.car_config_code,b.variety_code_big_name,b.variety_code_small_name')
            ->select();
        $part_no_array = [];
        $array         = [];
        foreach ($list as $value) {
            $part_no_array[$value['id']][] = $value['car_config_code'];
            if ($value['variety_code_big_name'] != '养护品') {
                $array[] = $value['variety_code_small_name'];
            }
        }
        return ['part_no_array' => $part_no_array, 'bj_type_name' => array_unique($array)];
    }

    private function sku_price_sku_code($price, $sku_code, $sku_list, $dd_commodity_type)
    {
        $data         = [];
        $new_price    = [];
        $new_sku_code = [];
        $price1       = [];
        if (count($price) != count($sku_code)) {
            if (count($price) == 1) {
                if (count($price) < count($sku_code)) {
                    foreach ($price as $v) {
                        foreach ($sku_code as $value) {
                            $price1[] = $v;
                        }
                        break;
                    }
                }
            }
        } else {
            $price1 = $price;
        }
        foreach ($price1 as $k => $val) {
            if (!in_array($val, $new_price)) {
                $new_price[] = floor($val);
            }
            $new_sku_code[floor($val)][] = $sku_code[$k];
        }
        foreach ($new_price as $value) {
//            $maintain_q = Db::name('e3s_maintenance_package')->where(['maintain_group_code'=>array('in',$new_sku_code[$value])])->field('discount,maintain_total_count')->find();
//            $maintain_q = [];
            $discount             = '';
            $maintain_total_count = '';
//            if(!empty($maintain_q)){
//                $discount = $maintain_q['discount']*10;
//                $maintain_total_count = $maintain_q['maintain_total_count'];
//            }
            if ($dd_commodity_type != 4) {
                $data[] = [
                    'sp_value_list' => $sku_list['sp_value_list'],
                    'price'         => $value,
                    'stock'         => $sku_list['stock'],
                    'image'         => $sku_list['image'],
                    'sku_code'      => implode(',', array_unique($new_sku_code[$value])),
                    'tax'           => $sku_list['tax'],
                    'tax_code'      => $sku_list['tax_code'],
                    'cost_price'    => '',//计算成本价
                    'card_id'       => $sku_list['card_id'],
                    'hours_id'      => $sku_list['hours_id'],
                    'city_type'     => $sku_list['city_type'],
                    'upgrade_type'  => '',
                    //                    'maintain_q' => $discount,
                    //                    'maintain_num' => $maintain_total_count
                ];
            } else {
                $data[] = [
                    'sp_value_list' => $sku_list['sp_value_list'],
                    'price'         => $value,
                    'stock'         => $sku_list['stock'],
                    'image'         => $sku_list['image'],
                    'sku_code'      => implode(',', array_unique($new_sku_code[$value])),
                    'tax'           => $sku_list['tax'],
                    'tax_code'      => $sku_list['tax_code'],
                    'cost_price'    => '',//计算成本价
                    'card_id'       => $sku_list['card_id'],
                    'hours_id'      => $sku_list['hours_id'],
                    'city_type'     => '',
                    'upgrade_type'  => $sku_list['city_type'],
                    //                    'maintain_q' => $discount,
                    //                    'maintain_num' => $maintain_total_count
                ];
            }
        }
        return $data;
    }

    private function package_sku($sku_list, $dd_commodity_type)
    {
        $data = [];
        foreach ($sku_list as $value) {
            $price    = explode(',', $value['price']);
            $sku_code = explode(',', $value['sku_code']);
            $new_sku  = $this->sku_price_sku_code($price, $sku_code, $value, $dd_commodity_type);
            foreach ($new_sku as $val) {
                $data[] = $val;
            }
        }
        return $data;
    }

    public function save()
    {
        $post = input('post.');
//        print_json($post);
        set_time_limit(0);
        ini_set('memory_limit', '4096M');
        $admin_name = $this->admin_info['username'];
//        try {
//            $commodity_log = new DbCommodityLog();
//            $commodity_log->insertData([
//                'type' => input('post.action'),
//                'receive_note' => json_encode(input('post.')),
//            ]);
//        }catch (Exception $exception){
//            Logger::info('pay-ment-settNotice-callback', $exception->getMessage());
//        }
        if (!isset($_POST['car_series_id'])) {
            $car_series_id = [];
        } else {
            $car_series_id = $_POST['car_series_id'];
        }
        //套餐商品转回数组
        if (in_array(input('post.dd_commodity_type'), [1, 3, 4, 12])) {
            if (is_array($_POST['sku_list'])) {
                $sku_list = $_POST['sku_list'];
            } else {
                $sku_list = json_decode($_POST['sku_list'], true);
            }
        } else {
            if (isset($_POST['sku_list']) && is_array($_POST['sku_list'])) {
                $sku_list = $_POST['sku_list'];
            } else {
                print_json(1, '商品规格不能为空');
            }
        }

        if ($this->admin_info['type'] == 1) {
            $shelves_sources = empty($_POST['shelves_sources']) ? print_json(1, '上架源不能为空', json_encode_cn(input('post.'))) : $_POST['shelves_sources'];
            $shelves_sources = implode(',', $shelves_sources);
        } else {
            $shelves_sources = 2;
        }

        $dd_commodity_type = input('post.dd_commodity_type', 0);
        $commodity_name = input('post.commodity_name');
        $commodity_class = input('post.commodity_class');
        if ($commodity_class == DbCommodity::COMMODITY_CLASS_KEY9) {
            $dd_commodity_type = 0;
        }

        $bs_data      = [
            'commodity_name'             => trim($commodity_name),
            //   'commodity_attr'=>input('post.commodity_attr'),
            'is_pure'                    => input('post.is_pure'),
            'is_shop'                    => input('post.is_shop'),
            'comm_type_id'               => input('post.comm_type_id'),
            /*    'is_mail'=>input('post.is_mail'),
                'mail_price'=>input('post.mail_price'),*/
            //            'detail_content'             => input('post.content', ''),
            'sort'                       => input('post.sort'),
            'car_series_id'              => implode(',', $car_series_id),
            'original_price_range_start' => input('post.original_price_range_start'),
            'original_price_range_end'   => input('post.original_price_range_end'),
            'discount_price_range_start' => input('post.discount_price_range_start'),
            'discount_price_range_end'   => input('post.discount_price_range_end'),
            'unit'                       => input('post.unit'),
            'commodity_code'             => input('post.commodity_code', ''),
            'shelves_sources'            => $shelves_sources,
            'brands_id'                  => input('post.brands_id'),
            'video'                      => input('post.video_img'),
            'tax_code'                   => input('post.tax_code', ''),//税务编码
            'dd_commodity_type'          => $dd_commodity_type,//店商品类型 9到店备件 1保养套餐-老友惠保养套餐 3保养套餐-心悦保养套餐 4保养套餐-双保专属权益套餐 6保养套餐-其他 7到店代金券
            'arrival_bdp'                => input('post.arrival_bdp', 0),//是否给bdp;0否1是
            'supplier'                   => input('post.supplier', ''),//供应商
            'machine_oil_type'           => input('post.machine_oil_type', 0),//油型
            'work_hour_type'             => input('post.work_hour_type', 0),//工时业务类别选择
            'commodity_class'            => $commodity_class,//商品种类
            'is_integral_shop'           => input('post.is_integral_shop', 0),//积分兑换商品
            'liability_clause'            => $post['liability_clause'] ?? '',
            'liability_clause_pdf'        => $post['liability_clause_pdf'] ?? '',
            'no_nev_liability_clause'     => $post['no_nev_liability_clause'] ?? '',
            'no_nev_liability_clause_pdf' => $post['no_nev_liability_clause_pdf'] ?? '',
        ];
        $set_type     = input('post.set_type');
        $commodity_id = input('post.id');
        $is_preview   = input('post.is_preview', null);

        $card_msg = [];
        $card_ids = [];
        if (in_array($bs_data['dd_commodity_type'], [7, 8]) || $bs_data['commodity_class'] == 6) {
            foreach ($sku_list as $v) {
                if (empty($v['card_id'])) {
                    print_json(1, '请先选择卡券');
                }
                if (isset($card_msg[$v['card_id']])) {
                    $card_msg[$v['card_id']] += $v['stock'];
                } else {
                    $card_msg[$v['card_id']] = $v['stock'];
                }
                $card_ids[] = $v['card_id'];
            }
        }
        $card_list = $this->db_card->where(['id' => ['in', $card_ids]])->column('available_count,card_name', 'id');
        foreach ($card_msg as $k => $v) {
            if ($card_list[$k]['available_count'] < $v) {
                print_json(1, $card_list[$k]['card_name'] . '库存数量不足');
            }
        }

        //遍历图片
        //var_dump($_POST['image_list']);
        if (!empty($_POST['image_list'])) {
            $image_data = [];
            /* foreach($_POST['image_list'] as $key=>$val){
                 if(empty($val['image_list'])){
                     print_json(1,'上传图片不能为空');
                 }
                 $image_data[$val['sp_value_id']]=$val['image_list'];

                 if($key==0 && !isset($data['cover_image']) && !empty($val['image_list'][0])){
                     $bs_data['cover_image']=$val['image_list'][0];
                 }
             }*/
            $bs_data['cover_image'] = $_POST['image_list'][0];
            $bs_data['sku_image']   = json_encode($_POST['image_list']);
        } else {
            print_json(1, '默认图不能为空'); //调试
        }
        // var_dump($_POST);
        //设置总库存
        $bs_data['count_stock'] = 0;
        foreach ($sku_list as $key => $val) {
            $bs_data['count_stock'] += $val['stock'];
            if (!in_array($bs_data['dd_commodity_type'], [7, 8]) && input('commodity_class') != 6) {
                $sku_list[$key]['card_id'] = 0;
            }
            if (input('post.action') == 'add' && empty($val['id'])) {
                unset($sku_list[$key]['id']);
            }
        }

        if (in_array($bs_data['dd_commodity_type'], [1, 3, 4, 12])) {
            $sku_list = $this->package_sku($sku_list, $bs_data['dd_commodity_type']);
        }
        $sku_model  = new  DbCommoditySku();
        $comm_model = new  DbCommodity();

        // 商品分类提示
        $class_info_model = new DbCommodityClassInfo();
        $tips_info = $class_info_model->where(['class_id'=>$bs_data['commodity_class']])->find();
        if (empty($tips_info)) {
            // 新增
            $add = [
                'class_id' => $bs_data['commodity_class'],
                'tips' => $post['tips'] ?? '',
                'creator' => $this->admin_info['username'],
            ];
            $class_info_model->insertGetId($add);
        } else {
            // 更新
            $upd = [
                'tips' => $post['tips'] ?? '',
                'modifier' => $this->admin_info['username'],
            ];
            $class_info_model->where(['id'=>$tips_info['id']])->update($upd);
        }

        //新增
        if (input('post.action') == 'add') {
            $bs_data['create_dlr_code'] = $this->admin_info['dlr_code'];
            $bs_data['set_type']        = $this->admin_info['type'];

            $bs_data['commodity_card_ids'] = input('post.commodity_card_ids');
            if ($bs_data['commodity_class'] == 3 && empty($bs_data['commodity_card_ids'])) print_json(1, '卡券不能为空');
            if ($bs_data['commodity_class'] != 3) unset($bs_data['commodity_card_ids']);

            if ($bs_data['commodity_class'] == 5) {
                if (empty($_POST['mark_coupon_id'])) {
                    print_json(1, '平台卡券不能为空');
                }
                $coupon_model = new DbCoupon();
                $coupon       = $coupon_model->getOne(['where' => ['mark_coupon_id' => $_POST['mark_coupon_id']]]);
                if (!$coupon || ($coupon['deadline_type'] == 1 && $coupon['use_end_date'] <= date('Y-m-d H:i:s'))) {
                    print_json(1, '平台卡券不存在或已过期');
                }
                $bs_data['commodity_card_ids'] = $_POST['mark_coupon_id'];
            }

            //预览
            if ($is_preview) {
//                $prev_key = $this->_prevKey();
//                redis($prev_key . 'commodity', $bs_data, 3600);
//                redis($prev_key . 'sku', $sku_list, 3600);
//                print_json(0, 'ok', $this->_preQrCode($prev_key));
            } else {
                $bs_data['is_update'] = 1;
                $bs_data['modifier'] = $admin_name;
                $res                  = $comm_model->allowField(true)->save($bs_data);
                if ($res) {
                    $commodity_id = $comm_model->id;
                    if (in_array($bs_data['dd_commodity_type'], [1, 3, 4, 12])) {
                        $sku_model->where(['commodity_id' => $commodity_id])->delete();//删除之前的sku
                        $as_data = [];
                        $i       = 0;
                        foreach ($sku_list as $value) {
                            $i++;
                            $as_data['sku_list'][]   = $value;
                            $as_data['commodity_id'] = $commodity_id;
                            $as_data['type']         = 1;
                            $as_data['admin_name']   = $admin_name;
                            if ($i == 50) {
                                Queue::push('app\common\queue\CommoditySku', json_encode($as_data), config('queue_type.default'));
                                $i       = 0;
                                $as_data = [];
                            }
                        }
                        if (!empty($as_data)) {
                            Queue::push('app\common\queue\CommoditySku', json_encode($as_data), config('queue_type.default'));
                        }
                        print_json(0, '请等待五分钟后查看商品', ['id' => $commodity_id]);
                    } else if ($bs_data['dd_commodity_type'] == 9) {
                        //备件商品
                        foreach ($sku_list as $key => $val) {
                            $sku_list[$key]['commodity_id'] = $commodity_id;
                            $sku_code_arr                   = explode(',', $val['sku_code']);
//                            $price                   = explode(',', $val['price']);
                            if (!empty($sku_code_arr)) {
//                                $num = 0;
                                unset($val['price'], $val['sku_code'], $val['cost_price']);
                                $as_data = [];
                                foreach ($sku_code_arr as $k => $v) {

                                    $as_data['sku']          = $val;
                                    $as_data['sku_list']     = $v;
                                    $as_data['commodity_id'] = $commodity_id;
                                    $as_data['type']         = 9;
                                    $as_data['admin_name']   = $admin_name;
                                    Queue::push('app\common\queue\CommoditySku', json_encode($as_data), config('queue_type.default'));
                                }
                            }
                        }
                        print_json(0, '请等待三分钟后查看商品', ['id' => $commodity_id]);
                    } elseif ($bs_data['dd_commodity_type'] == 7) {
                        $new_sku_list_array = [];
                        foreach ($sku_list as $key => $val) {
                            if (!empty($val['sku_code'])) {
                                $sku_code_arr = explode(',', $val['sku_code']);
                                foreach ($sku_code_arr as $k => $v) {
                                    $relate_car = $this->findCarType($v, 0);
                                    foreach ($relate_car as $item) {
                                        $new_sku_list_array[$k]                         = $val;
                                        $new_sku_list_array[$k]['commodity_id']         = $commodity_id;
                                        $new_sku_list_array[$k]['sku_code']             = $v;
                                        $new_sku_list_array[$k]['is_enable']            = 1;
                                        $new_sku_list_array[$k]['relate_car_ids']       = $item['car_ids'];
                                        $new_sku_list_array[$k]['relate_car_18n']       = $item['n_18'];
                                        $new_sku_list_array[$k]['city_type']            = '';
                                        $new_sku_list_array[$k]['relate_car_work_hour'] = $item['relate_car_work_hour'];
                                        $new_sku_list_array[$k]['e3s_bj_type_name']     = $item['e3s_bj_type_name'];
                                        $new_sku_list_array[$k]['part_start_time']      = $item['fit_beg_time'];
                                        $new_sku_list_array[$k]['part_end_time']        = $item['fit_end_time'];
                                        $new_sku_list_array[$k]['relate_dlr_code']      = $item['relate_dlr_code'];
                                    }
                                }
                            } else {
                                $new_sku_list_array[$key]                 = $val;
                                $new_sku_list_array[$key]['commodity_id'] = $commodity_id;
                                $new_sku_list_array[$key]['is_enable']    = 1;
                            }
                        }
                        if (!empty($new_sku_list_array)) {
                            $sku_model->allowField(true)->saveAll($new_sku_list_array);
                        }
                        print_json(0, '添加成功', ['id' => $commodity_id]);
                    } else {
                        $new_sku_list_array = [];
                        foreach ($sku_list as $key => $val) {
                            $sku_list[$key]['commodity_id'] = $commodity_id;
                            $sku_code_arr                   = explode(',', $val['sku_code']);
                            $price                          = explode(',', $val['price']);
                            if (!empty($sku_code_arr)) {
                                if (input('post.dd_commodity_type') == 10) {
                                    $new_sku_list = [];
                                    foreach ($sku_code_arr as $k => $v) {
                                        $new_sku_list[$k]                   = $val;
                                        $new_sku_list[$k]['commodity_id']   = $commodity_id;
                                        $new_sku_list[$k]['price']          = $price[$k];
                                        $new_sku_list[$k]['sku_code']       = $v;
                                        $relate_car                         = $this->findCarType($v, 2, $sku_code_arr);
                                        $new_sku_list[$k]['relate_car_18n'] = implode(',', $relate_car['n_18']);
                                        if (empty($relate_car['car_ids'])) {
                                            $new_sku_list[$k]['relate_car_ids'] = 999999999;
                                        } else {
                                            $new_sku_list[$k]['relate_car_ids'] = implode(',', $relate_car['car_ids']);
                                        }
                                        $new_sku_list[$k]['city_type']            = $relate_car['city_type'];
                                        $relate_car_work_hour                     = $this->work_hour($v);
                                        $new_sku_list[$k]['relate_car_work_hour'] = json_encode($relate_car_work_hour['part_no_array']);
                                        $new_sku_list[$k]['e3s_bj_type_name']     = implode(',', $relate_car_work_hour['bj_type_name']);
                                    }
                                    $new_sku_list_array = array_merge($new_sku_list_array, $new_sku_list);
                                } else {
                                    $relate_car                             = $this->findCarType($sku_code_arr, 4);
                                    $sku_list[$key]['relate_car_18n']       = implode(',', $relate_car['n_18']);
                                    $sku_list[$key]['relate_car_ids']       = implode(',', $relate_car['car_ids']);
                                    $relate_car_work_hour                   = $this->work_hour($sku_code_arr);
                                    $sku_list[$key]['relate_car_work_hour'] = json_encode($relate_car_work_hour['part_no_array']);
                                    $sku_list[$key]['e3s_bj_type_name']     = implode(',', $relate_car_work_hour['bj_type_name']);
                                }
                            }
                        }
                        //插入规格
                        if ($dd_commodity_type == 10) {
                            $sku_model->allowField(true)->saveAll($new_sku_list_array);
                        } else {
                            $sku_model->allowField(true)->saveAll($sku_list);
                        }

                        // 保存预览key
                        $prev_key = 'pre_com_key' . $commodity_id;
                        redis($prev_key . 'commodity', $bs_data, 36000);
                        redis($prev_key . 'sku', $sku_list, 36000);
                        print_json(0, '添加成功', ['id' => $commodity_id]);
                    }

                } else {
                    print_json(1, $comm_model->getError());
                }
            }
        } elseif (input('post.action') == 'update') {
            // 保存预览key
            $prev_key = 'pre_com_key' . $commodity_id;
            //预览
            if ($is_preview) {
//                $prev_key = $this->_prevKey();
//                redis($prev_key . 'commodity', $bs_data, 3600);
                $bs_data_redis                   = redis($prev_key . 'commodity');
                $bs_data_redis['detail_content'] = input('post.content', '');
                redis($prev_key . 'commodity', $bs_data_redis, 3600);
                print_json(0, 'ok', $this->_preQrCode($prev_key, $commodity_id));
            } else {
                redis($prev_key . 'commodity', $bs_data, 36000);
                redis($prev_key . 'sku', $sku_list, 36000);
                if (strpos($shelves_sources, "5") !== false) {
                    $bs_data['live_refresh'] = 1;
                }
                if ($bs_data['commodity_class'] == 5) {
                    if (empty($_POST['mark_coupon_id'])) {
                        print_json(1, '平台卡券不能为空');
                    }
                    $bs_data['commodity_card_ids'] = $_POST['mark_coupon_id'];
                }
                $res = $comm_model->allowField(true)->isUpdate(true)->save($bs_data, ['id' => $commodity_id]);
                if ($res || !$comm_model->getError()) {
                    $is_off = input('is_off', 0);
                    if ($is_off == 1) {
                        $set_upd = [
                            'latest_listing_time' => '', // 最新上架时间
                            'latest_end_time' => date('Y-m-d H:i:s'),
                            'is_enable' => 0,
                            'modifier' => $admin_name,
                        ];
                        $upd = [
                            'is_enable' => 0,
                            'modifier' => $admin_name,
                        ];
                        $map = [
                            'commodity_id' => $commodity_id,
                            'is_enable' => 1
                        ];
                        $this->dbCommoditySet->where($map)->update($set_upd);

                        $this->sku_model->where($map)->update($upd);
                        //查询关联下架商品id start
                        $find = Db::name('db_commodity_set_sku')->alias('a')
                            ->join('db_commodity b', 'a.commodity_id = b.id')
                            ->join('db_commodity_set c', 'a.commodity_id = c.commodity_id')
//                            ->where(['a.commodity_id' => $commodity_id, 'a.is_enable' => 1])
//                            ->whereOr(['a.group_sub_commodity_id' => $commodity_id])
                            ->where(['a.is_enable'=>1])
                            ->where(function ($query) use ($commodity_id){
                                $query->where('a.commodity_id', '=', $commodity_id)
                                    ->whereOr('a.group_sub_commodity_id', '=', $commodity_id);
                            })
                            ->group('a.commodity_id')
                            ->field('b.id')
                            ->select();
                        if (!empty($find)) {
                            foreach ($find as $value) {
                                $commodityId = $value['id'];
                                // 下架商品
                                $map = ['commodity_id' => $value['id'], 'is_enable'=>1];
                                $this->dbCommoditySet->where($map)->update($set_upd);

                                $this->dbCommoditySetSku->where(['is_enable'=>1])
                                    ->where(function ($query) use ($commodityId) {
                                        $query->where('commodity_id', '=', $commodityId)
                                        ->whereOr('group_sub_commodity_id', '=', $commodityId);
                                    })
                                    ->update($upd);
                                DbCommodityFlat::where('commodity_id', $value['id'])->delete();
                            }
                        }
                        //查询关联下架商品id end
                    }
                    if (in_array($bs_data['dd_commodity_type'], [1, 3, 4, 12])) {
                        if ($is_off == 1 || $is_off == 2) {
                            $as_data = [];
                            $i       = 0;
                            $sku_model->where(['commodity_id' => $commodity_id])->delete();
                            foreach ($sku_list as $value) {
                                $i++;
                                $as_data['sku_list'][]   = $value;
                                $as_data['commodity_id'] = $commodity_id;
                                $as_data['type']         = 1;
                                $as_data['admin_name']   = $admin_name;
                                if ($i == 50) {
                                    //写入队列
                                    Queue::push('app\common\queue\CommoditySku', json_encode($as_data), config('queue_type.default'));
                                    $i       = 0;
                                    $as_data = [];
                                }
                            }
                            if (!empty($as_data)) {
                                Queue::push('app\common\queue\CommoditySku', json_encode($as_data), config('queue_type.default'));
                            }
                            print_json(0, '请等待五分钟后查看商品', ['id' => $commodity_id]);
                        } else {
                            print_json(0, '更新完成', ['id' => $commodity_id]);
                        }

                    }
                    elseif ($bs_data['dd_commodity_type'] == 9) {
                        $comm_model->allowField(true)->isUpdate(true)->save(['is_update' => 1], ['id' => $commodity_id]);
                        foreach ($sku_list as $key => $val) {
//                            $row   = $sku_model->getOne(['where' => ['sp_value_list' => $val['sp_value_list'],'sku_code' => $val['sku_code'], 'is_enable' => 1, 'commodity_id' => $commodity_id]]);
                            $sku_code_arr = explode(',', $val['sku_code']);
                            if (!empty($sku_code_arr)) {
                                //如果没有上架商品就修改sku
                                $as_data = [];
                                foreach ($sku_code_arr as $k => $v) {
                                    $as_data['sku']          = $val;
                                    $as_data['sku_list']     = $v;
                                    $as_data['commodity_id'] = $commodity_id;
                                    $as_data['type']         = 9;
                                    $as_data['admin_name']   = $admin_name;
                                    Queue::push('app\common\queue\CommoditySku', json_encode($as_data), config('queue_type.default'));
                                }


                            }

                        }
                        print_json(0, '修改成功', ['id' => $commodity_id]);
                    }
                    elseif ($bs_data['dd_commodity_type'] == 7) {
                        $new_sku_list_array = [];
                        $num                = 0;
                        $this->sku_model->where(['commodity_id' => $commodity_id])->update(['is_enable' => 0]);//删除原先的sku
                        foreach ($sku_list as $key => $val) {
                            if (!empty($val['sku_code'])) {
                                $sku_code_arr = explode(',', $val['sku_code']);
                                foreach ($sku_code_arr as $k => $v) {
                                    $relate_car = $this->findCarType($v, 0);
                                    foreach ($relate_car as $item) {
                                        $row                      = $sku_model->getOne(['where' => ['sp_value_list' => $val['sp_value_list'], 'commodity_id' => $commodity_id, 'sku_code' => $v, 'part_start_time' => $item['fit_beg_time'], 'part_end_time' => $item['fit_end_time']]]);
                                        $new_sku_list_array[$num] = $val;
                                        if (!empty($row)) {
                                            $new_sku_list_array[$num]['id'] = $row['id'];
                                        }
                                        $new_sku_list_array[$num]['commodity_id']         = $commodity_id;
                                        $new_sku_list_array[$num]['sku_code']             = $v;
                                        $new_sku_list_array[$num]['is_enable']            = 1;
                                        $new_sku_list_array[$num]['relate_car_ids']       = $item['car_ids'];
                                        $new_sku_list_array[$num]['relate_car_18n']       = $item['n_18'];
                                        $new_sku_list_array[$num]['city_type']            = '';
                                        $new_sku_list_array[$num]['relate_car_work_hour'] = $item['relate_car_work_hour'];
                                        $new_sku_list_array[$num]['e3s_bj_type_name']     = $item['e3s_bj_type_name'];
                                        $new_sku_list_array[$num]['part_start_time']      = $item['fit_beg_time'];
                                        $new_sku_list_array[$num]['part_end_time']        = $item['fit_end_time'];
                                        $new_sku_list_array[$num]['relate_dlr_code']      = $item['relate_dlr_code'];
                                        $num++;
                                    }
                                }
                            } else {
                                $val['commodity_id'] = $commodity_id;
                                $row = $sku_model->getOne(['where' => ['sp_value_list' => $val['sp_value_list'], 'commodity_id' => $commodity_id, 'sku_code' => $val['sku_code']]]);
                                $new_sku_list_array[$key] = $val;
                                if (!empty($row)) {
                                    $new_sku_list_array[$key]['id'] = $row['id'];
                                }

                                $new_sku_list_array[$key]['is_enable'] = 1;
                            }
                        }
                        if (!empty($new_sku_list_array)) {
                            $sku_model->allowField(true)->saveAll($new_sku_list_array);
                        }
                        print_json(0, '添加成功', ['id' => $commodity_id]);
                    }
                    else {
                        // 延保服务包
                        if ($bs_data['commodity_class'] == DbCommodity::COMMODITY_CLASS_KEY9) {
                            foreach ($sku_list as $key => $val) {
                                $sku_code_arr = explode(',', $val['sku_code']);
                                if (!empty($sku_code_arr)) {
                                    //如果没有上架商品就修改sku
                                    $as_data = [];
                                    foreach ($sku_code_arr as $k => $v) {
                                        $as_data['sku']          = $val;
                                        $as_data['sku_list']     = $v;
                                        $as_data['commodity_id'] = $commodity_id;
                                        $as_data['type']         = 10;
                                        $as_data['admin_name']   = $admin_name;
                                        Queue::push('app\common\queue\CommoditySku', json_encode($as_data), config('queue_type.default'));
                                    }
                                }

                            }
                            print_json(0, '修改成功', ['id' => $commodity_id]);
                        } else {
                            $comm_model->allowField(true)->isUpdate(true)->save(['is_update' => 1], ['id' => $commodity_id]);
//                        $sku_model->allowField(true)->isUpdate(true)->save(['is_enable'=>0], ['commodity_id' => $commodity_id]);
                            foreach ($sku_list as $key => $val) {
                                $row                      = $sku_model->getOne(['where' => ['sp_value_list' => $val['sp_value_list'], 'commodity_id' => $commodity_id, 'is_enable' => 1]]);
                                $image                    = !empty($val['image']) ? $val['image'] : '';
                                $sku_code_arr             = explode(',', $val['sku_code']);
                                $relate_car_18n           = '';
                                $relate_car_ids           = '';
                                $sku_relate_car_work_hour = '';
                                $sku_bj_type_name         = '';
                                $city_type                = '';
                                $maintain_q               = '';
                                $upgrade_type             = '';
                                $maintain_num             = '';
                                if (!empty($sku_code_arr)) {
                                    if ($dd_commodity_type == 10) {
                                        if ($is_off == 1 || $is_off == 2) {
                                            $price        = explode(',', $val['price']);
                                            $cost_price   = explode(',', $val['cost_price']);
                                            $new_sku_list = [];
                                            foreach ($sku_code_arr as $k => $v) {
                                                $relate_car           = $this->findCarType($v, 2, $sku_code_arr);
                                                $relate_car_work_hour = $this->work_hour($v);
                                                $relate_car_18n       = implode(',', $relate_car['n_18']);
                                                if (empty($relate_car['car_ids'])) {
                                                    $relate_car_ids = 999999999;
                                                } else {
                                                    $relate_car_ids = implode(',', $relate_car['car_ids']);
                                                }
                                                $sku_relate_car_work_hour = json_encode($relate_car_work_hour['part_no_array']);
                                                $sku_bj_type_name         = implode(',', $relate_car_work_hour['bj_type_name']);
                                                if (count($cost_price) > $k) {
                                                    $new_cost_price = $cost_price[$k];
                                                } else {
                                                    $new_cost_price = $cost_price[0];
                                                }
                                                $new_sku_list[$k] = [
                                                    'price'            => $price[$k],
                                                    'stock'            => $val['stock'],
                                                    'commodity_id'     => $commodity_id,
                                                    'sp_value_list'    => $val['sp_value_list'],
                                                    'image'            => $image,
                                                    'sku_code'         => $v,
                                                    'tax'              => $val['tax'],
                                                    'tax_code'         => $val['tax_code'],
                                                    'cost_price'       => $new_cost_price,
                                                    'relate_car_18n'   => $relate_car_18n,
                                                    'relate_car_ids'   => $relate_car_ids,
                                                    'relate_car_work_hour' => $sku_relate_car_work_hour,
                                                    'e3s_bj_type_name' => $sku_bj_type_name,
                                                    'city_type'        => $relate_car['city_type']
                                                ];
                                            }
                                            $sku_model->insertAll($new_sku_list);
                                        }
                                    } else {
                                        $relate_car               = $this->findCarType($sku_code_arr, 1, []);
                                        $relate_car_18n           = implode(',', $relate_car['n_18']);
                                        $relate_car_ids           = implode(',', $relate_car['car_ids']);
                                        $relate_car_work_hour     = $this->work_hour($sku_code_arr);
                                        $sku_relate_car_work_hour = json_encode($relate_car_work_hour['part_no_array']);
                                        $sku_bj_type_name         = implode(',', $relate_car_work_hour['bj_type_name']);
                                    }
                                }
                                if (!in_array($dd_commodity_type, [9, 10])) {
                                    if ($row) {
                                        $sku_model->where(['sp_value_list' => $val['sp_value_list'], 'id' => $row['id']])->update(
                                            [
                                                'price'            => $val['price'],
                                                'stock'            => $val['stock'],
                                                'commodity_id'     => $commodity_id,
                                                'sp_value_list'    => $val['sp_value_list'],
                                                'image'            => $image,
                                                'sku_code'         => $val['sku_code'],
                                                'tax'              => $val['tax'],
                                                'tax_code'         => $val['tax_code'],
                                                'cost_price'       => $val['cost_price'],
                                                'relate_car_18n'   => $relate_car_18n,
                                                'relate_car_ids'   => $relate_car_ids,
                                                'relate_car_work_hour' => $sku_relate_car_work_hour,
                                                'e3s_bj_type_name' => $sku_bj_type_name,
                                                'card_id'          => $val['card_id'],
                                                'hours_id'         => $val['hours_id'],
                                                'city_type'        => $city_type,
                                                'maintain_q'       => $maintain_q,
                                                'upgrade_type'     => $upgrade_type,
                                                'maintain_num'     => $maintain_num,
                                                'is_enable'        => 1,
                                            ]);
                                    } else {
                                        $sku_model->insertData(
                                            [
                                                'price'                => $val['price'],
                                                'stock'                => $val['stock'],
                                                'commodity_id'         => $commodity_id,
                                                'sp_value_list'        => $val['sp_value_list'],
                                                'image'                => $image,
                                                'sku_code'             => $val['sku_code'],
                                                'tax'                  => $val['tax'],
                                                'tax_code'             => $val['tax_code'],
                                                'cost_price'           => $val['cost_price'],
                                                'relate_car_18n'       => $relate_car_18n,
                                                'relate_car_ids'       => $relate_car_ids,
                                                'relate_car_work_hour' => $sku_relate_car_work_hour,
                                                'e3s_bj_type_name'     => $sku_bj_type_name,
                                                'card_id'              => $val['card_id'],
                                                'hours_id'             => $val['hours_id'],
                                                'city_type'            => $city_type,
                                                'maintain_q'           => $maintain_q,
                                                'upgrade_type'         => $upgrade_type,
                                                'maintain_num'         => $maintain_num,
                                                'delivery_coefficient' => $val['delivery_coefficient'],
                                                'creator'              => $this->admin_info['username'] ?? 'test',
                                            ]);
                                    }
                                }
                            }
                        }
                        print_json(0, '修改成功', ['id' => $commodity_id]);
                    }
                } else {
                    print_json(1, $comm_model->getError());
                }
            }

        }
    }

    /**
     * 组合商品保存
     * @return void
     * @throws \Exception
     */
    public function saveGroup()
    {
        set_time_limit(0);
        if ($this->admin_info['type'] == 1) {
            $shelves_sources = empty($_POST['shelves_sources']) ? print_json(1, '上架源不能为空', json_encode_cn(input('post.'))) : $_POST['shelves_sources'];
            $shelves_sources = implode(',', $shelves_sources);
        } else {
            $shelves_sources = 2;
        }

        $commodity_name = input('post.commodity_name');

        $bs_data      = [
            'commodity_name'             => trim($commodity_name),
            'is_pure'                    => input('post.is_pure'),
            'is_shop'                    => input('post.is_shop'),
            'comm_type_id'               => input('post.comm_type_id'),
            //            'detail_content'             => input('post.content', ''),
            'sort'                       => input('post.sort'),
            'original_price_range_start' => input('post.original_price_range_start'),
            'original_price_range_end'   => input('post.original_price_range_end'),
            'discount_price_range_start' => input('post.discount_price_range_start'),
            'discount_price_range_end'   => input('post.discount_price_range_end'),
            'unit'                       => input('post.unit'),
            'commodity_code'             => input('post.commodity_code', ''),
            'shelves_sources'            => $shelves_sources,
            'brands_id'                  => input('post.brands_id'),
            'video'                      => input('post.video_img'),
            'tax_code'                   => input('post.tax_code', ''),//税务编码
            'supplier'                   => input('post.supplier', ''),//供应商
            'arrival_bdp'                => input('post.arrival_bdp', 0),//是否给bdp;0否1是
            'dd_commodity_type'          => input('post.dd_commodity_type', 0),//店商品类型 9到店备件 1保养套餐-老友惠保养套餐 3保养套餐-心悦保养套餐 4保养套餐-五年双保升级权益套餐 6保养套餐-其他 7到店代金券
        ];
        $set_type     = input('post.set_type');
        $commodity_id = input('post.id');
        $is_preview   = input('post.is_preview', null);

        //遍历图片
        if (!empty($_POST['image_list'])) {
            $bs_data['cover_image'] = $_POST['image_list'][0];
            $bs_data['sku_image']   = json_encode($_POST['image_list']);
        } else {
            print_json(1, '默认图不能为空');//调试
        }
        //设置总库存
        $bs_data['count_stock'] = 0;
        $sku_model              = new  DbCommoditySku();
        $comm_model             = new  DbCommodity();
        //商品种类
        $bs_data['commodity_class'] = input('post.commodity_class');
        //新增
        if (input('post.action') == 'add') {
            $bs_data['create_dlr_code'] = $this->admin_info['dlr_code'];
            $bs_data['set_type']        = $this->admin_info['type'];
            $bs_data['is_grouped']      = 1;

            $bs_data['commodity_card_ids'] = input('post.commodity_card_ids');
            if ($bs_data['commodity_class'] == 3 && empty($bs_data['commodity_card_ids'])) print_json(1, '卡券不能为空');
            if ($bs_data['commodity_class'] != 3) unset($bs_data['commodity_card_ids']);

            if ($bs_data['commodity_class'] == 5) {
                if (empty($_POST['mark_coupon_id'])) {
                    print_json(1, '平台卡券不能为空');
                }
                $coupon_model = new DbCoupon();
                $coupon       = $coupon_model->getOne(['where' => ['mark_coupon_id' => $_POST['mark_coupon_id']]]);
                if (!$coupon || ($coupon['deadline_type'] == 1 && $coupon['use_end_date'] <= date('Y-m-d H:i:s'))) {
                    print_json(1, '平台卡券不存在或已过期');
                }
                $bs_data['commodity_card_ids'] = $_POST['mark_coupon_id'];
            }

            //预览
            if ($is_preview) {
//                $prev_key = $this->_prevKey();
//                redis($prev_key . 'commodity', $bs_data, 3600);
//                print_json(0, 'ok', $this->_preQrCode($prev_key));
            } else {

                $res = $comm_model->allowField(true)->save($bs_data);
                if ($res) {
                    $commodity_id = $comm_model->id;
                    $prev_key     = 'pre_com_key' . $commodity_id;
                    redis($prev_key . 'commodity', $bs_data, 3600);
                    print_json(0, '添加成功', ['id' => $commodity_id]);
                } else {
                    print_json(1, $comm_model->getError());
                }
            }
        } elseif (input('post.action') == 'update') {
            // 保存预览key
            $prev_key = 'pre_com_key' . $commodity_id;
            //预览
            if ($is_preview) {
//                $prev_key = $this->_prevKey();
//                redis($prev_key . 'commodity', $bs_data, 3600);
                $bs_data_redis                   = redis($prev_key . 'commodity');
                $bs_data_redis['detail_content'] = input('post.content', '');
                redis($prev_key . 'commodity', $bs_data_redis, 3600);
                print_json(0, 'ok', $this->_preQrCode($prev_key, $commodity_id));
            } else {
                redis($prev_key . 'commodity', $bs_data, 36000);
                if (strpos($shelves_sources, "5") !== false) {
                    $bs_data['live_refresh'] = 1;
                }
                if ($bs_data['commodity_class'] == 5) {
                    if (empty($_POST['mark_coupon_id'])) {
                        print_json(1, '平台卡券不能为空');
                    }
                    $bs_data['commodity_card_ids'] = $_POST['mark_coupon_id'];
                }
                $res = $comm_model->allowField(true)->isUpdate(true)->save($bs_data, ['id' => $commodity_id]);

                if ($res || !$comm_model->getError()) {
                    $this->sku_model->where(['commodity_id' => $commodity_id])->update(['is_enable' => 0]);
                    $is_off = input('is_off', 0);
                    if ($is_off == 1) {
                        //查询关联下架商品id start
                        $commodity_where['a.commodity_id'] = $commodity_id;
                        $commodity_where['a.is_enable']    = 1;
                        $com_id                            = Db::name('db_commodity_set_sku')->alias('a')
                            ->where($commodity_where)
                            ->whereOr(['a.group_sub_commodity_id' => $commodity_id])->group('a.commodity_id')
                            ->field('a.group_sub_commodity_id,a.commodity_id')
                            ->select();
                        $commodity_ids                     = [];
                        foreach ($com_id as $value) {
                            $commodity_ids[] = $value['commodity_id'];
                            if ($value['group_sub_commodity_id'] != 0) {
                                $commodity_ids[] = $value['group_sub_commodity_id'];
                            }
                        }
                        if (!empty($commodity_ids)) {
                            $find = Db::name('db_commodity_set_sku')->alias('a')
                                ->join('db_commodity b', 'a.commodity_id = b.id')
                                ->join('db_commodity_set c', 'a.commodity_id = c.commodity_id')
                                ->where(['a.commodity_id' => array('in', $commodity_ids), 'a.is_enable' => 1])
                                ->whereOr(['a.group_sub_commodity_id' => array('in', $commodity_ids)])
                                ->group('a.commodity_id')
                                ->field('b.id')
                                ->select();
                            if (!empty($find)) {
                                foreach ($find as $value) {
                                    $this->dbCommoditySet->where(['commodity_id' => $value['id']])
                                        ->update(['is_enable' => 0]);
                                    $this->dbCommoditySetSku->where(['commodity_id' => $value['id']])
                                        ->whereOr(['group_sub_commodity_id' => $value['id']])
                                        ->update(['is_enable' => 0]);
                                    DbCommodityFlat::where('commodity_id', $value['id'])->delete();
                                }
                            }
                        }
                        //查询关联下架商品id end
                    }
                    print_json(0, '修改成功', ['id' => $commodity_id]);

                } else {
                    print_json(1, $comm_model->getError());
                }
            }
        }
    }


    private function _prevKey()
    {

        return $com_key = 'pre_com_' . time() . get_rand_str(5); //预览商品key值

    }

    private function _preQrCode($prev_key, $commodity_id = 'test_preview')
    {
        $com_url = url('index_v2/normal/good_detail', ['preview' => 2, 'prev_key' => $prev_key, 'dlr_code' => 'NISSAN', 'id' => $commodity_id], '', true);
        /*   $file_name='qc_code_image/'.$prev_key.'.png';
           QrCode::png($com_url,config('upload.path').$file_name, 'L',10);
           $qr_image= config('upload.qrc_url').$file_name;*/
        return ['qr_image' => '', 'com_url' => $com_url];
    }

    public function getQrCode()
    {
        $commodity_id = input('get.commodity_id');
        $com_url      = url('index_v2/goods/detail', ['dlr_code' => 'NISSAN', 'id' => $commodity_id, 'preview' => 1], '', true);
        /*  $file_name='qc_code_image/defa-'.$commodity_id.'.png';
          QrCode::png($com_url,config('upload.path').$file_name, 'L',10);
          $qr_img= config('upload.qrc_url').$file_name;*/
        print_json(1, '', ['qr_image' => '', 'com_url' => $com_url]);
    }


    public function successStep3()
    {
        $is_ok        = input('is_ok', 1);
        $commodity_id = input('commodity_id');
        $is_grouped   = input('is_grouped', 0);
        if ($is_ok == 1) {
            $this->assign('step', 3);
            return $this->fetch('success_step_3');
        } else {
            $username['username']  = $this->admin_info['username'];
            $username['timestamp'] = time();
            $token                 = http_build_query($username);
            $this->redirect(Env::get('TOB_URL', 'https://pz1a-sit.szlanyou.com/dndc-dealer-mall-admin') . '/commodity_main/Commodity/index?sign=' . base64_encode($token) . '&commodity_id=' . $commodity_id . '&is_grouped=' . $is_grouped);
        }
    }

    /**
     * 获取商品分类
     */

    public function ajaxGetCommTypeId()
    {
        $comm_parent_id = input('get.comm_parent_id');
        $list           = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        print_json(0, '', $list);

    }

    public function info()
    {
        $commodity = new CommodityService();
        // $list     =$commodity->getCommodityInfo(324,'TEST');
        //var_dump($list);
        $list = $commodity->getOneSku(101);

        print_r($list);
        /* $list = $commodity->getSetSku(42,'NISSAN',1);
         exit(json_encode($list));*/


    }

    public function test()
    {
        $kk = ['5' => ["commodity/20170725/74018b032f09007270c11a28d64c9b28.jpg"]];

        $this->comm_model->where(['id' => 31])->update(['sku_image' => json_encode($kk)]);


    }

    public function delete()
    {

        print_json(1, '删除失败');
        $id  = input('post.id');
        $res = $this->comm_model->where(['id' => $id, 'create_dlr_code' => $this->admin_info['dlr_code']])->update(['is_enable' => 0]);
        if ($res) {
            //flat 索引
            $params = [
                'commodity_id' => $id,
                'is_delete'    => true
            ];
//            Hook::exec('app\\admin_v2\\behavior\\ReindexProduct', 'run', $params);
            print_json(0, '删除成功');
        } else {
            print_json(1, '删除失败');
        }
    }

    public function test1()
    {
        $a  = '12554558545';
        $dd = substr($a, 0, 3) . '****' . substr($a, -4);
        var_dump($dd);
        /* $ser_com=new CommodityService();
         $res=$ser_com->getDefaultSku(56);
         print_r($res);*/

    }

    public function test3()
    {
        $commodity = new CommodityService();
        $list      = $commodity->getCommodityInfo(416, 'H2901', 0, '', 0, 2, 39);
        print_json($list);
        $kk = $commodity->updateStock(222, 355, 'NISSAN', 1);
        print_json($kk);
        //$list     =$commodity->getFightGroupOneSku(1);
        //$list     =$commodity->getSetSku(141,'NISSAN',1);
        //$list=$commodity->getOneSku(9);
        //print_r($list);
    }

    public function test4()
    {
        $dd = ['a' => 44, 'b' => 44];
        $c  = ['a' => 4444];
        $cc = array_merge($dd, $c);


    }


    public function ajaxGetCard()
    {

        $where = [];
        if (!empty(input('get.card_name'))) {
            $where['card_name'] = ['like', '%' . input('get.card_name') . '%'];
        }
        $where['dlr_code'] = $this->admin_info['dlr_code'];
        $where['type']     = 1;
        $params            = [
            'field'    => 'id,card_id,type,(case type when 1 then "微信优惠券" when 2 then "商城卡券" end) as type_name,card_type,card_name,set_type as create_by_role,(case set_type when 1 then "平台" when 2 then "自建" when 3 then "集团" end) as belong_to',
            'where'    => $where,
            'order'    => 'id desc',
            'pagesize' => input('get.pagesize'),
            'query'    => input('get.')
        ];
//        $list = $this->dbCard->getListPaginate($params);
        $dbCard = new DbCard();
        $list   = $dbCard->getDlrCard($params);
        $card_type_arr = $dbCard->cardType();

        foreach ($list as $key => $value) {
            $list[$key]['id'] = (string)$value['id'];
            $list[$key]['card_type_name'] =  $card_type_arr[$value['card_type']] ?? '';
        }
        print_json(0, '', $list);
    }

    public function ajaxLiveGetCard()
    {
        $where = ['is_enable'=>1];
        if (!empty(input('get.card_name'))) {
            $where['card_name'] = ['like', '%' . input('get.card_name') . '%'];
        }
        $where['card_type'] = ['not in', [6, 7]];
        if (!empty(input('get.card_type'))) {
            $where['card_type'] = input('get.card_type');
        }
        $shelves_sources = input('shelves_sources', '');
        if (!empty($shelves_sources)) {
            $where['shelves_type'] = ['in', $shelves_sources];
        } else {
            $where['shelves_type'] = ['in', [5, 6, 7]];
        }
        $where['act_status']      = 2;
        $where['available_count'] = ['gt', 0];
        $params                   = [
            'field'    => 'id, id as card_id,card_type,(case card_type when 1 then "代金券" when 2 then "折扣券" when 6 then "到店代金券" when 7 then "上门取送车券" end) as card_type_name,card_type,(case set_type when 1 then "平台" when 2 then "自建" when 3 then "集团" end) as belong_to,card_name',
            'where'    => $where,
            'order'    => 'id desc',
            'pagesize' => input('get.pagesize'),
            'query'    => input('get.')
        ];
        $list                     = (new DbCard())->getDlrCard($params);
        foreach ($list as $key => $item) {
            $list[$key]['id'] = (string)$item['id'];
            $list[$key]['card_id'] = (string)$item['card_id'];
        }
        print_json(0, '', $list);
    }

    public function one()
    {
        $id                         = input('post.id');
        $val                        = $this->comm_model->getOneByPk($id);
        $val['is_shop_name']        = $val['is_shop'] == 1 ? '是' : '否';
        $val['is_mail_name']        = $val['is_mail'] == 1 ? '是' : '否';
        $val['commodity_attr_name'] = \app\common\model\db\DbCommodity::attribute($val['commodity_attr']);
        $val['cover_image']         = config('upload.url') . $val['cover_image'];

        $return = "
            原价（元）:{$val['original_price_range_start']}-{$val['original_price_range_end']}<br>
            现价（元）:{$val['discount_price_range_start']}-{$val['discount_price_range_end']}<br>
            库存:{$val['count_stock']}<br>
            计量单位:{$val['unit']}<br>
            排序:{$val['sort']}<br>
            是否商城商品:{$val['is_shop_name']}<br>
            支持邮寄:{$val['is_mail_name']}<br>
            发布时间:{$val['created_date']}<br>
        ";
        print_json(0, 'success', ['info' => $return]);
    }

    public function change_check()
    {
        $input = input('post.');
        if (in_array($input['dd_commodity_type'], [1, 3, 4,12])) {
            if (!is_array($input['old_sku_list'])) {
                $old_sku_list_data = json_decode($input['old_sku_list'], true);
            } else {
                $old_sku_list_data = $input['old_sku_list'];
            }
            $old_sku_list      = [];
            foreach ($old_sku_list_data as $key => $value) {
                foreach ($value as $v) {
                    $old_sku_list[] = $v;
                }
            }
            if (!is_array($input['change_sku_list'])) {
                $change_sku_list = json_decode($input['change_sku_list'], true);
            } else {
                $change_sku_list = $input['change_sku_list'];
            }
        } else {
            if(!isset($input['old_sku_list'])){
                print_json(0);
            }
            $old_sku_list = $input['old_sku_list'];
            if (!is_array($old_sku_list)) {
                $old_sku_list = json_decode($input['old_sku_list'], true);
            }
            $change_sku_list = $input['change_sku_list'];
        }
        $shelves_sources     = explode(',', $input['shelves_sources']);
        $old_shelves_sources = explode(',', $input['old_shelves_sources']);
        if (count($change_sku_list) != count($old_sku_list)) {
            print_json(1);
        }
        foreach ($change_sku_list as $k => $v) {
            foreach ($v as $ky => $vl) {
                if (empty($vl) && empty($old_sku_list[$k][$ky])) continue;
                if (isset($old_sku_list[$k][$ky]) && $vl != $old_sku_list[$k][$ky]) {
                    print_json(1);
                }
            }
        }
        foreach ($old_shelves_sources as $v) {
            if (!in_array($v, $shelves_sources)) {
                print_json(1);
            }
        }
        print_json(0);
    }

    public function ajaxGetCommTypeList()
    {
        $params                     = [];
        $params['where']            = [];
        $params['where']['c.level'] = 3;
        $params['order']            = [];
        $params['query']            = [];
        $params['field']            = "a.comm_type_name as one_name,a.id as one_id,b.comm_type_name as two_name,b.id as two_id,c.comm_type_name as three_name,c.id as three_id";

        $commodity_name     = input('commodity_name');
        $comm_parent_id     = input('comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $page_size          = input('pagesize', 5);

        if (!empty($commodity_name)) {
            $params['where']['c.comm_type_name'] = ['like', "%$commodity_name%"];
        }
        if (!empty($three_comm_type_id)) {
            $params['where']['c.id'] = $three_comm_type_id;
        } else {
            if (!empty($sub_comm_type_id)) {
                $comm_type_column        = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id], 'column' => 'id']);
                $params['where']['c.id'] = ['in', $comm_type_column];
            } else {
                if (!empty($comm_parent_id)) {
                    $two_type_column         = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                    $three_type_column       = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => ['in', $two_type_column]], 'column' => 'id']);
                    $params['where']['c.id'] = ['in', $three_type_column];
                }
            }
        }
        $res = $this->comm_type_model->alias('c')
            ->join('t_db_commodity_type b', 'c.comm_parent_id=b.id', 'left')
            ->join('t_db_commodity_type a', 'b.comm_parent_id=a.id', 'left')
            ->where($params['where'])->field($params['field'])->order($params['order'])
            ->paginate($page_size, false, array('query' => $params['query']));
        //   echo $this->commodityType->getLastSql();exit;
        print_json(0, '', $res);
    }

    //查看说明商品
    public function des_commodity()
    {
        $des_data                          = input('dec_arr');
        $pagesize                          = 10;
        $params                            = [];
        $params['where']                   = [];
        $params['where']['is_enable']      = 1;
        $params['order']                   = [];
        $params['query']                   = [];
        $params['where']['commodity_type'] = ['in', explode(',', $des_data)];
        //判断分类
        $exit_arr = [];
        $type_des = $this->dbCommodityDesType->where($params['where'])->select();

        foreach ($type_des as $des) {
            $exit_arr[] = $des['commodity_type'];
        }

        $list = $this->dbCommodityDesType
            ->alias('t')
            ->join('t_db_commodity_des d', 't.des_id=d.id', 'left')
            ->where('commodity_type', 'in', $exit_arr)
            ->where('t.is_enable', 1)
            ->paginate($pagesize, false, array('query' => $params['query']));

        if (!empty($list)) {
            foreach ($list as $k => $v) {
                $commodity_type_str = $v['commodity_type'];
                $selected_data      = $this->comm_type_model->alias('c')
                    ->join('t_db_commodity_type b', 'c.comm_parent_id=b.id', 'left')
                    ->join('t_db_commodity_type a', 'b.comm_parent_id=a.id', 'left')
                    ->where("c.is_enable=1 and c.id in({$commodity_type_str})")
                    ->field('a.comm_type_name as one_name,a.id as one_id,b.comm_type_name as two_name,b.id as two_id,c.comm_type_name as three_name,c.id as three_id')
                    ->select();
                foreach ($selected_data as $selected) {
                    $str = $selected['one_name'] . '->' . $selected['two_name'] . '->' . $selected['three_name'];
                }
                $display_channel_arr = explode(',', $v['display_channel']);
                foreach ($display_channel_arr as &$channel) {
                    $channel = DbCommodity::displayChannel()[$channel];
                }
                $list[$k]['channel'] = implode(' 、', $display_channel_arr);
                $list[$k]['type']    = $str;
            }
            $page = $list->render();
            $this->assign('list', $list);
            $this->assign('page', $page);//分页
            return $this->fetch('des_commodity');
        }
    }

    //是否存在相同的说明
    public function check_des()
    {
        $commodity_arr                     = $_POST['dec_arr'] ?? '';
        $params                            = [];
        $params['where']                   = [];
        $params['where']['is_enable']      = 1;
        $params['where']['commodity_type'] = ['in', $commodity_arr];
        $list                              = $this->dbCommodityDesType->where($params['where'])->select();
        $post_arr                          = [];
        $exist_arr                         = [];
        foreach ($list as $val) {
            $post_arr[$val['des_id']][] = $val['commodity_type'];
        }
        foreach ($post_arr as $v) {
            foreach ($v as $v1) {
                $exist_arr[] = $v1;
            }
        }
        if (!empty($exist_arr)) {
            print_json(1);
        }
        print_json(0);
    }

    private function doHook($type = 'update', $sku_list = [], $id = 0)
    {
        Hook::import(require ROOT_PATH . 'config/tags.php');
        $result = true;
        //查询act_id对应的活动，查询up_down_channel_dlr ，做出判断 shelves_type
        switch ($type) {
            case 'add':
            case 'update':
                # 添加商品不需要刷活动信息
                $data['commodity_id'] = $id;
                $data['sku_list']     = $sku_list;
                Hook::listen('flat_commodity_sku', $data);
                break;
        }
        return false;
    }

    // 保存商品拓展信息
    public function saveCommodityExpand()
    {
        $expand_model = new DbCommodityExpand();
        $sku_model    = new DbCommoditySku();
        $input_data   = input('post.');
        $commodity_id = $input_data['commodity_id'];
        $insert_data  = $input_data['poster_data'] ?? [];
        $detail_msg   = $input_data['detail_msg'];

        $poster_show_type = $input_data['poster_show_type'] ?? 1;

        if (empty($detail_msg)) print_json(1, '商品详情不能为空');
        $sku_list = $sku_model->getList(['where' => ['commodity_id' => $commodity_id, 'is_enable' => 1]]);
        if ($detail_msg['detail_type'] == 1) {
            if (empty($detail_msg['detail_all'])) {
                print_json(1, '商品详情适用全部规格时detail_all不能为空');
            }
            $insert_data[] = [
                'expand_type'          => 2,
                'commodity_id'         => $commodity_id,
                'commodity_sku_detail' => $detail_msg['detail_all'],
            ];
        } elseif ($detail_msg['detail_type'] == 2) {
            if (count($detail_msg['detail_list']) != count($sku_list)) print_json(1, '规格对应详情数量有误');
            foreach ($detail_msg['detail_list'] as $v) {
                $insert_data[] = [
                    'expand_type'          => 2,
                    'commodity_id'         => $commodity_id,
                    'commodity_sku_id'     => $v['commodity_sku_id'],
                    'commodity_sku_detail' => $v['commodity_sku_detail'],
                ];
            }
        }
        try {
            $expand_model->startTrans();
            $expand_model->where(['commodity_id' => $commodity_id])->delete(); // 直接删除再新增
            if (!empty($insert_data)) {
                foreach ($insert_data as $v) {
                    $v['commodity_id'] = $commodity_id;
                    $expand_model->insert($v);
                }
                $this->comm_model->saveData(['detail_type' => $detail_msg['detail_type'], 'poster_show_type' => $poster_show_type], ['id' => $commodity_id]);
            }
            $expand_model->commit();
        } catch (Exception $exception) {
            Logger::info('save-commodity-expand', $exception->getMessage());
        }
        print_json(0, 'ok');
    }

    // 获取商品拓展信息
    public function getCommodityExpand($pre_commodity_id = '')
    {
        $commodity_id = empty($pre_commodity_id) ? input('commodity_id') : $pre_commodity_id;
        if (empty($commodity_id)) print_json(1, '商品id不能为空');

        $expand_model   = new DbCommodityExpand();
        $sku_model      = new DbCommoditySku();
        $commodity_info = $this->comm_model->getOne(['where' => ['id' => $commodity_id]]);
        if (empty($commodity_info)) print_json(1, '商品id有误');

//        $sku_list = $sku_model->getList(['where' => ['commodity_id' => $commodity_id, 'is_enable' => 1]]);
//        $sku_ids = array_column($sku_list, 'id');
        $sku_name_list = $sku_model->getSkuName($commodity_id);

        $commodity_expand = $expand_model->getList(['where' => ['commodity_id' => $commodity_id, 'is_enable' => 1]]);
        $poster           = [];
        $detail_sku       = [];
        $detail_all       = $this->DeleteHtml($commodity_info['detail_content']);
        foreach ($commodity_expand as $item) {
            if ($item['expand_type'] == 1) {
                $poster[md5($item['poster_image'])][] = [
                    'poster_show_channel' => $item['poster_show_channel'],
                    'poster_image'        => $item['poster_image'],
                    'poster_jump_type'    => $item['poster_jump_type'],
                    'poster_type_value'   => $item['poster_type_value'],
                    'poster_type_show'    => $item['poster_type_show'],
                ];
//                $poster                             = array_merge(
//                    $poster, [
//                    'poster_show_type'    => $item['poster_show_type'],
//                    'poster_show_channel' => $item['poster_show_channel'],
//                    'poster_image'        => $item['poster_image'],
//                    'poster_jump_type'    => $item['poster_jump_type'],
//                    'poster_type_value'   => $item['poster_type_value'],
//                ]
//                );
            } elseif ($item['expand_type'] == 2) {
                if ($commodity_info['detail_type'] == 1) {
                    $detail_all = $this->DeleteHtml($item['commodity_sku_detail']);
                } else {
                    $detail_sku[] = [
                        'commodity_sku_id'     => $item['commodity_sku_id'],
                        'commodity_sku_detail' => $this->DeleteHtml($item['commodity_sku_detail']),
                    ];
                }
            }
        }

        $return = [
            'poster_show_type' => $commodity_info['poster_show_type'],
            'poster_data'      => $poster,
            'detail_msg'       => [
                'detail_type' => $commodity_info['detail_type'],
                'detail_all'  => $detail_all,
                'detail_list' => $detail_sku,
            ],
            'sku_list'         => $sku_name_list,
        ];
        if (!empty($pre_commodity_id)) return $return;
        print_json(0, '', $return);
    }

    //判断是否在众筹活动添加
    public function crowdfund_start($commodity_set_id)
    {
        $list = Db::name('db_crowdfund_commodity')->alias('a')
            ->join('t_db_crowdfund b', 'a.crowdfund_id = b.id')
            ->where(['a.commodity_set_id' => $commodity_set_id, 'b.act_status' => 2])
            ->field('b.title')
            ->find();
        if (!empty($list)) {
            print_json(0, '', $list, true);
        } else {
            print_json(1, '没有添加活动', []);
        }
    }

    //京东主图
    public function jd_sku_img()
    {
        $sku_id = input('sku_id');
        $type   = input('img_type', 1);//主图1 详情2
        $model  = new JdOrderN();
        $data   = [];
        if ($type == 1) {
            $result = $model->getImage([$sku_id]);
            if ($result['resultCode'] == 0000) {
                if (!empty($result['result'])) {
                    foreach ($result['result'][$sku_id] as $value) {
                        $data[] = [
                            'img_url'       => config('jd_fuli.jdimg_url') . $value['path'],
                            'sku_id'        => $value['skuId'],
                            'is_enable'     => $value['yn'] == 1 ? '可用' : '不可用',
                            'created_date'  => $value['created'],
                            'sorts'         => $value['orderSort'],
                            'position'      => $value['position'],
                            'is_main_image' => $value['isPrimary'] == 1 ? '是' : '否',
                        ];
                    }
                }
            }

        } elseif ($type == 2) {
            $result = $model->getDetail($sku_id);
            $img    = json_decode($result['wxintroduction'], true);
            foreach ($img as $key => $value) {
                $data[] = [
                    'img_url'      => $value,
                    'sku_id'       => $sku_id,
                    'is_enable'    => '可用',
                    'created_date' => date("Y-m-d H:i:s"),
                    'sorts'        => $key + 1,
                    'position'     => ''
                ];
            }
        }
        print_json(0, '获取成功', $data);
    }


    public function copyGoods()
    {
        $commodity_id = input('commodity_id');
        $is_grouped = input('is_grouped');
        // 查询商品
        $commodity_model = new DbCommodity();
        $commodity_info  = $commodity_model->where('id', $commodity_id)->find();
        if (empty($commodity_info)) {
            print_json(1, '该商品不存在');
        }
        $commodity_info = $commodity_info->toArray();
        unset($commodity_info['id']);
        unset($commodity_info['created_date']);
        unset($commodity_info['last_updated_date']);
        // 新增商品
        $new_commodity_id = $commodity_model->insertGetId($commodity_info);

        // 非组合商品
        $map          = ['commodity_id' => $commodity_id, 'is_enable' => 1];
        if(!$is_grouped) {
            // 查询sku  新增sku
            $commodity_sku_model = new DbCommoditySku();

            $sku_list     = $commodity_sku_model->where($map)->select();
            $new_sku_list = [];
            foreach ($sku_list as $item) {
                $item = $item->toArray();
                unset($item['id']);
                $item['commodity_id'] = $new_commodity_id;
                $new_sku_list[]       = $item;
            }
            $commodity_sku_model->insertAll($new_sku_list);
        }


        // 商品扩展信息
        $expand_model = new DbCommodityExpand();
        $expand_info = $expand_model->where($map)->find();
        if (!empty($expand_info)) {
            $expand_info = $expand_info->toArray();
            unset($expand_info['id']);
            $expand_info['commodity_id'] = $new_commodity_id;
            $expand_model->insertGetId($expand_info);
        }

        print_json(0,'复制成功',['commodity_id'=>$new_commodity_id]);

    }


    public function getClassTips()
    {
        $classId = input('class_id');
        $class_model = new DbCommodityClassInfo();
        $map = ['class_id'=>$classId];
        $tips = $class_model->where($map)->value('tips') ?? '';
        print_json(0,'success', $tips);
    }
}
