<?php


namespace app\admin_v2\controller;


use app\admin_v2\service\NewMediaCommodityService;
use app\common\model\db\DbExports;
use app\common\model\sys\SysMenu;
use think\Env;
use think\Queue;
use think\Request;
use tool\PhpExcel;

class NewMediaCommodity extends Common
{

    /**
     * 列表
     * @throws \think\exception\DbException
     */
    public function index()
    {
        $input = input('get.');
        $service = new NewMediaCommodityService();
        $list = $service->getList($input);
        print_json(0, 'success', $list);
    }



    /**
     * 下载模板
     */
    public function download()
    {
        $files    = 'public/static/template/新媒体商品维护表结构.xlsx';
        $filename = '新媒体商品维护表结构.xlsx';
        download($files, $filename);
    }


    /**
     * 下载抖音商品
     */
    public function getDyCommodityDownload()
    {
        $service = new NewMediaCommodityService();
        $service->downDyCommodity();
    }



    public function getKsCommodity()
    {
        $service = new NewMediaCommodityService();
        $service->downKsCommodity();
    }

    /**
     * 导入数据
     * @param Request $request
     */
    public function import(Request $request)
    {
        $file = $request->file('file');
        $res = PhpExcel::import_excel($file);
        if ($res['error']) {
            print_json(1, $res['msg']);
        }
        $service = new NewMediaCommodityService();
        $result = $service->importData($res);
        if ($result->isSuccess()) {
            print_json(0, 'success');
        } else {
            print_json(1, $result->getMessage());
        }
    }


    /**
     * 明细
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function info()
    {
        $input = input('get.');
        $service = new NewMediaCommodityService();
        $result = $service->skuInfo($input['sku_id']);
        print_json(0, 'success', $result);
    }



    public function downloadCommodity()
    {
        $input = input('get.');
        $map = [];
        if (!empty($input['platform'])) {
            $map['platform'] = $input['platform'];
        }
        if (!empty($input['commodity_name'])) {
            $map['commodity_name'] = $input['commodity_name'];
        }
        if (!empty($input['commodity_id'])) {
            $map['a.commodity_id'] = $input['commodity_id'];
        }
        if (!empty($input['commodity_type'])) {
            $map['commodity_type'] = $input['commodity_type'];
        }
        $params_key = md5(json_encode($input));

        $export = DbExports::create([
            'export_type'   => 'new_media_goods',
            'filter_params' => json_encode($map),
            'export_key'    => $params_key,
            'creator'       => $this->admin_info['username']
        ]);

        Queue::push('app\admin_v2\queue\NewMediaCommodityQueue', json_encode([
            'map'       => $map,
            'id'           => $export->id,
        ]), config('queue_type.new_media'));
        print_json(0, '已加入异步下载列表');
    }


    /**
     * 删除
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function delCommodity()
    {
        $skuId = input('sku_id');
        if (empty($skuId)) {
            print_json(1,'参数缺失');
        }
        $service = new NewMediaCommodityService();
        $re = $service->delCommodity($skuId);
        if ($re->isSuccess()) {
            print_json(0,'删除成功');
        } else {
            print_json(1,$re->getMessage());
        }
    }

}