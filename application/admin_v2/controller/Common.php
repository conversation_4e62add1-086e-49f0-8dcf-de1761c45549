<?php

namespace app\admin_v2\controller;

use app\common\model\bu\BuCheapSuitCommodity;
use app\admin_v2\service\HomeTitleService;
use app\admin_v2\validate\HomeTitleValidate;
use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\bu\BuCheapSuitSub;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\db\DbArea;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCrowdfund;
use app\common\model\db\DbDlr;
use app\common\model\db\DbDraw;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbFightGroupCommodity;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbFullDiscountCommDlr;
use app\common\model\db\DbHomeSm;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbLimitDiscountCommodity;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbNDiscountCommodity;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbPreSaleCommodity;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSystemValue;
use app\common\model\sys\SysAdmin;
use app\common\model\sys\SysAdminGroup;
use app\common\model\sys\SysLog;
use app\common\model\sys\SysLogModule;
use app\common\model\sys\SysMenu;
use app\common\net_service\NetGoods;
use app\common\port\connectors\SsoOauth;
use app\common\port\connectors\Tag;
use think\Controller;
use think\Db;
use think\Env;
use think\Model;
use think\Session;
use app\common\model\wlz\WlzCrowdsInfo;
use tool\Logger;


class Common extends Controller
{
    protected $adminInfo;
    protected $admin_info;
    protected $title;

    protected function _initialize()
    {
        if (input('test') == '123456' && config('app_status') == 'develop') {
            $this->admin_info = ['username'=>'test','type'=>1];
            return true;
        }

        $this->admin_info = session('admin_info');
        $this->adminInfo = session('admin_info');

        if (empty($this->admin_info)) {
            //跳转
            # 众筹模块不判断
            if(!in_array($this->request->controller(),['Crowdfund','SystemValue','InventoryFlow','PssInventory',"JdMaterial","JdCommodityPurchase","JdExports","EarlyWarningInform"])){
//                dd(123);
//                return $this->redirect('login/index');
//                $oAuth = new SsoOauth();
//                $this->redirect($oAuth->oauthUrl());
            }
            $oAuth = new SsoOauth();
            $this->redirect($oAuth->oauthUrl());
        } else {
            header('Access-Control-Allow-Origin:*');
            $menu_auth = $this->_auth();
            if(!$menu_auth){
                return $this->redirect('login/sso_login_error',['errorMsg'=>'该用户没有配置权限组！']);
            }
//            if (!$menu_auth) $this->redirect('Login/index');
            $this->assign('menu_list', $menu_auth['menu_list']);
            $this->assign('menu_name_arr', $menu_auth['menu_name_arr']);
            $this->assign('admin_info', $this->admin_info);
            $this->assign('title', config('shop_title'));
//          $this->_setPage();
        }
        Logger::error('adminactioninfo-' . $this->request->url() . ':' . $this->admin_info['username'] . '>' . $this->admin_info['dlr_code'], json_encode_cn(input('post.')));
        //插入系统日志
        $controller = $this->request->controller();
        $method = strtolower($this->request->method());
//        $this->shelves_type       = $this->pageType == DbHomeSm::DEFAULT_PAGE ? 5 : 7;
//        $this->channel_type       = $this->pageType == DbHomeSm::DEFAULT_PAGE ? 'GWSM' : 'QCSM';
        if ($method == 'post') {
            $params = input('post.');
            if (!empty($params)) {
                $model_sys_log_module = new SysLogModule();
                $where['controller']  = $controller;
                $where['log_on']      = 1;
                $sys_log_module       = $model_sys_log_module->where($where)->find();
                if (!empty($sys_log_module)) {
                    $model_sys_log              = new SysLog();
                    $action                     = $this->request->action();
                    $insert['method']           = strtolower($action);
                    $insert['ip_address']       = $this->request->ip();
                    $insert['username']         = $this->admin_info['username'];
                    $insert['dlr_code']         = $this->admin_info['dlr_code'];
                    $insert['module']           = $this->request->module();
                    $insert['controller']       = $controller;
                    $insert['content']          = json_encode(input('post.'));
                    $insert['browser_versions'] = get_browser_versions();
                    $model_sys_log->insertGetId($insert);
                }
            }
        }
    }

    /**
     * 判断ajax 请求
     */
    protected function _checkAjax()
    {
        if (!request()->isAjax()) print_json(1, '非法操作');

    }

    /**
     * 获取菜单方法
     * 通过用户id获取菜单，admin账号有所有权限
     */
    protected function _getMenu()
    {
        $g_model = new SysAdminGroup();
        $row     = $g_model->getOneByPk($this->admin_info['gid']);
        if ($row) {
            $this->admin_info['group_name'] = $row['group_name'];  //设置权限组名称
        }
        if ($this->admin_info['gid'] == 1) {    //超级管理员
            $where['is_enable'] = 1;
        } else {
            $where = ['is_enable' => 1, 'id' => ['in', explode(',', $row['sys_menu_id'])]];
        }
        $m_model = new SysMenu();
        $list    = $m_model->getList(['where' => $where, 'order' => 'menu_rank ASC,sort ASC,id ASC']);
        $list    = collection($list)->toArray();
        return $list;

    }

    protected function _getUrlInfo()
    {
        return request()->controller() . '/' . request()->action();
    }

    /**
     * 菜单验证
     * @return bool
     */
    private function _auth()
    {
        $menu_res = $this->_getMenu();
        //  var_dump($menu_res);
        $m_model = new SysMenu();
        //判断菜单权限
        if (empty($menu_res)) return false;
        $controller = request()->controller();
        if(in_array($controller,['Yang','Cww'])){
            return  ['menu_list'=>[],'menu_name_arr'=>[]];
        }
        if ($this->admin_info['gid'] == 1 || $this->request->controller() == 'Common') {
            return $m_model->generateTree($menu_res, request()->controller(), request()->action());
        } else {


            foreach ($menu_res as $key => $val) {
                if ($controller == $val['controller']) {
                    return $m_model->generateTree($menu_res, request()->controller(), request()->action());
                }
            }
        }

        return false;
    }

    /**
     * 输出日志
     */
    protected function _addLog()
    {


    }

    /**
     * 修改密码
     * @return array
     */
    public function updatePassword()
    {
        $this->_checkAjax();
        $password    = input('post.password');
        $re_password = input('post.password');
        if ($password != $re_password) {
            print_json(1, '两次密码不一致');
        }
        if (!(strlen($password) >= 6 && strlen($password) <= 20)) {
            print_json(1, '密码长度6到10位');
        }
        $md5_pass = md5($password);
        $model    = new SysAdmin();
        $res      = $model->where(['id' => $this->admin_info['id']])->update([
            'password'          => $md5_pass,
            'modifier'          => $this->admin_info['username'],
            'last_updated_date' => date('Y-m-d H:i:s'),
        ]);
        if ($res) {
            print_json(0, '修改成功');
        } else {
            print_json(1, '修改失败');
        }


    }


    protected function checkTagArr($tag_arr, $up_down_dlr = 'GWSM', $commodity_id = 0)
    {
        $return = [];
        foreach ($tag_arr as $item) {
            if ($item < 10) {
                $return[] = $item;
                continue;
            }

            //10=>'优惠套装',11=>'满优惠',12=>'限时优惠',13=>'多人拼团',14=>'N件N折',15=>'预售活动'
            $clm = '';
            switch ($item) {
                case 10:
                    $obj = new BuCheapSuitIndex();
                    $sub = new BuCheapSuitSub();
                    $clm = 'index_id';
                    break;
                case 11:
                    $obj = new DbFullDiscount();
                    $sub = new DbFullDiscountCommDlr();
                    $clm = 'discount_activity_id';
                    break;
                case 12:
                    $obj = new DbLimitDiscount();
                    $sub = new DbLimitDiscountCommodity();
                    $clm = 'limit_discount_id';
                    break;
                case 13:
                    $obj = new DbFightGroup();
                    $sub = new DbFightGroupCommodity();
                    $clm = 'fight_group_id';
                    break;
                case 14:
                    $obj = new DbNDiscount();
                    $sub = new DbNDiscountCommodity();
                    $clm = 'n_id';
                    break;
                case 15:
                    $obj = new DbPreSale();
                    $sub = new DbPreSaleCommodity();
                    $clm = 'pre_sale_id';
                    break;
            }

            if (empty($clm)) {
                continue;
            }

            //查询对应渠道下是否有在处于活动中的
            $res = $obj->where('act_status', 2)
                ->whereLike("up_down_channel_dlr", "%" . $up_down_dlr . "%")->column('id');
            if (!empty($res)) {

                $find = $sub->whereIn($clm, $res)->where('commodity_id', $commodity_id)->find();
                if (!empty($find)) {
                    $return[] = $item;
                }
            }
        }
        return $return;
    }

    public function getDlrInInfo($in, $column = 'dlr_name')
    {
        $s_model              = new DbSystemValue();
        $up_down_channel_info = $s_model->getNameList(20);

        $dlr_name_arr = DbDlr::whereIn('dlr_code', explode(',', $in))
            ->whereNotIn('dlr_code', array_keys($up_down_channel_info))
            ->column($column);
        return implode(',', $dlr_name_arr);
    }

    //从json字符串中获取到所有商品id
    public function getCommodityIds($id)
    {
        return (new DbHomeSm())->getCommodityIds($id);
    }

    /*
     * 1210版本 用户标签
     */
    public function get_user_groups_labels()
    {
        $params['where']['is_enable'] = 1;
        $params['field']              = 'id as value,crowd_name as label';
        $list                         = (new WlzCrowdsInfo())->getListPaginate($params);
        $data                         = $list->toArray();
        print_json('0', 'ok', $data['data']);
    }


    // 内容标签 start
    //获取标签列表
    public function get_content_babels()
    {
        $name                 = input('name', '');
        $pageSize             = input('pageSize', 10);
        $brand                = input('brand', 0);
        $params['searchType'] = input('type', 0);
        if (!empty($name)) {
            $params['tagName'] = $name;
        }
        $params['page']      = input('page', 1);
        $params['perPage']   = input('list_rows', 10);
        $params['brandId']   = $brand;
        $page_type           = input('page_type', 1);
        $params['modelCode'] = $this->brandModelCode($page_type);
        $res                 = Tag::create('tag')->tagsList($params);
        if ($res['code'] == 1) {
            print_json('1', '获取失败');
        }
        if ($res['data']['result'] != 1) {
            print_json('1', '第三方获取失败');
        }
        $data['total']        = isset($res['data']['records']) ? $res['data']['records'] : 0;
        $data['per_page']     = (int)$pageSize;
        $data['current_page'] = isset($res['data']['pageindex']) ? $res['data']['pageindex'] : 1;
        $array                = [];
        if (!empty($res['data']['rows'])) {
            foreach ($res['data']['rows'] as $key => $value) {
                $array[$key]['tagDefinition'] = $value['tagDefinition'];
                $array[$key]['id']            = $value['categoryId'];
                $array[$key]['title']         = $value['categoryName'];
                $array[$key]['is_add_tag']    = $value['mustAddTag'];
                $list                         = [];
                if (!empty($value['tagList'])) {
                    foreach ($value['tagList'] as $k => $val) {
                        $list[$k]['source_type'] = $val['sourceType'];
                        $list[$k]['id']          = $val['tagId'];
                        $list[$k]['title']       = $val['tagName'];
                    }
                }
                $array[$key]['list'] = $list;

            }
        }
        $res_add = Tag::create('tag')->tagsList([
            'page'       => 1,
            'perPage'    => 10000,
            'searchType' => input('type', 0),
            'modelCode'  => $params['modelCode'],
            'brandId'    => $brand
        ]);
        $ids     = [];
        if (!empty($res_add['data']['rows'])) {
            foreach ($res_add['data']['rows'] as $key => $value) {
                if ($value['mustAddTag'] == 1) {
                    $ids[] = $value['categoryId'];
                }
            }
        }
        $data['ids']  = $ids;
        $data['data'] = $array;

        print_json('0', 'ok', $data);
    }

    //获取已选标签
    public function get_tag_department()
    {
        $params['contentId'] = 3;
        $page_type           = input('page_type', 1);
        $params['modelCode'] = $this->brandModelCode($page_type);
        $res                 = Tag::create('tag')->tagsSelect($params);
        if ($res['code'] == 1) {
            print_json('1', '获取失败');
        }
        if ($res['data']['result'] != 1) {
            print_json('1', '第三方获取失败');
        }
        $array = [];
        foreach ($res['data']['rows']['departmentList'] as $key => $value) {
            $array[$key]['value'] = $value['id'];
            $array[$key]['label'] = $value['departmentName'];
            $list                 = [];
            if (!empty($value['departmentList'])) {
                foreach ($value['departmentList'] as $k => $val) {
                    $list[$k]['value'] = $val['id'];
                    $list[$k]['label'] = $val['departmentName'];
                }
            }
            $array[$key]['children'] = $list;
        }
        print_json('0', 'ok', $array);
    }

    //创建标签
    public function add_custom_tag()
    {
        $name                = input('name', '');
        $notes               = input('notes', '');
        $params['creator']   = $this->admin_info['username'];
        $params['tagList'][] = [
            "notes"   => $notes,
            "tagName" => $name,
        ];
        $page_type           = input('page_type', 1);
        $params['modelCode'] = $this->brandModelCode($page_type);
        $res                 = Tag::create('tag')->tagsAdd($params);
        if ($res['code'] == 1) {
            print_json('1', $res['msg']['msg']);
        }
        print_json('0', 'ok', '创建自定义标签成功');
    }

    //标签列表页点击确认
    public function is_tag_choice()
    {
        $id                         = input('id', 0);
        $page_type                  = input('page_type', 1);
        $params['selectedTagArray'] = explode(',', $id);
        $params['modelCode']        = $this->brandModelCode($page_type);
        $res                        = Tag::create('tag')->isTagChoice($params);
        if ($res['code'] == 1) {
            print_json('1', $res['msg']['msg']);
        }
        print_json('0', 'ok', $res['msg']);
    }

    //获取品牌
    public function get_tags_brand()
    {
        $params              = [];
        $page_type           = input('page_type', 1);
        $params['modelCode'] = $this->brandModelCode($page_type);
        $res                 = Tag::create('tag')->tagsBrand($params);
        if ($res['code'] == 1) {
            print_json('1', $res['msg']['msg']);
        }
        $data  = [];
        $array = isset($res['data']['rows']) ? $res['data']['rows'] : [];
        foreach ($array as $value) {
            $data[] = [
                'id'   => $value['id'],
                'name' => $value['brandName'],
            ];
        }
        print_json('0', 'ok', $data);
    }

    public function brandModelCode($page_type)
    {
        $modelCode = '';
        if ($page_type == 1) {
            $modelCode = config('tag_code.small_index_code');
        } elseif ($page_type == 2) {
            $modelCode = config('tag_code.app_index_code');
        } elseif ($page_type == 5) {
            $modelCode = config('tag_code.qc_small_index_code');
        } elseif ($page_type == 6) {
            $modelCode = config('tag_code.qc_app_index_code');
        }
        return $modelCode;
    }
    // 内容标签 end


    // 省广下单数据处理
    public function tradePushData($order_codes, $remarks = [])
    {
        include_once 'area_sg.php';
        if (empty($order_codes)) return [];

        $all_order_list = (new BuOrder())->where(['order_code' => ['in', $order_codes]])->select();
        $trade_list     = [];
        foreach ($all_order_list as $order) {
            $order_code = $order['order_code'];
            if (empty($order_code)) continue;
            $order_commodity = (new BuOrderCommodity())
                ->alias('a')
                ->field('a.*,b.commodity_code')
                ->join('t_db_commodity b', 'a.commodity_id=b.id')
                ->where(['a.order_code' => $order_code, 'a.supplier' => '省广', 'a.order_commodity_status' => ['in', [0, 2]]])
                ->select();
            if (empty($order_commodity)) continue;
            $paid       = 0;
            $order_list = [];
            foreach ($order_commodity as $k => $v) {
                $paid         += $v['cost_price'] * $v['count'];
                $order_list[] = [
                    'oid'            => $v['order_code'] . '_' . ($k + 1),
                    'num'            => $v['count'],
                    'price'          => $v['cost_price'],
                    'status'         => 30,
                    'refund_status'  => 0,
                    'goods_id'       => $v['commodity_id'],
                    'spec_id'        => $v['sku_id'],
                    'goods_no'       => $v['commodity_code'],
                    'spec_no'        => $v['third_sku_code'],
                    'goods_name'     => $v['commodity_name'],
                    'spec_name'      => $v['sku_info'],
                    'adjust_amount'  => 0,
                    'discount'       => 0,
                    'share_discount' => 0,
                ];
                (new BuOrderCommodity())->saveData(['third_order_id' => $v['order_code'] . '_' . ($k + 1)], ['id' => $v['id']]);
            }

            $area_ids         = explode(',', $order['address_area_ids']);
            $province         = (new DbArea())->where(['base_province_id' => $area_ids[0], 'area_layer' => 1])->find();
            $city             = (new DbArea())->where(['base_city_id' => $area_ids[1], 'area_layer' => 2])->find();
            $county           = (new DbArea())->where(['base_county_id' => $area_ids[2], 'area_layer' => 3])->find();
            $count            = mb_strlen($province['area_name'] . $city['area_name'] . $county['area_name']);
            $address          = mb_substr($order['receipt_address'], $count);
            $receiver_address = $province['area_name'] . ' ' . $city['area_name'] . ' ' . $county['area_name'] . ' ' . $address;
            $trade_list[]     = [
                'tid'               => $order_code,
                'trade_status'      => 30, // 代表已付款
                'pay_status'        => 2, // 平台订单付款状态:0:未付款,1:部分付款,2:已付款
                'delivery_term'     => 1, // 1:款到发货,2:货到付款(包含部分货到付款),3:分期付款,4:挂账
                'trade_time'        => $order['created_date'],
                'pay_time'          => $order['pay_time'],
                'buyer_nick'        => $order['name'],
                'receiver_province' => '',
                'receiver_name'     => $order['name'],
                'receiver_mobile'   => $order['phone'],
                'receiver_city'     => '',
                'receiver_district' => '',
                'receiver_address'  => $receiver_address,
                'seller_memo'            => $remarks[$order_code] ?? '',
                'post_amount'       => $order['mail_price'],
                'paid'              => $paid + $order['mail_price'],
                'cod_amount'        => 0,
                'ext_cod_fee'       => 0,
                'other_amount'      => 0,
                'order_list'        => $order_list,
            ];
        }
        $data = [
            'relativeUrl' => 'trade_push.php',
            'params'      => [
                'shop_no'    => 'shuzi2-test',
                'trade_list' => json_encode($trade_list),
            ],
        ];
        return $data;
    }

    //业务归属
    public function gather_list()
    {
        $model = new DbSystemValue();
        $list  = $model->where(['value_type' => 26, 'is_enable' => 1])->field('sys_value_id as id,county_name as name')->select();
        print_json('0', 'ok', $list);
    }

    //自定义首页和专题页获取活动
    //获取活动
    public function get_activity()
    {
        //type 1限时优惠 2NN 3满优惠 4预售 5多人拼团 6优惠套装 7秒杀 8抽奖 9众筹
        $type = input('type') or print_json(1, '参数丢失');
        $search = input('title');//搜索名称

        $params['where']['is_enable'] = 1;
        if ($type != 7 && $type != 9) {
            $params['where']['act_status'] = 2;#处于活动中
        }
        if(in_array($type,[1,6,8,9])){
            $params['where']['act_status'] = array('in',[1,2]);
        }
        $params['where']['up_down_channel_dlr'] = array('like', '%' . $this->channel_type . '%');

        switch ($type) {
            case 1:
                $params['field']                  = 'id,title,start_time,end_time';
                $params['where']['set_type']      = $this->shelves_type;
                if(!empty($search)){
                    $params['where']['title']         = array('like', '%' . $search . '%');
                }
                $params['where']['discount_type'] = ['<>', 2];
                $params['where']['act_status']    = ['in', [1, 2]];
                $params['order'] = 'id desc';
                $list                             = (new DbLimitDiscount())->getListPaginate($params);
                break;
            case 2:
                $params['field']             = 'id,title,start_time,end_time';
                $params['where']['set_type'] = $this->shelves_type;
                $params['where']['title']    = array('like', '%' . $search . '%');
                $list                        = (new DbNDiscount())->getListPaginate($params);
                break;
            case 3:
                $params['field']                   = 'id,activity_title as title,start_time,end_time';
                $params['where']['set_type']       = $this->shelves_type;
                $params['where']['activity_title'] = array('like', '%' . $search . '%');
                $params['where']['discount_type']  = ['<>', 2];
                $list                              = (new DbFullDiscount())->getListPaginate($params);
                break;
            case 4:
                $params['field']             = 'id,title,front_s_time as start_time,balance_e_time as end_time';
                $params['where']['set_type'] = $this->shelves_type;
                $params['where']['title']    = array('like', '%' . $search . '%');
                $list                        = (new DbPreSale())->getListPaginate($params);
                break;
            case 5: // 拼团
                $params['field']             = 'id,title,start_time,end_time';
                $params['where']['set_type'] = $this->shelves_type;
                $params['where']['title']    = array('like', '%' . $search . '%');
                $list                        = (new DbFightGroup())->getListPaginate($params);
                break;
            case 6: // 优惠套装
                $params['field']               = 'id,name as title,s_time as start_time,e_time as end_time';
                $params['where']['type']       = $this->shelves_type;
                $params['where']['name']       = array('like', '%' . $search . '%');
                $params['where']['act_status'] = ['in', [1, 2]];
                $list                          = (new BuCheapSuitIndex())->getListPaginate($params);

                if (!empty($list)) {
                    foreach ($list as $one) {
                        $one['start_time'] = date("Y-m-d H:i:s", $one['start_time']);
                        $one['end_time']   = date("Y-m-d H:i:s", $one['end_time']);
                    }
                }

                break;
            case 7: // 秒杀
                $params['field']             = 'id,title,start_time,end_time';
//                $params['where']['set_type'] = $this->shelves_type;
                $params['where']['title']    = array('like', '%' . $search . '%');
                $params['order']             = 'id desc';
//                $params['where']['act_status'] = ['in', [1, 2]];
                $list = (new DbSeckill())->getListPaginate($params);
                break;
            case 8: // 抽奖
                $params['field']               = 'id,title,start_time,end_time,draw_url,game_id';
                $params['where']['set_type']   = $this->shelves_type;
                $params['where']['title']      = array('like', '%' . $search . '%');
                $params['where']['act_status'] = ['in', [1, 2]];
                $list                          = (new DbDraw())->getListPaginate($params);
                break;
            case 9: // 众筹
                $status = input('status');
                if ($this->shelves_type == 5) {
                    $params['where']['brand'] = 1;
                } elseif ($this->shelves_type == 6) {
                    $params['where']['brand'] = 3;
                } elseif ($this->shelves_type == 7) {
                    $params['where']['brand'] = 2;
                }
                $params['field'] = 'id,title,start_time,end_time,up_down_channel as channel';
                if (!empty($search)) {
                    $params['where']['title'] = array('like', '%' . $search . '%');
                }
                if (!empty($status)) {
                    $params['where']['act_status'] = $status;
                }
                $params['order']                    = 'id desc';
                $params['where']['up_down_channel'] = $params['where']['up_down_channel_dlr'];
                unset($params['where']['up_down_channel_dlr']);
                $list = (new DbCrowdfund())->getListPaginate($params);
                break;
        }
        print_json('0', 'ok', $list);
    }

    //获取商品
    public function get_product()
    {
        $flat = new DbCommodityFlat();
        $list = $flat->getProduct($this->shelves_type, $this->channel_type, $this->tag);
        print_json(0, 'ok', $list);
    }

    //套装列表
    public function get_suit()
    {
        $title = input('title','');
        if(!empty($title)){
            $param['where']['name'] = array('like','%'.$title."%");
        }
        $model = new BuCheapSuitIndex();
        $model_commodity = new BuCheapSuitCommodity();
        $param['where']['act_status'] = array('in',[1,2]);
        $param['where']['up_down_channel_dlr'] = array('like','%'.$this->channel_type.'%');
        $param['field'] = "id,name as title,s_time,e_time";
        $list = $model->getIndexList($param);
        foreach ($list as $key=>$value){
            $value->time = date("Y-m-d",$value->s_time).'~'.date('Y-m-d',$value->e_time);
            $params['where']['a.suit_id'] = $value->id;
            $params['field'] ='c.cover_image';
            $img = $model_commodity->getCheapCommodity($params);
            $imgs = [];
            foreach ($img as $val){
                $imgs[] = $val['cover_image'];
            }
            $value->imgs = $imgs;
            $value->total = count($img);
            unset($value->s_time,$value->e_time);
        }
        print_json('0', 'ok',$list);
    }




    public function get_card_commodity(HomeTitleValidate $validate)
    {

        $input  = input('get.');
        $result = $validate->scene('get_card_commodity')->check($input);
        //校验失败,返回异常
        if (empty($result)) {
            print_json(1, $validate->getError());
        }
        $service    = new HomeTitleService();
        $pageSize = $input['pagesize'] ?? 10;
        $list = $service->getCardCommodity($input, $pageSize);
        print_json(0, 'success', $list);
    }


    public function view()
    {
        $m_model = new SysMenu();
        $list    = $m_model->alias('a')
            ->join('t_sys_menu b', 'a.menu_pid = b.id')
            ->where(['a.controller' => request()->controller(), 'a.left_view' => 1, 'b.left_view' => 1])
            ->field('a.web_menu_url,b.menu_url')
            ->find();
        if (!empty($list['web_menu_url'])) {
            $username['username']  = $this->admin_info['username'];
            $username['timestamp'] = time();
            $token                 = http_build_query($username);
            $url = Env::get('TOB_URL') . '/' . $list['web_menu_url'] . '?sign=' . base64_encode($token);
            $this->redirect($url);
        }
    }
}

?>
