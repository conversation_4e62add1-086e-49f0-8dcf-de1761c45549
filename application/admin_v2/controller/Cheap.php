<?php

namespace app\admin_v2\controller;

use app\common\model\db\DbSystemValue;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbPreSale;
use think\Hook;
use think\Validate;
use app\common\model;
use app\common\model\bu\BuCheapSuitSub;
use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityType;
use app\common\service\CommodityService;
use app\common\model\db\DbCommodityDlr;
use app\common\model\db\DbCommoditySet;
use app\common\model\bu\BuCheapSuitCommodity;
use app\common\model\db\DbDlr;
use app\common\model\db\DbCommodityDlrType;
use app\common\service\BaseDataService;
use app\common\model\db\DbCard;
use app\common\model\db\DbGift;

class Cheap extends Common
{
    private $_dlrCode = "";
    private $_dbCommodityObj = "";
    private $_dbCommodityTypeObj = "";
    private $fight_group_comm;
    private $comm_service;
    private $_buCheapSuitIndexobj;
    private $_buCheapSuitSubobj;
    private $_buCheapSuitCommodity;
    private $comm_type_model;
    private $dlr;
    private $_dbCommodityFlatObj;
    private $gather_list;

    function __construct()
    {
        parent::__construct();
        $this->_dlrCode               = $this->admin_info['dlr_code'];
        $this->_dbCommodityObj        = new DbCommodity();
        $this->comm_type_model        = new DbCommodityType();
        $this->_dbCommodityTypeObj    = new DbCommodityType();
        $this->_buCheapSuitIndexobj   = new BuCheapSuitIndex();
        $this->_buCheapSuitSubobj     = new BuCheapSuitSub();
        $this->_buCheapSuitCommodity  = new BuCheapSuitCommodity();
        $this->comm_service           = new CommodityService();
        $this->dlr                    = new DbDlr();
        $this->_dbCommodityFlatObj    = new DbCommodityFlat();
        $this->_dbCommodityDlrTypeObj = new DbCommodityDlrType();
        $model                        = new DbSystemValue();
        $this->gather_list            = $model->where(['value_type' => 26, 'is_enable' => 1])->field('sys_value_id as id,county_name as name')->select();
        $this->assign('gather_list', $this->gather_list);
        $this->assign('admin_type', $this->admin_info['type']);
    }

    public function ajaxGetCommTypeId()
    {
        $comm_parent_id = input('get.comm_parent_id');
        $list           = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        print_json(0, '', $list);

    }

    public function wx()
    {
        $show   = 0;
        $type   = 0;
        $status = 0;
        $query  = [];
        $where  = "type = 3";
        $role   = $this->admin_info['type'];

        if ($role == 2) {
            $query['dlr_code'] = $this->_dlrCode;
            $where             .= " and (a.dlrs = '{$this->_dlrCode}' )";
        }

        if (!empty(input('get.name'))) {
            $query['name'] = input('name');
            $name          = input('get.name');
            $where         .= " and a.name like '%" . $name . "%'";
            $show          = 1;
        }


        $sa_name = '';
        if (!empty(input('get.status'))) {
            $query['status'] = input('status');
            $status          = input('get.status');
            $time            = time();
            if ($status == 1) {
                $where .= " and a.s_time > $time";
            } elseif ($status == 2) {
                $where .= " and a.s_time <= '{$time}' and a.e_time >= '{$time}'";
            } elseif ($status == 3) {
                $where .= " and a.e_time < $time";
            }
            $show = 1;
        }

        if (!empty(input('get.default-daterange'))) {
            $range                      = explode('~', input('default-daterange'));
            $query['default-daterange'] = input('default-daterange');
            $s                          = strtotime(trim($range[0]));
            $e                          = strtotime(trim($range[1]));
            $where                      .= " and a.s_time <= '{$s}' and a.e_time >= '{$e}'";
            $show                       = 1;
        }

        $params   = [
            'order'    => 'a.id desc',
            'where'    => $where,
            'field'    => 'a.id,a.name,a.s_time,a.e_time,a.type,a.dlr_code',
            'query'    => $query,
            'pagesize' => 20
        ];
        $time     = time();
        $indexObj = new BuCheapSuitIndex();
        $list     = $indexObj->getIndexList($params);

        $page = $list->render();
        foreach ($list as $k => $item) {
            if ($time < $item['s_time']) {
                $list[$k]['status'] = '未开始';
            } elseif ($time >= $item['s_time'] && $time <= $item['e_time']) {
                $list[$k]['status'] = '进行中';
            } elseif ($time > $item['e_time']) {
                $list[$k]['status'] = '已结束';
            }
        }
        $this->assign('dlrCode', $this->_dlrCode);
        $this->assign('role', $role);
        $this->assign('page', $page);
        $this->assign('query', $query);
        $this->assign('list', $list);
        $this->assign('sa_name', $sa_name);
        $this->assign('status', $status);
        $this->assign('type', $type);
        $this->assign('show', $show);
        return $this->fetch('cheap/index_wx');
    }

    public function live()
    {
        $show          = 0;
        $type          = 0;
        $status        = 0;
        $query         = [];
        $set_type      = input('set_type', 5);
        $where         = "type = " . $set_type;
        $role          = $this->admin_info['type'];
        $gather_id     = input('get.gather_id', 0);
        $is_pv_subsidy = input('get.is_pv_subsidy');
        $theme_name    = input('get.theme_name');

        if (!empty(input('get.name'))) {
            $query['name'] = input('name');
            $name          = input('get.name');
            $where         .= " and a.name like '%" . $name . "%'";
            $show          = 1;
        }

        $sa_name = '';
        if (!empty(input('get.status'))) {
            $query['status'] = input('status');
            $status          = input('get.status');
            $time            = time();
            if ($status == 1) {
                $where .= " and a.s_time > $time";
            } elseif ($status == 2) {
                $where .= " and a.s_time <= '{$time}' and a.e_time >= '{$time}'";
            } elseif ($status == 3) {
                $where .= " and a.e_time < $time";
            } elseif ($status == 4) {
                $where .= " and a.is_enable = 0";
            }
            $show = 1;
            if (in_array($status, [1, 2, 3])) {
                $where .= " and a.is_enable = 1";
            }
        }

        $live_type = input('get.live_type');
        if (empty($live_type)) {
            $live_type = 0;
//            $where     .= " and a.up_down_channel_dlr not like '%PZ1ASM%' and a.up_down_channel_dlr not like '%PZ1AAPP%'";
            $where .= " and ( FIND_IN_SET('GWSM',a.up_down_channel_dlr) or FIND_IN_SET('GWAPP',a.up_down_channel_dlr) )";
        } else {
            if ($live_type == 1) {
                $live_type = 1;
                $where     .= " and ( FIND_IN_SET('PZ1ASM',a.up_down_channel_dlr) or FIND_IN_SET('PZ1AAPP',a.up_down_channel_dlr) )";
            }
            if ($live_type == 2) {
                $live_type = 2;
                $where     .= " and ( FIND_IN_SET('QCSM',a.up_down_channel_dlr) or FIND_IN_SET('QCAPP',a.up_down_channel_dlr))";
            }
        }
        if (!empty(input('get.default-daterange'))) {
            $range                      = explode('~', input('default-daterange'));
            $query['default-daterange'] = input('default-daterange');
            $s                          = strtotime(trim($range[0]));
            $e                          = strtotime(trim($range[1]));
            $where                      .= " and a.s_time >= '{$s}' and a.e_time <= '{$e}'";
            $show                       = 1;
        }
        if (!empty($gather_id)) {
            $where .= ' and a.gather_id = ' . $gather_id;
//            $where['a.gather_id'] = $gather_id;
        }
        if (!empty($is_pv_subsidy)) {
            if ($is_pv_subsidy != 2) {
                $where .= ' and a.is_pv_subsidy = ' . $is_pv_subsidy;
//                $where['a.is_pv_subsidy'] = $is_pv_subsidy;
            } else {
                $where .= ' and a.is_pv_subsidy = 0';
//                $where['a.is_pv_subsidy'] = 0;
            }
        }
        if (!empty($theme_name)) {
            $where .= ' and a.theme_name like "%.' . $theme_name . "%";
//            $where['a.theme_name'] = array('like','%'.$theme_name.'%');
        }

        $params       = [
            'order'    => 'a.id desc',
            'where'    => $where,
            'field'    => 'a.id,a.name,a.s_time,a.e_time,a.type,a.dlr_code,a.up_down_channel_name,a.is_enable,a.gather_id,a.is_pv_subsidy,a.theme_name',
            'query'    => $query,
            'pagesize' => 20
        ];
        $gather_array = [];
        foreach ($this->gather_list as $value) {
            $gather_array[$value['id']] = $value['name'];
        }
        $time     = time();
        $indexObj = new BuCheapSuitIndex();
        $list     = $indexObj->getIndexList($params);
        $page     = $list->render();
        foreach ($list as $k => $item) {
            $in = empty($item['is_enable']) || $status == 4;
            if ($time < $item['s_time']) {
                $list[$k]['status'] = $in ? '已关闭' : '未开始';
            } elseif ($time >= $item['s_time'] && $time <= $item['e_time']) {
                $list[$k]['status'] = $in ? '已关闭' : '进行中';
            } elseif ($time > $item['e_time']) {
                $list[$k]['status'] = $in ? '已关闭' : '已结束';
            }
            if ($item['gather_id'] == 0 || empty($item['gather_id'])) {
                $list[$k]['gather_name'] = '-';
            } else {
                if (isset($gather_array[$item['gather_id']])) {
                    $list[$k]['gather_name'] = $gather_array[$item['gather_id']];
                } else {
                    $list[$k]['gather_name'] = '-';
                }
            }
            $list[$k]['is_pv_subsidy_status'] = $item['is_pv_subsidy'] == 1 ? '是' : '否';
        }

        $this->assign('dlrCode', $this->_dlrCode);
        $this->assign('role', $role);
        $this->assign('page', $page);
        $this->assign('query', $query);
        $this->assign('list', $list);
        $this->assign('sa_name', $sa_name);
        $this->assign('status', $status);
        $this->assign('type', $type);
        $this->assign('set_type', $set_type);
        $this->assign('show', $show);
        $this->assign('live_type', $live_type);
        return $this->fetch('cheap/index_live');
    }

    /**
     * 列表
     */
    public function index()
    {
        $show   = 0;
        $type   = 0;
        $status = 0;
        $query  = [];
        $where  = [];
        $role   = $this->admin_info['type'];
        $where  = " type !=3   ";
        if ($role == 2) {
            $query['dlr_code'] = $this->_dlrCode;
            $where             .= " and (a.dlrs = '{$this->_dlrCode}' )";
        }

        if (!empty(input('get.name'))) {
            $query['name'] = input('name');
            $name          = input('get.name');
            $where         .= " and a.name like '%" . $name . "%'";
            $show          = 1;
        }

        if (!empty(input('get.type'))) {
            $query['type'] = input('type');
            $type          = input('get.type');
            $where         .= " and type = '{$type}'";
            $show          = 1;
        }

        $sa_name = '';
        if (!empty(input('get.status'))) {
            $query['status'] = input('status');
            $status          = input('get.status');
            $time            = time();
            if ($status == 1) {
                $where .= " and a.s_time > $time";
            } elseif ($status == 2) {
                $where .= " and a.s_time <= '{$time}' and a.e_time >= '{$time}'";
            } elseif ($status == 3) {
                $where .= " and a.e_time < $time";
            }
            $show = 1;
        }

        if (!empty(input('get.default-daterange'))) {
            $range                      = explode('~', input('default-daterange'));
            $query['default-daterange'] = input('default-daterange');
            $s                          = strtotime(trim($range[0]));
            $e                          = strtotime(trim($range[1]));
            $where                      .= " and a.s_time <= '{$s}' and a.e_time >= '{$e}'";
            $show                       = 1;
        }

        $params   = [
            'order'    => 'a.id desc',
            'where'    => $where,
            'field'    => 'a.id,a.name,a.s_time,a.e_time,a.type,a.dlr_code',
            'query'    => $query,
            'pagesize' => 20
        ];
        $time     = time();
        $indexObj = new BuCheapSuitIndex();
        $list     = $indexObj->getIndexList($params);

        $page = $list->render();
        foreach ($list as $k => $item) {
            if ($time < $item['s_time']) {
                $list[$k]['status'] = '未开始';
            } elseif ($time >= $item['s_time'] && $time <= $item['e_time']) {
                $list[$k]['status'] = '进行中';
            } elseif ($time > $item['e_time']) {
                $list[$k]['status'] = '已结束';
            }
        }
        $this->assign('dlrCode', $this->_dlrCode);
        $this->assign('role', $role);
        $this->assign('page', $page);
        $this->assign('query', $query);
        $this->assign('list', $list);
        $this->assign('sa_name', $sa_name);
        $this->assign('status', $status);
        $this->assign('type', $type);
        $this->assign('show', $show);

        return $this->fetch('cheap/index');
    }

    /**
     * 车生活
     * @return mixed
     */
    public function cheapInfoLive()
    {
        $setType = input('set_type', 5);

        /**
         * 商品类型列表 积分商城和官微相同
         */
        $type            = DbCommodityDlrType::getType($setType);
        $commDlrTypeList = (new DbCommodityDlrType())->getList([
            'field' => 'id,inner_name',
            'where' => ['is_enable' => 1, 'type' => $type],
        ]);
        $commDlrTypeData = array_column($commDlrTypeList, 'inner_name', 'id');
        $this->assign('comm_dlr_type_list', $commDlrTypeList);
//        $re = $this->comm_type_model->getCommodityByParentId(0);
//        dd($re);
        $this->assign('comm_dlr_type_json', json_encode($commDlrTypeData));
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', (new DbSystemValue())->getNameList(20));
        $this->assign('commodity_class', DbCommodity::commodityClass());
        $this->assign('live_type', input('live_type'));
        $this->assign('set_type', input('set_type', 5));
        return $this->fetch('cheap/info_live');
    }

    public function cheapInfo()
    {
        $comm_parent_id     = input('get.comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $comm_parent_list   = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list     = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        if (!empty($order_code)) {
            $where['a.order_code'] = ['like', '%' . $order_code . '%'];
        }
        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('sub_comm_type_id', $sub_comm_type_id);
        $this->assign('three_comm_type_id', $three_comm_type_id);
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);

        $baseService = new BaseDataService();
        $dlr_data    = $baseService->getDlrList();
        $this->assign('dlr_data', $dlr_data);
        return $this->fetch('cheap/info');
    }

    public function cheapInfoWx()
    {

        $comm_parent_id     = input('get.comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $comm_parent_list   = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list     = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        if (!empty($order_code)) {
            $where['a.order_code'] = ['like', '%' . $order_code . '%'];
        }

        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('sub_comm_type_id', $sub_comm_type_id);
        $this->assign('three_comm_type_id', $three_comm_type_id);
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);

        $typeList    = $this->_dbCommodityTypeObj->where("comm_parent_id = 0")->select();
        $typesubList = array();
        $ptype       = input('ptype',0);
        if ($ptype > 0) {
            $typesubList = $this->_dbCommodityTypeObj->where("comm_parent_id = {$ptype}")->select();
        }
        $this->assign('typesubList', $typesubList);
        $this->assign('typeList', $typeList);

        $baseService = new BaseDataService();
        $dlr_data    = $baseService->getDlrList();
        $this->assign('dlr_data', $dlr_data);

        return $this->fetch('cheap/infowx');
    }

    public function update()
    {
        $id        = input('id');
        $indexInfo = $this->_buCheapSuitIndexobj->where(array('id' => $id))->find();
        $this->assign('indexInfo', $indexInfo);


        $comm_parent_id     = input('get.comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $comm_parent_list   = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list     = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        if (!empty($order_code)) {
            $where['a.order_code'] = ['like', '%' . $order_code . '%'];
        }

        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('sub_comm_type_id', $sub_comm_type_id);
        $this->assign('three_comm_type_id', $three_comm_type_id);
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);

        $subgroup = $this->_buCheapSuitSubobj->query("select * from t_bu_cheap_suit_sub a INNER join t_db_commodity_set b ON  a.commodity_set_id = b.id where a.index_id='{$id}' group by a.commodity_id");

        $row_array     = array();
        $commodity_arr = array();
        $sku_array     = array();
        foreach ($subgroup as $item) {
            $commodity_id              = $item['commodity_id'];
            $commodity_set_id          = $item['commodity_set_id'];
            $comm_set_model            = new DbCommoditySet();
            $com_set_row               = $comm_set_model->getOne(['where' => ['id' => $commodity_set_id], 'field' => 'commodity_id,dlr_code,set_type']);
            $sku                       = $this->comm_service->getSetSku($commodity_id, $com_set_row['dlr_code'], $com_set_row['set_type']);
            $sku_list                  = $sku['sku_list'];
            $commodity_row             = $this->_dbCommodityObj->getOne(['where' => ['id' => $commodity_id], 'field' => 'commodity_name,cover_image,comm_type_id,id']);
            $commodity_row['dlr_code'] = $com_set_row['dlr_code'];
            $subList                   = $this->_buCheapSuitSubobj->where(array('index_id' => $id, 'commodity_id' => $commodity_id))->select();
            $sku_item                  = array();
            foreach ($subList as $subsku) {
                $sku_item[$subsku['sku']] = $subsku['price'];
            }
            $json = array(
                'commodity_id'     => $commodity_row['id'],
                'commodity_set_id' => $commodity_set_id,
                'commodity_name'   => $commodity_row['commodity_name'],
                'sku_list'         => $sku_item,
            );

            $commodity_arr[]                   = $commodity_row['id'];
            $commodity_row['json']             = json_encode_cn($json);
            $commodity_row['is_home']          = $item['is_home'];
            $commodity_row['commodity_set_id'] = $item['commodity_set_id'];
            $row_array[]                       = $commodity_row;
        }
        $this->assign('commodity_arr', json_encode($commodity_arr));


        $this->assign('id', $id);
        $this->assign('row_array', $row_array);
        $baseService = new BaseDataService();
        $dlr_data    = $baseService->getDlrList();
        $this->assign('dlr_data', $dlr_data);
        return $this->fetch('cheap/update');
    }

    public function updateLive()
    {
        $id        = input('id');
        $indexInfo = $this->_buCheapSuitIndexobj->where(array('id' => $id))->find();

        //优惠券
        $card_arr            = [];
        $selected_data       = [];
        $commodity_card_name = [];
        if (!empty($indexInfo['rel_card_ids'])) {
            $commodity_card_name = (new DbCard())->getColumn(['where' => ['id' => ['in', $indexInfo['rel_card_ids']]], 'column' => 'card_name']);
            $selected_data       = (new DbCard())->getColumn(['where' => ['id' => ['in', $indexInfo['rel_card_ids']]], 'column' => 'id,card_name']);
            $card_arr            = explode(',', $indexInfo['rel_card_ids']);
        }
        $indexInfo['commodity_card_name'] = implode(',', $commodity_card_name);
        $indexInfo['num_card']            = empty($indexInfo['rel_card_ids']) ? 0 : count(explode(',', $indexInfo['rel_card_ids']));

        $setType         = input('set_type', 5);
        $type            = DbCommodityDlrType::getType($setType);
        $commDlrTypeList = (new DbCommodityDlrType())->getList([
            'field' => 'id,inner_name',
            'where' => ['is_enable' => 1, 'type' => $type],
        ]);
        $commDlrTypeData = array_column($commDlrTypeList, 'inner_name', 'id');
        $this->assign('comm_dlr_type_json', json_encode($commDlrTypeData));
        $this->assign('comm_dlr_type_list', $commDlrTypeList);
        $this->assign('selected', json_encode($card_arr));
        $this->assign('selected_data', $selected_data);
        $this->assign('selected_card_name', json_encode($commodity_card_name));


        $this->assign('indexInfo', $indexInfo);
        $this->assign('row', $indexInfo);

//        $subgroup = $this->_buCheapSuitCommodity->query("select * from t_bu_cheap_suit_commodity a INNER join t_db_commodity_set b ON  a.commodity_set_id = b.id where a.suit_id='{$id}' group by a.commodity_id");
        $params['where']['a.suit_id'] = $id;
        $params['group']              = 'a.commodity_id';
        $params['field']              = '*';
        $subgroup                     = $this->_buCheapSuitCommodity->getCommodityList($params);

        $row_array             = array();
        $commodity_arr         = array();
        $oneCommodityDlrTypeId = 0;
        foreach ($subgroup as $item) {
            $commodity_id                      = $item['commodity_id'];
            $commodity_set_id                  = $item['commodity_set_id'];
            $comm_set_model                    = new DbCommoditySet();
            $com_set_row                       = $comm_set_model->getOne(['where' => ['id' => $commodity_set_id], 'field' => 'commodity_id,dlr_code,set_type']);
            $commodity_row                     = $this->_dbCommodityObj->getOne(['where' => ['id' => $commodity_id], 'field' => 'commodity_name,cover_image,comm_type_id,id']);
            $commodity_row['dlr_code']         = $com_set_row['dlr_code'];
            $sku_item                          = json_decode($item['sku_json']);
            $json                              = array(
                'commodity_id'          => $commodity_row['id'],
                'commodity_set_id'      => $commodity_set_id,
                'commodity_class'       => $item['commodity_class'],
                'commodity_dlr_type_id' => $item['commodity_dlr_type_id'],
                'dd_commodity_type'     => $item['dd_commodity_type'],
                'commodity_name'        => $commodity_row['commodity_name'],
                'sku_list'              => $sku_item,
            );
            $commodity_arr[]                   = $commodity_row['id'];
            $commodity_row['json']             = json_encode_cn($json);
            $commodity_row['commodity_set_id'] = $item['commodity_set_id'];
            $commodity_row['commodity_class']  = $item['commodity_class'];
            $row_array[]                       = $commodity_row;
            $oneCommodityDlrTypeId             = $item['commodity_dlr_type_id'];
        }
        $up_down_channel_info = (new DbSystemValue())->getNameList(20);
        $info_str             = implode(',', array_keys($up_down_channel_info));
        $this->assign('commodity_arr', json_encode($commodity_arr));
        $this->assign('oneCommodityDlrTypeId', $oneCommodityDlrTypeId);
        $this->assign('id', $id);
        $this->assign('row_array', $row_array);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', $up_down_channel_info);
//        $this->assign('dlr_str', $this->getDlrInInfo($indexInfo['up_down_channel_dlr']));
        $this->assign('dlr_hidden', (strpos($indexInfo['up_down_channel_dlr'], 'GWDLR') !== false) ? '' : 'hidden');
        $this->assign('commodity_class', DbCommodity::commodityClass());
        $this->assign('live_type', input('live_type'));
        $this->assign('set_type', input('set_type'));
        return $this->fetch('cheap/update_live');
    }

    public function updatewx()
    {
        $id        = input('id');
        $indexInfo = $this->_buCheapSuitIndexobj->where(array('id' => $id))->find();
        $this->assign('indexInfo', $indexInfo);


        $comm_parent_id     = input('get.comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $comm_parent_list   = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list     = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        if (!empty($order_code)) {
            $where['a.order_code'] = ['like', '%' . $order_code . '%'];
        }

        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('sub_comm_type_id', $sub_comm_type_id);
        $this->assign('three_comm_type_id', $three_comm_type_id);
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);

        $subgroup = $this->_buCheapSuitSubobj->query("select * from t_bu_cheap_suit_sub a INNER join t_db_commodity_set b ON  a.commodity_set_id = b.id where a.index_id='{$id}' group by a.commodity_id");

        $row_array     = array();
        $commodity_arr = array();
        $sku_array     = array();
        foreach ($subgroup as $item) {
            $commodity_id              = $item['commodity_id'];
            $commodity_set_id          = $item['commodity_set_id'];
            $comm_set_model            = new DbCommoditySet();
            $com_set_row               = $comm_set_model->getOne(['where' => ['id' => $commodity_set_id], 'field' => 'commodity_id,dlr_code,set_type']);
            $sku                       = $this->comm_service->getSetSku($commodity_id, $com_set_row['dlr_code'], $com_set_row['set_type']);
            $sku_list                  = $sku['sku_list'];
            $commodity_row             = $this->_dbCommodityObj->getOne(['where' => ['id' => $commodity_id], 'field' => 'commodity_name,cover_image,comm_type_id,id']);
            $commodity_row['dlr_code'] = $com_set_row['dlr_code'];
            $subList                   = $this->_buCheapSuitSubobj->where(array('index_id' => $id, 'commodity_id' => $commodity_id))->select();
            $sku_item                  = array();
            foreach ($subList as $subsku) {
                $sku_item[$subsku['sku']] = $subsku['price'];
            }
            $json = array(
                'commodity_id'     => $commodity_row['id'],
                'commodity_set_id' => $commodity_set_id,
                'commodity_name'   => $commodity_row['commodity_name'],
                'sku_list'         => $sku_item,

            );

            $commodity_arr[]                   = $commodity_row['id'];
            $commodity_row['json']             = json_encode_cn($json);
            $commodity_row['is_home']          = $item['is_home'];
            $commodity_row['commodity_set_id'] = $item['commodity_set_id'];
            $row_array[]                       = $commodity_row;

        }
        $this->assign('commodity_arr', json_encode($commodity_arr));


        $this->assign('id', $id);
        $this->assign('row_array', $row_array);
        $baseService = new BaseDataService();
        $dlr_data    = $baseService->getDlrList();
        $this->assign('dlr_data', $dlr_data);
        return $this->fetch('cheap/update_wx');
    }

    public function ajaxGetSetId()
    {
        $dlrs   = input('dlrCodes');
        $dlrarr = explode(',', $dlrs);

        $comm_set_model = new DbCommoditySet();
        $pid            = $comm_set_model->getAllCommoditySetId($dlrarr);

        echo json_encode($pid);
        exit;
    }

    private function _getCommTypeName()
    {
        $list = $this->_dbCommodityTypeObj->commodityTypeList();
        $data = [];
        foreach ($list as $key => $val) {
            $data[$val['id']] = $val['comm_type_name'];
        }
        return $data;
    }

    public function show()
    {
        $id          = input('id');
        $typeList    = $this->_dbCommodityTypeObj->where("comm_parent_id = 0")->select();
        $typesubList = array();
        $ptype       = input('ptype', 0);
        if ($ptype > 0) {
            $typesubList = $this->_dbCommodityTypeObj->where("comm_parent_id = {$ptype}")->select();
        }
        $this->assign('typesubList', $typesubList);
        $this->assign('typeList', $typeList);

        $indexInfo = $this->_buCheapSuitIndexobj->where(array('id' => $id))->find();
        $this->assign('indexInfo', $indexInfo);


        $subgroup  = $this->_buCheapSuitSubobj->where(array('index_id' => $id))->group('commodity_id')->select();
        $row_array = array();
        $sku_array = array();

        foreach ($subgroup as $item) {
            $commodity_id              = $item['commodity_id'];
            $commodity_set_id          = $item['commodity_set_id'];
            $comm_set_model            = new DbCommoditySet();
            $com_set_row               = $comm_set_model->getOne(['where' => ['id' => $commodity_set_id], 'field' => 'commodity_id,dlr_code,set_type']);
            $sku                       = $this->comm_service->getSetSku($commodity_id, $com_set_row['dlr_code'], $com_set_row['set_type']);
            $sku_list                  = $sku['sku_list'];
            $commodity_row             = $this->_dbCommodityObj->getOne(['where' => ['id' => $commodity_id], 'field' => 'commodity_name,cover_image,comm_type_id,id']);
            $commodity_row['dlr_code'] = $com_set_row['dlr_code'];
            $subList                   = $this->_buCheapSuitSubobj->where(array('index_id' => $id, 'commodity_id' => $commodity_id))->select();
            $sku_item                  = array();
            foreach ($subList as $subsku) {
                $sku_item[$subsku['sku']] = $subsku['price'];
            }

            $json                  = array(
                'commodity_id'     => $commodity_row['id'],
                'commodity_set_id' => $commodity_set_id,
                'commodity_name'   => $commodity_row['commodity_name'],
                'sku_list'         => $sku_item
            );
            $commodity_row['json'] = json_encode_cn($json);
            $row_array[]           = $commodity_row;
        }
        $this->assign('id', $id);
        $this->assign('time', time());
        $this->assign('row_array', $row_array);
        return $this->fetch('cheap/show');
    }

    public function selectType()
    {
        $id          = input('id');
        $typesubList = $this->_dbCommodityTypeObj->where("comm_parent_id = {$id}")->select();
        echo json_encode_cn(array('error' => 0, 'data' => $typesubList));
    }

    public function getSkuLive()
    {
        $commodity_id     = input('get.commodity_id');
        $commodity_set_id = input('get.commodity_set_id');
        $sku              = $this->comm_service->getSetSku($commodity_set_id);
        $sku_list         = $sku['sku_list'];
        $commodity_row    = $this->_dbCommodityFlatObj->getOne([
            'where'        => ['commodity_id' => $commodity_id, 'commodity_set_id' => $commodity_set_id],
            'field'        => 'commodity_name,cover_image,comm_type_id,commodity_class,dd_commodity_type',
            'shelves_type' => 5
        ]);

        foreach ($sku_list as $key => $val) {
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'];
            if (empty($val['sp_value_arr'])) {
                continue;
            }
            foreach ($val['sp_value_arr'] as $key1 => $val1) {
                $sku_list[$key]['sku_val'] .= '/' . $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
            }
        }
        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);
    }

    public function getSku()
    {
        $commodity_id     = input('get.commodity_id');
        $commodity_set_id = input('get.commodity_set_id');
        $comm_set_model   = new DbCommoditySet();
        $com_set_row      = $comm_set_model->getOne(['where' => ['id' => $commodity_set_id], 'field' => 'commodity_id,dlr_code,set_type']);
        $sku              = $this->comm_service->getSetSku($commodity_set_id);
        $sku_list         = $sku['sku_list'];
        $commodity_row    = $this->_dbCommodityObj->getOne(['where' => ['id' => $commodity_id], 'field' => 'commodity_name,cover_image,comm_type_id']);

        foreach ($sku_list as $key => $val) {
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'];
            foreach ($val['sp_value_arr'] as $key1 => $val1) {
                $sku_list[$key]['sku_val'] .= '/' . $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
            }
        }
        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);
    }


    /**
     * 通过上架id 获取可以选专营店
     */
    public function ajaxGetDlr_bak()
    {
        if ($this->admin_info['type'] == 1) {
            $dlr_list = $this->dlr->getList(['where' => [], 'field' => 'dlr_code,dlr_name']);
        } else {
            $dlr_list = $this->dlr->getList(['where' => ['dlr_code' => ['in', $this->_dlrCode]], 'field' => 'dlr_code,dlr_name']);
        }
        print_json(0, '', $dlr_list);
    }

    public function ajaxGetDlr()
    {

        $baseService = new BaseDataService();
        $dlr_data    = $baseService->getDlrList();
        print_json(0, '', $dlr_data);
    }

    /**
     * 获取车商城商品列表
     * @return mixed
     */
    public function ajaxGetLiveCommodityList()
    {
        $commodity_name        = input('commodity_name');
        $top_type              = input('comm_parent_id');
        $second_type           = input('sub_comm_type_id');
        $third_type            = input('three_comm_type_id');
        $active_id             = input('active_id');
        $live_type             = input('live_type');
        $commodity_dlr_type_id = input('commodity_dlr_type_id');
        $up_down_channel_dlr   = input('dlrs');
        $up_down_channel_dlr   = explode(',', $up_down_channel_dlr);
        $dlr_array             = [];
        foreach ($up_down_channel_dlr as $value) {
            $dlr_array[] = ['like', '%' . $value . '%'];
        }
        $dlr_array[] = 'and';
        $where = [
            'b.listing_type'      => 1,
            'c.dd_commodity_type' => ['neq', 12],
            'c.commodity_class'   => ['neq', DbCommodity::COMMODITY_CLASS_KEY9], // 延保服务包不能参加
        ];

        if (empty($live_type)) {
            $shelves_type = 5;
//            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
            $where['a.up_down_channel_dlr'] = $dlr_array;
            $where['b.qsc_group']           = ''; // 取送车服务包不能参加
        } elseif ($live_type == 1) {
            $shelves_type = 6;
//            $where[]      = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        } elseif ($live_type == 2) {
            $shelves_type                   = 7;
            $where['a.up_down_channel_dlr'] = $dlr_array;
//            $where[]      = [['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], 'or'];
        }
//        $where['a.seckill_dis'] = ''; // 参加秒杀商品不参与优惠套装活动

        if (!empty($commodity_name)) {
            $where['a.commodity_name'] = ['like', "%$commodity_name%"];
        }

        $type_id = 0;

        if (!empty($third_type)) {
            $type_id = $third_type;
        } else if (!empty($second_type)) {
            $type_id = $second_type;
        } else if (!empty($top_type)) {
            $type_id = $top_type;
        }

//        $where['commodity_class'] = 1;#只是实物商品
//        $where['a.pay_style']     = ['<>', 3];#商品支付方式,不可以纯积分支付的商品

        if (!empty($type_id)) {
            $where[] = ['exp', " (find_in_set({$type_id},a.comm_type_id_str) ) "];
        }

        if (!empty($commodity_dlr_type_id)) {
            $where['b.commodity_dlr_type_id'] = $commodity_dlr_type_id;
        }

        $where['b.shelves_type'] = $shelves_type;

        $where['c.is_grouped'] = 0;
        $field                   = "b.commodity_dlr_type_id,a.commodity_class,a.comm_type_id,a.up_down_channel_name,
        a.commodity_id,a.commodity_set_id,a.commodity_name,a.cover_image,a.price,a.count_stock,c.dd_commodity_type";
        $params = array(
            'qurey'    => input('get.'),
            'where'    => $where,
            'pagesize' => input('pagesize'),
            'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
            'field'    => $field,
        );

        $flat = new DbCommodityFlat();
        $list = $flat->getPz1aCommodityList($params);
//        $date = date('Y-m-d H:i:s');

//        //判断是否已经参加了预售
//        $list = (new DbPreSale())->getIsPreProduct($list, [
//            'a.set_type'       => $shelves_type,
//            'a.is_enable'      => ['=', 1],
//            'a.front_s_time'   => ['<', $date],
//            'a.balance_e_time' => ['>', $date]
//        ]);
//
//        //判断是否参加买赠
        $list = (new model\db\DbSeckill())->getIsSeckillProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ]);


        //如果是快递到家
        $homeArr = $this->_dbCommodityDlrTypeObj->getExpressHomeType();
        foreach ($list as $key => $val) {
            $list[$key]['home'] = 0;
            if (isset($val['commodity_dlr_type_id']) && in_array($val['commodity_dlr_type_id'], $homeArr)) {
                $list[$key]['home'] = 1;
            }
        }

        $res         = [];
        $res['list'] = $list;
        print_json(0, '', $res);
    }

    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetCommodityList()
    {
        $commodity_name     = input('commodity_name');
        $comm_type_id       = input('comm_type_id');
        $comm_parent_id     = input('comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $wxtag              = input('wxtag', 0);
        $params['where']    = [];
        $params['query']    = [];

        $params['where']['is_grouped'] = 0;
        if (!empty($commodity_name)) {
            $params['where']['a.commodity_name'] = ['like', "%$commodity_name%"];
        }

        if (!empty($three_comm_type_id)) {
            $params['where']['a.comm_type_id'] = $three_comm_type_id;
        } else {
            if (!empty($sub_comm_type_id)) {
                $comm_type_column = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id], 'column' => 'id']);

                $params['where']['a.comm_type_id'] = ['in', $comm_type_column];
            } else {
                if (!empty($comm_parent_id)) {
                    $two_type_column                   = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                    $three_type_column                 = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => ['in', $two_type_column]], 'column' => 'id']);
                    $params['where']['a.comm_type_id'] = ['in', $three_type_column];
                }
            }
        }
        $params['where']['a.commodity_class'] = 1;
        $params['field']                      = 'e.commodity_dlr_type_id,e.is_mail,a.id,a.commodity_name,a.cover_image,a.comm_type_id,a.set_type,a.create_dlr_code, b.comm_type_name,b.comm_parent_id,e.id as commodity_set_id,e.dlr_code,e.original_price_range_end,e.count_stock';
        $params['order']                      = 'a.id desc';
        $params['query']                      = input('get.');
        $params['pagesize']                   = input('pagesize');
        $dlr_code                             = $this->admin_info['dlr_code'];
        $set_type                             = $this->admin_info['type'];

        $dlrstr = input('dlrs');
        $dlrs   = $this->admin_info['dlr_code'];
        $dlrArr = array();
        if ($this->admin_info['type'] == 1) {
            if (!empty($dlrstr)) {
                $dlrArr = explode(',', $dlrstr);
            }
            if ($wxtag == 1) {

                $list = $this->_dbCommodityObj->getSelfmployedCommodityWxByDrl($this->admin_info['dlr_code'], $dlrArr, $params);
            } else {

                $list = $this->_dbCommodityObj->getSelfmployedCommodityByDrl($this->admin_info['dlr_code'], $dlrArr, $params);
            }
        } else {

            $list = $this->_dbCommodityObj->getShelvesListByDlr($set_type, $dlr_code, $params);
        }
        ///  echo  $this->_dbCommodityObj->getLastSql();exit;
        foreach ($list as $key => $val) {
            if (!empty($val['original_price_range_end'])) {
                $list[$key]['highest_price'] = $val['original_price_range_end'];
            }
        }
        // $list =  $this->_dbCommodityObj->getShelvesListByDlr($set_type,$dlr_code,$params);
        //如果在，是快递到家
        $homeArr               = $this->_dbCommodityDlrTypeObj->getExpressHomeType();
        $commodity_set_id_list = [];
        foreach ($list as $key => $val) {
            $commodity_set_id_list[] = $val['commodity_set_id'];
            $list[$key]['is_fight']  = 0;
            $list[$key]['home']      = 0;

            if (isset($val['commodity_dlr_type_id']) && in_array($val['commodity_dlr_type_id'], $homeArr)) {
                $list[$key]['home'] = 1;
            }
        }

        $comm_parent_list2        = $this->_dbCommodityTypeObj->getCommTypeName();
        $res                      = [];
        $res['list']              = $list;
        $res['comm_parent_list2'] = $comm_parent_list2;
        print_json(0, '', $res);
    }

    public function delete()
    {
        $this->_checkAjax();
        $id  = input('post.id');
        $row = $this->_buCheapSuitIndexobj->getOneByPk($id);
        $this->_buCheapSuitSubobj->where(['index_id' => $id])->delete();
        $this->_buCheapSuitIndexobj->where(['id' => $id])->delete();
        if ($row['type'] == 5) {
            $cheap_commodity_arr = $this->_buCheapSuitIndexobj->getAllSuit([
                'where' => ['a.id' => $id],
                'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
            ]);
            $this->doHook('delete', $cheap_commodity_arr, $id);
        }
        print_json(0, '删除成功');
    }


    /**
     * 保存数据
     */
    public function save()
    {
        $post = input('post.');
        $role = $this->admin_info['type'];
        $title = input('post.name') or print_json(1, '活动名称不能为空');
        $start_time = input('post.s_time') or print_json(1, '开始时间不能为空');
        $end_time = input('post.e_time') or print_json(1, '结束时间不能为空');


        if ($role == 2) {
            $dlrs      = $this->_dlrCode;
            $dlrs_name = $this->admin_info['dlr_name'];
        } else {
            $dlrs = input('post.dlrs') or print_json(1, '专营店不能为空');

        }

        $dlrs_name = input('post.dlrs_name');
//        $sku_list  = input('sku_list', '');
        $sku_list = $post['sku_list'] ?? '';
        $wxtag = $post['wxtag'] ?? '';
        $home      = input('post.home');

        if (empty($sku_list)) print_json(1, '商品不能为空');
        $tag = input('post.tag');
        $id  = input('post.id');
        if ($start_time > $end_time) print_json(1, '结束时间不能小于开始时间');


        $data = [
            'name'      => $title,
            's_time'    => strtotime($start_time),
            'e_time'    => strtotime($end_time),
            'type'      => 1,
            'dlr_code'  => $this->_dlrCode,
            'tag'       => $tag,
            'dlrs'      => $dlrs,
            'dlrs_name' => $dlrs_name
        ];

        if ($role == 2) {
            $data['type'] = 2;
        }
        if ($wxtag == 1) {
            $data['type'] = 3;
        }
        $last_id = 320;
        $res     = $this->insertCommodiyData($sku_list, $last_id, 'add', $home);
        exit();
        //开启事物
        $this->_buCheapSuitIndexobj->startTrans();
        $BuCheapSuitSubobj = new BuCheapSuitSub();
        if (empty($id)) {  //插入
            $last_id = $this->_buCheapSuitIndexobj->insertGetId($data);
            $res     = $this->insertCommodiyData($sku_list, $last_id, 'add', $home);
            //提交

            $this->_buCheapSuitIndexobj->commit();
            if ($res) print_json(0, '保存成功');

        } else {    //修改
            $row = $this->_buCheapSuitIndexobj->getOneByPk($id);

            if ($row) {


                $this->_buCheapSuitIndexobj->where(['id' => $id])->update($data);

                $this->_buCheapSuitSubobj->where(['index_id' => $id])->delete();

                $res = $this->insertCommodiyData($sku_list, $id, 'update', $home);

                $this->_buCheapSuitIndexobj->commit();
                print_json(0, '保存成功');

            }

        }
        print_json(1, '保存失败');
    }

    /**
     * 保存数据 车生活
     */
    public function saveLive()
    {
        $post = input('post.');
        $title = input('post.name') or print_json(1, '活动名称不能为空');
        $start_time = input('post.s_time') or print_json(1, '开始时间不能为空');
        $end_time = input('post.e_time') or print_json(1, '结束时间不能为空');
        $sku_list = [];
        if (empty($_POST['sku_list'])) print_json(1, '商品规格不能为空');
        if (!empty($_POST['sku_list'])) {
            $sku_list = json_decode($_POST['sku_list'], true);
        }
        $home                = input('post.home');
        $commodity_class     = input('post.commodity_class_live');
        $is_enable           = input('post.is_enable');
        $card_available      = input('post.card_available', 0);
        $type                = input('post.type',5);
        $gather_id = input('gather_id',0);
        $is_pv_subsidy = input('is_pv_subsidy',0);
        $theme_name = input('theme_name');
        $up_down_channel = $post['up_down_channel'] ?? [];
        $up_down_channel_dlr = getUpDownChannel(
            implode(',', $up_down_channel),
            input('post.dlr_code', '')
        );

        if (empty($sku_list)) print_json(1, '商品不能为空');
        $tag = input('post.tag');
        $id  = input('post.id');
        if ($start_time > $end_time) print_json(1, '结束时间不能小于开始时间');
        $s_time              = strtotime($start_time);
        $e_time              = strtotime($end_time);
        $act_status          = $this->_buCheapSuitIndexobj->getActStatus($s_time, $e_time);
        $full_discount_rules = input('post.full_discount_rules', '');

        $dis_type   = input('post.dis_type', 1);
        $discount   = input('post.discount', 0);
        $final_info = [];
        if ($dis_type == 2) {
            if (!empty($full_discount_rules)) {
                $rules_info = json_decode($full_discount_rules);
                $key_info   = array_column($rules_info, 0);
                rsort($key_info);
                foreach ($key_info as $item) {
                    foreach ($rules_info as $rule_item) {
                        if ($rule_item[0] == $item) {
                            $final_info[] = $rule_item;
                        }
                    }
                }
            }
            if (empty($final_info)) {
                print_json(1, '立减梯度不能为空');
            }
        }
        if ($dis_type != 1 && $dis_type != 2) {
            if ($discount == 0) {
                print_json(1, '折扣/一口价价格不能为空');
            }
        }
        $data = [
            'name'                  => $title,
            's_time'                => $s_time,
            'e_time'                => $e_time,
            'type'                  => $type,
            'dlr_code'              => $this->_dlrCode,
            'tag'                   => $tag,
            'card_available'        => $card_available,
            'is_enable'             => $is_enable,
            'up_down_channel_name'  => implode(',', $post['up_down_channel_name'] ?? []),
            'up_down_channel_dlr'   => $up_down_channel_dlr,
            'act_status'            => $act_status,
            'e3s_activity_id'       => $post['e3s_activity_id'] ?? 0,
            'activity_type'         => $post['activity_type'] ?? 0,
            'settlement_rule_id'    => $post['settlement_rule_id'],
            'settlement_rule_name'  => $post['settlement_rule'],
            'settlement_rule_type'  => $post['settlement_rule_type'],
            'settlement_rule_value' => $post['settlement_rule_value'],
            'act_sett_standard'     => $post['act_sett_standard'],
            'rel_card_ids'          => $post['rel_card_ids'] ?? '',
            'can_refund'            => $post['can_refund'] ?? 1,
            'detail_content'        => $post['detail_content'] ?? '',
            'full_info'             => json_encode($final_info),
            'dis_type'              => $dis_type,
            'discount'              => $discount,
            'gather_id'             => $gather_id,
            'is_pv_subsidy'         => $is_pv_subsidy,
            'theme_name'            => $theme_name,
        ];
        if (!empty($data['e3s_activity_id'])) {
            if (empty($data['activity_type'])) print_json(1, '存在e3s活动时活动设置类型必选');
        } else {
            $data['activity_type'] = 0;
        }
        $e3s_activity = input('e3s_activity', '');
        if (!empty($e3s_activity)) {
            $data['e3s_activity_name'] = explode(' | ', $e3s_activity)[1];
        }
        $settlement_rule = input('settlement_rule', '');

        //开启事物
        $this->_buCheapSuitIndexobj->startTrans();

        if (empty($id)) {  //插入
            $last_id = $this->_buCheapSuitIndexobj->insertGetId($data);
            $res     = $this->insertCommodiyDataLive($sku_list, $last_id, 'add', $home, $commodity_class);
            //提交
            $this->_buCheapSuitIndexobj->commit();
            if (($act_status == 2) && !empty($is_enable)) {
                $cheap_commodity_arr = $this->_buCheapSuitIndexobj->getAllSuit([
                    'where' => ['a.id' => $last_id],
                    'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
                ]);
                $this->doHook('add', $cheap_commodity_arr, $last_id);
            }

            if ($res) print_json(0, '保存成功');
        } else {    //修改
            $row = $this->_buCheapSuitIndexobj->getOneByPk($id);
            if ($row) {

                $old_commodity_id_arr = $this->_buCheapSuitCommodity->where(['suit_id' => $id])->column('commodity_id');
                $new_commodity_id_arr = array_column($sku_list, 'commodity_id');
                $rm_commodity_id_arr  = array_diff($old_commodity_id_arr, $new_commodity_id_arr);

                $this->_buCheapSuitIndexobj->where(['id' => $id])->update($data);
                $this->_buCheapSuitCommodity->where(['suit_id' => $id])->delete();
                $this->insertCommodiyDataLive($sku_list, $id, 'update', $home, $commodity_class);
                $this->_buCheapSuitIndexobj->commit();
                if (($act_status == 2) && !empty($is_enable)) {
                    $cheap_commodity_arr = $this->_buCheapSuitIndexobj->getAllSuit([
                        'where' => ['a.id' => $id],
                        'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
                    ]);
                    $this->doHook('update', $cheap_commodity_arr, $id);
                } else if (empty($is_enable)) {
                    $cheap_commodity_arr = $this->_buCheapSuitIndexobj->getAllSuit([
                        'where' => ['a.id' => $id],
                        'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
                    ]);
                    $this->doHook('delete', $cheap_commodity_arr, $id);
                }

                if (!empty($rm_commodity_id_arr)) {
                    $this->doHook('delete', $rm_commodity_id_arr, $id);
                }

                print_json(0, '保存成功');
            }
        }
        print_json(1, '保存失败');
    }

    /**
     * 保存数据
     */
    public function save1()
    {
        $post = input('post.');
        $role = $this->admin_info['type'];
        $title = input('post.name') or print_json(1, '活动名称不能为空');
        $start_time = input('post.s_time') or print_json(1, '开始时间不能为空');
        $end_time = input('post.e_time') or print_json(1, '结束时间不能为空');


        if ($role == 2) {
            $dlrs      = $this->_dlrCode;
            $dlrs_name = $this->admin_info['dlr_name'];
        } else {
            $dlrs = 'GWSC';

        }

        $dlrs_name = input('post.dlrs_name');
        $sku_list  = input('sku_list', '');
        $home      = input('post.home');

        if (empty($sku_list)) print_json(1, '商品不能为空');
        $tag = input('post.tag');

        $id = input('post.id');
        if ($start_time > $end_time) print_json(1, '结束时间不能小于开始时间');


        $data = [
            'name'      => $title,
            's_time'    => strtotime($start_time),
            'e_time'    => strtotime($end_time),
            'type'      => 1,
            'dlr_code'  => $this->_dlrCode,
            'tag'       => $tag,
            'dlrs'      => $dlrs,
            'dlrs_name' => $dlrs_name
        ];

        if ($role == 2) {
            $data['type'] = 2;
        }
        $wxtag = $post['wxtag'] ?? '';
        if ($wxtag == 1) {
            $data['type'] = 3;
        }

        //开启事物
        $this->_buCheapSuitIndexobj->startTrans();

        $BuCheapSuitSubobj = new BuCheapSuitSub();
        if (empty($id)) {  //插入
            $last_id = $this->_buCheapSuitIndexobj->insertGetId($data);
            $res     = $this->insertCommodiyData($sku_list, $last_id, 'add', $home);
            //提交

            $this->_buCheapSuitIndexobj->commit();
            if ($res) print_json(0, '保存成功');

        } else {    //修改
            $row = $this->_buCheapSuitIndexobj->getOneByPk($id);

            if ($row) {


                $this->_buCheapSuitIndexobj->where(['id' => $id])->update($data);

                $this->_buCheapSuitSubobj->where(['index_id' => $id])->delete();

                $res = $this->insertCommodiyData($sku_list, $id, 'update', $home);

                $this->_buCheapSuitIndexobj->commit();
                print_json(0, '保存成功');

            }

        }
        print_json(1, '保存失败');
    }

    public function insertCommodiyData($sku_list, $last_id, $action, $home)
    {
        dd($sku_list);
        if (empty($sku_list)) {
            return false;
        }
        $data = [];
//        $buCheapSuitSubObj = new BuCheapSuitSub();
        $buCheapSuitSubObj = new BuCheapSuitCommodity();
        foreach ($sku_list as $key => $val) {
            $sku_price = $val['sku_list'];
            foreach ($sku_price as $j => $item) {
                $data[] = array(
                    'index_id'         => $last_id,
                    'commodity_id'     => $val['commodity_id'],
                    'sku'              => $j,
                    'price'            => $item,
                    'commodity_set_id' => $val['commodity_set_id'],
                    'is_home'          => $home
                );
            }
        }
        $lastid = $buCheapSuitSubObj->insertAll($data);
        return $lastid;
    }

    public function insertCommodiyDataLive($sku_list, $last_id, $action, $home, $commodity_class = 1)
    {
        if (empty($sku_list)) {
            return false;
        }
        $data                 = [];
        $buCheapSuitCommodity = new BuCheapSuitCommodity();
        foreach ($sku_list as $key => $val) {
            $data[] = array(
                'suit_id'           => $last_id,
                'dd_commodity_type' => $val['dd_commodity_type'],
                'commodity_id'      => $val['commodity_id'],
                'sku_json'          => json_encode($val['sku_list']),
                'commodity_set_id'  => $val['commodity_set_id'],
                'commodity_class'   => $commodity_class,
            );
        }
        return $buCheapSuitCommodity->insertAll($data);
    }

    private function doHook($type = 'delete', $commodity_arr = [], $act_id = 0)
    {
        Hook::import(require ROOT_PATH . 'config/tags.php');
        $result = true;

        //查询act_id对应的活动，查询up_down_channel_dlr ，做出判断 shelves_type
        $activity = BuCheapSuitIndex::where('id', $act_id)->find();

        if (!empty($activity['up_down_channel_dlr'])) {

            $up_down_arr = explode(',', $activity['up_down_channel_dlr']);
            $shelves_ni  = array_intersect(DbDlr::$ni_arr, $up_down_arr);
            $shelves_pz  = array_intersect(DbDlr::$pz1a_arr, $up_down_arr);
            $shelves_qc  = array_intersect(DbDlr::$qc_arr, $up_down_arr);

            if (!empty($shelves_ni)) {
                $shelves_type = DbDlr::$ni_shelves;
            } else if (!empty($shelves_pz)) {
                $shelves_type = DbDlr::$pz1a_shelves;
            } elseif (!empty($shelves_qc)) {
                $shelves_type = DbDlr::$qc_shelves;
            } else {
                return false;
            }
            switch ($type) {
                case 'add':
                case 'update':
                    # 添加商品不需要刷活动信息
                    foreach ($commodity_arr as $item_one) {
                        $item_one['shelves_type'] = $shelves_type;
                        Hook::listen('flat_cheap', $item_one);
                    }
                    break;
                case 'delete':
                    # 删除就只需要将数据删除就ok
                    foreach ($commodity_arr as $item_one) {
                        $del_params = [
                            'shelves_type' => $shelves_type,
                            'del_dis'      => true,
                            'act_id'       => $act_id,
                            'commodity_id' => empty($item_one['commodity_id']) ? $item_one : $item_one['commodity_id']
                        ];
                        Hook::listen('flat_cheap', $del_params);
                    }
                    break;
            }

            $detail_param = ['key' => 'cache_prefix.commodity_detail', 'suffix' => '', 'set' => 'cache_prefix.commodity_detail_set'];
            Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $detail_param);

            return $result;
        }

        return false;
    }

    /**
     * 获取卡券
     */
    public function ajaxGetCard()
    {
        $this->_checkAjax();
        $model        = new DbCard();
        $card_name    = input('get.card_name');
        $set_type     = input('set_type', 0);
        $where        = [];
        $shelves_type = 5;
        if ($set_type == 5) {
            $where['up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], ['notlike', '%QCSM%'], ['notlike', '%QCAPP%'], 'and'];
        } elseif ($set_type == 6) {
            $shelves_type = 6;
            $where[]      = [['exp', 'FIND_IN_SET("PZ1ASM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",up_down_channel_dlr)'], 'or'];
        } elseif ($set_type == 7) {
            $shelves_type = 7;
            $where[]      = [['exp', 'FIND_IN_SET("QCSM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",up_down_channel_dlr)'], 'or'];
        }

        if (!empty($card_name))
            $where['card_name'] = ['like', '%' . $card_name . '%'];

        $where['shelves_type'] = $shelves_type;
        $where['is_enable']    = 1;
        $where['type']         = 2;
        $where['act_status']   = ['in', [1, 2, 3]];
        $params                = [
            'where'    => $where,
            'order'    => 'id desc',
            'field'    => 'id,is_enable,act_status,card_type,card_name,date_type,validity_date_start,validity_date_end,fixed_term,fixed_begin_term,(case dlr_code when "NISSAN" then "平台" else "自建" end) as belong_to,available_count,up_down_channel_name',
            'pagesize' => input('get.pagesize'),
            'query'    => input('get.')
        ];
        $list = $model->getListPaginate($params);
        $card_type_arr = $model->cardType();

        foreach ($list as $key=>$value){
            $list[$key]['id'] = (string)$value['id'];
            $list[$key]['card_type_name'] =  $card_type_arr[$value['card_type']] ?? '';

            switch ($value['date_type']){
                case 1:
                    $list[$key]['validity_date'] = $value['validity_date_start'] . '至' . $value['validity_date_end'];
                    break;
                case 2:
                    if ($value['fixed_begin_term'] == 0) {
                        $list[$key]['validity_date'] = "自领取当天有效，有效期" . $value['fixed_term'] . '天';
                    } elseif ($value['fixed_begin_term'] == 1) {
                        $list[$key]['validity_date'] = "领取后" . $value['fixed_term'] . '天后有效';
                    }
                    break;
                default:
                    break;
            }
            $value['status_name'] = empty($value['is_enable']) ? '已关闭' : $model->cardStatus()[$value['act_status']];
        }
        print_json(0, '', $list);
    }

}

