<?php

namespace app\admin_v2\controller;

use app\common\model\db\DbExports;

class Exports extends Common
{

    private $dbExport;

    private $path = 'http://47.115.87.190/';


    public function __construct()
    {
        parent::__construct();
        $this->dbExport = new DbExports();
        $this->path     = config('DOMAIN_URL');
    }

    public function index()
    {
        $export_key  = input('get.export_key');
        $export_type = input('get.export_type');

        $query            = [
            'export_key'  => $export_key,
            'export_type' => $export_type
        ];
        $export_type_list = [
            'order'            => '订单',
            'subscribe'        => '预约单',
            'delivery'         => '发货单',
            'after_order'      => '售后单',
            'coupon'           => '优惠券',
            'crowdfund'        => '众筹订单',
            'card_commodity'   => '优惠券商品',
            'goods'            => '商品e3s',
            'tax_invoice_type' => '开票税务分类',
            'e3s_set_meal'     => 'E3S套餐产品',
            'upload_result'    => '税务分类上传结果',
            'invoice_image'    => '发票下载',
            'invoice_excel'    => '开票记录',
            'new_media_goods'  => '新媒体商品',
            'new_media_dlr'    => '新媒体门店',
            'new_media_order'  => '新媒体订单',
        ];


        $key_arr = array_keys($export_type_list);
        $where   = ['export_type' => ['in', $key_arr]];
        if (!empty($export_key)) {
            $where['export_key'] = $export_key;
        }

        if (!empty($export_type)) {
            $where['export_type'] = $export_type;
        }

        $params = [
            'field' => '*',
            'where' => $where,
            'order' => 'created_date desc',
            'query' => input('get.')
        ];

        $list = $this->dbExport->getListPaginate($params);
        foreach ($list as $item) {
            $item['filter_params'] = str_replace(array_keys(DbExports::$type_column['order']), array_values
            (DbExports::$type_column['order']), $item['filter_params']);
        }

        $page = $list->render();
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('query', $query);
        $this->assign('path', $this->path);
        $this->assign('type', DbExports::$export_type);
        $this->assign('export_type_list', $export_type_list);

        return $this->fetch("exports/index");
    }


    public function download()
    {
        $export_id = input('export_id');
        $info      = $this->dbExport->where('id', $export_id)->field('export_status,file_address')->find();
        if ($info['export_status'] != 2) {
            print_json(1, '当前文件没有生成成功');
        }
        $filenameArr = explode('/', $info['file_address']);
        $filename    = end($filenameArr);
        download($info['file_address'], $filename);
    }


}
