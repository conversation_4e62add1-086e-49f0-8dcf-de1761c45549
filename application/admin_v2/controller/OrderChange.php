<?php


namespace app\admin_v2\controller;


use app\common\model\bu\BuOrder;

class OrderChange extends Common
{

    public function index()
    {
        if ($this->request->isAjax()) {
            $order_code     = trim(input('order_code'));
            $status         = trim(input('order_status',0));
            if(!$status){
                print_json(1, '请选择订单状态');
            }
            $order_model    = new BuOrder();
            $order_code_arr = explode(',', $order_code);
            // 判断是否有交易关闭的订单
            $map = ['order_code'=>['in', $order_code_arr], 'order_status'=>18];
            $close_order_code = $order_model->where($map)->column('order_code');
            $new_order_code = array_diff($order_code_arr,$close_order_code);
            if (empty($new_order_code)) {
                print_json(1, '交易关闭的订单不允许修改');
            }
            $upd            = ['order_status' => $status, 'modifier' => $this->admin_info['username'], 'last_updated_date' => date('Y-m-d H:i:s')];
            $re = $order_model->whereIn('order_code', $new_order_code)->update($upd);
            if ($re) {
                $str = implode(',',$close_order_code);
                print_json(0, '修改成功',['str'=>$str]);
            } else {
                print_json(1, '修改失败');
            }

        }
        $order_status = BuOrder::orderStatus();
        $this->assign('order_status', $order_status);
        return $this->fetch('index');

    }
}
