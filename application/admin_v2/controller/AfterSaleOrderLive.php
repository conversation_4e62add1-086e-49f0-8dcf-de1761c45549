<?php

/**
 * 人车生活售后订单
 * @author: zxtdcyy
 * @time: 2021-06-11
 */

namespace app\admin_v2\controller;

use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuOrderMoreCardPoint;
use app\common\model\bu\BuOrderSettlement;
use app\common\model\db\DbAfterSaleOrderCommodity;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbAfterSalePlatformAddresses;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbDlr;
use app\common\model\db\DbExports;
use app\common\model\db\DbJdWarehouse;
use app\common\model\db\DbSystemValue;
use app\common\model\db\DbUser;
use app\common\model\pss\PssInventoryInout;
use app\common\net_service\Inventory;
use app\common\net_service\JdWarehouse;
use app\common\net_service\NetGiftOrder;
use app\common\net_service\NetOrder;
use app\common\net_service\NetSupplier;
use app\common\port\connectors\JdCloudWarehouse;
use app\common\service\BusiplatNevService;
use think\Queue;
use think\Request;

/**
 * Class AfterSaleOrderLive
 * @package app\admin_v2\controller
 */
class AfterSaleOrderLive extends Common
{
    private $afs_model;
    private $afs_goods_model;
    private $order_model;
    private $settle;
    private $order_commodity;
    private $jd_ware_model;
    private $jd_finish_status = "finish";


    public function __construct(Request $request = null)
    {
        parent::__construct($request);

        $this->afs_model       = new DbAfterSaleOrders();
        $this->afs_goods_model = new DbAfterSaleOrderCommodity();
        $this->order_model     = new BuOrder();
        $this->settle          = new BuOrderSettlement();
        $this->order_commodity = new BuOrderCommodity();
        $this->jd_ware_model   = new DbJdWarehouse();
    }

    /**
     * #
     * @return mixed
     */
    public function index($dlrs = [], $brand = 1)
    {
        if (empty($dlrs)) $dlrs = DbDlr::$ni_arr;
        $buOrder            = new DbAfterSaleOrders();
        $order_id           = trim(input('get.order_id'));
        $goods_name         = trim(input('get.goods_name'));
        $user_phone         = input('get.user_phone');
        $user_name          = input('get.user_name');
        $afs_service_id     = input('get.afs_service_id');
        $afs_status         = input('get.afs_status');
        $afs_type           = input('get.afs_type');
        $sy_create_date     = date('Y-m-d', strtotime('-2day')) . ' ~ ' . date('Y-m-d');
        $created_date       = input('created_date',$sy_create_date);
        $refund_delivery_at = input('refund_delivery_at', '');
        $order_source       = input('get.order_source');
        $is_down            = input('get.is_down');
        $logistics_mode     = input('get.logistics_mode');
        $vin                = input('get.vin', '');
        $is_n               = intval(input('get.is_n'));
        $is_ajax_down       = input('get.is_ajax_down');
        $where              = [];
        if ($this->admin_info['type'] == 2) {
            $where['b.dd_dlr_code'] = $this->admin_info['dlr_code'];
        } else {
            $where['b.dlr_code'] = ['in', $dlrs];
        }
        if ($is_n >= 1) {
            $where['a.is_n'] = $is_n;
        }

        $query = [
            'is_n'               => $is_n,
            'order_id'           => $order_id,
            'afs_service_id'     => $afs_service_id,
            'user_phone'         => $user_phone,
            'user_name'          => $user_name,
            'afs_status'         => $afs_status,
            'afs_type'           => $afs_type,
            'created_date'       => $created_date,
            'refund_delivery_at' => $refund_delivery_at,
            'order_source'       => $order_source,
            'logistics_mode'     => $logistics_mode,
            'dlrs'               => $dlrs,
            'vin'                => $vin,
        ];

        if (!empty($order_id)) {
            $where['b.order_code'] = $order_id;
        }
        if (!empty($goods_name)) {
            $where['a.goods_name'] = ['like', "%{$goods_name}%"];
        }
        if (!empty($commodity_id)) {
            $where['a.commodity_id'] = $commodity_id;
        }
        if (!empty($afs_service_id)) {
            $where['a.afs_service_id'] = $afs_service_id;
        }
        if (!empty($afs_status)) {
            $where['a.afs_status'] = $afs_status;
        }
        if (!empty($afs_type)) {
            $where['a.afs_type'] = $afs_type;
        }
        if (!empty($user_name)) {
            $where['b.name'] = $user_name;
        }
        if (!empty($user_phone)) {
            $where['b.phone'] = $user_phone;
        }
        if (!empty($logistics_mode)) {
            $where['b.logistics_mode'] = $logistics_mode;
        }
        if (!empty($created_date)) {
            list($created_date_start, $created_date_end) = explode('~', $created_date);
            $where['a.created_date'] = ['between', [date('Y-m-d H:i:s', strtotime($created_date_start)), date('Y-m-d H:i:s', strtotime($created_date_end) + 86400)]];
        }
        if (!empty($refund_delivery_at)) {
            list($refund_delivery_at_start, $refund_delivery_at_end) = explode('~', $refund_delivery_at);
            $where['a.refund_delivery_at'] = ['between', [date('Y-m-d H:i:s', strtotime($refund_delivery_at_start)), date('Y-m-d H:i:s', strtotime($refund_delivery_at_end) + 86400)]];
        }
        if (!empty($order_source)) {
            $where['b.order_source'] = $order_source;
        }
        if (!empty($vin)) {
            $where[] = ['exp', "b.vin = '{$vin}' OR b.order_vin = '{$vin}'"];
        }
        $where['a.is_refund_order'] = 1;

        if (!$order_id && !$goods_name && !$afs_service_id && !$afs_status && !$afs_type && !$user_name && !$user_phone && !$logistics_mode && !$created_date && !$refund_delivery_at && !$order_source && !$is_down) {
            $where['a.created_date'] = ['between', [date('Y-m-d') . " 00:00:00", date('Y-m-d') . " 23:59:59"]];
        }

        $params = [
            'where' => $where,
            'query' => $query,
            'order' => 'a.id desc',
            'field' => "a.is_n,a.id,a.afs_service_id,a.settlement_ids,a.order_id,a.afs_type,a.afs_reason,a.afs_status,a.is_enable,a.refund_info,a.check_info,a.refund_delivery_at,
            a.refund_money,a.refund_points,a.pre_refund_money,a.pre_refund_points,b.order_code,a.back_afs_no,b.dd_dlr_code,b.dlr_code,b.name as user_name,a.platform_waybill_info,a.operate_list,
            b.phone as user_phone,b.cashier_trade_no,b.cashier_trade_no2,b.money,b.integral,b.pre_use_money,b.pre_point,a.created_date,b.channel,b.order_source,b.cashier_trade_no,a.user_waybill_info,
            b.logistics_mode,b.vin,b.order_vin,a.refund_goods,a.jd_warehouse_after_id,jdwr.jd_delivery_no",
        ];

        if ($is_ajax_down == 1) {
//            $params['field'] = "a.id,a.afs_service_id,b.cashier_trade_no,b.cashier_trade_no2,b.order_code,a.back_afs_no,a.settlement_ids,b.phone as user_phone,b.name as user_name,b.money,b.integral,a.afs_type,a.afs_reason,a.afs_status,a.check_info,a.platform_waybill_info,a.refund_info,b.logistics_mode,
//            a.refund_money,a.refund_points,f.refund_money,f.refund_points,f.pre_refund_money,f.pre_refund_points,a.created_date,b.channel,b.order_source,dc.cover_image,dc.commodity_name,oc.sku_info,oc.count,oc.price,dc.commodity_code,oc.third_sku_code sku_code,b.dd_dlr_code,b.dlr_code,a.user_waybill_info,b.pre_use_money,
//            b.pre_point,dc.comm_type_id,f.remark,b.vin,b.order_vin,a.operate_list,a.refund_delivery_at,jdwr.jd_delivery_no";
            $params['field'] = "a.id,a.afs_service_id,b.cashier_trade_no,b.cashier_trade_no2,b.order_code,a.back_afs_no,a.settlement_ids,b.phone as user_phone,b.name as user_name,b.money,b.integral,a.afs_type,a.afs_reason,a.afs_status,a.check_info,a.platform_waybill_info,a.refund_info,b.logistics_mode,
            a.refund_money,a.refund_points,a.pre_refund_money,a.pre_refund_points,a.created_date,b.channel,b.order_source,dc.cover_image,dc.commodity_name,oc.sku_info,oc.count,oc.price,dc.commodity_code,oc.third_sku_code sku_code,b.dd_dlr_code,b.dlr_code,a.user_waybill_info,b.pre_use_money,
            b.pre_point,dc.comm_type_id,a.remark,b.vin,b.order_vin,a.operate_list,a.refund_delivery_at,jdwr.jd_delivery_no";

            if (empty($created_date)) {
                print_json(1, '申请时间不能为空');
            }
            list($start_time, $end_time) = explode('~', $created_date);
            $days = date_diff(date_create($start_time), date_create($end_time))->days;
            if ($days > 31) {
                print_json(1, '申请时间请小于1个月');
            }
            if (!getRedisLock('after_order_is_ajax_down_after', 300)) {
                print_json(1, '限制5分钟一次');
            }

            $params_key = md5(json_encode($params));

            $export = DbExports::create([
                'export_type'   => 'after_order',
                'filter_params' => json_encode($params['where']),
                'export_key'    => $params_key,
                'creator'       => $this->admin_info['username']
            ]);

            Queue::push('app\admin_v2\queue\AfterOrderExport', json_encode([
                'params' => $params,
                'time'   => $created_date,
                'id'     => $export->id,
                'brand'  => 1, // 日产
            ]), config('queue_type.export'));
            print_json(0, '已加入异步下载列表');
        }
        if ($is_down) {
            $params['pagesize'] = 50000;

//            $params['field'] = "a.id,a.afs_service_id,b.cashier_trade_no,b.cashier_trade_no2,b.order_code,a.back_afs_no,a.settlement_ids,b.phone as user_phone,b.name as user_name,b.money,b.integral,a.afs_type,a.afs_reason,a.afs_status,a.check_info,a.platform_waybill_info,a.refund_info,b.logistics_mode,
//            a.refund_money,a.refund_points,f.refund_money,f.refund_points,f.pre_refund_money,f.pre_refund_points,a.created_date,b.channel,b.order_source,dc.cover_image,dc.commodity_name,oc.sku_info,oc.count,oc.price,dc.commodity_code,oc.third_sku_code sku_code,b.dd_dlr_code,b.dlr_code,a.user_waybill_info,b.pre_use_money,
//            b.pre_point,dc.comm_type_id,f.remark,b.vin,b.order_vin,a.operate_list,a.refund_goods,a.refund_delivery_at,a.jd_warehouse_after_id,oc.supplier,jdwr.jd_delivery_no";
            $params['field'] = "a.id,a.afs_service_id,b.cashier_trade_no,b.cashier_trade_no2,b.order_code,a.back_afs_no,a.settlement_ids,b.phone as user_phone,b.name as user_name,b.money,b.integral,a.afs_type,a.afs_reason,a.afs_status,a.check_info,a.platform_waybill_info,a.refund_info,b.logistics_mode,
            a.refund_money,a.refund_points,a.pre_refund_money,a.pre_refund_points,a.created_date,b.channel,b.order_source,dc.cover_image,dc.commodity_name,oc.sku_info,oc.count,oc.price,dc.commodity_code,oc.third_sku_code sku_code,b.dd_dlr_code,b.dlr_code,a.user_waybill_info,b.pre_use_money,
            b.pre_point,dc.comm_type_id,a.remark,b.vin,b.order_vin,a.operate_list,a.refund_goods,a.refund_delivery_at,a.jd_warehouse_after_id,oc.supplier,jdwr.jd_delivery_no";
            $list            = $buOrder->getOrderLimit($params);

        } else {
            $list = $buOrder->getLists($params);
        }


        $back_commodity = [];
        $inout_model    = new PssInventoryInout();
        foreach ($list as $key => &$val) {
            //订单表：money + integral/10 + pre_point/10 + pre_use_money
            $val['order_money'] =
                $val['money']
                + $val['integral'] / 10
                + $val['pre_point'] / 10
                + $val['pre_use_money'];
            $val['order_money'] = round($val['order_money'], 2);
//            9  => '退货待收',
//        12 => '换货待收',
            $val['go_in'] = 0;
            if (in_array($val['afs_status'], [9, 12]) && !$val['jd_warehouse_after_id']) {
                $val['go_in'] = 1;
            }


            $list[$key]    = $val;
            $have_pv_goods = 0;
            $operate_list  = json_decode($val['operate_list'], true);
            if (!empty($val['refund_goods'])) {
                $refund_goods = json_decode($val['refund_goods'], true);
                foreach ($refund_goods as $v) {
                    $tmp[$v['order_commodity_id']] = $v['count'];
                    if (isset($v['supplier']) && in_array($v['supplier'], BuOrderCommodity::$jd_warehouse_supplier)) {
                        $have_pv_goods = 1;
                    }
                }
                $val['refund_goods'] = $tmp ?? [];
            } elseif (!empty($operate_list['back_commodity'])) {
                $sku_ids = array_keys($operate_list['back_commodity']);
                unset($operate_list['back_commodity']);
                $refund_goods = (new BuOrderCommodity())->where(['order_code' => $val['order_code'], 'sku_id' => ['in', $sku_ids]])->column('count', 'id');
                $buOrder->saveData(['operate_list' => json_encode_cn($operate_list), 'refund_goods' => json_encode_cn($refund_goods)], ['id' => $val['id']]);
                $val['refund_goods'] = $refund_goods;
            }
            if ($val['go_in'] == 0 || !$have_pv_goods) {
                $val['go_in'] = 0;
            }
            $back_commodity[$val['order_code']] = $val['refund_goods'];
            if (empty($val['refund_goods'])) {
                $refund_goods                       = (new BuOrderCommodity())->where(['order_code' => $val['order_code']])->column('count', 'id');
                $back_commodity[$val['order_code']] = $refund_goods;
            }
            //新加判断有没有库存流水 start
            $order_commodity_id   = (new BuOrderCommodity())->where(['order_code' => $val['order_code']])->column('id');
            $inout_count          = $inout_model->where(['order_code' => $val['order_code'], 'order_commodity_id' => array('in', $order_commodity_id), 'inout' => 2])->count();
            $val['inout_count']   = $inout_count ?? 0;
            $val['have_pv_goods'] = $have_pv_goods;
            //新加判断有没有库存流水 start


            // 处理退货单号和发货单号
            $jd_back_no = '';
            if ($val['after_order_commodities']) {
                $jd_delivery_no = [];
                foreach ($val['after_order_commodities'] as $item) {
                    $jd_delivery_no[] = $item['after_jd_warehouse']['jd_delivery_no'] ?? '';
                }
                $jd_back_no = implode(',', $jd_delivery_no);
            }
            $val['jd_back_no']        = $jd_back_no;
            $val['user_waybill_info'] = empty($val['user_waybill_info']) ? '' : json_decode($val['user_waybill_info'], true);
        }
        if ($is_down) {
            if (!empty($list)) {
                $order_source_list = BuOrder::orderSource();
                foreach ($list as &$list_item) {

                    // 组合商品
                    $orderCommodities = $list_item->orderCommodities;
                    $moCommodityName  = '';
                    foreach ($orderCommodities as $value) {
                        if ($value['mo_id'] != 0) {
                            $moCommodityName = $value['commodity_name'];
                        }
                    }
                    $list_item['mo_commodity_name'] = $moCommodityName;

                    $user_waybill_info                = empty($list_item['user_waybill_info']) ? '' : $list_item['user_waybill_info'];
                    $list_item['channel']             = BuOrder::$channel[$list_item['channel']];
                    $list_item['afs_status']          = DbAfterSaleOrders::$after_sale_status[$list_item['afs_status']] ?? '';
                    $list_item['afs_type']            = DbAfterSaleOrders::$afs_type[$list_item['afs_type']] ?? '';
                    $list_item['order_source']        = $order_source_list[$list_item['order_source']] ?? '';
                    $list_item['user_waybill_info']   = empty($user_waybill_info) ? '' : (($user_waybill_info['company_name'] ?? '') . ' ' . ($user_waybill_info['number'] ?? ''));
                    $list_item['money']               = $list_item['money'] + floatval($list_item['pre_use_money']);
                    $list_item['integral']            = $list_item['integral'] + intval($list_item['pre_point']);
                    $list_item['refund_money']        = $list_item['refund_money'] + floatval($list_item['pre_refund_money']);
                    $list_item['refund_points']       = $list_item['refund_points'] + intval($list_item['pre_refund_points']);
                    $list_item['check_name']          = '';
                    $list_item['check_time']          = '';
                    $list_item['info_name']           = '';
                    $list_item['info_time']           = '';
                    $list_item['logistics_mode_name'] = $list_item['logistics_mode'] == 1 ? '自提' : '快递';
                    $list_item['cashier_trade_no']    = $list_item['cashier_trade_no'] . ' ' . $list_item['cashier_trade_no2'];

                    $commodity_type_key = 'tmp_type_key_' . $list_item['comm_type_id'];
                    $commodity_type     = redis($commodity_type_key);

                    if (empty($commodity_type)) {
                        $commodity_type = (new DbCommodityType())->getSubParent($list_item['comm_type_id']);
                        redis($commodity_type_key, $commodity_type, 120);
                    }

                    if (empty($commodity_type)) {
                        $level_name = '';
                    } else {
                        $level_name = $commodity_type->pparent_type_name;
                    }

                    $list_item['level_name'] = $level_name;

                    if (!empty($list_item['check_info'])) {
                        $check_arr               = json_decode($list_item['check_info'], true) ?? [];
                        $list_item['check_name'] = $check_arr['name'] ?? '';
                        $list_item['check_time'] = $check_arr['time'] ?? '';
                    }

                    if (!empty($list_item['platform_waybill_info'])) {
                        $platform_waybill_info  = json_decode($list_item['platform_waybill_info'], true) ?? [];
                        $list_item['info_name'] = $platform_waybill_info['name'] ?? '';
                        $list_item['info_time'] = $platform_waybill_info['time'] ?? '';
                    } else {
                        $refund_info            = json_decode($list_item['refund_info'], true) ?? [];
                        $list_item['info_name'] = $refund_info['name'] ?? '';
//                        $list_item['info_time'] = $refund_info['time'] ?? '';
                    }
                }
            }

            $fiels  = 'id,afs_service_id,order_code,settlement_ids,cashier_trade_no,user_phone,user_name,vin,order_vin,order_money,integral,afs_type,afs_reason,afs_status,refund_money,refund_points,user_waybill_info,jd_back_no,jd_delivery_no,created_date,channel,order_source,cover_image,mo_commodity_name,commodity_name,level_name,sku_info,count,price,commodity_code,sku_code,dd_dlr_code,check_name,check_time,info_name,info_time,logistics_mode_name,remark';
            $titles = '序号,售后单号,订单编号,结算单,支付单号,会员手机,会员姓名,vin,下单vin,订单金额,厂家积分,售后类型,售后原因,售后状态,退款金额,退积分,回寄物流,退货入库单,换货出库单,申请时间,渠道来源,订单来源,商品图片,组合商品名称,商品名称,商品一级分类,商品信息,数量,单价,商品编码,规格编码,专营店,审核人,审核时间,退款or发货人,退款or发货时间,物流方式,备注';

            export_excel($list, $fiels, $titles, 'Ni+商城售后订单商品信息' . date('Y-m-d H:i:s'));
        } else {

            $order_source_list = BuOrder::orderSource();

            $DbSystemValueObj = new DbSystemValue();
            $valuelist        = $DbSystemValueObj->where("value_type = 13")->select();
            $jd_ware_list     = $DbSystemValueObj->where("value_type = 29")->select();
            $sys              = DbSystemValue::where('value_type', 20)->column('value_code');
            $extra            = ['PV'];
            $inout_type       = $DbSystemValueObj->where("value_type = 27")->field('county_name,value_code')->select();
            $this->assign('valuelist', $valuelist);
            $this->assign('jd_ware_list', $jd_ware_list);
            $page = $list->render();
            $this->assign('list', $list);
            $this->assign('page', $page);
            $this->assign('role', $this->admin_info['type']);
            $this->assign('query', $query);
            $this->assign('comm_type_list', $this->_commodityTypeList());
            $this->assign('order_source_list', $order_source_list);
            $this->assign('after_sale_status', DbAfterSaleOrders::$after_sale_status);
            $this->assign('channel', BuOrder::$channel);
            $this->assign('dlr_info', array_merge($sys, $extra));
            $this->assign('back_commodity', json_encode($back_commodity));
            $this->assign('inout_type', $inout_type);
            $this->assign('brand', $brand);
            return $this->fetch('after_sale_order_live/index');
        }

    }

    public function show()
    {
        $afs_id              = input('id', '');
        $type                = input('type', 'show');
        $brand               = input('brand', 1); // 品牌1日产2pz3启辰
        $afsData             = $this->afs_model->getOneByPk($afs_id);
        $orderData           = $this->order_model->getOne(['where' => ['id' => $afsData['order_id']]]);
        $afsData['show_msg'] = '';
        if ($orderData['promotion_source'] == 2) {
            $afsData['show_msg'] = '此单为优惠套装退款，将会退套装订单所有子单商品和金额';
        } elseif ($orderData['promotion_source'] == 3) {
            $afsData['show_msg'] = '此单为臻享服务包退款，将会退臻享服务包订单所有子单商品和金额';
        }

        $orderData['can_not_all'] = 0; // 可部分退
//        if (in_array($orderData['promotion_source'], [2, 3])) {
//            $orderData['can_not_all'] = 1; // 服务包不可部分退
//        }
        // 判断售后入库单
        $afterSaleOrderModel = new DbAfterSaleOrderCommodity();

        $map = ['afs_id' => $afs_id, 'jd_warehouse_after_id' => ['neq', 0]];

        $jd_warehouse_after_id_arr = $afterSaleOrderModel->where($map)->column('jd_warehouse_after_id');
        if (!empty($jd_warehouse_after_id_arr)) {
            $jd_ware_list = $this->jd_ware_model->getList(['where' => ['id' => ['in', $jd_warehouse_after_id_arr]]]);
            // 判断JD订单是否已完成
            foreach ($jd_ware_list as $key => $item) {
                if (!in_array($item['jd_status'], [200, 400])) {
                    $result = JdCloudWarehouse::create('jd_cloud_warehouse')->queryRtwOrderDetail($item['id']);
                    if ($result['code'] == 1000) {
                        $upd = ['jd_status' => $result['data']['status']];
                        $this->jd_ware_model->where('id', $item['id'])->update($upd);
                        $jd_ware_list[$key]['jd_status'] = $result['data']['status'];
                    }
                }
            }
            $afsData['jd_ware_list'] = $jd_ware_list;
        }
        if (!empty($afsData['jd_warehouse_send_id'])) {
            $jd_ware_send_info            = $this->jd_ware_model->getOne(['where' => ['id' => $afsData['jd_warehouse_send_id']]]);
            $afsData['jd_ware_send_info'] = $jd_ware_send_info;

        }

        $address          = new DbAfterSalePlatformAddresses();
        $address_platform = $address->getList(['where' => ['is_enable' => 1], 'field' => 'id,recipients,phone,address', 'order' => 'id desc']);
        $settle           = $this->settle->getList(['where' => ['is_enable' => 1, 'order_code' => $orderData['order_code']]]);

        if (!empty($settle)) {
            foreach ($settle as $st_item) {
                $pre_info = (new BuOrder())->getOne([
                    'where' => ['id' => $afsData['order_id'], 'cashier_trade_no2' => $st_item['cashier_trade_no']]
                ]);
                if (!empty($pre_info)) {
                    #是预售
                    $st_item['point']        = intval($pre_info['pre_point']) ?? 0;
                    $st_item['refund_money'] = $afsData['pre_refund_money'];
                    $st_item['refund_point'] = $afsData['pre_refund_points'];
                } else {
                    $normal_info = (new BuOrder())->getOne([
                        'where' => ['id' => $afsData['order_id'], 'cashier_trade_no' => $st_item['cashier_trade_no']]
                    ]);
                    #普通订单
                    $st_item['point']        = $normal_info['integral'] ?? 0;
                    $st_item['refund_money'] = $afsData['refund_money'];
                    $st_item['refund_point'] = $afsData['refund_points'];
                }
            }
        }


        $buOrder = new BuOrder();
        $params  = [
            'field' => 'c.id as commodity_id,c.create_dlr_code as dlr_code,c.commodity_name,c.commodity_code,b.order_code,b.car_info,b.sku_id,b.sku_info,b.price,b.count,b.commodity_pic as cover_image,b.third_sku_code sku_code,b.id,b.delivery_coefficient,b.supplier',
            'where' => [in_array($orderData['promotion_source'], [2, 3]) ? 'a.parent_order_code' : 'a.order_code' => $orderData['order_code']]
        ];
        $detail  = $buOrder->getOrderCommodityInfo($params);

        $afs_goods_model = new DbAfterSaleOrderCommodity();

        $field          = 'order_commodity_id,after_count,in_ware_count,jd_in_ware_count,jd_warehouse_after_id';
        $afs_goods_list = $afs_goods_model->getList(['where' => ['afs_id' => $afs_id, 'is_enable' => 1], 'field' => $field]);
        $in_ware_arr    = [];
        if ($afs_goods_list) {
            foreach ($afs_goods_list as $v) {
                $in_ware_arr[$v['order_commodity_id']] = $v;
            }
        }


        $dbUser = new DbUser();
        $user   = $dbUser->getOne(['where' => ['id' => $orderData['user_id']]]);

        $order_list       = [];
        $refund_goods     = json_decode($afsData['refund_goods'], true) ?? [];
        $refund_goods_msg = json_decode($afsData['refund_goods_msg'], true) ?? [];
        foreach ($detail as $k => $v) {
            $v['back_num']         = 0;
            $v['refund_goods_msg'] = '';
            if (isset($in_ware_arr[$v['id']])) {
                $detail[$k]['jd_in_ware_count'] = $in_ware_arr[$v['id']]['jd_in_ware_count'];
            } else {
                $detail[$k]['jd_in_ware_count'] = 0;

            }
            foreach ($refund_goods as $val) {
                if ($val['order_commodity_id'] == $v['id']) $v['back_num'] = $val['count'];
            }
            foreach ($refund_goods_msg as $val) {
                if ($val['order_commodity_id'] == $v['id']) {
                    $v['refund_goods_msg'] = '实际退：';
                    if (!empty($val['pre_refund_money'])) $v['refund_goods_msg'] .= '预售金额 ' . $val['pre_refund_money'] . '元；';
                    if (!empty($val['pre_refund_points'])) $v['refund_goods_msg'] .= '预售积分 ' . $val['pre_refund_points'] . '；';
                    if (!empty($val['refund_money'])) $v['refund_goods_msg'] .= '金额 ' . $val['refund_money'] . ' 元';
                    if (!empty($val['refund_points'])) $v['refund_goods_msg'] .= '积分 ' . $val['refund_points'] . '；';
                }
            }
            $order_list[$v['order_code']][] = $v;
        }

        $this->assign('order_info', $orderData);
        $this->assign('address_platform', $address_platform);
        $this->assign('afs_info', $afsData);
        $this->assign('type', $type);
        $this->assign('settle', $settle);
        $this->assign('detail', $detail);
        $this->assign('user', $user);
        $this->assign('order_list', $order_list);
        $this->assign('brand', $brand);

        if ($type == 'show') {
            //只显示信息,不进行编辑
//            dd(collection($detail)->toArray());
            return $this->fetch('after_sale_order_live/show');
        } else {
            //审查
            return $this->fetch('after_sale_order_live/edit');
        }
    }

    /**
     * 各种审核操作
     */
    public function save()
    {
        $post = input('post.');
//        print_json($post);
        $id           = input('id');
        $afs_type     = input('afs_type');
        $afs_check    = input('afs_check');#是否通过
        $check_remark = input('check_remark', '');#审核建议
        $remark       = input('remark', '');#备注

        $store_address          = input('store_address', false);#是否保存为常用收件信息
        $recipients             = input('recipients', '');#
        $phone                  = input('phone', '');#退款积分
        $address                = input('address', '');#退款积分
        $post_data              = input('post.');
        $order_commodity_id_arr = $post_data['order_commodity_id'] ?? [];
        $back_num               = $post_data['back_num'] ?? [];
        $supplier               = $post_data['supplier'] ?? [];
        if ($post_data['is_all_ref'] == 0 && (empty($order_commodity_id_arr))) {
            print_json(1, '部分退至少需选择一个商品');
        }
        // 订单商品id
        $order_commodity_id_arr = array_keys($order_commodity_id_arr);

        $afs_data   = $this->afs_model->getOneByPk($id);
        $order_data = $this->order_model->getOneByPk($afs_data['order_id']);

        if (empty($afs_data) || empty($order_data)) {
            print_json(1, '数据错误');
        }

        if (($afs_type == DbAfterSaleOrders::ONLY_REFUND) && $afs_data['afs_type'] == $afs_type) {
            //仅退款
            $afs_status = empty($afs_check) ? DbAfterSaleOrders::$afss_type['only_refund'][3] :
                DbAfterSaleOrders::$afss_type['only_refund'][4];#退款拒绝,退款中
        } else if (($afs_type == DbAfterSaleOrders::BACK_PRO) && $afs_data['afs_type'] == $afs_type) {
            //退货
            $afs_status = empty($afs_check) ? DbAfterSaleOrders::$afss_type['back_pro'][8] :
                DbAfterSaleOrders::$afss_type['back_pro'][9];#退货拒绝,退货待收
        } else {
            //换货
            $afs_status = empty($afs_check) ? DbAfterSaleOrders::$afss_type['chg_pro'][11] :
                DbAfterSaleOrders::$afss_type['chg_pro'][12];#退货拒绝,退货待收
        }

        $operate_list = json_decode($afs_data['operate_list'], true) ?? [];
        $check_info   = [
            'name' => $this->admin_info['username'] ?? "",
            'desc' => '审核操作',
            'time' => date("Y-m-d H:i:s")
        ];

        array_push($operate_list, array_merge(['afs_status' => $afs_status], $check_info));

        $platform_info = ($afs_status == DbAfterSaleOrders::$afss_type['back_pro'][9] || $afs_status ==
            DbAfterSaleOrders::$afss_type['chg_pro'][12]) ? json_encode_cn([
            'recipients' => $recipients,
            'phone'      => $phone,
            'address'    => $address,
        ]) : '';


        $refund_goods = [];
        if ($post_data['is_all_ref'] == 0) {
            // 部分退
            foreach ($back_num as $k => $v) {
                // 选则复选框的
                if (in_array($k, $order_commodity_id_arr)) {
                    $tmp            = [
                        'order_commodity_id' => $k,
                        'count'              => $v,
                        'supplier'           => $supplier[$k],
                    ];
                    $refund_goods[] = $tmp;
                }
            }
        } else {
            // 全退
            $order_commodity_model = new BuOrderCommodity();
            $field                 = 'id as order_commodity_id,count,supplier';
            $refund_goods          = $order_commodity_model->where('order_code', $order_data['order_code'])->field($field)->select();

        }
        if ($refund_goods) {
            $afs_goods_data = [];
            foreach ($refund_goods as $v) {
                $afs_goods_data[] = [
                    'afs_id'             => $id,
                    'order_commodity_id' => $v['order_commodity_id'],
                    'after_count'        => $v['count'],
                ];
            }
            $this->afs_goods_model->insertAll($afs_goods_data);
        }
        $refund_goods = !empty($refund_goods) ? json_encode_cn($refund_goods) : '';
        $infos        = [
            'afs_status'    => $afs_status,
            'check_remark'  => $check_remark,
            'remark'        => $remark,
            'platform_info' => $platform_info,
            'operate_list'  => json_encode_cn($operate_list),
            'refund_goods'  => $refund_goods,
            'is_all_refund' => $post_data['is_all_ref'],
            'check_info'    => json_encode_cn($check_info),
        ];

        $res = $this->afs_model->saveData($infos, ['id' => $id]);


        if (in_array($order_data['promotion_source'], [2, 3])) {
            $order_commodity_list = (new BuOrderCommodity())
                ->alias('a')
                ->join('t_bu_order b', 'a.order_code=b.order_code')
                ->where(['a.parent_order_code' => $order_data['parent_order_code'], 'parent_order_type' => ['neq', 3]])
                ->column('a.id, b.id order_id', 'a.id');
            $refund_goods_sub     = [];
            foreach ($order_commodity_list as $k => $v) {
                if (!empty($back_num[$k])) {
                    $refund_goods_sub[$v][] = [
                        'order_commodity_id' => $k,
                        'count'              => $back_num[$k],
                    ];
                }
            }
            foreach ($refund_goods_sub as $k => $v) {
                $infos['refund_goods'] = !empty($v) ? json_encode_cn($v) : '';
                $afs_sub               = $this->afs_model->getOne(['where' => ['order_id' => $k], 'order' => 'id desc']);
                $res                   = $this->afs_model->saveData($infos, ['id' => $afs_sub['id']]);
                $this->inQueue($afs_sub['id']);
            }
        }

        if (empty($afs_check)) {
            $net_order = new NetOrder();
            $net_order->cardAfterSale($order_data, 2);
        }
        //审核通过的话，提交售后到供应商系统
        if(!empty($afs_check) && $order_data['logistics_mode'] == 2 && $afs_type != DbAfterSaleOrders::ONLY_REFUND){
            $netsupplier = new NetSupplier();
            $netsupplier->sendAfterSaleToSupplier($id);
        }

        if (!empty($store_address)) {
            DbAfterSalePlatformAddresses::create([
                'recipients' => $recipients,
                'phone'      => $phone,
                'address'    => $address,
                'creator'    => $this->admin_info['username']
            ]);
        }

        if ($res) {
            $this->inQueue($id);
            print_json(0, 'ok');
        } else {
            print_json(1, '查询数据失败,请刷新');
        }
    }

    public function afsStatus()
    {
        $id         = input('id');
        $afs_status = input('afs_status');

        $afs_data  = $this->afs_model->getOneByPk($id);
        $status_wt = [DbAfterSaleOrders::$afss_type['back_pro'][9], DbAfterSaleOrders::$afss_type['chg_pro'][12]];
        if (!empty($afs_data) && in_array($afs_data['afs_status'], $status_wt)) {
            if ($afs_data['afs_status'] == DbAfterSaleOrders::$afss_type['chg_pro'][12]) {
                $afs_status = DbAfterSaleOrders::$afss_type['chg_pro'][13];
            }
            $recipients_info = json_encode_cn(['name' => $this->admin_info['username'], 'time' => date("Y-m-d H:i:s")]);

            $operate_list = json_decode($afs_data['operate_list'], true);
            array_push($operate_list, [
                'afs_status' => $afs_status,
                'name'       => $this->admin_info['username'] ?? "",
                'desc'       => '后台操作',
                'time'       => date("Y-m-d H:i:s")
            ]);

            $this->afs_model->saveData([
                'afs_status'      => $afs_status,
                'recipients_info' => $recipients_info,
                'modifier'        => $this->admin_info['username'],
                'operate_list'    => json_encode_cn($operate_list)
            ], ['id' => $id]);

            $this->inQueue($id);
            print_json(0, 'ok');
        }
        print_json(1, '状态错误');
    }

    public function send()
    {
        $post_data    = input('post.');
        $id           = input('id');
        $order_id     = input('order_id');
        $company      = input('company');
        $number       = input('number');
        $company_name = input('company_name');
        $save_type    = input('save_type', 0); // 1-JD云仓
        $admin_name   = $this->admin_info['username'] ?? 'system';

        if (empty($id) || empty($order_id) || empty($company) || ($save_type == 0 && empty($number))) {
            print_json(1, '请填入有效信息');
        }
        $afs_data = $this->afs_model->getOneByPk($id);

        if (empty($afs_data) || $afs_data['afs_status'] != DbAfterSaleOrders::$afss_type['chg_pro'][12]) {
            print_json(1, '售后信息有误');
        }

        $waybill_type = 1; // 物流类型 1:普通物流  2:JD云仓物流
        if ($save_type == 1) {
            $waybill_type = 2;
        }
        $platform_waybill_info = [
            'company'      => $company,
            'company_name' => $company_name,
            'number'       => $number ?? '',
            'waybill_type' => $waybill_type,
            'name'         => $this->admin_info['username'],
            'time'         => date("Y-m-d H:i:s"),
        ];

        $time_now     = date("Y-m-d H:i:s");
        $operate_list = json_decode($afs_data['operate_list'], true);
        array_push($operate_list, [
            'afs_status' => DbAfterSaleOrders::$afss_type['chg_pro'][13],
            'name'       => $this->admin_info['username'] ?? "",
            'desc'       => '发货操作',
            'time'       => $time_now,
        ]);

        $this->afs_model->saveData([
            'afs_status'            => DbAfterSaleOrders::$afss_type['chg_pro'][13],
            'platform_waybill_info' => json_encode_cn($platform_waybill_info),
            'refund_delivery_at'    => $time_now,
            'modifier'              => $this->admin_info['username'],
            'operate_list'          => json_encode_cn($operate_list)
        ], ['id' => $id]);


        // 判断是否是JD云仓发货
        if ($save_type == 1) {
            // 入库数量不为空
            if (!empty($post_data['jd_in_ware_count'])) {

                $afterSaleOrderCommodityModel = new DbAfterSaleOrderCommodity();

                // 进销存入库
                $aft_order_commodities = $afterSaleOrderCommodityModel->alias('a')
                    ->join('t_bu_order_commodity b', 'a.order_commodity_id=b.id')
                    ->field('a.in_ware_count,a.order_commodity_id,b.third_sku_code')
                    ->where('afs_id', $id)
                    ->select();
                foreach ($aft_order_commodities as $key => $item) {
                    if ($item['in_ware_count'] > 0) {
                        $queue_data = [
                            'type'               => 2,
                            'sku_code'           => $item['third_sku_code'],
                            'count'              => $item['in_ware_count'], // 入库数量 查表
                            'sale_no'            => $afs_data['afs_service_id'],
                            'afs_type'           => 'in02', // 退款重新入库
                            'user_name'          => $admin_name,
                            'order_commodity_id' => $item['order_commodity_id']
                        ];
                        Queue::push('app\common\queue\InventoryStock', json_encode($queue_data), config('queue_type.default'));
                    }
                }

                foreach ($post_data['jd_in_ware_count'] as $key => $val) {
                    // 入库数量不为0
                    if ($val == 0) {
                        // 删除为空的数据
                        unset($post_data['jd_in_ware_count'][$key]);
                        continue;
                    }
                    // 进销存出库
                    $queue_data = [
                        'type'               => 4, // 直接出库
                        'order_commodity_id' => $key,
                        'count'              => $val,
                    ];
                    Queue::push('app\common\queue\InventoryStock', json_encode($queue_data), config('queue_type.default'));

                }
                if (!empty($post_data['jd_in_ware_count'])) {
                    $service = new JdWarehouse();
                    $result  = $service->create($post_data['order_code'], $post_data['goods_id'], $post_data['company'], $post_data['jd_in_ware_count'], $admin_name);
                    // 更新售后商品表的发货id
                    if ($result['code'] == 200) {
                        $jd_warehouse_id = $result['data']['jd_warehouse_id'];

                        $upd = ['jd_warehouse_send_id' => $jd_warehouse_id, 'last_updated_date' => date('Y-m-d H:i:s')];
                        $afterSaleOrderCommodityModel->where('afs_id', $id)->update($upd);
                        // 售后表
                        $this->afs_model->where('id', $id)->update($upd);
                    } else {
                        // 售后状态回退
                        $this->afs_model->saveData([
                            'afs_status'            => DbAfterSaleOrders::$afss_type['chg_pro'][12],
                        ], ['id' => $id]);
                        print_json(1, $result['msg']);
                    }
                }
            }

        }

        $this->inQueue($id);
        print_json(0, 'ok');
    }

    public function refund()
    {
        $post = input('post.');
        $id               = input('id');
        $order_id         = input('order_id');
        $cashier_trade_no = input('cashier_trade_no');

        $refund_remark = input('refund_remark');
        $save_type     = input('save_type', 1);//1退款，2入库

        $post_data         = input('post.');
        $pre_refund_money  = 0;
        $pre_refund_points = 0;

        $refund_money  = 0;
        $refund_points = 0;

        $back_money       = $post_data['back_money'] ?? [];
        $back_point       = $post_data['back_point'] ?? [];
        $pre_back_money   = $post_data['pre_back_money'] ?? [];
        $pre_back_point   = $post_data['pre_back_point'] ?? [];
        $back_num         = $post_data['back_num'] ?? [];
        $stock_num        = $post_data['stock_num'] ?? [];
        $refund_goods_msg = [];
        $tmp              = [];
        foreach ($back_money as $k => $v) {
            $tmp[$k]['refund_money'] = $v;
            $refund_money            = number_format($refund_money + floatval($v), 2, '.', '');
        }
        foreach ($back_point as $k => $v) {
            $tmp[$k]['refund_points'] = $v;
            $refund_points            = number_format($refund_points + intval($v), 2, '.', '');
        }
        foreach ($pre_back_money as $k => $v) {
            $tmp[$k]['pre_refund_money'] = $v;
            $pre_refund_money            = number_format($pre_refund_money + floatval($v), 2, '.', '');
        }
        foreach ($pre_back_point as $k => $v) {
            $tmp[$k]['pre_refund_points'] = $v;
            $pre_refund_points            = number_format($pre_refund_points + intval($v), 2, '.', '');
        }
        foreach ($back_num as $k => $v) {
            $tmp[$k]['count'] = $v;
        }
        // 入库数量
        foreach ($stock_num as $k => $v) {
            $tmp[$k]['stock_num'] = $v;
        }

        foreach ($tmp as $k => $v) {
            $refund_goods_msg[] = array_merge(['order_commodity_id' => $k], $v);
        }
        $not_refund_points = input('not_refund_points', 0);
        if ($not_refund_points == 1) {
            $pre_refund_points = $refund_points = 0;
        }

        $order_data = $this->order_model->getOneByPk($order_id);
        $afs_data   = $this->afs_model->getOneByPk($id);
        if (empty($afs_data) || empty($order_data)) {
            print_json(1, '数据信息有误');
        }

        if (($refund_points > ($order_data['integral'] ?? 0)) || ($refund_money > $order_data['money'])) {
            print_json(1, '退款信息有误');
        }

        if (empty($order_data['cashier_trade_no']) && ($order_data['cashier_trade_no'] != $cashier_trade_no)) {
            print_json(1, '数据信息有误2');
        }

        if (!empty($pre_refund_points) && $pre_refund_points != $order_data['pre_point']) {
            print_json(1, '预售定金积分不可部分退');
        }

        // 判断赠品券订单
        $net_gift = new NetGiftOrder();
        $re = $net_gift->giftOrderAfterSale($order_data);
        if ($re['code'] <> 200) {
            print_json(1, $re['msg']);
        }



        $time_now    = date("Y-m-d H:i:s");
        $refund_info = [
            'name' => $this->admin_info['username'],
            'time' => $time_now,
        ];
        // 入库提交了是否退库存，退款就不会有
        if (isset($post_data['is_refund_stock'])) {
            $afs_type                       = $post_data['afs_type'];
            $is_refund_stock                = $post_data['is_refund_stock'];
            $refund_info['is_refund_stock'] = $is_refund_stock; // 是否退库存
            $refund_info['afs_type']        = $afs_type; // 入库类型
        } else {
            $is_refund_stock = json_decode($afs_data['refund_info'], true)['is_refund_stock'] ?? '';
            $afs_type        = json_decode($afs_data['refund_info'], true)['afs_type'] ?? '';

            $refund_info['is_refund_stock'] = $is_refund_stock; // 是否退库存
            $refund_info['afs_type']        = $afs_type; // 入库类型
        }


        //判断是否退库存 已经入库过了就不在退库存
        if ($is_refund_stock == 1 && $save_type == 1) {
            //处理退库存操作
            $stock_num = $post_data['stock_num'] ?? [];
            if (!empty($stock_num)) {
                foreach ($stock_num as $key => $value) {
                    $sku_code                   = $this->order_commodity->where(['id' => $key])->column('third_sku_code');
                    $queue_data                 = [
                        'type'               => 2,
                        'sku_code'           => $sku_code[0],
                        'count'              => $value,
                        'sale_no'            => $afs_data['afs_service_id'],
                        'afs_type'           => $afs_type,
                        'order_commodity_id' => $key,
                        'user_name'          => $this->admin_info['username'] ?? 'system'
                    ];
                    Queue::push('app\common\queue\InventoryStock', json_encode($queue_data), config('queue_type.default'));
                }

            }
        }

        $is_all_refund = 0;
        if (
            ($pre_refund_money == $order_data['pre_use_money']) &&
            ($pre_refund_points == $order_data['pre_point']) &&
            ($refund_money == $order_data['money']) &&
            ($refund_points == $order_data['integral'])
        ) {
            $is_all_refund = 1;
        }
        $order_commodity_model = new BuOrderCommodity();

        //插入售后商品表
        foreach ($refund_goods_msg as $k => $v) {
            $afs_goods_data = [
                'afs_id'             => $id,
                'order_commodity_id' => $v['order_commodity_id'],
                'after_count'        => $v['count'],
                'return_money'       => $v['refund_money'],
                'return_point'       => $v['refund_points'],
            ];
            if (isset($v['stock_num'])) {
                $afs_goods_data['in_ware_count']    = $v['stock_num'];
                $afs_goods_data['jd_in_ware_count'] = $v['stock_num'];
            }
            $afs_goods_where = ['afs_id' => $id, 'order_commodity_id' => $v['order_commodity_id']];
            $afs_goods       = $this->afs_goods_model->where($afs_goods_where)->find();
            if ($afs_goods) {
                $afs_goods_data['last_updated_date'] = date('Y-m-d H:i:s');
                $this->afs_goods_model->where($afs_goods_where)->update($afs_goods_data);
            } else {
                $this->afs_goods_model->insertData($afs_goods_data);
            }
        }

        // 重新计算订单商品表的售后累计数据，避免重复提交导致金额翻倍
        if ($save_type == 1) {
            // 先获取当前售后单涉及的所有订单商品ID
            $order_commodity_ids = array_column($refund_goods_msg, 'order_commodity_id');
            $order_commodity_ids = array_unique($order_commodity_ids);

            foreach ($order_commodity_ids as $order_commodity_id) {
                // 根据afs_goods表重新计算该订单商品的累计售后数据
                $afs_summary = $this->afs_goods_model
                    ->where(['order_commodity_id' => $order_commodity_id, 'is_enable' => 1])
                    ->field('SUM(after_count) as total_after_count, SUM(return_money) as total_return_money, SUM(return_point) as total_return_point')
                    ->find();

                // 更新订单商品表的累计数据
                $order_commodity_model->where('id', $order_commodity_id)->update([
                    'after_commodity_num' => $afs_summary['total_after_count'] ?: 0,
                    'return_money' => $afs_summary['total_return_money'] ?: 0,
                    'return_point' => $afs_summary['total_return_point'] ?: 0,
                ]);
            }
        }


        $save_data = [
            'pre_refund_money'   => $pre_refund_money,
            'pre_refund_points'  => $pre_refund_points,
            'refund_money'       => $refund_money,
            'refund_points'      => $refund_points,
            'refund_info'        => json_encode_cn($refund_info),
            'refund_delivery_at' => $time_now,
            'modifier'           => $this->admin_info['username'],
            'refund_remark'      => $refund_remark,
            'refund_goods_msg'   => json_encode_cn($refund_goods_msg),
            'is_all_refund'      => $is_all_refund,
        ];
        if (isset($post_data['jd_remark']) && !empty($post_data['jd_remark'])) {
            $save_data['jd_ware_remark'] = $post_data['jd_remark'];
        }
        $up_money = $this->afs_model->saveData($save_data, ['id' => $id]);

        if (empty($up_money)) {
            print_json(1, '售后数据更新失败');
        }
        if ($save_type == 2 && $is_refund_stock == 1) {
            // 接京东退货接口
            $service = new JdWarehouse();
            $re      = $service->createAfter($order_data['order_code'], $id, $afs_data['afs_service_id']);
            foreach ($re as $item) {
                // 有失败 直接提示
                if ($item['code'] == 0) {
                    print_json(1, $item['msg']);
                }
            }

            print_json(0, '售后退货入库申请成功');
        }

        $is_dlr = $this->admin_info['type'] == 2;
        $net    = new NetOrder();

        $parent_num = $this->order_model->where(['parent_order_code' => $order_data['parent_order_code']])->count();
        if (in_array($order_data['promotion_source'], [2, 3]) && $parent_num > 1) { // 服务包和套装处理
            $order_commodity_list = (new BuOrderCommodity())
                ->alias('a')
                ->join('t_bu_order b', 'a.order_code=b.order_code')
                ->where(['a.parent_order_code' => $order_data['parent_order_code'], 'parent_order_type' => ['neq', 3]])
                ->column('b.id order_id,b.order_code,b.money,b.integral,b.pre_use_money,b.pre_point,b.cashier_trade_no', 'a.id');
            $refund_msg_list      = [];
            foreach ($order_commodity_list as $k => $v) {
                $refund_msg                      = $tmp[$k] ?? [];
                $refund_msg['pre_refund_money']  = $refund_msg['pre_refund_money'] ?? 0.00;
                $refund_msg['pre_refund_points'] = $refund_msg['pre_refund_points'] ?? 0;
                $refund_msg['refund_money']      = $refund_msg['refund_money'] ?? 0.00;
                $refund_msg['refund_points']     = $refund_msg['refund_points'] ?? 0;

                if (isset($refund_msg_list[$v['order_id']])) {
                    $refund_msg_list[$v['order_id']]['pre_refund_money']  = number_format($refund_msg_list[$v['order_id']]['pre_refund_money'] + $refund_msg['pre_refund_money'], 2, '.', '');
                    $refund_msg_list[$v['order_id']]['pre_refund_points'] = number_format($refund_msg_list[$v['order_id']]['pre_refund_points'] + $refund_msg['pre_refund_points'], 2, '.', '');
                    $refund_msg_list[$v['order_id']]['refund_money']      = number_format($refund_msg_list[$v['order_id']]['refund_money'] + $refund_msg['refund_money'], 2, '.', '');
                    $refund_msg_list[$v['order_id']]['refund_points']     = number_format($refund_msg_list[$v['order_id']]['refund_points'] + $refund_msg['refund_points'], 2, '.', '');
                } else {
                    $refund_msg_list[$v['order_id']]['pre_refund_money']  = $refund_msg['pre_refund_money'];
                    $refund_msg_list[$v['order_id']]['pre_refund_points'] = $refund_msg['pre_refund_points'];
                    $refund_msg_list[$v['order_id']]['refund_money']      = $refund_msg['refund_money'];
                    $refund_msg_list[$v['order_id']]['refund_points']     = $refund_msg['refund_points'];
                }
                $refund_msg_list[$v['order_id']]['refund_goods_msg'][] = array_merge(['order_commodity_id' => $k], $refund_msg);
            }

            $parent_is_all_refund = 1;
            foreach ($refund_msg_list as $k => $v) {
                $order_data_sub = $this->order_model->getOneByPk($k);
                $is_all_refund  = 0;
                if (
                    ($v['pre_refund_money'] == $order_data_sub['pre_use_money']) &&
                    ($v['pre_refund_points'] == $order_data_sub['pre_point']) &&
                    ($v['refund_money'] == $order_data_sub['money']) &&
                    ($v['refund_points'] == $order_data_sub['integral'])
                ) {
                    $is_all_refund = 1;
                }
                $save_data = [
                    'pre_refund_money'   => $v['pre_refund_money'],
                    'pre_refund_points'  => $v['pre_refund_points'],
                    'refund_money'       => $v['refund_money'],
                    'refund_points'      => $v['refund_points'],
                    'refund_info'        => json_encode_cn($refund_info),
                    'refund_delivery_at' => $time_now,
                    'modifier'           => $this->admin_info['username'],
                    'refund_remark'      => $refund_remark,
                    'refund_goods_msg'   => json_encode_cn($v['refund_goods_msg']),
                    'is_all_refund'      => $is_all_refund,
                ];
                $afs_sub   = $this->afs_model->getOne(['where' => ['order_id' => $k], 'order' => 'id desc']);
                $this->afs_model->saveData($save_data, ['id' => $afs_sub['id']]);
                $res = $net->refund($order_data_sub['order_code'], $order_data_sub['cashier_trade_no'], $is_dlr);
                if ($res['code'] == 200) {
                    $this->inQueue($afs_sub['id']);
                    if ($is_all_refund = 0) {
                        $parent_is_all_refund = 0;
                    }

                    if ($v == end($refund_msg_list)) { // 最后一条子单处理完要更新主单
                        // 修改订单
                        $this->order_model->saveData(['order_status' => 18, 'last_updated_date' => date('Y-m-d H:i:s'), 'modifier' => $afs_sub['modifier'] ?? ''], ['order_code' => $order_data_sub['parent_order_code']]);

                        // 修改售后单
                        $afs_data           = $this->afs_model->getOne(['where' => ['id' => $id]]);
                        $refund_delivery_at = empty($refund_time) ? date("Y-m-d H:i:s") : $refund_time;
                        $operate_list       = json_decode($afs_data['operate_list'], true);
                        array_push(
                            $operate_list, [
                                'afs_status' => DbAfterSaleOrders::$afss_type['only_refund'][6],
                                'name'       => 'system',
                                'desc'       => '已退款',
                                'time'       => $refund_delivery_at,
                            ]
                        );
                        $refund_info         = json_decode($afs_data['refund_info'], true);
                        $refund_info['time'] = $refund_delivery_at;

                        $cashier_refund_nos = $this->afs_model->where(['order_id' => ['in', array_keys($refund_msg_list)], 'afs_status' => 6, 'cashier_refund_no' => ['neq', '']])->column('cashier_refund_no');

                        $this->afs_model->saveData(
                            [
                                'afs_status'         => DbAfterSaleOrders::$afss_type['only_refund'][6],
                                'operate_list'       => json_encode_cn($operate_list),
                                'cashier_refund_no'  => implode(',', $cashier_refund_nos),
                                'refund_delivery_at' => $refund_delivery_at,
                                'refund_info'        => json_encode_cn($refund_info),
                                'is_all_refund'      => $parent_is_all_refund,
                            ],
                            ['id' => $id]
                        );
                        $net->orderChange($order_data['order_code']);
                        $this->inQueue($id);
                    }

                }
            }
        } else {
            $res = $net->refund($order_data['order_code'], $order_data['cashier_trade_no'], $is_dlr);
            if ($res['code'] == 200) {
                if ($order_data['order_source'] == 42) {
                    // 判断日产充电桩
                    $busiplat_nev_service = new BusiplatNevService();
                    $busiplat_nev_service->after($order_data['order_code']);
                }

                $this->inQueue($id);
            }
        }

        if($order_data['logistics_mode'] ==2){
            $netSupplier =  new NetSupplier();
            $netSupplier->updateAfterSaleToSupplier($afs_data['id']);
        }

        if ($res['code'] == 200) {
            print_json(0, $res['msg'] ?? 'ok');
        } else {
            print_json(1, $res['msg']);
        }
    }

    public function ajaxGetAfs()
    {
        $id  = input('id');
        $afs = $this->afs_model->getOneByPk($id);
        if (empty($afs)) {
            print_json(1, '售后信息不存在');
        }
        $ware_info = '';
        if ($afs['jd_warehouse_after_id']) {
            $ware_list = $this->jd_ware_model->getList(['where' => ['id' => ['in', $afs['jd_warehouse_after_id']]]]);
            foreach ($ware_list as $v) {
                $ware_info .= $v['jd_delivery_no'] . " " . $v['jd_status'] . '<br/>';
            }
        }
        $afs['ware_info'] = $ware_info;

        $afs['user_info'] = json_decode($afs['user_info'], true);
        print_json(0, 'ok', $afs);
    }

    public function ajaxGetDetail()
    {
        $order_id       = input('order_id');
        $id             = input('id');
        $afs_service_id = input('afs_service_id');
        if ($id) {
            $afs_data       = $this->afs_model->getOneByPk($id);
            $afs_service_id = $afs_data['afs_service_id'];
        }
        $afs_data                = $this->afs_model->getOne(['where' => ['afs_service_id' => $afs_service_id]]);
        $afs_data['refund_info'] = json_decode($afs_data['refund_info'], true);
        // 获取售后信息

        $order_data             = $this->order_model->getOneByPk($order_id);
        $order_data['integral'] = $order_data['integral'] ?? 0;

        $order_data['can_not_all'] = 0; // 可部分退
        $order_data['can_not_msg'] = ''; // 可部分退
//        if ($order_data['promotion_source'] == 3) {
//            $order_data['can_not_all'] = 1; // 服务包不可部分退
//            $order_data['can_not_msg'] = '*该订单为臻享服务包，不能部分退现金、积分操作'; // 服务包全部不可部分退
//        }
        if ($order_data['brand'] == 3 && ($order_data['pre_point'] > 0 || $order_data['integral'])) {
            $map        = ['order_code' => $order_data['order_code'], 'is_enable' => 1];
            $pointCount = BuOrderMoreCardPoint::where($map)->group('ic_card_no')->count();
            if ($pointCount > 1) {
                $order_data['can_not_all'] = 2; // 多卡支付不可部分退积分
                $order_data['can_not_msg'] = '*该订单为多卡支付积分，不能部分退积分操作'; // 多卡支付不可部分退积分
            }
        }


        $settle_data                  = $this->settle->getList(['where' => ['is_enable' => 1, 'order_code' => $order_data['order_code']]]);
        $order_data['afs_service_id'] = $afs_service_id;
        $order_data['settle']         = $settle_data;
//        $order_data['settle_cnt']     = count($settle_data);
        $order_data['settle_cnt'] = 0;
        $user_info_str            = '';
        $afs_user_info            = json_decode($afs_data['user_info'], true);
        if (!empty($afs_user_info)) {
            $user_info_str = sprintf("%s,%s,%s", $afs_user_info['name'], $afs_user_info['phone'], $afs_user_info['address']);
        }
        $afs_data['user_info_str'] = $user_info_str;

        $back_commodity = json_decode($afs_data['refund_goods'], true) ?? [];
        $where          = ['a.order_code' => $order_data['order_code'], 'a.mo_id' => 0];
        if (in_array($order_data['promotion_source'], [2, 3])) $where = ['a.parent_order_code' => $order_data['order_code'], 'a.mo_id' => 0];
        if (!empty($back_commodity)) {
            $where['a.id'] = ['in', array_column($back_commodity, 'order_commodity_id')];
        }
        $order_data['back_money'] = number_format($order_data['money'] + $order_data['pre_use_money'], 2, '.', ''); // 回显订单总金额
        $order_data['back_point'] = $order_data['integral'] + $order_data['pre_point']; // 回显订单总积分
        if ($afs_data['is_all_refund'] == 1) {
            $is_all_back = '是';
        } else {
            $is_all_back = '否，选择商品';
        }
//        $order_data['is_all_back']           = empty($afs_data['refund_goods']) ? '是' : '否，选择商品';
        $order_data['is_all_back']           = $is_all_back;
        $order_data['jd_warehouse_after_id'] = $afs_data['jd_warehouse_after_id'];
        $order_data['jd_no_finish']          = 0;
        if ($afs_data['jd_warehouse_after_id']) {
            $jd_ware = $this->jd_ware_model->getList(['where' => ['id' => ['in', $afs_data['jd_warehouse_after_id']]]]);
            if ($jd_ware) {
                $order_data['jd_warehouse_in_no'] = implode(',', array_column($jd_ware, 'jd_delivery_no'));
//                $jd_warehouse_jd_status_arr =array_unique(array_column($jd_ware,'jd_delivery_no'));
                foreach ($jd_ware as $v) {
                    if ($v['jd_delivery_no'] != $this->jd_finish_status) {
                        $order_data['jd_no_finish'] = 1;
                    }
                }
            }

        }
        $order_data['show_back_money']     = 0;
        $order_data['show_pre_back_money'] = $order_data['pre_use_money']; // 预售只会有一个商品
        $order_data['show_back_point']     = 0;
        $order_data['show_pre_back_point'] = $order_data['pre_point']; // 预售只会有一个商品
        $field                             = 'a.commodity_name,a.sku_info,a.count,a.actual_point,a.actual_use_money,a.third_sku_code,b.commodity_code,
        a.sku_id,a.mo_id,a.mail_price,a.pre_sale_id,p.inout_count as is_stock,a.supplier,a.commodity_pic,a.delivery_coefficient,
        afs_goods.id as afs_goods_id,afs_goods.jd_in_ware_count,afs_goods.after_count,jdw.delivery_no,jdw.jd_delivery_no,
        jdw.jd_status,jdw.jd_type';
        $order_commodity_data              = (new BuOrderCommodity())
            ->alias('a')
            ->join('t_db_commodity b', 'a.commodity_id=b.id')
            ->join('t_db_after_sale_order_commodity afs_goods', 'afs_goods.order_commodity_id=a.id and afs_goods.is_enable=1', 'left')
            ->join('t_db_jd_warehouse jdw', 'afs_goods.jd_warehouse_after_id=jdw.id and jdw.is_enable=1', 'left')
            ->join('t_pss_inventory_inout p', 'a.third_sku_code = p.sku_code and a.order_code=p.order_code and p.inout = 2', 'left')
            ->where($where)
            ->column($field, 'a.id');
        if (input('text') == 1) {
            $sql = (new BuOrderCommodity())->getLastSql();
            dd($sql);
        }
        $order_data['is_stock'] = 0;
        $order_data['have_pv']  = 0;
        $delivery_no            = implode(',   ', array_column($order_commodity_data, 'delivery_no'));

        $jd_delivery_no = [];
        foreach ($order_commodity_data as $k => &$v) {

            if ($v['pre_sale_id'] > 0) {
                $v['pre_use_money']        = $order_data['pre_use_money'];
                $v['actual_use_money']     = $order_data['money'];
                $v['pre_point']            = $order_data['pre_point'];
                $v['back_point']           = $order_data['integral'];
                $order_data['pre_sale_id'] = $v['pre_sale_id'];
            }
            if (in_array($v['supplier'], BuOrderCommodity::$jd_warehouse_supplier)) {
                $order_data['have_pv'] = 1;
            }

            $v['back_money'] = number_format($v['actual_use_money'] + $v['mail_price'], 2, '.', ''); // 回显商品总金额
            $v['back_point'] = $v['actual_point']; // 回显商品总积分
            $third_sku_codes = explode(',', $v['third_sku_code']) ?? [];
            if (count($third_sku_codes) > 1) {
                $v['third_sku_code'] = $third_sku_codes[0] . '...';
            }

            foreach ($back_commodity as $val) {
                if ($val['order_commodity_id'] == $k) $v['count'] = $val['count'];
            }
            $order_data['show_back_money'] = number_format($order_data['show_back_money'] + $v['actual_use_money'] + $v['mail_price'], 2, '.', '');
            $order_data['show_back_point'] += $v['back_point'];
            if (!empty($v['is_stock'])) {
                $order_data['is_stock'] = 1;
            }
            $v['in_num'] = $v['count'] * $v['delivery_coefficient'];

            $jd_delivery_no[$v['delivery_no']] = DbJdWarehouse::jdStatus($v['jd_type'], $v['jd_status']);
        }
        $order_data['jd_tip'] = 0;
        if ($order_data['have_pv'] == 1 && $order_data['jd_no_finish'] == 1) {
            $order_data['jd_tip'] = 1;
        }
        $afs_data['delivery_no']            = $delivery_no;
        $afs_data['jd_delivery_no']         = $jd_delivery_no;
        $order_data['order_commodity_data'] = $order_commodity_data;
        $order_data['afs_data']             = $afs_data;


        print_json(0, 'ok', $order_data);
    }

    private function _commodityTypeList($comm_parent_id = 0)
    {
        $dbCommType     = new DbCommodityType();
        $params         = ['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'field' => 'id,comm_type_name', 'order' => 'sort'];
        $commodity_list = $dbCommType->getList($params);
        return $commodity_list;
    }

    private function inQueue($afs_id)
    {
        //数据对接到售后中心
        $as_data = ['afs_id' => $afs_id, 'type' => 'update', 'created_at' => time()];
        Queue::push('app\common\queue\AfterSale', json_encode($as_data), config('queue_type.after_sale'));
    }

    /*
     * 发起NI+售后
     * */
    public function after_index()
    {
        $buOrder = new BuOrder();

        $order_code     = trim(input('get.order_code'));
        $pay_order_code = trim(input('get.pay_order_code'));
        $created_date   = input('get.created_date');
        $name           = input('get.name');
        $phone          = input('get.phone');
        $channel        = input('get.channel');

        //渠道来源
        $channelList = BuOrder::$channel;

        $where = [
            ['exp', "( ( a.order_status in (2, 9, 23,24) ) or ( a.order_status = 7 and a.order_source = 24 and pay_time >= subdate(now(), 7))) 
            or (a.order_status = 12 and a.order_source =42)"],
            'a.is_enable'      => 1,
            'a.logistics_mode' => 2,
            ['exp', "( ( b.afs_status is null ) or ( b.afs_status in ('2','3','5','8','11') ) )"],
            ['exp', "( ( d.status is null ) or ( d.status = 1 ) )"],
            ['exp', "( ( a.promotion_source != 3 && a.parent_order_type != 3) or ( a.promotion_source = 3 && a.parent_order_type = 3 ) )"],
        ];

        if ($this->admin_info['type'] == 2) { // 专营店账号
            $where = [
                'a.order_status'   => ['in', [2, 13, 19]],
                'a.is_enable'      => 1,
                'a.logistics_mode' => 1,
                ['exp', "( ( b.afs_status is null ) or ( b.afs_status in ('2','3','5','8','11') ) )"],
                ['exp', "( ( a.promotion_source != 3 && a.parent_order_type != 3) or ( a.promotion_source = 3 && a.parent_order_type = 3 ) )"],
                ['exp', "a.dlr_code = '{$this->admin_info['dlr_code']}' OR a.dd_dlr_code = '{$this->admin_info['dlr_code']}'"]
            ];
        }

        $query = [
            'created_date'     => '',
            'order_code'       => $order_code,
            'pay_order_code'   => $pay_order_code,
            'name'             => $name,
            'phone'            => $phone,
            //渠道来源
            'channel'          => $channel,
            'selected_channel' => explode(",", $channel),
        ];

        if (!empty($channel)) {
            $where['a.channel'] = ['in', explode(",", $channel)];
        }

        if (!empty($order_code)) {
            $where['a.order_code'] = $order_code;
        }
        if (!empty($pay_order_code)) {
            $where['a.pay_order_code'] = $pay_order_code;
        }
        if (!empty($name)) {
            $where['a.name'] = $name;
        }
        if (!empty($phone)) {
            $where['a.phone'] = $phone;
        }
        if (!empty($created_date)) {
            list($created_date_start, $created_date_end) = explode('~', $created_date);
            $where['a.created_date'] = ['between', [date('Y-m-d H:i:s', strtotime($created_date_start)), date('Y-m-d H:i:s', strtotime($created_date_end) + 86400)]];
            $query['created_date']   = $created_date_start . ' ~ ' . $created_date_end;
        }

        if (!$order_code && !$channel && !$pay_order_code && !$name && !$phone && !$created_date) {
            $where['a.created_date'] = ['between', [date('Y-m-d') . " 00:00:00", date('Y-m-d') . " 23:59:59"]];
        }

        if (empty(input('get.'))) {
            unset($where);
            $where['a.id'] = 0;
        }

        $params = [
            'where' => $where,
            'query' => $query,
            'order' => 'a.created_date desc',
            'group' => 'a.id',
            'field' => "a.id,a.order_code,a.pre_point,a.pre_use_money,a.pay_order_code,a.total_money,a.money,a.dd_dlr_code,a.dlr_code,a.name,a.phone,a.vin,a.license_plate,a.integral,a.card_code,a.created_date,a.order_status,a.common_carrier,a.waybill_number,a.channel,a.order_source,a.receipt_address,a.pay_time",
        ];
        $list   = $buOrder->canAfterOrder($params);
        if (input('test') == 1) {
            echo $buOrder->getLastSql();
            die();
        }
        $c_model           = new DbCard();
        $order_source_list = BuOrder::orderSource();
        $card_id_arr       = array_column(collection($list)->toArray(), 'card_id');
        $card_arr          = [];
        if ($card_id_arr) {
            $c_params  = [
                'where' => ['id' => ['in', $card_id_arr]],
                'field' => 'card_id,card_name'
            ];
            $card_list = $c_model->getList($c_params);
            foreach ($card_list as $cc_v) {
                $card_arr[$cc_v['card_id']] = $cc_v['card_name'];
            }
        }

        foreach ($list as $key => $val) {
//            $list[$key]['dlr'] = $val['dlr_code'] . ' - ' . $val['dlr_name'];
            $list[$key]['order_status_name'] = $buOrder::orderStatus($val['order_status']);
            $list[$key]['order_source_name'] = $order_source_list[$val['order_source']];
            if (in_array($val['order_status'], [1, 3, 5, 6, 8])) {
                $list[$key]['money'] = '0.00';
            }
            $list[$key]['card_name'] = '';
            if (!empty($val['card_id'])) {
                $_card_id        = explode(',', $val['card_id']);
                $card_named_list = '';
                foreach ($_card_id as $_c_v) {
                    $card_named_list .= $card_arr[$_c_v] . ',';
                }
                $list[$key]['card_name'] = rtrim($card_named_list, ',');
            }

            //渠道来源
            $list[$key]['channel_name'] = $channelList[$val['channel']] ?? '未知';

            //订单表：money + integral/10 + pre_point/10 + pre_use_money
            $list[$key]['order_money'] =
                $val['money']
                + $val['integral'] / 10
                + $val['pre_point'] / 10
                + $val['pre_use_money'];
            $list[$key]['order_money'] = round($list[$key]['order_money'], 2);

        }
        $page = $list->render();
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('query', $query);
        $this->assign('order_source_list', $order_source_list);
        $this->assign('role', $this->admin_info['type']);
        $this->assign('channel_list', $channelList);
        return $this->fetch('after_index');
    }

    public function afterApply()
    {
        $input = input();
//        dd($input);
        if ($input['order_id'] > 0) {
            $order_model = new BuOrder();
            $after_model = new DbAfterSaleOrders();
            $order       = $order_model->getOne(['where' => ['id' => $input['order_id']]]);
//            if (!$order || (!in_array($order['order_status'], [2, 9, 23,24]) && !($order['order_status'] == 7 && $order['order_source'] == 24) &&
//                    // 日产充电桩
//                    !($order['order_status'] == 12  && $order['order_source'] = 42))
//                ) {
//                print_json(1, '订单状态异常');
//            }
            if (($order['order_source'] == 2) && ($input['afs_type'] == 1)) {
                print_json(1, '拼团订单不可退款');
            }
            $r_record = $after_model->getOne(['where' => ['order_id' => $input['order_id']], 'order' => "id desc", 'is_enable' => 1]);
            if (!empty($r_record) && !in_array($r_record['afs_status'], [2, 3, 5, 8, 11])) {
                print_json(1, '售后单已存在');
            }
            $init_status = DbAfterSaleOrders::$afss_type['only_refund'][1];

            //申请退款(2:已支付) 和 申请售后(已完成)的订单状态是不同的,需验证
            if ($input['afs_type'] == DbAfterSaleOrders::ONLY_REFUND) {
                $init_status = DbAfterSaleOrders::$afss_type['only_refund'][1];
//                if (!in_array($order['order_status'], [2, 23]) &&
//                    !($order['order_status'] == 7 && $order['order_source'] == 24) &&
//                    // 日产充电桩
//                    !($order['order_status'] == 12 && $order['order_source'] == 42)
//                ) {
//                    print_json(1, '订单状态异常');
//                }

            } else {
                $init_status = ($input['afs_type'] == DbAfterSaleOrders::BACK_PRO) ? DbAfterSaleOrders::$afss_type['back_pro'][7] : DbAfterSaleOrders::$afss_type['chg_pro'][10];
//                if ($order['order_status'] != 9 && $order['order_source'] != 42) {
//                    print_json(1, '订单状态异常');
//                }
            }

            // 2023-10-12 17:08:23 烂口汤说后台发起给最大权限不用校验是否可以退款
            // 2025年3月18日 15:50:01  还是需要失效卡券状态
            $net_order = new NetOrder();
            $re = $net_order->cardAfterSale($order, 1, 0, 1);
            if ($re['code'] <> 200){
                print_json(1, $re['msg']);
            }

            // 赠品券订单
            $net_gift = new NetGiftOrder();
            $re = $net_gift->giftOrderAfterSale($order);
            if ($re['code'] <> 200) {
                print_json(1, $re['msg']);
            }

            $user_info = [];
            if ($input['afs_type'] == 3) {
                $user_info = [
                    'name'    => $input['user_name'],
                    'phone'   => $input['phone'],
                    'address' => $input['address'],
                ];
            }

            $settle = BuOrderSettlement::where('order_code', $order['order_code'])->column('cashier_settlement_no');

            $data = array(
                'afs_service_id'      => $order['dlr_code'] . $input['afs_type'] . date('ymdHis') . mt_rand(1000, 9999),
                'settlement_ids'      => empty($settle) ? "" : implode(",", $settle),
                'order_id'            => $input['order_id'],
                'afs_type'            => $input['afs_type'],
                'afs_reason'          => '其它',
                'afs_reason_detail'   => '',
                'afs_reason_pictures' => json_encode_cn([]),
                'user_info'           => json_encode_cn($user_info),
                'afs_status'          => $init_status,
                'operate_list'        => json_encode_cn([['afs_status' => $init_status, 'name' => 'admin：' . $this->admin_info['username'], 'desc' => '申请售后', 'time' => date("Y-m-d H:i:s")]]),
            );

            $after_model->insertData($data);
            $afs_id = $after_model->getLastInsID();
            if($input['afs_type']== DbAfterSaleOrders::ONLY_REFUND && $order['logistics_mode']==2){
                $supplier =  new NetSupplier();
                $supplier->sendAfterSaleToSupplier($afs_id);
            }
            //数据对接到售后中心
            $as_data = ['afs_id' => $afs_id, 'order_code' => $order['order_code'], 'type' => 'create', 'user_id' => $order['user_id'], 'created_at' => time()];
            Queue::push('app\common\queue\AfterSale', json_encode($as_data), config('queue_type.after_sale'));
            print_json(0, '操作成功');
        } else {
            print_json(1, '参数错误');
        }

    }

    // 取消
    public function changeAfter()
    {
        $id     = input('id', 0);
        $afsObj = new DbAfterSaleOrders();
        $where  = ['id' => $id, 'is_enable' => 1];
        $afs    = $afsObj->getOne(['where' => $where]);
        if ($afs['can_cancel'] == 2) {
            print_json(1, '售后单不可取消');
        }

        if ($afs['jd_warehouse_after_id']) {
            // 查询售后订单商品表
            $after_order_commodity_model = new DbAfterSaleOrderCommodity();
            $jd_warehouse_after_id_arr   = $after_order_commodity_model->where('afs_id', $id)->group('jd_warehouse_after_id')->column('jd_warehouse_after_id');
            $jd_warehouse_model          = new DbJdWarehouse();
            // 查询JD退货订单状态是否已完成
            $map               = [
                'id'        => ['in', $jd_warehouse_after_id_arr],
                'is_enable' => 1,
                'jd_status' => ['in', [200, 400]]
            ];
            $jd_warehouse_info = $jd_warehouse_model->where($map)->find();
            if ($jd_warehouse_info) {
                print_json(1, '退货入库单号' . $jd_warehouse_info['jd_delivery_no'] . '，状态为已完成，取消无效');
            }
            foreach ($jd_warehouse_after_id_arr as $jdWarehouseId) {
                // 取消单据
                $result = JdCloudWarehouse::create('jd_cloud_warehouse')->cancelOrder($jdWarehouseId);
                if ($result['code'] != 1000) {
                    print_json(1, $result['message']);
                }
            }

        }
        if (!in_array($afs['afs_status'], [2, 5, 6, 13, 14])) {
            $recipients_info = json_encode_cn(['name' => $this->admin_info['username'], 'time' => date("Y-m-d H:i:s")]);

            $operate_list = json_decode($afs['operate_list'], true);
            array_push($operate_list, [
                'afs_status' => 2,
                'name'       => $this->admin_info['username'] ?? "",
                'desc'       => '后台操作',
                'time'       => date("Y-m-d H:i:s")
            ]);

            $this->afs_model->saveData([
                'afs_status'      => 2,
                'recipients_info' => $recipients_info,
                'modifier'        => $this->admin_info['username'],
                'operate_list'    => json_encode_cn($operate_list)
            ], ['id' => $id]);
            //数据对接到售后中心
            $as_data = ['afs_id' => $id, 'type' => 'update', 'created_at' => time()];
            Queue::push('app\common\queue\AfterSale', json_encode($as_data), config('queue_type.after_sale'));
            print_json(0, 'ok');
        }
        print_json(1, '取消售后单失败');
    }
}
