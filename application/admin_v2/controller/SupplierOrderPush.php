<?php
/**
 * 供应商订单推送报表控制器
 * @author: AI Assistant
 * @time: 2025-01-21
 */

namespace app\admin_v2\controller;

use app\common\model\bu\BuSupplierOrderPushLog;
use app\common\model\db\DbLog;
use app\common\model\sys\SysMenu;
use app\common\port\connectors\Supplier;
use think\Db;
use think\Env;
use tool\Logger;

class SupplierOrderPush extends Common
{
    /**
     * 跳转到前端项目页面
     */
    public function index()
    {
        $m_model = new SysMenu();
        $list    = $m_model->alias('a')
            ->join('t_sys_menu b', 'a.menu_pid = b.id')
            ->where(['a.controller' => request()->controller(), 'a.left_view' => 1, 'b.left_view' => 1])
            ->field('a.web_menu_url,b.menu_url')
            ->find();
        if (!empty($list['web_menu_url'])) {
            $username['username']  = $this->admin_info['username'];
            $username['timestamp'] = time();
            $token                 = http_build_query($username);
            $this->redirect(Env::get('TOB_URL') . '/' . $list['web_menu_url'] . '?sign=' . base64_encode($token));
        }
    }

    /**
     * 获取供应商订单推送列表数据
     */
    public function getList()
    {
        $params = input('get.');

        // 构建查询条件
        $where = [];

        // 推送状态筛选
        if (isset($params['push_status']) && $params['push_status'] !== '') {
            $where['push_status'] = $params['push_status'];
        }

        // 推送时间范围筛选
        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $where['push_time'] = ['between', [$params['start_time'], $params['end_time']]];
        } elseif (!empty($params['start_time'])) {
            $where['push_time'] = ['>=', $params['start_time']];
        } elseif (!empty($params['end_time'])) {
            $where['push_time'] = ['<=', $params['end_time']];
        }

        // 订单编码筛选
        if (!empty($params['order_code'])) {
            $where['order_detail_no'] = ['like', '%' . $params['order_code'] . '%'];
        }

        $model = new BuSupplierOrderPushLog();
        $queryParams = [
            'where' => $where,
            'field' => 'id,total_commodity_amount,order_detail_no,total_amount,delivery_fee,actual_payment_amount,order_status,receipt_address,remark,coupon_discount_amount,payment_method,order_paid_time,total_payment_integral,user_name,user_mobile,register_mobile,delivery_callback_url,split_time,split_status,activity_discount_amount,push_status,audit_status,push_time,created_date',
            'order' => 'id desc',
            'pagesize' => $params['pagesize'] ?? 20,
            'query' => $params
        ];

        $list = $model->getListData($queryParams);

        // 格式化数据
        $items = [];
        foreach ($list as $item) {
            // 转换金额字段为元（返回整数）
            $item['total_amount'] = intval($item['total_amount'] / 100);
            $item['delivery_fee'] = intval($item['delivery_fee'] / 100);
            $item['actual_payment_amount'] = intval($item['actual_payment_amount'] / 100);
            $item['coupon_discount_amount'] = intval($item['coupon_discount_amount'] / 100);
            $item['activity_discount_amount'] = intval($item['activity_discount_amount'] / 100);
            $item['total_commodity_amount'] = intval($item['total_commodity_amount'] / 100);

            // 转换积分字段（返回整数）
            $item['total_payment_integral'] = intval($item['total_payment_integral'] / 10);

            // 添加状态文本
            $item['push_status_text'] = $item['push_status'] == 1 ? '成功' : '失败';
            $item['audit_status_text'] = $this->getAuditStatusText($item['audit_status']);

            $items[] = $item;
        }

        print_json(0, 'success', [
            'list' => $items,
            'total' => $list->total(),
            'current_page' => $list->currentPage(),
            'per_page' => $list->listRows()
        ]);
    }

    /**
     * 获取订单推送详情
     */
    public function detail()
    {
        $id = input('get.id');
        if (empty($id)) {
            print_json(1, '参数错误');
        }

        $model = new BuSupplierOrderPushLog();
        $detail = $model->getOneByPk($id);

        if (!$detail) {
            print_json(1, '记录不存在');
        }

        // 将 Model 对象转换为数组，避免引用修改问题
        $detail = $detail->toArray();

        // 格式化金额（返回整数）
        $detail['total_amount'] = intval($detail['total_amount'] / 100);
        $detail['delivery_fee'] = intval($detail['delivery_fee'] / 100);
        $detail['actual_payment_amount'] = intval($detail['actual_payment_amount'] / 100);
        $detail['coupon_discount_amount'] = intval($detail['coupon_discount_amount'] / 100);
        $detail['activity_discount_amount'] = intval($detail['activity_discount_amount'] / 100);
        $detail['total_commodity_amount'] = intval(($detail['total_commodity_amount'] ?? 0) / 100);
        $detail['payment_method_name'] = $this->getPaymentMethodText($detail['payment_method']);

        // 根据订单编号前两位判断订单来源
        if (substr($detail['order_detail_no'], 0, 2) === 'GW') {
            $detail['order_source_name'] = '日产商城';
        } else {
            $detail['order_source_name'] = '启辰商城';
        }

        // 解析商品详情
        if (!empty($detail['detail'])) {
            $detail['commodity_list'] = json_decode($detail['detail'], true);
            // 格式化商品价格和积分

            if (is_array($detail['commodity_list'])) {
                foreach ($detail['commodity_list'] as &$commodity) {
                    // 金额字段转换为元（返回整数）
                    $commodity['commodityPrice'] = intval(($commodity['commodityPrice'] ?? 0) / 100);
                    $commodity['commodityTotalPrice'] = intval(($commodity['commodityTotalPrice'] ?? 0) / 100);
                    $commodity['couponDiscountAmount'] = intval(($commodity['couponDiscountAmount'] ?? 0) / 100);
                    $commodity['originalTotalCostAmount'] = intval(($commodity['originalTotalCostAmount'] ?? 0) / 100);
                    $commodity['logisticsDeliveryFee'] = intval(($commodity['logisticsDeliveryFee'] ?? 0) / 100);
                    $commodity['commodityPurchasePrice'] = intval(($commodity['commodityPurchasePrice'] ?? 0) / 100);

                    // 积分字段转换（返回整数）
                    $commodity['totalCostIntegral'] = intval(($commodity['totalCostIntegral'] ?? 0) / 10);
                }
                // 确保修改后的数组被正确赋值回去
                unset($commodity); // 清除引用，避免后续问题
            }
        }

        $detail['push_status_text'] = $detail['push_status'] == 1 ? '成功' : '失败';
        $detail['audit_status_text'] = $this->getAuditStatusText($detail['audit_status']);

        print_json(0, 'success', $detail);
    }

    /**
     * 重传订单接口
     */
    public function retransmit()
    {
        $params = input('post.');

        if (empty($params['id'])) {
            print_json(1, '参数错误');
        }

        $model = new BuSupplierOrderPushLog();
        $record = $model->find($params['id']);

        if (!$record) {
            print_json(1, '记录不存在');
        }

        // 开启事务
        Db::startTrans();

        try {

            // 更新可修改的字段
            $updateData = [];
            $allowedFields = [
                'total_amount', 'delivery_fee', 'actual_payment_amount', 'receipt_address',
                'remark', 'coupon_discount_amount', 'order_paid_time', 'total_payment_integral',
                'user_name', 'user_mobile', 'register_mobile', 'split_time', 'split_status',
                'activity_discount_amount','total_commodity_amount'
            ];

            foreach ($allowedFields as $field) {
                if (isset($params[$field])) {
                    金额字段需要转换为分
                    if (in_array($field, ['total_amount', 'delivery_fee', 'actual_payment_amount', 'coupon_discount_amount', 'activity_discount_amount','total_commodity_amount'])) {
                        $updateData[$field] = intval($params[$field] * 100);
                    } else {
                        $updateData[$field] = $params[$field];
                    }
                    $updateData[$field] = $params[$field];
                }
            }

            // 处理商品详情更新
            if (isset($params['detail']) && is_array($params['detail'])) {
                // 商品详情中的活动类型和卡券ID不可更改，其他可更改
                $commodityList = $params['detail'];
                foreach ($commodityList as &$commodity) {
                    // 转换价格为分（使用intval确保返回整数）
                    if (isset($commodity['commodityPrice'])) {
                        $commodity['commodityPrice'] = intval($commodity['commodityPrice'] * 100);
                    }
                    if (isset($commodity['commodityTotalPrice'])) {
                        $commodity['commodityTotalPrice'] = intval($commodity['commodityTotalPrice'] * 100);
                    }
                    if (isset($commodity['couponDiscountAmount'])) {
                        $commodity['couponDiscountAmount'] = intval($commodity['couponDiscountAmount'] * 100);
                    }
                    if (isset($commodity['originalTotalCostAmount'])) {
                        $commodity['originalTotalCostAmount'] = intval($commodity['originalTotalCostAmount'] * 100);
                    }
                    if (isset($commodity['logisticsDeliveryFee'])) {
                        $commodity['logisticsDeliveryFee'] = intval($commodity['logisticsDeliveryFee'] * 100);
                    }
                    if (isset($commodity['commodityPurchasePrice'])) {
                        $commodity['commodityPurchasePrice'] = intval($commodity['commodityPurchasePrice'] * 100);
                    }
                }
                $updateData['detail'] = json_encode($commodityList, JSON_UNESCAPED_UNICODE);
            }

            // 更新数据库记录
            $model->where('id', $params['id'])->update($updateData);

            // 重新获取更新后的记录
            $updatedRecord = $model->find($params['id']);

            // 构建推送数据
            $pushData = [
                'orderDetailNo' => $updatedRecord['order_detail_no'],
                'totalAmount' => $updatedRecord['total_amount'],
                'deliveryFee' => $updatedRecord['delivery_fee'],
                'actualPaymentAmount' => $updatedRecord['actual_payment_amount'],
                'orderStatus' => $updatedRecord['order_status'],
                'receiptAddress' => $updatedRecord['receipt_address'],
                'remark' => $updatedRecord['remark'],
                'createdDate' => $updatedRecord['created_date'],
                'couponDiscountAmount' => $updatedRecord['coupon_discount_amount'],
                'paymentMethod' => $updatedRecord['payment_method'],
                'orderPaidTime' => $updatedRecord['order_paid_time'],
                'totalPaymentIntegral' => $updatedRecord['total_payment_integral'],
                'userName' => $updatedRecord['user_name'],
                'userMobile' => $updatedRecord['user_mobile'],
                'registerMobile' => $updatedRecord['register_mobile'],
                'deliveryCallbackUrl' => $updatedRecord['delivery_callback_url'],
                'splitTime' => $updatedRecord['split_time'],
                'splitStatus' => $updatedRecord['split_status'],
                'activityDiscountAmount' => $updatedRecord['activity_discount_amount'],
                'detail' => json_decode($updatedRecord['detail'], true)
            ];

            // 调用供应商接口
            $response = Supplier::create('supplier')->submitSupplierOrder($pushData);

            // 更新推送状态
            $pushStatus = (isset($response['result']) && $response['result'] == 1) ? 1 : 0;
            $model->where('id', $params['id'])->update([
                'push_status' => $pushStatus,
                'push_time' => date('Y-m-d H:i:s')
            ]);

            // 记录到DbLog
            $logData = [
                'type' => 'retransmit_supplier_order',
                'is_success' => $pushStatus,
                'send_note' => json_encode($pushData, JSON_UNESCAPED_UNICODE),
                'receive_note' => json_encode($response, JSON_UNESCAPED_UNICODE),
                'order_code' => $updatedRecord['order_detail_no']
            ];

            $logModel = new DbLog();
            $logModel->insertData($logData);

            Db::commit();

            if ($pushStatus) {
                print_json(0, '重传成功', [
                    'push_status' => $pushStatus,
                    'response' => $response
                ]);
            } else {
                print_json(1, '重传失败', [
                    'push_status' => $pushStatus,
                    'response' => $response
                ]);
            }

        } catch (\Exception $e) {
            Db::rollback();
            Logger::error('供应商订单重传失败', [
                'error' => $e->getMessage(),
                'params' => $params
            ]);

            print_json(1, '重传失败：' . $e->getMessage());
        }
    }



    /**
     * 获取审核状态文本
     */
    private function getAuditStatusText($status)
    {
        $statusMap = [
            0 => '未审核',
            1 => '审核通过',
            2 => '审核拒绝'
        ];

        return $statusMap[$status] ?? '未知';
    }

    private function getPaymentMethodText($method)
    {
        $method_arr = explode(',', $method);

        //现金=1，积分=2，卡券=3
        $methodMap = [
            1 => '现金',
            2 => '积分',
            3 => '卡券'
        ];
        $met_str = '';
        foreach($method_arr as $k=>$v){
            $met_str.=$methodMap[$v].'+';
        }

        return $met_str?trim($met_str, '+'):'';
    }
}
