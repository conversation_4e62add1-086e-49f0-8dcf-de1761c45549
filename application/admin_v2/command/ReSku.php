<?php


namespace app\admin_v2\command;


use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommoditySku;
use app\common\model\e3s\E3sCarSeries;
use app\common\model\e3s\E3sPartCarSeries;
use app\common\model\e3s\E3sSparePart;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Config;
use app\common\model\db\DbCommoditySet;
use think\Hook;
use think\Log;
use tool\Logger;

class ReSku extends Command
{

    /**
     * 全量重新索引列表价格
     */
    protected function configure()
    {
        $this->setName('resku')->setDescription('批量更新sku和set sku时间');
    }

    /**
     * @param Input $input
     * @param Output $output
     * @return int|void|null
     */
    protected function execute(Input $input, Output $output)
    {
        Logger::debug('cron refresh all product start');

        Config::load(ROOT_PATH . 'config/config.php');
        Config::load(ROOT_PATH . 'config/' . config('app_status') . '.php');
        Hook::import(require ROOT_PATH . 'config/tags.php');

        $commodity = new DbCommodity();
        $commodity_sku = new DbCommoditySku();
        $where['dd_commodity_type'] = 9;
        $where['is_grouped'] = 0;
        //获取全部到店备件普通商品
        $list = $commodity->alias('a')
            ->where($where)
            ->order('id desc')
            ->select();
        foreach ($list as $value)
        {
            //普通商品
            $res = $commodity_sku->where(['commodity_id'=>$value['id'],'is_enable'=>1])
                ->group('sku_code,sp_value_list')
                ->select();
            if(!empty($res))
            {
                //设置sku is_enable 为0 start
                $commodity_sku->where(['commodity_id'=>$value['id']])->update(['is_enable' => 0]);
                //设置sku is_enable 为0 end
                $data = [];
                foreach ($res as $val){
                    $part = $this->find_fit_time($val['sku_code']);
                    foreach ($part as $v){
                        $data[] = [
                            'commodity_id' =>$val['commodity_id'],
                            'sp_value_id_1' =>$val['sp_value_id_1'], 'sp_value_id_2' =>$val['sp_value_id_2'],
                            'sp_value_id_3' =>$val['sp_value_id_3'], 'price' =>$v['sale_price'],
                            'stock' =>$val['stock'], 'sp_value_list' =>$val['sp_value_list'],
                            'image' =>$val['image'], 'sku_code' =>$val['sku_code'],
                            'cost_price' =>$v['dlr_price'], 'tax_code' =>$val['tax_code'],
                            'tax' =>$val['tax'],
                            'relate_car_18n' =>$v['relate_car_18n'],
                            'relate_car_ids' =>$v['relate_car_ids'],
                            'relate_car_work_hour' =>$v['relate_car_work_hour'],
                            'e3s_bj_type_name' =>$val['e3s_bj_type_name'], 'card_id' =>$val['card_id'],
                            'city_type' =>$val['city_type'], 'hours_id' =>$val['hours_id'],
                            'part_start_time' =>$v['fit_beg_time'], 'part_end_time' =>$v['fit_end_time'],
                            'rep_part_no' =>$v['rep_part_no']
                        ];
                    }
                }
                if(!empty($data)){
                    $sku_id = $commodity_sku->saveAll($data);
                    $this->save_set_sku($value['id'],$sku_id);//变更set_sku
                }
            }
        }
        Logger::debug('cron refresh all product end');
    }

    public function save_set_sku($sku_id,$data){
        $set_sku = new DbCommoditySetSku();
        $list = $set_sku
            ->where(['commodity_id' => $sku_id,'is_enable' => 1])
            ->group('commodity_set_id')
            ->select();
        //更新sku表到set_sku
        if(!empty($list)){
            $array = [];
            foreach ($list as $value){
//                    //把之前的set_sku is_enable 设置0 start
                $set_sku->where(['commodity_id'=>$sku_id])->update(['is_enable'=>0]);
                //把之前的set_sku is_enable 设置0 end
                foreach ($data as $val){
                    $array[] = [
                        'price' => $val['price'], 'stock' => $value['stock'],
                        'dlr_code' => $value['dlr_code'], 'commodity_sku_id' => $val['id'] ?? 0,
                        'set_type' => $value['set_type'], 'commodity_id' => $value['commodity_id'],
                        'commodity_set_id' => $value['commodity_set_id'], 'divided_into' => $value['divided_into'],
                        'install_fee' => $value['install_fee'], 'cost_price' => $val['cost_price'],
                        'group_sub_commodity_id' => $value['group_sub_commodity_id'],
                        'group_sub_set_sku_id' => $value['group_sub_set_sku_id'],
                        'relate_car_18n' => $val['relate_car_18n'],
                        'relate_car_ids' => $val['relate_car_ids'],
                        'city_type' => $value['group_sub_set_sku_id'],
                    ];
                }
            }
            if(!empty($array)){
                $set_sku->saveAll($array);
            }
        }
    }

    public function find_fit_time($sku_code)
    {
        $spare_part = new E3sSparePart();
        $part_car = new E3sPartCarSeries();
        $car_series = new E3sCarSeries();
        $res = $spare_part->alias('a')
            ->join('t_e3s_part_car_series b','a.part_no = b.part_no')
            ->where([
                'a.part_no'=>$sku_code, 'a.is_enable'=>1,'b.is_enable'=>1,
                'b.fit_beg_time' => array('neq','')
            ])->field('a.sale_price,a.dlr_price,a.part_no,a.rep_part_no,b.fit_beg_time,b.fit_end_time')
            ->group('b.fit_beg_time,b.fit_end_time')
            ->select();
        $data = [];
        foreach ($res as $val){
            $relate_car_work_hour = $part_car
                ->where(['part_no' =>$val['part_no'], 'fit_beg_time' =>$val['fit_beg_time'],
                    'fit_end_time' =>$val['fit_end_time'],
                ])->field('id,car_config_code')->select();
            $part_no_array = [];
            $car_ids = [];
            foreach ($relate_car_work_hour as $value) {
                $part_no_array[$value['id']][] = $value['car_config_code'];
                $car_ids[] = $value['car_config_code'];
            }
            $relate_car_ids = $car_series
                ->where(['car_config_code'=> array('in',$car_ids)])
                ->column('id');
            $data[] = [
                'sale_price' =>$val['sale_price'],
                'dlr_price' =>$val['dlr_price'],
                'relate_car_18n' =>implode(',',$car_ids),
                'relate_car_ids' =>implode(',',array_values($relate_car_ids)),
                'relate_car_work_hour' =>json_encode($part_no_array),
                'fit_beg_time' =>$val['fit_beg_time'],
                'fit_end_time' =>$val['fit_end_time'],
                'rep_part_no' =>$val['rep_part_no']
            ];
        }
        return $data;

    }
}