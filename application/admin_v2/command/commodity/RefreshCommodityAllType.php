<?php

namespace app\admin_v2\command\commodity;

use app\common\command\Base;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySet;
use think\Hook;
use think\Log;
use tool\Logger;

/**
 * Class RefreshCommodityAllType
 * @package app\admin_v2\command\commodity
 */
class RefreshCommodityAllType extends Base
{
    public static function doIt()
    {
        Logger::debug('cron refresh commodity all type start');
        static::loadConfig();
        $cronTime = date('Y-m-d').' 07:00:00'; // 全量定时任务执行完时间
        $time = date('Y-m-d H:i:s');
        if ($time < $cronTime) {
            return ; // 全量定时任务执行完再执行这个任务
        }

        try {

            $now_time = date('Y-m-d H:i:s');
//            dd($now_time);
            #查询上架时间和下架时间都不为空的数据
            // 上架

////            $set = Db('db_commodity_set')->where("`latest_listing_time` < '%s' and `is_enable` = 0 and (`latest_end_time` > '%s' or `latest_end_time` is null)", $now_time, $now_time)
//                ->where(['commodity_id'=> 39900])
//                ->select();
            $set_model = new DbCommoditySet();
            $map = [
                'latest_listing_time' => ['lt', $now_time],
                'is_enable' => 0,
            ];
            $set = $set_model->where($map)->where(function ($query) use ($now_time) {
                $query->where('latest_end_time', 'gt', $now_time)
                    ->whereOr('latest_end_time',null);
            })->select();
//            $sql = $set_model->getLastSql();
//            print_json($sql);
            if (!empty($set)) {
                foreach ($set as $val) {
                    $val['commodity_set_id'] = $val['id'];
                    unset($val['id']);
                    $val['modifier']='rcat_s';
                    Hook::listen('flat', $val);
                }
            }
            // 下架
//            $where = sprintf("`latest_end_time`<'%s' and `is_enable` = 1", $now_time);
            $where = [
                'latest_end_time' => ['lt', $now_time],
                'is_enable' => 1
            ];
            $set   = $set_model->where($where)->select();
            if (!empty($set)) {
                foreach ($set as $val) {
                    $val['commodity_set_id'] = $val['id'];
                    unset($val['id']);
                    $val['modifier']='rcat_d';
                    Hook::listen('flat', $val);
                }
            }



            $set_list = DbCommoditySet::where(sprintf("`activity_image` <> '' and  `activity_start_time` <= '%s' AND  `is_enable` = 1 and  (`activity_end_time` >= '%s' or `activity_end_time` is null)", $now_time, $now_time))->select();

            if (!empty($set_list)) {
                // 更新flat表

                foreach ($set_list as $val) {
                    $map = ['commodity_set_id' => $val['id']];
                    $upd = ['activity_image' => $val['activity_image']];
                    DbCommodityFlat::where($map)->update($upd);
                }
            }


            #商品检查
            DbCommodity::where('live_refresh', 1)->chunk(100, function ($commdity_arr) {
                foreach ($commdity_arr as $one) {
                    if (empty($one['is_enable'])) {
                        $param5 = ['commodity_id' => $one['id'], 'shelves_type' => 5, 'is_delete' => true];
                        $param6 = ['commodity_id' => $one['id'], 'shelves_type' => 6, 'is_delete' => true];
                        $param7 = ['commodity_id' => $one['id'], 'shelves_type' => 7, 'is_delete' => true];
                        Hook::listen('flat_item', $param5);
                        Hook::listen('flat_item', $param6);
                        Hook::listen('flat_item', $param7);
                    } else {
                        DbCommoditySet::whereIn('shelves_type', [5, 6, 7])
                            ->where('commodity_id', $one['id'])->chunk(10, function ($arr) {
                                foreach ($arr as $set_info) {
                                    $set_info['commodity_set_id'] = $set_info['id'];
                                    $param                        = collection($set_info->getData())->toArray();
                                    $param['modifier']='rcat_c';
                                    unset($param['id']);
                                    Hook::listen('flat', $param);
                                }
                            });
                    }
                    DbCommodity::where('id', $one['id'])->update(['live_refresh' => 0]);
                }
            });

            $flat_id_arr = DbCommodityFlat::where('is_enable', 1)->whereIn('shelves_type', [5, 6, 7])
                ->column("commodity_id");

            #商品上架状态检查
            if (!empty($flat_id_arr)) {
                $flat_id_arr = array_unique($flat_id_arr);
                DbCommoditySet::where('is_enable', 1)
                    ->whereIn('shelves_type', [5, 6, 7])
                    ->whereNotIn('commodity_id', $flat_id_arr)
                    ->chunk(100, function ($set_arr) {
                        foreach ($set_arr as $set_item) {
                            $set_item['commodity_set_id'] = $set_item['id'];
                            $param                        = collection($set_item->getData())->toArray();
                            unset($param['id']);
                            $param['modifier']='rcat_f';

                            Hook::listen('flat', $param);
                        }
                    });
            }




        } catch (\Exception $e) {
            Log::error('refresh commodity all type: ' . $e->getMessage());
        }

        Logger::debug('cron refresh commodity all type end');
    }
}

