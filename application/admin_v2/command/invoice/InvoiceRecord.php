<?php

namespace app\admin_v2\command\invoice;

use tool\Logger;
use think\Exception;
use app\common\command\Base;
use app\common\model\db\InvoiceLogModel;
use app\common\model\db\InvoiceRecordModel;
use app\common\port\connectors\InvoiceHX;
use app\common\service\InvoiceRecordCallbackService;

class InvoiceRecord extends Base
{
    public static function doIt()
    {
        Logger::debug('cron InvoiceRecord log start');
        static::loadConfig();

        self::getInvoice(1); // 蓝票
        self::getInvoice(2); // 红票
        self::getRedConfirmInfo(); // 红字确认单
    }


    /**
     * 获取红蓝票的详情
     */
    public static function getInvoice($type)
    {
        try {
            $invoice_record_model = new InvoiceRecordModel();
            // 查询蓝票3分钟之前的开票中的数据
            $threeMinutesAgo = date('Y-m-d H:i:s', strtotime('-3 minutes'));

            if ($type == 1) {
                // 蓝票
                $map = [
                    'invoice_data_status' => 1,
                    'record_date'         => ['<=', $threeMinutesAgo],
                    'is_enable'           => 1,
                ];
            } else {
                // 红票
                $map = [
                    'red_invoice_data_status' => 1,
                    'red_bill_status'         => ['neq', 0],
                    'red_record_date'         => ['<=', $threeMinutesAgo],
                    'is_enable'               => 1,
                ];
            }
            $invoice_record_model->where($map)->chunk(50, function ($invoice_records) use ($type) {
                $order_no_arr = array_column($invoice_records, 'order_no');
                if ($type == 1) {
                    $params = [
                        'orderNo'      => $order_no_arr, // 订单号
                        'sellerTaxnum' => $invoice_records[0]['seller_tax_num'], // 销方纳税人识别号
                        'invoiceType'  => 1,  // 类型 1:蓝票 2:红票
                    ];
                } else {
                    $params = [
                        'orderNo'      => $order_no_arr, // 订单号
                        'sellerTaxnum' => $invoice_records[0]['seller_tax_num'], // 销方纳税人识别号
                        'invoiceType'  => 2,  // 类型 1:蓝票 2:红票
                    ];
                }
                $log = new InvoiceLogModel();

//                $add    = [
//                    'request_url'    => 'salescore/lan/query-invoice.do',
//                    'request_params' => json_encode_cn($params),
//                    'creator'        => 'command-invoice',
//                ];
//                $log_id = $log->insertGetId($add);

                $re = InvoiceHX::create('invoice_hx')->queryInvoice($params);
//                $upd = ['response' => json_encode_cn($re)];
//                $log->where('id', $log_id)->update($upd);
                if ($re['code'] == 200) {
                    $callbackService = new InvoiceRecordCallbackService();

                    foreach ($re['data'] as $key => $datum) {
                        $datum['invoiceStatus'] = $datum['status'];
                        if ($type == 1) {
                            $datum['invoiceType'] = 1; // 蓝票
                        } else {
                            $datum['invoiceType'] = 3; // 红票
                            $datum['red_invoice_code']  = $datum['orderNo'];
                        }
                        $add     = [
                            'request_url'    => 'salescore/lan/query-invoice.do',
                            'request_params' => json_encode_cn($datum),
                            'creator'        => 'command-' . $type,
                            'order_no'       => $datum['orderNo']
                        ];
                        $logId   = $log->insertGetId($add);
                        $message = $callbackService->sendInvoiceCallbackProcess($datum);
                        if ($message->isSuccess()) {
                            $msg = '处理成功';
                        } else {
                            $msg = $message->getMessage();
                        }
                        $log->where('id', $logId)->update(['response' => $msg]);
                    }
                } else {
                    Logger::error('command-InvoiceRecord:' . json_encode_cn($re));
                }
            });
        } catch (Exception $e) {
            $msg = $e->getMessage();
            Logger::error('command-InvoiceRecord:' . $msg);
        }

    }


    /**
     * 红字确认单获取详情
     */
    public static function getRedConfirmInfo()
    {
        try {

            $threeMinutesAgo = date('Y-m-d H:i:s', strtotime('-3 minutes'));

            $map = [
                'red_bill_status' => 0, // 申请中
                'red_record_date' => ['<=', $threeMinutesAgo]
            ];

            $invoice_record_model = new InvoiceRecordModel();
            $service              = new InvoiceRecordCallbackService();
            $invoice_record_model->where($map)->chunk(50, function ($records) use ($service, $invoice_record_model) {
                foreach ($records as $datum) {
                    $re = InvoiceHX::create('invoice_hx')->redConfirmQuery($datum);
                    if ($re['code'] == 200) {
                        $info               = $re['data'][0];

                        $upd = [
                            'red_invoice_code' => $info['orderNo'], // 定时任务匹配订单
                        ];
                        $invoice_record_model->where('id', $datum['id'])->update($upd);
                        $info['billInfoNo'] = $info['billNo'] ?? '';
                        $info['orderNo']    = $datum['order_no'];
                        $service->runSendRedInvoiceBackType2($info);
                    }
                }
            });
        } catch (Exception $e) {
            $msg = $e->getMessage();
            Logger::error('command-getRedConfirmInfo:' . $msg);
        }
    }
}