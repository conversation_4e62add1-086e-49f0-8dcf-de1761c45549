<?php

/*** Created by demo.
 * PROJECT：php-wxmp-dealers
 * User: <EMAIL>
 * Date: 2025/3/27
 * Time: 09:41
 * remark:
 */

namespace app\admin_v2\service;

use app\common\model\db\DbOrderInvoice;
use app\common\model\db\DbTaxInvoiceType;
use app\common\model\db\InvoiceApplyModel;
use app\common\model\db\InvoiceRecordModel;
use app\common\port\connectors\InvoiceHX;
use think\Model;
use tool\Logger;


class InvoiceRecordService extends InvoiceService
{
    /**
     * @var InvoiceRecordModel
     */
    protected $model;

    public static function getInstance(): self
    {
        return new self();
    }

    public function __construct()
    {
        $this->model = new InvoiceRecordModel();
    }

    /**
     *
     * @param $param
     */
    public function getRecordList($param)
    {
        $this->model->alias('t1')->field([
            'id',
            'apply_id',
            'order_no as record_no',
            'order_number',
            'invoice_type',
            'invoice_head_type',
            'invoice_type_str',
            'relation_good_order_no',
            'invoice_status',
            'invoice_code',
            'invoice_number',
            'els_code',
            'record_date',
            'invoice_date',
            'red_invoice_code',
            'red_record_date',
            'buyer_name',
            'buyer_tax_num',
            'tax_amount',
            'tax_free_amount',
            'image_url',
            'pdf_url',
            'ofd_url',
            'picture_url',
            'is_red_invoice',
            'red_invoice_code',
            'red_invoice_date',
            'red_invoice_number',
            'red_els_code',
            'red_image_url',
            'red_pdf_url',
            'red_ofd_url',
            'red_picture_url',
            'invoice_id',
            'red_invoice_id',
            'red_message',
        ]);
        if (!empty($param['relation_good_order_no'])) {
//            $sql = "SELECT 1 FROM t_db_tax_invoice_apply_detail detail  WHERE detail.apply_id=t1.apply_id AND detail.good_order_no = '{$param["relation_good_order_no"]}' ";
//            $this->model->whereExists($sql);
            $this->model->where('relation_good_order_no', '=', $param['relation_good_order_no']);
        }
        if ($param['invoice_status'] ?? '') {
            $this->model->whereIn('invoice_status', self::getInvoiceStatusByDataStatus($param['invoice_status']));
        }
        if ($param['invoice_type_str'] ?? '') {
            $this->model->whereIn('invoice_type_str', explode(',', $param['invoice_type_str']));
        }

        if ($param['record_start_date'] ?? '' && $param['record_end_date'] ?? '') {
            $this->model->where('record_date', '>=', $param['record_start_date']);
            $this->model->where('record_date', '<=', $param['record_end_date']);
        }

        if ($param['invoice_start_date'] ?? '' && $param['invoice_end_date'] ?? '') {
            $this->model->where('invoice_date', '>=', $param['invoice_start_date']);
            $this->model->where('invoice_date', '<=', $param['invoice_end_date']);
        }

        if ($param['record_no'] ?? '') {
            $this->model->where('order_no', '=', $param['record_no']);
        }

        if ($param['buyer_name'] ?? '') {
            $this->model->whereLike('buyer_name', "%{$param['buyer_name']}%");
        }

        if ($param['red_invoice_start_date'] ?? '' && $param['red_invoice_end_date'] ?? '') {
            $this->model->where('red_invoice_date', '>=', $param['red_invoice_start_date']);
            $this->model->where('red_invoice_date', '<=', $param['red_invoice_end_date']);
        }

        if ($param['invoice_code'] ?? '') {
            $this->model->where("(invoice_code = '{$param['invoice_code']}' OR red_invoice_code = '{$param['invoice_code']}')");
        }

        if ($param['invoice_number'] ?? '') {
            $this->model->where("(invoice_number = '{$param['invoice_number']}' OR red_invoice_number = '{$param['invoice_number']}')");
        }

        if ($param['els_code'] ?? '') {
            $this->model->where("(els_code = '{$param['els_code']}' OR red_els_code = '{$param['els_code']}')");
        }

        $this->model->order('id', 'desc');
        $list = $this->getPaginatorList(($param['page'] ?? 1), ($param['pagesize'] ?? 10));

        foreach ($list->items() as &$item) {
            $item['invoice_status'] = $this->getInvoiceStatusByStatus($item['invoice_status']);
        }

        return $list;
    }

    /**
     * 使用 ID 进行开红票
     * @param $id
     * @return array|mixed|string
     * @throws \think\exception\DbException
     */
    public function redInoviceById($id)
    {
        $_model = $this->getDetailById($id);
        if (empty($_model)) {
            throw new \Exception('没有找到对应的记录');
        }

        if (!in_array($_model->invoice_status, [InvoiceRecordModel::INVOICE_SUCCESS, InvoiceRecordModel::INVOICE_RED_FAIL])) {
            throw new \Exception('未开票成功，无法进行开红票');
        }

        if ($_model->invoice_data_status != InvoiceRecordModel::INVOICE_REQUEST_SUCCESS) {
            throw new \Exception('未开蓝票成功，无法进行开红票');
        }

        return $this->invoiceRed($_model);
    }

    /**
     * 当前对象进行 开红票
     * @return array|mixed|string
     */
    public function invoiceRed(InvoiceRecordModel $_model)
    {
        if (in_array($_model->invoice_type_str, ['pc', 'bs'])) {
            return $this->invoiceRedSubmitForm($_model);
        }
        return [];
    }

    /**
     * 数电发票 + 普票 = 发票类型代码传 pc:数电普票(电子)    冲红使用3.46红字确认单接口
     */
    public function invoiceRedSubmitForm(InvoiceRecordModel $_model)
    {
        $callbackUrl = config('invoice_callback_url') . 'net_small/invoice_callback/sendRedConfirmInvoiceCallback';

        $param = [];
        //业务系统回调地址
        $param['bizCallbackUrl'] = $callbackUrl;
        //申请方身份：0 销方, 1 购方, 2二手车拍卖企业, 3二手车市场
        $param['applySource'] = 0;
        //冲红原因：1销货退回, 2开票有误, 3服务中止, 4销售折让
        $param['redReason'] = 2;
        //"税号(税号、组织编码其一必填)
        $param['taxNum'] = $_model->seller_tax_num;
        //对应蓝票数电号码（数电普票、数电专票都需要，蓝票为数电票必填）
        $param['blueInvoiceNumber'] = $_model->els_code;
        //蓝字增值税发票代码(如果蓝票为增值税发票，则增值税代号码必填)
//        $param['blueInvoiceCode'] = $_model->invoice_code;
        //蓝字增值税发票号码(如果蓝票为增值税发票，则增值税代号码必填)
//        $param['blueInvoiceNo'] = $_model->invoice_number;
        //p:电子增值税普通发票，c:增值税普通发票(纸票)，s:增值税专用发票，b:增值税电子专用发票
        $param['blueInvoiceLine'] = $_model->invoice_type_str;
        //销方名称
        $param['sellerName'] = $_model->seller_name;
        //销方税号
        $param['sellerTaxnum'] = $_model->seller_tax_num;
        //购方名称
        $param['buyerName'] = $_model->buyer_name;
        if ($_model->buyer_tax_num ?? '') {
            //购方税号
            $param['buyerTaxnum'] = $_model->buyer_tax_num;
        }
        $param['columnFirst'] = 'NI+商城';

        $param['details'] = [];
        $result_data = InvoiceHX::create('invoice_hx')->apply($param);
        $_update = [];

        if (isset($result_data['code']) && $result_data['code'] == 200) {
            $_update['red_invoice_id'] = $result_data['data'];
            $_update['fault_message'] = $result_data['message'];
            $_update['red_invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_WAITING;

            $_update['invoice_status'] = InvoiceRecordModel::INVOICE_RED_APPLY_SUCCESS;
            $_update['red_bill_status'] = 0; // 红字确认单 申请中

            $_update['red_record_date'] = date("Y-m-d H:i:s");
        } else {
            $_update['fault_message'] = $result_data['message'] ?? '';
            $_update['red_invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_FAIL;

            $_update['invoice_status'] = InvoiceRecordModel::INVOICE_RED_FAIL;
            $_update['red_bill_status'] = 4; // 红字确认单 申请失败
            $_update['red_record_date'] = date("Y-m-d H:i:s");
        }
        $_model->message = json_encode($result_data['message'] ?? '', JSON_UNESCAPED_UNICODE);
        if ($_update) $this->model->where('id', '=', $_model->id)->update($_update);
        return $result_data;
    }


    /**
     *
     * 重开发票，请求开票失败时，要重新开票
     * @param $param
     * @return array
     * @throws \think\exception\DbException
     */
    public function reTryInvoice($param): array
    {
        $param['apply_way'] = InvoiceApplyModel::APPLY_WAY_MANAGER;

        if (empty($param['id'] ?? '')) {
            throw new \Exception('请传入正确的记录 ID');
        }

        $invoiceRecord = $this->getDetailById($param['id']);
        if (empty($invoiceRecord)) {
            throw new \Exception('没有找到对应的开票记录');
        }

        if ($invoiceRecord['invoice_status'] != InvoiceRecordModel::INVOICE_FAIL) {
            return ['code' => 400, 'message' => '开票失败才能重新开票'];
        }

        $applyService = InvoiceApplyService::getInstance();

        $invoiceApply = $applyService->getDetailById($invoiceRecord['apply_id']);

        if (empty($invoiceApply)) {
            throw new \Exception('没有找到对应的申请记录');
        }

        $message = '';
        $save_data = [];

        if (!$applyService->checkParams($param, $save_data, $message)) {
            throw new \Exception($message);
        }

        //重新开票是已经申请通过的，只是修改了部分数据
//        $save_data['apply_invoice_status'] = InvoiceApplyModel::APPLY_STATUS_PASS;

        $apply_details = array_merge([], $save_data['details']);
        unset($save_data['details']);

        foreach ($apply_details as $apply_detail) {
            if (empty($apply_detail['id'])) {
                throw new \Exception("商品ID不能为空");
            }
            if (!is_numeric($apply_detail['id'])) {
                throw new \Exception("商品ID有误");
            }
        }

        try {
            $invoiceApply->startTrans();

            $save_data['last_updated_date'] = date('Y-m-d H:i:s');
            if ($save_data['apply_invoice_status'] != InvoiceApplyModel::APPLY_STATUS_PASS) {
                $save_data['public_message'] = '';
                $save_data['privacy_message'] = '';

                $save_data['apply_way'] = InvoiceApplyModel::APPLY_WAY_RETRY;
            }


            $save_data['created_date'] = date('Y-m-d H:i:s');

            if (!$invoiceApply->isUpdate()->save($save_data)) {
                $invoiceApply->rollback();
                return ['code' => 400, 'message' => '保存申请失败'];
            }

            if (!InvoiceApplyDetailService::getInstance()->saveList($invoiceApply->id, $apply_details)) {
                $invoiceApply->rollback();
                return ['code' => 400, 'message' => '保存商品失败'];
            }

            if ($invoiceApply->apply_invoice_status != InvoiceApplyModel::APPLY_STATUS_PASS) {
                $_record_data = [
                    'invoice_status' => InvoiceRecordModel::INVOICE_CHECKING
                ];
                $invoiceRecord->save($_record_data);
            }

            $invoiceApply->commit();

            if ($invoiceApply->invoice_type == InvoiceApplyModel::APPLY_TYPE_SPECIAL) {
                //如果是申请专票，则删除申请记录
                $invoiceRecord->delete();
                InvoiceHX::create('invoice_hx')->deleteInvoice($invoiceRecord->seller_tax_num,$invoiceRecord->order_no);

            } else {
//                $order_number = $this->getOrderNumber($invoiceRecord->record_no);
                // 重开使用原来的订单号
//                if ($order_number == 0) {
//                    $order_no = $invoiceRecord->record_no;
//                } else {
//                    $order_no = $invoiceRecord->record_no.str_pad($order_number, 2, "0", STR_PAD_LEFT);
//                }
//                $upd = [
//                    'order_number' => $order_number,
//                    'order_no' => $order_no,
//                ];
//                $invoiceRecord->save($upd);
                $this->updateRecordByApply($invoiceRecord,$invoiceApply,$message);
            }

        } catch (\Exception $e) {
            $invoiceApply->rollback();
            return ['code' => 400, 'message' => $e->getMessage()];
        }


        return ['code' => 200, 'message' => $message];
    }


    /**
     * 换开发票，开票成功后，更新换信息开票
     * @param $param
     * @param string $message
     * @return bool
     * @throws \think\exception\DbException
     */
    public function redoInvoice($param, &$message = ''): bool
    {
        $save_data = [];
        if (!in_array($param['invoice_head_type'] ?? '', [InvoiceApplyModel::APPLY_INVOICE_HEAD_PERSON, InvoiceApplyModel::APPLY_INVOICE_HEAD_COMPANY])) {
            $message = '请正确填写发票抬头';
            return false;
        }

        $save_data['invoice_head_type'] = $param['invoice_head_type'];

        if (empty($param['buyer_name'])) {
            $message = '请选择请填写购买人名称';
            return false;
        }

        $save_data['notify_phone'] = $param['notify_phone'] ?? '';

        $save_data['notify_email'] = $param['notify_email'] ?? '';

        if (is_string($param['buyer_materials_image'])) {
            $param['buyer_materials_image'] = explode(',', $param['buyer_materials_image']);
        }
        $save_data['buyer_materials_image'] = json_encode_cn($param['buyer_materials_image']);

        $save_data['buyer_name'] = $param['buyer_name'];

        if (!in_array($param['invoice_type'] ?? '', [InvoiceApplyModel::APPLY_TYPE_GENERAL, InvoiceApplyModel::APPLY_TYPE_SPECIAL])) {
            $message = "请正确填写发票类型";
            return false;
        }

        $save_data['invoice_type'] = $param['invoice_type'];

        if ($save_data['invoice_type'] == InvoiceApplyModel::APPLY_TYPE_SPECIAL) {
            //专票时
            foreach (['buyer_tax_num', 'buyer_bank', 'buyer_account', 'buyer_address', 'buyer_phone'] as $buyer_str) {
                if (empty($param[$buyer_str])) {
                    $message = '请选择请填写完整买方关键信息';
                    return false;
                }
            }
        }

        $save_data['buyer_tax_num'] = $param['buyer_tax_num'] ?? '';
        $save_data['buyer_bank'] = $param['buyer_bank'] ?? '';
        $save_data['buyer_account'] = $param['buyer_account'] ?? '';
        $save_data['buyer_address'] = $param['buyer_address'] ?? '';
        $save_data['buyer_phone'] = $param['buyer_phone'] ?? '';

        if (empty($param['id'])) {
            $message = '请传入正确的记录 ID';
            return false;
        }

        $_model = $this->getDetailById($param['id']);
        if (empty($_model)) {
            $message = '没有找到对应的记录';
            return false;
        }

        if ($_model['invoice_status'] != InvoiceRecordModel::INVOICE_SUCCESS) {
            $message = '开票成功才能换开发票';
            return false;
        }

        if ($_model->red_invoice_data_status == InvoiceRecordModel::INVOICE_REQUEST_WAITING) {
            $message = '开票冲红中';
            return false;
        }
        if ($_model->red_invoice_data_status == InvoiceRecordModel::INVOICE_REQUEST_SUCCESS) {
            $message = '开票已经冲红';
            return false;
        }


        if ($_model->invoice_type == InvoiceApplyModel::APPLY_TYPE_SPECIAL) {
            //原来的开票是专票时
            if (!isset($param['is_deducted'])) {
                $message = '专票要选择是否抵扣哦';
                return false;
            }

            if ($param['is_deducted'] == 1) {
                $message = "对于已抵扣的专用发票需要冲红的情况，应由购买方在增值税发票新系统中发起红字信息确认单，"
                    . "销售方在收到红字信息表并确认后，需根据信息表开具红字增值税专用发票，"
                    . "完成冲红，再走重开流程";
                return false;
            }
        }

        $update_data = [];
        $update_data['after_red_need_blue'] = 1;
        /**
         * 普票马上冲红，专票需要审核
         */
        if ($save_data['invoice_type'] == InvoiceApplyModel::APPLY_TYPE_GENERAL) {
            //普票马上冲红
            //对原来的票进行冲红
            $result = $this->invoiceRed($_model);
            if (!isset($result['code'])) {
                $message = '接口发生未知错误';
                return false;
            }

            if ($result['code'] != 200) {
                $message = $result['message'];
                return false;
            }
        } else {
            $update_data['invoice_status'] = InvoiceRecordModel::INVOICE_REPLACE;
        }

        $this->model->startTrans();

        //产生一条新的开票等待确认的申请
        if (!InvoiceApplyService::getInstance()->createInvoiceWaitingSubmitApply($_model->apply_id, $save_data, $message)) {
            $this->model->rollback();
            return false;
        }
        $this->model->where('id', '=', $_model->id)->update($update_data);
        $this->model->commit();


        if (isset($update_data['invoice_status']))
            DbOrderInvoice::updateOrderInvoiceStatus($_model->relation_good_order_no, InvoiceRecordService::getInstance()->getInvoiceStatusByStatus($update_data['invoice_status']));

        return true;
    }


    /**
     * @param $id
     * @param string $message
     * @return bool
     * @throws \think\exception\DbException
     * @throws \think\exception\PDOException
     */
    public function cancelRedoInvoice($id, &$message = '操作成功'): bool
    {
        $model = $this->getDetailById($id);
        if (empty($model)) {
            $message = '没有找到对应的记录';
            return false;
        }

        if ($model->invoice_status != InvoiceRecordModel::INVOICE_REPLACE) {
            $message = '记录并不在换开中';
            return false;
        }

        $this->model->startTrans();

        InvoiceApplyModel::where('parent_id', $model->apply_id)->delete();

        $data_update = [];
        $data_update['invoice_status'] = InvoiceRecordModel::INVOICE_SUCCESS;
        $data_update['after_red_need_blue'] = 0;
        $this->model->where('id', '=', $model->id)->update($data_update);

        $this->model->commit();
        return true;
    }


    /**
     * 开票信息及明细
     * @param $id
     * @return array
     */
    public function getRecord($id): array
    {
        $model = $this->getDetailById($id);
        if (empty($model)) {
            throw new \Exception('没有找到对应的记录');
        }
        $data = $model->toArray();

        $data['invoice_status_name'] = InvoiceRecordModel::INVOICE_STATUS[InvoiceRecordService::getInstance()->getInvoiceStatusByStatus($data['invoice_status'])] ?? '';
        $data['invoice_type_str_name'] = '';
        if ($data['invoice_type_str'] == 'bs') {
            $data['invoice_type_str_name'] = '数电专票(电子)';
        }
        if ($data['invoice_type_str'] == 'pc') {
            $data['invoice_type_str_name'] = '数电普票(电子)';
        }

        $data['details'] = InvoiceApplyDetailService::getInstance()->getInvoiceDetail($data['apply_id']);
        return $data;
    }

    public function getRecordListByIds($ids)
    {
        $this->model->whereIn('id', $ids);
        return $this->model->select();
    }

    public function getInvoiceTypeStrName($invoice_type_str): string
    {
        if ($invoice_type_str == 'bs') {
            return '数电专票(电子)';
        }
        if ($invoice_type_str == 'pc') {
            return '数电普票(电子)';
        }
        return '';
    }

    /**
     * 失败数量
     * @return int|string
     */
    public function failCount()
    {
        return $this->model->where('invoice_status', '=', InvoiceRecordModel::INVOICE_FAIL)->count();
    }

    /**
     * 根据ID开蓝票
     * @param $id
     * @return array|mixed|string
     */
    public function blueInvoiceById($id, &$message = '')
    {
        $model = $this->getDetailById($id);
        if (empty($model)) {
            throw new \Exception('没有找到对应的记录');
        }
        if (in_array($model->invoice_status, [InvoiceRecordModel::INVOICE_SUCCESS, InvoiceRecordModel::INVOICE_RED_SUCCESS])) {
            throw new \Exception('已经开票，无法继续进行红票');
        }
        return $this->invoiceBlue($model, $message);
    }

    /**
     * 根据 ID 查询对象进行开蓝票
     * @param $id
     * @return array|mixed|string
     */
    public function createRecordByApplyId($applyId, &$message = '操作成功')
    {
        $apply = InvoiceApplyService::getInstance()->getDetailById($applyId);
        if (empty($apply)) {
            $message = '没有找到对应的记录';
            return [];
        }
        return $this->createRecordByApply($apply, $message);
    }


    /**
     * 重开蓝票
     * @param InvoiceRecordModel $model
     * @param InvoiceApplyModel $apply
     * @param string $message
     * @return array|mixed|string
     */
    public function updateRecordByApply(InvoiceRecordModel $model, InvoiceApplyModel $apply, &$message = '操作成功')
    {
        if ($apply->apply_invoice_status != InvoiceApplyModel::APPLY_STATUS_PASS) {
            $message = '开票申请审核中';
            return [];
        }
        $data = [];
//        $record_no = $this->getRecordNo($apply['id']);
//        $order_number = $this->getOrderNumber($record_no);
        // 修改order_no
//        if ($order_number == 0) {
//            $order_no = $record_no;
//        } else {
//            $order_no = $record_no.str_pad($order_number, 2, "0", STR_PAD_LEFT);
//        }

        $data['apply_id'] = $apply['id'];
//        $data['record_no'] = $this->getRecordNo($apply['id']);
//        $data['order_number'] = $this->getOrderNumber($data['record_no']);
//        $data['record_no'] = $record_no;
//        $data['order_number'] = $order_number;
        $data['invoice_type'] = $apply['invoice_type'];
        $data['invoice_type_str'] = $apply['invoice_type_str'];
        $data['invoice_head_type'] = $apply['invoice_head_type'];
        $data['relation_good_order_no'] = $apply['relation_good_order_no'];
        $data['notify_phone'] = $apply['notify_phone'] ?? '';
        $data['notify_email'] = $apply['notify_email'] ?? '';
        $data['tax'] = $apply['tax'];
        $data['tax_amount'] = $apply['tax_amount'];
        $data['total_cost_amount'] = $apply['total_cost_amount'];
        $data['tax_free_amount'] = $apply['tax_free_amount'];
        $data['invoice_status'] = InvoiceRecordModel::INVOICE_APPLY;
        $data['invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_NONE;
        $data['buyer_name'] = $apply['buyer_name'];
        $data['buyer_tax_num'] = $apply['buyer_tax_num'] ?? '';
        $data['buyer_bank'] = $apply['buyer_bank'] ?? '';
        $data['buyer_account'] = $apply['buyer_account'] ?? '';
        $data['buyer_address'] = $apply['buyer_address'] ?? '';
        $data['buyer_phone'] = $apply['buyer_phone'] ?? '';
        $data['seller_name'] = $apply['seller_name'];
        $data['seller_tax_num'] = $apply['seller_tax_num'];
        $data['seller_bank'] = $apply['seller_bank'];
        $data['seller_account'] = $apply['seller_account'];
        $data['seller_address'] = $apply['seller_address'];
        $data['seller_phone'] = $apply['seller_phone'];
        $data['seller_payee'] = $apply['seller_payee'] ?? '';
        $data['seller_clerker'] = $apply['seller_clerker'] ?? '';
        $data['seller_checker'] = $apply['seller_checker'] ?? '';
//        $data['order_no'] = $order_no;

        Logger::info('updateRecordByApply', json_encode($data));
        if ($this->model->where('id', '=', $model->id)->update($data)) {
            try {
                // 删除发起失败的记录
                $result_data = InvoiceHX::create('invoice_hx')->deleteInvoice($model->seller_tax_num,$model->order_no);

                return $this->blueInvoiceById($model->id, $message);
            } catch (\Exception $e) {
                $message = $e->getMessage();
                Logger::error('updateRecordByApply:',$message);
            }
        }

        return [];
    }

    /**
     * 使用对象开蓝票
     * @param InvoiceApplyModel $apply
     * @return array
     */
    public function createRecordByApply(InvoiceApplyModel $apply, &$message = '操作成功'): array
    {
        if ($apply->apply_invoice_status != InvoiceApplyModel::APPLY_STATUS_PASS) {
            $message = '开通申请审核中';
            return [];
        }

        $record_no = $this->getRecordNo($apply->id);
        $order_number = $this->getOrderNumber($record_no);
        // 修改order_no
        if ($order_number == 0) {
            $order_no = $record_no;
        } else {
            $order_no = $record_no.str_pad($order_number, 2, "0", STR_PAD_LEFT);
        }
        $data = [
            'apply_id' => $apply->id,
            'record_no' => $record_no,
            'order_number' => $order_number,
            'invoice_type' => $apply->invoice_type,
            'invoice_type_str' => $apply->invoice_type_str,
            'invoice_head_type' => $apply->invoice_head_type,
            'relation_good_order_no' => $apply->relation_good_order_no,
            'notify_phone' => $apply->notify_phone,
            'notify_email' => $apply->notify_email,
            'tax' => $apply->tax,
            'tax_amount' => $apply->tax_amount,
            'total_cost_amount' => $apply->total_cost_amount,
            'tax_free_amount' => $apply->tax_free_amount,
            'invoice_status' => InvoiceRecordModel::INVOICE_APPLY,
            'invoice_data_status' => InvoiceRecordModel::INVOICE_REQUEST_NONE,
            'buyer_name' => $apply->buyer_name,
            'buyer_tax_num' => $apply->buyer_tax_num,
            'buyer_bank' => $apply->buyer_bank,
            'buyer_account' => $apply->buyer_account,
            'buyer_address' => $apply->buyer_address,
            'buyer_phone' => $apply->buyer_phone,
            'seller_name' => $apply->seller_name,
            'seller_tax_num' => $apply->seller_tax_num,
            'seller_bank' => $apply->seller_bank,
            'seller_account' => $apply->seller_account,
            'seller_address' => $apply->seller_address,
            'seller_phone' => $apply->seller_phone,
            'seller_payee' => $apply->seller_payee,
            'seller_clerker' => $apply->seller_clerker,
            'seller_checker' => $apply->seller_checker,
            'order_no' => $order_no, // 新增
        ];

        $data['last_updated_date'] = date('Y-m-d H:i:s');

        $id = $this->model->insertGetId($data);
        Logger::info('createRecordByApply:',json_encode_cn($data));
        if ($id) {
            try {
                return $this->blueInvoiceById($id, $message);
            } catch (\Exception $e) {
                $message = $e->getMessage();
                Logger::error('createRecordByApply:',$message);
            }
        }
        $message = '保存失败';
        return [];
    }

    /**
     * 当前对象进行 开蓝票
     * @return array|mixed|string
     */
    public function invoiceBlue(InvoiceRecordModel $model, &$message = '')
    {
        return $this->invoice($model, 1, $message);
    }

    /**
     * 当前对象进行 开红、蓝票
     * @param $invoice_type
     * @return array|mixed|string
     */
    public function invoice(InvoiceRecordModel $model, $invoice_type, &$message = '')
    {
        $callbackUrl = config('invoice_callback_url') . 'net_small/invoice_callback/sendInvoiceCallback';
        $params = [];
        $params['bizCallbackUrl'] = $callbackUrl;

        $params['invoiceLine'] = $model->invoice_type_str;

        $substring = substr($model->order_no, 0, 7);
        $gwsm = 'GWSM';
        $gwapp = 'GWAPP';
        $qcsm = 'QCSM';
        $qcapp = 'QCAPP';
        $columnFirst = '';
        if ((strpos($substring, $gwsm) !== false) || (strpos($substring, $gwapp) !== false)) {
            $columnFirst = '日产商城';
        }
        if ((strpos($substring, $qcsm) !== false) || (strpos($substring, $qcapp) !== false)) {
            $columnFirst = '启辰商城';
        }
        //业务方个性化字段,本应用只作保存。
        $params['columnFirst'] = $columnFirst;
        //业务方个性化字段,本应用只作保存。
        $params['columnSecond'] = $this->dealer_code ?? '';
        //对购方税号校验：0-不校验（若税号在电子税局中不存在，也能继续开票），1校验（若税号在电子税局中不存在则不能开票）  若不传，则默认取开票规则配置，若传了则以传入的为准
        $params['taxNumVerifyFlag'] = 0;
        //数电电票是否展示购销方银行账户信息：0全不显示1仅显示销方2仅显示购方3全部显示
        $params['showBankAccountType'] = 0;
        //数电发票收款人复核人是否显示:0否1是
        $params['showPayeeCheckerFlag'] = 0;
        //特定要素：0普通发票 01成品油 02稀土 03建筑服务 04货物运输发票 05不动产销售发票 06不动产租赁发票09旅客运输发票12自产农产品销售 14机动车 15-二手车 16农产品收购 24-报废产品收购 33二手车反向开具 35 矿产品发票 51数电二手机动车正常开具 52数电二手机动车反向开具
        $params['specificFactor'] = 0;
        //代开标志，0:非代开;1:代开。代开蓝票备注文案要求包含：“代开企业税号:***代开企业名称:***.”；代开红票备注文案要求：“对应正数发票代码:***号码:***代开企业税号:***代开企业名称:***.”。（代开企业税号与代开企业名称之间仅支持一个空格或无符号
        $params['substituteFlag'] = 0;
        //请求来源，0 :api接口，可自定义，但必须是诺税通已维护好的枚举值  默认为0
        $params['requestSrc'] = 0;
        //推送方式，-1:不推送;0:邮箱;1:手机(默认);2:邮箱、手机
        $params['notifyType'] = -1;
        //
        $params['buyerName'] = $model->buyer_name;
        $params['buyerPhone'] = $model->buyer_phone ?? '';
        $params['buyerTaxnum'] = $model->buyer_tax_num ?? '';
        $params['buyerAddress'] = $model->buyer_address ?? '';
        $params['buyerBank'] = $model->buyer_bank ?? '';
        $params['buyerAccount'] = $model->buyer_account ?? '';

        $params['sellerName'] = $model->seller_name;
        $params['sellerTaxnum'] = $model->seller_tax_num;
        $params['sellerAddress'] = $model->seller_address;
        $params['sellerPhone'] = $model->seller_phone;
        $params['sellerBank'] = $model->seller_bank;
        $params['sellerAccount'] = $model->seller_account;
        $params['payee'] = $model->seller_payee;
        $params['clerker'] = $model->seller_clerker;
        $params['checker'] = $model->seller_checker;

        //清单标志，0:非清单；1:清单，纸票超过8行自动转成清单，电票无清单概念，默认都传0  默认为0
        $params['listFlag'] = 0;
//        if ($invoice_type == 2) {
//            if ($model->order_number == 1) {
//                $params['orderNo'] = "ch" . $model->record_no;
//            } else {
//                $params['orderNo'] = "ch" . $model->record_no . str_pad($model->order_number, 2, '0', STR_PAD_LEFT);
//            }
//            //蓝票 0-蓝字发票 1-红字发票
//            $params['checkBlue'] = 1;
//            //开票类型，1:蓝票；2:红票
//            $params['invoiceType'] = 2;
//
//            //冲红原因 1：销售退回 2：开票有误 3：服务终止 4：销售折让 当发票种类为：c/p/f/r/e的红字发票时填写
//            $params['redReason'] = 2;
//        } else {
//            if ($model->order_number == 1) {
//                $params['orderNo'] = $model->record_no;
//            } else {
//                $params['orderNo'] = $model->record_no . str_pad($model->order_number, 2, '0', STR_PAD_LEFT);
//            }
        $params['orderNo'] = $model->order_no;
            //蓝票 0-蓝字发票 1-红字发票
            $params['checkBlue'] = 0;
            //开票类型，1:蓝票；2:红票
            $params['invoiceType'] = 1;


            if ('pc' == $model->invoice_type_str) {
                //数电发票 + 普票 = 发票类型代码传 pc:数电普票(电子)    冲红使用3.46红字确认单接口

                //开票时抬头类型若选择为“个人”，且为数电普票时，则传1，数电专票和数电普票选企业时传0；增值税发票无论什么类型都不用传
                $params['naturalPersonFlag'] = 0;

                //购方自然人标志传1时，传0；购方自然人标志传0时，传1，自然人标志为空时不用传
                $params['naturalPersonVerifyFlag'] = 0;
            }
            if ('bs' == $model->invoice_type_str) {
                //数电发票 + 专票 = 发票类型代码传 bs:数电专票(电子)   冲红使用3.46红字确认单接口
                $params['naturalPersonFlag'] = 0;
            }


            if ($this->notify_email ?? '') {
                $params['notifyType'] = 0;
                $params['notifyEmail'] = $model->notify_email;
            }
            if ($this->notifyPhone ?? '') {
                $params['notifyType'] = 1;
                $params['notifyEmail'] = $model->notify_phone;
            }
//        }

        $params['bizCallbackUrl'] = $callbackUrl;
        $params['detail'] = $this->formatDetail($model, $invoice_type);

        $result_data = InvoiceHX::create('invoice_hx')->Invoice($params);

        $_update = [];

        $message = $result_data['message'] ?? '';

        if (isset($result_data['code']) && $result_data['code'] == 200) {
            //请求成功
            if ($invoice_type == 2) {
                //红票时
                $_update['red_invoice_id'] = $result_data['data'];
                $_update['fault_message'] = $result_data['message'] ?? '已请求开票';
                $_update['red_invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_WAITING;

                $_update['invoice_status'] = InvoiceRecordModel::INVOICE_RED_APPLY_SUCCESS;

                $_update['red_record_date'] = date("Y-m-d H:i:s");
                //更新订单状态
            } else {
                //蓝票时
                $_update['invoice_id'] = $result_data['data'];
                $_update['fault_message'] = $result_data['message'] ?? '已请求开票';
                $_update['invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_WAITING;
                $_update['invoice_status'] = InvoiceRecordModel::INVOICE_APPLY_SUCCESS;
                $_update['record_date'] = date("Y-m-d H:i:s");
                //更新订单状态
            }
        } else {
            //请求失败
            if ($invoice_type == 2) {
                $_update['fault_message'] = $result_data['message'] ?? '请求冲红失败';
                $_update['red_invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_FAIL;
                $_update['invoice_status'] = InvoiceRecordModel::INVOICE_RED_FAIL;
                $_update['red_record_date'] = date("Y-m-d H:i:s");
            } else {
                $_update['fault_message'] = $result_data['message'] ?? '请求开票失败';
                $_update['invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_FAIL;
                $_update['invoice_status'] = InvoiceRecordModel::INVOICE_FAIL;
                $_update['record_date'] = date("Y-m-d H:i:s");
            }
        }

        $_update['message'] = json_encode($result_data, JSON_UNESCAPED_UNICODE);

        if ($_update) $this->model->where('id', '=', $model->id)->update($_update);

        if (isset($_update['invoice_status']))
            DbOrderInvoice::updateOrderInvoiceStatus($model->relation_good_order_no, $this->getInvoiceStatusByStatus($_update['invoice_status']));

        return $result_data;
    }

    /**
     * @param $invoice_type
     * @return array
     */
    public function formatDetail(InvoiceRecordModel $model, $invoice_type): array
    {
        $detail_list = [];
        $index = 0;
        if (in_array($model->invoice_type_str, ['bs', 'pc'])) {
            $index = 1;
        }

        $details = InvoiceApplyDetailService::getInstance()->getInvoiceDetail($model->apply_id);

        foreach ($details as $detail) {
            $detail_one = [];
            if ($invoice_type == 2) {
                //红票数据组织
                if ($detail['good_row_type'] == 3) {
                    continue;
                }
                if ($detail['good_row_type'] == 2) {
                    $this->processDiscountedApplyDetail($detail, $details);
                }
            }

            $detail_one['detailIndex'] = $index++;
            //含税标志，0:不含税,1:含税，必填
            $detail_one['withTaxFlag'] = 1;
            $detail_one['columnFive'] = $detail['id'];
            $detail_one['columnFirst'] = !empty($detail['ms_order_code2'])?"{$detail['good_order_no']}_2":'';
            $detail_one['columnSecond'] ='';
            $detail_one['goodsName'] = $detail['good_name'];
            //数量，数量、单价必须都不填，或都必填，不可只填一个；当数量、单价都不填时，不含税金额、税额、含税金额都必填；
            //当数量、单价都填时，不含税金额、税额、含税金额可为空。建议保留小数点后8位以上。开具红字发票时，数量为负数
            $detail_one['goodsNum'] = $detail['good_num'];
            //单位，开具成品油发票时必填，必须为”升”或“吨”
            $detail_one['goodsUnit'] = $detail['good_unit'];
            //税率
            $detail_one['taxrate'] = $detail['good_tax_rate'];
            //规格型号 最多只能40个长度
            $specType = substr(trim($detail['good_spec_type']), 0, 40);

            $detail_one['specType'] = $specType;
            //税收分类编码，可不传入，由接口进行智能匹配，如对接口自动匹配准确率要求高的企业，建议传入
            $detail_one['goodsCode'] = $detail['good_tax_code'] ?? '';
            //
            $detail_one['columnThree'] = $detail['good_sku_code'] ?? '';
            //含税单价，当含税标志为：含税且不含税金额、税额、含税金额未全部传入时，必填，数量和单价必须都不填或者都必填，不可只填一个。
            //冲红时项目单价为正数建议保留小数点后8位以上，减少计算误差。
            $detail_one['goodsPrice'] = $detail['good_price'];
            //不含税金额，精确到小数点后面两位，红字发票为负数。不含税金额、税额、含税金额任何一个不传时，会根据传入的单价，数量进行计算，
            //可能和实际数值存在误差，建议都传入
            $detail_one['taxFreeAmount'] = strval($detail['tax_free_amount']);
            //含税金额，精确到小数点后面两位，红字发票为负数。不含税金额、税额、含税金额任何一个不传时，会根据传入的单价，
            //数量进行计算，可能和实际数值存在误差，建议都传入
            $detail_one['taxAmount'] = strval($detail['tax_amount']);
            //税额，精确到小数点后面两位，红字发票为负数。不含税金额、税额、含税金额任何一个不传时，会根据传入的单价，数量进行计算，可能和实际数值存在误差，建议都传入
            $detail_one['tax'] = strval($detail['tax']);

            if ($detail_one['taxAmount'] == 0.01) {
                $detail_one['tax'] = null;
                $detail_one['taxFreeAmount'] = null;
            }
            if ($detail_one['taxAmount'] == -0.01) {
                $detail_one['tax'] = 0;
                $detail_one['taxFreeAmount'] = $detail_one['taxAmount'];
            }

            if ($detail['good_row_type'] == 1) {
                //正常行
                $detail_one['detailType'] = 0;
            }

            if ($detail['good_row_type'] == 2) {
                //被折扣行
                $detail_one['detailType'] = 2;
            }

            if ($detail['good_row_type'] == 3) {
                //折扣行
                $detail_one['detailType'] = 1;
                $detail_one['goodsUnit'] = null;
                $detail_one['goodsPrice'] = null;
                $detail_one['goodsNum'] = null;
                $detail_one['specType'] = null;
            }
            //优惠政策标识，0:不使用;1:使用，数电发票时为空，仅传入优惠政策编码即可
            $detail_one['favouredPolicyFlag'] = 0;

            if (in_array($model->invoice_type_str, ['bs', 'pc'])) {
                $detail_one['favouredPolicyFlag'] = null;
            }

            if (!empty($detail_one['goodsCode'])) {
                $invoiceTypeInstance = new DbTaxInvoiceType();
                $invoiceType = $invoiceTypeInstance->findByCode($detail_one['goodsCode']);
                if (!empty($invoiceType)) {
                    if (in_array($model->invoice_type_str, ['bs', 'pc'])) {
                        //数电发票
                        $detail_one['favouredPolicyFlag'] = null;
                        $detail_one['favouredPolicyCode'] = $invoiceType['policy_id'];
                    } else {
                        //税盘发票不处理
                    }
                }
                $invoiceTypeInstance = null;
            }
            $detail_list[] = $detail_one;
        }

        return $detail_list;
    }

    /**
     * 合并被折扣行+折扣行
     * @param $detail
     * @param $details
     */
    public function processDiscountedApplyDetail(&$detail, $details)
    {
        foreach ($details as $detailTmp) {
            if ($detailTmp['good_row_type'] != 3) {
                continue;
            }
            if ($detailTmp['good_id'] == $detail['good_id'] && $detailTmp['good_order_no'] == $detail['good_order_no']) {
                $detail['tax_free_amount'] = doubleval($detail['tax_free_amount']) + doubleval($detailTmp['tax_free_amount']);
                $detail['tax'] = doubleval($detail['tax']) + doubleval($detailTmp['tax']);
                $detail['tax_amount'] = doubleval($detail['tax_amount']) + doubleval($detailTmp['tax_amount']);
            }
        }
    }

    /**
     * 开票记录号，非商城订单号，每次开票都唯一，
     * 规则：开票记录内包含的第一个订单号+包含的订单数（两位）+公司编码
     * 如DFS2410211044214188801DNDC
     * 若同一批订单多次换开，则在最后加上自增两位数，如DFS2410211044214188801DNDC01
     * @param $apply_id
     * @return string
     */
    public static function getRecordNo($apply_id): string
    {
        $details = InvoiceApplyDetailService::getInstance()->getInvoiceDetail($apply_id);
        $first_good_order_no = '';
        $good_order_nos = [];
        foreach ($details as $detail) {
            if (empty($first_order_no)) {
                $first_good_order_no = $detail['good_order_no'];
            }
            if (!in_array($detail['good_order_no'], $good_order_nos)) {
                $good_order_nos[] = $detail['good_order_no'];
            }
        }

        $count = count($good_order_nos);
        // 格式化 $count 为两位数
        $formatted_count = str_pad($count, 2, '0', STR_PAD_LEFT);
        return $first_good_order_no . $formatted_count . 'DNDC';
    }

    /**
     * @param $record_no
     */
    public function getOrderNumber($record_no)
    {
        return $this->model->where('record_no', '=', $record_no)->count();
    }

    /**
     * @param string $status_str
     * @return array
     */
    public static function getInvoiceStatusByDataStatus($status_str)
    {
        $return_arr = [];
        $status_str_arr = explode(',', $status_str);
        foreach (InvoiceRecordModel::INVOICE_STATUS_TO_DATA as $key => $value) {
            if (in_array($key, $status_str_arr)) {
                $return_arr = array_merge($return_arr, $value);
            }
        }
        return $return_arr;
    }

    /**
     * @param $status
     * @return int
     */
    public function getInvoiceStatusByStatus($status): int
    {
        foreach (InvoiceRecordModel::INVOICE_STATUS_TO_DATA as $key => $value) {
            if (in_array($status, $value)) {
                return $key;
            }
        }
        return 0;
    }

    /**
     * @param $applyId
     */
    /**
     * @param $applyId
     * @return array|bool|\PDOStatement|string|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getRecordByApplyId($applyId)
    {
        $this->model->order('id desc');
        $this->model->where('apply_id', $applyId);
        return $this->model->find();
    }

    /**
     * @param $applyId
     * @return array|bool|\PDOStatement|string|Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getWaitRedRecordByApplyId($applyId)
    {
        $this->model->where('after_red_need_blue', 1);
        return $this->getRecordByApplyId($applyId);
    }


}