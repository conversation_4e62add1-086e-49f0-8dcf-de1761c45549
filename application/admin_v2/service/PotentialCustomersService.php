<?php


namespace app\admin_v2\service;


use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbRecommendHotCommodity;
use app\common\model\db\DbRecommendInfomercial;
use app\common\model\db\DbRecommendJumpLink;
use app\common\return_data\JsonBuilder;
use app\common\return_data\MessageBag;

class PotentialCustomersService
{

    private $jump_link_model;
    private $hot_commodity_model;
    private $infomercial;

    public function __construct()
    {
        $this->jump_link_model     = new DbRecommendJumpLink();
        $this->hot_commodity_model = new DbRecommendHotCommodity();
        $this->infomercial         = new DbRecommendInfomercial();
    }


    public function saveLink($input, $admin_name)
    {
        // 删除
        $map = ['activity_type' => 3];
        $this->jump_link_model->where($map)->delete();
        if (!empty($input['sm_jump_type'])) {
            $sm_add = [
                'activity_type' => 3,
                'channel'       => 1,
                'jump_type'     => $input['sm_jump_type'],
                'path'          => $input['sm_path'],
                'creator'       => $admin_name
            ];
            $this->jump_link_model->insert($sm_add);
        }
        if (!empty($input['app_jump_type'])) {
            $app_add = [
                'activity_type' => 3,
                'channel'       => 2,
                'jump_type'     => $input['app_jump_type'],
                'path'          => $input['app_path'],
                'creator'       => $admin_name
            ];
            $this->jump_link_model->insert($app_add);
        }
        return true;
    }


    /**
     * 获取链接
     * @return bool|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getLink()
    {
        $map   = ['activity_type' => 3];
        $field = 'channel, jump_type, path';
        $list  = $this->jump_link_model->where($map)->field($field)->select();
        foreach ($list as $item) {
            $item['remark'] = $item->remark;
        }
        return $list;
    }


    /**
     * 查询上架商品
     * @param $input
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getSetCommodities($input)
    {
        $map = ['a.shelves_type' => 5]; // 日产
        if (!empty($input['comm_type_id'])) {
            if ($input['level'] == 3) {
                $map['b.comm_type_id'] = $input['comm_type_id'];
            }
            if ($input['level'] == 2) {
                $arr                   = $this->getThreeCommTypeIdByTwo($input['comm_type_id']);
                $map['b.comm_type_id'] = ['in', $arr];
            }
            if ($input['level'] == 1) {
                $arr                   = $this->getThreeCommTypeIdByOne($input['comm_type_id']);
                $map['b.comm_type_id'] = ['in', $arr];
            }
        }

        if (!empty($input['commodity_name'])) {
            $map['b.commodity_name'] = ['like', '%' . $input['commodity_name'] . '%'];
        }

        $field                = 'a.commodity_id,b.commodity_name,b.cover_image,b.is_grouped,b.comm_type_id,e.comm_type_name as one_comm_type_name,
         d.comm_type_name as tow_comm_type_name, c.comm_type_name as three_comm_type_name';
        $commodity_flat_model = new DbCommodityFlat();
        $list                 = $commodity_flat_model->alias('a')->with(['potCustomerCommodity'])
            ->join('t_db_commodity b', 'a.commodity_id = b.id')
            ->join('t_db_commodity_type c', 'b.comm_type_id = c.id')
            ->join('t_db_commodity_type d', 'c.comm_parent_id = d.id')
            ->join('t_db_commodity_type e', 'd.comm_parent_id = e.id')
            ->field($field)
            ->where($map)
            ->order('a.updated_at desc')
            ->paginate($input['pagesize']);
        foreach ($list as $key => $item) {
            $item['set_enable'] = 1;
            $is_add             = 0;
            if (!empty($item['pot_customer_commodity'])) {
                $is_add = 1;
            }
            $item['is_add'] = $is_add;
            unset($item['pot_customer_commodity']);
        }
        return $list;
    }


    /**
     * 二级id获取三级id
     * @param $id
     * @return array|false|string
     */
    public function getThreeCommTypeIdByTwo($id)
    {
        $map        = ['comm_parent_id' => $id, 'level' => 3];
        $type_model = new DbCommodityType();
        return $type_model->where($map)->column('id');
    }


    /**
     * 根据一级id获取三级id
     * @param $id
     * @return array
     */
    public function getThreeCommTypeIdByOne($id)
    {
        $map        = ['comm_parent_id' => $id, 'level' => 2];
        $type_model = new DbCommodityType();
        $twoIdArr   = $type_model->where($map)->column('id');
        $threeIdArr = [];
        foreach ($twoIdArr as $twoId) {
            $arr = $this->getThreeCommTypeIdByTwo($twoId);
            if (!empty($arr)) {
                $threeIdArr = array_merge($threeIdArr, $arr);
            }
        }
        return $threeIdArr;

    }


    /**
     * 保存商品
     * @param $commodityList
     * @param $admin_name
     * @return false|int|string
     */
    public function saveCommodity($commodityList, $admin_name)
    {
        // 获取现在有多少个商品
        $map = ['recommend_type' => 2];
        $num = $this->hot_commodity_model->where($map)->count();

        $add = [];
        foreach ($commodityList as $key => $commodityId) {
            $add[] = [
                'recommend_type' => 2,
                'commodity_id'   => $commodityId,
                'sort'           => ($num + $key + 1), // 排序
                'creator'        => $admin_name
            ];
        }
        return $this->hot_commodity_model->insertAll($add);
    }


    /**
     * 获取潜客商品
     * @param $input
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getPageCommodity($input)
    {
        $map = ['a.recommend_type' => 2]; // 潜客商品
        if (!empty($input['comm_type_id'])) {
            if ($input['level'] == 3) {
                $map['b.comm_type_id'] = $input['comm_type_id'];
            }
            if ($input['level'] == 2) {
                $arr                   = $this->getThreeCommTypeIdByTwo($input['comm_type_id']);
                $map['b.comm_type_id'] = ['in', $arr];
            }
            if ($input['level'] == 1) {
                $arr                   = $this->getThreeCommTypeIdByOne($input['comm_type_id']);
                $map['b.comm_type_id'] = ['in', $arr];
            }
        }
        if (!empty($input['commodity_name'])) {
            $map['b.commodity_name'] = ['like', '%' . $input['commodity_name'] . '%'];
        }
        if (isset($input['is_grouped']) && $input['is_grouped'] != '') {
            $map['b.is_grouped'] = $input['is_grouped'];
        }
        if (isset($input['set_enable']) && $input['set_enable'] != '') {
            $map['f.is_enable'] = $input['set_enable'];
        }

        if (isset($input['commodity_id']) && $input['commodity_id'] != '') {
            $map['a.commodity_id'] = $input['commodity_id'];
        }

        $field = 'a.id,a.commodity_id,a.sort,b.commodity_name,b.cover_image,b.is_grouped,b.comm_type_id,e.comm_type_name as one_comm_type_name,
         d.comm_type_name as tow_comm_type_name, c.comm_type_name as three_comm_type_name,f.is_enable as set_enable';
        return $this->hot_commodity_model->alias('a')
            ->join('t_db_commodity b', 'a.commodity_id = b.id')
            ->join('t_db_commodity_set f', 'a.commodity_id=f.commodity_id and shelves_type=5')
            ->join('t_db_commodity_type c', 'b.comm_type_id = c.id')
            ->join('t_db_commodity_type d', 'c.comm_parent_id = d.id')
            ->join('t_db_commodity_type e', 'd.comm_parent_id = e.id')
            ->field($field)
            ->where($map)
            ->order('a.sort')
            ->paginate($input['pagesize']);
    }


    /**
     * 删除
     * @param $id
     * @return MessageBag
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function delCommodity($id)
    {
        $map  = ['id' => $id, 'recommend_type' => 2];
        $info = $this->hot_commodity_model->where($map)->find();

        $messageBag = new MessageBag();

        if (empty($info)) {
            $messageBag->setMessage('已删除');
        } else {
            $re = $this->hot_commodity_model->where('id', $id)->delete();
            if ($re) {
                $messageBag->setMessage('删除成功');
            } else {
                $messageBag->setCode(JsonBuilder::CODE_ERROR);
                $messageBag->setMessage('删除失败');
            }
        }
        return $messageBag;
    }


    /**
     * 商品排序
     * @param $sortCommodities
     * @param $admin_name
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function sortCommodity($sortCommodities, $admin_name)
    {
        foreach ($sortCommodities as $item) {
            $map = [
                'commodity_id'   => $item['commodity_id'],
                'recommend_type' => 2,
            ];
            $upd = [
                'sort'          => $item['sort'],
                'modifier'      => $admin_name,
                'modified_date' => date('Y-m-d H:i:s'),
            ];
            $this->hot_commodity_model->where($map)->update($upd);
        }
        $map   = ['recommend_type' => 2];
        $order = 'sort asc,modified_date desc';
        $list  = $this->hot_commodity_model->where($map)->order($order)->select();
        foreach ($list as $key => $item) {
            $map = ['id' => $item['id']];
            $upd = ['sort' => ($key + 1)];
            $this->hot_commodity_model->where($map)->update($upd);
        }
        return true;
    }


    /**
     * 保存广告
     * @param $input
     * @param $admin_name
     * @return DbRecommendInfomercial|int|string
     */
    public function saveAdvertisement($input, $admin_name)
    {
        if ($input['start_type'] == 1) {
            $input['start_date'] = date('Y-m-d H:i:s'); // 立即生效
        }
        if ($input['end_type'] == 1) {
            $input['end_date'] = ''; // 长期有效
        }

        if (!empty($input['id'])) {
            $input['modifier'] = $admin_name;
            return $this->infomercial->where('id', $input['id'])->update($input);
        } else {
            $input['creator'] = $admin_name;
            return $this->infomercial->insertGetId($input);
        }
    }


    /**
     * 查询广告
     * @param  $pagesize
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getAdvertisement($pagesize)
    {
        return $this->infomercial->paginate($pagesize);
    }


    /**
     * 更新状态
     * @param $input
     * @param $admin_name
     * @return MessageBag
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function updEnable($input, $admin_name)
    {
        $info       = $this->infomercial->where('id', $input['id'])->find();
        $messageBag = new MessageBag();
        if (empty($info)) {
            $messageBag->setCode(JsonBuilder::CODE_ERROR);
            $messageBag->setMessage('广告不存在');
        } else {

            $upd = [
                'is_enable' => $input['is_enable'],
                'modifier'  => $admin_name,
            ];
            $re  = $this->infomercial->where('id', $info['id'])->update($upd);
            if (!$re) {
                $messageBag->setCode(JsonBuilder::CODE_ERROR);
                $messageBag->setMessage('更新失败');
            } else {
                $messageBag->setMessage('更新成功');
            }
        }
        return $messageBag;

    }


    /**
     * 删除
     * @param $id
     * @return MessageBag
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function delAdvertisement($id)
    {
        $info       = $this->infomercial->where('id', $id)->find();
        $messageBag = new MessageBag();
        if (empty($info)) {
            $messageBag->setMessage('已删除');
        } else {
            $re = $this->infomercial->where('id', $id)->delete();
            if ($re) {
                $messageBag->setMessage('删除成功');
            } else {
                $messageBag->setCode(JsonBuilder::CODE_ERROR);
                $messageBag->setMessage('删除失败');
            }
        }
        return $messageBag;

    }



}