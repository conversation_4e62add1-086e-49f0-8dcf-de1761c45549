<?php
/**
 * Created by PhpStorm.
 * User: hjz
 * Date: 2020/12/22
 * Time: 3:11 PM
 */

namespace app\net_dealer\controller;


use app\common\model\db\DbActivityAssSku;
use app\common\model\db\DbActivityCenterLog;
use app\common\model\db\DbActivityMuchCard;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommodityType;
use app\common\model\e3s\E3sCarSeries;
use app\common\service\ActivityCardService;
use app\common\validate\MergeCard as MergeCardValidate;
use app\common\model\db\DbActivityCard;
use app\common\model\db\DbActivity;
use app\common\model\db\DbCommodityCard;
use app\common\model\db\DbCardCarSeries;
use ForkModules\Traits\ResponseTrait;
use app\common\net_service\Snowflake;
use think\Queue;

class MergeCard extends Common
{
    use ResponseTrait;

    private  $cardModel;
    private $domainUrl ;

    public function __construct()
    {
        parent::__construct();
        $this->cardModel =  new DbCard();
        $this->domainUrl = config('card_center.url');
    }

    private function div($value){
        if(empty($value) || strtolower($value) == "null"){
            return 0;
        }else{
           return $value / 100;
        }
    }


    /**
     * @Apidoc\Title("创券接口")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-dealer/Mergecard/createCard")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("融合卡券")
     * @Apidoc\ParamType("json")
     *
     */
    public function createCard(MergeCardValidate $validate)
    {

        $requestData = $this->request->only(array_keys($validate->getRuleKey()));

        $dbActivityCenterLog =  new DbActivityCenterLog();
        $logData=[
            'activity_id'=>$requestData['quickWinCardId'] ?? '',
            'request_id'=>'card',
            'oneid'=>111,
            'request_url'=>"test",
            'request_info'=>json_encode_cn($requestData),
        ];
        $logid = $dbActivityCenterLog->insertGetId($logData);
        $result      = $validate->scene("save_card")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $data['brand_id'] = $requestData['brandId'] ?? '';//品牌
        $upDownChannelDlrArr = DbCard::getChannel($data['brand_id']);
        $upDownChannelDlrNameArr = DbCard::getChannelName($data['brand_id']);

        if(!empty($upDownChannelDlrArr)){
            $newUpDownChannelDlrArr = [];
            foreach($upDownChannelDlrArr as $upDownChannelDlritem){
                $newUpDownChannelDlrArr[] = $upDownChannelDlritem;
            }
            if(!empty($newUpDownChannelDlrArr)){
                $data['up_down_channel_dlr'] = implode(',',$newUpDownChannelDlrArr);
            }

            $newUpDownChannelDlrNameArr = [];
            foreach($upDownChannelDlrNameArr as $upDownChannelDlrNameitem){
                $newUpDownChannelDlrNameArr[] = $upDownChannelDlrNameitem;
            }
            if(!empty($newUpDownChannelDlrNameArr)){
                $data['up_down_channel_name'] = implode(',',$newUpDownChannelDlrNameArr);
            }
        }
        if($data['brand_id'] == 1){
            $data['shelves_type'] = 5;
        }
        if($data['brand_id'] == 2){
            $data['shelves_type'] = 7;
        }
//        $data['coupon_status'] = $requestData['couponStatus'] ?? '';//卡券状态 0-未投放 1-已投放，2-已过期
        $data['receive_scene'] = $requestData['receiveScene'] ?? '';//业务场景
        $data['card_type'] = $this->getCardType($requestData['couponType']);//卡券分类
        $data['card_name'] = $requestData['couponTitle'] ?? '';//卡券名称
        $data['card_desc'] = $requestData['couponDesc'] ?? '';//优惠说明,字数上限300个汉字
        $data['card_discount'] = $requestData['discountNum'] ?? 0;//最高折扣，折扣券专用
        $data['max_discount'] = $requestData['maxDiscount'] ?? 0;//最高折扣金额
        $data['max_discount'] = $this->div($data['max_discount']);
        $data['card_quota'] = 0;
        if($requestData['couponType'] == 1 || $requestData['couponType'] == 9){
            $data['card_quota'] = $requestData['discountPrice'] ?? 0;//代金券优惠金额
            $data['card_quota'] = $this->div($data['card_quota']);
            $data['least_cost'] = $requestData['minConsumption'] ?? 0;//最低消费
        }else if($requestData['couponType'] == 2){
            $data['card_quota'] = $requestData['deductionPrice'] ?? 0;//满减券抵扣金额
            $data['card_quota'] = $this->div($data['card_quota']);
            $data['least_cost'] = $requestData['fullPrice'] ?? 0;//最低消费
        }else{
            $data['least_cost'] = $requestData['minConsumption'] ?? 0;//最低消费
        }
        $data['least_cost'] = $this->div($data['least_cost']);

        $data['is_enable'] = $requestData['isEnable'] ?? 1;//是否删除0删除1不删除

        // 卡券状态 0-未投放 1-已投放，2-已过期
        if($requestData['couponStatus'] == 1
//            && $data['is_enable'] == 1
        ){
            $data['is_enable'] = 1;
        }else{
            $data['is_enable'] = 0;
        }

        $data['available_count'] = $requestData['couponNum'] ?? 0;//发放数量
        $data['count'] = $requestData['couponNum'] ?? 0;//发放数量
        $data['car_config_code'] = $requestData['carConfigCode'] ?? '';//18位码多个以，隔开

        $data['apply_dlr_code'] = $requestData['storeCodes']?? '';//适用专营店多个以，隔开
        $data['e3s_ids'] = $requestData['e3sPartCodes'] ?? '';//E3S备件编码多个以，隔开
        //$e3sPartCode = $requestData['e3sPartCode'] ?? '';//E3S件编码多个以，隔开
        $data['e3s_type_ids'] = $requestData['e3sTypeIds'] ?? '';//E3S分类ID多个以，隔开
        $data['variety_code'] = $requestData['applicationVarietyCodes'] ?? '';//E3S备件分类编码多个以，隔开
        $data['use_des'] = $requestData['ruleContent'] ?? "";//使用须知
        $data['receive_rule'] = $requestData['receiveRule'] ??  1;//领取规则 1-用户 2-vin
        $data['get_limit'] = empty($requestData['receiveLimit']) ?  999 : $requestData['receiveLimit'];//领取限制，0-无限制，1-自定义
        $data['receive_start_date'] = $requestData['receiveStartDate'] ?? '';//领取开始时间
        $data['receive_end_date'] = $requestData['receiveEndDate'] ?? '';//每日结束领取时间
        $data['date_type'] = empty($requestData['deadlineType']) ? 2 : 1 ;//卡券：0-按天数，1-按日期 商城：1表示固定日期区间，2表示固定时长 结果：0-2，1-1
        $data['act_status'] = 2;
        //洋说如果是到店代金券就直接存2
        if($data['date_type'] == 1 && $requestData['couponType'] != 9){
            if(time() < strtotime($data['receive_start_date'])) {
                $data['act_status'] = 1;
            }
        }

        $data['validity_date_start'] = $requestData['useStartDate'] ?? '';//固定日期区间专用，有效期开始
        $data['validity_date_end'] = $requestData['useEndDate'] ?? '';//固定日期区间专用，有效期结束
        $data['fixed_term'] =  $requestData['deadlineDuringDays'] ?? '';//定时长专用，领取后多少天内有效，单位为天
        $data['fixed_begin_term'] = $requestData['deadlineStartDays'] ?? '';//固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天'
        if($data['date_type'] == 2){
            $data['validity_date_start'] = date('Y-m-d', strtotime($data['receive_start_date'] . " +{$data['fixed_begin_term']} day"));
            $data['validity_date_end'] = date('Y-m-d', strtotime($data['receive_end_date'] . " +{$data['fixed_term']} day"));
        }

        // 结算规则
        $data['settlement_rule_type'] =  $requestData['settlementRuleType'] ?? 1;//结算规则 1固定金额 2比例结算
        $data['settlement_rule_id'] =  $requestData['cardRule'] ?? '';//结算规则id
        $data['settlement_rule_value'] =  $requestData['settlementRuleValue'] ?? "";//结算规则值
        $data['settlement_rule_name'] =  $requestData['settlementRuleName'] ?? "";//结算规则值名称
        $data['card_sett_standard'] =  DbCard::getSettStandardVal($requestData['settlementStandard'] ?? 0);//结算规则标准值

//        if(empty($data['settlement_rule_id'])){ //卡券策略 卡券如果是无，则映射成我们的全部叠加 即共用
//            $data['can_with'] = 1;
//        }

        $data['quick_win_card_id'] = $requestData['quickWinCardId'] ?? ''; //卡券中心id
        $data['is_new_card'] = 1; //卡券融合的卡券
        // 卡券结算规则
        $commodity_ids = $requestData['commodityIds'] ?? '';//适用商品ID多个以，隔开
        $commodityTypeOneIds = $requestData['commodityTypeOneIds'] ?? '';//一级分类适用商品分类ID多个以，隔开
        $commodityTypeTwoIds = $requestData['commodityTypeTwoIds'] ?? '';//二级分类适用商品分类ID多个以，隔开
        $commodityTypeThreeIds = $requestData['commodityTypeThreeIds'] ?? '';//三级分类适用商品分类ID多个以，隔开

        $notAllEmptyTag =  !empty($commodityTypeOneIds) ||   !empty($commodityTypeTwoIds) ||   !empty($commodityTypeThreeIds) ;
        $AllEmptyTag =  empty($commodityTypeOneIds) &&   empty($commodityTypeTwoIds) &&   empty($commodityTypeThreeIds) ;

        $e3sId = $data['e3s_ids'];
//        if((!empty($e3sId) && $AllEmptyTag && !empty($commodity_ids)) || (!empty($e3sId) && $notAllEmptyTag && empty($commodity_ids)) ){
//            if($requestData['couponType'] != 9 && $requestData['couponType'] != 1){
//                return $this->setResponseError("分类,商品和备件id只能有一个不能为空")->send();
//            }
//        }
//
//
//        if((empty($e3sId) && !empty($commodity_ids) && $notAllEmptyTag)   || (empty($e3sId) && empty($commodity_ids)  && $AllEmptyTag)){
//            if($requestData['couponType'] != 9 && $requestData['couponType'] != 1) {
//                return $this->setResponseError("分类和商品只能有一个不能为空")->send();
//            }
//        }
        $commodityType = '';
        if(!empty($commodity_ids)){
            $data['relevancy_type'] = 4;
        }
        if(!empty($commodityTypeOneIds)){
            $data['relevancy_type'] = 1;
            $commodityType = $commodityTypeOneIds;
        }
        if(!empty($commodityTypeTwoIds)){
            $data['relevancy_type'] = 2;
            $commodityType =  $commodityTypeTwoIds;
        }
        if(!empty($commodityTypeThreeIds)){
            $data['relevancy_type'] = 3;
            $commodityType = $commodityTypeThreeIds;
        }
        if(!empty($data['variety_code'])) {
            $data['relevancy_type'] = 5; // 备件分类
        }

        $e3sCarSeriesObj = new E3sCarSeries();

        if(!empty($data['car_config_code'])){
            $car_config_code_arr = explode(',',$data['car_config_code']);
            $result = $e3sCarSeriesObj->whereIn("car_config_code",$car_config_code_arr)->where(['is_enable'=>1])->select();
            $carseries_id_arr = [];
            foreach($result as $item){
                $carseries_id_arr[] = $item['id'];
            }
            if(!empty($carseries_id_arr)){
                $data['car_series_id_str'] = implode(',',$carseries_id_arr);
            }
        }
        //刘洋说为到店代金券时为-1
        if($requestData['couponType'] == 9){
            $data['activity_id'] = -1;
            $data['admin_act_id'] = -1;
        }

        if (in_array($data['receive_scene'], [59,60]) && $data['card_type'] == 2 && $data['card_discount'] == 0) {
            // 赠品券
            $data['is_gift_card'] = 1;
        }


        $cardInfo = $this->cardModel->where(['quick_win_card_id'=>$requestData['quickWinCardId']])->find();
        if(empty($cardInfo)){
            Snowflake::setWorkerId(1); // 设置工作机器 ID
            Snowflake::setDatacenterId(1); // 设置数据中心 ID

            $id = Snowflake::nextId(); // 生成主键id
            $cardInfoId = $id;
            $data['id'] = $id;
            $this->cardModel->insertGetId($data);
        }else{
            $this->cardModel->where(['id'=>$cardInfo['id']])->update($data);
            $cardInfoId = $cardInfo['id'];
        }

        if($data['card_type'] != 9){
            $data['id'] = $cardInfoId;
            $this->doClassIds($data,$requestData);
        }

        //通知刘洋
        $e3s_id_arr = !empty($e3sId) ? explode(',',$e3sId) : [];

        $commodity_ids_arr = !empty($commodity_ids) ? explode(',',$commodity_ids) : [];

        $notifyData = [
            'card_id'=>$cardInfoId,
            'e3s_id_arr'=>$e3s_id_arr,
            'commodity_ids_arr'=>$commodity_ids_arr,
            'commodity_three_type_arr'=>$commodityType,
            'quick_win_card_id'=>$requestData['quickWinCardId'],
            'variety_code_arr' => explode(',', $data['variety_code']) ?? [],
            'is_gift_card' => $data['is_gift_card'] ?? 0,
        ];
        $this->notifierCard($notifyData);
        print_json(0, 'ok');
    }

    public function notifierCard($notifyData){
        $data = [
            'relevance_type' => 2, // 卡券保存关联
            'card_id' => $notifyData['card_id'], // 卡券id
            'admin_name' => 'notifier-card',
            'quick_win_card_id'=>$notifyData['quick_win_card_id'],
            'is_gift_card' => $notifyData['is_gift_card'],
        ];
        // notifier_type // 1关联商品  2关联键号  3关联商品分类  4关联备件分类
        if(!empty($notifyData['e3s_id_arr'])){
            $data['notifier_type'] = 2; //  2关联键号
            $data['data'] = $notifyData['e3s_id_arr'];
        } else if(!empty($notifyData['commodity_ids_arr'])){
            $data['notifier_type'] = 1; // 1关联商品
            $data['data'] = $notifyData['commodity_ids_arr'];
        } else if (!empty($notifyData['commodity_three_type_arr'])){
            $data['notifier_type'] = 3; // 关联商品分类
            $data['data'] = $notifyData['commodity_three_type_arr'];
        } else {
            $data['notifier_type'] = 4; // 关联备件分类
            $data['data'] = $notifyData['variety_code_arr']; // 关联备件分类
        }

        Queue::push('app\admin_v2\queue\NewCardCommodity', json_encode($data), config('queue_type.notifier_card'));
//        $service = new ActivityCardService();
//        $service->notifierRelevance($data['card_id'], $data['notifier_type'], $data['data'], $data['admin_name']);

    }

    private function getCardType($cardTypeId){
        //商城的 1代金券；2 折购券；6到店代金券
        $dataType = [
            1=>1,
            2=>1,
            3=>2,
            7=>1,
            9=>6,
        ];
        return $dataType[$cardTypeId];
    }

    private function doClassIds($cardInfo,$requestData){
        $dbCommodityCard = new DbCommodityCard();
        $dbCommodityCard->where(["card_id"=>$cardInfo['id']])->delete();

        $brand_code = "";
        if($requestData['brandId'] == 1){
            $brand_code = "GWSM";
        }
        if($requestData['brandId'] == 2){
            $brand_code = "QCSM";
        }

        if(!empty($commoidty_ids)){
            $dbCommoditySet = new DbCommoditySet();
            $commoidty_list = $dbCommoditySet->where("commodity_id in (".$commoidty_ids.") and up_down_channel_dlr = '$brand_code'")->select();
            foreach($commoidty_list as $commoidty_item){
                $dataCard = [
                    "commodity_id"=>$commoidty_item['commodity_id'],
                    "commodity_set_id"=>$commoidty_item['id'],
                    'card_id'=>$cardInfo['id']
                ];
                $dbCommodityCard->insertGetId($dataCard);
            }
        }
        if(!empty($commoidtyTypeOneIds)){
            $commoidty_type_arr = explode(',',$commoidtyTypeOneIds);
            foreach($commoidty_type_arr as $commoidty_type_item){
                $dataCard = [
                    "class_id"=>$commoidty_type_item,
                    "card_id"=>$cardInfo['id']
                ];
                $dbCommodityCard->insertGetId($dataCard);
            }
        }

        if(!empty($commoidtyTypeTwoIds)){
            $commoidty_type_arr = explode(',',$commoidtyTypeTwoIds);
            foreach($commoidty_type_arr as $commoidty_type_item){
                $dataCard = [
                    "class_id"=>$commoidty_type_item,
                    "card_id"=>$cardInfo['id']
                ];
                $dbCommodityCard->insertGetId($dataCard);
            }
        }

        if(!empty($commoidtyTypeThreeIds)){
            $commoidty_type_arr = explode(',',$commoidtyTypeThreeIds);
            foreach($commoidty_type_arr as $commoidty_type_item){
                $dataCard = [
                    "class_id"=>$commoidty_type_item,
                    "card_id"=>$cardInfo['id']
                ];
                $dbCommodityCard->insertGetId($dataCard);
            }
        }
    }


    /**
     * @Apidoc\Title("商品列表查询接口")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-dealer/Mergecard/getCommodityList")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("融合卡券")
     * @Apidoc\ParamType("json")
     *
     * @Apidoc\Param("type_id",type="integer", require=false,desc="商品分类id"),
     * @Apidoc\Param("dlr_code",type="string", require=false,desc="专营店编码")
     *
     *
     * @Apidoc\Returned("error", type="int",desc="0成功1失败")
     * @Apidoc\Returned("msg", type="string",desc="提示信息")
     * @Apidoc\Returned("data",type="array/json",desc="商品分类列表",
     *     @Apidoc\Returned ("name",type="varchar(200)",desc="商品名称"),
     *     @Apidoc\Returned ("id",type="int(10)",desc="商品id")
     * )，
     *
     *
     */
    public function getCommodityList(MergeCardValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("get_commodity_list")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $where = ['a.is_enable'=>1,'b.is_enable'=>1];
        if(!empty($requestData['commodity_type_id']))
        {
            $where['a.comm_type_id'] =$requestData['commodity_type_id'];
        }

        if(!empty($requestData['commodity_name']))
        {
            $where['a.commodity_name'] =['like',"%".$requestData['commodity_name'].'%'];
        }

        if(!empty($requestData['dlr_code']))
        {
            $where['b.dlr_code'] = $requestData['dlr_code'];
        }
        $params['where']  = $where;
        $params['query']  = input('get.');
        $params['group']  ='a.id';
        $params['field']  ='a.id,a.commodity_name';
        $commodityObj =  new DbCommodity();
        $commodityList = $commodityObj->getCommodityCardListByCode($params);
        print_json(0, 'ok',$commodityList);
    }

    /**
     * @Apidoc\Title("商品分类列表查询接口")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-dealer/Mergecard/getCommodityTypeList")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("融合卡券")
     * @Apidoc\ParamType("json")
     *
     * @Apidoc\Param("commodity_name",type="string", require=false,desc="商品名称")
     * @Apidoc\Param("brand",type="int", require=false,desc="1日产2英菲")
     *
     * @Apidoc\Returned("error", type="int",desc="0成功1失败"),
     * @Apidoc\Returned("msg", type="string",desc="提示信息"),
     * @Apidoc\Returned("data",type="array/json",desc="商品分类列表",
     *     @Apidoc\Returned ("name",type="varchar(200)",desc="一级分类名称"),
     *     @Apidoc\Returned ("id",type="int(10)",desc="一级分类id"),
     *     @Apidoc\Returned("two", type="object", desc="二级集合",
     *         @Apidoc\Returned ("name",type="varchar(200)",desc="二级分类名称"),
     *     @Apidoc\Returned ("id",type="int(10)",desc="二级分类id"),
     *     @Apidoc\Returned("three", type="object", desc="三级集合",
     *          @Apidoc\Returned ("name",type="varchar(200)",desc="三级分类名称"),
     *      @Apidoc\Returned ("id",type="int(10)",desc="三级分类id"),
     *      @Apidoc\Returned("commodiity", type="object", desc="商品集合",
     *         @Apidoc\Returned ("commodity_name",type="varchar(200)",desc="商品名称"),
     *                 @Apidoc\Returned ("id",type="int(10)",desc="商品id"),
     *     ),
     *     ),
     *     ),
     * )，
     *
     *
     */
    public function getCommodityTypeList(MergeCardValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $dbActivityCenterLog =  new DbActivityCenterLog();
//        $logData=[
//            'activity_id'=>333,
//            'request_id'=>'333',
//            'oneid'=>333,
//            'request_url'=>"test",
//            'request_info'=>json_encode_cn($requestData),
//        ];
//        $logid = $dbActivityCenterLog->insertGetId($logData);
        $result      = $validate->scene("get_commodity_type_list")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $where = [];
        if(!empty($requestData['commodity_name'])){
            $where['c.commodity_name'] = ['like','%'.$requestData['commodity_name'].'%'];
        }

        if(!empty($requestData['brand'])){
            if($requestData['brand'] == 1){
                $brand = 5;
            }else if ($requestData['brand'] == 2){
                $brand = 7;
            }

            $where['set.shelves_type'] = $brand;
        }
        $commodityTypeObj = new DbCommodityType();
        $typeList = $commodityTypeObj->alias("one")
            ->join("t_db_commodity_type two","one.id=two.comm_parent_id and two.is_enable =1")
            ->join("t_db_commodity_type three","two.id=three.comm_parent_id and three.is_enable =1")
            ->join("t_db_commodity c","c.comm_type_id = three.id and c.is_enable=1","left")
            ->join("t_db_commodity_set_sku sk","c.id = sk.commodity_id and sk.is_enable=1","left")
            ->join("t_db_commodity_set set","set.id = sk.commodity_set_id and set.is_enable=1","left")
            ->where($where)
            ->order("one.sort asc,two.sort asc,three.sort asc")
            ->group("c.id")
            ->field("c.comm_type_id,c.id as commodity_id,c.commodity_name,one.id as one_id,one.comm_type_name as one_name,two.id as two_id,two.comm_type_name as two_name,two.comm_parent_id as two_parent_id,three.id as three_id,three.comm_type_name as three_name,three.comm_parent_id as three_parent_id")->select();
         // ->field("one.id as one_id,one.comm_type_name as one_name,two.id as two_id,two.comm_type_name as two_name,two.comm_parent_id as two_parent_id,three.id as three_id,three.comm_type_name as three_name,three.comm_parent_id as three_parent_id")->select();
       // echo $commodityTypeObj->getLastSql();exit;
        $commodityTypeList = [];
        $one = [];
        foreach($typeList as $oneItem){
            $one[$oneItem['one_id']] = $oneItem['one_name'];
        }
        //第一层
        if(!empty($one)){
            foreach($one as $i=>$oneItem){
                $oneType = [];
                $oneType['id'] = $i;
                $oneType['name'] = $oneItem;
                $commodityTypeList[] = $oneType;
            }
        }

        //第二层
        $two = [];
        foreach($typeList as $twoItem){
            $two[$twoItem['two_id']] = $twoItem;
        }
        if(!empty($two) && !empty($commodityTypeList)){
            foreach($two as $twoItem){
                foreach($commodityTypeList as $one=>$oneType){
                    if($oneType['id'] == $twoItem['two_parent_id']){
                        $twoType = [];
                        $twoType['id'] = $twoItem['two_id'];
                        $twoType['name'] = $twoItem['two_name'];
                        $commodityTypeList[$one]['two'][] = $twoType;
                    }
                }
            }
        }
        //第三层
        $three = [];
        foreach($typeList as $threeItem){
            $three[$threeItem['three_id']] = $threeItem;
        }
        if(!empty($three) && !empty($commodityTypeList)){
            foreach($three as $threeItem){
                foreach($commodityTypeList as $one=>$twoType){
                    foreach ($twoType['two'] as $two=>$twoItem){
                        if($twoItem['id'] == $threeItem['three_parent_id']){
                            $threeType = [];
                            $threeType['id'] = $threeItem['three_id'];
                            $threeType['name'] = $threeItem['three_name'];
                            $commodityTypeList[$one]['two'][$two]['three'][] = $threeType;
                        }
                    }
                }
            }
        }

        //商品
        foreach($typeList as $c){
            foreach($commodityTypeList as $one=>$twoType){
                foreach ($twoType['two'] as $two=>$threeItem){
                    foreach ($threeItem['three'] as $three=>$threeItem){
                        if($c['comm_type_id'] == $threeItem['id']){
                            $commodityList = [];
                            $commodityList['id'] = $c['commodity_id'];
                            $commodityList['commodity_name'] = $c['commodity_name'];
                            $commodityTypeList[$one]['two'][$two]['three'][$three]['commodiity'][] = $commodityList;
                        }
                    }
                }
            }
        }
        print_json(0, 'ok',$commodityTypeList);
    }


    /**
     * @Apidoc\Title("活动创建接口")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-dealer/Mergecard/createActivity")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("融合卡券")
     * @Apidoc\ParamType("json")
     *
     * @Apidoc\Param("activityId",type="string", require=true,desc="活动id"),
     * @Apidoc\Param("brand",type="int(11)", require=true,desc="品牌id"),
     * @Apidoc\Param("activityName",type="string", require=true,desc="活动名称"),
     * @Apidoc\Param("activityTimeStart",type="int(11)", require=true,desc="时间开始"),
     * @Apidoc\Param("activityTimeEnd",type="string", require=true,desc="时间结束"),
     * @Apidoc\Param("activityMainObj",type="string", require=true,desc="活动主体"),
     * @Apidoc\Param("businessBelong",type="string", require=true,desc="业务归属"),
     * @Apidoc\Param("pvSubsidyFlag",type="string", require=true,desc="是否PV补贴 0-否 1-是"),
     * @Apidoc\Param("channel",type="string", require=true,desc="渠道 商城APP-app 商城小程序-mini_app 商城官网-official_website"),
     * @Apidoc\Param("upDownChannelDlr",type="string", require=false,desc="渠道-商城自己的字段"),
     * @Apidoc\Param("couponTriggerType",type="int(11)", require=true,desc="卡券触发方式 1-后台统一推送 2-用户行为触发 3-C端主动参与活动 4-C端主动领取"),
     * @Apidoc\Param("behaviorTriggerSceneList",type="string", require=true,desc="行为触发场景"),
     * @Apidoc\Param("receiveCouponPoints",type="string", require=true,desc="领券点位"),
     * @Apidoc\Param("activityMutexFlag",type="int(11)", require=true,desc="是否互斥活动 0-否 1-是"),
     * @Apidoc\Param("mutexActivityList",type="string", require=true,desc="互斥活动id"),
     * @Apidoc\Param("activityStatusFlag",type="int(11)", require=true,desc="活动状态 1-启用 0-关闭"),
     * @Apidoc\Param("couponActivateSceneList",type="string", require=true,desc="卡券激活场景")
     * @Apidoc\Param("grantRateLimit",type="int(11)", require=true,desc="发放日期限制：1-每日 2-每周 3-每月 4-活动期间 5-无限制"),
     * @Apidoc\Param("limitNumber",type="int(11)", require=true,desc="日期限制数量"),
     * @Apidoc\Param("couponMutexSuperposeType",type="string", require=true,desc="卡券策略：1-与指定券叠加 2-与指定券互斥 3-与全部券叠加 4-与全部券互斥"),
     * @Apidoc\Param("couponId",type="string", require=true,desc="互斥/可叠加优惠券id"),
     * @Apidoc\Param("selectObj",type="string", require=true,desc="活动属性: 1-选人 2-选车 3-选人+车"),
     * @Apidoc\Param("userIdentification",type="int(11)", require=true,desc="用户标识: 1-手机号 2-oneId 3-vin"),
     * @Apidoc\Param("bigDataCrowdList",type="string", require=true,desc="适用用户群体"),
     * @Apidoc\Param("dealerCodeRelationList",type="string", require=true,desc="关联门店"),
     * @Apidoc\Param("carSeriesList",type="array", require=true,desc="车系车型列表",
     *      @Apidoc\Param("carTypeName", type="string",desc="车系名称"),
     *      @Apidoc\Param("carTypeCode", type="string",desc="车系编码"),
     *      @Apidoc\Param("carSeriesName", type="string",desc="车型名称"),
     *      @Apidoc\Param("carSeriesCode", type="string",desc="车型编码"),
     *      @Apidoc\Param("carConfigCode", type="string",desc="18位码"),
     *
     * ),
     * @Apidoc\Param("cardList",type="array", require=true,desc="活动卡券",
     *      @Apidoc\Param("cardId", type="string",desc="商城卡券id"),
     *      @Apidoc\Param("quickWinCardId", type="string",desc="卡券中心卡券id"),
     * ),
     *
     *
     *
     * @Apidoc\Returned("error", type="int",desc="0成功1失败")
     * @Apidoc\Returned("msg", type="string",desc="提示信息")
     */
    public function createActivity(MergeCardValidate $validate)
    {

        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("save_activity")->check($requestData);
        $dbActivityCenterLog =  new DbActivityCenterLog();
        $logData=[
            'activity_id'=> $requestData['activityId'] ?? "",
            'request_id'=>'activity',
            'oneid'=>222,
            'request_url'=>"test",
            'request_info'=>json_encode_cn($requestData),
        ];
        $logid = $dbActivityCenterLog->insertGetId($logData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $dbActivityObj =  new DbActivity();
        $data['activity_id'] = $requestData['activityId'] ?? "";
        $data['brand'] = $requestData['brand'] ?? "";
        $data['activity_name'] = $requestData['activityName'] ?? "";
        $data['activity_time_start'] = $requestData['activityTimeStart'] ?? "";
        $data['activity_time_end'] = $requestData['activityTimeEnd'] ?? "";
        $data['activity_main_obj'] = $requestData['activityMainObj'] ?? "";
        $data['business_belong'] = $requestData['businessBelong'] ?? "";
        $data['pv_subsidy_flag'] = $requestData['pvSubsidyFlag'] ?? "";
        $data['channel'] = $requestData['channel'] ?? "";
        $data['coupon_trigger_type'] = $requestData['couponTriggerType'] ?? "";
        $data['behavior_trigger_scene_list']  = $requestData['behaviorTriggerSceneList'] ?? '';
        $data['receive_coupon_points'] = $requestData['receiveCouponPoints'] ?? "";
        $data['activity_mutex_flag'] = $requestData['activityMutexFlag'] ?? "";
        $data['mutex_activity_list'] = $requestData['mutexActivityList'] ?? "";
        $data['activity_status_flag'] = $requestData['activityStatusFlag'] ?? "";
        $data['coupon_invalid_scene_list'] = $requestData['couponInvalidSceneList'] ?? "";

        //   $data['coupon_activate_scene_list'] = $requestData['couponActivateSceneList'] ?? "";
//        if(!empty($couponActivateSceneList)){
//            $data['coupon_activate_scene_list'] = implode(',', $couponActivateSceneList);
//        }
        $data['coupon_mutex_superpose_type'] = $requestData['couponMutexSuperposeType'] ?? "";
        if(empty($data['coupon_mutex_superpose_type'])){
            $data['coupon_mutex_superpose_type'] = 3;
        }
        $data['select_obj'] = $requestData['selectObj'] ?? "";
        $data['user_identification'] = $requestData['userIdentification'] ?? "";
        $bigDataCrowdList = $requestData['bigDataCrowdList'] ?? '';
        if(!empty($bigDataCrowdList)){
            $data['big_data_crowd_list'] = json_encode($bigDataCrowdList);
        }
        $data['is_enable'] = $requestData['isEnable'] ?? 1;
        // 推送类型  todo  推送类型 0-无 1-精推 2-全推
        $data['push_type'] = $requestData['crowdPackagePushType'] ?? 0;

        $upDownChannelDlrArr = DbCard::getChannel($data['brand']);
        if(!empty($upDownChannelDlrArr)){
            $channelArr = explode(',', $data['channel']);
            $newUpDownChannelDlrArr = [];
            foreach($channelArr as $channelArrItem){
                $newUpDownChannelDlrArr[] = $upDownChannelDlrArr[$channelArrItem];
            }
            if(!empty($newUpDownChannelDlrArr)){
                $data['up_down_channel_dlr'] = implode(',',$newUpDownChannelDlrArr);
            }
        }
   //     $data['dealer_code_relation_list'] = $requestData['dealerCodeRelationList'] ?? "";

        $activity_info = $dbActivityObj->where(['activity_id'=>$requestData['activityId']])->find();
        if(empty($activity_info)){
            $dbActivityObj->insertGetId($data);
        }else{
            $dbActivityObj->where(['activity_id'=>$requestData['activityId']])->update($data);
        }

        if(!empty($requestData['cardList'])){
            $this->createActivityCard($requestData['activityId'],$requestData['cardList'],$requestData['couponTriggerType']);
        }


        //这个是2的话放在behaviorSceneBindCodeList里，是1的话放cardList的couponActivateSceneBindCodeList里
        if($requestData['couponTriggerType'] == 2) {
            $behaviorSceneBindCodeList = $requestData['behaviorSceneBindCodeList'] ?? '';
            if (!empty($behaviorSceneBindCodeList)) {
                $this->createActivityAssSku($requestData['activityId'], $behaviorSceneBindCodeList);
            }
        }

        print_json(0, 'ok');
    }

    private function createActivityAssSku($activityId,$behaviorsceneBindcodeList){
        $dbActivityAssSkuObj = new DbActivityAssSku();
        $dbActivityAssSkuObj->where(['activity_id'=>$activityId])->delete();
        foreach($behaviorsceneBindcodeList as $behaviorsceneBindcodeItem){
            $codeData['activity_id'] = $activityId;
            $codeData['sku_code'] = "";
            $codeData['sku_class_code'] = "";

            if($behaviorsceneBindcodeItem['bind_code_type'] == 1 || $behaviorsceneBindcodeItem['bind_code_type'] == 2){//备件与套餐
                $codeData['sku_code'] = $behaviorsceneBindcodeItem['bind_code'] ?? '';
            }
            //浩贤说3：商品id 还没用到
//            if($behaviorsceneBindcodeItem['bind_code_type'] == 3){//商品id
//                $codeData['commodity_id'] = $behaviorsceneBindcodeItem['bind_code'] ?? '';
//            }

            if($behaviorsceneBindcodeItem['bind_code_type'] == 4){//分类
                $codeData['sku_class_code'] = $behaviorsceneBindcodeItem['bind_code_category'] ?? '';
            }
            $codeData['sku_type'] = $behaviorsceneBindcodeItem['bind_code_type'];
            if(!empty($codeData['sku_code']) || !empty($codeData['sku_class_code'])){
                $dbActivityAssSkuObj->insertGetId($codeData);
            }

        }
    }

    private function createActivityCarSeries($activityId,$carSeriesList){
        $carSeriesObj  = new DbCardCarSeries();
        $carSeriesObj->where(['activity_id'=>$activityId])->delete();
        foreach($carSeriesList as $carSeriesItem){
            $seriesData['activity_id'] = $activityId;
            $seriesData['car_type_name'] = $carSeriesItem['carTypeName'] ?? '';
            $seriesData['car_type_code'] = $carSeriesItem['carTypeCode'] ?? '';
            $seriesData['car_series_name'] = $carSeriesItem['carSeriesName'] ?? '';
            $seriesData['car_series_code'] = $carSeriesItem['carSeriesCode'] ?? '';
            $seriesData['car_config_code'] = $carSeriesItem['carConfigCode'] ?? '';
            $carSeriesObj->insertGetId($seriesData);
        }
    }

    private function createActivityCard($activityId,$cardList,$couponTriggerType){
        $cardObj  = new DbActivityCard();
        $cardObj->where(['activity_id'=>$activityId])->delete();


        $dbCardObj = new DbCard();
        $quick_win_arr = [];
        $coupon_invalid_scene_list = [];
        foreach($cardList as $cardItem){
            if(!empty($cardItem['quickWinCardId'])){
                $quick_win_arr[] =  $cardItem['quickWinCardId'];
            }
            if(!empty($cardItem['couponInvalidSceneList'])){
                $coupon_invalid_scene_list[] =  implode(',',$cardItem['couponInvalidSceneList']);
            }
        }
        $dbActivityObj =  new DbActivity();
        if(!empty($coupon_invalid_scene_list)) {
            $dbActivityObj->where(['activity_id'=>$activityId])->update(['coupon_invalid_scene_list'=>implode(',',$coupon_invalid_scene_list)]);
        }

        $db_card_id_arr = [];
        if(!empty($quick_win_arr)){
            $db_card_list = $dbCardObj->whereIn('quick_win_card_id',$quick_win_arr)->order("receive_start_date asc")->select();
            foreach($db_card_list as $k=>$db_card_item){
                $db_card_id_arr[$db_card_item['quick_win_card_id']] = $db_card_item['id'];
            }
        }
        $much_data=[];
        foreach($cardList as $cardItem){
            $cardData['quick_win_card_id'] = $cardItem['quickWinCardId'] ?? '';

            if(!empty($cardData['quick_win_card_id'])){
               // $db_card_info = $dbCardObj->where(['is_enable'=>1,'quick_win_card_id'=>$cardData['quick_win_card_id']])->find();
                if(!empty($db_card_id_arr)){
                    $cardData['card_id'] = $db_card_id_arr[$cardData['quick_win_card_id']] ?? 0;
                }

                $cardData['coupon_id'] = $cardItem['couponId'] ?? '' ;
                $cardData['grant_rate_limit'] = $cardItem['grantRateLimit'] ?? '' ;
                $cardData['limit_number'] = $cardItem['limitNumber'] ?? '' ;
                $cardData['user_limit_number'] = $cardItem['userLimitNumber'] ?? '' ;
                $cardData['consume_type'] = $cardItem['consumeType'] ?? '';
                //$cardData['coupon_activate_scene_parent'] = $cardItem['couponActivateSceneParent'] ?? '';
                $couponActivateSceneList = $cardItem['couponActivateSceneList'] ?? [];
                if(!empty($couponActivateSceneList)){
                    $cardData['coupon_activate_scene_list'] = implode(',',$couponActivateSceneList);
                    $dbActivityObj->where(['activity_id'=>$activityId])->update(['behavior_trigger_scene_list'=>$cardData['coupon_activate_scene_list']]);
                }
                if($db_card_id_arr){
                    $much_data[]= [
                        'activity_id'=>$activityId,
                        'card_id'=> $cardData['card_id'],
                        'quick_win_card_id'=> $cardData['quick_win_card_id'],
                        'coupon_trigger_type'=> $couponTriggerType,
                    ];
                }


            }

            $cardData['activity_id'] = $activityId;
            $cardObj->insertGetId($cardData);

            //这个是2的话放在behaviorSceneBindCodeList里，是1的话放cardList的couponActivateSceneBindCodeList里
            if($couponTriggerType == 1) {
                if($much_data){
                    //卡券触发方式 1-后台统一推送 2-用户行为触发 3-C端主动参与活动 4-C端主动领取
                    //1的话就插表
                    $much_model =  new DbActivityMuchCard();
                    $much_model->insertAll($much_data);
                }
                $couponActivateSceneBindCodeList = $cardItem['couponActivateSceneBindCodeList'] ?? '';

                if (!empty($couponActivateSceneBindCodeList)) {
                    $this->createActivityAssSku($activityId, $couponActivateSceneBindCodeList);
                }

            }
        }

        $service = new ActivityCardService();
        $service->activityCard($activityId);
//        Queue::push('app\admin_v2\queue\CardActivity', ['activity_id' => $activityId], config('queue_type.coupon'));
    }


    public function getCardCommodity(MergeCardValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("get_commodity_card")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $quickWinCardIds = $requestData['quickWinCardIds'];
        $dbCommodityCardObj = new DbCommodityCard();
        $dataList = $dbCommodityCardObj->alias("a")->join("t_db_card b","a.card_id=b.id")
//                                            ->join("t_db_commodity c","a.commodity_id=c.id")

                                            ->join("t_db_commodity_flat flat","(FIND_IN_SET( a.class_id, flat.comm_type_id_str ) OR   a.commodity_set_id = flat.commodity_set_id  ) and a.is_enable=1 and flat.is_enable=1")
                                            ->where(["b.quick_win_card_id"=>['in',$quickWinCardIds],"a.is_enable"=>1,"b.is_enable"=>1,"flat.is_enable"=>1])
                                            ->group("a.commodity_id")->field("a.commodity_id,flat.commodity_name")
                                            ->select();
       // echo $dbCommodityCardObj->getLastSql();exit;
        print_json(0, 'ok',$dataList);
    }






}
