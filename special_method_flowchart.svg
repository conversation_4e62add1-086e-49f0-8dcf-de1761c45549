<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 1200 800">
  <defs>
    <style>
      .mermaid-diagram {
        font-family: "trebuchet ms", verdana, arial, sans-serif;
        font-size: 16px;
        fill: #333;
      }
      .node rect, .node circle, .node ellipse, .node polygon, .node path {
        fill: #ECECFF;
        stroke: #9370DB;
        stroke-width: 1px;
      }
      .node text {
        fill: #333;
      }
      .cluster rect {
        fill: #ffffde;
        stroke: #aaaa33;
        stroke-width: 1px;
      }
      .cluster text {
        fill: #333;
      }
      .relation {
        stroke: #333;
        stroke-width: 1px;
        fill: none;
      }
      .arrowheadPath {
        fill: #333;
        stroke: #333;
      }
      .edgePath .path {
        stroke: #333;
        stroke-width: 1.5px;
      }
      .flowchart-link {
        stroke: #333;
        fill: none;
      }
      .edgeLabel {
        background-color: #e8e8e8;
        text-align: center;
      }
      .edgeLabel rect {
        opacity: 0.5;
        background-color: #e8e8e8;
        fill: #e8e8e8;
      }
      .label {
        font-family: "trebuchet ms", verdana, arial, sans-serif;
        color: #333;
      }
      .label text {
        fill: #333;
      }
      .node rect,
      .node circle,
      .node ellipse,
      .node polygon,
      .node path {
        fill: #ECECFF;
        stroke: #9370DB;
        stroke-width: 1px;
      }
      .dbQuery rect {
        fill: #ffcdd2;
        stroke: #d32f2f;
        stroke-width: 2px;
      }
      .heavyProcess rect {
        fill: #ff5722;
        stroke: #bf360c;
        stroke-width: 3px;
      }
      .cacheHit rect {
        fill: #c8e6c9;
        stroke: #388e3c;
        stroke-width: 2px;
      }
      .apiCall rect {
        fill: #fff3e0;
        stroke: #f57c00;
        stroke-width: 2px;
      }
      .startEnd rect {
        fill: #e1f5fe;
        stroke: #0277bd;
        stroke-width: 2px;
      }
    </style>
  </defs>
  
  <!-- 这是一个简化的SVG表示，实际的Mermaid图表需要通过Mermaid渲染引擎生成 -->
  <text x="600" y="50" text-anchor="middle" class="mermaid-diagram" style="font-size: 24px; font-weight: bold;">
    Special方法完整调用流程图
  </text>
  
  <text x="600" y="100" text-anchor="middle" class="mermaid-diagram" style="font-size: 16px;">
    此SVG文件包含完整的Mermaid流程图代码
  </text>
  
  <text x="600" y="150" text-anchor="middle" class="mermaid-diagram" style="font-size: 14px;">
    请使用支持Mermaid的工具打开以下代码：
  </text>
  
  <!-- Mermaid代码作为注释保存 -->
  <!--
  graph TD
    A[Special方法开始] --> B[参数验证 SpecialValidate]
    B --> C[性能监控开始 microtime]
    C --> D[缓存Key生成]
    
    D --> E{专题基础信息缓存检查}
    E -->|缓存被禁用| F[强制数据库查询]
    E -->|缓存命中| G[返回缓存数据]
    F --> F1["SELECT title,data_json,bg_color,bg_img<br/>FROM t_db_special_sm<br/>WHERE page_type=? AND id=?"]
    F1 --> F2[Redis缓存写入 5-10小时]
    
    G --> H[二维码处理]
    F2 --> H
    H --> H1{根据page_type判断渠道}
    H1 -->|渠道1-日产| H2[QrCodeService::rcQrCode<br/>CarLive API调用]
    H1 -->|渠道2-启辰| H3[QrCodeService::qcQrCode<br/>InterFun API调用]
    H1 -->|渠道3-pz1a| H4[QrCodeService::pzQrCode<br/>Pz1a API调用]
    H2 --> H5[二维码缓存写入]
    H3 --> H5
    H4 --> H5
    
    H5 --> I{员工验证条件检查<br/>is_staff=1?}
    I -->|是| J[员工验证流程]
    I -->|否| K[跳过员工验证]
    J --> J1[DbUser::getOneByPk<br/>用户信息查询]
    J1 --> J2[AcPhonelist::getOne<br/>电话验证查询]
    J2 --> J3{验证结果}
    J3 -->|失败| J4[SendSms::send_sms<br/>发送短信验证码]
    J3 -->|成功| K
    J4 --> J5[BaseTool::sms_send<br/>API调用]
    J5 --> K
    
    K --> L{工会认证条件检查}
    L -->|需要认证| M[工会认证流程]
    L -->|否| N[推荐数据获取]
    M --> M1[AcGongHuiInfo::getOneBySp<br/>工会信息查询]
    M1 --> M2[AcHaveTradeList::getOne<br/>交易记录查询]
    M2 --> M3[BuOrder复杂联表查询]
    M3 --> M4["SELECT a.*,b.waybill_number<br/>FROM t_bu_order a<br/>JOIN t_bu_order_commodity b<br/>ON a.order_code=b.order_code<br/>WHERE a.order_status NOT IN (1,3,8,18)<br/>AND b.commodity_id IN (...)<br/>AND user_id=? ORDER BY a.id DESC"]
    M4 --> N
  -->
</svg>
