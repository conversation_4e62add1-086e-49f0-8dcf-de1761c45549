/**
Custom module for you to write your own javascript functions
**/
var
    Custom = function () {

    // private functions & variables

    var myFunc = function(text) {
        alert(text);
    }


        /**
         * 判断文件类型
         * @param fileid  input标签id
         * @param file_size 文件大小
         * @param alert_msg  错误提示信息
         * @returns {boolean}
         */
    var  validationFile=function(fileid,file_size,alert_msg) {
        // var fileTypeArr = ['video/mp4','application/msword','application/x-msdownload'];
        // if(null == file){
        //     return false;
        // }
        // if(!file.type){
        //     layer.msg("文件类型不合法");
        //     return false;
        // }
        // var flag = false;
        // for(var i=0;i<fileTypeArr.length;i++){
        //     if(file.type == fileTypeArr[i]){
        //         flag = true;
        //         break;
        //     }
        // }
        //
        // if(flag){
        //     showTip("文件类型不合法","warning");
        //     return false;
        // }
        //
        // if(file.size > 5000000){
        //     showTip("文件大小不能超过5MB","warning");
        //     return false;
        // }

        /**
         * begin
         *
         * 此处有修改
         * 修改人：吴炜文
         * 时间2017.10.20 16:00
         * 修改内容：上传图片大小、格式做限制
         */
        var fileInputs = $("#"+fileid);
        var fileInput = fileInputs[0];
        if(fileInput&&fileInput.files[0]){
            var fileName = fileInput.files[0].name;
            var fileSize = fileInput.files[0].size;
            var fileType = fileName.substr(fileName.lastIndexOf('.')+1,fileName.length);
            if(fileType!='jpeg'&&fileType!='jpg'&&fileType!='png'){
                console.log("fileType = "+fileType);
                layer.msg("文件格式不正确");
                fileInputs.val('');
                return;
            }
            if(fileSize>file_size*1024){
                console.log(111);
                layer.msg(alert_msg);
                fileInputs.val('');
                return;
            }
        }
            /**
             * end
             */
        return true;
    }


    // public functions
    return {

        //main function
        init: function () {
            //initialize here something.
        },

        //some helper function
        doSomeStuff: function () {
            myFunc();
        },

        validationFile:function (fileid,file_size,alert_msg) {
           return validationFile(fileid,file_size,alert_msg);

        },




        load:function(){
            load_index=layer.load(2, {shade: [0.2,'#000'], scrollbar: false}); //0代表加载的风格，支持0-2
        },
        //使用 FromData 可以提交图片文件
        ajaxFromData:function(url,data,alert_obj,href_url){
            var load_index = layer.load(2, {shade: [0.2,'#000'], scrollbar: false}); //0代表加载的风格，支持0-2
            if(alert_obj){
                alert_obj.hide();
            }
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                dataType: 'JSON',
                cache: false,
                processData: false,
                contentType: false,
                success : function(res){
                    layer.close(load_index)
                    if (res.error==0){    //成功返回
                        layer.msg(res.msg, { icon:6,time:1000},function(){
                            if(!href_url){
                                history.back(-1);
                            }else{
                                location.href=href_url;
                            }
                        });

                    }else{
                        if(alert_obj){
                            alert_obj.find('p').html(res.msg);
                            alert_obj.show();
                        }else {
                            layer.msg(res.msg, { icon:5,time:1000});
                            return false;
                        }

                    }
                },
                error: function(XMLHttpRequest, textStatus, errorThrown) {
                    layer.alert('系统错误:状态码'+XMLHttpRequest.status+' '+errorThrown,{icon: 2});
                }
            });

        },

        // 用于导入数据
        ajaxImpData:function(url,data,msg,href_url,callBack){
            var imp_index=layer.msg(msg, {
                icon: 16
                ,shade: [0.2,'#000'],
                time: 0,
                scrollbar: false
            });
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                dataType: 'JSON',
                cache: false,
                processData: false,
                contentType: false,
                success : function(res){
                    layer.close(imp_index);

                    if(callBack&&typeof(callBack)=="function"){
                        callBack.call(this,res);
                    }else{
                        if (res.error==0){    //成功返回
                            layer.msg(res.msg, { icon:6,time:1000},function(){
                                if(!href_url){
                                    window.location.reload();
                                }else{
                                    location.href=href_url;
                                }
                            });

                        }else{
                            layer.alert(res.msg,{icon: 5,scrollbar: false});
                            return false;
                        }

                    }
                },
                error: function(XMLHttpRequest, textStatus, errorThrown) {
                    layer.close(imp_index);
                    layer.alert('系统错误:状态码'+XMLHttpRequest.status+' '+errorThrown,{icon: 2});
                }
            });
        },

        ajaxPost:function(url,data,alert_obj,href_url,callBack){
            var load_index = layer.load(2, {shade: [0.2,'#000'], scrollbar: false}); //0代表加载的风格，支持0-2
            if(alert_obj){
                alert_obj.hide();
            }
            //
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                dataType: 'JSON',
                success : function(res){
                    layer.close(load_index);
                    //增加回调方法
                    if(callBack&&typeof(callBack)=="function"){
                        callBack.call(this,res);
                    }else {

                        if (res.error==0){    //成功返回
                            if(res.msg){
                                var sucess_msg  ='操作成功';
                            }else {
                                var sucess_msg  =res.msg;
                            }
                            layer.msg(res.msg,{anim:0,time:1000,scrollbar: false},function(){
                                if(!href_url){
                                    window.location.reload();
                                }else{
                                    location.href=href_url;
                                }
                            });
                        }else{
                            if(alert_obj){
                                alert_obj.find('p').html(res.msg);
                                alert_obj.show();
                            }else {
                                layer.msg(res.msg, { icon:5,time:1000});
                                return false;
                            }
                        }

                    }

                },
                error: function(XMLHttpRequest, textStatus, errorThrown) {
                    layer.close(load_index);
                    layer.alert('系统错误:状态码'+XMLHttpRequest.status+' '+errorThrown,{icon: 2});
                }
            });

        },


        /**
         * 异步文件上传
         * @param fileid
         * @param data
         * @param callBack
         */
        ajaxFileUpload:function(fileid,data,callBack){

            $.ajaxFileUpload({
                url : AJAX_FILE_UPLOAD_URL, //用于文件上传的服务器端请求地址
                secureuri : false, //一般设置为false
                fileElementId : fileid, //文件上传空间的id属性  <input type="file" id="file" name="file" />
                dataType : 'json', //返回值类型 一般设置为json
                data : data,  // 上传数据 包括路径 file_path
                async : false,
                contentType : "text/json;charset=utf-8",
                success : function(res){ //服务器成功响应处理函数
                    callBack.call(this,res);
                }
            });
        },

        selectDlr:function (ajax_data,selected_data,callback) {
            var modal = $("#select-dlr-modal");
            var dlr_content = $('#select-dlr-content');
            var dlr_label_selected = '';
            console.log('selected_data='+selected_data);
            // console.log(ajax_data);
            var flag_dlr_all_checked = true;
            dlr_content.empty();
            if(typeof ajax_data.label_list =='object'&&ajax_data.label_list.length>0){
                var dlr_label_content = modal.find('.dlr-label-content');
                var len = ajax_data.label_list.length;
                dlr_label_content.empty();
                $.each(ajax_data.label_list,function (i,item) {
                    var form_group = $('<div class="form-group"></div>');
                    form_group.appendTo(dlr_label_content);
                    var label = $('<label data-toggle="tip" title="'+item.name+'" style="width:136px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;"></label>');
                    label.appendTo(form_group);
                    var input = $('<input type="radio" value="'+item.label+'" type="checkbox"'+item.checked+' name="dlr_label" class="label-single" data-label-code="'+item.label+'" data-label-name="'+item.name+'">');
                    input.appendTo(label);
                    label.append(item.name);
                    if (len == (i+1)) {
                        var form_group = $('<div class="form-group"></div>');
                        form_group.appendTo(dlr_label_content);
                        var label = $('<label data-toggle="tip" title="' + item.name + '" style="width:136px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;"></label>');
                        label.appendTo(form_group);
                        var input = $('<input type="radio" value="0" type="checkbox" name="dlr_label" class="label-single" data-label-code="0" data-label-name="取消分组选择">');
                        input.appendTo(label);
                        label.append('取消分组选择');
                    }
                })
                console.log('yes:'+typeof ajax_data.label_list);
            }else {
                console.log('no:'+typeof ajax_data.label_list);
            }
            $.each(ajax_data.dlr_list,function (pro_index,province) {
                var flag_pro_checked = true;
                var form_group = $('<div class="dlr-group"></div>');
                form_group.appendTo(dlr_content);
                $.each(province.dlr_list,function (dlr_index,dlr) {
                    var index= $.inArray(dlr.dlr_code, selected_data);
                    var checked = index>-1 ? 'checked' : '';
                    if(flag_pro_checked){
                        flag_pro_checked = index>-1 ? true : false;
                    }
                    var label = $('<label class="m-r-5" data-toggle="tip" title="'+dlr.dlr_name+'" style="width:136px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;"></label>');
                    label.appendTo(form_group);
                    var input = $('<input type="checkbox"'+checked+' class="dlr-single" data-dlr-label="'+dlr.label+'" data-dlr-code="'+dlr.dlr_code+'" data-dlr-id="'+dlr.base_dealer_id+'" data-dlr-name="'+dlr.dlr_name+'" data-dlr-brand="'+dlr.brand_type+'" data-has-ice-service-type="'+dlr.has_ice_service_type+'" data-has-nev-service-type="'+dlr.has_nev_service_type+'">');
                    input.appendTo(label);
                    label.append(dlr.dlr_name);
                })
                var lengend_before = $('<legend></legend>');
                form_group.prepend(lengend_before);
                var checked = flag_pro_checked?'checked':'';
                var pro_label = $('<label></label>');
                var pro_checkbox = $('<input type="checkbox"'+checked+' class="dlr-province">');
                pro_checkbox.appendTo(pro_label);
                pro_label.append(province.area_name);
                form_group.prepend(pro_label);
                var lengend = $('<legend></legend>');
                lengend.appendTo(form_group)
                if(flag_dlr_all_checked){
                    flag_dlr_all_checked = flag_pro_checked?true:false;
                }
            })
            if(flag_dlr_all_checked){
                $(".dlr-all").attr('checked',true)
            }else {
                $(".dlr-all").removeAttr('checked')
            }
            modal.modal("show");

            var obj = {
                bind:function () {
                    $('.label-single').bind("change",function () {
                        var label = $(this).data('label-code');
                        var check = $(this).attr('checked');
                        var dlr_list = modal.find('.dlr-single');
                        if(check){
                            dlr_label_selected = label;
                            $.each(dlr_list,function (i,item) {
                                var dlr_label = $(this).data('dlr-label').toString();
                                //console.log(dlr_label)
                                if (dlr_label){
                                    var dlr_label_arr = dlr_label.split(',');

                                    if(dlr_label_arr.length>0 && $.inArray(label.toString(),dlr_label_arr)>-1){

                                        $(this).attr("checked",true);
                                    }else {
                                        $(this).removeAttr("checked");
                                    }
                                }

                            })
                        }
                    })
                    $(".dlr-all").bind("change",function () {
                        var check = $(this).attr("checked");
                        var dlr_list = dlr_content.find(".dlr-single");
                        var pro_list = dlr_content.find(".dlr-province");
                        if(check){
                            $.each(dlr_list,function (i,item) {
                                $(this).attr("checked",true);
                            })
                            $.each(pro_list,function (i,item) {
                                $(this).attr("checked",true);
                            })
                        }else {
                            $.each(dlr_list,function (i,item) {
                                $(this).removeAttr("checked");
                            })
                            $.each(pro_list,function (i,item) {
                                $(this).removeAttr("checked");
                            })
                        }
                    })
                    $('.dlr-province').bind("change",function () {
                        var check = $(this).attr('checked');
                        var dlr_list = $(this).closest('.dlr-group').find('.dlr-single');
                        if(check){
                            $.each(dlr_list,function (i,item) {
                                $(this).attr("checked",true);
                            })
                            // var dlr_all = dlr_content.find(".dlr-single");
                            // var flag_allDlr_choose = true;
                            // $.each(dlr_all,function (i,item) {
                            //     if(!$(this).attr('checked')){
                            //         flag_allDlr_choose = false;
                            //         return;
                            //     }
                            // })
                            // if(!flag_allDlr_choose){
                            //     $(".dlr-all").removeAttr("checked");
                            // }else {
                            //     $(".dlr-all").attr("checked",true);
                            // }

                        }else {
                            dlr_list = $(this).closest('.dlr-group').find('input[class="dlr-single"]:checked')
                            $.each(dlr_list,function (i,item) {
                                $(this).removeAttr("checked");
                            })
                            // $(this).closest(".modal-body").find(".dlr-all").removeAttr("checked");
                        }
                    })
                },
                unbind:function () {
                    $(".label-single,.dlr-all,.dlr-province").unbind("change");
                }
            }
            obj.unbind();
            obj.bind();
            $("#dlr-search-btn").bind("click",function () {
//                var val = modal.find("#dlr-search-name").val().trim();
                var brand_type = modal.find("#brand_type").val().trim();
                var has_ice_service_type = modal.find("#has_ice_service_type").val().trim();
                var has_nev_service_type = modal.find("#has_nev_service_type").val().trim();
                var dlr_list = dlr_content.find('.dlr-single');
//                console.log(has_ice_service_type)
                console.log(has_nev_service_type)
//                console.log(brand_type)
//                console.log(dlr_list)
                if(brand_type==0 && has_ice_service_type==999 && has_nev_service_type==0){
                    $.each(dlr_list,function (i,dlr) {
                        $(dlr).closest('label').removeClass('hidden');
                    })
                }else {
                    $.each(dlr_list, function (i, dlr) {
                        $(dlr).closest('label').removeClass('hidden');
                        var dlr_brand = $(this).data('dlr-brand') + "";
                        var has_ice = $(this).data('has-ice-service-type') + "";
                        var has_nev = $(this).data('has-nev-service-type') + "";
                        if (dlr_brand.indexOf(brand_type) == -1 && brand_type > 0) {
                            $(dlr).closest('label').addClass('hidden');
                        }
                        if (has_ice.indexOf(has_ice_service_type) == -1 && has_ice_service_type!=999) {
                            $(dlr).closest('label').addClass('hidden');
                        }
//                        console.log(has_nev)
                        if (has_nev.indexOf(has_nev_service_type) == -1 && has_nev_service_type>0) {
                            $(dlr).closest('label').addClass('hidden');
                        }


                    })
//                    if (val != '') {
//                        $.each(dlr_list, function (i, dlr) {
//                            var dlr_name = $(this).data('dlr-name') + "";
//                            console.log('dlr_name:' + dlr_name);
//                            if (dlr_name.indexOf(val) == -1) {
//                                $(dlr).closest('label').addClass('hidden');
//                            } else if (brand_type > 0) {
//                                var dlr_brand = $(this).data('dlr-brand') + "";
//                                if (dlr_brand.indexOf(brand_type) == -1) {
//                                    $(dlr).closest('label').addClass('hidden');
//                                } else {
//                                    $(dlr).closest('label').removeClass('hidden');
//                                }
//                            }else{
//                                $(dlr).closest('label').removeClass('hidden');
//                            }
//                        })
//                    } else if (brand_type > 0) {
//                        $.each(dlr_list, function (i, dlr) {
//                            var dlr_brand = $(this).data('dlr-brand') + "";
//                            if (dlr_brand.indexOf(brand_type) == -1) {
//                                $(dlr).closest('label').addClass('hidden');
//                            } else {
//                                $(dlr).closest('label').removeClass('hidden');
//                            }
//                        })
//                    }

                }
                obj.unbind();
                obj.bind();
            })



            $("#dlr_cancel,#dlr_add").unbind("click");
            if(callback) {
                $("#dlr_cancel").bind("click",function () {
                    //还原
                    modal.modal("hide");
                    var dlr_list = modal.find("input[class='dlr-single']");
                    var dlr_code_array = selected_data;
                    var dlr_name_array = [];
                    var index = -1;
                    $.each(dlr_list,function (i,dlr) {
                        index = $.inArray($(this).data('dlr-code')+'',selected_data)
                        console.log('dlr-code='+$(this).data('dlr-code'));
                        console.log('index='+index);
                        if(index>-1){
                            dlr_name_array[dlr_name_array.length] = $(this).data('dlr-name');
                        }
                    })
                    callback(dlr_code_array,dlr_name_array);
                })
                $("#dlr_add").bind("click",function () {
                    modal.modal("hide");
                    var dlr_list = dlr_content.find("input[class='dlr-single'][type='checkbox']:checked");
                    var dlr_code_array    = [];
                    var dlr_name_array    = [];
                    var base_dlr_id_array = [];
                    console.log('count dlr_code_array = '+dlr_list.length);
                    $.each(dlr_list,function (i,dlr) {
                        // if ($(dlr).parent().hasClass('hidden') == false) {
                            dlr_code_array[dlr_code_array.length] = $(this).data('dlr-code');
                            dlr_name_array[dlr_name_array.length] = $(this).data('dlr-name');
                            base_dlr_id_array[base_dlr_id_array.length] = $(this).data('dlr-id');
                        // }
                    })
                    callback(dlr_code_array,dlr_name_array,base_dlr_id_array);
                })
            }
        },

        /**
         * 字符串转对象
         * @param serializedString
         * @returns {*}
         */
        deserialize:function (serializedString) {
            return serializedString.split('&').reduce((obj, pair) => {
                const [key, value] = pair.split('=').map(decodeURIComponent);
                obj[key] = value;
                return obj;
            }, {});
        },
    };
}();

