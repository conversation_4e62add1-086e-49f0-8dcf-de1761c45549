/**
 * Created by lyj on 2017/11/23.
 */
/**
 * 活动商品列表搜索 主品
 * @param isSkuConfirmAll
 * @returns {boolean}
 */
function commTypeSearch(isSkuConfirmAll = 0) {
    // console.log('commTypeSearch',isSkuConfirmAll)
    var comm_parent_id = $("select[name='comm_parent_id']").val();
    var sub_comm_type_id = $("select[name='sub_comm_type_id']").val();
    var three_comm_type_id = $("select[name='three_comm_type_id']").val();
    var commodity_name = $("input[name='commodity_name']").val();
    var commodity_class = $("select[name='commodity_class']").val();
    var start_time = $("#start_time").val();
    var end_time = $("#end_time").val();
    var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+commodity_name+'&commodity_class='+commodity_class+'&start_time='+start_time+'&end_time='+end_time;
    //额外参数验证
    if(typeof(isInitComm) != "undefined") {
        if(isInitComm == 0){
            return false;
        }
    }
    // console.log(isSkuConfirmAll);return

    initComm(param, 1,isSkuConfirmAll);

}


/**
 * 活动商品列表搜索 赠品
 * @param isSkuConfirmAll
 * @returns {boolean}
 */
function commTypeSearch3(isSkuConfirmAll = 0) {
    var comm_parent_id = $("select[name='comm_parent_id']").val();
    var sub_comm_type_id = $("select[name='sub_comm_type_id']").val();
    var three_comm_type_id = $("select[name='three_comm_type_id']").val();
    var commodity_name = $("input[name='commodity_name']").val();
    var commodity_class = $("select[name='commodity_class']").val();
    var start_time = $("#start_time").val();
    var end_time = $("#end_time").val();
    var param = ajaxGiftCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+commodity_name+'&commodity_class='+commodity_class+'&start_time='+start_time+'&end_time='+end_time;
    //额外参数验证
    if(typeof(isInitComm) != "undefined") {
        if(isInitComm == 0){
            return false;
        }
    }
    // console.log(isSkuConfirmAll);return

    initComm(param, 2,isSkuConfirmAll);

}

// $(function () {


    //选择时间清除选择的商品
    $(".datetimepicker3").datetimepicker({
        format:"YYYY-MM-DD HH:mm:ss",
        locale: moment.locale('zh-cn'),
    }).on('dp.change', function(e){
        if(e.date){
            if( $("table #haved-commodtiy tr:visible").length ){
                var index = layer.open({
                    title: ['操作提醒'],
                    btn: ['确认', '取消'],
                    content:"<div style='font-size: 15px'>更改活动时间会清空下方添加商品列表</div>",
                    yes: function (res) {
                        layer.close(index)
                        $("#haved-commodtiy tr").remove()
                        $("#haved-commodtiy-gift tr").remove()
                        var start_time = $("#start_time").val();
                        var end_time = $("#end_time").val();
                        var url = ajaxCommUrl+'&start_time='+start_time+'&end_time='+end_time
                        var url_gift = ajaxGiftCommUrl+'&start_time='+start_time+'&end_time='+end_time
                        initComm(url,1);
                        initComm(url_gift,2);
                        // initComm(ajaxCommUrl);
                    }
                })
            }else{
                var start_time = $("#start_time").val();
                var end_time = $("#end_time").val();
                var url = ajaxCommUrl+'&start_time='+start_time+'&end_time='+end_time
                var url_gift = ajaxGiftCommUrl+'&start_time='+start_time+'&end_time='+end_time
                initComm(url,1);
                initComm(url_gift,2);
            }
        }
    });

    //初始化
    $(".default-select2").select2({width:'150px'});


    $("#comm-type-search").on('click',function(){
        var obj = $(this).parents('.form-group');
        var comm_parent_id = obj.find("select[name='comm_parent_id']").val();
        var sub_comm_type_id = obj.find("select[name='sub_comm_type_id']").val();
        var three_comm_type_id = obj.find("select[name='three_comm_type_id']").val();
        var commodity_name = obj.find("input[name='commodity_name']").val();
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var start_time = $("#start_time").val();
        var end_time = $("#end_time").val();
        var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+ sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+ commodity_name+'&commodity_class='+commodity_class+'&start_time='+start_time+'&end_time='+end_time;
        initComm(param,1);
    });

    $("#comm-type-search-gift").on('click',function(){
        var obj = $(this).parents('.form-group');
        var comm_parent_id = obj.find("select[name='comm_parent_id']").val();
        var sub_comm_type_id = obj.find("select[name='sub_comm_type_id']").val();
        var three_comm_type_id = obj.find("select[name='three_comm_type_id']").val();
        var commodity_name = obj.find("input[name='commodity_name']").val();
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();
        var start_time = $("#start_time").val();
        var end_time = $("#end_time").val();
        var param = ajaxGiftCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+ sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+ commodity_name+'&commodity_class='+commodity_class+'&start_time='+start_time+'&end_time='+end_time+'&commodity_dlr_type_id='+commodity_dlr_type_id;
        initComm(param,2);
    });

    /*------------------- begin 添加商品、分类 -----------------------*/
    initComm(ajaxCommUrl,1);
    initComm(ajaxGiftCommUrl,2);

    function initComm(url,type,isSkuConfirmAll) {
        if(type === 1){
            $.getJSON(url, null, function (resData) {
                createPageComm(10, 10, resData.data.list.total, url,1);//创建翻页功能按钮，翻
                $("#add-comm-tbody").empty();
                if (resData.data.list.total > 0) {                          //页向后台请求连接
                    setComm(url,1,isSkuConfirmAll);
                }
            });
        }else{
            $.getJSON(url, null, function (resData) {
                createPageComm(10, 10, resData.data.list.total, url,2);//创建翻页功能按钮，翻
                $("#add-comm-tbody-gift").empty();
                if (resData.data.list.total > 0) {                          //页向后台请求连接
                    setComm(url,2,isSkuConfirmAll);
                }
            });
        }

    }

    function createPageComm(pageSize, buttons, total, url,type) {        //contracts_url为点击
        if(type === 1){
            $("#comm-pagination").jBootstrapPage({      //功能按钮后需要访问action路径
                pageSize : pageSize,
                total : total,
                maxPageButton:buttons,
                onPageClicked: function(obj, page) {    //分页事件
                    $("#add-comm-tbody").empty();
                    $("#sku-confirm-all").data("current-page",page+1);
                    setComm(url+"&page="+(page+1),1);
                }
            });
        }else{
            $("#comm-pagination-gift").jBootstrapPage({      //功能按钮后需要访问action路径
                pageSize : pageSize,
                total : total,
                maxPageButton:buttons,
                onPageClicked: function(obj, page) {    //分页事件
                    $("#add-comm-tbody-gift").empty();
                    $("#sku-confirm-all-gift").data("current-page",page+1);//切换分页后写到data值
                    setComm(url+"&page="+(page+1),2);
                }
            });
        }

    }

    //获取并设置商品
    function setComm(param,type,isSkuConfirmAll){
        if(type === 1){
            var url = ajaxCommUrl;
        }else{
            var url = ajaxGiftCommUrl;
        }
        // console.log(url);
        if(param!=null) url=param;

        $.get(url,function(res){
            var html='';
            var list = res.data.list.data;
            var skuConfirmAllUnm = 0;
            //当前页
            var current_page = res.data.list.current_page;
            //当前分页条数
            var per_page = res.data.list.per_page;

            if(type === 1){
                var comm_set_id_arr=gethavedCommodityId();
            }else{
                var comm_set_id_gift_arr=gethavedCommodityIdGift();
            }

            $.each(list,function(i,val){
                var checkbox_obj = '';
                if (type === 1) {
                    var index= Number($.inArray(val.commodity_set_id, comm_set_id_arr));
                } else {
                    var index= Number($.inArray(val.commodity_set_id, comm_set_id_gift_arr));
                }

                if((isSkuConfirmAll > 0) && (val.is_limit != 1) && (val.is_pre != 1) && (val.is_seckill != 1) && (val.is_full != 1 &&(val.is_gift !=1))){
                    if(type == 1){
                        index = 1;
                        //批量添加操作
                        skuConfirm(val,'add',i)
                        skuConfirmAllUnm +=1;
                    }else{
                        index = 1;
                        //批量添加操作
                        skuConfirm1(val, 'add', i)
                        skuConfirmAllUnm +=1;
                    }

                }
                if (index>-1){
                    var button='<button data-comm-set-id="'+val.commodity_set_id+'" data-grouped="'+val.is_grouped+'" data-comm-id="'+val.commodity_id+'" '+
                        ' class=" btn btn-default active btn-sm btn-fight" disabled>已添加</button>';
                } else {
                    if(val.is_fight == 1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'" data-grouped="'+val.is_grouped+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与团购</button>';
                    }else if(val.is_pre==1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'" data-grouped="'+val.is_grouped+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与预售</button>';
                    }else if(val.is_limit==1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'" data-grouped="'+val.is_grouped+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与其他限时折扣</button>';
                    }else if(val.is_seckill==1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'" data-grouped="'+val.is_grouped+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与其他秒杀活动</button>';
                    }else if(val.is_full ==1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'" data-grouped="'+val.is_grouped+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与其他满优惠活动</button>';
                    }else if(val.is_gift ==1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'" data-grouped="'+val.is_grouped+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与['+val.activity_name+']买赠活动</button>';
                    }
                    else{
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'" data-grouped="'+val.is_grouped+'" data-comm-id="'+val.commodity_id+'" '+
                            ' class=" btn btn-white btn-sm  btn-fight">选择添加</button>';
                    }
                }

                html += '<tr id="comm_tab_tr_'+val.commodity_id+'">' +
                    '<td class="text-left"><image class="cover-image" src="'+val.cover_image+'">'+
                    '<a  class="init-commodity-preview" data-dlr-code="'+val.dlr_code+'" data-commodity-id="'+val.commodity_id+'"> '+val.commodity_name+' </a>' +
                    '</td>' +
                    '<td class="text-center">'+ val.up_down_channel_name +'</td>' +
                    '<td class="text-center">'+ val.price +'</td>' +
                    '<td class="text-center">'+ val.count_stock +'</td>' +
                    '<td class="text-center">' + button +
                    '</td> </tr>';
            });
            if(type === 1){
                $("#add-comm-tbody").html(html);
            }else{
                $("#add-comm-tbody-gift").html(html);
            }

        },'json');
    }

    //添加商品
    $("#add-comm-tbody").on('click',".btn-fight",function (e) {
        e.preventDefault();
        var obj=$(this);
        $.getJSON(getSkuUrl,{commodity_id:$(this).data('comm-id'),commodity_set_id:$(this).data('comm-set-id'),set_type:set_type},function (res) {
            var sku_list=res.data.sku_list;
            $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
            $(".sku-comm").html(res.data.commodity_row.commodity_name);
            $("#comm-name-header").html(res.data.commodity_row.commodity_name);
            var html='';
            if(obj.data('grouped') === 1) {
                $("#title1 p").html('订单内包含该组合商品则可选择赠品')
                html += '<img class=" cover-image" src="'+res.data.commodity_row.cover_image+'">'+res.data.commodity_row.commodity_name
                $("#sku-modal table").hide()
                $("#sku-tb").show()
                $("#sku-tb").html(html);
            }else{
                $("#title1 p").html('默认该商品全部规格选中参加该项促销活动,如某个规格不想参与该项活动,可通过取消选择排除掉;')
                $("#sku-modal table").show()
                $("#sku-tb").hide()
                $.each(sku_list,function(i,val){
                    var sku_check='<input type="checkbox" checked class="sku-ckeck" value="6" name="">';
                    var price = '<input type="hidden" class="sku-price" value="'+val.price+'">'+val.price;
                    html += '<tr set_sku_id="'+val.id+'">' +
                        '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                        '<td>'+price+'</td>' +
                        '<td >' + val.count_stock +
                        '</td> </tr>';
                });
            }
            $("#sku-modal").data("grouped",obj.data('grouped'))
            $("#sku-modal").find("[name='comm_discount']").val('');
            $("#sku-modal").data("comm-id",obj.data('comm-id'));
            $("#sku-modal").data("comm-set-id",obj.data('comm-set-id'));
            $("#sku-modal").data('type','add');
            $(".sku-tb").html(html);
            $("#sku-modal").modal('show');
            //显示立减或者折扣
            //更新折扣价格
            //dlr初始化
            $("#sku-modal").find(".sku-dlr").val("").data("sku-dlr","");
        });
    });

    $("#add-comm-tbody-gift").on('click',".btn-fight",function (e) {
        e.preventDefault();
        var obj=$(this);
        $.getJSON(getSkuUrl,{commodity_id:$(this).data('comm-id'),commodity_set_id:$(this).data('comm-set-id')},function (res) {
            var sku_list=res.data.sku_list;
            $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
            $(".sku-comm").html(res.data.commodity_row.commodity_name);
            $("#comm-name-header-gift").html(res.data.commodity_row.commodity_name);
            var html='';
            if(obj.data('grouped') === 1) {
                $("#title2 p").html('组合商品作为赠品，则默认所有子商品必选，整个组合商品价格为0')
                html += '<img class=" cover-image" src="'+res.data.commodity_row.cover_image+'">'+res.data.commodity_row.commodity_name
                $("#sku-modal-gift table").hide()
                $("#sku-tb-gift").show()
                $("#sku-tb-gift").html(html);
            }else{
                $("#title2 p").html('作为赠品时价格为0;')
                $("#sku-modal-gift table").show()
                $("#sku-tb-gift").hide()
                $.each(sku_list,function(i,val){
                    var sku_check='<input type="checkbox" checked class="sku-ckeck" disabled value="6" name="">';
                    var price = '<input type="hidden" class="sku-price" value="'+val.price+'">'+val.price;
                    html += '<tr set_sku_id="'+val.id+'">' +
                        '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                        '<td>'+price+'</td>' +
                        '<td >' + val.count_stock +
                        '</td> </tr>';
                });
            }
            $("#sku-modal-gift").data("grouped",obj.data('grouped'))
            $("#sku-modal-gift").find("[name='comm_discount']").val('');
            $("#sku-modal-gift").data("comm-id",obj.data('comm-id'));
            $("#sku-modal-gift").data("comm-set-id",obj.data('comm-set-id'));
            $("#sku-modal-gift").data('type','add');
            $(".sku-tb-gift").html(html);
            $("#sku-modal-gift").modal('show');
            //显示立减或者折扣
            //更新折扣价格
            //dlr初始化
            $("#sku-modal-gift").find(".sku-dlr").val("").data("sku-dlr","");
        });
    });

    //查看商品
    $("body").on("click",".btn-fight-view",function (e) {

        e.preventDefault();
        var set_sku_list=$(this).data('sku-list');
        var sku_price_list=set_sku_list.sku_list;
        var obj=$(this);
        $.getJSON(getSkuUrl,{commodity_id:set_sku_list.commodity_id,commodity_set_id:set_sku_list.commodity_set_id},function (res) {
            var sku_list=res.data.sku_list;
            $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
            $(".sku-comm").html(res.data.commodity_row.commodity_name);
            var html='';
            if(obj.data('grouped') === 1) {
                $("#title1 p").html('订单内包含该组合商品则可选择赠品')
                html += '<img class=" cover-image" src="'+res.data.commodity_row.cover_image+'">'+res.data.commodity_row.commodity_name
                $("#sku-modal table").hide()
                $("#sku-tb").show()
                $("#sku-tb").html(html);
            }else{
                $("#title1 p").html('默认该商品全部规格选中参加该项促销活动,如某个规格不想参与该项活动,可通过取消选择排除掉;')
                $("#sku-modal table").show()
                $("#sku-tb").hide()
                $.each(sku_list,function(i,val){
                    var checked=''
                    if(sku_price_list[val.id]){
                        checked='checked';
                    }
                    var sku_check='<input type="checkbox" ' +checked+'  class="sku-ckeck" value="6" name="">';
                    var stock = '<div>'+
                        '<input  type="hidden" class="form-control stock input-sm" name="stock" value="'+val.count_stock+'">'+ val.count_stock+
                        '</div>';
                    var price = '<input type="hidden" class="sku-price" value="'+val.price+'">'+val.price;
                    html += '<tr set_sku_id="'+val.id+'">' +
                        '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                        '<td>'+ price +'</td>' +
                        '<td >' + stock +
                        '</td> </tr>';
                });
            }


            $("#sku-modal").data("comm-id",set_sku_list.commodity_id);
            $("#sku-modal").data("comm-set-id",set_sku_list.commodity_set_id);
            $("#comm-name-header").html(set_sku_list.commodity_name);
            var index=$(".btn-fight-view").index(obj);
            $("#sku-modal").data('type',index);
            $(".sku-tb").html(html);
            $("#sku-modal").modal('show');

            //dlr初始化
            $("#sku-modal").find(".sku-dlr").val(set_sku_list.dlr_name).attr("data-sku-dlr",set_sku_list.dlr_code);
        });
        //alert($(this).data("sku-list"));
    })

    $("body").on("click",".btn-fight-view-gift",function (e) {

        e.preventDefault();
        var set_sku_list=$(this).data('sku-list');
        var sku_price_list=set_sku_list.sku_list;

        var obj=$(this);
        $.getJSON(getSkuUrl,{commodity_id:set_sku_list.commodity_id,commodity_set_id:set_sku_list.commodity_set_id},function (res) {
            var sku_list=res.data.sku_list;
            // console.log(sku_list)
            $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
            $(".sku-comm").html(res.data.commodity_row.commodity_name);
            var html='';
            if(obj.data('grouped') === 1) {
                $("#title2 p").html('组合商品作为赠品，则默认所有子商品必选，整个组合商品价格为0')
                html += '<img class=" cover-image" src="'+res.data.commodity_row.cover_image+'">'+res.data.commodity_row.commodity_name
                $("#sku-modal-gift table").hide()
                $("#sku-tb-gift").show()
                $("#sku-tb-gift").html(html);
            }else{
                $("#title2 p").html('作为赠品时价格为0;')
                $("#sku-modal-gift table").show()
                $("#sku-tb-gift").hide()
                $.each(sku_list,function(i,val){
                    var checked=''
                    if(sku_price_list[val.id]){
                        checked='checked';
                    }
                    var sku_check='<input type="checkbox" ' +checked+'  class="sku-ckeck" disabled value="6" name="">';
                    var stock = '<div>'+
                        '<input  type="hidden" class="form-control stock input-sm" name="stock" value="'+val.count_stock+'">'+ val.count_stock+
                        '</div>';
                    var price = '<input type="hidden" class="sku-price" value="'+val.price+'">'+val.price;
                    html += '<tr set_sku_id="'+val.id+'">' +
                        '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                        '<td>'+ price +'</td>' +
                        '<td >' + stock +
                        '</td> </tr>';
                });
            }


            $("#sku-modal-gift").data("comm-id",set_sku_list.commodity_id);
            $("#sku-modal-gift").data("comm-set-id",set_sku_list.commodity_set_id);
            $("#comm-name-header").html(set_sku_list.commodity_name);
            var index=$(".btn-fight-view-gift").index(obj);
            $("#sku-modal-gift").data('type',index);
            $(".sku-tb-gift").html(html);
            $("#sku-modal-gift").modal('show');

            //dlr初始化
            $("#sku-modal-gift").find(".sku-dlr").val(set_sku_list.dlr_name).attr("data-sku-dlr",set_sku_list.dlr_code);
        });
        //alert($(this).data("sku-list"));
    })

    //批量设置价格
    $('#all_price').editable({
        success: function(response, newValue) {
            $(".sku-price").val(newValue);

            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^[0-9/.]*$/).test(value)) {
                return '格式有误';
            }
        }
    });

    var commodity_list =new Object();
    //sku 点击确认
    $("#sku-confirm").on('click',function (e) {
        var sku_modal=$(this).parents("#sku-modal");
        var home =  sku_modal.find("#modal_home").val();

        var grouped = sku_modal.data('grouped')
        commodity_list.commodity_id    =sku_modal.data('comm-id');
        commodity_list.commodity_set_id=sku_modal.data('comm-set-id');
        commodity_list.dlr_code        =sku_modal.find(".sku-dlr").attr("data-sku-dlr");
        commodity_list.dlr_name        =sku_modal.find(".sku-dlr").val();
        commodity_list.comm_discount  =sku_modal.find("[name='comm_discount']").val();
        commodity_list.sku_stock  =sku_modal.find("[name='sku_stock']").val();
        commodity_list.commodity_name  =sku_modal.find("#comm-name-header").html();
        commodity_list.is_grouped  = grouped;
        commodity_list.is_gift  = 0;//是否赠品 1赠品
        var image=sku_modal.find(".cover-image").attr("src");
        var  commodity_name=sku_modal.find(".sku-comm").html();
        var sku_list={};
        // console.log('grouped---',grouped)
        // console.log('sku_list---',sku_list)
        if(grouped === 0){
            $(".sku-tb tr").each(function () {
                if($(this).find(".sku-ckeck").attr("checked")){
                    var set_sku_id=$(this).attr("set_sku_id");
                    sku_list[set_sku_id]=$(this).find(".sku-price").val();
                }
            });
        }
        // console.log('添加商品',sku_list)
        // return;
        commodity_list.sku_list=sku_list;
        //新增数据添加到后面
        var json_st=JSON.stringify(commodity_list);
        var type   =sku_modal.data('type');
        //添加

        if (type=='add'){
            var html='<tr class="info haved-add" data><td style="width: 350px;"><input type="hidden" name="home"  class="home" value="'+home+'"/><img class="cover-image" src="'+image+'">'+commodity_name+'</td><td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list">删除</button></td></tr>';
            //alert(html);
            $("#haved-commodtiy").append(html);
            comm_set_id_arr.push(commodity_list.commodity_set_id);

            // console.log("添加商品："+html)
            commodity_select()
        }else {  //查看
            //
            var html='<td style="width: 350px;"><img class="cover-image" src="'+image+'">'+commodity_name+'</td><td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list ">删除</button></td>';
            $(".btn-fight-view").eq(type).parents('tr').html(html);
        }

        //专营店 禁用添加按钮
        if(admin_type==2 || admin_type==1){
            var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+commodity_list.commodity_set_id+"']");
            btn_obj.removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
            //btn_obj.parents("button").html('已添加');
            // console.log(btn_obj);
        }
        $("#sku-modal").modal('hide');

        // console.log(commodity_list);
    })

    //sku 点击确认
    var commodity_list_gift =new Object();
    $("#sku-confirm-gift").on('click',function (e) {
        var sku_modal=$(this).parents("#sku-modal-gift");
        var home =  sku_modal.find("#modal_home").val();
        commodity_list_gift.commodity_id    =sku_modal.data('comm-id');
        commodity_list_gift.commodity_set_id=sku_modal.data('comm-set-id');
        commodity_list_gift.dlr_code        =sku_modal.find(".sku-dlr").attr("data-sku-dlr");
        commodity_list_gift.dlr_name        =sku_modal.find(".sku-dlr").val();
        commodity_list_gift.comm_discount  =sku_modal.find("[name='comm_discount']").val();
        commodity_list_gift.sku_stock  =sku_modal.find("[name='sku_stock']").val();
        commodity_list_gift.commodity_name  =sku_modal.find("#comm-name-header").html();
        commodity_list_gift.is_gift  = 1;//是否赠品 1赠品
        var image=sku_modal.find(".cover-image").attr("src");
        var  commodity_name=sku_modal.find(".sku-comm").html();
        var grouped = sku_modal.data('grouped')
        commodity_list_gift.is_grouped  = grouped;
        var sku_list={};
        if(grouped === 0){
            $(".sku-tb-gift tr").each(function () {
                if($(this).find(".sku-ckeck").attr("checked")){
                    var set_sku_id=$(this).attr("set_sku_id");
                    sku_list[set_sku_id]=$(this).find(".sku-price").val();
                }
            });
        }
        commodity_list_gift.sku_list=sku_list;
        // console.log('sku_list_gift:',sku_list)
        // console.log('commodity_list_gift:',commodity_list_gift)
        //新增数据添加到后面
        var json_st=JSON.stringify(commodity_list_gift);
        var type   =sku_modal.data('type');
        //添加

        if (type=='add'){
            var html='<tr class="info haved-add" data><td style="width: 350px;"><input type="hidden" name="home"  class="home" value="'+home+'"/><img class="cover-image" src="'+image+'">'+commodity_name+'</td><td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view-gift">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list-gift">删除</button></td></tr>';
            //alert(html);
            $("#haved-commodtiy-gift").append(html);
            comm_set_id_gift_arr.push(commodity_list_gift.commodity_set_id);

            // console.log("添加商品："+html)
            commodity_select_gift();
        }else {  //查看
            //
            var html='<td style="width: 350px;"><img class="cover-image" src="'+image+'">'+commodity_name+'</td><td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view-gift">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list-gift ">删除</button></td>';
            $(".btn-fight-view-gift").eq(type).parents('tr').html(html);
        }

        //专营店 禁用添加按钮
        if(admin_type==2 || admin_type==1){
            // console.log('111',22222)
            var btn_obj=$('#add-comm-tbody-gift').find("[data-comm-set-id='"+commodity_list_gift.commodity_set_id+"']");
            btn_obj.removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
            //btn_obj.parents("button").html('已添加');
            // console.log('btn_obj_gift:',btn_obj);
        }
        $("#sku-modal-gift").modal('hide');

        // console.log('commodity_list_gift:',commodity_list_gift);
    })

    /**********删除主商品************/
    $("body").on("click",".del-sku-list",function (e) {
        e.preventDefault();
        var sku_list=$(this).parents("tr").find(".btn-fight-view").data("sku-list");
        var type=$(".del-sku-list").index($(this));
        $("#del-sku-modal").find("#del-data-id").val(type).data('comm-set-id',sku_list.commodity_set_id);
        $("#del-sku-modal").modal('show');
    })
    $("#del-confirm").on("click",function () {
        var comm_set_id = $("#del-sku-modal").find("#del-data-id").val();
        comm_set_id_arr.splice($.inArray(comm_set_id,comm_set_id_arr),1);

        $(".del-sku-list").eq($("#del-sku-modal").find("#del-data-id").val()).parents('tr').remove();
        $("#del-sku-modal").modal('hide');
        //专营店端修改添加按钮
        if(admin_type==2 || admin_type==1){
            var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+$("#del-sku-modal").find("#del-data-id").data('comm-set-id')+"']");
            // console.log('btn_obj_del:',btn_obj)
            btn_obj.removeClass("btn-default active").addClass("btn-white").attr("disabled",false).html('选择添加');
        }
        // console.log('comm_set_id_arr:',comm_set_id_arr)
    });
    /**********删除主商品************/



    /**********删除赠品************/
    $("body").on("click",".del-sku-list-gift",function (e) {
        e.preventDefault();
        var sku_list=$(this).parents("tr").find(".btn-fight-view-gift").data("sku-list");
        var type=$(".del-sku-list-gift").index($(this));
        $("#del-sku-modal-gift").find("#del-data-id-gift").val(type).data('comm-set-id',sku_list.commodity_set_id);
        $("#del-sku-modal-gift").modal('show');
    })
    $("#del-confirm-gift").on("click",function () {
        var comm_set_id = $("#del-sku-modal-gift").find("#del-data-id-gift").val();
        comm_set_id_gift_arr.splice($.inArray(comm_set_id,comm_set_id_gift_arr),1);
        $(".del-sku-list-gift").eq($("#del-sku-modal-gift").find("#del-data-id-gift").val()).parents('tr').remove();
        $("#del-sku-modal-gift").modal('hide');
        //专营店端修改添加按钮
        if(admin_type==2 || admin_type==1){
            var btn_obj=$('#add-comm-tbody-gift').find("[data-comm-set-id='"+$("#del-sku-modal-gift").find("#del-data-id-gift").data('comm-set-id')+"']");
            // console.log('btn_obj_del_gift:',btn_obj)
            btn_obj.removeClass("btn-default active").addClass("btn-white").attr("disabled",false).html('选择添加');
        }
    });
    /**********删除赠品************/

    //遍历已添加数据,获取专营店
    function getdlr(commodity_set_id) {
        // console.log(commodity_set_id);
        var dl_code_list=[]
        $('#haved-commodtiy tr').each(function () {
            var sku_list=$(this).find('.btn-fight-view').data('sku-list');
            console.log(sku_list);
            if(commodity_set_id==sku_list.commodity_set_id){
                dl_code_list= dl_code_list.concat(sku_list.dlr_code.split(','));
            }
        });
        // console.log(dl_code_list);
        return dl_code_list;
    }

    $(".datetimepicker3").datetimepicker({
        format:"YYYY-MM-DD HH:mm:00",
        locale: moment.locale('zh-cn'),
    });

    /* 专营店端获取添加id*/
    function gethavedCommodityId() {
        var comm_set_id_arr=[];
        $("#haved-commodtiy tr").each(function () {
            var sku_list=$(this).find(".btn-fight-view").data("sku-list");
            comm_set_id_arr.push(sku_list.commodity_set_id);
        });
        return comm_set_id_arr;
    }

    function gethavedCommodityIdGift() {
        var comm_set_id_arr_gift=[];
        $("#haved-commodtiy-gift tr").each(function () {
            var sku_list=$(this).find(".btn-fight-view-gift").data("sku-list");
            comm_set_id_arr_gift.push(sku_list.commodity_set_id);
        });
        return comm_set_id_arr_gift;
    }

    //获取并设置商品分类
    function setCommType(param){
        var url = ajaxCommTypeUrl;
        if(param!=null) url=param;
        var data_id = $('#addCommTypeModal').find("[name='text_click_id']").attr('data-id');
        $.get(url,function(res){
            var html='';
            var list = res.data.data;
            $.each(list,function(i,val){
                var checkbox_obj = '';
                if(val.id==data_id) {
                    checkbox_obj = '<label> <input type="checkbox" class="commodity_checkbox" data-id="' + val.id +'" data-name="'+val.comm_type_name+'" data-parent-name="'+val.comm_parent_name+'" value="" name="" checked>选择 </label>';
                }else{
                    checkbox_obj = '<label> <input type="checkbox" class="commodity_checkbox" data-id="' + val.id + '" data-name="'+val.comm_type_name+'" data-parent-name="'+val.comm_parent_name+'" value="" name="">选择 </label>';
                }
                html += '<tr id="comm_tab_tr_'+val.id+'">' +
                    '<td class="text-center">'+ val.comm_type_name +'</td>' +
                    '<td class="text-center">' + checkbox_obj +
                    '</td> </tr>';
            });
            $("#add-comm-type-tbody").html(html);

        },'json');
    }

    //搜索商品分类列表
    /* $("#comm-type-search").on('click',function(){
     var obj = $(this).parent();
     var comm_parent_id = obj.find("select[name='comm_parent_id']").val();
     var comm_type_name = obj.find("input[name='comm_type_name']").val();
     var param = ajaxCommTypeUrl + '&comm_parent_id='+comm_parent_id+'&comm_type_name='+comm_type_name;
     initCommType(param);
     });*/

    //选择商品-取消
    $("#add-comm-no").on('click',function(){
        $("#addCommModal").click();
    });
    /*------------------- end 添加商品、分类 -----------------------*/

    function getSkuList() {
        var sku_list=[];
        $("#haved-commodtiy tr").each(function () {
            //alert($(this).find(".btn-fight-view").data("sku-list"));
            sku_list.push($(this).find(".btn-fight-view").data("sku-list"));
        })
        if(sku_list == false){
            return false;
        }
        var sku_list_gift = [];
        $("#haved-commodtiy-gift tr").each(function () {
            //alert($(this).find(".btn-fight-view").data("sku-list"));
            sku_list.push($(this).find(".btn-fight-view-gift").data("sku-list"));
            sku_list_gift.push($(this).find(".btn-fight-view-gift").data("sku-list"));
        })
        if(sku_list_gift == false){
            return false;
        }
        return sku_list;
    }

    $("#put-form").on('click',function(){
        var $form=$("#fight-form");
        // dealer_select 有hidden, 就清空 dlr_hide 的value
        if($("#dealer_select").hasClass("hidden")){
            $("#dlr_hide").attr('value','');
        }else{
            var dlr_value = $("#dlr_hide").val();
            //遍历 checkbox-inline, 把没有勾选的数据从dlr_hide 中去除
            $(".checkbox-inline input:not(:checked)").each(function(i,v){
                var reg = new RegExp(this.value,"g");//g,表示全部替换。
                dlr_value = dlr_value.replace(reg,"");
            })
            $("#dlr_hide").attr('value',dlr_value);
        }
        var up_down_channel_name = getUpDownChannel();
        var validate=$form.psly().validate();  //表单验证
        if(!validate) return false;
        var form_data=$form.serialize();
        var sku_list = getSkuList();

        // 活动图片
        var activity_image=[];
        $(".activity_image_group .goods_pic").each(function(){
            $(this).find('li img').each(function(){
                activity_image.push($(this).attr("image-value"));
            });
        });

        var user_segment = $('input[name="user_segment"]:checked').val();
        var user_segment_options = $('input[name="user_segment_options"]:checked').val();
        if(!getSkuList()){
            layer.msg('主品赠品商品不能为空');
            return false;
        }else{
            var data=$.param({sku_list:JSON.stringify(sku_list),'up_down_channel_name':up_down_channel_name,'user_segment':user_segment,'user_segment_options':user_segment_options,"activity_image":activity_image}) + '&' + form_data;
            Custom.ajaxPost(save_url,data,null,index_url);
        }
    });

/**
 * 批量添加 主品操作
 * @param sku_modal
 * @param type
 */
var comm_set_id_arr = []
function skuConfirm(sku_modal, type = 'add', key = 0){
    // console.log('skuConfirm')
    // console.log('sku_modal:',sku_modal);
    var home =  sku_modal.home;
    var commodity_class =  sku_modal.commodity_class;

    var commodity_list =new Object();
    commodity_list.commodity_id    =sku_modal.commodity_id;
    commodity_list.commodity_set_id=sku_modal.commodity_set_id;
    commodity_list.dlr_code        =sku_modal.dlr_code;
    commodity_list.dlr_name        =sku_modal.dlr_name;
    commodity_list.commodity_name  =sku_modal.commodity_name;
    commodity_list.is_gift  = 0;//是否赠品 1赠品
    commodity_list.is_grouped  = sku_modal.is_grouped;
    commodity_list.sku_list        = {};

    var image= sku_modal.cover_image;
    var  commodity_name=sku_modal.commodity_name;
    if ((!$(".sku-dlr").val() || !commodity_list.dlr_code) && $("input[nam='set_type']") == 1){
        layer.msg('请选择经销商');
        return ;
    }
    //新增数据添加到后面
    // console.log('type:',type)
    //添加
    if (type=='add'){
        //已经添加的直接跳过
        var index = $.inArray(commodity_list.commodity_set_id, comm_set_id_arr);
        if(index > -1){
            $("#sku-modal").modal('hide');
            return;
        }

        comm_set_id_arr.push(commodity_list.commodity_set_id);

        $.getJSON(getSkuUrl,{commodity_id:sku_modal.commodity_id,commodity_set_id:sku_modal.commodity_set_id},function (res) {
            var skus =res.data.sku_list;
            var sku_list = {}
            $.each(skus,function(i,val){
                var set_sku_id = val.set_sku_id;
                sku_list[set_sku_id] = val.price;
            });
            commodity_list.sku_list = sku_list;
            // console.log('commodity_list:',commodity_list)
            var json_st=JSON.stringify(commodity_list);

            //将已添加的商品入栈
            var html='<tr class="info" data>' +
                '<td style="width: 350px;">' +
                '<input type="hidden" name="home"  class="home" value="'+home+'"/><input type="hidden" '+
                'name="commodity_class"  class="commodity_class" value="'+commodity_class+'"/><img class="cover-image" '+
                ' src="'+image+'">'+commodity_name+'' +
                '</td>' +
                '<td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list" data-comm_name="'+commodity_name+'">删除</button>' +
                '</td>' +
                '</tr>';
            $("#haved-commodtiy").append(html);
            commodity_select()
        });

    }

    //选择添加完毕后，禁用添加按钮
    var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+commodity_list.commodity_set_id+"']");
    btn_obj.removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
    btn_obj.parents("button").html('已添加');
    $("#sku-modal").modal('hide');

    // console.log("add:comm_set_id_arr = "+comm_set_id_arr);
}

/**
 * 批量添加 赠品操作
 * @param sku_modal
 * @param type
 */
var comm_set_id_gift_arr = []
function skuConfirm1(sku_modal, type = 'add', key = 0){
    // console.log('sku_modal_gift:',sku_modal)
    var home =  sku_modal.home;
    var commodity_class_gift =  sku_modal.commodity_class;

    var commodity_list_gift =new Object();
    commodity_list_gift.commodity_id    =sku_modal.commodity_id;
    commodity_list_gift.commodity_set_id=sku_modal.commodity_set_id;
    commodity_list_gift.dlr_code        =sku_modal.dlr_code;
    commodity_list_gift.dlr_name        =sku_modal.dlr_name;
    commodity_list_gift.commodity_name  =sku_modal.commodity_name;
    commodity_list_gift.is_gift  = 1;//是否赠品 1赠品
    commodity_list_gift.is_grouped  = sku_modal.is_grouped;

    commodity_list_gift.sku_list        = {};

    var image= sku_modal.cover_image;
    var  commodity_name=sku_modal.commodity_name;
    if ((!$(".sku-dlr").val() || !commodity_list_gift.dlr_code) && $("input[nam='set_type']") == 1){
        layer.msg('请选择经销商');
        return ;
    }
    //新增数据添加到后面
    // console.log('type:',type)
    //添加
    if (type=='add'){
        // console.log('comm_set_id_gift_arr:')
        //已经添加的直接跳过
        var index = $.inArray(commodity_list_gift.commodity_set_id, comm_set_id_gift_arr);
        // console.log('index-gift:',index)
        if(index > -1){
            $("#sku-modal-gift").modal('hide');
            return;
        }

        comm_set_id_gift_arr.push(commodity_list_gift.commodity_set_id);
        $.getJSON(getSkuUrl,{commodity_id:sku_modal.commodity_id,commodity_set_id:sku_modal.commodity_set_id},function (res) {
            var skus =res.data.sku_list;
            var sku_list = {}

            $.each(skus,function(i,val){
                var set_sku_id = val.set_sku_id;
                sku_list[set_sku_id] = val.price;
            })
            commodity_list_gift.sku_list = sku_list;
            // console.log('commodity_list_gift:',commodity_list_gift)
            var json_st=JSON.stringify(commodity_list_gift);

            //将已添加的商品入栈
            var html='<tr class="info" data>' +
                '<td style="width: 350px;">' +
                '<input type="hidden" name="home"  class="home" value="'+home+'"/><input type="hidden" '+
                'name="commodity_class"  class="commodity_class" value="'+commodity_class_gift+'"/><img class="cover-image" '+
                ' src="'+image+'">'+commodity_name+'' +
                '</td>' +
                '<td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view-gift">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list-gift" data-comm_name="'+commodity_name+'">删除</button>' +
                '</td>' +
                '</tr>';
            $("#haved-commodtiy-gift").append(html);
            commodity_select_gift()
        });



    }

    //选择添加完毕后，禁用添加按钮
    var btn_obj=$('#add-comm-tbody-gift').find("[data-comm-set-id='"+commodity_list_gift.commodity_set_id+"']");
    // console.log('btn_obj:',btn_obj)
    btn_obj.removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
    btn_obj.parents("button").html('已添加');
    $("#sku-modal-gift").modal('hide');

    // console.log("add:comm_set_id_arr = "+comm_set_id_arr);
}
// });


$(".user_segment").click(function(){
    if($(this).val() == 1){
        $("#user_level_box").show();
        $("#car_onwer_box").hide();
        $("#member_box").show();
    }else{
        $("#user_level_box").hide();
        $("#member_box").hide();
        $("#car_onwer_box").show();
    }
    $("#haved-commodtiy").empty()
    $("#haved-commodtiy-gift").empty()
    // console.log('comm_set_id_arr:',comm_set_id_arr)
    // console.log('comm_set_id_gift_arr:',comm_set_id_gift_arr)
    if (comm_set_id_gift_arr.length >0 ||  comm_set_id_arr.length > 0) {
        var index = layer.open({
            title: ['操作提醒'],
            btn: ['确认', '取消'],
            content: "<div style='font-size: 15px'>更改人群类型会清空下方添加商品列表</div>",
            yes: function (res) {
                layer.close(index)
                comm_id = [];
                comm_set_id_arr = [];
                comm_set_id_gift_arr = [];
                commodity_list = {}
                commodity_list_gift = {}
                $("#haved-commodtiy").empty()
                $("#haved-commodtiy-gift").empty()

            }
        })
    }
    commTypeSearch(0)
    commTypeSearch3(0)

})

function commodity_select_gift(){
    preSpan = document.getElementById("spanPre_gift");
    firstSpan = document.getElementById("spanFirst_gift");
    nextSpan = document.getElementById("spanNext_gift");
    lastSpan = document.getElementById("spanLast_gift");
    pageNumSpan = document.getElementById("spanTotalPage_gift");
    currPageSpan = document.getElementById("spanPageNum_gift");
    blockTable = document.getElementById("commodity_select_gift");
    numCount = document.getElementById("commodity_select_gift").rows.length;
    // columnsCounts = blockTable.rows[0].cells.length;
    pageCount = 10;
    pageNum = parseInt(numCount/pageCount);
    if(0 != numCount%pageCount){
        pageNum += 1;
    }
    if(numCount > pageCount){
        $("#pagiDiv_gift").css('display','')
    }else{
        $("#pagiDiv_gift").css('display','none')
    }
    firstPage(currPageNum)
}
