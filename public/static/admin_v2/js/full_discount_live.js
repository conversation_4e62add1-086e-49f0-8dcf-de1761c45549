/**
 * Created by www on 17/11/24.
 */
//搜索商品列表

initComm(ajaxCommUrl);

$("#comm-type-search").on('click',function(){
    var obj = $(this).parents('.form-group');
    var comm_parent_id = obj.find("select[name='comm_parent_id']").val();
    var sub_comm_type_id = obj.find("select[name='sub_comm_type_id']").val();
    var three_comm_type_id = obj.find("select[name='three_comm_type_id']").val();
    var commodity_name = obj.find("input[name='commodity_name']").val();
    var commodity_class = obj.find("select[name='commodity_class']").val();

    var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+commodity_name+'&commodity_class='+commodity_class+'&start_time='+start_time+'&end_time='+end_time+'&discount_type='+discount_type;

    //额外参数验证
    if(typeof(isInitComm) != "undefined") {
        console.log(param);
        if(isInitComm == 0){
            return false;
        }
    }

    initComm(param);

});

/**
 * 活动商品列表搜索
 * @param isSkuConfirmAll
 * @returns {boolean}
 */
function commTypeSearch(isSkuConfirmAll = 0) {
    var comm_parent_id = $("select[name='comm_parent_id']").val();
    var sub_comm_type_id = $("select[name='sub_comm_type_id']").val();
    var three_comm_type_id = $("select[name='three_comm_type_id']").val();
    var commodity_name = $("input[name='commodity_name']").val();
    var commodity_class = $("select[name='commodity_class']").val();

    var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+commodity_name+'&commodity_class='+commodity_class+'&start_time='+start_time+'&end_time='+end_time+'&discount_type='+discount_type;

    //额外参数验证
    if(typeof(isInitComm) != "undefined") {
        if(isInitComm == 0){
            return false;
        }
    }
    console.log(param);

    initComm(param, isSkuConfirmAll);

}

function initComm(url, isSkuConfirmAll = 0) {
    var commodity_name = $("[name='commodity_name']").val()
    // url = url+'&start_time='+start_time+'&end_time='+end_time+'&discount_type='+discount_type+'&commodity_name='+commodity_name
    $.getJSON(url, null, function (resData) {
        resData.data.list.isSkuConfirmAll = isSkuConfirmAll;
        createPageComm(10, 10, resData.data.list.total, url);//创建翻页功能按钮，翻
        $("#add-comm-tbody").empty();
        if (resData.data.list.total > 0) {                          //页向后台请求连接
            setComm(url, isSkuConfirmAll);
        }
    });
}
function createPageComm(pageSize, buttons, total, url) {        //contracts_url为点击
    $("#comm-pagination").jBootstrapPage({      //功能按钮后需要访问action路径
        pageSize : pageSize,
        total : total,
        maxPageButton:buttons,
        onPageClicked: function(obj, page) {    //分页事件
            $("#add-comm-tbody").empty();
            setComm(url+"&page="+(page+1));
        }
    });
}

//获取并设置商品
function setComm(param, isSkuConfirmAll = 0){
    var url = ajaxCommUrl+'&start_time='+start_time+'&end_time='+end_time+'&discount_type='+discount_type;
    if(param!=null) url=param;

    $.get(url,function(res){
        var html='';
        var list = res.data.list.data;

        //当前页是否已全选择
        var skuConfirmAllUnm = 0;
        //当前页
        var current_page = res.data.list.current_page;
        //当前分页条数
        var per_page = res.data.list.per_page;
        //重置当前页所有的comm_set_id值
        currentPageCommSetIds = [];

        comm_set_id_arr=gethavedCommodityId();

        $.each(list,function(i,val){
            currentPageCommSetIds.push(val.commodity_set_id)

            var index= $.inArray(val.commodity_set_id, comm_set_id_arr);
            if((isSkuConfirmAll > 0) && (val.is_full != 1) && (val.is_pre != 1) && (val.is_seckill != 1) && (val.is_limit != 1)){
                index = 1;
                //批量添加操作
                skuConfirm(val)
                skuConfirmAllUnm +=1;
            }

            if(index>-1){
                var button='<button data-per-page="'+per_page+'" data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                    '" commodity_dlr_type_id="'+val.commodity_dlr_type_id+'" commodity_class="'+val.commodity_class+'" data-dlr-code="'+val.dlr_code+'" class="btn btn-sm  btn-fight btn-default active" disabled>已添加</button>';
            }else{
                if(val.is_full == 1){
                    var button='<button data-per-page="'+per_page+'" data-comm-id="'+val.commodity_id+'" data-comm-set-id="'+val.commodity_set_id+
                        '"data-dlr-code="'+val.dlr_code+'"  class=" btn btn-white active btn-sm " disabled>已参与其他满优惠</button>';
                }else if(val.is_pre == 1){
                    var button='<button data-per-page="'+per_page+'" data-comm-id="'+val.commodity_id+'"'+
                        ' class=" btn btn-white active btn-sm " disabled >已参与预售</button>';
                }else if(val.is_seckill == 1){
                    var button='<button data-per-page="'+per_page+'" data-comm-id="'+val.commodity_id+'"'+
                        ' class=" btn btn-white active btn-sm " disabled >已参与秒杀</button>';
                }else if(val.is_limit == 1){
                    var button='<button data-per-page="'+per_page+'" data-comm-id="'+val.commodity_id+'"'+
                        ' class=" btn btn-white active btn-sm " disabled >工时已参与限时折扣</button>';
                }else if(val.is_segment==1){
                    var button='<button data-per-page="'+per_page+'" data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                        ' class=" btn btn-white active btn-sm " disabled >定向人群不一致</button>';
                }else{
                    var button='<button data-per-page="'+per_page+'" data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                        '" commodity_dlr_type_id="'+val.commodity_dlr_type_id+'" commodity_class="'+val.commodity_class+'" data-dlr-code="'+val.dlr_code+'" class="btn btn-sm  btn-fight btn-default btn-white">选择添加</button>';
                }
            }

            html += '<tr id="comm_tab_tr_'+val.commodity_id+'">' +
                '<td class="text-left"><image class="cover-image" src="'+val.cover_image+'"><a class="init-commodity-preview" data-dlr-code="'+val.dlr_code+'" data-commodity-id="'+val.commodity_id+'">'+  val.commodity_name +'</a></td>' +
                '<td class="text-center">'+ val.up_down_channel_name +'</td>' +
                '<td class="text-center">'+ val.price +'</td>' +
                '<td class="text-center">'+ val.count_stock +'</td>' +
                '<td class="text-center">' + button +
                '</td> </tr>';
        });
        $("#add-comm-tbody").html(html);

        if(isSkuConfirmAll == 0 || !isSkuConfirmAll){
            skuConfirmAllUnm = getSkuConfirmAllUnm(comm_set_id_arr);
        }
        //增加批量添加按钮禁用操作
        setSkuConfirmAllStatus(isSkuConfirmAll, skuConfirmAllUnm, current_page, per_page);

    },'json');
}

//添加商品
$("#add-comm-tbody").on('click',".btn-fight",function (e) {
    e.preventDefault();
    var obj=$(this);

    var fhome = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
    var fcommodity_class = $("#haved-commodtiy").find("tr:first-child").find(".commodity_class").val();

    var home = $(this).attr('home');
    var commodity_class = $(this).attr('commodity_class');
    var commodity_dlr_type_id = $(this).attr('commodity_dlr_type_id');

    if(fhome != home){
        if(fhome == 1){
            //layer.msg('只能添加“快递到家“的商品，该商品为“到店安装”');
            //return false;
        }
        if(fhome == 0){
            //layer.msg('只能添加“到店安装“的商品，该商品为“快递到家”');
            //return false;
        }
    }

//    if( (typeof(fcommodity_class) != "undefined") && (fcommodity_class != commodity_class)){
//        layer.msg('活动商品只能选择相同类型/种类;<br/>您已选择的是:'+commodity_class_type[fcommodity_class]);
//        return false;
//    }

    $("#modal_home").val("");
    $("#modal_home").val(home);
    $("#modal_commodity_class").val("");
    $("#modal_commodity_class").val(commodity_class);
    $("#commodity_dlr_type_id").val("");
    $("#commodity_dlr_type_id").val(commodity_dlr_type_id);

    $.getJSON(getSkuUrl,{commodity_id:$(this).data('comm-id'),commodity_set_id:$(this).data('comm-set-id')},function (res) {
        var sku_list=res.data.sku_list;
        $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
        $(".sku-comm").html(res.data.commodity_row.commodity_name);
        $("#comm-name-header").html(res.data.commodity_row.commodity_name);
        var html='';
        $.each(sku_list,function(i,val){
            html += '<tr set_sku_id="'+val.id+'">' +
                '<td class="text-left">'+val.sku_val+'</td>' +
                '<td >'+ val.price +'</td>' +
                '<td >' + val.stock +
                '</td> </tr>';
        });

        $("#sku-modal").data("per-page",obj.data('per-page'));
        $("#sku-modal").data("commodity-class",obj.data('commodity-class'));
        $("#sku-modal").data("comm-id",obj.data('comm-id'));
        $("#sku-modal").data("comm-set-id",obj.data('comm-set-id'));
        $("#sku-modal").data("commodity-dlr-type",obj.data('commodity-dlr-type'));
        $("#sku-modal").data('type','add');
        $(".sku-tb").html(html);
        $("#sku-modal").modal('show');
        //dlr初始化
        $("#sku-modal").find(".sku-dlr").val("").data("sku-dlr","");
    });
})

//sku 点击确认
$("#sku-confirm").on('click',function (e) {
    var sku_modal=$(this).parents("#sku-modal");
    var per_page = sku_modal.data('per-page');

    var home =  sku_modal.find("#modal_home").val();
    var commodity_class =  sku_modal.find("#modal_commodity_class").val();
    var commodity_dlr_type_id =  sku_modal.find("#commodity_dlr_type_id").val();

    var commodity_list =new Object();
    commodity_list.commodity_id    =sku_modal.data('comm-id');
    commodity_list.commodity_set_id=sku_modal.data('comm-set-id');
    commodity_list.dlr_code        =sku_modal.find(".sku-dlr").data("sku-dlr");
    commodity_list.dlr_name        =sku_modal.find(".sku-dlr").val();
    commodity_list.commodity_name  =sku_modal.find("#comm-name-header").html();
    commodity_dlr_type_selected    =sku_modal.data('commodity-dlr-type');
    console.log(commodity_list)
    console.log("commodity_dlr_type_selected = "+commodity_dlr_type_selected);
    var image=sku_modal.find(".cover-image").attr("src");
    var  commodity_name=sku_modal.find(".sku-comm").html();
    if ((!$(".sku-dlr").val() || !commodity_list.dlr_code) && $("input[nam='set_type']") == 1){
        layer.msg('请选择经销商');
        return ;
    }
    //新增数据添加到后面
    var json_st=JSON.stringify(commodity_list);
    var type   =sku_modal.data('type');
    //添加
    if (type=='add'){
        if(oneCommodityClass==0){
            oneCommodityClass = commodity_class;
        }
        if(oneCommodityClass != commodity_class){
            $("#sku-modal").modal('hide');
            var msg1 = "只能添加“"+commodityClassJson[oneCommodityClass]+"“的商品，该商品为“"+commodityClassJson[commodity_class]+"”";
            layer.msg(msg1);
            return;
        }

        if(oneCommodityDlrTypeId==0){
            oneCommodityDlrTypeId = commodity_dlr_type_id;
        }
        // if(oneCommodityDlrTypeId != commodity_dlr_type_id){
        //     $("#sku-modal").modal('hide');
        //     var msg2 = "只能添加同种类型的商品，该商品为“"+commDlrTypeJson[oneCommodityDlrTypeId]+"“";
        //     layer.msg(msg2);
        //     return;
        // }

        //已经添加的直接跳过
        var index = $.inArray(commodity_list.commodity_set_id, comm_set_id_arr);
        if(index > -1){
            $("#sku-modal").modal('hide');
            return;
        }

        //将已添加的商品入栈
        comm_set_id_arr.push(commodity_list.commodity_set_id);
        var html='<tr class="info" commid="'+commodity_list.commodity_id+'" set_id="'+commodity_list.commodity_set_id+'"  data>' +
            '<td style="width: 350px;">' +
            '<input type="hidden" name="home"  class="home" value="'+home+'"/><input type="hidden" '+
            'name="commodity_class"  class="commodity_class" value="'+commodity_class+'"/><img class="cover-image" '+
            ' src="'+image+'">'+commodity_name+'' +
            '</td>' +
            '<td class="text-right">'+
            "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
            '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list" data-comm_name="'+commodity_name+'">删除</button>' +
            '</td>' +
            '</tr>';
        $("#haved-commodtiy").append(html);
        commodity_select()
        var delConfirmPage = $("#sku-confirm-all").data("current-page");
        //如果没读取到值，就不操作批量添加
        if(delConfirmPage){
            var skuConfirmAllUnm = getSkuConfirmAllUnm(comm_set_id_arr);
            //设置批量添加操作按钮
            setSkuConfirmAllStatus(1, skuConfirmAllUnm, delConfirmPage, per_page);
        }

    }else {
        //查看
        var html='<td style="width: 350px;">' +
            '<img class="cover-image" src="'+image+'">'+commodity_name+'</td><td class="text-right">'+
            "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
            '<button class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list" data-comm_name="'+commodity_name+'">删除</button>' +
            '</td>';
        // if(action=='edit'){
        //     if(activity_status!=1){
        //         html='<td style="width: 350px;">' +
        //             '<img class="cover-image" src="'+image+'">'+commodity_name+'</td><td class="text-right">'+
        //             "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
        //             '</td>';
        //     }
        // }

        $(".btn-fight-view").eq(type).parents('tr').html(html);
    }

    //选择添加完毕后，禁用添加按钮
    var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+commodity_list.commodity_set_id+"']");
    btn_obj.removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
    btn_obj.parents("button").html('已添加');
    $("#sku-modal").modal('hide');

    console.log("add:comm_set_id_arr = "+comm_set_id_arr);
})

/**
 * 获取当前页已选中的商品数量
 * @param comm_set_id_arr
 * @returns {number}
 */
function getSkuConfirmAllUnm(comm_set_id_arr){
    var num = 0;
    for (var i=0; i < currentPageCommSetIds.length; i++){
        var index = $.inArray(currentPageCommSetIds[i], comm_set_id_arr);
        if(index > -1){
            num ++;
        }
    }
    return num;
}

/**
 * 批量添加操作
 * @param sku_modal
 * @param type
 */
function skuConfirm(sku_modal, type = 'add'){
    console.log(sku_modal);
    var home =  sku_modal.home;
    var commodity_class =  sku_modal.commodity_class;

    var commodity_list =new Object();

    commodity_list.commodity_id    =sku_modal.commodity_id;
    commodity_list.commodity_set_id=sku_modal.commodity_set_id;
    commodity_list.dlr_code        =sku_modal.dlr_code;
    commodity_list.dlr_name        =sku_modal.dlr_name;
    commodity_list.commodity_name  =sku_modal.commodity_name;
    var image= sku_modal.cover_image;
    var  commodity_name=sku_modal.commodity_name;
    if ((!$(".sku-dlr").val() || !commodity_list.dlr_code) && $("input[nam='set_type']") == 1){
        layer.msg('请选择经销商');
        return ;
    }
    //新增数据添加到后面
    var json_st=JSON.stringify(commodity_list);

    //添加
    if (type=='add'){
        //已经添加的直接跳过
        var index = $.inArray(commodity_list.commodity_set_id, comm_set_id_arr);
        if(index > -1){
            $("#sku-modal").modal('hide');
            return;
        }

        //将已添加的商品入栈
        comm_set_id_arr.push(commodity_list.commodity_set_id);
        var html='<tr class="info" data>' +
            '<td style="width: 350px;">' +
            '<input type="hidden" name="home"  class="home" value="'+home+'"/><input type="hidden" '+
            'name="commodity_class"  class="commodity_class" value="'+commodity_class+'"/><img class="cover-image" '+
            ' src="'+image+'">'+commodity_name+'' +
            '</td>' +
            '<td class="text-right">'+
            "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
            '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list" data-comm_name="'+commodity_name+'">删除</button>' +
            '</td>' +
            '</tr>';
        $("#haved-commodtiy").append(html);
        commodity_select()
    }else {

    }

    //选择添加完毕后，禁用添加按钮
    var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+commodity_list.commodity_set_id+"']");
    btn_obj.removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
    btn_obj.parents("button").html('已添加');
    $("#sku-modal").modal('hide');

    console.log("add:comm_set_id_arr = "+comm_set_id_arr);
}

/**
 * 设置批量添加操作按钮
 * @param isSkuConfirmAll
 * @param skuConfirmAllUnm
 * @param per_page
 */
function setSkuConfirmAllStatus(isSkuConfirmAll, skuConfirmAllUnm, current_page, per_page){
    current_page = parseInt(current_page);
    //oneCommodityClass  oneCommodityDlrTypeId
    var obj = $(".comm-type-search2").parents('.form-group');
    var commodity_class = obj.find("select[name='commodity_class']").val();
    var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();
    var pageAllStr = current_page +'x'+ commodity_class +'v'+ commodity_dlr_type_id;

    //增加批量添加按钮禁用操作
    if(skuConfirmAllUnm == per_page){
        $("#sku-confirm-all")
            .removeClass("btn-white")
            .addClass("btn-default active")
            .html('已全部添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 0);
        skuConfirmAllCurrentPage.push(pageAllStr);
    }else if((isSkuConfirmAll > 0) && (skuConfirmAllUnm != per_page)){
        $("#sku-confirm-all")
            .removeClass("btn-default active")
            .addClass("btn-white")
            .html('批量添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 1);
        skuConfirmAllCurrentPage.splice(skuConfirmAllCurrentPage.indexOf(pageAllStr), 1);
    }else if((isSkuConfirmAll == 0) && (skuConfirmAllCurrentPage.indexOf(pageAllStr) != '-1')){
        $("#sku-confirm-all")
            .removeClass("btn-white")
            .addClass("btn-default active")
            .html('已全部添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 0);
    }else {
        $("#sku-confirm-all")
            .removeClass("btn-default active")
            .addClass("btn-white")
            .html('批量添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 1);
    }

    $("#sku-confirm-all").data("current-page", current_page);

    //批量操作的时候，需要重新选中当前批量选中操作
    if(isSkuConfirmAll > 0){
        //清除当前选中分页
        $("#comm-pagination li").removeClass('active');
        //选中当前页面
        $("#comm-pagination").find('[pnum="'+current_page+'"]').addClass("active");
    }

}

//查看商品
$("body").on("click",".btn-fight-view",function (e) {
    e.preventDefault();
    var set_sku_list=$(this).data('sku-list');
    var obj=$(this);
    $.getJSON(getSkuUrl,{commodity_id:set_sku_list.commodity_id,commodity_set_id:set_sku_list.commodity_set_id},function (res) {
        var sku_list=res.data.sku_list;
        $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
        $(".sku-comm").html(res.data.commodity_row.commodity_name);
        var html='';
        $.each(sku_list,function(i,val){
            html += '<tr set_sku_id="'+val.id+'">' +
                '<td class="text-left">'+val.sku_val+'</td>' +
                '<td >'+ val.price +'</td>' +
                '<td >' + val.stock +
                '</td> </tr>';
        });
        $("#sku-modal").data("comm-id",set_sku_list.commodity_id);
        $("#sku-modal").data("comm-set-id",set_sku_list.commodity_set_id);
        $("#comm-name-header").html(set_sku_list.commodity_name);
        var index=$(".btn-fight-view").index(obj);
        $("#sku-modal").data('type',index);
        $(".sku-tb").html(html);
        $("#sku-modal").modal('show');

        //dlr初始化
        $("#sku-modal").find(".sku-dlr").val(set_sku_list.dlr_name).data("sku-dlr",set_sku_list.dlr_code);


    });

    //alert($(this).data("sku-list"));

})

/**********删除sku************/
$("body").on("click",".del-sku-list",function (e) {
    e.preventDefault();
    var sku_list=$(this).parents("tr").find(".btn-fight-view").data("sku-list");
    var type=$(".del-sku-list").index($(this));
    var comm_name = $(this).data('comm_name');
    $("#del-sku-modal").find("#del-data-id").val(type).data('comm-set-id',sku_list.commodity_set_id);
    $("#del-sku-modal").find(".alert").html('<i class="fa fa-info-circle"></i>您选择对商品 '+comm_name+' 进行移除操作吗？');
    $("#del-sku-modal").modal('show');
})

$("#del-confirm").on("click",function () {
    var comm_set_id = $("#del-sku-modal").find("#del-data-id").data('comm-set-id');
    $(".del-sku-list").eq($("#del-sku-modal").find("#del-data-id").val()).parents('tr').remove();
    $("#del-sku-modal").modal('hide');
    var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+$("#del-sku-modal").find("#del-data-id").data('comm-set-id')+"']");
    btn_obj.removeClass("btn-default active").addClass("btn-white").attr("disabled",false).html('选择添加');
    comm_set_id_arr.splice($.inArray(comm_set_id,comm_set_id_arr),1);
    console.log("del:comm_set_id_arr = "+comm_set_id_arr);
    if(comm_set_id_arr.length==0){
        commodity_dlr_type_selected = 0;
    }

    var delConfirmPage = $("#sku-confirm-all").data("current-page");
    //如果没读取到值，就不操作批量添加
    if(delConfirmPage){
        if(comm_set_id_arr.length<1){
            oneCommodityClass = oneCommodityDlrTypeId = 0;
        }

        //设置批量添加操作按钮
        setSkuConfirmAllStatus(1, -1, delConfirmPage, 0);
    }

})

/**********删除sku************/



var sku_dlrs = [];
//获取专营店
$(".sku-dlr").on('click',function () {
    var commodity_set_id=$(this).parents('#sku-modal').data('comm-set-id');
    var dlr_sku_arr = sku_dlrs = $(this).data('sku-dlr').split(",");
    $.getJSON(getDlr_url,{commodity_set_id:commodity_set_id},function (resData) {
        if(resData.error==0){
            $("#dlr_content").empty();
            var data = $.parseJSON(resData.data);
            console.log(data);
            Custom.selectDlr(data,dlr_sku_arr,function (dlr_code_arr,dlr_name_arr) {
                console.log('dlr_code_arr = '+dlr_code_arr);
                console.log('dlr_name_arr = '+dlr_name_arr);
                var dlr_ids = dlr_code_arr.join(',');
                var dlr_names = dlr_name_arr.join(',');
                $(".sku-dlr").val(dlr_names)
                $(".sku-dlr").data("sku-dlr",dlr_ids);
            })
            // for (var i = 0;i<data.length;i++){
            //     var index= $.inArray(data[i].dlr_code, dlr_sku_arr);
            //     var checked = index>-1 ? 'checked' : '';
            //     var label = $('<label class="m-r-5 dlr_label text-hidden" data-toggle="tip" title="'+data[i].dlr_name+'"></label>');
            //     label.appendTo($("#dlr_content"));
            //     var input = $('<input type="checkbox"'+checked+' class="dlr_checkbox single min" data-id="'+data[i].dlr_code+'" data-title="'+data[i].dlr_name+'">');
            //     input.appendTo(label);
            //     label.append(data[i].dlr_name);
            // }
            // $("#dlr-modal").modal("show");
        }else {
            layer.msg(resData.msg);
        }
    })

})

//全选专营店
// $("#check_all").on("change",function () {
//     var check = $(this).attr("checked");
//     if(check){
//         $.each($(".dlr_checkbox.single"),function (i,ele) {
//             $(ele).attr("checked",true);
//         })
//     }else {
//         $.each($(".dlr_checkbox.single"),function (i,ele) {
//             $(ele).attr("checked",false);
//         })
//
//     }
// })
//
// $('.dlr_checkbox').live("click",function () {
//     var check = $(this).attr("checked");
//     var dlr_code = $(this).data('id');
//     if(!check){
//         if(activity_status!=1&&$.inArray(dlr_code, sku_dlrs)>-1){
//             layer.msg("活动正在进行，无法取消");
//             $(this).attr("checked",true);
//             return false;
//         }
//     }
//
// })

//添加专营店
// $("#dlr_add").on("click",function () {
//     var dlr_checked_code=[];
//     var  dlr_checked_name=[];
//     $('.dlr_checkbox.single').each(function (i) {
//         if($(this).attr("checked")){
//             dlr_checked_code.push($(this).attr("data-id"));
//             dlr_checked_name.push($(this).attr("data-title"));
//         }
//     })
//
//     var dlr_ids = dlr_checked_code.join(",");
//     var dlr_names = dlr_checked_name.join(",");
//     $(".sku-dlr").val(dlr_names)
//     $(".sku-dlr").data("sku-dlr",dlr_ids);
//
// })

function getSkuList() {
    var sku_list=[];
    $("#haved-commodtiy tr").each(function () {
        sku_list.push($(this).find(".btn-fight-view").data("sku-list"));
    })
    return sku_list;

}

$("body").on("click","#add_submit",function () {
    var $form=$("#add-form");
    var validate=$form.psly().validate();  //表单验证
    if(!validate) return false;
    // dealer_select 有hidden, 就清空 dlr_hide 的value
    if($("#dealer_select").hasClass("hidden")){
        $("#dlr_hide").attr('value','');
    }else{
        var dlr_value = $("#dlr_hide").val();
        //遍历 checkbox-inline, 把没有勾选的数据从dlr_hide 中去除
        $(".checkbox-inline input:not(:checked)").each(function(i,v){
            var reg = new RegExp(this.value,"g");//g,表示全部替换。
            dlr_value = dlr_value.replace(reg,"");
        })
        $("#dlr_hide").attr('value',dlr_value);
    }
    var up_down_channel_name = getUpDownChannel();
    var form_data=$form.serialize();
    var gd = get_data();
    if(gd==false){
        return false;
    }
    var full_discount_info =  JSON.stringify(gd);
    var form_data=$form.serialize();
    console.log(form_data);
    // return;
    var home = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
    var commodity_class = $("#haved-commodtiy").find("tr:first-child").find(".commodity_class").val();
    v_dis_type = '';
    if(user_segment == 1 ){
        $(".v_dis_type").each(function(){
           var v_dis_type = v_dis_type + $(this).val() + ","
        })
    }
    carv_dis_type = '';
    if(user_segment == 2 ){
        $(".carv_dis_type").each(function(){
           var  carv_dis_type = carv_dis_type + $(this).val() + ","
        })

    }

    var user_segment = $('input:radio[name=user_segment]:checked').val();
    var user_segment_options = $('input[name="user_segment_options"]:checked').val();
    if(user_segment == 1){
        var counts = $("#member_box .rule_new_box").find("table").find("tr").eq(0).find(".stylelt").size();
        var num = 0
        var is_user_level_die = 0
        var ret_num = 0;
        var which_select = selected_options();
       // counts = counts - which_select;
        $(".v_dis_type").each(function(i,v){
            new_num = parseFloat($(v).val());
            if (i > 0 && ret_num > new_num) {

                is_user_level_die = 1;
            } else {
                ret_num = new_num;
            }
            if(  (i+1) % counts == 0 ){
                num = 0
                ret_num = 0;
            }
        })

        if(is_user_level_die == 1){
            layer.msg('低等级优惠大于高等级优惠');
            return false;
        }
    }



    if (user_segment == 2) {
        var data = [];
        var no_car_val_old = 0;
        var no_car_val_new = 0;
        var is_die = 0;
        var is_car_die = 0;
        $('#car_onwer_box').find('table tr').each(function (index,value){

            if(index >0 ){

                var no_car = $(this).find('.tip_show_0').val();
                var car = $(this).find('.tip_show_1').val();

                if (index == 1) {
                    if (no_car == '') {
                        no_car_val_old = 0
                    } else {
                        no_car_val_old = 1;
                    }
                } else {
                    if (no_car == '') {
                        no_car_val_new = 0;
                    } else {
                        no_car_val_new = 1;
                    }
                    if (no_car_val_new != no_car_val_old) {
                        is_die = 1;
                    }
                    no_car_val_old = no_car_val_new
                }

                if (no_car != '' && parseFloat(no_car) > parseFloat(car)) {
                    is_car_die = 1;
                }


                if  (set_type == 6) {
                    var a_car = $(this).find('.tip_show_2').val();

                    if (no_car != '') {
                        if (no_car > a_car) {
                            is_car_die = 1;
                        }
                        // 日产和pz1a
                        if (car > a_car) {
                            is_car_die = 1;
                        }
                    }
                }
            }
        });
        if (is_die == 1) {
            layer.msg('多个梯度，请保证多个梯度资格一致');
            return false
        }
        if (is_car_die == 1) {
            layer.msg('车主的优惠力度需大于等于非车主');
            return false
        }
    }

    // if(user_segment == 2){
    //     var is_user_level_die = 0
    //     var ret_num = 0;
    //     var rowcount = 0;
    //     $(".level_item").each(function(i,v){
    //         var rowcount_item = 0
    //         if(set_type == 6){
    //             var no_car = $(this).find(".tip_show_0").val()
    //             var nissan_car = $(this).find(".tip_show_1").val()
    //             var aryi_car = $(this).find(".tip_show_2").val()
    //             if(no_car > 0) rowcount_item = rowcount_item +1;
    //             if(nissan_car > 0) rowcount_item = rowcount_item +1;
    //             if(aryi_car > 0) rowcount_item = rowcount_item +1;
    //             if(i == 0){
    //                 rowcount = rowcount_item;
    //             }else{
    //                 if(rowcount != rowcount_item){
    //                     is_user_level_die = 2;
    //                 }
    //             }
    //
    //             if(nissan_car != ''){
    //                 if(dis_type == 1) {//折扣
    //                     if(aryi_car > nissan_car ){
    //                         is_user_level_die = 1;
    //                     }
    //                 }else{
    //                     if(aryi_car < nissan_car){
    //                         is_user_level_die =1
    //                     }
    //                 }
    //             }
    //
    //             if(no_car !=''){
    //                 if(dis_type == 1) {//折扣
    //                     if(aryi_car > no_car ){
    //                         is_user_level_die = 1;
    //                     }
    //                 }else{
    //                     if(aryi_car < no_car){
    //                         is_user_level_die =1
    //                     }
    //                 }
    //             }
    //
    //             if(no_car != '' && nissan_car !=''){
    //
    //                 if(dis_type == 1) {//折扣
    //                     if(nissan_car > no_car || aryi_car > nissan_car){
    //                         is_user_level_die = 1
    //                     }
    //                 }else{
    //                     if(nissan_car < no_car || nissan_car < nissan_car){
    //                         is_user_level_die =1
    //                     }
    //                 }
    //             }
    //         }else{
    //             var no_car = $(this).find(".tip_show_0").val()
    //             var nissan_car = $(this).find(".tip_show_1").val()
    //
    //             if(no_car != ''){
    //                 if(dis_type == 1) {//折扣
    //                     if(nissan_car > no_car){
    //                         is_user_level_die = 1;
    //                     }
    //                 }else{
    //                     if(nissan_car < no_car){
    //                         is_user_level_die =1
    //                     }
    //                 }
    //             }
    //         }
    //     })
    //     if(is_user_level_die == 1){
    //         layer.msg('车主的优惠力度需大于等于非车主');
    //         return false;
    //     }
    //     if(is_user_level_die == 2){
    //         layer.msg('梯度要一致');
    //         return false;
    //     }
    // }

    // 活动图片
    var activity_image=[];
    $(".activity_image_group .goods_pic").each(function(){
        $(this).find('li img').each(function(){
            activity_image.push($(this).attr("image-value"));
        });
    });

    var data=$.param({sku_list:getSkuList(),'commodity_class':commodity_class,'user_segment_options':user_segment_options,'user_segment':user_segment,'home':home,'up_down_channel_name':up_down_channel_name,full_discount_rules:full_discount_info,'v_dis_type':v_dis_type,'carv_dis_type':carv_dis_type,'activity_image':activity_image}) + '&' + form_data;
    Custom.ajaxPost(save_url,data,null,index_url);
})

$("body").on("click","#edit_submit",function () {
    var $form=$("#edit-form");
    var validate=$form.psly().validate();  //表单验证
    if(!validate) return false;
    // dealer_select 有hidden, 就清空 dlr_hide 的value
    if($("#dealer_select").hasClass("hidden")){
        $("#dlr_hide").attr('value','');
    }else{
        var dlr_value = $("#dlr_hide").val();
        //遍历 checkbox-inline, 把没有勾选的数据从dlr_hide 中去除
        $(".checkbox-inline input:not(:checked)").each(function(i,v){
            var reg = new RegExp(this.value,"g");//g,表示全部替换。
            dlr_value = dlr_value.replace(reg,"");
        })
        $("#dlr_hide").attr('value',dlr_value);
    }
    var up_down_channel_name = getUpDownChannel();
    var form_data=$form.serialize();
    var gd = get_data();

    if(gd== false){
        return false;
    }
    var user_segment = $('input:radio[name=user_segment]:checked').val();
    if(user_segment == 1){
        var counts = $("#member_box .rule_new_box").find("table").find("tr").eq(0).find(".stylelt").size() ;
        var num = 0
        var is_user_level_die = 0
        var ret_num = 0;
        $(".v_dis_type").each(function(i,v){

            //old_num = $(v).val();
            old_num = parseFloat($(v).val());
           console.log(ret_num,old_num)
            if (i > 0 && ret_num > old_num) {
                is_user_level_die = 1;
            } else {
                ret_num = old_num;
            }
            if(  (i+1) % counts == 0 ){
                num = 0
                ret_num = 0;
            }
        })
        if(is_user_level_die == 1){
            layer.msg('低等级优惠大于高等级优惠');
            return false;
        }
    }

    // if(user_segment == 2){
    //     var counts = $("#car_onwer_box .rule_new_box").find("table").find("tr").eq(0).find(".stylelt").size() -1;
    //     var is_user_level_die = 0
    //     var ret_num = 0;
    //     $(".carv_dis_type").each(function(i,v){
    //         var old_num = $(v).val();
    //         if (ret_num > 0 && ret_num > old_num) {
    //             is_user_level_die = 1;
    //         } else {
    //             ret_num = old_num;
    //         }
    //         if(  (i+1) % counts == 0 ){
    //             ret_num = 0;
    //         }
    //     })
    //
    //     if(is_user_level_die == 1){
    //         layer.msg('车主的优惠力度需大于等于非车主');
    //         return false;
    //     }
    // }
    if (user_segment == 2) {
        var data = [];
        var no_car_val_old = 0;
        var no_car_val_new = 0;
        var is_die = 0;
        var is_car_die = 0;
        $('#car_onwer_box').find('table tr').each(function (index,value){
            if(index >0 ){
                var no_car = $(this).find('.tip_show_0').val();
                var car = $(this).find('.tip_show_1').val();

                if (index == 1) {
                    if (no_car == '') {
                        no_car_val_old = 0
                    } else {
                        no_car_val_old = 1;
                    }
                } else {
                    if (no_car == '') {
                        no_car_val_new = 0;
                    } else {
                        no_car_val_new = 1;
                    }
                    if (no_car_val_new != no_car_val_old) {
                        is_die = 1;
                    }
                    no_car_val_old = no_car_val_new
                }

                if (no_car != '' && parseFloat(no_car) > parseFloat(car)) {
                    is_car_die = 1;
                }

                if  (set_type == 6) {
                    var a_car = $(this).find('.tip_show_2').val();

                    if (no_car != '') {
                        if (no_car > a_car) {
                            is_car_die = 1;
                        }
                        // 日产和pz1a
                        if (car > a_car) {
                            is_car_die = 1;
                        }
                    }
                }
            }
        });
        if (is_die == 1) {
            layer.msg('多个梯度，请保证多个梯度资格一致');
            return false
        }
        if (is_car_die == 1) {
            layer.msg('车主的优惠力度需大于等于非车主');
            return false
        }
    }


    var full_discount_info =  JSON.stringify(gd);
    var user_segment = $('input:radio[name=user_segment]:checked').val();
    var home = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
    var commodity_class = $("#haved-commodtiy").find("tr:first-child").find(".commodity_class").val();

    var user_segment_options = $('input[name="user_segment_options"]:checked').val();
    var data=$.param({sku_list:getSkuList(),'commodity_class':commodity_class,'user_segment_options':user_segment_options,'user_segment':user_segment,'home':home,'up_down_channel_name':up_down_channel_name,full_discount_rules:full_discount_info}) + '&' + form_data;
    Custom.ajaxPost(save_url,data,null,index_url);
})

$("[name='discount_type']").on('change', function (){
    discount_type=$(this).val();
    initComm(ajaxCommUrl);
})

//更改活动时间
$('input[name=start_time],input[name=end_time]').on('blur',function(e){
    var s_time = $('input[name=start_time]').val();
    var e_time = $('input[name=end_time]').val();
    if(start_time != s_time || end_time != e_time){
        if($('#haved-commodtiy tr').length != 0 ){
            alert("更改活动时间会清空下方添加商品列表");
        }
        start_time = s_time;
        end_time = e_time;
        $('#haved-commodtiy').text('');
        $("#sku-confirm-all")
            .removeClass("btn-default active")
            .addClass("btn-white")
            .html('批量添加');
        $("#sku-confirm-all").data('is-sku-confirm-all',1)
        comm_id = [];
        comm_set_id_arr = [];
        skuConfirmAllCurrentPage = []

        var commodity_class = $("select[name='commodity_class']").val();
        var commodity_dlr_type_id = $("select[name='commodity_dlr_type_id']").val();
        var comm_parent_id = $("select[name='comm_parent_id']").val();
        var page = $("#sku-confirm-all").data("current-page");
        var user_segment = $('input:radio[name=user_segment]:checked').val()
        if(user_segment == 'undefined') user_segment = 0;
        ajaxCommUrl = ajaxCommUrl2 + '&commodity_dlr_type_id='+commodity_dlr_type_id+ '&page='+page + '&user_segment='+user_segment+'&commodity_class='+commodity_class+'&comm_parent_id='+comm_parent_id;


        initComm(ajaxCommUrl);
    }
})

function gethavedCommodityId() {
    var comm_set_id_arr=[];
    $("#haved-commodtiy tr").each(function () {
        var sku_list=$(this).find(".btn-fight-view").data("sku-list");
        comm_set_id_arr.push(sku_list.commodity_set_id);
    });
    return comm_set_id_arr;
}

function selected_options(){
    return  $('input[name="user_segment_options"]:checked').data('show-key');
}

//更新会员等级
$('input[name="user_segment_options"]').click(function(i,v){
    var which_select = selected_options();
    $("#member_box .rule_new_box").find('table tr:gt(1)').remove()
    for(var i = 0;i< 10 ; i++){
        $(".show_"+i).show()
    }
    for(var i = 0;i< which_select ; i++){
        $(".show_"+i).hide()
    }
    $("#member_box .rule_new_box").find('table tr input').val('')
    $("#haved-commodtiy").empty();
    initComm(ajaxCommUrl);
})