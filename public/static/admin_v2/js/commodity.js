$(".default-select2").select2();

var commodity_class_type = ['默认类型','实物商品','虚拟商品','电子卡劵','CSS流量套餐','平台卡券','取送车券'];

//获取sku列表值
function getSkuList() {
    var sku_list = [];
    var bj_type = $("[name='dd_commodity_type']:checked").val();

    $("#sku tbody>tr").each(function () {
        if(bj_type == 1 || bj_type == 3 || bj_type == 12){
            var sku_row = {
                sp_value_list: $(this).data('sku-list'),
                price: $(this).find(".sku_price").val(),
                stock: $(this).find(".sku_stock").val(),
                image: $(this).find(".add-sku-image").data("sku-image"),
                sku_code: $(this).find(".sku_code").val(),
                tax: $(this).find(".tax").val(),
                tax_code: $(this).find(".tax_code").val(),
                cost_price: $(this).find(".cost_price").val(),
                card_id: $(this).find(".card_id").val(),
                hours_id: $(this).find(".hours_id").val(),
                city_type: $(this).find(".city_type").val(),
                delivery_coefficient: $(this).find(".delivery_coefficient").val(),
            };
        }else{
            var sku_row = {
                sp_value_list: $(this).data('sku-list'),
                price: $(this).find(".sku_price").val(),
                stock: $(this).find(".sku_stock").val(),
                image: $(this).find(".add-sku-image").data("sku-image"),
                sku_code: $(this).find(".sku_code").val(),
                tax: $(this).find(".tax").val(),
                tax_code: $(this).find(".tax_code").val(),
                cost_price: $(this).find(".cost_price").val(),
                card_id: $(this).find(".card_id").val(),
                hours_id: $(this).find(".hours_id").val(),
                city_type: $(this).find(".city_type").val(),
                delivery_coefficient: $(this).find(".delivery_coefficient").val(),
            };
        }


        sku_list.push(sku_row);

    });
    // console.log('sku_list',sku_list);
    return sku_list;

}

//获取图片列表值
function getUpDownChannel() {
    var channel_list = [];
    $("#up_down .checkbox-inline input:checked").each(function () {
        channel_list.push($(this).attr("data-name"));
    })
    return channel_list;
}


//获取图片列表值
function getImageList() {
    var image_list = [];
    /*$(".image_group .goods_pic").each(function(){
     var sp_value_id=$(this).data("image-id");
     var image_obj =[];
     $(this).find('li img').each(function(){
     console.log($(this).attr("image-value"));
     image_obj.push($(this).attr("image-value"));
     });
     image_list.push({sp_value_id:sp_value_id,image_list:image_obj});

     });*/

    $(".image_group .goods_pic li img").each(function () {
        image_list.push($(this).attr("image-value"));
    })

    // console.log(image_list);
    return image_list;

}


//递归遍历 忘上层遍历
function car_series(parent) {
    if (parent) {
        // console.log(parent);
        var selector = "[data-parent='" + parent + "']";

        if ($(selector + ":checked").length >= $(selector).length) {
            $("#" + parent).attr("checked", true);
        } else {
            $("#" + parent).attr("checked", false);
        }
        //console.log($("#"+parent).eq(0).attr("data-parent"));
        if ($("#" + parent).eq(0).attr("data-parent")) {
            car_series($("#" + parent).eq(0).attr("data-parent"));
        } else {
            return false;
        }
    } else {
        return false;
    }

}
//车系点击状态改变
$(".car_checkbox").on('change', function () {
    var check = $(this).attr("checked");
    if (!check) check = false;
    if (!$(this).attr('data-parent')) {
        $(".car_checkbox").attr('checked', check);
    } else {
        if ($(this).attr('id')) {
            $("[data-parent='" + $(this).attr('id') + "']").attr("checked", check);
        }
        car_series($(this).attr('data-parent'));

    }
    //alert(33);

});
//车系初始化
function carIni() {
    $(".car-series .form-group").each(function () {
        car_series($(this).find(".min:eq(0)").attr('data-parent'));
    });
    setCarSeries()
}

//车系确定
var car_series_id = [];
$("#add-confirm").on("click", function () {
    setCarSeries();

});

//设置车系值
function setCarSeries() {
    car_series_id = [];
    var i = 0;
    var car_series = '';
    $(".car-series .min:checked").each(function () {
        car_series_id.push($(this).val());
        if (i <= 3) {
            car_series += $(this).data("car-series") + ',';
        }
        i++;
    });
    $("input[name='car_series_name']").val(car_series);
    $('#car-modal').modal('hide');

}

//品牌
$(".car-brand-type").on('click', function (e) {
    var brand_type = $(this).data("brand-type");

    var check = $(this).attr("checked");
    if (check) {
        $("[data-sub-brand-type='" + brand_type + "'].car_checkbox").attr("checked", true);

    } else {
        $("[data-sub-brand-type='" + brand_type + "'].car_checkbox").attr("checked", false);

    }

})


//批量处理初始化
function batchIni() {
    //批量设置价格
    $('.all_price').editable({
        success: function (response, newValue) {
            $(".sku_price").val(newValue);
            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^[0-9/.]*$/).test(value)) {
                return '格式有误';
            }
        }
    });

    $('#all_price_th_a').editable({
        success: function (response, newValue) {
            $(".sku_price").val(newValue);
            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^[0-9/.]*$/).test(value)) {
                return '格式有误';
            }
        }
    });

    // $('#all_price_td').editable({
    //     success: function (response, newValue) {
    //         $(".sku_price").val(newValue);
    //         $(".editable-cancel").click();
    //         return false;
    //     },
    //     validate: function (value) { //字段验证
    //         if (!$.trim(value)) {
    //             return '不能为空';
    //         }
    //         if (!(/^[0-9/.]*$/).test(value)) {
    //             return '格式有误';
    //         }
    //     }
    // });

    //批量设置成本价
    $('.all_cost_price').editable({
        success: function (response, newValue) {
            $(".cost_price").val(newValue);
            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            // if (!(/^[0-9]*$/).test(value)) {
            //     return '格式有误';
            // }
        }
    });


    //批量设置库存
    $('.all_stock').editable({
        success: function (response, newValue) {
            $(".sku_stock").val(newValue);
            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^[0-9]*$/).test(value)) {
                return '格式有误';
            }
        }

    });

    //批量设置发货系数
    $('.all_coefficient').editable({
        success: function (response, newValue) {
            $(".delivery_coefficient").val(newValue);
            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!(/^[0-9]*$/).test(value)) {
                return '格式有误';
            }
        }

    });


    //批量设置税率
    $('.all_tax').editable({
        success: function (response, newValue) {

            $(".tax").val(newValue);
            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^(0|6|9|11|13|16|17)$/).test(value)) {
                return '只可填写0、6、9、11、13、16、17中的取值';
            }
        }

    });


//批量设置税务编码
    $('.all_tax_code').editable({
        autotext: 'never',
        onblur: 'ignore',
        success: function (response, newValue) {
            $(".tax_code").val(newValue);
            $('.tax').val(public_rate)
            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^[0-9]*$/).test(value)) {
                return '只能填写19位纯数字';
            }
        },
    });

}

//选择备件，获得弹窗
var sku_code = "{$sku_code}";
$("body").on('click',".win_beijian",function(){
    var sku_code = $(this).parent().prev().find('input').val()
    //组合sku逻辑
    var appoint = $(this).parent().parent().parent().index()
    $.get(url_get_bj_table, {"appoint":appoint,"sku_code":sku_code}, function (res) {
        $("#bj-modal").html($(res));
        $("#bj-modal").modal("show");
        count_bj_ajax(ajaxGetBj_url + "?pagesize=10&appoint="+appoint+"&sku_code="+sku_code,appoint);
    });
});

// 选择延保套餐 获得弹框
$("body").on('click',".win_yanbao",function(){
    console.log('延保服务');
    var sku_code = $(this).parent().prev().find('input').val()
    console.log('sku_code:',sku_code);
    if (sku_code == undefined) {
        sku_code = '';
    }
    //组合sku逻辑
    var appoint = $(this).parent().parent().parent().index()
    $.get(url_get_yanbao_table, {"appoint":appoint,"n_product_code":sku_code}, function (res) {
        $("#yanbao-modal").html($(res));
        $("#yanbao-modal").modal("show");
        count_yanbao_ajax(ajaxGetYanBao_url + "?pagesize=10&appoint="+appoint+"&n_product_code="+sku_code,appoint);
    });
});


// //备件车型，获得弹窗
$("body").on('click',".e3s_car_detail",function(){
    layer.open({
        type:2,
        title: ['查看#备件名称#车型详情'],
        area: ['70%','80%'],
        content: ajaxGetBjCar_url+"?type=1&part_no="+$(this).data('part_no')

    })
})
$("body").on('click',".e3s_taocan_car_detail",function(){
    layer.open({
        type:2,
        title: ['查看#备件名称#车型详情'],
        area: ['70%','80%'],
        content: ajaxGetBjCar_url+"?type=2&product_type_id="+$(this).data('product_type_id')

    })
})

$(".sku_ckeck").on('change', function () {
    // console.log('新增规格------')
    //判断规格个数
    var count = $(".sku_ckeck:checked").parents('li').length;
    // console.log(count);
    if (count > 4) {
        $(this).attr("checked", false);
        layer.msg('规格不能超过4个');
        return false;
    }
    //判断首个规格
    var check = $(this).attr("checked");
    var sp_value_id = $(this).val();
    var sp_value_name = $(this).attr('sp-value-name');
    var pa_sp_id = $(".sku_ckeck:checked").parents('div:eq(0)').attr('sp-id');
    var sp_id = $(this).attr('sp-id');
    // console.log(pa_sp_id);
    var $goods_pic = $(".goods_pic");
    var bj_type = $("[name='dd_commodity_type']:checked").val();
    var area_value_id_arr = [];
    var area_value_name_arr = [];
    var area_type_value_id_arr = [];
    var area_type_value_name_arr = [];
    if(bj_type == 1 || bj_type == 3 || bj_type == 12){
        $(".sku_check_area:checked").each(function () {
            area_value_id_arr.push($(this).val());
            area_value_name_arr.push($(this).attr('sp-value-name'))
        });
    }else if(bj_type == 4){
        $(".sku_check_area_type:checked").each(function () {
            area_type_value_id_arr.push($(this).val());
            area_type_value_name_arr.push($(this).attr('sp-value-name'))
        });
    }

    var html = '';
    //处理图片逻辑
    //  $(".sku_ckeck:checked").parents('div:eq(0)').find('.sku_ckeck:checked').each(function(){
    //
    //  html        = getImageHtml($(this).val(),$(this).attr('sp-id'),$(this).attr('sp-value-name'));
    //  var html_obj=$(html);
    //  if($("#image-id-"+$(this).val()).length>0){
    //  //  alert($("#image-id-"+$(this).val()).find('ul').html());
    //  html_obj.find('ul').html($("#image-id-"+$(this).val()).find('ul').html());
    //  }
    //
    //  $(".image_group").append(html_obj);
    //  });
    //  $goods_pic.remove();
    //  if($(".goods_pic").length<=0){
    //
    //  html=getImageHtml(0,0,'默认')
    //  $(".image_group").append(html);
    //  }
    var sp_value_id_arr = [];
    $(".sku_ckeck:checked").each(function () {
        sp_value_id_arr.push($(this).val());
    });

    //  var sp_value_id=$("input[name='sp_value_id[]']").val();
    //组合sku逻辑
    $.get(url_get_sku_table, {sp_value_id_arr: sp_value_id_arr,bj_type:bj_type,area_value_id:area_value_id_arr,area_value_name:area_value_name_arr,area_type_value_id_arr:area_type_value_id_arr,area_type_value_name_arr:area_type_value_name_arr}, function (res) {
        var obj = $(res);
        obj.find("tr[data-sku-list]").each(function () {
            var sku_list = $(this).data('sku-list');
            var sku_price = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku_price").val();
            var sku_cost_price = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".cost_price").val();
            var sku_stock = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku_stock").val();
            var sku_tax = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".tax").val();
            var sku_tax_code = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".tax_code").val();
            var sku_code = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku_code").val();
            if (sku_price) {
                $(this).find(".sku_price").val(sku_price);
            }
            if($("input[name='dd_commodity_type']:checked").val() == 9){
                var th = '';
                th += '<div href="#" data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></div>'
                $("#all_price_th").html(th)
            }
            if (sku_stock) {
                $(this).find(".sku_stock").val(sku_stock);
            }
            if (sku_code) {
                $(this).find(".sku_code").val(sku_code);
            }
            if (sku_cost_price) {
                $(this).find(".cost_price").val(sku_cost_price);
            }
            if (sku_tax) {
                $(this).find(".tax").val(sku_tax);
            }
            if (sku_tax_code) {
                $(this).find(".tax_code").val(sku_tax_code);
            }
            var sku_image = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku-image-td").html();
            $(this).find(".sku-image-td").html(sku_image);
        });
         //console.log(obj)
        $("#sku").html(obj);
        //初始化
        batchIni();
    });
});

$(".sku_check_area").on('change', function () {
    // tab 地区
    var count = $(".sku_check_area:checked").parents('li').length;

    //判断首个规格
    var check = $(this).attr("checked");
    var sp_value_id = $(this).val();
    var sp_value_name = $(this).attr('sp-value-name');
    var pa_sp_id = $(".sku_ckeck:checked").parents('div:eq(0)').attr('sp-id');
    var sp_id = $(this).attr('sp-id');
    // console.log(pa_sp_id);
    var $goods_pic = $(".goods_pic");
    var bj_type = $("[name='dd_commodity_type']:checked").val();

    var area_value_id_arr = [];
    var area_value_name_arr = [];
    var area_type_value_id_arr = [];
    var area_type_value_name_arr = [];
    if(bj_type == 1 || bj_type == 3 || bj_type == 12){
        $(".sku_check_area:checked").each(function () {
            area_value_id_arr.push($(this).val());
            area_value_name_arr.push($(this).attr('sp-value-name'))
        });
    }else if(bj_type == 4){
        $(".sku_check_area_type:checked").each(function () {
            area_type_value_id_arr.push($(this).val());
            area_type_value_name_arr.push($(this).attr('sp-value-name'))
        });
    }
    var sp_value_id_arr = [];
    $(".sku_ckeck:checked").each(function () {
        sp_value_id_arr.push($(this).val());
    });
    //  var sp_value_id=$("input[name='sp_value_id[]']").val();
    //组合sku逻辑
    $.get(url_get_sku_table, {sp_value_id_arr: sp_value_id_arr,bj_type:bj_type,area_value_id:area_value_id_arr,area_value_name:area_value_name_arr,area_type_value_id_arr:area_type_value_id_arr,area_type_value_name_arr:area_type_value_name_arr}, function (res) {
        var obj = $(res);
        obj.find("tr[data-sku-list]").each(function () {
            var sku_list = $(this).data('sku-list');
            var sku_price = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku_price").val();
            var sku_cost_price = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".cost_price").val();
            var sku_stock = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku_stock").val();
            var sku_tax = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".tax").val();
            var sku_tax_code = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".tax_code").val();
            var sku_code = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku_code").val();
            if (sku_price) {
                $(this).find(".sku_price").val(sku_price);
            }
            if($("input[name='dd_commodity_type']:checked").val() == 9){
                var th = '';
                th += '<div href="#" data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></div>'
                $("#all_price_th").html(th)
            }
            if (sku_stock) {
                $(this).find(".sku_stock").val(sku_stock);
            }
            if (sku_code) {
                $(this).find(".sku_code").val(sku_code);
            }
            if (sku_cost_price) {
                $(this).find(".cost_price").val(sku_cost_price);
            }
            if (sku_tax) {
                $(this).find(".tax").val(sku_tax);
            }
            if (sku_tax_code) {
                $(this).find(".tax_code").val(sku_tax_code);
            }
            var sku_image = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku-image-td").html();
            $(this).find(".sku-image-td").html(sku_image);
        });
        // console.log(obj)
        $("#sku").html(obj);
        //初始化
        batchIni();
    });


});

$(".sku_check_area_type").on('change', function () {
    // tab 地区
    var count = $(".sku_check_area_type:checked").parents('li').length;

    //判断首个规格
    var check = $(this).attr("checked");
    var sp_value_id = $(this).val();
    var sp_value_name = $(this).attr('sp-value-name');
    var pa_sp_id = $(".sku_ckeck:checked").parents('div:eq(0)').attr('sp-id');
    var sp_id = $(this).attr('sp-id');
    // console.log(pa_sp_id);
    var $goods_pic = $(".goods_pic");
    var bj_type = $("[name='dd_commodity_type']:checked").val();

    var area_value_id_arr = [];
    var area_value_name_arr = [];
    var area_type_value_id_arr = [];
    var area_type_value_name_arr = [];
    if(bj_type == 1 || bj_type == 3 || bj_type == 12){
        $(".sku_check_area:checked").each(function () {
            area_value_id_arr.push($(this).val());
            area_value_name_arr.push($(this).attr('sp-value-name'))
        });
    }else if(bj_type == 4){
        $(".sku_check_area_type:checked").each(function () {
            area_type_value_id_arr.push($(this).val());
            area_type_value_name_arr.push($(this).attr('sp-value-name'))
        });
    }
    var sp_value_id_arr = [];
    $(".sku_ckeck:checked").each(function () {
        sp_value_id_arr.push($(this).val());
    });
    //  var sp_value_id=$("input[name='sp_value_id[]']").val();
    //组合sku逻辑
    $.get(url_get_sku_table, {sp_value_id_arr: sp_value_id_arr,bj_type:bj_type,area_value_id:area_value_id_arr,area_value_name:area_value_name_arr,area_type_value_id_arr:area_type_value_id_arr,area_type_value_name_arr:area_type_value_name_arr}, function (res) {
        var obj = $(res);
        obj.find("tr[data-sku-list]").each(function () {
            var sku_list = $(this).data('sku-list');
            var sku_price = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku_price").val();
            var sku_cost_price = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".cost_price").val();
            var sku_stock = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku_stock").val();
            var sku_tax = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".tax").val();
            var sku_tax_code = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".tax_code").val();
            var sku_code = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku_code").val();
            if (sku_price) {
                $(this).find(".sku_price").val(sku_price);
            }
            if($("input[name='dd_commodity_type']:checked").val() == 9){
                var th = '';
                th += '<div href="#" data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></div>'
                $("#all_price_th").html(th)
            }
            if (sku_stock) {
                $(this).find(".sku_stock").val(sku_stock);
            }
            if (sku_code) {
                $(this).find(".sku_code").val(sku_code);
            }
            if (sku_cost_price) {
                $(this).find(".cost_price").val(sku_cost_price);
            }
            if (sku_tax) {
                $(this).find(".tax").val(sku_tax);
            }
            if (sku_tax_code) {
                $(this).find(".tax_code").val(sku_tax_code);
            }
            var sku_image = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku-image-td").html();
            $(this).find(".sku-image-td").html(sku_image);
        });
        // console.log(obj)
        $("#sku").html(obj);
        //初始化
        batchIni();
    });


});

function getImageHtml(image_id, sp_id, image_name) {
    var html = ' <div class="dndc-upload-pic goods_pic" sp-id="' + sp_id + '"data-image-id="' + image_id + '" id="image-id-' + image_id + '"><label class="sp-value m-t-10 m-l-10">' + image_name + '</label> <label> <a href="javascript:;" class="btn-image btn btn-primary  m-r-5">上传图片</a>'
        + '</label><p>支持扩展名:.png,.jpg<br/>最多支持5张,限制50K内</p> <ul> </ul></div>';
    return html;
}

//图片上传
var image_sp_value_id = '';
$('.image_group').on('click', '.btn-image', function () {
    image_sp_value_id = $(this).parents('.goods_pic').data('image-id')
    var image_coutn = $(this).parents('.goods_pic').find('li').length;
    if (image_coutn >= 5) {
        layer.msg('图片最多能上传5张');
        return;
    }
    $("#goods-image-input").click();

});
$(".image_group").on('click', "del", function () {
    $(this).parents('li').remove();
});
//修改图片
var on_span = 0;
$(".image_group").on('click', "span", function () {
    $(this).parents('li').addClass('on_span');
    on_span = 1;
    $("#goods-image-input").click();
    // $("#goods-image-input").click($(this));
    // var files = this.files;
    // var cc_rr = check_file(files);
    // if(cc_rr==1){
    //     return;
    // }
    // Custom.ajaxFileUpload('goods-image-input',{file_path:'commodity'},function(res){
    //     if(res.error==0){
    //         var data=res.data
    //         var html='<img image-value="'+data.image+'" src="'+data.upload_url+data.image+'"> <del></del><span>修改</span> ';
    //         $(this).parents('li').html(html);
    //     }else{
    //         layer.msg(res.msg);
    //     }
    //     $("#goods-image-input").val("");
    //     image_sp_value_id='';
    // })
});


$(".image_group").on('change', "#goods-image-input", function (e) {

    /**
     * begin
     *
     * 此处有修改
     * 修改人：吴炜文
     * 时间2017.10.20 16:00
     * 修改内容：上传图片大小、格式做限制
     */
    var files = this.files;
    var cc_rr = check_file(files);
    if (cc_rr == 1) {
        return;
    }
    /**
     * end
     */

    Custom.ajaxFileUpload('goods-image-input', {file_path: 'commodity'}, function (res) {
        if (res.error == 0) {
            var data = res.data;
            if (on_span == 1) {
                var html = '<img image-value="' + data.image + '" src="' + data.upload_url + data.image + '"> <del></del><span>修改</span> ';
                $('.on_span').html(html);
                $('.on_span').removeClass('on_span');
                on_span = 0;
            } else {
                var html = '<li class="move-item"> <img image-value="' + data.image + '" src="' + data.upload_url + data.image + '"> <del></del><span>修改</span> </li>';
                $("#image-id-" + image_sp_value_id).find('ul').append(html);
            }
        } else {
            layer.msg(res.msg);
        }
        $("#goods-image-input").val("");
        image_sp_value_id = '';
    })
});

function check_file(files) {
    if (files && files[0]) {
        var fileName = files[0].name;
        var fileSize = files[0].size;
        if (fileSize > 200 * 1024) {
            // console.log(1112);
            layer.msg("文件大小不能超过200k");
            return 1;
        }
        var fileType = fileName.substr(fileName.lastIndexOf('.') + 1, fileName.length);
        if (fileType != 'jpeg' && fileType != 'jpg' && fileType != 'png') {
            // console.log("fileType = " + fileType);
            layer.msg("文件格式不正确");
            return 1;
        }
    }
    return 0;
    // return ;
}
//上传视频
var video_path = $("#video_img").val();
if(video_path){
    video_path =[video_path];
    init_config = {type: 'video', filetype: "video/mp4", key: 1};
}else{
    video_path=[];
    init_config ='';
    console.info('没图片');
}
$("#file_video").fileinput({
    uploadUrl: AJAX_FILE_UPLOAD_URL, //上传的地址
    language: 'zh',
    theme: 'fas',
    allowedFileExtensions : ['avi','mp4'],
    // overwriteInitial: false,
    // maxFileSize: 0,
    allowedFileTypes: ['video'],
    overwriteInitial: false,
    showRemove: false,
    showUpload: false,
    showCaption: false,
    maxFileCount: 1,
    autoReplace: true,
    initialPreview: video_path,
    initialPreviewAsData: true,
    initialPreviewFileType: 'video', // image is the default and can be overridden in config below
    initialPreviewConfig: [
        init_config
    ],
    slugCallback: function(filename) {
        return filename.replace('(', '_').replace(']', '_');
    }
}).on("fileclear",function(event, data, msg){
    $("#video_img").val('');
});

// $("#file_video").fileinput({
//     uploadUrl: AJAX_FILE_UPLOAD_URL, //上传的地址
//     language: 'zh',
//     maxFileSize: 200 * 1024,
//     uploadAsync: true,
//     showRemove: true,
//     showUpload: false,
//     allowedFileExtensions: ['avi', 'mp4'],
//     allowedFileTypes: ['video'],
//     autoReplace: true,
//     dropZoneEnabled: true,
//     maxFileCount: 1,
//     msgUploadBegin:'',
//     overwriteInitial: false,
//     initialPreview: [
//         video_path
//     ],
//     initialPreviewAsData: true, // defaults markup
//     initialPreviewFileType: 'image', // image is the default and can be overridden in config below
//     initialPreviewConfig: [
//
//         {type: "video", filetype: "video/mp4", key: 1}
//     ],
//     slugCallback: function (filename) {
//         return filename.replace('(', '_').replace(']', '_');
//     }
//
// }).on("fileclear",function(event, data, msg){
//     if(!confirm("确定删除原文件？删除后不可恢复")){
//         return false;
//     }else{
//         $("#video_img").val('');
//     }
// });

$("#file_video").on("filebatchselected", function (event, files) {
    layer.load();
    Custom.ajaxFileUpload('file_video', {file_path: 'commodity',validate:"mp4,avi"}, function (res) {
        // $.hideLoading();
        layer.closeAll('loading');
        if (res.error == 0) {
            var data = res.data;
            console.info(data);
            $("#video_img").val(data.upload_url + data.image);
        } else {
            layer.msg(res.msg);
        }
        //                $("#goods-image-input").val("");
        //                image_sp_value_id='';
    })
});


$('.kv-file-remove').on('click',function(){
    $(this).unbind().closest('.file-preview-frame').fadeOut();
    $('.kv-fileinput-error').hide();
})
$('.file-preview-thumbnails').on('DOMSubtreeModified',function(){
    $('.file-preview-frame.krajee-default.file-preview-initial.file-sortable.kv-preview-thumb').hide();
})
// $("#file_video").on("fileclear",function(event, data, msg){
//     if(!confirm("确定删除原文件？删除后不可恢复")){
//         return false;
//     }else{
//         $("#video_img").val('');
//     }
// });



var sku_image_obj = "";
//sku单个图片上传
$("body").on("click", ".add-sku-image,.update-sku-image", function () {
    sku_image_obj = $(this);
    $("#sku-image-file").click();
})

$(".form-group").on("change", "#sku-image-file", function (e) {


    var files = this.files;
    if (files && files[0]) {
        var fileName = files[0].name;
        var fileSize = files[0].size;
        if (fileSize > 200 * 1024) {
            layer.msg("文件大小不能超过200k");
            return;
        }
        var fileType = fileName.substr(fileName.lastIndexOf('.') + 1, fileName.length);
        if (fileType != 'jpeg' && fileType != 'jpg' && fileType != 'png') {
            // console.log("fileType = " + fileType);
            layer.msg("文件格式不正确");
            return;
        }
    }

    Custom.ajaxFileUpload('sku-image-file', {file_path: 'commodity'}, function (res) {
        if (res.error == 0) {
            var data = res.data
            sku_image_obj.parents("tr").find(".add-sku-image").attr("data-sku-image", data.image);
            sku_image_obj.parents("tr").find(".pop-preview img").attr("src", data.image);
            //
            if (sku_image_obj.hasClass("add-sku-image")) {
                sku_image_obj.parents("tr").find(".parents-update").show();
                sku_image_obj.parents(".parents-add").hide();
            }

        } else {
            layer.msg(res.msg);
        }
        $("#sku-image-file").val("");
        sku_image_obj = '';
        // alert($("#sku-image-file").val());
    })

})
//删除sku 图片
$("body").on("click", ".del-sku-image", function (e) {

    $(this).parents("tr").find(".add-sku-image").attr("data-sku-image", "");
    $(this).parents("tr").find(".parents-add").show();
    $(this).parents(".parents-update").hide();
})

//商品类型
$("input[name='commodity_class']").on('change', function () {
    var commodity_class = $(this).val();
    console.log('commodity_class:', commodity_class)
    $('.shelves_sources_check').each(function (i, v) {
        $(v).removeAttr('disabled');
    })

    if (commodity_class == 3) {
        $("#commodity_card_ids").show();
    } else {
        $("#commodity_card_ids").hide();
    }
    if (commodity_class == 5) {
        $("#mark_coupon_id").show();
    } else {
        $("#mark_coupon_id").hide();
    }
    if (commodity_class == 6) {
        $("input[name='dd_commodity_type']:checked").removeAttr('checked');
        $('.dd_commodity_type_7').find('.editable-click').text('关联取送车券');
        $('.dd_commodity_type_7').show();
        ajaxcard7Url += "&card_type=7";
        $('#shelves_sources_check_5').removeAttr('checked');
        $('#shelves_sources_check_6').removeAttr('checked');
        $('#shelves_sources_check_7').removeAttr('checked');
        $('#shelves_sources_check_8').removeAttr('checked');
        $('#shelves_sources_check_8').attr('disabled', 'disabled');
        $("#sku_code input").removeAttr('readonly')
        $("#all_price_td input").removeAttr('readonly')
    }else if($("input[name='dd_commodity_type']:checked").val() != 7 && $("input[name='dd_commodity_type']:checked").val() != 8){
        $('.dd_commodity_type_7').hide();
    }
    // 启辰充电桩
    if (commodity_class == 7) {
        $('.shelves_sources_check').each(function (i, v) {
            $(v).removeAttr('checked');
            $(v).attr('disabled', 'disabled');
        })
        $('#shelves_sources_check_4').removeAttr('disabled');
        $('#shelves_sources_check_4').attr('checked', 'checked');
        $('#shelves_sources_check_7').removeAttr('disabled');
        $('#shelves_sources_check_7').attr('checked', 'checked');
    }
    // 日产充电桩
    if (commodity_class == 8) {
        $('.shelves_sources_check').each(function (i, v) {
            $(v).removeAttr('checked');
            $(v).attr('disabled', 'disabled');
        })
        $('#shelves_sources_check_4').removeAttr('disabled');
        $('#shelves_sources_check_4').attr('checked', 'checked');
        $('#shelves_sources_check_5').removeAttr('disabled');
        $('#shelves_sources_check_5').attr('checked', 'checked');
    }

    // 延保服务包
    if (commodity_class == 9) {
        $('.shelves_sources_check').each(function (i, v) {
            $(v).removeAttr('checked');
            $(v).attr('disabled', 'disabled');
        });

        $('#shelves_sources_check_5').removeAttr('disabled');
        $('#shelves_sources_check_5').attr('checked', 'checked');
        $('#shelves_sources_check_7').removeAttr('disabled');
        $('#shelves_sources_check_7').attr('checked', 'checked');

        $(".liability_clause").show();

        var th = '';
        th += '<div data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></div>'
        $("#all_price_th").html(th)
        var html = '';
        html += '<input onmouseover="this.title=this.value" readonly="readonly" type="text" data-parsley-errors-container="#element" class="form-control sku_price col-md-3 sku_price2">'
        html += '<span class="input-group-addon">元</span>'
        $("#all_price_td").html(html);

        $("#sku_code input").attr('readonly','readonly')
        $("#all_price_td input").attr('readonly','readonly')

        $(".win_yanbao").attr('style','display:block!important');
    } else {
        $(".liability_clause").hide();
        // var th = '';
        // th += '<a href="#" id="all_price_th_a" data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></a>'
        // console.log(th)
        // $("#all_price_th").html(th);
        // var html = '';
        // html += '<input onmouseover="this.title=this.value" type="text" data-parsley-errors-container="#element" class="form-control sku_price col-md-3 sku_price2" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">'
        // html += '<span class="input-group-addon" >元</span>'
        // $("#all_price_td").html(html);

        $("#sku_code input").removeAttr('readonly');
        $("#all_price_td input").removeAttr('readonly');
        $(".win_yanbao").attr('style','display:none!important');

    }

    var params = {"class_id":commodity_class}
    $.getJSON(tips_url, params, function (resData) {
        $('#tips').val(resData.data);
    });
})



function commodityClass(commodity_class)
{
    console.log('commodity_class:',commodity_class);
    // 日产充电桩
    if (commodity_class == 8) {
        $('.shelves_sources_check').each(function (i, v) {
            $(v).attr('disabled', 'disabled');
        })
        $('#shelves_sources_check_4').removeAttr('disabled');
        $('#shelves_sources_check_5').removeAttr('disabled');
    }
    // 延保服务包
    if (commodity_class == 9) {
        $('.shelves_sources_check').each(function (i, v) {
            $(v).attr('disabled', 'disabled');
        })
        $('#shelves_sources_check_5').removeAttr('disabled');
        $('#shelves_sources_check_7').removeAttr('disabled');
    }

}

function count_bj_ajax(urlparam,appoint) {
    $.getJSON(urlparam, null, function (resData) {
        createBjPage(10, 10, resData.data.total, urlparam);//创建翻页功能按钮，翻
        $("tbody#bj-body").empty();
        if (resData.data.total > 0) {                          //页向后台请求连接
            list_bj_ajax(urlparam,appoint);
        } else {
            // layer.msg("没有更多数据");
            $("#bj-modal").modal("show");
        }
    })
}


/*延保服务包*/
function count_yanbao_ajax(urlparam,appoint) {
    $.getJSON(urlparam, null, function (resData) {
        createYanBaoPage(10, 10, resData.data.total, urlparam);//创建翻页功能按钮，翻
        $("tbody#yanbao-body").empty();
        if (resData.data.total > 0) {                          //页向后台请求连接
            list_yan_bao_ajax(urlparam,appoint);
        } else {
            // layer.msg("没有更多数据");
            $("#yanbao-modal").modal("show");
        }
    });
}


function count_taocan_ajax(urlparam,appoint,add_key) {
    $.getJSON(urlparam, null, function (resData) {
        createTaoCanPage(10, 10, resData.data.total, urlparam,add_key);//创建翻页功能按钮，翻
        $("tbody#taocan-body").empty();
        if (resData.data.total > 0) {                          //页向后台请求连接
            list_taocan_ajax(urlparam,appoint,add_key);
        } else {
            // layer.msg("没有更多数据");
            $("#taocan-modal").modal("show");
        }
    })
}
function list_taocan_ajax(urlparam,appoint,add_key) {
    // console.log('appoint-------------',appoint)
    var is_checkbox = true;
    $.getJSON(urlparam, null, function (result) {
        var check_length = 0;
        for (var i = 0; i < result.data.data.length; i++) {   //json格式，多行数据的数组
            var tr = $("<tr></tr>");                      //一行记录
            tr.appendTo($("tbody#taocan-body"));
            var td = $("<td>" + (result.data.data[i].id ? result.data.data[i].id : '')  + "</td>");    //序号
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].maintain_group_code ? result.data.data[i].maintain_group_code : '') + "</td>");
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].oil_supplier ? result.data.data[i].oil_supplier : "") + "</td>");
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].part_name ? result.data.data[i].part_name : '') + "</td>");
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].maintain_group_name ? result.data.data[i].maintain_group_name : '') + "</td>");
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].car_brand_cn ? result.data.data[i].car_brand_cn : '') + "</td>");
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].belong_zone_code ? result.data.data[i].belong_zone_code : '') + "</td>");
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].maintain_total_count ? result.data.data[i].maintain_total_count : '') + "</td>");
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].saler_amount ? result.data.data[i].saler_amount : '') + "</td>");
            td.appendTo(tr);
            var td = $("<td>" +Math.round(result.data.data[i].saler_amount/result.data.data[i].discount)+ "</td>");  //销售原价
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].discount_amount ? result.data.data[i].discount_amount : '') + "</td>");
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].package_status ? result.data.data[i].package_status : '禁用') + "</td>");    //状态
            td.appendTo(tr);
            var td = $("<td><a href='javascript:void(0)' data-maintain_group_code=" +(result.data.data[i].maintain_group_code)+ " class='e3s_package'>" + ('明细') + "</a></td>");    //备件适用车型
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].product_variety_name ? result.data.data[i].product_variety_name : '') + "</td>");
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].product_type_name ? result.data.data[i].product_type_name : '') + "</td>");
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].product_type_code ? result.data.data[i].product_type_code : '') + "</td>");
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].discount ? result.data.data[i].discount : '') + "</td>");
            td.appendTo(tr);
            var td = $("<td><a href='javascript:void(0)' data-product_type_id=" +(result.data.data[i].product_type_id)+ " class='e3s_taocan_car_detail'>" + ('车型详情') + "</a></td>");    //备
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].dlr_order_switch_cn ? result.data.data[i].dlr_order_switch_cn : '') + "</td>");    //订货状态
            td.appendTo(tr);
            td = $("<td></td>"); //操作
            var index = $.inArray(result.data.data[i].id, bj_checked_id);
            var part_no = localStorage.getItem("taocan_no_"+add_key+"_"+appoint);
            var add_taocan = "add_taocan_local(this,'"+appoint+"','"+add_key+"')";
            if(part_no != null){
                if(part_no.length > 0){
                    var array = part_no.split(',')
                    var is_array = $.inArray(result.data.data[i].maintain_group_code, array);
                    if(is_array != -1) {
                        ++check_length
                        var option = $('<label>选择<input class="card_checkbox part_no" onchange="'+add_taocan +'" name="select_taocan" type="checkbox" data-price='+ result.data.data[i].saler_amount +'   data-bj_type='+ result.data.data[i].bj_type +'   data-discount='+ result.data.data[i].discount +' data-sku_code=' + result.data.data[i].maintain_group_code +' checked></label>');
                    }else{
                        var option = $('<label>选择<input class="card_checkbox part_no" onchange="'+add_taocan +'" name="select_taocan" type="checkbox" data-price='+ result.data.data[i].saler_amount +'  data-bj_type='+ result.data.data[i].bj_type +'   data-discount='+ result.data.data[i].discount +'  data-sku_code=' + result.data.data[i].maintain_group_code +'></label>');
                    }
                }else{
                    var option = $('<label>选择<input class="card_checkbox part_no" onchange="'+add_taocan +'" name="select_taocan" type="checkbox" data-price='+ result.data.data[i].saler_amount +'  data-bj_type='+ result.data.data[i].bj_type +'   data-discount='+ result.data.data[i].discount +' data-sku_code=' + result.data.data[i].maintain_group_code +'></label>');
                }
            }else{
                var option = $('<label>选择<input class="card_checkbox part_no" onchange="'+add_taocan +'" name="select_taocan" type="checkbox" data-price='+ result.data.data[i].saler_amount +'  data-bj_type='+ result.data.data[i].bj_type +'   data-discount='+ result.data.data[i].discount +'  data-sku_code=' + result.data.data[i].maintain_group_code +'></label>');
            }
            option.appendTo(td);
            td.appendTo(tr);
        }
        if(check_length == 10){
            $('input[name = select_all]').prop("checked","true")
        }else if(check_length == result.data.data.length){
            $('input[name = select_all]').prop("checked","true")
        }else{
            $('input[name = select_all]').removeAttr("checked")
        }
        $("#taocan-modal").modal("show");
    })
}

function count_contracts_ajax(urlparam) {
    $.getJSON(urlparam, null, function (resData) {
        createPage(5, 10, resData.data.total, urlparam);//创建翻页功能按钮，翻
        $("tbody#card-body").empty();
        if (resData.data.total > 0) {                          //页向后台请求连接
            list_contracts_ajax(urlparam);
        } else {
            // layer.msg("没有更多数据");
            $("#card-modal").modal("show");
        }
    })
}

function count_bj_car_ajax(urlparam) {
    $.getJSON(urlparam, null, function (resData) {
        createBjCarPage(5, 10, resData.data.total, urlparam);//创建翻页功能按钮，翻
        $("tbody#bj-car-body").empty();
        if (resData.data.total > 0) {                          //页向后台请求连接
            list_bj_car_ajax(urlparam);
        } else {
            // layer.msg("没有更多数据");
            $("#bj-car-modal").modal("show");
        }
    })
}

function createPage(pageSize, buttons, total, contracts_url) {        //contracts_url为点击
    $("#card-modal .pagination").jBootstrapPage({      //功能按钮后需要访问action路径
        pageSize: pageSize,
        total: total,
        maxPageButton: buttons,
        onPageClicked: function (obj, page) {
            $("tbody#card-body").empty();
            list_contracts_ajax(contracts_url + "&page=" + (page + 1));
        }
    });
}

function createBjPage(pageSize, buttons, total, contracts_url,sku_code) {        //contracts_url为点击
    $(".bj_div .pagination").jBootstrapPage({      //功能按钮后需要访问action路径
        pageSize: pageSize,
        total: total,
        maxPageButton: buttons,
        onPageClicked: function (obj, page) {
            $("tbody#bj-body").empty();
            list_bj_ajax(contracts_url + "&page=" + (page + 1)+"&sku_code" + sku_code,appoint);
        }
    });
}



function createYanBaoPage(pageSize, buttons, total, contracts_url,sku_code)
{
    $(".yanbao_div .pagination").jBootstrapPage({      //功能按钮后需要访问action路径
        pageSize: pageSize,
        total: total,
        maxPageButton: buttons,
        onPageClicked: function (obj, page) {
            $("tbody#yanbao-body").empty();
            list_yan_bao_ajax(contracts_url + "&page=" + (page + 1)+"&sku_code" + sku_code,appoint);
        }
    });
}


function createTaoCanPage(pageSize, buttons, total, contracts_url,add_key) {        //contracts_url为点击
    $(".bj_div .pagination").jBootstrapPage({      //功能按钮后需要访问action路径
        pageSize: pageSize,
        total: total,
        maxPageButton: buttons,
        onPageClicked: function (obj, page) {
            $("tbody#taocan-body").empty();
            list_taocan_ajax(contracts_url + "&page=" + (page + 1),appoint,add_key);
        }
    });
}

function createBjCarPage(pageSize, buttons, total, contracts_url) {        //contracts_url为点击
    $(".bj_car_div .pagination").jBootstrapPage({      //功能按钮后需要访问action路径
        pageSize: pageSize,
        total: total,
        maxPageButton: buttons,
        onPageClicked: function (obj, page) {
            $("tbody#bj-car-body").empty();
            list_bj_car_ajax(contracts_url + "&page=" + (page + 1));
        }
    });
}

var bj_checked_id = [];
var bj_checked_name = [];
function list_bj_ajax(urlparam,appoint) {
    // console.log('appoint-------------',appoint)
    var is_checkbox = true;
    $.getJSON(urlparam, null, function (result) {
        var check_length = 0;
        for (var i = 0; i < result.data.data.length; i++) {   //json格式，多行数据的数组
            var tr = $("<tr></tr>");                      //一行记录
            tr.appendTo($("tbody#bj-body"));
            var td = $("<td>" + (result.data.data[i].id ? result.data.data[i].id : '')  + "</td>");    //序号
            td.appendTo(tr);
            var td = $("<td>" + (result
                .data.data[i].variety_code_mid_name ? result.data.data[i].variety_code_mid_name : '') + "</td>");    //重点备件品类
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].part_no ? result.data.data[i].part_no : "") + "</td>");    //备件编码
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].part_name ? result.data.data[i].part_name : '') + "</td>");    //备件名称
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].part_brand_code_cn ? result.data.data[i].part_brand_code_cn : '') + "</td>");    //备件品牌
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].variety_code_big_name ? result.data.data[i].variety_code_big_name : '') + "</td>");    //项目大类
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].variety_code_small_name ? result.data.data[i].variety_code_small_name : '') + "</td>");    //项目小类
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].other_attr ? result.data.data[i].other_attr : '') + "</td>");    //其它属性
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].rep_part_no ? result.data.data[i].rep_part_no : '') + "</td>");    //替换件
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].sale_price ? result.data.data[i].sale_price : '') + "</td>");    //备件单价（元）
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].dlr_price ? result.data.data[i].dlr_price : '') + "</td>");    //成本价（元）
            td.appendTo(tr);
            var td = $("<td><a href='javascript:void(0)' data-part_no=" +(result.data.data[i].part_no)+ " class='e3s_car_detail'>" + ('查看') + "</a></td>");    //备件适用车型
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].spec_part_group ? result.data.data[i].spec_part_group : '') + "</td>");    //备件分组
            td.appendTo(tr);
            var td = $("<td><a href='javascript:void(0);' data-spec_part_group_id=" +(result.data.data[i].spec_part_group_id)+ " class='specific_dlr'>" + ('详情') + "</a></td>");
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].part_status_cn ? result.data.data[i].part_status_cn : '') + "</td>");    //状态
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].dlr_order_switch_cn ? result.data.data[i].dlr_order_switch_cn : '') + "</td>");    //订货状态
            td.appendTo(tr);
            td = $("<td></td>"); //操作
            var index = $.inArray(result.data.data[i].id, bj_checked_id);
            var part_no = localStorage.getItem("part_no_"+appoint);
            if(part_no != null){
                if(part_no.length > 0){
                    var array = part_no.split(',')
                    var is_array = $.inArray(result.data.data[i].part_no, array);
                    if(is_array != -1) {
                        ++check_length
                        var option = $("<label>选择<input class='card_checkbox part_no' onchange='add_local(this,"+appoint+")' name='select_part_no' type='checkbox' data-price='" + result.data.data[i].sale_price +"' data-id='" + result.data.data[i].id + "' data-part_no='" + result.data.data[i].part_no + "' checked></label>");
                    }else{
                        var option = $("<label>选择<input class='card_checkbox part_no' onchange='add_local(this,"+appoint+")' name='select_part_no' type='checkbox' data-price='" + result.data.data[i].sale_price +"' data-id='" + result.data.data[i].id + "' data-part_no='" + result.data.data[i].part_no + "'></label>");
                    }
                }else{
                    var option = $("<label>选择<input class='card_checkbox part_no' onchange='add_local(this,"+appoint+")' name='select_part_no' type='checkbox' data-price='" + result.data.data[i].sale_price +"' data-id='" + result.data.data[i].id + "' data-part_no='" + result.data.data[i].part_no + "'></label>");
                }
            }else{
                var option = $("<label>选择<input class='card_checkbox part_no' onchange='add_local(this,"+appoint+")' name='select_part_no' type='checkbox' data-price='" + result.data.data[i].sale_price +"' data-id='" + result.data.data[i].id + "' data-part_no='" + result.data.data[i].part_no + "'></label>");
            }
            option.appendTo(td);
            td.appendTo(tr);
        }
        // console.log(check_length)
        if(check_length == 10){
            $('input[name = select_all]').prop("checked","true")
        }else{
            $('input[name = select_all]').removeAttr("checked")
        }
        $("#bj-modal").modal("show");
    })
}




function list_yan_bao_ajax(urlparam,appoint) {
    $("#yanbao-body").empty();
    $.getJSON(urlparam, null, function (result) {
        console.log('result:',result);
        var check_length = 0;
        for (var i = 0; i < result.data.data.length; i++) {   //json格式，多行数据的数组
            var tr = $("<tr></tr>");                      //一行记录
            tr.appendTo($("tbody#yanbao-body"));
            var td = $("<td>" + (result.data.data[i].id ? result.data.data[i].id : '')  + "</td>");    //序号
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].car_series_code) + "</td>");    //车系编码
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].car_series_name) + "</td>");    //车系名称
            td.appendTo(tr);
            var td = $("<td>" + ( result.data.data[i].n_car_type) + "</td>");    //车型
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].n_car_type_remark) + "</td>");    //适用车型说明
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].n_product_code) + "</td>");    //延保产品编码
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].n_product_name) + "</td>");    //延保产品名称	
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].guarantee_month_num) + "</td>");    //延保时间（月）
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].n_guarantee_condition) + "</td>");    //延保购买条件	
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].n_guarantee_content) + "</td>");    //延保内容	
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].dlr_price ) + "</td>");    //专营店价格
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].use_price ) + "</td>");    //用户价
            td.appendTo(tr);
            if (result.data.data[i].start_month_qty == '0') {
                var td = $("<td>" + (result.data.data[i].start_month_qty + "<= 月数 <" + result.data.data[i].end_month_qty ) + "</td>");    //适用月数范围
            } else {
                var td = $("<td>" + (result.data.data[i].start_month_qty + "< 月数 <" + result.data.data[i].end_month_qty ) + "</td>");    //适用月数范围
            }
            td.appendTo(tr);
            if (result.data.data[i].start_mile_qty == '0') {
                var td = $("<td>" + (result.data.data[i].start_mile_qty + "<= 里程数 < " + result.data.data[i].end_mile_qty) + "</td>");    //适用里程数范围
            } else {
                var td = $("<td>" + (result.data.data[i].start_mile_qty + "< 里程数 < " + result.data.data[i].end_mile_qty) + "</td>");    //适用里程数范围
            }
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].car_brand_cn ) + "</td>");    //品牌名称
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].modified_date ) + "</td>");    //最后更新时间
            td.appendTo(tr);
            var td = $("<td>" + ((result.data.data[i].is_enable == 1)? '可用' : '停用' ) + "</td>");    //是否可用
            td.appendTo(tr);
            td = $("<td></td>"); //操作
            var n_product_code = localStorage.getItem("yan_bao_code_"+appoint);
            if(n_product_code != null){
                if(n_product_code.length > 0){
                    var array = n_product_code.split(',')
                    var is_array = $.inArray(result.data.data[i].n_product_code, array);
                    if(is_array != -1) {
                        ++check_length
                        var option = $("<label>选择<input class='card_checkbox n_product_code' onchange='add_yan_bao_local(this,"+appoint+")' name='select_n_product_code' type='checkbox' data-price='" + result.data.data[i].use_price +"' data-id='" + result.data.data[i].id + "' data-n_product_code='" + result.data.data[i].n_product_code + "' checked></label>");
                    }else{
                        var option = $("<label>选择<input class='card_checkbox n_product_code' onchange='add_yan_bao_local(this,"+appoint+")' name='select_n_product_code' type='checkbox' data-price='" + result.data.data[i].use_price +"' data-id='" + result.data.data[i].id + "' data-n_product_code='" + result.data.data[i].n_product_code + "'></label>");
                    }
                }else{
                    var option = $("<label>选择<input class='card_checkbox n_product_code' onchange='add_yan_bao_local(this,"+appoint+")' name='select_n_product_code' type='checkbox' data-price='" + result.data.data[i].use_price +"' data-id='" + result.data.data[i].id + "' data-n_product_code='" + result.data.data[i].n_product_code + "'></label>");
                }
            }else{
                var option = $("<label>选择<input class='card_checkbox n_product_code' onchange='add_yan_bao_local(this,"+appoint+")' name='select_n_product_code' type='checkbox' data-price='" + result.data.data[i].use_price +"' data-id='" + result.data.data[i].id + "' data-n_product_code='" + result.data.data[i].n_product_code + "'></label>");
            }
            option.appendTo(td);
            td.appendTo(tr);
        }
        $("#yanbao-modal").modal("show");
    });
}

//备件商品写进浏览器缓存
function add_local(i,appoint){
    var part_no = localStorage.getItem("part_no_"+appoint);
    var part_price = localStorage.getItem("part_price_"+appoint);
    var string = '';
    var string_price = '';
    if(i.checked){
        if(part_no != null){
            let array = part_no.split(',')
            var is_true = true;
            $.each(array,function(key,val){
                if(val == $(i).data('part_no')){
                    is_true = false;
                    return false;
                }
            })
            if(is_true){
                if(part_no.length > 0){
                    string += part_no+","+$(i).data('part_no')
                    string_price += part_price+","+$(i).data('price')
                }else{
                    string = $(i).data('part_no')
                    string_price = $(i).data('price')
                }
            }else{
                string = part_no
                string_price = part_price
            }
        }else{
            string = $(i).data('part_no')
            string_price = $(i).data('price')
        }
        localStorage.setItem("part_no_"+appoint, string);
        localStorage.setItem("part_price_"+appoint, string_price);
    }else{
        if(part_no != null){
            let array = part_no.split(',')
            var array_price = part_price.split(',')
            $.each(array,function(key,val){
                if(val != $(i).data('part_no')){
                    string += val + ","
                    string_price += array_price[key] + ','
                }
            })
            if(string.length >0){
                string = string.substr(0,string.length-1);
                string_price = string_price.substr(0,string_price.length-1);
                localStorage.setItem("part_no_"+appoint, string);
                localStorage.setItem("part_price_"+appoint, string_price);
            }else{
                localStorage.removeItem('part_no_'+appoint)
                localStorage.removeItem('part_price_'+appoint)
            }

        }
    }
}



// 延保服务包写进浏览器缓存
function add_yan_bao_local(i,appoint)
{
    var yan_bao_code = localStorage.getItem("yan_bao_code_"+appoint);
    var yan_bao_price = localStorage.getItem("yan_bao_price_"+appoint);
    var string = '';
    var string_price = '';
    if(i.checked){
        if(yan_bao_code != null){
            let array = yan_bao_code.split(',')
            var is_true = true;
            $.each(array,function(key,val){
                if(val == $(i).data('n_product_code')){
                    is_true = false;
                    return false;
                }
            })
            if(is_true){
                if(yan_bao_code.length > 0){
                    string += yan_bao_code+","+$(i).data('n_product_code')
                    string_price += yan_bao_price+","+$(i).data('price')
                }else{
                    string = $(i).data('n_product_code')
                    string_price = $(i).data('price')
                }
            }else{
                string = yan_bao_code
                string_price = yan_bao_price
            }
        }else{
            string = $(i).data('n_product_code')
            string_price = $(i).data('price')
        }
        localStorage.setItem("yan_bao_code_"+appoint, string);
        localStorage.setItem("yan_bao_price_"+appoint, string_price);
    }else{
        if(yan_bao_code != null){
            let array = yan_bao_code.split(',')
            var array_price = yan_bao_price.split(',')
            $.each(array,function(key,val){
                if(val != $(i).data('n_product_code')){
                    string += val + ","
                    string_price += array_price[key] + ','
                }
            })
            if(string.length >0){
                string = string.substr(0,string.length-1);
                string_price = string_price.substr(0,string_price.length-1);
                localStorage.setItem("yan_bao_code_"+appoint, string);
                localStorage.setItem("yan_bao_price_"+appoint, string_price);
            }else{
                localStorage.removeItem('yan_bao_code_'+appoint)
                localStorage.removeItem('yan_bao_price_'+appoint)
            }

        }
    }
}

//套餐写进浏览器缓存
function add_taocan_local(i,appoint,address_key)
{

    // 判断类型和折扣
    var bj_type = $(i).data('bj_type');
    if (bj_type == 12) {
        var discount = $(i).data('discount');
        if (discount != '0.7') {
            layer.msg('所选套餐折扣须为7折');
            $(i).prop('checked', false);
            return
        }
    }

    var taocan_no = localStorage.getItem("taocan_no_"+address_key+"_"+appoint);
    var taocan_price = localStorage.getItem("taocan_price_"+address_key+"_"+appoint);
    var string = '';
    var string_price = '';
    if(i.checked){
        if(taocan_no != null){
            let array = taocan_no.split(',')
            var is_true = true;
            $.each(array,function(key,val){
                if(val == $(i).data('sku_code')){
                    is_true = false;
                    return false;
                }
            })
            if(is_true){
                if(taocan_no.length > 0){
                    string += taocan_no+","+$(i).data('sku_code')
                    string_price += taocan_price+","+$(i).data('price')
                }else{
                    string = $(i).data('sku_code')
                    string_price = $(i).data('price')
                }
            }else{
                string = taocan_no
                string_price = taocan_price
            }
        }else{
            string = $(i).data('sku_code')
            string_price = $(i).data('price')
        }
        localStorage.setItem("taocan_no_"+address_key+"_"+appoint, string);
        localStorage.setItem("taocan_price_"+address_key+"_"+appoint, string_price);
    }else{
        if(taocan_no != null){
            let array = taocan_no.split(',')
            var array_price = taocan_price.split(',')
            $.each(array,function(key,val){
                if(val != $(i).data('sku_code')){
                    string += val + ","
                    string_price += array_price[key] + ','
                }
            })
            if(string.length >0){
                string = string.substr(0,string.length-1);
                string_price = string_price.substr(0,string_price.length-1);
                localStorage.setItem("taocan_no_"+address_key+"_"+appoint, string);
                localStorage.setItem("taocan_price_"+address_key+"_"+appoint, string_price);
            }else{
                localStorage.removeItem("taocan_no_"+address_key+"_"+appoint)
                localStorage.removeItem("taocan_price_"+address_key+"_"+appoint)
            }

        }
    }
}

function add_local_wi_code(i,appoint){
    var part_no = localStorage.getItem("wi_code_"+appoint);
    var hours_id = localStorage.getItem("hours_id_"+appoint);
    var string = '';
    var string_id = '';
    if(i.checked){
        if(part_no != null){
            let array = part_no.split(',')
            var is_true = true;
            $.each(array,function(key,val){
                if(val == $(i).data('wi_code')){
                    is_true = false;
                    return false;
                }
            })
            if(is_true){
                string = $(i).data('wi_code')
                string_id = $(i).data('hours_id')
            }else{
                string = part_no
                string_id = hours_id
            }
        }else{
            string = $(i).data('wi_code')
            string_id = $(i).data('hours_id')
        }
        localStorage.setItem("wi_code_"+appoint, string);
        localStorage.setItem("hours_id_"+appoint, string_id);
    }else{
        if(part_no != null){
            let array = part_no.split(',')
            $.each(array,function(key,val){
                if(val != $(i).data('wi_code')){
                    string += val
                    string_id += $(i).data('hours_id')
                }
            })
            if(string.length >0){
                string = string.substr(0,string.length-1);
                string_id = string_id.substr(0,string_id.length-1);
                localStorage.setItem("wi_code_"+appoint, string);
                localStorage.setItem("hours_id_"+appoint, string_id);
            }else{
                localStorage.removeItem('wi_code_'+appoint)
                localStorage.removeItem('hours_id_'+string_id)
            }

        }
    }
}

function list_bj_car_ajax(urlparam) {

    $.getJSON(urlparam, null, function (result) {
        for (var i = 0; i < result.data.data.length; i++) {   //json格式，多行数据的数组
            var tr = $("<tr></tr>");                      //一行记录
            tr.appendTo($("tbody#bj-car-body"));
            var td = $("<td>" + (result.data.data[i].id ? result.data.data[i].id : '') + "</td>");    //序号
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].part_name ? result.data.data[i].part_name : '') + "</td>");    //备件名称
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].part_no ? result.data.data[i].part_no : '') + "</td>");    //备件编码
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].part_series_code ? result.data.data[i].part_series_code : "") + "</td>");    //备件车系
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].part_series_cn ? result.data.data[i].part_series_cn : '') + "</td>");    //备件车型
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].car_config_code ? result.data.data[i].car_config_code : '') + "</td>");    //18位码

            td.appendTo(tr);
        }
        $("#bj-car-modal").modal("show");
    })
}

var card_checked_id = [];
var card_checked_name = [];
function list_contracts_ajax(urlparam) {

    var ccn = $(".hide_card_ids").val();
    if( ccn != ''){
        card_checked_id = ccn.split(',');
        card_checked_id = card_checked_id.map(function(value,index){
            return parseInt(value);
        });
    }

    $.getJSON(urlparam, null, function (result) {
        for (var i = 0; i < result.data.data.length; i++) {   //json格式，多行数据的数组
            var tr = $("<tr></tr>");                      //一行记录
            tr.appendTo($("tbody#card-body"));
            var td = $("<td>" + (i+1) + "</td>");    //序号
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].belong_to ? result.data.data[i].belong_to : '') + "</td>");    //归属
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].card_type_name ? result.data.data[i].card_type_name : '') + "</td>");    //类型
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].id ? result.data.data[i].card_id : "") + "</td>");    //优惠券id
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].card_name ? result.data.data[i].card_name : '') + "</td>");    //优惠券名称
            td.appendTo(tr);
            td = $("<td></td>"); //操作
            var index = $.inArray(result.data.data[i].id, card_checked_id);


            // console.log(index);

            if (index > -1) {
                var option = $("<label>选择<input class='card_checkbox' type='checkbox' data-id='" + result.data.data[i].id + "' data-type='" + result.data.data[i].card_type + "' data-role='" + result.data.data[i].create_by_role + "' data-title='" + result.data.data[i].card_name + "' checked></label>");
            } else {
                var option = $("<label>选择<input class='card_checkbox' type='checkbox' data-id='" + result.data.data[i].id + "' data-type='" + result.data.data[i].card_type + "' data-role='" + result.data.data[i].create_by_role + "' data-title='" + result.data.data[i].card_name + "'></label>");
            }
            option.appendTo(td);
            td.appendTo(tr);
        }
        $("#card-modal").modal("show");
    })
}

$(".commodity_card").on("click", function () {
    count_contracts_ajax(ajaxGetCard_url + "?pagesize=5");
})
$("#card-search").on("click", function () {
    var card_name_like = $("#card_name").val();
    var shelves_sources = getShelvesSources();
    var urlParams = ajaxGetCard_url + "?pagesize=5&card_name=" + card_name_like + '&shelves_sources='+shelves_sources;
    count_contracts_ajax(urlParams);
})

$("body").on('click',"#bj-search",function(){
    var appoint = $('input[name="appoint"]').val();
    // console.log(appoint)
    var home_type_data = '';
    var home_type = home_type_class.getValue('value');
    if(home_type.length !== 0){
        home_type_data = home_type.join()
    }
    var urlParams = ajaxGetBj_url + "?pagesize=10&"+$("#bj_search_form").serialize()+"&spec_part_group_id="+home_type_data;
    count_bj_ajax(urlParams,appoint);
});


$("body").on('click',"#yanbao-search",function(){
    console.log('appoint:', appoint);

    var urlParams = ajaxGetYanBao_url + "?pagesize=10&"+$("#yanbao_search_form").serialize();
    count_yanbao_ajax(urlParams,appoint);
});

$("body").on('click',"#taocan-search",function(){
    var appoint = $('input[name="appoint"]').val();
    var sku_list = $(this).data('sku-list');
    // console.log('sku_list',sku_list);
    var urlParams = ajaxGetTaoCan_url + "?pagesize=10&"+$("#taocan_search_form").serialize();
    count_taocan_ajax(urlParams,appoint,$('.bj_div').data('address_key'));
});



//点击选择卡券
$("#card_form").on("click", '.card_checkbox', function () {
    if ($(this).attr("checked")) {
        card_checked_id.push($(this).data('id'));
        card_checked_name.push($(this).data('title'));
    } else {
        card_checked_id.splice($.inArray($(this).data('id'), card_checked_id), 1);
        card_checked_name.splice($.inArray($(this).data('title'), card_checked_name), 1);
    }
})

$("#card_add").on('click', function () {
    $(".commodity_card").val(card_checked_name.join(','));
    $(".hide_card_ids").val(card_checked_id.join(','));
})


//屏蔽删除
function onlyView() {
    if (event.keyCode > 0)
        event.returnValue = false;
}


// 验证套餐商品总的规格编码
$(document).on("click", ".win_taocan", function () {
    var address_key = $(this).data('address');
    var bj_type = $("input[name='dd_commodity_type']:checked").val()
    var belong_zone_code = $(this).parents('.text-left').prevAll('.belong_code').data('belong_code')
    var validate_goods_spec = $(this).parents('.text-left').find('.sku_code').val();
    if(belong_zone_code == null){
        layer.msg('请选择地区');
        return
    }
    //组合sku逻辑
    // var appoint = $(this).parent().parent().parent().index()
    var app = $(this).parents('tr').data('sku-list');
    if(app != ''){
        var appoint = app.replace(/,/g, '');
    }
    var params = {
        "appoint":appoint,
        "bj_type":bj_type,
        'belong_zone_code':belong_zone_code,
        'validate_goods_spec':validate_goods_spec,
        'address_key':address_key
    }
    if (bj_type == 12) {
        params.discount = '0.7';
    }
    $.get(validate_spec_val, params, function (res) {
        $("#taocan-modal").html($(res));
        $("#taocan-modal").modal("show");
        var urlParam = ajaxGetTaoCan_url + "?pagesize=10&appoint="+appoint+"&bj_type="+bj_type+"&sku_code="+validate_goods_spec;
        if (bj_type == 12) {
             urlParam = ajaxGetTaoCan_url + "?pagesize=10&appoint="+appoint+"&bj_type="+bj_type+"&sku_code="+validate_goods_spec+"&discount="+params.discount;
        }
        count_taocan_ajax(urlParam,appoint,address_key);
    });

});

//工时，获得弹窗
$("body").on('click',".win_hours",function(){
    //查看车型数据
    var appoint = $(this).parent().parent().parent().index()
    $.get(url_get_hours_table, {appoint:appoint}, function (res) {
        $("#hours-modal").html($(res));
        $("#hours-modal").modal("show");
        count_hours_car_ajax(ajaxGetHours_url + "?pagesize=10&appoint="+appoint,appoint);
    });

})

function createHoursPage(pageSize, buttons, total, contracts_url,appoint) {        //contracts_url为点击
    $(".hours_div .pagination").jBootstrapPage({      //功能按钮后需要访问action路径
        pageSize: pageSize,
        total: total,
        maxPageButton: buttons,
        onPageClicked: function (obj, page) {
            $("tbody#hours-body").empty();
            list_hours_ajax(contracts_url + "&page=" + (page + 1)+"&" + $("#hours_search_form").serialize(),appoint);
        }
    });
}

function count_hours_car_ajax(urlparam,appoint) {
    $.getJSON(urlparam, null, function (resData) {
        var buttons = 10;
        if(resData.data.total < 10){
            buttons =  resData.data.total/resData.data.per_page
        }
        createHoursPage(resData.data.per_page, buttons, resData.data.total, urlparam,appoint);//创建翻页功能按钮，翻
        $("tbody#hours-body").empty();
        if (resData.data.total > 0) {                          //页向后台请求连接
            list_hours_ajax(urlparam,appoint);
        } else {
            // layer.msg("没有更多数据");
            $("#hours-modal").modal("show");
        }
    })
}
function list_hours_ajax(urlparam,appoint) {
    $("tbody#hours-body").empty();
    var is_checkbox = true;
    $.getJSON(urlparam, null, function (result) {
        // console.log(result.data)
        var check_length = 0;
        for (var i = 0; i < result.data.data.length; i++) {   //json格式，多行数据的数组
            var tr = $("<tr></tr>");                      //一行记录
            tr.appendTo($("tbody#hours-body"));
            var td = $("<td>" + (result.data.data[i].id ? result.data.data[i].id : '')  + "</td>");    //序号
            td.appendTo(tr);
            var td = $("<td>" + (result
                .data.data[i].wi_code ? result.data.data[i].wi_code : '') + "</td>");    //重点备件品类
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].part_no ? result.data.data[i].part_no : "") + "</td>");    //备件编码
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].wi_name ? result.data.data[i].wi_name : "") + "</td>");    //备件编码
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].wi_qty ? result.data.data[i].wi_qty : '') + "</td>");    //备件单价（元）
            td.appendTo(tr);
            var td = $("<td><a href='javascript:void(0)' data-part_no=" +(result.data.data[i].part_no)+ " class='e3s_car_detail'>" + ('查看') + "</a></td>");    //备件适用车型
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].modified_date ? result.data.data[i].modified_date : '') + "</td>");    //状态
            td.appendTo(tr);
            td = $("<td></td>"); //操作
            var index = $.inArray(result.data.data[i].id, bj_checked_id);
            var wi_code = localStorage.getItem("wi_code_"+appoint);
            var hours_id = localStorage.getItem("hours_id_"+appoint);
            if(hours_id != null){
                if(hours_id.length > 0){
                    var array = hours_id.split(',')
                    if(array == result.data.data[i].id) {
                        ++check_length
                        var option = $("<label>选择<input class='card_checkbox wi_code' onchange='add_local_wi_code(this,"+appoint+")' name='select_wi_code' type='radio' data-wi_code='" + result.data.data[i].wi_code + "' data-hours_id='" + result.data.data[i].id + "' checked></label>");
                    }else{
                        var option = $("<label>选择<input class='card_checkbox wi_code' onchange='add_local_wi_code(this,"+appoint+")' name='select_wi_code' type='radio' data-wi_code='" + result.data.data[i].wi_code + "' data-hours_id='" + result.data.data[i].id + "'></label>");
                    }
                }else{
                    var option = $("<label>选择<input class='card_checkbox wi_code' onchange='add_local_wi_code(this,"+appoint+")' name='select_wi_code' type='radio' data-wi_code='" + result.data.data[i].wi_code + "' data-hours_id='" + result.data.data[i].id + "'></label>");
                }
            }else{
                var option = $("<label>选择<input class='card_checkbox wi_code' onchange='add_local_wi_code(this,"+appoint+")' name='select_wi_code' type='radio' data-wi_code='" + result.data.data[i].wi_code + "' data-hours_id='" + result.data.data[i].id + "'></label>");
            }
            option.appendTo(td);
            td.appendTo(tr);
        }
        if(check_length == 10){
            $('input[name = select_all]').prop("checked","true")
        }else{
            $('input[name = select_all]').removeAttr("checked")
        }
        $("#hours-modal").modal("show");
    })
}

$("body").on('click',"#hours-search",function(){
    // var appoint = $('input[name="appoint"]').val();
    var appoint = $(this).parent().parent().parent().index()
    var urlParams = ajaxGetHours_url + "?pagesize=10&appoint="+appoint+"&"+$("#hours_search_form").serialize();
    list_hours_ajax(urlParams,appoint);
})


$("body").on('click',".win_pz1a",function(){
    //查看车型数据
    var sku_code = $(this).parent().prev().find('input').val()
    var appoint = $(this).parent().parent().parent().index()
    $.get(url_get_pz1a_table, {"appoint":appoint,"sku_code":sku_code}, function (res) {
        $("#pz1a-modal").html($(res));
        $("#pz1a-modal").modal("show");
        count_pz1a_car_ajax(ajaxGetPz1a_url + "?pagesize=10&appoint="+appoint,appoint);
    });
})

function count_pz1a_car_ajax(urlparam,appoint) {
    $.getJSON(urlparam, null, function (resData) {
        var buttons = 10;
        if(resData.data.total < 10){
            buttons =  resData.data.total/resData.data.per_page
        }
        createPz1aPage(resData.data.per_page, buttons, resData.data.total, urlparam,appoint);//创建翻页功能按钮，翻
        $("tbody#pz1a-body").empty();
        if (resData.data.total > 0) {                          //页向后台请求连接
            list_pz1a_ajax(urlparam,appoint);
        } else {
            // layer.msg("没有更多数据");
            $("#pz1a-modal").modal("show");
        }
    })
}

function createPz1aPage(pageSize, buttons, total, contracts_url,appoint) {        //contracts_url为点击
    $(".pz1a_div .pagination").jBootstrapPage({      //功能按钮后需要访问action路径
        pageSize: pageSize,
        total: total,
        maxPageButton: buttons,
        onPageClicked: function (obj, page) {
            $("tbody#pz1a-body").empty();
            list_pz1a_ajax(contracts_url + "&page=" + (page + 1)+"&" + $("#hours_search_form").serialize(),appoint);
        }
    });
}

function list_pz1a_ajax(urlparam,appoint) {
    $("tbody#pz1a-body").empty();
    $.getJSON(urlparam, null, function (result) {
        var check_length = 0;
        for (var i = 0; i < result.data.data.length; i++) {   //json格式，多行数据的数组
            var tr = $("<tr></tr>");                      //一行记录
            tr.appendTo($("tbody#pz1a-body"));
            var td = $("<td>" + (result.data.data[i].id ? result.data.data[i].id : '')  + "</td>");    //序号
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].sp_basic_code ? result.data.data[i].sp_basic_code : '') + "</td>");    //pz1a编码
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].sp_basic_name ? result.data.data[i].sp_basic_name : "") + "</td>");    //pz1a套餐名称
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].level_name ? result.data.data[i].level_name : "") + "</td>");    //城市级别
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].maintain_total_count ? result.data.data[i].maintain_total_count : '') + "</td>");    //保养次数
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].saler_amount ? result.data.data[i].saler_amount : '') + "</td>");    //销售价格
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].discount ? result.data.data[i].discount : '') + "</td>");    //折扣
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].service_year ? result.data.data[i].service_year : '') + "</td>");    //使用年期
            td.appendTo(tr);
            var td = $("<td><a href='javascript:void(0)' data-sp_basic_id=" +(result.data.data[i].sp_basic_id)+ " class='e3s_pz1a_car_detail'>" + ('查看服务车型') + "</a></td>");    //备件适用车型
            td.appendTo(tr);
            td = $("<td></td>"); //操作
            var index = $.inArray(result.data.data[i].id, bj_checked_id);
            var pz1a_code = localStorage.getItem("pz1a_code_"+appoint);
            if(pz1a_code != null){
                if(pz1a_code.length > 0){
                    var array = pz1a_code.split(',')
                    var is_array = $.inArray(result.data.data[i].sp_basic_code, array);
                    if(is_array != -1) {
                        ++check_length
                        var option = $("<label>选择<input class='card_checkbox sp_basic_code' onchange='add_local_sp_basic_code(this,"+appoint+")' name='select_sp_basic_code' type='checkbox' data-sp_basic_id='" + result.data.data[i].sp_basic_id + "' data-sp_basic_code='" + result.data.data[i].sp_basic_code + "' data-pz1a_price='" + result.data.data[i].saler_amount + "' checked></label>");
                    }else{
                        var option = $("<label>选择<input class='card_checkbox sp_basic_code' onchange='add_local_sp_basic_code(this,"+appoint+")' name='select_sp_basic_code' type='checkbox' data-sp_basic_id='" + result.data.data[i].sp_basic_id + "' data-sp_basic_code='" + result.data.data[i].sp_basic_code + "' data-pz1a_price='" + result.data.data[i].saler_amount + "'></label>");
                    }
                }else{
                    var option = $("<label>选择<input class='card_checkbox sp_basic_code' onchange='add_local_sp_basic_code(this,"+appoint+")' name='select_sp_basic_code' type='checkbox' data-sp_basic_id='" + result.data.data[i].sp_basic_id + "' data-sp_basic_code='" + result.data.data[i].sp_basic_code + "' data-pz1a_price='" + result.data.data[i].saler_amount + "'></label>");
                }
            }else{
                var option = $("<label>选择<input class='card_checkbox sp_basic_code' onchange='add_local_sp_basic_code(this,"+appoint+")' name='select_sp_basic_code' type='checkbox' data-sp_basic_id='" + result.data.data[i].sp_basic_id + "' data-sp_basic_code='" + result.data.data[i].sp_basic_code + "' data-pz1a_price='" + result.data.data[i].saler_amount + "'></label>");
            }
            option.appendTo(td);
            td.appendTo(tr);
        }
        if(check_length == 10){
            $('input[name = select_all]').prop("checked","true")
        }else{
            $('input[name = select_all]').removeAttr("checked")
        }
        $("#pz1a-modal").modal("show");
    })
}

$("body").on('click',"#pz1a-search",function(){
    var appoint = $('input[name="appoint"]').val();
    var urlParams = ajaxGetPz1a_url + "?pagesize=10&"+$("#pz1a_search_form").serialize();
    list_pz1a_ajax(urlParams,appoint);
})

function add_local_sp_basic_code(i,appoint){
    var part_no = localStorage.getItem("pz1a_code_"+appoint);
    var pz1a_price = localStorage.getItem("pz1a_price_"+appoint);
    var pz1a_id = localStorage.getItem("pz1a_id_"+appoint);
    var string = '';
    var string_price = '';
    var string_id = '';
    if(i.checked){
        if(part_no != null){
            let array = part_no.split(',')
            var is_true = true;
            $.each(array,function(key,val){
                if(val == $(i).data('sp_basic_code')){
                    is_true = false;
                    return false;
                }
            })
            if(is_true){
                if(part_no.length > 0){
                    string += part_no+","+$(i).data('sp_basic_code')
                    string_price += pz1a_price+","+$(i).data('pz1a_price')
                    string_id += pz1a_id+","+$(i).data('sp_basic_id')
                }else{
                    string = $(i).data('sp_basic_code')
                    string_price = $(i).data('pz1a_price')
                    string_id = $(i).data('sp_basic_id')
                }
            }else{
                string = part_no
                string_price = pz1a_price
                string_id = pz1a_id
            }
        }else{
            string = $(i).data('sp_basic_code')
            string_price = $(i).data('pz1a_price')
            string_id = $(i).data('sp_basic_id')
        }
        localStorage.setItem("pz1a_code_"+appoint, string);
        localStorage.setItem("pz1a_price_"+appoint, string_price);
        localStorage.setItem("pz1a_id_"+appoint, string_id);
    }else{
        if(part_no != null){
            let array = part_no.split(',')
            let array_price = pz1a_price.split(',')
            let array_id = pz1a_id.split(',')
            $.each(array,function(key,val){
                if(val != $(i).data('sp_basic_code')){
                    string += val + ","
                    string_price += array_price[key] + ','
                    string_id += array_id[key] + ','
                }
            })
            // console.log('string_price:',string_price)
            if(string.length >0){
                string = string.substr(0,string.length-1);
                string_price = string_price.substr(0,string_price.length-1);
                string_id = string_id.substr(0,string_id.length-1);
                localStorage.setItem("pz1a_code_"+appoint, string);
                localStorage.setItem("pz1a_price_"+appoint, string_price);
                localStorage.setItem("pz1a_id_"+appoint, string_id);
            }else{
                localStorage.removeItem('pz1a_code_'+appoint)
                localStorage.removeItem('pz1a_price_'+appoint)
                localStorage.removeItem('pz1a_id_'+appoint)
            }

        }
    }
}

//选择PZ1A套餐 查看服务车型
$("body").on('click',".e3s_pz1a_car_detail",function(){
        var sp_basic_id = $(this).data('sp_basic_id');
        //查看车型数据
        $.get(url_get_pz1a_car_table, {sp_basic_id:sp_basic_id}, function (res) {
            $("#pz1a-car-modal").html($(res));
            count_pz1a_car_18n_ajax(ajaxGetPz1aCar_url + "?pagesize=10&sp_basic_id=" + sp_basic_id);
        });
    })

function count_pz1a_car_18n_ajax(urlparam,appoint) {
    $.getJSON(urlparam, null, function (resData) {
        var buttons = 10;
        if(resData.data.total < 10){
            buttons =  resData.data.total/resData.data.per_page
        }
        createPz1aCarPage(resData.data.per_page, buttons, resData.data.total, urlparam,appoint);//创建翻页功能按钮，翻
        $("tbody#pz1a-car-body").empty();
        if (resData.data.total > 0) {                          //页向后台请求连接
            list_pz1a_car_ajax(urlparam,appoint);
        } else {
            // layer.msg("没有更多数据");
            $("#pz1a-car-modal").modal("show");
        }
    })
}

function list_pz1a_car_ajax(urlparam,appoint) {
    $("tbody#pz1a-car-body").empty();
    $.getJSON(urlparam, null, function (result) {
        var check_length = 0;
        for (var i = 0; i < result.data.data.length; i++) {   //json格式，多行数据的数组
            var tr = $("<tr></tr>");                      //一行记录
            tr.appendTo($("tbody#pz1a-car-body"));
            var td = $("<td>" + (result.data.data[i].sp_basic_code ? result.data.data[i].sp_basic_code : '') + "</td>");    //pz1a编码
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].sp_basic_name ? result.data.data[i].sp_basic_name : "") + "</td>");    //pz1a套餐名称
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].service_car_type ? result.data.data[i].service_car_type : "") + "</td>");    //城市级别
            td.appendTo(tr);
            var td = $("<td>" + (result.data.data[i].car_config_code ? result.data.data[i].car_config_code : '') + "</td>");    //销售价格
            td.appendTo(tr);
        }
        if(check_length == 10){
            $('input[name = select_all]').prop("checked","true")
        }else{
            $('input[name = select_all]').removeAttr("checked")
        }
        $("#pz1a-car-modal").modal("show");
    })
}

function createPz1aCarPage(pageSize, buttons, total, contracts_url,appoint) {        //contracts_url为点击
    $(".pz1a_car_div .pagination").jBootstrapPage({      //功能按钮后需要访问action路径
        pageSize: pageSize,
        total: total,
        maxPageButton: buttons,
        onPageClicked: function (obj, page) {
            $("tbody#pz1a-car-body").empty();
            list_pz1a_car_ajax(contracts_url + "&page=" + (page + 1), appoint);
        }
    });
}
$('.shelves_sources_check').on('change', function (){
    var dd_commodity_type = $(":input[name='dd_commodity_type']:checked").val();
    var commodity_class = $(":input[name='commodity_class']:checked").val();
    // console.log($(this).val())
    if (dd_commodity_type == 7 || dd_commodity_type == 8 || commodity_class == 6) {
        if ($(this).val() == 5) {
            $('#shelves_sources_check_6').removeAttr('checked');
            $('#shelves_sources_check_7').removeAttr('checked');
        } else if ($(this).val() == 6) {
            $('#shelves_sources_check_5').removeAttr('checked');
            $('#shelves_sources_check_7').removeAttr('checked');
        } else if ($(this).val() == 7) {
            $('#shelves_sources_check_5').removeAttr('checked');
            $('#shelves_sources_check_6').removeAttr('checked');
        }
        _check_card($(this).val());
    }
})

function getShelvesSources(){
    var  shelves_sources_check = '0';
    $.each($('.shelves_sources_check:checked'),function(key,val){
        shelves_sources_check += ',' + $(this).val();
    })
    return shelves_sources_check;
}

function _check_card(shelves_type){
    $("#sku tbody>tr").each(function () {
        $(this).find(".del_card").click();
    });
}
var jd_img = [];
$("#jd-sku-search").on("click", function () {
    var sku_id = $("#sku_id").val();
    var url = jd_sku_img + "?sku_id=" + sku_id + '&img_type=1';
    $.getJSON(url, null, function (res) {
        $("#jd-body tr").remove();
        if(res.error == 1){
            layer.msg(res.msg);
            return false
        }
        $.each(res.data,function(k,v){
            var tr = $("<tr></tr>");                      //一行记录
            tr.appendTo($("tbody#jd-body"));
            if($.inArray(v.img_url,jd_img) == -1){
                var td = $("<td><img class='img-rounded' style='max-width: 120px' src=" + v.img_url + "><span class='btn btn-sm btn-info jd-img-add' onclick='jd_img_add(this)' style='margin-left: 3px;' data-jd_img_url='"+v.img_url+"'>添加</span></td>");
            }else{
                var td = $("<td><img class='img-rounded' style='max-width: 120px' src=" + v.img_url + "><span class='btn btn-sm btn-default jd-img-add'  style='margin-left: 3px;' data-jd_img_url='"+v.img_url+"'>已添加</span></td>");
            }
            td.appendTo(tr);
            var td = $("<td>" + v.is_enable + "</td>");    //pz1a套餐名称
            td.appendTo(tr);
            var td = $("<td>" + v.is_main_image + "</td>");    //城市级别
            td.appendTo(tr);
            var td = $("<td>" + v.sorts + "</td>");    //城市级别
            td.appendTo(tr);
            var td = $("<td>" + v.position + "</td>");    //城市级别
            td.appendTo(tr);
            var td = $("<td>" + v.created_date + "</td>");    //城市级别
            td.appendTo(tr);
            // console.log(tr)
        })
    });
})


function jd_img_add(e)
{
    var image_coutn = $('.goods_pic').find('li').length;
    if (image_coutn >= 5) {
        layer.msg('图片最多能上传5张');
        return;
    }
    var img = $(e).data('jd_img_url')
    $(e).removeAttr('onclick');
    $(e).addClass('btn-default')
    $(e).removeClass('btn-info')
    $(e).html('已添加');

    var html = '<li class="move-item"> <img image-value="' + img + '" src="' + img + '"> <del></del><span>修改</span> </li>';
    $("#image-id-0").find('ul').append(html);
    jd_img.push(img)
}

//添加商品
$(".select_invoice_type_show").on('click',function (e) {

    var selecti = $(this).data("i");
    console.log('对象:',selecti)
    var is_search = $(this).attr('is_search');
    console.log('is_search:',is_search);
    if (is_search == 1) {
        // 搜索来的
        var params = {
            tax_code:$("#tax_code").val(),
            tax_name:$("#tax_name").val(),
            short_name:$("#short_name").val()
        }
    } else {
        // 第一次打开
        var params = {};
        $("#tax_code").val('');
        $("#tax_name").val('');
        $("#short_name").val('');
    }
    e.preventDefault();
    var obj=$(this);
    $.getJSON(url_select_invoice_type,params,function (res) {
        var data_list=res.data;
        $(".invoice-type-tb").empty()
////////////////////////////////////////////////////////////////////////////////////////////////////////////
        var pagecount = 1//默认第一页
        $("#invoice_type_count").empty().append(data_list.length)
        var result = [];
        if (data_list.length === 0) {
            return ;
        }
        for (var i = 0, len = data_list.length; i < len; i += 10) {
            result.push(data_list.slice(i, i + 10));
        }

        var html = "";
        if (pagecount == 1) {
            var dataFirst = result[0]
            $.each(dataFirst, function (ki, val) {
                ki++;
            html += '<tr invoice_type_id="'+val.id+'">' +
                    '<td class="text-left">'+ ki++ +'</td>' +
                    '<td class="text-left">'+val.tax_code+'</td>' +
                    '<td >'+ val.tax_name +'</td>' +
                    '<td >'+val.short_name+'</td>' +
                    '<td >' + val.tax_rate + '</td>'+
                    '<td >是</td>'+
                    '<td >' + truncateString(val.desc)  + '</td>'+
                    '<td ><a class="select_tax_code batch_tax_code"   data-i="'+selecti+'" data-rate="'+val.first_rate+'" data-code="'+val.tax_code+'" >选择</a></td>'+
                    '</tr>';
                })
            $('#invoice_type_search').attr('data-i',selecti); // 搜索按钮添加属性
            $(".invoice-type-tb").html(html);
        }
            $("#invoice_type_page").Page({

                totalPages: data_list.length,//分页总数
                liNums: 9,//分页的数字按钮数(建议取奇数)
                activeClass: 'activP', //active 类样式定义
                callBack: function (page) {
                    pagecount = page
                    var arr = result[page - 1]
                    $(".invoice-type-tb").empty()
                    var html = ''
                    var i = 1;

                    $.each(arr, function (k, val) {

                        k++;
                        html += '<tr invoice_type_id="'+val.id+'">' +
                            '<td class="text-left">'+k+'</td>' +
                            '<td class="text-left">'+val.tax_code+'</td>' +
                            '<td >'+ val.tax_name +'</td>' +
                            '<td >'+val.short_name+'</td>' +
                            '<td >' + val.tax_rate + '</td>'+
                            '<td >是</td>'+
                            '<td >' + truncateString(val.desc)  + '</td>'+
                            '<td ><a class="select_tax_code" data-i="'+selecti+'" data-rate="'+val.first_rate+'" data-code="'+val.tax_code+'" >选择</a></td>'+
                            '</tr>';

                    })

                    $(".invoice-type-tb").html(html);
                }
            });




////////////////////////////////////////////////////////////////////////////////////////////////////////////


        $("#invoice-type-modal").data("comm-id",obj.data('comm-id'));
        $("#invoice-type-modal").data("comm-set-id",obj.data('comm-set-id'));
        $("#invoice-type-modal").data('dd-commodity-type',obj.data('dd-commodity-type'));
        $("#invoice-type-modal").data('type','add');
      //  $(".invoice-type-tb").html(html);
        $("#invoice-type-modal").modal('show');
        //dlr初始化
        $("#invoice-type-modal").find(".sku-dlr").val("").data("sku-dlr","");
    });
})

$(".select_tax_code").live('click',function (e) {
    $("#invoice-type-modal").modal('hide');
    var classn = ".invoice_type_"+$(this).data("i")
    var rate = ".invoice_rate_"+$(this).data("i")
    $(rate).val($(this).data("rate"))
    $(classn).val($(this).data("code"))
})

$("#taxt_code_th input").live('click',function(){
    // var params = {tax_code:$("#tax_code").val(),tax_name:$("#tax_name").val(),short_name:$("#short_name").val()};
    var params = {};
    $("#tax_code").val('');
    $("#tax_name").val('');
    $("#short_name").val('');

    $.getJSON(url_select_invoice_type,params,function (res) {
        var data_list=res.data;
        $(".invoice-type-tb").empty()
////////////////////////////////////////////////////////////////////////////////////////////////////////////
        var pagecount = 1//默认第一页
        $("#invoice_type_count").empty().append(data_list.length)
        var result = [];
        if (data_list.length === 0) {
            return ;
        }
        for (var i = 0, len = data_list.length; i < len; i += 10) {
            result.push(data_list.slice(i, i + 10));
        }

        html = "";
        if (pagecount == 1) {
            var dataFirst = result[0]
            $.each(dataFirst, function (ki, val) {
                ki++;
                html += '<tr invoice_type_id="'+val.id+'">' +
                    '<td class="text-left">'+ ki++ +'</td>' +
                    '<td class="text-left">'+val.tax_code+'</td>' +
                    '<td >'+ val.tax_name +'</td>' +
                    '<td >'+val.short_name+'</td>' +
                    '<td >' + val.tax_rate + '</td>'+
                    '<td >是</td>'+
                    '<td >' + truncateString(val.desc)  + '</td>'+
                    '<td ><a class="batch_tax_code" data-rate="'+val.first_rate+'" data-code="'+val.tax_code+'" >选择</a></td>'+
                    '</tr>';
            })
            $(".invoice-type-tb").html(html);
        }
        $("#invoice_type_page").Page({

            totalPages: data_list.length,//分页总数
            liNums: 9,//分页的数字按钮数(建议取奇数)
            activeClass: 'activP', //active 类样式定义
            callBack: function (page) {
                pagecount = page
                var arr = result[page - 1]
                $(".invoice-type-tb").empty()
                var html = ''
                var i = 1;

                $.each(arr, function (k, val) {

                    k++;
                    html += '<tr invoice_type_id="'+val.id+'">' +
                        '<td class="text-left">'+k+'</td>' +
                        '<td class="text-left">'+val.tax_code+'</td>' +
                        '<td >'+ val.tax_name +'</td>' +
                        '<td >'+val.short_name+'</td>' +
                        '<td >' + val.tax_rate + '</td>'+
                        '<td >是</td>'+
                        '<td >' + truncateString(val.desc)  + '</td>'+
                        '<td ><a class="batch_tax_code"  data-rate="'+val.first_rate+'" data-code="'+val.tax_code+'" >选择</a></td>'+
                        '</tr>';

                })

                $(".invoice-type-tb").html(html);
            }
        });
        $("#invoice-type-modal").modal('show');
    });
})

var public_rate = 0;
$(".batch_tax_code").live('click',function (e) {
    $("#invoice-type-modal").modal('hide');
    $("#taxt_code_th input").val($(this).data("code"))
    public_rate = $(this).data("rate")
})

function truncateString(str) {
    // 判断字符串长度是否超过10个字符
    if (str.length > 16) {
        // 如果超过，则截取前10个字符并添加省略号
        return str.slice(0, 10) + '...';
    } else {
        // 如果没有超过，则直接返回原字符串
        return str;
    }
}




/*责任条款(NEV车型)*/
$(".form-group").on("change", "#liability-clause-pdf", function (e) {
    var files = this.files;
    console.log('file:',files)
    if (files && files[0]) {
        var fileName = files[0].name;
        var fileSize = files[0].size;
        if (fileSize > 400 * 1024) {
            layer.msg("文件大小不能超过400k");
            return;
        }
        var fileType = fileName.substr(fileName.lastIndexOf('.') + 1, fileName.length);
        if (fileType != 'pdf' && fileType != 'jpeg' && fileType != 'jpg' && fileType != 'png') {
            layer.msg("文件格式不正确");
            return;
        }
    }
    Custom.ajaxFileUpload('liability-clause-pdf', {file_path: 'pdf'}, function (res) {
        console.log('res:',res);
        if (res.error == 0) {
            var data = res.data
            console.log('data:',data)
            $("input[name='liability_clause_pdf']").val(data.image);
        } else {
            layer.msg(res.msg);
            return false
        }
    });
})


/*责任条款(非NEV车型)*/
$(".form-group").on("change", "#no-nev-liability-clause-pdf", function (e) {
    var files = this.files;
    console.log('file:',files)
    if (files && files[0]) {
        var fileName = files[0].name;
        var fileSize = files[0].size;
        if (fileSize > 400 * 1024) {
            layer.msg("文件大小不能超过400k");
            return;
        }
        var fileType = fileName.substr(fileName.lastIndexOf('.') + 1, fileName.length);
        if (fileType != 'pdf' && fileType != 'jpeg' && fileType != 'jpg' && fileType != 'png') {
            layer.msg("文件格式不正确");
            return;
        }
    }
    Custom.ajaxFileUpload('no-nev-liability-clause-pdf', {file_path: 'pdf'}, function (res) {
        console.log('res:',res);
        if (res.error == 0) {
            var data = res.data
            console.log('data:',data)
            $("input[name='no_nev_liability_clause_pdf']").val(data.image);
        } else {
            layer.msg(res.msg);
            return false
        }
    });
})

