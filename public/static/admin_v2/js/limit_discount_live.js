/**
 * Created by lyj on 2017/11/23.
 */


//初始化
$(".default-select2").select2({width:'150px'});


/*------------------- begin 添加商品、分类 -----------------------*/
initComm(ajaxCommUrl+'&start_time='+start_time+'&end_time='+end_time+'&discount_type='+discount_type);
//操作类型切换
$("[name='operation_type']").on('change',function(){
    $(this).closest('.with-border').find("[name='operation_type_data']").val('');
});

var comm_checked_id = [];       //商品id
var comm_tab_tr = [];       //商品表单tr

//搜索商品列表
$("#comm-type-search").on('click',function(){
    var obj = $(this).parents('.form-group');
    var comm_parent_id = obj.find("select[name='comm_parent_id']").val();
    var sub_comm_type_id = obj.find("select[name='sub_comm_type_id']").val();
    var three_comm_type_id = obj.find("select[name='three_comm_type_id']").val();
    var commodity_name = obj.find("input[name='commodity_name']").val();
    var commodity_class = obj.find("select[name='commodity_class']").val();
    var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+commodity_name+'&commodity_class='+commodity_class+'&start_time='+start_time+'&end_time='+end_time+'&discount_type='+discount_type;
    initComm(param);
});

//更改活动时间
$('input[name=start_time],input[name=end_time]').on('blur',function(e){
    var s_time = $('input[name=start_time]').val();
    var e_time = $('input[name=end_time]').val();
    if(start_time != s_time || end_time != e_time){
        if($('#haved-commodtiy tr').length != 0 ){
            alert("更改活动时间会清空下方添加商品列表");
        }
        start_time = s_time;
        end_time = e_time;
        $("#sku-confirm-all").data('is-sku-confirm-all',1)
        comm_id = [];
        comm_set_id_arr = [];
        skuConfirmAllCurrentPage = []
        $('#haved-commodtiy').text('');
        initComm(ajaxCommUrl);
    }
})

/**
 * 活动商品列表搜索
 * @param isSkuConfirmAll
 * @returns {boolean}
 */
function commTypeSearch(isSkuConfirmAll = 0) {
    console.log('commTypeSearch',isSkuConfirmAll)
    var comm_parent_id = $("select[name='comm_parent_id']").val();
    var sub_comm_type_id = $("select[name='sub_comm_type_id']").val();
    var three_comm_type_id = $("select[name='three_comm_type_id']").val();
    var commodity_name = $("input[name='commodity_name']").val();
    var commodity_class = $("select[name='commodity_class']").val();
    var user_segment = $('input:radio[name=user_segment]:checked').val()
    if(user_segment == 'undefined') user_segment = 0;
    var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+commodity_name+'&commodity_class='+commodity_class+'&start_time='+start_time+'&end_time='+end_time+'&discount_type='+discount_type + '&user_segment='+user_segment;  ;

    //额外参数验证
    if(typeof(isInitComm) != "undefined") {
        if(isInitComm == 0){
            return false;
        }
    }
    //console.log(param);

    initComm(param, isSkuConfirmAll);

}



function initComm(url, isSkuConfirmAll) {
    var commodity_name = $("[name='commodity_name']").val()
    var user_segment = $('input:radio[name=user_segment]:checked').val()
    if(user_segment == 'undefined') user_segment = 0;
    url = url+'&start_time='+start_time+'&end_time='+end_time+'&discount_type='+discount_type+'&commodity_name='+commodity_name + '&user_segment='+user_segment;

    $.getJSON(url, null, function (resData) {
        resData.data.list.isSkuConfirmAll = isSkuConfirmAll;
        createPageComm(5, 10, resData.data.list.total, url);//创建翻页功能按钮，翻
        $("#add-comm-tbody").empty();
        if (resData.data.list.total > 0) {                          //页向后台请求连接
            setComm(url, isSkuConfirmAll);
        }
    });
}
/*  function initCommType(url) {
 $.getJSON(url, null, function (resData) {
 createPageCommType(5, 10, resData.data.total, url);//创建翻页功能按钮，翻
 $("#add-comm-type-tbody").empty();
 if (resData.data.total > 0) {                          //页向后台请求连接
 setCommType(url);
 }
 });
 }*/
function createPageComm(pageSize, buttons, total, url) {        //contracts_url为点击
    $("#comm-pagination").jBootstrapPage({      //功能按钮后需要访问action路径
        pageSize : pageSize,
        total : total,
        maxPageButton:buttons,
        onPageClicked: function(obj, page) {    //分页事件
            $("#add-comm-tbody").empty();
            setComm(url+"&page="+(page+1));
        }
    });
}
/* function createPageCommType(pageSize, buttons, total, url) {        //contracts_url为点击
 $("#comm-type-pagination").jBootstrapPage({      //功能按钮后需要访问action路径
 pageSize : pageSize,
 total : total,
 maxPageButton:buttons,
 onPageClicked: function(obj, page) {    //分页事件
 $("#add-comm-type-tbody").empty();
 setCommType(url+"&page="+(page+1));
 }
 });
 }*/
//获取并设置商品
function setComm(param, isSkuConfirmAll = 0){
    var url = ajaxCommUrl;
    // console.log('url',url);
    if(param!=null) url=param;
    $.get(url,function(res){
        var html='';
        var list = res.data.list.data;
        var comm_set_id_arr=gethavedCommodityId();

        //当前页是否已全选择
        var skuConfirmAllUnm = 0;
        //当前页
        var current_page = res.data.list.current_page;
        //当前分页条数
        var per_page = res.data.list.per_page;
        //重置当前页所有的comm_set_id值
        currentPageCommSetIds = [];
        // console.log('list',list)
        $.each(list,function(i,val){
            currentPageCommSetIds.push(val.commodity_set_id)
            var checkbox_obj = '';
            var index= Number($.inArray(val.commodity_set_id, comm_set_id_arr));
            if((isSkuConfirmAll > 0) && (val.is_limit != 1) && (val.is_pre != 1) && (val.is_seckill != 1) && (val.is_full != 1) && (val.is_segment!=1)){
                index = 1;
                //批量添加操作
                skuConfirm(val)
                skuConfirmAllUnm +=1;
            }

            if (index>-1){
                var button='<button data-per-page="'+per_page+'" data-comm-set-id="'+val.commodity_set_id+'" home="'+val.home+'" data-comm-id="'+val.commodity_id+'" '+
                    ' class=" btn btn-default active btn-sm btn-fight" disabled>已添加</button>';
            } else {
                if(val.is_fight == 1){
                    var button='<button data-per-page="'+per_page+'" data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                        ' class=" btn btn-white active btn-sm " disabled >已参与团购</button>';
                }else if(val.is_pre==1){
                    var button='<button data-per-page="'+per_page+'" data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                        ' class=" btn btn-white active btn-sm " disabled >已参与预售</button>';
                }else if(val.is_limit==1){
                    var button='<button data-per-page="'+per_page+'" data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                        ' class=" btn btn-white active btn-sm " disabled >已参与其他限时折扣</button>';
                }else if(val.is_seckill==1){
                    var button='<button data-per-page="'+per_page+'" data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                        'class=" btn btn-white active btn-sm " disabled >已参与其他秒杀活动</button>';
                }else if(val.is_full==1){
                    var button='<button data-per-page="'+per_page+'" data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                        ' class=" btn btn-white active btn-sm " disabled >工时已参与其他满减</button>';
                }else if(val.is_segment==1){
                    var button='<button data-per-page="'+per_page+'" data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                        'class=" btn btn-white active btn-sm " disabled >定向人群不一致</button>';
                }else{
                    var button='<button data-per-page="'+per_page+'" data-comm-set-id="'+val.commodity_set_id+'" home="'+val.home+'" data-comm-id="'+val.commodity_id+'" '+
                        'class=" btn btn-white btn-sm  btn-fight">选择添加</button>';
                }
            }

            html += '<tr id="comm_tab_tr_'+val.commodity_id+'">' +
                '<td class="text-left"><image class="cover-image" src="'+val.cover_image+'">'+
                '<a  class="init-commodity-preview" data-dlr-code="'+val.dlr_code+'" data-commodity-id="'+val.commodity_id+'"> '+val.commodity_name+' </a>' +
                '</td>' +
                '<td class="text-center">'+ val.up_down_channel_name +'</td>' +
                '<td class="text-center">'+ val.price +'</td>' +
                '<td class="text-center">'+ val.count_stock +'</td>' +
                '<td class="text-center">' + button +
                '</td> </tr>';

        });
        $("#add-comm-tbody").html(html);

        if(isSkuConfirmAll == 0 || !isSkuConfirmAll){
            skuConfirmAllUnm = getSkuConfirmAllUnm(comm_set_id_arr);
        }
        //增加批量添加按钮禁用操作
        // console.log('isSkuConfirmAll',isSkuConfirmAll)
        // console.log('skuConfirmAllUnm',skuConfirmAllUnm)
        // console.log('current_page',current_page)
        // console.log('per_page',per_page)
        setSkuConfirmAllStatus(isSkuConfirmAll, skuConfirmAllUnm, current_page, per_page);

    },'json');
}

//折扣操作
$('body').on('click','.dis-op',function(){
    var that = $(this);

    if(that.hasClass('btn-danger')){
        that.parents('.slope-dis').remove();
    }else{
        var element = that.parents('.slope-dis').clone();
        if((element.hasClass('dis_1') && $('.dis_1').length < 10) || (element.hasClass('dis_2') && $('.dis_2').length < 10)){
            element.find('.dis-op').addClass('btn-danger').removeClass('btn-success');
            element.find('.dis-op i').addClass('fa-minus').removeClass('fa-plus');
            element.find(':input').val('');
            that.parents('.slope-dis').parent().append(element);
        }
    }
    discount();
})

//获取商品原价的会员价
//原价，折扣，折扣类型，最大可优惠
function getProductPrice(price,dis,dis_type,max_dis){
    if(dis == '') return price;
    if (dis_type == 1) {
        var sku_price = ((dis / 10) * price);
    } else if (dis_type == 2) {
        var sku_price = price - dis;
    }
    if(max_dis > 0){
        if((price - sku_price ) > max_dis){
            sku_price = price - max_dis;
        }
    }
    return sku_price
}

//取两们小数
function getprice(price){
    if( Math.round(price * 100) / 100 < 0){
        return 0;
    }else{
        return Math.round(price * 100) / 100;
    }
}

//返回价格
function getdistypeprice(dis_type,price,dis){
    if (dis == 0) return price;
    if (dis_type == 1) {
        var sku_price = ((dis / 10) * price);

    } else if (dis_type == 2) {
        var sku_price = price - dis;
    }
    return sku_price
}

//批量更改优惠
$(".op-select2 ").live('change',function(){
    $(".dis-select").val($(this).val())
})

var user_level_dis = [];
var user_level_title = [];
var user_level_code = [];
//添加商品
$("#add-comm-tbody").on('click',".btn-fight",function (e) {
    e.preventDefault();
    var obj=$(this);
    var home = $(this).attr('home');
    var slope_option = '';
    var fhome = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
    $("#modal_home").val("");
    $("#modal_home").val(home);
    if(user_segment == 0) {
        var name = (dis_type == 1 ? '.set-discount':'.set-price');
        if($(name).eq(0).val() == ''){
            layer.msg('请完善优惠规则！');
            return false;
        }
    }

    $.getJSON(getSkuUrl,{commodity_id:$(this).data('comm-id'),commodity_set_id:$(this).data('comm-set-id')},function (res) {
        var dis_tap = 0;
        var sku_list=res.data.sku_list;
        var commodity = res.data.commodity_row;
        $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
        $(".sku-comm").html(res.data.commodity_row.commodity_name);
        $("#comm-name-header").html(res.data.commodity_row.commodity_name);
        lowest_price = res.data.commodity_row.lowest_price;
        highest_price = res.data.commodity_row.highest_price;
        is_grouped = res.data.commodity_row.is_grouped;
        var html='';
        if (is_grouped == 0) {
            $('.sku-tb').parent().show();
        }else{
            $('.sku-tb').parent().hide();
        }
        var name = (dis_type == 1 ? 'set-discount':'set-price');
        var dis_name = (dis_type == 1 ? '折':'元');
        var dis_input = '';
        var slope_option = '';
        if(user_segment == 0){//旧路，也天添加有车主或有会的
            if(commodity.is_segment == 0) { //商品无会员无车主
                user_level_dis = [];
                $('.'+name).each(function (index, item) {
                    slope_option += '<option value="'+$(this).val()+'">' + $(this).val()+ dis_name + '</option>'
                    dis_input += ' <div class="input-group col-md-2 pull-left" style="margin-right:10px;margin-top:5px" > ' +
                        '<input type="text" class="form-control dis-val" maxlength="3" readonly value="'+ $(this).val() +'">'+
                        '<span class="input-group-addon">'+dis_name+'</span>' +
                        '</div>';
                    user_level_dis.push($(this).val())
                    if($(this).val()==''){
                        dis_tap = 1;
                    }
                })
            }

            if(commodity.is_segment == 1) { //商品有会员
                user_level_dis = [];
                $("."+name).each(function(){
                    slope_option += '<option value="'+$(this).val()+'">' + $(this).val()+ dis_name + '</option>'
                    dis_input += ' <div class="input-group col-md-2 pull-left" style="margin-right:10px;margin-top:5px" > ' +
                        '<input type="text" class="form-control dis-val" maxlength="3" readonly value="'+ $(this).val() +'">'+
                        '<span class="input-group-addon">'+dis_name+'</span>' +
                        '</div>';
                    user_level_dis.push($(this).val())
                    if($(this).val()==''){
                        dis_tap = 1;
                    }
                })
            }
            if(commodity.is_segment == 2) { //商品有车主
                user_level_dis = [];
                $("."+name).each(function(){
                    slope_option += '<option value="'+$(this).val()+'">' + $(this).val()+ dis_name + '</option>'
                    dis_input += ' <div class="input-group col-md-2 pull-left" style="margin-right:10px;margin-top:5px" > ' +
                        '<input type="text" class="form-control dis-val" maxlength="3" readonly value="'+ $(this).val() +'">'+
                        '<span class="input-group-addon">'+dis_name+'</span>' +
                        '</div>';
                    user_level_dis.push($(this).val())
                    if($(this).val()==''){
                        dis_tap = 1;
                    }
                })
            }
        }


        //定向会员
        if(user_segment == 1){
            for(i=1;i<=$("#member_box .rule_new_box").find(".level_item").size();i++) {
                slope_option += '<option value="'+i+'">活动梯度' + i + '</option>';
            }
            user_level_title = []
            user_level_code = []
            user_level_dis = [];
            var user_html = '';
            $(".user_level").each(function(i,v){

                // if(i >= selected_options()){
                //会员等级名
                user_level_title.push($(this).data('title'))

                //会员等编码
                user_level_code.push($(this).data('code'))
                // }


                var dis_tmp = []
                var dis_type_class = ".dis_type_" + (i + 1);
                $(dis_type_class).each(function(i,v){
                    if($(this).val() !=''){
                        dis_tmp.push($(this).val())
                    }
                })
                if(dis_tmp != ''){
                    ////////////////////补全数组////////////////////////
                    for(var ki = 0 ; ki < selected_options();ki++){
                        dis_tmp.unshift(0)
                    }
                    ////////////////////////////////////////////
                    user_level_dis[i] = dis_tmp
                }
            })

            user_html = "<table><tr ><td style='width:150px'></td>"
            $(user_level_title).each(function(i,v){
                user_html = user_html+ "<td style='width:100px'>"+v+"</td>"
            })

            $(user_level_dis).each(function(i,v){
                user_html = user_html+ "<tr class='levelitem_"+(i+1)+"'><td>活动梯度"+(i+1)+"</td>"
                $(v).each(function(ii,vv){
                    user_html = user_html+ "<td><input value='"+vv+"' size='3' class='user_l' disabled />"+dis_name+"</td>"
                })
                user_html = user_html+ "</tr>"
            })
            user_html = user_html + "</tr></table>";

            $("#show_title_box").hide()
            $("#user_tmp_box").empty().append(user_html)


            $("#user_tmp_box").show();
            $("#car_tmp_box").hide();
        }
        //定向车主
        if(user_segment == 2){
            // alert(commodity.is_segment)
            for(i=1;i<=$("#car_onwer_box .rule_new_box").find(".level_item").size();i++) {
                slope_option += '<option value="'+i+'">活动梯度' + i + '</option>';
            }
            user_level_title = [];
            if(set_type == 6){
                user_level_title.push('非车主','车主','Ariya车主')
            }else{
                user_level_title.push('非车主','车主')
            }

            user_level_dis = [];
            var user_html = '';
            $(".level_item").each(function(i,v){
                var dis_tmp = []
                var dis_type_class = ".carv_dis_type_" + (i + 1);
                $(dis_type_class).each(function(i,v){
                    // if(set_type == 6){
                    //     if($(this).val() !=''){
                    //         dis_tmp.push($(this).val())
                    //     }else{
                    //         dis_tmp.push(0)
                    //     }
                    // }else{
                    //     if($(this).val() !=''){
                    //         dis_tmp.push($(this).val())
                    //     }
                    // }
                    if($(this).val() !=''){
                        dis_tmp.push($(this).val())
                    }else{
                        dis_tmp.push(0)
                    }
                })

                if(dis_tmp != ''){
                    ////////////////////补全数组////////////////////////
                    // if(set_type == 6) {
                    //     if($(dis_tmp).size() == 2){
                    //         dis_tmp.unshift(0)
                    //     }
                    // }else{
                    //     if($(dis_tmp).size() == 1){
                    //         dis_tmp.unshift(0)
                    //     }
                    // }
                    ////////////////////////////////////////////
                    user_level_dis[i] = dis_tmp
                }
            })
            user_html = "<table><tr><td style='width:150px'></td>"
            $(user_level_title).each(function(i,v){
                user_html = user_html+ "<td style='width:100px'>"+v+"</td>"
            })

            $(user_level_dis).each(function(i,v){
                user_html = user_html+ "<tr class='levelitem_"+(i+1)+"'><td>活动梯度"+(i+1)+"</td>"
                $(v).each(function(ii,vv){
                    user_html = user_html+ "<td><input value='"+vv+"' class='car_l' size='3' disabled/>"+dis_name+"</td>"
                })
                user_html = user_html+ "</tr>"
            })
            user_html = user_html + "</tr></table>";

            $("#show_title_box").hide()
            $("#car_tmp_box").empty().append(user_html)

            $("#user_tmp_box").hide();
            $("#car_tmp_box").show();
        }

        $('#comm_discount').html(dis_input);

        if(user_segment == 1){
            var show_tip = 0;
            $(".v_dis_type").each(function(){
                if($(this).val() == ''){
                    show_tip = 1;
                }
            })
            if(show_tip == 1){
                layer.msg('请完善优惠规则！');
                return false;
            }
        }

        if(user_segment == 2){
            show_tip = 0;
            if(set_type == 6){
                $(".tip_show_2").each(function(){
                    if($(this).val() == ''){
                        show_tip = 1;
                    }
                })
            }else{
                $(".tip_show_1").each(function(){
                   // alert($(this).val())
                    if($(this).val() == ''){
                        show_tip = 1;
                    }
                })
            }
            if(show_tip == 1){
                layer.msg('请完善优惠规则！');
                return false;
            }
        }

        var html = '';
        $.each(sku_list, function (i, val) {
            var sku_check = '<input type="checkbox" checked class="sku-ckeck" value="6" name="sku-ckeck">';
            var sku_price = '<div class="input-group " style="width: 100px;">' +
                '<input  type="text" keys="'+commodity.is_segment+'" class="form-control sku-price input-sm" readonly="readonly" value="'+ getdistypeprice(dis_type,val.price,user_level_dis[0])+'" data-parsley-required="true"  data-parsley-min="0" data-parsley-pattern-message="格式不正确,请输入不小于0的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">' +
                '<span class="input-group-addon">元</span> </div>';
            if(user_segment == 0){//旧路，也要加有车主或有会的
                if(commodity.is_segment == 0) { //商品无会员无车主
                    html += '<tr set_sku_id="' + val.id + '">' +
                        '<td>'+sku_check+'</td>' +
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="0"  type="hidden"  /><input class="is_user_segment" value="0" type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">' + val.price + '</td>' +
                        '<td><div class="input-group "><select class="form-control dis-select ">'+slope_option+'</select></div></td>' +
                        '<td >' + sku_price + '</td>' +
                        '<td >' + val.stock + '</td> </tr>';
                }
                if(commodity.is_segment == 1) { //商品有会员
                    var ht = "";
                    var ht_price = "";
                    var n = 0;
                    $.each(commodity.segment.user_segment_name,function(key,value){
                        if(value > 0){
                            var product_price = getProductPrice(val.price,value,commodity.segment.discount_type,commodity.segment.max_discount_amount)
                            var sku_price = getdistypeprice(dis_type,product_price,user_level_dis[0])
                            ht = ht +"<div>" +key +":<span num="+n+" class='sku_user_price'>"+ getprice(product_price) + "</span></div>"
                            ht_price = ht_price +"<div>" +key +":<span class='sku_user_price_"+n+"'>"+ getprice(sku_price) + "</span></div>"
                        }else{
                            var sku_price = getdistypeprice(dis_type,val.price ,user_level_dis[0])
                            ht = ht + "<div>" + key +":<span num="+n+" class='sku_user_price'>"+ getprice(val.price)  + "</span></div>"
                            ht_price = ht_price +"<div>" +key +":<span class='sku_user_price_"+n+"'>"+ getprice(sku_price)+ "</span></div>"
                        }
                        n = n + 1;
                    })
                    html += '<tr set_sku_id="' + val.id + '" >' +
                        '<td>'+ sku_check +'</td>' +
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="1" type="hidden"  /><input class="is_user_segment" value="0"  type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price"><div class="input-group " style="width: 100px;">'+ ht +'</div></td>' +
                        '<td><div class="input-group "><select class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td><div class="input-group " style="width: 100px;">'+ ht_price +'</div></td>' +
                        '<td>' + val.stock + '</td> </tr>';

                }
                if(commodity.is_segment == 2) { //商品有车主
                    var no_car_sku_price = getdistypeprice(dis_type,val.price ,user_level_dis[0])
                    var product_price = getProductPrice(val.price,commodity.segment.disc,commodity.segment.discount_type,commodity.segment.max_discount_amount)
                    var car_sku_price    = getdistypeprice(dis_type,product_price ,user_level_dis[1])
                    var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div>';
                    if(set_type == 6){
                        var aryaicar_sku_price    = getdistypeprice(dis_type,product_price ,user_level_dis[2])
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div><div>aryai车主:<span class="aryi_olds">'+ getprice(aryaicar_sku_price) +'</span></div>';

                        //  var aryai_html = '<div>Ariya车主:<span class="cars">'+ getprice(aryaicar_sku_price) +'</span></div>';
                    }else{
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div>';
                        // var aryai_html = '';
                    }
                    html += '<tr set_sku_id="' + val.id + '" >"' +
                        '<td>'+ sku_check +'</td>' +
                        '<td class="text-left" data-disc-code="'+commodity.dis_code+'" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="2"  type="hidden" /><input class="is_user_segment" value="0"  type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">'+'<div>非车主：<span class="old">' + val.price + '</span></div><div>车主:<span class="car">'+ getprice(product_price) +'</span></div></td>' +
                        '<td><div class="input-group "><select class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td >' + sku_price + '</td>' +
                        '<td >' + val.stock + '</td> </tr>';
                }
            }
            //定向会员
            if(user_segment == 1){
                if(commodity.is_segment == 0) { //商品无会员

                    var ht = "";
                    var ht_price = ""
                    var n = 0;
                    $.each(user_level_dis[0],function(key,value){
                        sku_price = getdistypeprice(dis_type,val.price,value)
                        ht_price = ht_price +"<div>" +user_level_title[key] +":<span class='ret_sku_price sku_user_price_"+n+"'>"+ getprice(sku_price) + "</span></div>"
                        n = n + 1;
                    })
                    html += '<tr set_sku_id="' + val.id + '" >' +
                        '<td>'+ sku_check +'</td>' +
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="0" type="hidden" /><input class="is_user_segment" value="1"  type="hidden" />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">' + val.price + '</td>' +
                        '<td><div class="input-group "><select class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td><div class="input-group sku_user_price" style="width: 100px;">'+ ht_price +'</div></td>' +
                        '<td>' + val.stock + '</td> </tr>';
                }
                if(commodity.is_segment == 1) { //商品有会员

                    var ht = "";
                    var ht_price = ""
                    var n = 0;
                    var j = 0;
                    $.each(commodity.segment.user_segment_name,function(key,value){
                        if(value > 0){
                            var product_price = getProductPrice(val.price,value,commodity.segment.discount_type,commodity.segment.max_discount_amount)
                            //var sku_price = getdistypeprice(dis_type,val.price * value,user_level_dis[0][n])
                            var sku_price = getdistypeprice(dis_type,product_price,user_level_dis[0][n])
                            ht = ht +"<div>" +key +":<span num="+n+" class='sku_user_price'>"+ getprice(product_price) + "</span></div>"
                            ht_price = ht_price +"<div>" +key +":<span class='sku_user_price_"+n+"'>"+ getprice(sku_price) + "</span></div>"
                        }else{
                            var sku_price = getdistypeprice(dis_type,val.price ,user_level_dis[0][n])
                            ht = ht + "<div>" + key +":<span num="+n+" class='sku_user_price'>"+ getprice(val.price)  + "</span></div>"
                            ht_price = ht_price +"<div>" +key +":<span class='sku_user_price_"+n+"'>"+ getprice(sku_price)+ "</span></div>"
                        }
                        n = n + 1;
                    })
                    html += '<tr set_sku_id="' + val.id + '" >' +
                        '<td>'+ sku_check +'</td>' +
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="1"  type="hidden" /><input class="is_user_segment" value="1"  type="hidden" />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price"><div class="input-group " style="width: 100px;">'+ ht +'</div></td>' +
                        '<td><div class="input-group "><select class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td><div class="input-group " style="width: 100px;">'+ ht_price +'</div></td>' +
                        '<td>' + val.stock + '</td> </tr>';
                }
            }
            //定向车主
            if(user_segment == 2){
                if(commodity.is_segment == 2) { //商品有车主
                    var no_car_sku_price = getdistypeprice(dis_type,val.price ,user_level_dis[0][0])
                    var product_price = getProductPrice(val.price,commodity.segment.disc,commodity.segment.discount_type,commodity.segment.max_discount_amount)
                    var car_sku_price    = getdistypeprice(dis_type,product_price ,user_level_dis[0][1])
                    if(set_type == 6){
                        var aryi_price = getProductPrice(val.price,commodity.segment.pdisc,commodity.segment.discount_type,commodity.segment.max_discount_amount)
                        var aryaicar_sku_price    = getdistypeprice(dis_type,aryi_price ,user_level_dis[0][2])
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div><div>Aryi车主:<span class="aryi_olds">'+ getprice(aryaicar_sku_price) +'</span></div>';
                        var aryai_html = '<div>aryi车主:<span class="cars">'+ getprice(aryi_price) +'</span></div>'
                    }
                    else{
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div>';
                        var aryai_html = ''
                    }
                    html += '<tr set_sku_id="' + val.id + '" >"' +
                        '<td>'+ sku_check +'</td>' +
                        '<td class="text-left" data-disc-code="'+commodity.dis_code+'" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="2" type="hidden" type="hidden"  /><input class="is_user_segment" value="2" type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">'+'<div>非车主：<span class="old">' + val.price + '</span></div><div>车主:<span class="car">'+ product_price +'</span></div>'+aryai_html+'</td>' +
                        '<td><div class="input-group "><select class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td >' + sku_price + '</td>' +
                        '<td >' + val.stock + '</td> </tr>';
                }

                if(commodity.is_segment == 0) { //商品无车主
                    var no_car_sku_price = getdistypeprice(dis_type,val.price ,user_level_dis[0][0])
                    var car_sku_price    = getdistypeprice(dis_type,val.price ,user_level_dis[0][1])
                    if(set_type == 6){
                        var aryaicar_sku_price    = getdistypeprice(dis_type,val.price ,user_level_dis[0][2])
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div><div>aryai车主:<span class="aryi_olds">'+ getprice(aryaicar_sku_price) +'</span></div>';
                    }
                    else{
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div>';
                    }
                    html += '<tr set_sku_id="' + val.id + '">' +
                        '<td>'+ sku_check +'</td>' +
                        '<td class="text-left" data-disc-code="" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="0" type="hidden"  /><input class="is_user_segment" value="2" type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">' + val.price + '</td>' +
                        '<td><div class="input-group "><select class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td >' + sku_price + '</td>' +
                        '<td >' + val.stock +
                        '</td> </tr>';
                }

            }
            $('#comm_discount').html(dis_input);
        });

        $("#sku-modal").find("[name='comm_discount']").val('');
        $("#sku-modal").data("comm-id",obj.data('comm-id'));
        $("#sku-modal").data("comm-set-id",obj.data('comm-set-id'));
        $("#sku-modal").data('type','add');

        $(".sku-tb").html(html);

        var group_select = '<select class="form-control group-select op-select2">'+slope_option+'</select>';
        $('#groupd_tmp_box').html(group_select);
        $('.dis-group').html(group_select);
        if(commodity.is_grouped == 1){
            $("#group_boxs").show()
            $('.dis-select').attr('disabled',true)
        }else{
            $("#group_boxs").hide()
        }
        $("#sku-modal").modal('show');
        $('.op-select2').select2();
        // 显示立减或者折扣
        // if(dis_type==1){
        //     $("#sku-modal .modal-body").find("[name='comm_discount']").val($("[name='discount']").val());

        // }else if(dis_type==2){
        //     $("#sku-modal .modal-body").find("[name='comm_discount']").val($("[name='dis_money']").val());

        // }else if(dis_type==3){
        //     $("#sku-modal .modal-body").find("[name='comm_discount']").val('');
        // }
        //更新折扣价格
       // update_sku_price($("#sku-modal").find(".modal-body"));
        //dlr初始化
        $("#sku-modal").find(".sku-dlr").val("").data("sku-dlr","");
    });
});



//折扣价设置，自动计算折扣价格
$("body").on("change","[name='comm_discount']",function(){
    var obj_body = $(this).parents(".modal-body");
    update_sku_price(obj_body);
});

//阶梯折扣选择
$('sku-tb').on('click','td .select2-selection__rendered',function(){
    if($('.group-select').length == 1){
        layer.msg('组合商品不支持该操作');
        return false
    }
})


$('body').on('change','.dis-select',function(){

    var discount = $(this).val();
    var price = $(this).parents('tr').find("#td_price").text();
    //$(this).parents('tr').find(".sku-price")
    var keys = $(".is_segment_key").val();
    var is_user_segment = $(".is_user_segment").val();
    var obj = $(this);
    if(is_user_segment == 0){//之前的逻辑
        if(discount){
            if(keys == 0){
                var sku_price = getdistypeprice(dis_type,price,discount)
                $(this).parents('tr').find(".sku-price").val(getprice(sku_price));
            }
            if(keys == 1){//新增定向会
                obj.parent().parent().parent().find(".sku_user_price").each(function (i, v) {
                    var sku_price = getdistypeprice(dis_type,parseFloat($(this).html()),discount)
                    obj.parent().parent().parent().find(".sku_user_price_" + i).empty().append(getprice(sku_price))
                    obj.parent().parent().parent().find(".sku_user_price_" + i).empty().append(getprice(sku_price))
                })
            }
            if(keys == 2){//新增定向车主
                var oldprice = $(this).parents('tr').find(".old").text();
                var carprice = $(this).parents('tr').find(".car").text();
                var new_old = getdistypeprice(dis_type,oldprice,discount)
                var new_car = getdistypeprice(dis_type,carprice,discount)
                obj.parent().parent().parent().find(".olds").empty().append(getprice(new_old))
                obj.parent().parent().parent().find(".cars").empty().append(getprice(new_car))
            }
        }
    }else if(is_user_segment == 1){//定向会员
        if(discount) {
            if(keys == 0){
                obj.parent().parent().parent().find(".ret_sku_price").each(function (i, v) {
                    sku_price = getdistypeprice(dis_type,price,user_level_dis[(discount-1)][i])
                    $(this).empty().append(getprice(sku_price))
                })
            }else{
                var sku_price = getdistypeprice(dis_type,parseFloat($(this).html()),user_level_dis[(discount-1)][i])
                obj.parent().parent().parent().find(".sku_user_price").each(function (i, v) {
                    var num = $(this).attr('num')
                    var sku_price = getdistypeprice(dis_type,parseFloat($(this).html()),user_level_dis[(discount-1)][i])
                    $(this).parent().parent().parent().parent().find(".sku_user_price_" + num).empty().append(getprice(sku_price))
                })
            }

        }
    }else if(is_user_segment == 2){//定向车主

        if(keys == 0){
            var new_old = getdistypeprice(dis_type,price,user_level_dis[($(this).val()-1)][0])
            var new_car = getdistypeprice(dis_type,price,user_level_dis[($(this).val()-1)][1])
            if(set_type == 6){
                var arraynew_car = getdistypeprice(dis_type,price,user_level_dis[($(this).val()-1)][2])
                obj.parent().parent().parent().find(".aryi_olds").empty().append(getprice(arraynew_car))
            }
            obj.parent().parent().parent().find(".olds").empty().append(getprice(new_old))
            obj.parent().parent().parent().find(".cars").empty().append(getprice(new_car))
        }else{

            var old = obj.parent().parent().parent().find(".old").html();
            var car = obj.parent().parent().parent().find(".car").html();

            if(set_type == 6){
                var aryi = obj.parent().parent().parent().find(".aryi").html();
                var arraynew_car = getdistypeprice(dis_type,aryi,user_level_dis[($(this).val()-1)][2])
                obj.parent().parent().parent().find(".aryi_olds").empty().append(getprice(arraynew_car))
            }
            var new_old = getdistypeprice(dis_type,old,user_level_dis[($(this).val()-1)][0])
            var new_car = getdistypeprice(dis_type,car,user_level_dis[($(this).val()-1)][1])

            obj.parent().parent().parent().find(".olds").empty().append(getprice(new_old))
            obj.parent().parent().parent().find(".cars").empty().append(getprice(new_car))
        }

    }

})

//组合商品阶梯折扣选择
$('body').on('change','.group-select',function(){
    var discount = $(this).val();
    update_sku_price($(this).parents(".modal-body"),discount);
    $('.dis-select option').each(function(i,op){
        if($(op).val() == discount){
            $(op).attr('selected',true);
        }
    })
    $('.dis-select').select2();

})

//更新折扣价格
function update_sku_price(obj_body,comm_discount=null){
    var obj_trs = obj_body.find(".sku-tb").find("tr");
    var is_user_segment = obj_trs.find(".is_user_segment").val()
    var is_segment_key = obj_trs.find(".is_segment_key").val()
    if(!comm_discount){
        comm_discount = $('.dis-val')[0].value;
    }

    for(var iis=0;iis<obj_trs.length;iis++){
        var obj_tr = obj_trs.eq(iis);
        if(is_user_segment == 1){
            if(is_segment_key == 0){
                var old_price = obj_tr.find("#td_price").html();
                $(".levelitem_"+comm_discount).find(".user_l").each(function(i,v){
                    var new_price = getdistypeprice(dis_type,old_price,$(v).val())
                    $(this).parent().parent().parent().parent().find(".sku_user_price_"+i).empty().append(getprice(new_price))
                });
            }else{

                var discount_lve = [];
                $(".levelitem_"+comm_discount).find(".user_l").each(function(i,v){
                    discount_lve.push($(v).val())
                });
                obj_tr.find(".sku_user_price").each(function(i,v){
                    var new_price = getdistypeprice(dis_type,$(v).text(),discount_lve[i])
                    $(this).parent().parent().parent().parent().find(".sku_user_price_"+i).empty().append(getprice(new_price))
                })
            }
        }
        if(is_user_segment == 2){
            if(is_segment_key == 0){
                var old_price = obj_tr.find("#td_price").html();
                var none = 1;
                var nissan = 1;
                var aryia = 1;
                $(".levelitem_"+comm_discount).find(".car_l").each(function(i,v){
                    if(i == 0 ){
                        none =$(v).val()
                    }
                    if(i == 1){
                        nissan = $(v).val()
                    }
                    if(i == 2){
                        aryia = $(v).val()
                    }

                });
                var new_none_price = getdistypeprice(dis_type,old_price,none)
                var new_nissan_price = getdistypeprice(dis_type,old_price,nissan)
                if(set_type == 6) {
                    var aryi = obj_tr.find(".aryi").html();
                    var arraynew_car = getdistypeprice(dis_type, aryi, aryia)
                    obj_tr.find(".aryi_olds").empty().append(getprice(arraynew_car))
                }
                obj_tr.find(".olds").empty().append(getprice(new_none_price))
                obj_tr.find(".cars").empty().append(getprice(new_nissan_price))
            }else{
                var old_price = obj_tr.find(".old").html();
                var old_car_price = obj_tr.find(".car").html();

                var none = 1;
                var nissan = 1;
                var aryia = 1;
                $(".levelitem_"+comm_discount).find(".car_l").each(function(i,v){
                    if(i == 0 ){
                        none =$(v).val()
                    }
                    if(i == 1){
                        nissan = $(v).val()
                    }
                    if(i == 2){
                        aryia = $(v).val()
                    }

                });

                if(set_type == 6) {
                    var aryi = obj_tr.find(".aryi").html();
                    var arraynew_car = getdistypeprice(dis_type, aryi, aryia)
                    obj_tr.find(".aryi_olds").empty().append(getprice(arraynew_car))
                }
                var new_none_price = getdistypeprice(dis_type,old_price,none)
                var new_nissan_price = getdistypeprice(dis_type,old_car_price,nissan)
                obj_tr.find(".olds").empty().append(getprice(new_none_price))
                obj_tr.find(".cars").empty().append(getprice(new_nissan_price))
            }
        }
        if(is_user_segment == 0){
            if(!comm_discount){
                comm_discount = $('.dis-val')[0].value;
            }
            if(is_segment_key == 1){
                obj_tr.find(".sku_user_price").each(function(i,v){
                    var new_price = getdistypeprice(dis_type,$(v).text(),comm_discount)
                    $(this).parent().parent().parent().parent().find(".sku_user_price_"+i).empty().append(getprice(new_price))
                })
            }
            else if(is_segment_key == 2){
                var old_price = obj_tr.find(".old").html();
                var old_car_price = obj_tr.find(".car").html();
                var new_none_price = getdistypeprice(dis_type,old_price,comm_discount)
                var new_nissan_price = getdistypeprice(dis_type,old_car_price,comm_discount)
                obj_tr.find(".olds").empty().append(getprice(new_none_price))
                obj_tr.find(".cars").empty().append(getprice(new_nissan_price))
            }else{
                if(dis_type!=3 && (comm_discount==null || comm_discount<=0)) return false;
                // var obj_tr = obj_body.find(".sku-tb").find("tr");
                var this_val = obj_tr.eq(iis);

                //console.log(this_val.html());
                //console.log(this_val.find("#td_price").text());
                if(dis_type==3){
                    if (discount_type == 2){
                        this_val.find(".sku-price").val(this_val.find("#td_price").text());
                    }else{
                        this_val.find(".sku-price").val('0');
                    }
                }else {
                    if (dis_type == 1) {
                        var sku_price = ((comm_discount / 10) * this_val.find("#td_price").text());

                    } else if (dis_type == 2) {
                        var sku_price = this_val.find("#td_price").text() - comm_discount;
                    }
                    switch (discount_type) {
                        case 1: $('#dis_t_word').text(dis_t_word+'（仅商品）');break;
                        case 2: $('#dis_t_word').text(dis_t_word+'（仅商品工时）');
                            sku_price = this_val.find("#td_price").text();break;
                        case 3: $('#dis_t_word').text(dis_t_word+'（商品+工时）');break;
                    }
                    this_val.find(".sku-price").val(Math.round(sku_price * 100) / 100);      //保留两位小数
                }
            }
        }


    }

}

//查看商品
$("body").on("click",".btn-fight-view",function (e) {
    e.preventDefault();
    var set_sku_list=$(this).data('sku-list');
    var sku_price_list=set_sku_list.sku_list;
    // var leve_dis = set_sku_list.sku_dis
    // alert(leve_dis.toString())
    var slope_option = '';
    var dis_input = '';
    // if (dis_type == 1 || dis_type == 2) {
    //     var name = (dis_type == 1 ? 'set-discount':'set-price');
    //     var dis_name = (dis_type == 1 ? '折':'元');
    //     $('.'+name).each(function (index, item) {
    //         slope_option += '<option value="'+$(item).val()+'">' + $(item).val()+ dis_name + '</option>'
    //         dis_input += ' <div class="input-group col-md-2 pull-left" style="margin-right:10px;margin-top:5px" > ' +
    //             '<input type="text" class="form-control dis-val" maxlength="3" readonly value="'+ $(item).val() +'">'+
    //             '<span class="input-group-addon">'+dis_name+'</span>' +
    //             '</div>';
    //     })
    //     $('#comm_discount').html(dis_input);
    // }
    ///////////////////////////
    var name = (dis_type == 1 ? 'set-discount':'set-price');
    var dis_name = (dis_type == 1 ? '折':'元');
    var dis_input = '';
    var slope_option = '';
    var is_segment = $(this).data('is_segment_key')

    console.log('user_segment:',user_segment)
    if(user_segment == 0){//旧路，也天添加有车主或有会的
        if(is_segment == 0) { //商品无会员无车主
            user_level_dis = [];
            $('.'+name).each(function (index, item) {
                slope_option += '<option value="'+$(this).val()+'">' + $(this).val()+ dis_name + '</option>'
                dis_input += ' <div class="input-group col-md-2 pull-left" style="margin-right:10px;margin-top:5px" > ' +
                    '<input type="text" class="form-control dis-val" maxlength="3" readonly value="'+ $(this).val() +'">'+
                    '<span class="input-group-addon">'+dis_name+'</span>' +
                    '</div>';
                user_level_dis.push($(this).val())
                if($(this).val()==''){
                    dis_tap = 1;
                }
            })
        }

        if(is_segment == 1) { //商品有会员
            user_level_dis = [];
            $("."+name).each(function(){
                slope_option += '<option value="'+$(this).val()+'">' + $(this).val()+ dis_name + '</option>'
                dis_input += ' <div class="input-group col-md-2 pull-left" style="margin-right:10px;margin-top:5px" > ' +
                    '<input type="text" class="form-control dis-val" maxlength="3" readonly value="'+ $(this).val() +'">'+
                    '<span class="input-group-addon">'+dis_name+'</span>' +
                    '</div>';
                user_level_dis.push($(this).val())
                if($(this).val()==''){
                    dis_tap = 1;
                }
            })
        }
        if(is_segment == 2) { //商品有车主
            user_level_dis = [];
            $("."+name).each(function(){
                slope_option += '<option value="'+$(this).val()+'">' + $(this).val()+ dis_name + '</option>'
                dis_input += ' <div class="input-group col-md-2 pull-left" style="margin-right:10px;margin-top:5px" > ' +
                    '<input type="text" class="form-control dis-val" maxlength="3" readonly value="'+ $(this).val() +'">'+
                    '<span class="input-group-addon">'+dis_name+'</span>' +
                    '</div>';
                user_level_dis.push($(this).val())
                if($(this).val()==''){
                    dis_tap = 1;
                }
            })
        }
    }
    //定向会员
    if(user_segment == 1){
        for(i=1;i<=$("#member_box .rule_new_box").find(".level_item").size();i++) {
            slope_option += '<option value="'+i+'">活动梯度' + i + '</option>';
        }
        user_level_title = []
        user_level_code = []
        user_level_dis = [];
        var user_html = '';
        $(".user_level").each(function(i,v){
            //会员等级名
            user_level_title.push($(this).data('title'))
            //会员等编码
            user_level_code.push($(this).data('code'))
            var dis_tmp = []
            var dis_type_class = ".dis_type_" + (i + 1);
            $(dis_type_class).each(function(i,v){
                if($(this).val() !=''){
                    dis_tmp.push($(this).val())
                }
            })
            if(dis_tmp != ''){
                ////////////////////补全数组////////////////////////
                for(var ki = 0 ; ki < selected_options();ki++){
                    dis_tmp.unshift(0)
                }
                ////////////////////////////////////////////
                user_level_dis[i] = dis_tmp
            }
        })

        user_html = "<table><tr><td style='width:100px'></td>"
        $(user_level_title).each(function(i,v){
            user_html = user_html+ "<td style='width:100px'>"+v+"</td>"
        })

        $(user_level_dis).each(function(i,v){
            user_html = user_html+ "<tr><td>活动梯度"+(i+1)+"</td>"
            $(v).each(function(ii,vv){
                user_html = user_html+ "<td><input value='"+vv+"' size='3' disabled/>"+dis_name+"</td>"
            })
            user_html = user_html+ "</tr>"
        })
        user_html = user_html + "</tr></table>";

        $("#show_title_box").hide()
        $("#user_tmp_box").empty().append(user_html)

        // $(".v_dis_type").each(function(){
        //     if($(this).val() == ''){
        //         ////////////////////补全数组////////////////////////
        //         for(var ki = 0 ; ki < selected_options();ki++){
        //             dis_tmp.unshift(0)
        //         }
        //         ////////////////////////////////////////////
        //         dis_tap = 1;
        //     }
        // })
        $("#user_tmp_box").show();
        $("#car_tmp_box").hide();
    }
    //定向车主
    if(user_segment == 2){
        for(i=1;i<=$("#car_onwer_box .rule_new_box").find(".level_item").size();i++) {
            slope_option += '<option value="'+i+'">活动梯度' + i + '</option>';
        }
        user_level_title = [];
        // user_level_title.push('非车主','车主')
        if(set_type == 6){
            user_level_title.push('非车主','车主','Ariya车主')
        }else{
            user_level_title.push('非车主','车主')
        }
        user_level_dis = [];
        var user_html = '';
        $(".level_item").each(function(i,v){
            var dis_tmp = []
            var dis_type_class = ".carv_dis_type_" + (i + 1);
            $(dis_type_class).each(function(i,v){
                if($(this).val() !=''){
                    dis_tmp.push($(this).val())
                }else{
                    dis_tmp.push(0)
                }
            })

            if(dis_tmp != ''){
                // if(set_type == 6) {
                //     if($(dis_tmp).size() == 2){
                //         dis_tmp.unshift(0)
                //     }
                // }else{
                //     if($(dis_tmp).size() == 1){
                //         dis_tmp.unshift(0)
                //     }
                // }
                user_level_dis[i] = dis_tmp
            }
        })

        user_html = "<table><tr><td style='width:150px'></td>"
        $(user_level_title).each(function(i,v){
            user_html = user_html+ "<td style='width:100px'>"+v+"</td>"
        })

        $(user_level_dis).each(function(i,v){
            user_html = user_html+ "<tr><td>活动梯度"+(i+1)+"</td>"
            $(v).each(function(ii,vv){
                user_html = user_html+ "<td><input value='"+vv+"' size='3' disabled/>"+dis_name+"</td>"
            })
            user_html = user_html+ "</tr>"
        })
        user_html = user_html + "</tr></table>";

        $("#show_title_box").hide()
        $("#car_tmp_box").empty().append(user_html)

        $(".carv_dis_type").each(function(){
            if($(this).val() == ''){
                dis_tap = 1;
            }
        })
        $("#user_tmp_box").hide();
        $("#car_tmp_box").show();
    }
    ///////////////////////////

    var obj=$(this);
    $.getJSON(getSkuUrl,{commodity_id:set_sku_list.commodity_id,commodity_set_id:set_sku_list.commodity_set_id},function (res) {
        var sku_list=res.data.sku_list;
        var commodity = res.data.commodity_row;
        //console.log(sku_list)
        $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
        $(".sku-comm").html(res.data.commodity_row.commodity_name);
        var html='';
        // if (res.data.commodity_row.is_grouped == 0){
        //     $('.sku-tb').parent().show();
        $.each(sku_list,function(i,val){
            var price='';
            var checked=''
            if(sku_price_list[val.id]){
                price  =sku_price_list[val.id];
                checked='checked';
            }
            var sku_check='<input type="checkbox" ' +checked+'  class="sku-ckeck" value="6" name="sku-ckeck">';
            var sku_price= '<div class="input-group " style="width: 100px;">'+
                '<input  type="text" class="form-control sku-price input-sm" readonly="readonly" value="'+price+'" data-parsley-required="true"  data-parsley-min="0" data-parsley-pattern-message="格式不正确,请输入不小于0的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">'+
                '<span class="input-group-addon">元</span> </div>';
            //////////////////////////////

            if(user_segment == 0){//旧路，也要加有车主或有会的
                if(commodity.is_segment == 0) { //商品无会员无车主
                    html += '<tr set_sku_id="' + val.id + '">' +
                        '<td>'+ sku_check +'</td>' +
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="0"  type="hidden" /><input class="is_user_segment" value="0"  type="hidden" />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">' + val.price + '</td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select ">'+slope_option+'</select></div></td>' +
                        '<td >' + sku_price + '</td>' +
                        '<td >' + val.stock +
                        '</td> </tr>';
                }
                if(commodity.is_segment == 1) { //商品有会员
                    var ht = "";
                    var ht_price = ""
                    var n = 0;
                    $.each(commodity.segment.user_segment_name,function(key,value){
                        if(value > 0){
                            var product_price = getProductPrice(val.price,value,commodity.segment.discount_type,commodity.segment.max_discount_amount)
                            var sku_price = getdistypeprice(dis_type,product_price,user_level_dis[0])
                            ht = ht +"<div>" +key +":<span num="+n+" class='sku_user_price'>"+ getprice(product_price) + "</span></div>"
                            ht_price = ht_price +"<div>" +key +":<span class='sku_user_price_"+n+"'>"+ getprice(sku_price) + "</span></div>"
                        }else{
                            var sku_price = getdistypeprice(dis_type,val.price ,user_level_dis[0])
                            ht = ht + "<div>" + key +":<span num="+n+" class='sku_user_price'>"+ getprice(val.price)  + "</span></div>"
                            ht_price = ht_price +"<div>" +key +":<span class='sku_user_price_"+n+"'>"+ getprice(sku_price)+ "</span></div>"
                        }
                        n = n + 1;
                    })

                    html += '<tr set_sku_id="' + val.id + '" >' +
                        '<td>'+ sku_check +'</td>' +
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="1" type="hidden"  /><input class="is_user_segment" value="0" type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price"><div class="input-group " style="width: 100px;">'+ ht +'</div></td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td><div class="input-group " style="width: 100px;">'+ ht_price +'</div></td>' +
                        '<td>' + val.stock + '</td> </tr>';
                }
                if(commodity.is_segment == 2) { //商品有车主
                    var selected =  set_sku_list.comm_discount[val.id] ;
                    var product_price = getProductPrice(val.price,commodity.segment.disc,commodity.segment.discount_type,commodity.segment.max_discount_amount)
                    var no_car_sku_price = getdistypeprice(dis_type,val.price ,selected)
                    var car_sku_price    = getdistypeprice(dis_type,product_price ,selected)
                    if(set_type == 6){
                        var arr_car_sku_price    = getdistypeprice(dis_type,product_price ,selected)
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div><div>Ariya车主:<span class="aryi">'+ getprice(arr_car_sku_price) +'</span></div>';
                        var ariya = '<div>Ariya车主:<span className="aryi">' + getprice(val.price * commodity.segment.disc) + '</span></div>';
                    }else{
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div>';
                        var ariya = ''
                    }
                    html += '<tr set_sku_id="' + val.id + '" >"' +
                        '<td>'+ sku_check +'</td>' +
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="2" type="hidden"  /><input class="is_user_segment" value="0" type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">'+'<div>非车主：<span class="old">' + val.price + '</span></div><div>车主:<span class="car">'+ getprice(product_price) +'</span></div>'+ariya+'</td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td >' + sku_price + '</td>' +
                        '<td >' + val.stock + '</td> </tr>';
                }
            }
            console.log('html--------0',html)
            //定向会员
            if(user_segment == 1){
                if(commodity.is_segment == 0) { //商品无会员
                    var ht = "";
                    var ht_price = ""
                    var n = 0;


                    $.each(user_level_dis[(set_sku_list.comm_discount_select[val.id]-1)],function(key,value){
                        sku_price = getdistypeprice(dis_type,val.price,value)
                        ht_price = ht_price +"<div>" +user_level_title[key] +":<span class='ret_sku_price sku_user_price_"+n+"'>"+ getprice(sku_price) + "</span></div>"
                        n = n + 1;
                    })

                    html += '<tr set_sku_id="' + val.id + '" >' +
                        '<td>'+ sku_check +'</td>' +
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="0" type="hidden" /><input class="is_user_segment" value="1"  type="hidden" />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">' + val.price + '</td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td><div class="input-group sku_user_price" style="width: 100px;">'+ ht_price +'</div></td>' +
                        '<td>' + val.stock + '</td> </tr>';

                }
                if(commodity.is_segment == 1) { //商品有会员
                    var ht = "";
                    var ht_price = ""
                    var n = 0;
                    $.each(commodity.segment.user_segment_name,function(key,value){
                        if(value > 0){
                            var product_price = getProductPrice(val.price,value,commodity.segment.discount_type,commodity.segment.max_discount_amount)
                            var sku_price = getdistypeprice(dis_type,product_price,user_level_dis[0][n])
                            ht = ht +"<div>" +key +":<span num="+n+" class='sku_user_price'>"+ getprice(product_price) + "</span></div>"
                            ht_price = ht_price +"<div>" +key +":<span class='sku_user_price_"+n+"'>"+ getprice(sku_price) + "</span></div>"
                        }else{
                            var sku_price = getdistypeprice(dis_type,val.price ,user_level_dis[0][n])
                            ht = ht + "<div>" + key +":<span num="+n+" class='sku_user_price'>"+ getprice(val.price)  + "</span></div>"
                            ht_price = ht_price +"<div>" +key +":<span class='sku_user_price_"+n+"'>"+ getprice(sku_price)+ "</span></div>"
                        }
                        n = n + 1;
                    })
                    html += '<tr set_sku_id="' + val.id + '" >' +
                        '<td>'+ sku_check +'</td>' +
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="1" type="hidden"   /><input class="is_user_segment" value="1"  type="hidden" />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price"><div class="input-group " style="width: 100px;">'+ ht +'</div></td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td><div class="input-group " style="width: 100px;">'+ ht_price +'</div></td>' +
                        '<td>' + val.stock + '</td> </tr>';
                }
            }
            console.log('html--------1',html)
            //定向车主
            if(user_segment == 2){

                var selected =  set_sku_list.comm_discount_select[val.id]-1 ;
                if(commodity.is_segment == 2) { //商品有车主
                    var product_price = getProductPrice(val.price,commodity.segment.disc,commodity.segment.discount_type,commodity.segment.max_discount_amount)
                    var no_car_sku_price = getdistypeprice(dis_type,val.price ,user_level_dis[selected][0])
                    var car_sku_price    = getdistypeprice(dis_type,product_price ,user_level_dis[selected][1])
                    if(set_type == 6){
                        var arr_car_sku_price    = getdistypeprice(dis_type,product_price ,user_level_dis[selected][2])
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div><div>Ariya车主:<span class="aryi">'+ getprice(arr_car_sku_price) +'</span></div>';
                        var ariy = '<div>Ariya车主:<span class="aryi">'+ getprice(product_price) +'</span></div>';
                    }else{
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div>';
                        var ariy = '';
                    }
                    html += '<tr set_sku_id="' + val.id + '" >"' +
                        '<td>'+ sku_check +'</td>' +
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="2" type="hidden"  /><input class="is_user_segment" value="2" type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">'+'<div>非车主：<span class="old">' + val.price + '</span></div><div>车主:<span class="car">'+ getprice(product_price) +'</span></div> '+ariy+'</td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td >' + sku_price + '</td>' +
                        '<td >' + val.stock + '</td> </tr>';
                }
                if(commodity.is_segment == 0) { //商品无车主
                    var no_car_sku_price = getdistypeprice(dis_type,val.price ,user_level_dis[selected][0])
                    var car_sku_price    = getdistypeprice(dis_type,val.price ,user_level_dis[selected][1])
                    if(set_type == 6){
                        var arr_car_sku_price    = getdistypeprice(dis_type,val.price ,user_level_dis[selected][2])
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div><div>Ariya车主:<span class="aryi">'+ getprice(arr_car_sku_price) +'</span></div>';

                    }else{
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div>';

                    }


                    html += '<tr set_sku_id="' + val.id + '">' +
                        '<td>'+ sku_check +'</td>' +
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="0"  type="hidden" /><input class="is_user_segment" value="2" type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">' + val.price + '</td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td >' + sku_price + '</td>' +
                        '<td >' + val.stock +
                        '</td> </tr>';
                }

            }
            console.log('html--------2',html)
            //////////////////////////////
        });
        console.log('html--------3',html)
        // }else{
        //     $('.sku-tb').parent().hide();
        // }
        if(dis_type==1){
            $("#sku-modal").find("[name='comm_discount']").val($("[name='discount']").val());
        }else if(dis_type==2){
            $("#sku-modal").find("[name='comm_discount']").val($("[name='dis_money']").val());
        }else if(dis_type==3){
            $("#sku-modal").find("[name='comm_discount']").val('');
        }

        $("#sku-modal").data("comm-id",set_sku_list.commodity_id);
        $("#sku-modal").data("comm-set-id",set_sku_list.commodity_set_id);
        $("#comm-name-header").html(commodity.commodity_name);
        var index=$(".btn-fight-view").index(obj);
        $("#sku-modal").data('type',index);
        $(".sku-tb").html(html);

        var group_select = '<select class="form-control group-select op-select2">'+slope_option+'</select>';
        $('.dis-group').html(group_select);
        if(commodity.is_grouped == 1){
            $("#group_boxs").show()
            $('.dis-select').attr('disabled',true)
        }else{
            $("#group_boxs").hide()
        }

        $(".sku-tb tr").each(function(index,item){
            var set_sku_id = $(item).attr('set_sku_id');
            if(set_sku_list.comm_discount_select && set_sku_list.comm_discount_select[set_sku_id]){
                $(item).find('.dis-select option').each(function(i,op){
                    if($(op).val() == set_sku_list.comm_discount_select[set_sku_id]){
                        $(op).attr('selected',true);
                    }
                })
                $('.group-select option').each(function(i,op){
                    if($(op).val() == set_sku_list.comm_discount_select[set_sku_id]){
                        $(op).attr('selected',true);
                    }
                })
            }
        })
        $("#sku-modal").modal('show');
        //$('.op-select2').select2();
        //if(set_sku_list.dis_type !== dis_type){
        //    update_sku_price($("#sku-modal").find(".modal-body"));
       // }
        //dlr初始化
        $("#sku-modal").find(".sku-dlr").val(set_sku_list.dlr_name).attr("data-sku-dlr",set_sku_list.dlr_code);
    });

    //alert($(this).data("sku-list"));
})

//修改查看商品
$("body").on("click",".btn-fight-view-edit",function (e) {
    e.preventDefault();
    var set_sku_list=$(this).data('sku-list');
    var sku_dis_select= JSON.parse(set_sku_list.sku_dis_select);
    var sku_price_list= set_sku_list.sku_list;
    var slope_option = '';
    var dis_input = '';

    ///////////////////////////
    var name = (dis_type == 1 ? 'set-discount':'set-price');
    var dis_name = (dis_type == 1 ? '折':'元');
    var dis_input = '';
    var slope_option = '';
    var is_segment = $(this).data('segment')

    if(user_segment == 0){//旧路，也天添加有车主或有会的
        if(is_segment == 0) { //商品无会员无车主
            user_level_dis = [];
            $('.'+name).each(function (index, item) {
                dis_input += ' <div class="input-group col-md-2 pull-left" style="margin-right:10px;margin-top:5px" > ' +
                    '<input type="text" class="form-control dis-val" maxlength="3" readonly value="'+ $(this).val() +'">'+
                    '<span class="input-group-addon">'+dis_name+'</span>' +
                    '</div>';
                user_level_dis.push($(this).val())
                if($(this).val()==''){
                    dis_tap = 1;
                }
            })
        }

        if(is_segment == 1) { //商品有会员
            user_level_dis = [];
            $("."+name).each(function(){
                dis_input += ' <div class="input-group col-md-2 pull-left" style="margin-right:10px;margin-top:5px" > ' +
                    '<input type="text" class="form-control dis-val" maxlength="3" readonly value="'+ $(this).val() +'">'+
                    '<span class="input-group-addon">'+dis_name+'</span>' +
                    '</div>';
                user_level_dis.push($(this).val())
                if($(this).val()==''){
                    dis_tap = 1;
                }
            })
        }
        if(is_segment == 2) { //商品有车主
            user_level_dis = [];
            $("."+name).each(function(){
                dis_input += ' <div class="input-group col-md-2 pull-left" style="margin-right:10px;margin-top:5px" > ' +
                    '<input type="text" class="form-control dis-val" maxlength="3" readonly value="'+ $(this).val() +'">'+
                    '<span class="input-group-addon">'+dis_name+'</span>' +
                    '</div>';
                user_level_dis.push($(this).val())
                if($(this).val()==''){
                    dis_tap = 1;
                }
            })
        }
        $('#comm_discount').html(dis_input);
    }
    //定向会员
    if(user_segment == 1){
        user_level_title = []
        user_level_code = []
        user_level_dis = [];
        var user_html = '';
        $(".user_level").each(function(i,v){
            //会员等级名
            user_level_title.push($(this).data('title'))
            //会员等编码
            user_level_code.push($(this).data('code'))
            var dis_tmp = []
            var dis_type_class = ".dis_type_" + (i + 1);
            $(dis_type_class).each(function(i,v){
                if($(this).val() !=''){
                    dis_tmp.push($(this).val())
                }
            })
            if(dis_tmp != ''){
                ////////////////////补全数组////////////////////////
                for(var ki = 0 ; ki < selected_options();ki++){
                    dis_tmp.unshift(0)
                }
                ////////////////////////////////////////////
                user_level_dis[i] = dis_tmp
            }
        })

        user_html = "<table><tr><td style='width:150px'></td>"
        $(user_level_title).each(function(i,v){
            user_html = user_html+ "<td style='width:100px'>"+v+"</td>"
        })

        $(user_level_dis).each(function(i,v){
            user_html = user_html+ "<tr class='levelitem_"+(i+1)+"'><td>活动梯度"+(i+1)+"</td>"
            $(v).each(function(ii,vv){
                user_html = user_html+ "<td><input value='"+vv+"' size='3' disabled/>"+dis_name+"</td>"
            })
            user_html = user_html+ "</tr>"
        })
        user_html = user_html + "</tr></table>";

        $("#show_title_box").hide()
        $("#user_tmp_box").empty().append(user_html)
        $(".v_dis_type").each(function(){
            if($(this).val() == ''){
                dis_tap = 1;
            }
        })
        $("#user_tmp_box").show();
        $("#car_tmp_box").hide();
    }
    //定向车主
    if(user_segment == 2){
        user_level_title = [];
        if(set_type == 6){
            user_level_title.push('非车主','车主','Ariya车主')
        }else{
            user_level_title.push('非车主','车主')
        }
        user_level_dis = [];
        var user_html = '';
        // $(".level_item").each(function(i,v){
        //     var dis_tmp = []
        //     var dis_type_class = ".carv_dis_type_" + (i + 1);
        //     $(dis_type_class).each(function(i,v){
        //         if($(this).val() !=''){
        //             dis_tmp.push($(this).val())
        //         }
        //     })
        //     if(dis_tmp != ''){
        //         if(set_type == 6) {
        //             if($(dis_tmp).size() == 2){
        //                 dis_tmp.unshift(0)
        //             }
        //         }else{
        //             if($(dis_tmp).size() == 1){
        //                 dis_tmp.unshift(0)
        //             }
        //         }
        //         user_level_dis[i] = dis_tmp
        //     }
        // })

        $(".level_item").each(function(i,v){
            var dis_tmp = []
            var dis_type_class = ".carv_dis_type_" + (i + 1);
            $(dis_type_class).each(function(i,v){
                // if(set_type == 6){
                //     if($(this).val() !=''){
                //         dis_tmp.push($(this).val())
                //     }else{
                //         dis_tmp.push(0)
                //     }
                // }else{
                //     if($(this).val() !=''){
                //         dis_tmp.push($(this).val())
                //     }
                // }
                if($(this).val() !=''){
                    dis_tmp.push($(this).val())
                }else{
                    dis_tmp.push(0)
                }
            })

            if(dis_tmp != ''){
                ////////////////////补全数组////////////////////////
                // if(set_type == 6) {
                //     if($(dis_tmp).size() == 2){
                //         dis_tmp.unshift(0)
                //     }
                // }else{
                //     if($(dis_tmp).size() == 1){
                //         dis_tmp.unshift(0)
                //     }
                // }
                ////////////////////////////////////////////
                user_level_dis[i] = dis_tmp
            }
        })


        user_html = "<table><tr ><td style='width:150px'></td>"
        $(user_level_title).each(function(i,v){
            user_html = user_html+ "<td style='width:100px'>"+v+"</td>"
        })

        $(user_level_dis).each(function(i,v){
            user_html = user_html+ "<tr class='levelitem_"+(i+1)+"'><td>活动梯度"+(i+1)+"</td>"
            $(v).each(function(ii,vv){
                user_html = user_html+ "<td><input value='"+vv+"' size='3' disabled/>"+dis_name+"</td>"
            })
            user_html = user_html+ "</tr>"
        })
        user_html = user_html + "</tr></table>";

        $("#show_title_box").hide()
        $("#car_tmp_box").empty().append(user_html)

        $(".carv_dis_type").each(function(){
            if($(this).val() == ''){
                dis_tap = 1;
            }
        })
        $("#user_tmp_box").hide();
        $("#car_tmp_box").show();
    }
    ///////////////////////////

    var obj=$(this);
    $.getJSON(getSkuUrl,{commodity_id:set_sku_list.commodity_id,commodity_set_id:set_sku_list.commodity_set_id},function (res) {
        var sku_list=res.data.sku_list;
        var commodity = res.data.commodity_row;
        //console.log(sku_list)
        $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
        $(".sku-comm").html(res.data.commodity_row.commodity_name);
        var html='';
        // if (res.data.commodity_row.is_grouped == 0){
        //     $('.sku-tb').parent().show();
        $.each(sku_list,function(i,val){
            var price='';
            var checked=''
            if(sku_price_list[val.id]){
                price  =sku_price_list[val.id];
                checked='checked';
            }
            var sku_check='<input type="checkbox" ' +checked+'  class="sku-ckeck" value="6" name="sku-ckeck">';
            var sku_price= '<div class="input-group " style="width: 100px;">'+
                '<input  type="text" class="form-control sku-price input-sm" readonly="readonly" value="'+price+'" data-parsley-required="true"  data-parsley-min="0" data-parsley-pattern-message="格式不正确,请输入不小于0的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">'+
                '<span class="input-group-addon">元</span> </div>';
            //////////////////////////////

            if(user_segment == 0){//旧路，也要加有车主或有会的
                if(commodity.is_segment == 0) { //商品无会员无车主
                    slope_option =  '<option>' + sku_dis_select[val.id] + dis_name+'</option>'
                    html += '<tr set_sku_id="' + val.id + '">' +
                        '<td>'+sku_check+'</td>'+
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="0" type="hidden"  /><input class="is_user_segment" value="0" type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">' + val.price + '</td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select ">'+slope_option+'</select></div></td>' +
                        '<td >' + sku_price + '</td>' +
                        '<td >' + val.stock +
                        '</td> </tr>';
                }
                if(commodity.is_segment == 1) { //商品有会员
                    var ht = "";
                    var ht_price = ""
                    var n = 0;
                    var selected = sku_dis_select[val.id] -1
                    slope_option = '<option>' + sku_dis_select[val.id] + dis_name+'</option>'
                    $.each(commodity.segment.user_segment_name,function(key,value){
                        if(value > 0){
                            var product_price = getProductPrice(val.price,value,commodity.segment.discount_type,commodity.segment.max_discount_amount)
                            var sku_price = getdistypeprice(dis_type,product_price,user_level_dis[selected])
                            //  var sku_price = getdistypeprice(dis_type,val.price * value,user_level_dis[selected])
                            ht = ht +"<div>" +key +":<span num="+n+" class='sku_user_price'>"+ getprice(product_price) + "</span></div>"
                            ht_price = ht_price +"<div>" +key +":<span class='sku_user_price_"+n+"'>"+ getprice(sku_price) + "</span></div>"
                        }else{
                            var sku_price = getdistypeprice(dis_type,val.price ,user_level_dis[selected])
                            ht = ht + "<div>" + key +":<span num="+n+" class='sku_user_price'>"+ getprice(val.price)  + "</span></div>"
                            ht_price = ht_price +"<div>" +key +":<span class='sku_user_price_"+n+"'>"+ getprice(sku_price)+ "</span></div>"
                        }
                        n = n + 1;
                    })

                    html += '<tr set_sku_id="' + val.id + '" >' +
                        '<td>'+sku_check+'</td>'+
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="1"  type="hidden" /><input class="is_user_segment" value="0" type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price"><div class="input-group " style="width: 100px;">'+ ht +'</div></td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td><div class="input-group " style="width: 100px;">'+ ht_price +'</div></td>' +
                        '<td>' + val.stock + '</td> </tr>';
                }
                if(commodity.is_segment == 2) { //商品有车主
                    slope_option = '<option>' + sku_dis_select[val.id] + dis_name+'</option>'
                    var selected =  set_sku_list.comm_discount[val.id] ;
                    var product_price = getProductPrice(val.price,commodity.segment.disc,commodity.segment.discount_type,commodity.segment.max_discount_amount)
                    var no_car_sku_price = getdistypeprice(dis_type,val.price ,selected)
                    var car_sku_price    = getdistypeprice(dis_type,product_price ,selected)
                    var arr_car_sku_price    = getdistypeprice(dis_type,product_price ,selected)


                    if(set_type == 6){
                        var arr_car_sku_price    = getdistypeprice(dis_type,product_price ,selected)
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div><div>Ariya车主:<span class="aryi">'+ getprice(arr_car_sku_price) +'</span></div>';
                    }else{
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div>';
                    }

                    html += '<tr set_sku_id="' + val.id + '" >"' +
                        '<td>'+sku_check+'</td>'+
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="2" type="hidden"  /><input class="is_user_segment" value="0" type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">'+'<div>非车主：<span class="old">' + val.price + '</span></div><div>车主:<span class="car">'+ getprice(product_price) +'</span></div></td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td >' + sku_price + '</td>' +
                        '<td >' + val.stock + '</td> </tr>';
                }
            }
            //定向会员
            if(user_segment == 1){
                slope_option = '<option value="'+sku_dis_select[val.id]+'">活动梯度' + sku_dis_select[val.id] + '</option>';
                if(commodity.is_segment == 0) { //商品无会员
                    var ht = "";
                    var ht_price = ""
                    var n = 0;
                    $.each(user_level_dis[sku_dis_select[val.id]-1],function(key,value){
                        sku_price = getdistypeprice(dis_type,val.price,value)
                        ht_price = ht_price +"<div>" +user_level_title[key] +":<span class='ret_sku_price sku_user_price_"+n+"'>"+ getprice(sku_price) + "</span></div>"
                        n = n + 1;
                    })

                    html += '<tr set_sku_id="' + val.id + '" >' +
                        '<td>'+sku_check+'</td>'+
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="0" type="hidden"  /><input class="is_user_segment" value="1" type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">' + val.price + '</td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td><div class="input-group sku_user_price" style="width: 100px;">'+ ht_price +'</div></td>' +
                        '<td>' + val.stock + '</td> </tr>';

                }
                if(commodity.is_segment == 1) { //商品有会员
                    var ht = "";
                    var ht_price = ""
                    var n = 0;
                    $.each(commodity.segment.user_segment_name,function(key,value){
                        if(value > 0){
                            var product_price = getProductPrice(val.price,value,commodity.segment.discount_type,commodity.segment.max_discount_amount)
                            var sku_price = getdistypeprice(dis_type,product_price,user_level_dis[sku_dis_select[val.id]-1][n])
                            ht = ht +"<div>" +key +":<span num="+n+" class='sku_user_price'>"+ getprice(product_price) + "</span></div>"
                            ht_price = ht_price +"<div>" +key +":<span class='sku_user_price_"+n+"'>"+ getprice(sku_price) + "</span></div>"
                        }else{
                            var sku_price = getdistypeprice(dis_type,val.price ,user_level_dis[sku_dis_select[val.id]-1][n])
                            ht = ht + "<div>" + key +":<span num="+n+" class='sku_user_price'>"+ getprice(val.price)  + "</span></div>"
                            ht_price = ht_price +"<div>" +key +":<span class='sku_user_price_"+n+"'>"+ getprice(sku_price)+ "</span></div>"
                        }
                        n = n + 1;
                    })
                    html += '<tr set_sku_id="' + val.id + '" >' +
                        '<td>'+sku_check+'</td>'+
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="1" type="hidden"  /><input class="is_user_segment" value="1" type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price"><div class="input-group " style="width: 100px;">'+ ht +'</div></td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td><div class="input-group " style="width: 100px;">'+ ht_price +'</div></td>' +
                        '<td>' + val.stock + '</td> </tr>';
                }
            }
            //定向车主
            if(user_segment == 2){
                slope_option = '<option value="'+sku_dis_select[val.id]+'">活动梯度' + sku_dis_select[val.id] + '</option>';
                var selected =  set_sku_list.comm_discount_select[val.id]-1 ;
                if(commodity.is_segment == 2) { //商品有车主
                    var no_car_sku_price = getdistypeprice(dis_type,val.price ,user_level_dis[selected][0])
                    var product_price = getProductPrice(val.price,commodity.segment.disc,commodity.segment.discount_type,commodity.segment.max_discount_amount)

                    var car_sku_price    = getdistypeprice(dis_type,product_price ,user_level_dis[selected][1])
                    if(set_type == 6){
                        var arr_car_sku_price    = getdistypeprice(dis_type,product_price ,user_level_dis[selected][2])
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div><div>Ariya车主:<span class="aryi">'+ getprice(arr_car_sku_price) +'</span></div>';

                    }else{
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div>';

                    }
                    html += '<tr set_sku_id="' + val.id + '" >"' +
                        '<td>'+sku_check+'</td>'+
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="2" type="hidden"  /><input class="is_user_segment" value="2" type="hidden"  />'  + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">'+'<div>非车主：<span class="old">' + val.price + '</span></div><div>车主:<span class="car">'+ getprice(product_price) +'</span></div></td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td >' + sku_price + '</td>' +
                        '<td >' + val.stock + '</td> </tr>';
                }
                if(commodity.is_segment == 0) { //商品无车主

                    var no_car_sku_price = getdistypeprice(dis_type,val.price ,user_level_dis[selected][0])
                    var car_sku_price    = getdistypeprice(dis_type,val.price ,user_level_dis[selected][1])
                    if(set_type == 6){
                        var arr_car_sku_price    = getdistypeprice(dis_type,val.price ,user_level_dis[selected][2])
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div><div>Ariya车主:<span class="aryi">'+ getprice(arr_car_sku_price) +'</span></div>';

                    }else{
                        var sku_price = '<div>非车主：<span class="olds">' + getprice(no_car_sku_price) + '</span></div><div>车主:<span class="cars">'+ getprice(car_sku_price) +'</span></div>';

                    }
                    html += '<tr set_sku_id="' + val.id + '">' +
                        '<td>'+sku_check+'</td>'+
                        '<td class="text-left" style="word-wrap:break-word;word-break:break-all"><input class="is_segment_key" value="0"  type="hidden"  /><input class="is_user_segment" value="2" type="hidden"  />' + val.sku_val + '</td>' +
                        '<td style="word-wrap:break-word;word-break:break-all">' + val.sku_code + '</td>' +
                        '<td id="td_price">' + val.price + '</td>' +
                        '<td><div class="input-group "><select disabled class="form-control dis-select op-select">'+slope_option+'</select></div></td>' +
                        '<td >' + sku_price + '</td>' +
                        '<td >' + val.stock +
                        '</td> </tr>';
                }

            }
            //////////////////////////////
        });
        // }else{
        //     $('.sku-tb').parent().hide();
        // }
        if(dis_type==1){
            $("#sku-modal").find("[name='comm_discount']").val($("[name='discount']").val());
        }else if(dis_type==2){
            $("#sku-modal").find("[name='comm_discount']").val($("[name='dis_money']").val());
        }else if(dis_type==3){
            $("#sku-modal").find("[name='comm_discount']").val('');
        }

        $("#sku-modal").data("comm-id",set_sku_list.commodity_id);
        $("#sku-modal").data("comm-set-id",set_sku_list.commodity_set_id);
        $("#comm-name-header").html(commodity.commodity_name);
        var index=$(".btn-fight-view").index(obj);
        $("#sku-modal").data('type',index);
        $(".sku-tb").html(html);
        var group_select = '<select class="form-control group-select op-select2">'+slope_option+'</select>';
        $('.dis-group').html(group_select);
        if(commodity.is_grouped == 1){
            $("#groupd_tmp_box").html(group_select);
            $("#group_boxs").show()
            $('.dis-select').attr('disabled',true)
            $(".sku-list-box").hide()
        }else{
            $("#group_boxs").hide()
            $(".sku-list-box").show()
        }

        $(".sku-tb tr").each(function(index,item){
            var set_sku_id = $(item).attr('set_sku_id');
            if(set_sku_list.comm_discount_select && set_sku_list.comm_discount_select[set_sku_id]){
                $(item).find('.dis-select option').each(function(i,op){
                    if($(op).val() == set_sku_list.comm_discount_select[set_sku_id]){
                        $(op).attr('selected',true);
                    }
                })
                $('.group-select option').each(function(i,op){
                    if($(op).val() == set_sku_list.comm_discount_select[set_sku_id]){
                        $(op).attr('selected',true);
                    }
                })
            }
        })
        $("#sku-modal").modal('show');
       // $('.op-select2').select2();

       // if(set_sku_list.dis_type !== dis_type){
           // update_sku_price($("#sku-modal").find(".modal-body"));
       // }
        //dlr初始化
        $("#sku-modal").find(".sku-dlr").val(set_sku_list.dlr_name).attr("data-sku-dlr",set_sku_list.dlr_code);
    });
    //alert($(this).data("sku-list"));
})


//批量设置价格
$('#all_price').editable({
    success: function(response, newValue) {
        $(".sku-price").val(newValue);

        $(".editable-cancel").click();
        return false;
    },
    validate: function (value) { //字段验证
        if (!$.trim(value)) {
            return '不能为空';
        }
        if (!(/^[0-9/.]*$/).test(value)) {
            return '格式有误';
        }
    }
});

//弹层经销商
$("#select-dlr").on('click',function () {
    var commodity_id=$(this).parents('#sku-modal').data('comm-id');
    var commodity_set_id=$(this).parents('#sku-modal').data('comm-set-id');
    var dlr_sku_arr =$(this).data('sku-dlr').split(",");
    var haved_dlr_code=getdlr(commodity_set_id);

    var select_data= [];
    if($("#select-dlr").attr('data-sku-dlr').length>0){
        select_data = $("#select-dlr").attr('data-sku-dlr').split(',')
    }

    $.getJSON(getDlr_url,{commodity_set_id:commodity_set_id},function (resData) {
        if(resData.error==0){
            $("#dlr_content").empty();
            var ajaxData = $.parseJSON(resData.data);
            // console.log(ajaxData);
            Custom.selectDlr(ajaxData,select_data,function (dlr_code,dlr_name) {
                $("#select-dlr").val(dlr_name);
                $("#select-dlr").attr("data-sku-dlr",dlr_code.join(','));
            });
        }else {
            layer.msg(resData.msg);
        }
    })

});
//添加专营店
$("#dlr_add").on("click",function () {
    var dlr_checked_code=[];
    var  dlr_checked_name=[];
    $('.dlr_checkbox.single').each(function (i) {
        if($(this).attr("checked")){
            dlr_checked_code.push($(this).attr("data-id"));
            dlr_checked_name.push($(this).attr("data-title"));
        }
    });

    var dlr_ids = dlr_checked_code.join(",");
    var dlr_names = dlr_checked_name.join(",");
    $(".sku-dlr").data("sku-dlr",dlr_ids);
    $(".sku-dlr").val(dlr_names)
    if(dlr_checked_name.length<=0){
        layer.msg("请选择经销商")
        return;
    }
});
//全选专营店
$("#check_all").on("change",function () {
    var check = $(this).attr("checked");
    if(check){
        $.each($(".dlr_checkbox.single"),function (i,ele) {
            $(ele).attr("checked",true);
        })
    }else {
        $.each($(".dlr_checkbox.single"),function (i,ele) {
            $(ele).attr("checked",false);
        })

    }
});

//sku 点击确认
$("#sku-confirm").on('click',function (e) {
    var sku_modal=$(this).parents("#sku-modal");
    var home =  sku_modal.find("#modal_home").val();
    var commodity_list =new Object();
    commodity_list.commodity_id    =sku_modal.data('comm-id');
    commodity_list.commodity_set_id=sku_modal.data('comm-set-id');
    commodity_list.dlr_code        =sku_modal.find(".sku-dlr").attr("data-sku-dlr");
    commodity_list.dlr_name        =sku_modal.find(".sku-dlr").val();
    commodity_list.comm_discount  = {};
    commodity_list.comm_discount_select = {};
    commodity_list.comm_select = {};
    commodity_list.dis_type = dis_type;
    var commodity_name = sku_modal.find("#comm-name-header").html();
    var escapedString = commodity_name.replace(/'/g, "&#39;").replace(/"/g, '&quot;');
    commodity_list.commodity_name  = escapedString;
    commodity_list.lowest_price  =lowest_price;
    commodity_list.highest_price  =highest_price;
    commodity_list.is_grouped  =is_grouped;
    commodity_list.dis_select_sku={}
    commodity_list.dis_level={}
    commodity_list.is_segment_key={}
    commodity_list.is_user_segment={}
    console.log('commodity_list',commodity_list)
    var image=sku_modal.find(".cover-image").attr("src");
    var  commodity_name=sku_modal.find(".sku-comm").html();
    var sku_list={};
    var check_price=true;
    var golab_is_user_segment = 0;
    var  golab_is_segment_key = 0;
    $(".sku-tb tr").each(function () {
        if($(this).find(".sku-ckeck").attr("checked")){
            var set_sku_id=$(this).attr("set_sku_id");
            // var reg=/^0\.([1-9]|\d[0-9])$|^[1-9]\d{0,8}\.\d{0,2}$|^[1-9]\d{0,8}$/
            // //if(!reg.test($(this).find(".sku-price").val())){
            // var a = parseFloat($(this).find(".sku-price").val());
            // var reg = /^\d+(?=\.{0,1}\d+$|$)/;
            // if(!reg.test(a) && is_grouped == 0){
            //     layer.msg('价格格式不对');
            //     check_price=false;
            //     return false;
            // }
            // if(a.toFixed(2)<0 && is_grouped == 0){
            //     layer.msg('价格格式不对');
            //     check_price=false;
            //     return false;
            // }else{
            //     sku_list[set_sku_id]=$(this).find(".sku-price").val();
            // }
            var is_segment_key = $(this).find(".is_segment_key").val();
            var is_user_segment = $(this).find(".is_user_segment").val();
            golab_is_segment_key  = is_segment_key
            golab_is_user_segment = is_user_segment
            var selected_val = $(this).find(".dis-select").val();
            var trobj = $(this);
            var select_val = {};
            var sku_price = {};
            commodity_list.is_segment_key[set_sku_id] = is_segment_key
            commodity_list.is_user_segment[set_sku_id] = is_user_segment
            commodity_list.comm_select[set_sku_id] = $(this).find(".dis-select").val();
            if(is_user_segment == 0){//无定向
                var dis_select =  $(this).find(".dis-select").val()
                if(is_segment_key == 0){//商品无会员无车主
                   // commodity_list.comm_discount[set_sku_id] = dis_select;
                    sku_list[set_sku_id]=$(this).find(".sku-price").val();
                }
                if(is_segment_key == 1){//商品有会员
                    // $.each(user_level_code_list,function(codekey,codevalue){
                    //     select_val[codevalue] = dis_select
                    //     sku_price[codevalue] =  trobj.find('.sku_user_price_'+codekey).text()
                    // })
                    // commodity_list.comm_discount[set_sku_id]= select_val;
                    // sku_list[set_sku_id]= sku_price;
                    sku_price =  trobj.find('.sku_user_price_0').text()
                  //  commodity_list.comm_discount[set_sku_id]= dis_select;
                    sku_list[set_sku_id]= sku_price;
                }
                if(is_segment_key == 2){//商品有车主
                    var old = trobj.find(".olds").text();
                    var new_car = trobj.find(".cars").text();
                    var code = trobj.find(".text-left").data("disc-code")
                    // sku_list[set_sku_id] = {}
                    // sku_list[set_sku_id]['NONE']= old;
                    // sku_list[set_sku_id][code]= new_car;
                    // var dis_select =  $(this).find(".dis-select").val()
                    // commodity_list.comm_discount[set_sku_id]= dis_select;
                   // commodity_list.comm_discount[set_sku_id]= dis_select;
                    sku_list[set_sku_id]= old;
                }
                commodity_list.comm_discount[set_sku_id] = dis_select;
                commodity_list.comm_discount_select[set_sku_id] = dis_select;
            }
            if(is_user_segment == 1){//定向会员
                $.each(user_level_code_list,function(codekey,codevalue){
                    select_val[codevalue] = user_level_dis[selected_val-1][codekey]
                    sku_price[codevalue] =  trobj.find('.sku_user_price_'+codekey).text()
                })
                commodity_list.comm_discount[set_sku_id]= select_val;
                sku_list[set_sku_id]= sku_price;
                var dis_select =  $(this).find(".dis-select").val()
                commodity_list.dis_select = dis_select;
                commodity_list.comm_discount_select[set_sku_id] = dis_select;
            }
            if(is_user_segment == 2){//定向车主
                var old = trobj.find(".olds").text();
                var new_car = trobj.find(".cars").text();
                if(is_segment_key == 0){//商品无车主
                    //sku_list[set_sku_id]= {"NONE_OLD":old,"NONE_CAR":new_car};
                    sku_list[set_sku_id]= old;
                }

                if(set_type == 5){
                    var code = 'N';
                }
                if(set_type == 6){
                    var code = 'P';
                }
                if(set_type == 7){
                    var code = 'V';
                }

                sku_list[set_sku_id] = {}
                if(set_type == 6){
                    var dis_select =  $(this).find(".dis-select").val()
                    if($(".tip_show_0").eq(0).val() != ''){
                        select_val['NONE'] = user_level_dis[dis_select-1][0]
                        sku_list[set_sku_id]['NONE']= old;
                    }
                    if($(".tip_show_1").eq(0).val() !=''){
                        sku_list[set_sku_id]['N']= new_car;
                        select_val['N'] = user_level_dis[dis_select-1][1]
                    }
                    var aryi_car = trobj.find(".aryi_olds").text();
                    sku_list[set_sku_id]['p']= aryi_car;
                    select_val['P'] = user_level_dis[dis_select-1][2]
                }else{
                    if(set_type == 5){
                        var code = 'N';
                    }

                    if(set_type == 7){
                        var code = 'V';
                    }
                    var dis_select =  $(this).find(".dis-select").val()
                    if($(".tip_show_0").eq(0).val() != ''){
                        select_val['NONE'] = user_level_dis[dis_select-1][0]
                        sku_list[set_sku_id]['NONE']= old;

                    }
                    sku_list[set_sku_id][code]= new_car;
                    select_val[code] = user_level_dis[dis_select-1][1]

                    if(user_level_dis[dis_select-1][0] > 0)  select_val['NONE'] = user_level_dis[dis_select-1][0]
                    select_val[code] = user_level_dis[dis_select-1][1]
                }
                commodity_list.comm_discount_select[set_sku_id] = dis_select;
                commodity_list.comm_discount[set_sku_id]= select_val;
            }

        }
        var dis_select =  $(this).find(".dis-select").val()
        commodity_list.dis_select_sku[set_sku_id] = dis_select;
    });

    commodity_list.sku_list=sku_list;
    //新增数据添加到后面
    var json_st=JSON.stringify(commodity_list);
    var type   =sku_modal.data('type');
    //添加

    if (type=='add'){
        var html='<tr class="info haved-add" data><td style="width: 350px;"><input type="hidden" name="home"  class="home" value="'+home+'"/><img class="cover-image" src="'+image+'">'+commodity_name+'</td><td class="text-right">'+
            "<button data-is_user_segment='"+golab_is_user_segment+"' data-is_segment_key='"+golab_is_segment_key+"' data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
            '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list">删除</button></td></tr>';
        //alert(html);
        $("#haved-commodtiy").append(html);
        commodity_select()

    }else {  //查看
        //
        var html='<td style="width: 350px;"><img class="cover-image" src="'+image+'">'+commodity_name+'</td><td class="text-right">'+
            "<button data-is_user_segment='"+golab_is_user_segment+"' data-is_segment_key='"+golab_is_segment_key+"' data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
            '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list ">删除</button></td>';
        $(".btn-fight-view").eq(type).parents('tr').html(html);
        $(".btn-fight-view-edit").eq(type).parents('tr').html(html);
    }

    //专营店 禁用添加按钮
    if(admin_type==2 || admin_type==1){
        var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+commodity_list.commodity_set_id+"']");
        btn_obj.removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
        //btn_obj.parents("button").html('已添加');
        // console.log(btn_obj);
    }
    $("#sku-modal").modal('hide');

    // console.log(commodity_list);
})

/**
 * 获取当前页已选中的商品数量
 * @param comm_set_id_arr
 * @returns {number}
 */
function getSkuConfirmAllUnm(comm_set_id_arr){
    var num = 0;
    for (var i=0; i < currentPageCommSetIds.length; i++){
        var index = $.inArray(currentPageCommSetIds[i], comm_set_id_arr);
        if(index > -1){
            num ++;
        }
    }
    return num;
}

/**
 * 批量添加操作
 * @param sku_modal
 * @param type
 */
function skuConfirm(sku_modal, type = 'add'){
    // console.log('skuConfirm')
    // console.log('sku_modal:',sku_modal);
    var home =  sku_modal.home;
    var commodity_class =  sku_modal.commodity_class;

    var commodity_list =new Object();
    commodity_list.commodity_id    =sku_modal.commodity_id;
    commodity_list.commodity_set_id=sku_modal.commodity_set_id;
    commodity_list.dlr_code        =sku_modal.dlr_code;
    commodity_list.dlr_name        =sku_modal.dlr_name;
    commodity_list.commodity_name  =sku_modal.commodity_name;
    commodity_list.comm_discount   = {};
    commodity_list.sku_list        = {};
    commodity_list.comm_select     = {};
    commodity_list.comm_discount_select = {};
    commodity_list.dis_select_sku={};

    var image= sku_modal.cover_image;
    var  commodity_name=sku_modal.commodity_name;
    if ((!$(".sku-dlr").val() || !commodity_list.dlr_code) && $("input[nam='set_type']") == 1){
        layer.msg('请选择经销商');
        return ;
    }
    //新增数据添加到后面
   // console.log('type:',type)
    //添加
    if (type=='add'){
        //已经添加的直接跳过
        console.log('comm_set_id_arr',comm_set_id_arr)
        var index = $.inArray(commodity_list.commodity_set_id, comm_set_id_arr);
        console.log('index',index)
        if(index > -1){
            $("#sku-modal").modal('hide');
            return;
        }
        var golab_is_segment_key = 0;
        comm_set_id_arr.push(commodity_list.commodity_set_id);
        $.getJSON(getSkuUrl,{commodity_id:sku_modal.commodity_id,commodity_set_id:sku_modal.commodity_set_id},function (res) {
            var skus = res.data.sku_list;
            var commodity_row = res.data.commodity_row;
            $.each(skus,function(i,val){
                var is_user_segment = $('input[name="user_segment"]:checked').val();
                if(is_user_segment != 1 && is_user_segment != 2)  is_user_segment = 0;
                var is_segment_key = res.data.commodity_row.is_segment;
                golab_is_segment_key  = is_segment_key
                golab_is_user_segment = is_user_segment
                var selected_val = 1;
                var dis_select =  1;
                var select_val = {};
                var sku_price = {};
                var code_tmp = {};
                var dis_input = '';
                var name = (dis_type == 1 ? '.set-discount':'.set-price');
                var dis_name =  (dis_type == 1 ? '折':'元');
                if(is_user_segment == 0){//无定向
                    $(name).each(function (index, item) {
                        dis_input += ' <div class="input-group col-md-2 pull-left" style="margin-right:10px;margin-top:5px" > ' +
                            '<input type="text" class="form-control dis-val" maxlength="3" readonly value="'+ $(this).val() +'">'+
                            '<span class="input-group-addon">'+dis_name+'</span>' +
                            '</div>';
                        if($(this).val()==''){
                            dis_tap = 1;
                        }
                    })
                    $('#comm_discount').html(dis_input);
                    var dis_select =  $(name).eq(0).val();
                    if(is_segment_key == 0){//商品无会员无车主
                        commodity_list.sku_list[val.set_sku_id]=val.price;
                    }
                    if(is_segment_key > 0){//商品有会员
                        commodity_list.sku_list[val.set_sku_id] = getprice(getdistypeprice(dis_type,val.price,dis_select));
                    }
                    commodity_list.comm_discount_select[val.set_sku_id] = dis_select;
                    commodity_list.comm_discount[val.set_sku_id]= dis_select;
                    commodity_list.dis_select_sku[val.set_sku_id] = dis_select;
                    commodity_list.comm_select[val.set_sku_id] = dis_select;
                }
                if(is_user_segment == 1){//定向会员
                    var dis_tmp = []
                    var dis_type_class = ".dis_type_1";
                    $(dis_type_class).each(function(i,v){
                        if($(this).val() !=''){
                            dis_tmp.push($(this).val())
                        }
                    })
                    if(dis_tmp != ''){
                        ////////////////////补全数组////////////////////////
                        for(var ki = 0 ; ki < selected_options();ki++){
                            dis_tmp.unshift(0)
                        }
                        ////////////////////////////////////////////
                    }
                    var dis_tmp_tmp = {};
                    if(is_segment_key == 1) {
                        var coden = 0;
                        $.each(commodity_row.segment.user_level_name_true_code, function (rowi, rowv) {
                            var product_price = getProductPrice(val.price, rowv, commodity_row.segment.discount_type, commodity_row.segment.max_discount_amount);
                            code_tmp[rowi] = getprice(getdistypeprice(dis_type, product_price, dis_tmp[coden]))
                            dis_tmp_tmp[rowi] = dis_tmp[coden];
                            coden = coden + 1;

                        })
                    }else{
                           $.each(user_level_code_list,function(codekey,codevalue){
                                dis_tmp_tmp[codevalue] = dis_tmp[codekey];
                                code_tmp[codevalue] = getprice(getdistypeprice(dis_type,val.price,dis_tmp[codekey]))
                            })
                    }
                    commodity_list.sku_list[val.set_sku_id] = code_tmp;
                    commodity_list.comm_discount[val.set_sku_id] = dis_tmp_tmp;
                    commodity_list.dis_select = dis_select;
                    commodity_list.comm_discount_select[val.set_sku_id] = dis_select;
                    commodity_list.comm_select[val.set_sku_id] = dis_select;
                    commodity_list.dis_select_sku[val.set_sku_id] = dis_select;
                }
                if(is_user_segment == 2){//定向车主
                    var old = val.price;
                    if(is_segment_key == 2) {
                        var new_car = getProductPrice(val.price, commodity_row.segment.disc, commodity_row.segment.discount_type, commodity_row.segment.max_discount_amount);
                        if(set_type == 6){
                            var ariy_car = getProductPrice(val.price, commodity_row.segment.pdisc, commodity_row.segment.discount_type, commodity_row.segment.max_discount_amount);
                        }
                    }else{ //商品无车主
                        var new_car = val.price;
                        if(set_type == 6){
                            var ariy_car = val.price;
                        }
                        commodity_list.sku_list[val.set_sku_id]= old;
                    }

                    if(set_type == 5 || set_type == 6){//ariy 只有非车主 车主 和 ary车主
                        var code = 'N';
                    }
                    if(set_type == 7){
                        var code = 'V';
                    }
                    commodity_list.sku_list[val.set_sku_id] = {}
                    var select_val = {}

                    var dis_tmp = []
                    $(".carv_dis_type_1").each(function(i,v){
                        if($(this).val() !=''){
                            dis_tmp.push($(this).val())
                        }else{
                            dis_tmp.push(0)
                        }
                    })


                    if(set_type == 6){
                        if($(".tip_show_0").eq(0).val() != ''){
                            select_val['NONE'] = dis_tmp[0]
                            commodity_list.sku_list[val.set_sku_id]['NONE']= getprice(old * dis_tmp[0] / 10);
                        }
                        if($(".tip_show_1").eq(0).val() !=''){
                            commodity_list.sku_list[val.set_sku_id]['N']= getprice(new_car * dis_tmp[1]);
                            select_val['N'] = dis_tmp[1]
                        }

                        commodity_list.sku_list[val.set_sku_id]['p']= getprice(ariy_car * dis_tmp[2] /10);
                        select_val['P'] = dis_tmp[2]
                    }else{
                        if($(".tip_show_0").eq(0).val() != ''){
                            select_val['NONE'] = dis_tmp[0]
                            commodity_list.sku_list[val.set_sku_id]['NONE']= getprice(old * dis_tmp[0] / 10);
                        }
                        commodity_list.sku_list[val.set_sku_id][code]= getprice(new_car * dis_tmp[1] / 10);
                        select_val[code] = dis_tmp[1]

                        if(dis_tmp[0] > 0)  select_val['NONE'] = dis_tmp[0]
                        select_val[code] = dis_tmp[1]
                    }
                    commodity_list.comm_discount_select[val.set_sku_id] = dis_select;
                    commodity_list.comm_discount[val.set_sku_id]= select_val;
                    commodity_list.comm_select[val.set_sku_id] = dis_select;
                    commodity_list.dis_select_sku[val.set_sku_id] = dis_select;
                }

            })


            //////////////////////////bbb批量添加改这里//////////////////////////////////




            // $.each(skus,function(i,val){
            //     if (dis_type == 1) {
            //         var comm_discount = $('.set-discount')[0].value;
            //         console.log(comm_discount);
            //         var sku_price = ((comm_discount / 10) * val.price);
            //         console.log(sku_price,val);
            //     } else if (dis_type == 2) {
            //         var comm_discount =$('.set-price')[0].value;
            //         var sku_price = val.price - comm_discount;
            //     }
            //     commodity_list.comm_discount[val.set_sku_id] = comm_discount;
            //     commodity_list.sku_list[val.set_sku_id] = Math.round(sku_price);
            // })

            var json_st=JSON.stringify(commodity_list);
            //将已添加的商品入栈
            var html='<tr class="info" data>' +
                '<td style="width: 350px;">' +
                '<input type="hidden" name="home"  class="home" value="'+home+'"/><input type="hidden" '+
                'name="commodity_class"  class="commodity_class" value="'+commodity_class+'"/><img class="cover-image" '+
                ' src="'+image+'">'+commodity_name+'' +
                '</td>' +
                '<td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+' data-is_segment_key="'+golab_is_segment_key+'" class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list" data-comm_name="'+commodity_name+'">删除</button>' +
                '</td>' +
                '</tr>';
            $("#haved-commodtiy").append(html);
            commodity_select()
        });
    }

    //选择添加完毕后，禁用添加按钮
    var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+commodity_list.commodity_set_id+"']");
    btn_obj.removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
    btn_obj.parents("button").html('已添加');
    $("#sku-modal").modal('hide');

    // console.log("add:comm_set_id_arr = "+comm_set_id_arr);
}


/**
 * 设置批量添加操作按钮
 * @param isSkuConfirmAll
 * @param skuConfirmAllUnm
 * @param per_page
 */
function setSkuConfirmAllStatus(isSkuConfirmAll, skuConfirmAllUnm, current_page, per_page){
    current_page = parseInt(current_page);
    var obj = $(".comm-type-search2").parents('.form-group');
    var commodity_class = obj.find("select[name='commodity_class']").val();
    var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();
    var pageAllStr = current_page +'x'+ commodity_class +'v'+ commodity_dlr_type_id;

    //增加批量添加按钮禁用操作
    if(skuConfirmAllUnm == per_page){
        $("#sku-confirm-all")
            .removeClass("btn-white")
            .addClass("btn-default active")
            .html('已全部添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 0);
        skuConfirmAllCurrentPage.push(pageAllStr);
    }else if((isSkuConfirmAll > 0) && (skuConfirmAllUnm != per_page)){
        $("#sku-confirm-all")
            .removeClass("btn-default active")
            .addClass("btn-white")
            .html('批量添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 1);
        skuConfirmAllCurrentPage.splice(skuConfirmAllCurrentPage.indexOf(pageAllStr), 1);
    }else if((isSkuConfirmAll == 0) && (skuConfirmAllCurrentPage.indexOf(pageAllStr) != '-1')){
        $("#sku-confirm-all")
            .removeClass("btn-white")
            .addClass("btn-default active")
            .html('已全部添加');
       $("#sku-confirm-all").data("is-sku-confirm-all", 0);
    }else {
        $("#sku-confirm-all")
            .removeClass("btn-default active")
            .addClass("btn-white")
            .html('批量添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 1);
    }

    $("#sku-confirm-all").data("current-page", current_page);

    //批量操作的时候，需要重新选中当前批量选中操作
    if(isSkuConfirmAll > 0){
        //清除当前选中分页
        $("#comm-pagination li").removeClass('active');
        //选中当前页面
        $("#comm-pagination").find('[pnum="'+current_page+'"]').addClass("active");
    }

}


/**********删除sku************/
$("body").on("click",".del-sku-list",function (e) {
    e.preventDefault();
    //  var sku_list=$(this).parents("tr").find(".btn-fight-view").data("sku-list");
    var sku_list=$(this).parents("tr").find(".btn-sm").data("sku-list");
    var type=$(".del-sku-list").index($(this));
    $("#del-sku-modal").find("#del-data-id").val(type).data('comm-set-id',sku_list.commodity_set_id);
    $("#del-sku-modal").modal('show');
})

$("#del-confirm").on("click",function () {
    $(".del-sku-list").eq($("#del-sku-modal").find("#del-data-id").val()).parents('tr').remove();
    $("#del-sku-modal").modal('hide');
    //专营店端修改添加按钮
    if(admin_type==2 || admin_type==1){
        var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+$("#del-sku-modal").find("#del-data-id").data('comm-set-id')+"']");
        btn_obj.removeClass("btn-default active").addClass("btn-white").attr("disabled",false).html('选择添加');
    }
});

/**********删除sku************/

//遍历已添加数据,获取专营店
function getdlr(commodity_set_id) {
    // console.log(commodity_set_id);
    var dl_code_list=[]
    $('#haved-commodtiy tr').each(function () {
        // var sku_list=$(this).find('.btn-fight-view').data('sku-list');
        var sku_list=$(this).find('.btn-sm').data('sku-list');
        console.log(sku_list);
        if(commodity_set_id==sku_list.commodity_set_id){
            dl_code_list= dl_code_list.concat(sku_list.dlr_code.split(','));
        }
    });
    // console.log(dl_code_list);
    return dl_code_list;
}

$(".datetimepicker3").datetimepicker({
    format:"YYYY-MM-DD HH:mm:00",
    locale: moment.locale('zh-cn'),
});

/* 专营店端获取添加id*/
function gethavedCommodityId() {
    var comm_set_id_arr=[];
    $("#haved-commodtiy tr").each(function () {
        var sku_list=$(this).find(".btn-sm").data("sku-list");
        comm_set_id_arr.push(sku_list.commodity_set_id);
    });
    return comm_set_id_arr;
}

//获取并设置商品分类
function setCommType(param){
    var url = ajaxCommTypeUrl;
    if(param!=null) url=param;
    var data_id = $('#addCommTypeModal').find("[name='text_click_id']").attr('data-id');
    $.get(url,function(res){
        var html='';
        var list = res.data.data;

        //当前页是否已全选择
        var skuConfirmAllUnm = 0;
        //当前页
        var current_page = res.data.list.current_page;
        //当前分页条数
        var per_page = res.data.list.per_page;
        //重置当前页所有的comm_set_id值
        currentPageCommSetIds = [];
        $.each(list,function(i,val){
            currentPageCommSetIds.push(val.commodity_set_id)
            if((isSkuConfirmAll > 0) && (val.is_full != 1) && (val.is_pre != 1)){
                index = 1;
                //批量添加操作
                skuConfirm(val)
                skuConfirmAllUnm +=1;
            }
            var checkbox_obj = '';
            if(val.id==data_id) {
                checkbox_obj = '<label> <input type="checkbox" class="commodity_checkbox" data-id="' + val.id +'" data-name="'+val.comm_type_name+'" data-parent-name="'+val.comm_parent_name+'" value="" name="" checked>选择 </label>';
            }else{
                checkbox_obj = '<label> <input type="checkbox" class="commodity_checkbox" data-id="' + val.id + '" data-name="'+val.comm_type_name+'" data-parent-name="'+val.comm_parent_name+'" value="" name="">选择 </label>';
            }
            html += '<tr id="comm_tab_tr_'+val.id+'">' +
                '<td class="text-center">'+ val.comm_type_name +'</td>' +
                '<td class="text-center">' + checkbox_obj +
                '</td> </tr>';
        });
        $("#add-comm-type-tbody").html(html);

    },'json');
}

//搜索商品分类列表
/* $("#comm-type-search").on('click',function(){
 var obj = $(this).parent();
 var comm_parent_id = obj.find("select[name='comm_parent_id']").val();
 var comm_type_name = obj.find("input[name='comm_type_name']").val();
 var param = ajaxCommTypeUrl + '&comm_parent_id='+comm_parent_id+'&comm_type_name='+comm_type_name;
 initCommType(param);
 });*/

//选择商品-取消
$("#add-comm-no").on('click',function(){
//        var text_id = $(this).closest('.form').find("[name='text_click_id']").val();
//        $('body').find("#"+text_id).attr('data-id','');
//        $('body').find("#"+text_id).val('');
//        $(this).closest('.form').find("input.commodity_checkbox").each(function(){
//            $(this).removeAttr('checked');
//        });
    $("#addCommModal").click();
});
/*------------------- end 添加商品、分类 -----------------------*/

function getSkuList() {
    var sku_list=[];
    $("#haved-commodtiy tr").each(function () {
        //alert($(this).find(".btn-fight-view").data("sku-list"));
        //sku_list.push($(this).find(".btn-fight-view").data("sku-list"));
        sku_list.push($(this).find(".btn-sm").data("sku-list"));
    })
    // console.log(sku_list);
    return sku_list;
}

$("#put-form").on('click',function(){
    var $form=$("#fight-form");
    // dealer_select 有hidden, 就清空 dlr_hide 的value
    if($("#dealer_select").hasClass("hidden")){
        $("#dlr_hide").attr('value','');
    }else{
        var dlr_value = $("#dlr_hide").val();
        //遍历 checkbox-inline, 把没有勾选的数据从dlr_hide 中去除
        $(".checkbox-inline input:not(:checked)").each(function(i,v){
            var reg = new RegExp(this.value,"g");//g,表示全部替换。
            dlr_value = dlr_value.replace(reg,"");
        })
        $("#dlr_hide").attr('value',dlr_value);
    }
    var up_down_channel_name = getUpDownChannel();

    var validate=$form.psly().validate();  //表单验证
    if(!validate) return false;
    var form_data=$form.serialize();
    var home = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
    var user_segment = $('input:radio[name=user_segment]:checked').val();
    //console.log(form_data);
    var sku_list = JSON.stringify(getSkuList())
    var user_segment_options = $('input[name="user_segment_options"]:checked').val();
    var carv_dis_type = '';
    var v_dis_type = '';
    if(user_segment == 1 ){
        $(".v_dis_type").each(function(){
            v_dis_type = v_dis_type + $(this).val() + ","
        })
        carv_dis_type = '';
    }
    if(user_segment == 2 ){
        $(".carv_dis_type").each(function(){
            carv_dis_type = carv_dis_type + $(this).val() + ","
        })
        v_dis_type = '';
    }

    if(user_segment ==1 ){
        var counts = $("#member_box .rule_new_box").find("table").find("tr").eq(0).find(".stylelt").size();
        var is_user_level_die = 0
        $(".level_item").each(function(i,v){
            var num = 0
            var ret_num = 0;
            var classname = ".dis_type_"+ (i+1) + ",.carv_dis_type_"+ (i+1)
            $(classname).each(function(ii,vv){
                var old_num = parseFloat($(this).val())
                if(old_num >0 ){
                    if(dis_type == 1){//折扣
                        if (ret_num > 0 && ret_num < old_num) {
                            is_user_level_die = 1;
                        } else {
                            ret_num = old_num;
                        }
                    }else{//满减
                        if (ret_num > 0 && ret_num > old_num) {
                            is_user_level_die = 1;
                        } else {
                            ret_num = old_num;
                        }
                    }
                }
            })
        })

        if(is_user_level_die == 1){
            layer.msg('低等级优惠小于高等级的优惠');
            return false;
        }
    }


    if (user_segment == 2) {
        var data = [];
        var no_car_val_old = 0;
        var no_car_val_new = 0;
        var is_die = 0;
        var is_car_die = 0;
        $('#car_onwer_box').find('.level_item').each(function (index,value){
            var no_car = parseFloat($(this).find('.tip_show_0').val());
            var car = parseFloat($(this).find('.tip_show_1').val());

            if (index == 0) {
                if (no_car == '') {
                    no_car_val_old = 0
                } else {
                    no_car_val_old = 1;
                }
            } else {
                if (no_car == '') {
                    no_car_val_new = 0;
                } else {
                    no_car_val_new = 1;
                }
                if (no_car_val_new != no_car_val_old) {
                    is_die = 1;
                }
                no_car_val_old = no_car_val_new
            }
            if(dis_type == 1){//折扣
                if (no_car != '' && no_car < car) {
                    is_car_die = 1;
                }
            }else{
                if (no_car != '' && no_car > car) {
                    is_car_die = 1;
                }
            }

            if  (set_type == 6) {
                var a_car = $(this).find('.tip_show_2').val();
                if (no_car != '') {
                    if(dis_type == 1){//折扣
                        if(car == ''){
                            if (no_car < a_car) {
                                is_car_die = 1;
                            }
                        }else{
                            if (no_car < a_car) {
                                is_car_die = 1;
                            }
                            // 日产和pz1a
                            if (car < a_car) {
                                is_car_die = 1;
                            }
                        }

                    }else{
                        if(car == ''){
                            if (no_car > a_car) {
                                is_car_die = 1;
                            }
                        }else{
                            if (no_car > a_car) {
                                is_car_die = 1;
                            }
                            // 日产和pz1a
                            if (car > a_car) {
                                is_car_die = 1;
                            }
                        }



                    }
                }
            }
        });
        if (is_die == 1) {
            layer.msg('多个梯度，请保证多个梯度资格一致');
            return false
        }

        if (is_car_die == 1) {
            layer.msg('车主的优惠力度需大于等于非车主');
            return false
        }
    }

    // 活动图片
    var activity_image=[];
    $(".activity_image_group .goods_pic").each(function(){
        $(this).find('li img').each(function(){
            activity_image.push($(this).attr("image-value"));
        });
    });


    // if(user_segment == 2){
    //     var is_user_level_die = 0
    //     var ret_num = 0;
    //     var rowcount = 0;
    //     $(".level_item").each(function(i,v){
    //         var rowcount_item = 0
    //         if(set_type == 6){
    //             var no_car = $(this).find(".tip_show_0").val()
    //             var nissan_car = $(this).find(".tip_show_1").val()
    //             var aryi_car = $(this).find(".tip_show_2").val()
    //             if(no_car > 0) rowcount_item = rowcount_item +1;
    //             if(nissan_car > 0) rowcount_item = rowcount_item +1;
    //             if(aryi_car > 0) rowcount_item = rowcount_item +1;
    //             if(i == 0){
    //                 rowcount = rowcount_item;
    //             }else{
    //                 if(rowcount != rowcount_item){
    //                     is_user_level_die = 2;
    //                 }
    //             }
    //
    //
    //             if(nissan_car != ''){
    //                 if(dis_type == 1) {//折扣
    //                     if(aryi_car > nissan_car ){
    //                         is_user_level_die = 1;
    //                     }
    //                 }else{
    //                     if(aryi_car < nissan_car){
    //                         is_user_level_die =1
    //                     }
    //                 }
    //             }
    //
    //             if(no_car !=''){
    //                 if(dis_type == 1) {//折扣
    //                     if(aryi_car > no_car ){
    //                         is_user_level_die = 1;
    //                     }
    //                 }else{
    //                     if(aryi_car < no_car){
    //                         is_user_level_die =1
    //                     }
    //                 }
    //             }
    //
    //             if(no_car != '' && nissan_car !=''){
    //
    //                 if(dis_type == 1) {//折扣
    //                     if(nissan_car > no_car || aryi_car > nissan_car){
    //                         is_user_level_die = 1
    //                     }
    //                 }else{
    //                     if(nissan_car < no_car || nissan_car < nissan_car){
    //                         is_user_level_die =1
    //                     }
    //                 }
    //             }
    //         }else{
    //            var no_car = $(this).find(".tip_show_0").val()
    //            var nissan_car = $(this).find(".tip_show_1").val()
    //
    //            if(no_car != ''){
    //                if(dis_type == 1) {//折扣
    //                    if(nissan_car > no_car){
    //                        is_user_level_die = 1;
    //                    }
    //                }else{
    //                    if(nissan_car < no_car){
    //                        is_user_level_die =1
    //                    }
    //                }
    //            }
    //         }
    //     })
    //     if(is_user_level_die == 1){
    //         layer.msg('车主的优惠力度需大于等于非车主');
    //         return false;
    //     }
    //     if(is_user_level_die == 2){
    //         layer.msg('梯度要一致');
    //         return false;
    //     }
    // }


    var user_segment = $('input:radio[name=user_segment]:checked').val();
    var data=$.param({sku_list:sku_list,'user_segment':user_segment,'user_segment_options':user_segment_options,'up_down_channel_name':up_down_channel_name,'home':home,'v_dis_type':v_dis_type,'carv_dis_type':carv_dis_type,"activity_image":activity_image}) + '&' + form_data;
    Custom.ajaxPost(save_url,data,null,index_url);
});

$("[name='discount_type']").on('change', function (){
    discount_type=$(this).val();
    initComm(ajaxCommUrl);
})

$('body').on('change','.sku-ckeck',function(){
    if($(this).is(':checked')){
        var that_val = $(this).parents('tr')
        var comm_discount = that_val.find('.dis-select').val();
        if (dis_type == 1) {
            var sku_price = ((comm_discount / 10) * that_val.find("#td_price").text());

        } else if (dis_type == 2) {
            var sku_price = that_val.find("#td_price").text() - comm_discount;
        }
        that_val.find(".sku-price").val(Math.round(sku_price * 100) / 100);      //保留两位小数
    }
})

function selected_options(){
    return  $('input[name="user_segment_options"]:checked').data('show-key');
}

//更新会员等级
$('input[name="user_segment_options"]').click(function(i,v){
    var which_select = selected_options();

    $("#member_box .rule_new_box").find('.level_item').remove()

    addrow()

    for(var i = 0;i< 10 ; i++){
        $(".show_"+i).show()
    }
    for(var i = 0;i< which_select ; i++){
        $(".show_"+i).hide()
    }
    $("#member_box .rule_new_box").find('.v_dis_type').val('')
    $("#haved-commodtiy").empty();
    initComm(ajaxCommUrl);
})

function addrow(){
    var unit = units();
    var which_select = selected_options();
    var html = '<div class="level_item">'
        +'<div class="level_name_box" >'
        +'<div class="level_name_item_box" >活动梯度 <span class="level_box"></span></div>'
        +'<div class="level_name_item_box " ><a href="#" data-type="text" class="btn btn-primary btn-sm editable editable-click batch_price" data-value="" data-placeholder="批量设置" data-title="批量设置">批量设置<i class="fa fa-edit m-l-5"></i></a></div>'
        +'</div>'

    var all_box = $('input[name="user_segment_options"]').size();
    var left_box_count = all_box - which_select
    for(var i = 0 ;i<left_box_count;i++ ){
        html = html +'<div class="radio-inline stylelt"><input size="4" class="v_dis_type " onchange="commodity()" /><span class="c_dis_type">' + unit + '</span></div>'
    }

    html = html +'  <div class="btnc"><span class="adbtn">【 + 】</span></div>';
    $("#member_box .rule_new_box").append(html);
    $('.batch_price').editable({
        success: function (response, newValue) {
            $(this).parent().parent().parent().find(".v_dis_type").val(newValue)
            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^[0-9]*$/).test(value)) {
                return '格式有误';
            }
        }
    });
    //梯度名排序
    for(i=1;i<=$("#member_box .rule_new_box").find(".level_item").size();i++) {
        $("#member_box .rule_new_box").find(".level_item").find(".level_box").each(function (i, v) {
            $(this).empty().append(i + 1)
        })
    }
    //input 类排序
    $("#member_box .rule_new_box").find(".level_item").each(function(iki,vv){
        $(this).find(".v_dis_type").each(function (i, v) {
            var class_dis = 'dis_type_'+ ( iki + 1)
            $(this).removeClass("dis_type_*").addClass(class_dis).addClass("v_dis_type")
        })
    })
}

$("#all-sku-ckeck").on('click',function (){
    if($('input[name = all-sku-ckeck]').is(':checked')){
        $("input[name='sku-ckeck']:checkbox").each(function(k,v){
            $(this).prop("checked","true")
        })
    }else{
        $("input[name='sku-ckeck']:checkbox").each(function(k,v){
            $(this).removeAttr("checked")
        })
    }
})
// $('input[name="user_segment_options"]').click(function(i,v){
//     var which_select = selected_options();
//     $("#member_box .rule_new_box").find('.level_item:gt(0)').remove()
//     for(var i = 0;i< 10 ; i++){
//         $(".show_"+i).show()
//     }
//     for(var i = 0;i< which_select ; i++){
//         $(".show_"+i).hide()
//     }
//     $("#member_box .rule_new_box").find('.v_dis_type').val('')
//     $("#haved-commodtiy").empty();
//     initComm(ajaxCommUrl);
// })