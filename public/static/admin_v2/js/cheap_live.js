/**
 * Created by lyj on 2017/11/23.
 */
// $(function () {

    //初始化

    $(".default-select2").select2({width:'150px'});

    initComm(ajaxCommUrl);

    //操作类型切换
    $("[name='operation_type']").on('change',function(){
        $(this).closest('.with-border').find("[name='operation_type_data']").val('');
    });


    var comm_checked_id = [];		//商品id
    var comm_tab_tr = [];		//商品表单tr

    //搜索商品列表
    $("#comm-type-search").on('click',function(){
        var obj = $(this).parents('.form-group');
        var comm_parent_id = obj.find("select[name='comm_parent_id']").val();
        var sub_comm_type_id = obj.find("select[name='sub_comm_type_id']").val();
        var three_comm_type_id = obj.find("select[name='three_comm_type_id']").val();
        var commodity_name = obj.find("input[name='commodity_name']").val();
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+sub_comm_type_id
        +'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+commodity_name+'&commodity_class='+commodity_class;
        initComm(param);
    });

    $(".checkbox-inline").on('click',function (){
        var up_down_channel = '';
        $("input[name='up_down_channel[]']:checkbox:checked").each(function(){
            up_down_channel += $(this).val() + ',';
        })
        if(up_down_channel != ''){
            $("#dlrs").val(up_down_channel.substr(0,up_down_channel.length-1))
        }else{
            $("#dlrs").val('')
        }
        initComm(ajaxCommUrl)
    })


    function initComm(url,isSkuConfirmAll) {
        var dlrs =  $("#dlrs").val();
        var url1 = url;
        url=url+"&dlrs="+dlrs;
        if(dlrs != ''){
            $.getJSON(url, null, function (resData) {
                resData.data.list.isSkuConfirmAll = isSkuConfirmAll;
                createPageComm(10, 10, resData.data.list.total, url);//创建翻页功能按钮，翻
                $("#add-comm-tbody").empty();
                if (resData.data.list.total > 0) {                          //页向后台请求连接
                    setComm(url1,isSkuConfirmAll);
                }
            });
        }else{
            createPageComm(10, 10, 0, url);//创建翻页功能按钮，翻
            $("#add-comm-tbody").empty();
        }
    }

    function createPageComm(pageSize, buttons, total, url) {        //contracts_url为点击
        $("#comm-pagination").jBootstrapPage({      //功能按钮后需要访问action路径
            pageSize : pageSize,
            total : total,
            maxPageButton:buttons,
            onPageClicked: function(obj, page) {    //分页事件
                $("#add-comm-tbody").empty();
                setComm(url+"&page="+(page+1));
            }
        });
    }

    //获取并设置商品
    function setComm(param,isSkuConfirmAll = 0){
        var url = ajaxCommUrl;
        var dlrs =  $("#dlrs").val();
        if(param!=null) url=param+"&dlrs="+dlrs;

        $.get(url,function(res){
            var html='';
            var list = res.data.list.data;
            var comm_set_id_arr=gethavedCommodityId();
            //当前页是否已全选择
            var skuConfirmAllUnm = 0;
            //当前页
            var current_page = res.data.list.current_page;
            //当前分页条数
            var per_page = res.data.list.per_page;
            currentPageCommSetIds = [];
            $.each(list,function(i,val){
                currentPageCommSetIds.push(val.commodity_set_id)
                // var index = Number($.inArray(val.commodity_id,commodity_arrs));
                var index= Number($.inArray(val.commodity_set_id, comm_set_id_arr));
                if((isSkuConfirmAll > 0) && (val.is_limit != 1) && (val.is_pre != 1) && (val.is_seckill != 1) && (val.is_full != 1)){
                    index = 1;
                    //批量添加操作
                    skuConfirm(val)
                    skuConfirmAllUnm +=1;
                }

                if ( index>-1){
                    var button='<button data-comm-id="'+val.commodity_id+'" commodity_class="'+val.commodity_class+
                    '" home="'+val.home+'" comm_type_id="'+val.comm_type_id+
                    '" commodity_dlr_type_id="'+val.commodity_dlr_type_id+'"  data-comm-set-id="'+val.commodity_set_id+'" data-dd-commodity-type="'+val.dd_commodity_type+'" data-commodity-class="'+val.commodity_class+'"  class=" btn btn-default active  btn-fight" disabled>已添加</button>';
                }else{
                    if(val.is_pre==1){
                        var button='<button data-comm-id="'+val.commodity_id+'"'+
                             ' class=" btn btn-white active btn-sm " disabled >已参与其他预售</button>';
                    }else if(val.is_seckill == 1){
                        var button='<button data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与其他秒杀活动</button>';
                    }else{
                        var button='<button data-comm-id="'+val.commodity_id+'" commodity_class="'+val.commodity_class+
                        '" home="'+val.home+'" comm_type_id="'+val.comm_type_id+
                        '" data-comm-set-id="'+val.commodity_set_id+'"  data-dd-commodity-type="'+val.dd_commodity_type+'" commodity_dlr_type_id="'+val.commodity_dlr_type_id+'" data-commodity-class="'+val.commodity_class+'" class=" btn btn-white btn-sm  btn-fight">选择添加</button>';
                    }
                }

                html += '<tr class="t_'+val.commodity_set_id+'" commid="'+val.commodity_id+'" id="comm_tab_tr_'+val.commodity_id+'">' +
                    '<td class="text-left"><image class="cover-image" src="'+val.cover_image+'"><a  class="init-commodity-preview" data-dlr-code='+val.create_dlr_code+' data-commodity-id='+val.commodity_id+'>'+  val.commodity_name +'</a></td>' +
                    '<td class="text-center">'+ val.up_down_channel_name +'</td>' +
                    '<td class="text-center">'+ val.price +'</td>' +
                    '<td class="text-center">'+ val.count_stock +'</td>' +
                    '<td class="text-center">' + button +
                    '</td></tr>';
            });
            $("#add-comm-tbody").html(html);
            if(isSkuConfirmAll == 0 || !isSkuConfirmAll){
                skuConfirmAllUnm = getSkuConfirmAllUnm(comm_set_id_arr);
            }
            setSkuConfirmAllStatus(isSkuConfirmAll, skuConfirmAllUnm, current_page, per_page);

        },'json');
    }
    //添加商品
    $("#add-comm-tbody").on('click',".btn-fight",function (e) {

        e.preventDefault();
        var obj=$(this);
        var commtypeid = $(this).attr('comm_type_id');
        var home = $(this).attr('home');
        // var commodity_class = $(this).attr('commodity_class');
        var commodity_dlr_type_id = $(this).attr('commodity_dlr_type_id');
        var typeCount =  $("#haved-commodtiy").find("tr").size();
        if(typeCount >= 5){
            layer.msg('最多只能添加5个商品');
            return false;
        }
        var dis_type = $("select[name='dis_type']").val()
        if(dis_type != 1){
            if(dis_type == 2){
                var discount = $(".full_decrement input[name='discount']").val()
            }else if(dis_type == 3){
                var discount = $(".discount_price input[name='discount']").val()
            }else if(dis_type == 4){
                var discount = $(".reduction_price input[name='discount']").val()
            }else if(dis_type == 5){
                var discount = $(".regular_price input[name='discount']").val()
            }else{
                var discount = 0
            }
            if(discount == ''){
                layer.msg('一口价/满减/限时折扣/限时立减的价格不能为空');
                return false;
            }
        }

        var fhome = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
        // var fcommodity_class = $("#haved-commodtiy").find("tr:first-child").find(".commodity_class").val();

        // if(fhome != home){
        //     if(fhome == 1){
        //         layer.msg('只能添加“快递到家“的商品，该商品为“到店安装”');
        //         return false;
        //     }
        //     if(fhome == 0){
        //         layer.msg('只能添加“到店安装“的商品，该商品为“快递到家”');
        //         return false;
        //     }
        // }

        // if( (typeof(fcommodity_class) != "undefined") && (fcommodity_class != commodity_class)){
        //     layer.msg('活动商品只能选择相同类型;<br/>您已选择的是:'+commodity_class_type[fcommodity_class]);
        //     return false;
        // }

        $("#modal_home").val("");
        $("#modal_home").val(home);
        // $("#modal_commodity_class").val("");
        // $("#modal_commodity_class").val(commodity_class);

        $("#sku-modal").find("#comm-id").val("");
        $("#sku-modal").find("#comm-id").val($(this).data('comm-id'));

        if(oneCommodityDlrTypeId==0){
            oneCommodityDlrTypeId = commodity_dlr_type_id;
        }
        if(oneCommodityDlrTypeId != commodity_dlr_type_id){
            $("#sku-modal").modal('hide');
            var msg2 = "只能添加同种类型的商品，该商品为“"+commDlrTypeJson[oneCommodityDlrTypeId]+"“";
            layer.msg(msg2);
            return;
        }

        $.getJSON(getSkuUrl,{commodity_id:$(this).data('comm-id'),commodity_set_id:$(this).data('comm-set-id')},function (res) {
            var sku_list=res.data.sku_list;
            $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
            $(".sku-comm").html(res.data.commodity_row.commodity_name);
            $("#comm-name-header").html(res.data.commodity_row.commodity_name);
            $("#modal_com_type_id").val("");
            $("#modal_com_type_id").val(res.data.commodity_row.comm_type_id);

            var html='';
            $.each(sku_list,function(i,val){
                var sku_check='<input type="checkbox" checked class="sku-ckeck" value="6" name="">';
                var sku_price= '<div class="input-group " style="width: 100px;">'+
                    '<input  type="text" class="form-control sku-price input-sm" value="" data-parsley-required="true"  data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">'+
                    '<span class="input-group-addon">元</span> </div>';
                if(dis_type != 1){
                    $("#all_price_th").hide()
                    var input = '<input type="hidden" class="sku-price" value="'+val.price+'">';
                    html += '<tr set_sku_id="'+val.id+'">' +
                        '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                        '<td >'+ val.price + input +'</td>' +
                        '<td >' + val.stock +
                        '</td></tr>';
                }else{
                    $("#all_price_th").show()
                    html += '<tr set_sku_id="'+val.id+'">' +
                        '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                        '<td >'+ val.price +'</td>' +
                        '<td >'+sku_price+'</td>' +
                        '<td >' + val.stock +
                        '</td></tr>';

                }

            });
            $("#sku-modal").data("comm-id",obj.data('comm-id'));
            $("#sku-modal").data("comm-set-id",obj.data('comm-set-id'));
            $("#sku-modal").data('dd-commodity-type',obj.data('dd-commodity-type'));
            $("#sku-modal").data('type','add');
            $(".sku-tb").html(html);
            $("#sku-modal").modal('show');
            //dlr初始化
            $("#sku-modal").find(".sku-dlr").val("").data("sku-dlr","");
        });
    })


    //查看商品
    $("body").on("click",".btn-fight-view",function (e) {
        e.preventDefault();
        var set_sku_list=$(this).data('sku-list');
        // console.log(set_sku_list);
        var sku_price_list=set_sku_list.sku_list;
        var obj=$(this);
        var dis_type = $("select[name='dis_type']").val()
        $.getJSON(getSkuUrl,{commodity_id:set_sku_list.commodity_id,commodity_set_id:set_sku_list.commodity_set_id},function (res) {
            var sku_list=res.data.sku_list;
            $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
            $(".sku-comm").html(res.data.commodity_row.commodity_name);
            var html='';
            $.each(sku_list,function(i,val){
                var price='';
                var checked=''
                if(sku_price_list[val.id]){
                    price  =sku_price_list[val.id];
                    checked='checked';
                }
                var sku_check='<input type="checkbox" ' +checked+'  class="sku-ckeck" value="6" name="">';
                var sku_price= '<div class="input-group " style="width: 100px;">'+
                    '<input  type="text" class="form-control sku-price input-sm" value="'+price+'" data-parsley-required="true"  data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">'+
                    '<span class="input-group-addon">元</span> </div>';
                if(dis_type != 1){
                    $("#all_price_th").hide()
                    var input = '<input type="hidden" class="sku-price" value="'+val.price+'">';
                    html += '<tr set_sku_id="'+val.id+'">' +
                        '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                        '<td >'+ val.price + input +'</td>' +
                        '<td >' + val.stock +
                        '</td></tr>';
                }else{
                    $("#all_price_th").show()
                    html += '<tr set_sku_id="'+val.id+'">' +
                        '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                        '<td >'+ val.price +'</td>' +
                        '<td >'+sku_price+'</td>' +
                        '<td >' + val.stock +
                        '</td> </tr>';
                }


            });

            $("#sku-modal").data("comm-id",set_sku_list.commodity_id);
            $("#sku-modal").data("comm-set-id",set_sku_list.commodity_set_id);
            $("#comm-name-header").html(set_sku_list.commodity_name);
            var index=$(".btn-fight-view").index(obj);
            $("#sku-modal").data('type',index);
            $(".sku-tb").html(html);
            $("#sku-modal").modal('show');

            //dlr初始化
            $("#sku-modal").find(".sku-dlr").val(set_sku_list.dlr_name).data("sku-dlr",set_sku_list.dlr_code);
        });
    })


    //批量设置价格
    $('#all_price').editable({
        success: function(response, newValue) {
            $(".sku-price").val(newValue);

            $(".editable-cancel").click();
            return false;

        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^[0-9/.]*$/).test(value)) {
                return '格式有误';
            }
        }
    });

    $("#sku-confirm").on('click',function (e) {
        var sku_modal=$(this).parents("#sku-modal");
        var commodity_list =new Object();
        var com_type_id =  sku_modal.find("#modal_com_type_id").val();
        var home =  sku_modal.find("#modal_home").val();
        var commodity_class =  sku_modal.find("#modal_commodity_class").val();

        commodity_list.commodity_id    =sku_modal.data('comm-id');
        commodity_list.commodity_set_id=sku_modal.data('comm-set-id');
        commodity_list.commodity_class = commodity_class;
        commodity_list.dd_commodity_type = sku_modal.data('dd-commodity-type');
        // commodity_list.dlr_code        =sku_modal.find(".sku-dlr").data("sku-dlr");
        // commodity_list.dlr_name        =sku_modal.find(".sku-dlr").val();
        commodity_list.commodity_name  =sku_modal.find("#comm-name-header").html();
        var image=sku_modal.find(".cover-image").attr("src");
        var  commodity_name=sku_modal.find(".sku-comm").html();
        var sku_list={};
        var check_price=true;
        var dis_type = $("select[name='dis_type']").val()
        $(".sku-tb tr").each(function () {
            if($(this).find(".sku-ckeck").attr("checked")){
                var set_sku_id=$(this).attr("set_sku_id");
                var reg=/^0\.([1-9]|\d[0-9])$|^[1-9]\d{0,8}\.\d{0,2}$|^[1-9]\d{0,8}$/
                if(!reg.test($(this).find(".sku-price").val())){
                    layer.msg('价格格式不对');
                    check_price=false;
                    return false;
                }else{
                    sku_list[set_sku_id]=$(this).find(".sku-price").val();
                }
            }
        })
        if (!check_price){
            return;
        }
        if(JSON.stringify(sku_list) == "{}"){
            layer.msg('请选择规格');
            return;
        }
        //if ((!$(".sku-dlr").val() || !commodity_list.dlr_code) && admin_type==1){
        //   layer.msg('请选择经销商');
        //  return ;
        // }

        commodity_list.sku_list=sku_list;
        //新增数据添加到后面
        var json_st=JSON.stringify(commodity_list);
        var type   =sku_modal.data('type');
        var comm_id = $("#comm-id").val();
        var inputstr = comm_id +":";
        $(".sku-tb").find("tr").each(function(i,v){
            var sku_val = $(this).attr("set_sku_id");
            var price =  $(this).find(".sku-price").val();
            inputstr = inputstr + sku_val +'=' + price+',';
        })

        //添加
        if (type=='add'){
            var html='<tr id="t_'+commodity_list.commodity_set_id+'" set_id="'+commodity_list.commodity_set_id+'" class="info haved-add"><td style="width: 350px;"><input type="hidden" class="com_type_id" value="'+com_type_id+'"/>' +
                '<input type="hidden" name="home"  class="home" value="'+home+'" /><input type="hidden" name="commodity_class"  class="commodity_class" value="'+commodity_class+'"/>'+
                '<img class="cover-image" src="'+image+'">'+commodity_name+'</td><td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list">删除</button></td></tr>';
            $("#haved-commodtiy").append(html);
            commodity_select()
        }else {  //查看
            var html='<td style="width: 350px;"><img class="cover-image" src="'+image+'">'+commodity_name+'</td><td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list ">删除</button></td>';
            $(".btn-fight-view").eq(type).parents('tr').html(html);
        }

        //专营店 禁用添加按钮

        var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+commodity_list.commodity_set_id+"']");
        btn_obj.removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
        //btn_obj.parents("button").html('已添加');

        $("#sku-modal").modal('hide');
        // console.log(commodity_list);
    })

    /**********删除sku************/
    $("body").on("click",".del-sku-list",function (e) {
        e.preventDefault();
        var sku_list=$(this).parents("tr").find(".btn-fight-view").data("sku-list");
        var type=$(".del-sku-list").index($(this));
        $("#del-sku-modal").find("#del-data-id").val(type).data('comm-set-id',sku_list.commodity_set_id);
        $("#del-sku-modal").modal('show');
    })

    $("#del-confirm").on("click",function () {
        $(".del-sku-list").eq($("#del-sku-modal").find("#del-data-id").val()).parents('tr').remove();
        $("#del-sku-modal").modal('hide');
        //专营店端修改添加按钮

        var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+$("#del-sku-modal").find("#del-data-id").data('comm-set-id')+"']");
        btn_obj.removeClass("btn-default active").addClass("btn-white").attr("disabled",false).html('选择添加');
        $.each(comm_set_id_arr,function(index,item){
            if(item == $("#del-sku-modal").find("#del-data-id").data('comm-set-id')){
                comm_set_id_arr.splice(index,1)
            }
        })
        // console.log('comm_set_id_arr',comm_set_id_arr)
    })

    /**********删除sku************/
    //遍历已添加数据,获取专营店
    function getdlr(commodity_set_id) {
        // console.log(commodity_set_id);
        var dl_code_list=[]
        $('#haved-commodtiy tr').each(function () {
            var sku_list=$(this).find('.btn-fight-view').data('sku-list');
            // console.log(sku_list);
            if(commodity_set_id==sku_list.commodity_set_id){
                dl_code_list= dl_code_list.concat(sku_list.dlr_code.split(','));
            }
        })
        // console.log(dl_code_list);
        return dl_code_list;
    }


    $(".datetimepicker3").datetimepicker({
        format:"YYYY-MM-DD HH:mm:00",
        locale: moment.locale('zh-cn'),
    });

    /* 专营店端获取添加id*/
    function gethavedCommodityId() {
        var comm_set_id_arr=[];
        $("#haved-commodtiy tr").each(function () {
            if ($(this).hasClass("haved-add")){
                var sku_list=$(this).find(".btn-fight-view").data("sku-list");
                comm_set_id_arr.push(sku_list.commodity_set_id);
            }
        })
        return comm_set_id_arr;
    }

    //获取并设置商品分类
    function setCommType(param){
        var url = ajaxCommTypeUrl;
        if(param!=null) url=param;
        var data_id = $('#addCommTypeModal').find("[name='text_click_id']").attr('data-id');
        $.get(url,function(res){
            var html='';
            var list = res.data.data;
            $.each(list,function(i,val){
                var checkbox_obj = '';
                if(val.id==data_id) {
                    checkbox_obj = '<label> <input type="checkbox" class="commodity_checkbox" data-id="' + val.id +'" data-name="'+val.comm_type_name+'" data-parent-name="'+val.comm_parent_name+'" value="" name="" checked>选择 </label>';
                }else{
                    checkbox_obj = '<label> <input type="checkbox" class="commodity_checkbox" data-id="' + val.id + '" data-name="'+val.comm_type_name+'" data-parent-name="'+val.comm_parent_name+'" value="" name="">选择 </label>';
                }
                html += '<tr id="comm_tab_tr_'+val.id+'">' +
                    '<td class="text-center">'+ val.comm_type_name +'</td>' +
                    '<td class="text-center">' + checkbox_obj +
                    '</td> </tr>';
            });
            $("#add-comm-type-tbody").html(html);

        },'json');
    }

    //搜索商品分类列表
    /* $("#comm-type-search").on('click',function(){
     var obj = $(this).parent();
     var comm_parent_id = obj.find("select[name='comm_parent_id']").val();
     var comm_type_name = obj.find("input[name='comm_type_name']").val();
     var param = ajaxCommTypeUrl + '&comm_parent_id='+comm_parent_id+'&comm_type_name='+comm_type_name;
     initCommType(param);
     });*/

    //选择商品-取消
    $("#add-comm-no").on('click',function(){
        $("#addCommModal").click();
    });
    /*------------------- end 添加商品、分类 -----------------------*/

    function getSkuList() {
        var sku_list=[];
        $("#haved-commodtiy tr").each(function () {
            //alert($(this).find(".btn-fight-view").data("sku-list"));
            sku_list.push($(this).find(".btn-fight-view").data("sku-list"));
        })
        return sku_list;
    }

    $("#put-form").on('click',function(){

        var $form=$("#fight-form");

        // dealer_select 有hidden, 就清空 dlr_hide 的value
        if($("#dealer_select").hasClass("hidden")){
            $("#dlr_hide").attr('value','');
        }else{
            var dlr_value = $("#dlrs").val();
            //遍历 checkbox-inline, 把没有勾选的数据从dlr_hide 中去除
            $(".checkbox-inline input:not(:checked)").each(function(i,v){
                var reg = new RegExp(this.value,"g");//g,表示全部替换。
                dlr_value = dlr_value.replace(reg,"");
            })
            $("#dlr_hide").attr('value',dlr_value);
        }
        var dis_type = $("select[name='dis_type']").val()
        var up_down_channel_name = getUpDownChannel();
        var full_discount_info = {};
        if(dis_type == 2 ){
            var gd = get_data();
            if(gd==false){
                return false;
            }
            full_discount_info =  JSON.stringify(gd);
        }
        var form_data=$form.serialize();
        var home = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
        var commodity_class = $("#haved-commodtiy").find("tr:first-child").find(".commodity_class").val();
        var sku_list = getSkuList();
        if(sku_list.length <=1){
            layer.msg('选择的商品不能少于于1个', {icon: 2,time:3000});
            return false;
        }
        // 活动图片
        var activity_image=[];
        $(".activity_image_group .goods_pic").each(function(){
            $(this).find('li img').each(function(){
                activity_image.push($(this).attr("image-value"));
            });
        });
        
        var data=$.param({sku_list:JSON.stringify(sku_list),'up_down_channel_name':up_down_channel_name,'home':home,'commodity_class_live':commodity_class,full_discount_rules:full_discount_info,'activity_image':activity_image}) + '&' + form_data;

        Custom.ajaxPost(save_url,data,null,index_url);
    });



// })

/**
 * 活动商品列表搜索
 * @param isSkuConfirmAll
 * @returns {boolean}
 */
function commTypeSearch(isSkuConfirmAll = 0) {
    var comm_parent_id = $("select[name='comm_parent_id']").val();
    var sub_comm_type_id = $("select[name='sub_comm_type_id']").val();
    var three_comm_type_id = $("select[name='three_comm_type_id']").val();
    var commodity_name = $("input[name='commodity_name']").val();
    var commodity_class = $("select[name='commodity_class']").val();

    var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+commodity_name+'&commodity_class='+commodity_class;

    //额外参数验证
    if(typeof(isInitComm) != "undefined") {
        if(isInitComm == 0){
            return false;
        }
    }
    //console.log(param);

    initComm(param, isSkuConfirmAll);

}

function gethavedCommodityId() {
    var comm_set_id_arr=[];
    $("#haved-commodtiy tr").each(function () {
        var sku_list=$(this).find(".btn-sm").data("sku-list");
        comm_set_id_arr.push(sku_list.commodity_set_id);
    });
    return comm_set_id_arr;
}

/**
 * 批量添加操作
 * @param sku_modal
 * @param type
 */
function skuConfirm(sku_modal, type = 'add'){
    // console.log('skuConfirm')
    // console.log('sku_modal:',sku_modal);
    var home =  sku_modal.home;
    var commodity_class =  sku_modal.commodity_class;

    var commodity_list =new Object();
    commodity_list.commodity_id    =sku_modal.commodity_id;
    commodity_list.commodity_set_id=sku_modal.commodity_set_id;
    commodity_list.dlr_code        =sku_modal.dlr_code;
    commodity_list.dlr_name        =sku_modal.dlr_name;
    commodity_list.commodity_name  =sku_modal.commodity_name;
    commodity_list.sku_list        = {};

    var image= sku_modal.cover_image;
    var  commodity_name=sku_modal.commodity_name;
    //新增数据添加到后面
    // console.log('type:',type)
    // console.log('sku_modal:',sku_modal)
    //添加
    if (type=='add'){
        //已经添加的直接跳过
        var index = $.inArray(commodity_list.commodity_set_id, comm_set_id_arr);
        if(index > -1){
            $("#sku-modal").modal('hide');
            return;
        }
        var golab_is_segment_key = 0;
        comm_set_id_arr.push(commodity_list.commodity_set_id);
        $.getJSON(getSkuUrl,{commodity_id:sku_modal.commodity_id,commodity_set_id:sku_modal.commodity_set_id},function (res) {
            var skus = res.data.sku_list;
            var commodity_row = res.data.commodity_row;
            $.each(skus,function(i,val){
                commodity_list.sku_list[val.set_sku_id] = val.price;
            })

            var json_st=JSON.stringify(commodity_list);
            //将已添加的商品入栈
            var html='<tr class="info" id="t_'+commodity_list.commodity_set_id+'" set_id="'+commodity_list.commodity_set_id+'">' +
                '<td style="width: 350px;">' +
                '<input type="hidden" name="home"  class="home" value="'+home+'"/><input type="hidden" '+
                'name="commodity_class"  class="commodity_class" value="'+commodity_class+'"/><img class="cover-image" '+
                ' src="'+image+'">'+commodity_name+'' +
                '</td>' +
                '<td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+' data-is_segment_key="'+golab_is_segment_key+'" class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list" data-comm_name="'+commodity_name+'">删除</button>' +
                '</td>' +
                '</tr>';
            $("#haved-commodtiy").append(html);
            commodity_select()
        });
    }

    //选择添加完毕后，禁用添加按钮
    var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+commodity_list.commodity_set_id+"']");
    btn_obj.removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
    btn_obj.parents("button").html('已添加');
    $("#sku-modal").modal('hide');

    // console.log("add:comm_set_id_arr = "+comm_set_id_arr);
}


/**
 * 设置批量添加操作按钮
 * @param isSkuConfirmAll
 * @param skuConfirmAllUnm
 * @param per_page
 */
function setSkuConfirmAllStatus(isSkuConfirmAll, skuConfirmAllUnm, current_page, per_page){
    current_page = parseInt(current_page);
    var obj = $(".comm-type-search").parents('.form-group');
    var commodity_class = obj.find("select[name='commodity_class']").val();
    var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();
    var pageAllStr = current_page +'x'+ commodity_class +'v'+ commodity_dlr_type_id;

    //增加批量添加按钮禁用操作
    if(skuConfirmAllUnm == per_page){
        // $("#sku-confirm-all")
        //     .removeClass("btn-white")
        //     .addClass("btn-default active")
        //     .html('已全部添加');
        //$("#sku-confirm-all").data("is-sku-confirm-all", 0);
        skuConfirmAllCurrentPage.push(pageAllStr);
    }else if((isSkuConfirmAll > 0) && (skuConfirmAllUnm != per_page)){
        $("#sku-confirm-all")
            .removeClass("btn-default active")
            .addClass("btn-white")
            .html('批量添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 1);
        skuConfirmAllCurrentPage.splice(skuConfirmAllCurrentPage.indexOf(pageAllStr), 1);
    }else if((isSkuConfirmAll == 0) && (skuConfirmAllCurrentPage.indexOf(pageAllStr) != '-1')){
        $("#sku-confirm-all")
            .removeClass("btn-white")
            .addClass("btn-default active")
            .html('已全部添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 0);
    }else {
        $("#sku-confirm-all")
            .removeClass("btn-default active")
            .addClass("btn-white")
            .html('批量添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 1);
    }

    $("#sku-confirm-all").data("current-page", current_page);

    //批量操作的时候，需要重新选中当前批量选中操作
    if(isSkuConfirmAll > 0){
        //清除当前选中分页
        $("#comm-pagination li").removeClass('active');
        //选中当前页面
        $("#comm-pagination").find('[pnum="'+current_page+'"]').addClass("active");
    }

}

/**
 * 获取当前页已选中的商品数量
 * @param comm_set_id_arr
 * @returns {number}
 */
function getSkuConfirmAllUnm(comm_set_id_arr){
    var num = 0;
    for (var i=0; i < currentPageCommSetIds.length; i++){
        var index = $.inArray(currentPageCommSetIds[i], comm_set_id_arr);
        if(index > -1){
            num ++;
        }
    }
    return num;
}