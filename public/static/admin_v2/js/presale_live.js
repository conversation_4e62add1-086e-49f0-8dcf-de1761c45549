


    initComm(ajaxCommUrl);

    //fight_group
    if($("input:radio:checked").val() == 1){
        $("#coupon").css('display','block')
    }else{
        $("#coupon").css('display','none')
    }
    //选择使用优惠券显示优惠券和不显示优惠券
    $(".is_coupon").on('click',function(){
        const coupon = $('input:radio:checked').val();
        if(coupon == 1){
            $("#coupon").css('display','block')
        }else{
            $("#coupon").css('display','none')
        }
    })

    //请求优惠券接口
    $("#coupon_name").on('click',function (){
        $("#coupon_list").modal('show');
        $.getJSON(ajaxCouponUrl, null, function (resData) {
            createPageComm(5, 10, resData.data.total, ajaxCouponUrl);//创建翻页功能按钮，翻
            $("#pagination").empty();
            if (resData.data.total > 0) {                          //页向后台请求连接
                setComm(ajaxCouponUrl);
            }
        });
    })
    //优惠券分页
    function createPageComm(pageSize, buttons, total, url)
    {        //contracts_url为点击
        $("#comm-pagination").jBootstrapPage({      //功能按钮后需要访问action路径
            pageSize : pageSize,
            total : total,
            maxPageButton:buttons,
            onPageClicked: function(obj, page) {    //分页事件
                $("#add-coupon-tbody").empty();
                setComm(url+"&page="+(page+1));
            }
        });
    }

    function createPageComms(pageSize, buttons, total, url)
    {        //contracts_url为点击
        $("#comm-paginations").jBootstrapPage({      //功能按钮后需要访问action路径
            pageSize : pageSize,
            total : total,
            maxPageButton:buttons,
            onPageClicked: function(obj, page) {    //分页事件
                $("#add-comm-tbody").empty();
                setComms(url+"&page="+(page+1));
            }
        });
    }

    //html显示优惠券
    function setComm(param)
    {
        if(param!=null) ajaxCouponUrl=param;
        $.get(ajaxCouponUrl,function(res){
            console.log(res)
            let html = '';
            let list = res.data.data;
            $.each(list,function(i,val){
                html += '<tr>'
                html += '<td class="text-center">'+val.card_name+'</td>'
                html += '<td class="text-center">'+val.card_id+'</td>'
                html += '<td class="text-center">'+val.card_type_name+'</td>'
                html += '<td class="text-center">'+val.validity_date+'</td>'
                if(coupon_id == val.id){
                    html += '<td class="text-center"><input type="radio" checked data-coupon-name="'+val.card_name+'" name="coupon_id" value="'+val.id+'"></td>'
                }else{
                    html += '<td class="text-center"><input type="radio" data-coupon-name="'+val.card_name+'" name="coupon_id" value="'+val.id+'"></td>'
                }
                html += '</tr>'
            });
            $("#coupon-pagination").html(html);

        },'json');
    }

    //选择优惠券点击确认显示
    $("#coupon-confirm").on('click',function (){
        var coupon_name = $('input:radio[name="coupon_id"]:checked').attr('data-coupon-name')
        var id = $('input:radio[name="coupon_id"]:checked').val()
        coupon_id = id
        $("#coupon_name").val(coupon_name)
        $("#coupon_id").val(id)
        $("#coupon_list").modal('hide');

    })


    function initComm(url, isSkuConfirmAll = 0) {
        $.getJSON(url, null, function (resData) {
            resData.data.list.isSkuConfirmAll = isSkuConfirmAll;
            createPageComms(10, 10, resData.data.list.total, url);//创建翻页功能按钮，翻
            $("#add-comm-tbody").empty();
            if (resData.data.list.total > 0) {                          //页向后台请求连接
                setComms(url, isSkuConfirmAll);
            }
        });
    }
   $("#comm-type-search").on('click',function(){
       var obj = $(this).parent();
       var objs = $(this).parents('.form-group');
       var comm_parent_id = $("#slt_comm_type_id").val();
       var sub_comm_type_id = $("#slt_sub_comm_type_id").val();
       var three_comm_type_id = $("#slt_three_comm_type_id").val();
       var commodity_name = $("input[name='commodity_name']").val();
       console.log(commodity_name)
       var commodity_class = objs.find("select[name='commodity_class']").val();
       var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+sub_comm_type_id+'' +
           '&three_comm_type_id='+three_comm_type_id+'&commodity_name='+commodity_name+'&commodity_class='+commodity_class;

       //额外参数验证
       if(typeof(isInitComm) != "undefined") {
           if(isInitComm == 0){
               return false;
           }
       }

       initComm(param);
   });

    /**
     * 活动商品列表搜索
     * @param isSkuConfirmAll
     * @returns {boolean}
     */
    function commTypeSearch(isSkuConfirmAll = 0) {
        var comm_parent_id = $("select[name='comm_parent_id']").val();
        var sub_comm_type_id = $("select[name='sub_comm_type_id']").val();
        var three_comm_type_id = $("select[name='three_comm_type_id']").val();
        var commodity_name = $("input[name='commodity_name']").val();
        var commodity_class = $("select[name='commodity_class']").val();

        var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+commodity_name+'&commodity_class='+commodity_class;

        //额外参数验证
        if(typeof(isInitComm) != "undefined") {
            if(isInitComm == 0){
                return false;
            }
        }
        console.log(param);

        initComm(param, isSkuConfirmAll);

    }

    function setComms(param, isSkuConfirmAll = 0){
        var url = ajaxCommUrl;
        if(param!=null) url=param;

        $.get(url,function(res){
            var html='';
            var resData = $.parseJSON(res).data;
            var list = resData.list.data;
            var comm_set_id_arr = gethavedCommodityId();

            //当前页是否已全选择
            var skuConfirmAllUnm = 0;
            //当前页
            var current_page = resData.list.current_page;
            //当前分页条数
            var per_page = resData.list.per_page;
            //重置当前页所有的comm_set_id值
            currentPageCommSetIds = [];

            $.each(list,function(i,val){
                currentPageCommSetIds.push(val.commodity_set_id)

                var index = Number($.inArray(val.commodity_set_id, comm_set_id_arr));
                if((isSkuConfirmAll > 0)
                    && (val.is_pre != 1)
                    && (val.is_fight != 1)
                    && (val.is_limit != 1)
                    && (val.is_n_discount != 1)
                    && (val.is_full != 1)
                    && (val.is_cheap != 1)
                    && (val.is_seckill != 1)
                ){
                    index = 1;
                    //批量添加操作
                    skuConfirm(val)
                    skuConfirmAllUnm +=1;
                }

                if (index>-1){
                    var button='<button data-per-page="'+per_page+'"  data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                    '"data-dlr-code="'+val.dlr_code+'"  class=" btn btn-default active btn-sm btn-fight" disabled>已添加</button>';
                }else{
                    //预售 团购 限折 NN 满优 套装
                    if(val.is_pre == 1){
                        var button='<button data-per-page="'+per_page+'"  data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                                '"data-dlr-code="'+val.dlr_code+'"  class=" btn btn-white active btn-sm " disabled >已参与其他预售</button>';
                    }else if(val.is_fight==1){
                        var button='<button data-per-page="'+per_page+'"  data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                                '"data-dlr-code="'+val.dlr_code+'"  class=" btn btn-white active btn-sm " disabled >已参与团购</button>';
                    }else if(val.is_limit==1){
                        var button='<button data-per-page="'+per_page+'"  data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                                '"data-dlr-code="'+val.dlr_code+'"  class=" btn btn-white active btn-sm " disabled >已参与限时折扣</button>';
                    }else if(val.is_n_discount ==1){
                        var button='<button data-per-page="'+per_page+'"  data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                                '"data-dlr-code="'+val.dlr_code+'"  class=" btn btn-white active btn-sm " disabled >已参与N件N折</button>';
                    }else if(val.is_full ==1){
                        var button='<button data-per-page="'+per_page+'"  data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                                 '"data-dlr-code="'+val.dlr_code+'"  class=" btn btn-white active btn-sm " disabled >已参与满优惠</button>';
                    }else if(val.is_cheap == 1){
                        var button='<button data-per-page="'+per_page+'"  data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                                 '"data-dlr-code="'+val.dlr_code+'"  class=" btn btn-white active btn-sm " disabled >已参与优惠套装</button>';
                    }else if(val.is_seckill == 1){
                        var button='<button data-per-page="'+per_page+'"  data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                            '"data-dlr-code="'+val.dlr_code+'"  class=" btn btn-white active btn-sm " disabled >已参与秒杀活动</button>';
                    } else {
                        var button='<button ' +
                            'data-per-page="'+per_page+'"  ' +
                            'data-comm-id="'+val.commodity_id+'" ' +
                            'home="'+val.home+'" ' +
                            'data-comm-set-id="'+val.commodity_set_id+'"' +
                            ' data-dlr-code="'+val.dlr_code+'" ' +
                            'commodity_dlr_type_id="'+val.commodity_dlr_type_id+'"' +
                            ' commodity_class="'+val.commodity_class+'" ' +
                            'class=" btn btn-white btn-sm  btn-fight">选择添加</button>';
                    }
                }

                html += '<tr id="comm_tab_tr_'+val.commodity_id+'">' +
                    '<td class="text-left"><image class="cover-image" src="'+val.cover_image+'">'+
                    '<a class="init-commodity-preview" data-dlr-code="'+val.dlr_code+'" data-commodity-id="'+val.commodity_id+'">'+val.commodity_name+'</a></td>'+
                    '<td class="text-center">'+ val.up_down_channel_name +'</td>' +
                    '<td class="text-center">'+ val.price +'</td>' +
                    '<td class="text-center">'+ val.count_stock +'</td>' +
                    '<td class="text-center">' + button +
                    '</td> </tr>';
            });
            $("#add-comm-tbody").html(html);

            if(isSkuConfirmAll == 0 || !isSkuConfirmAll){
                skuConfirmAllUnm = getSkuConfirmAllUnm(comm_set_id_arr);
            }
            //增加批量添加按钮禁用操作
            setSkuConfirmAllStatus(isSkuConfirmAll, skuConfirmAllUnm, current_page, per_page);

        })

    }

    //添加商品
    $("#add-comm-tbody").on('click',".btn-fight",function (e) {
        e.preventDefault();
        var obj=$(this);
        var home = $(this).attr('home');
        var commodity_class = $(this).attr('commodity_class');
        var commodity_dlr_type_id = $(this).attr('commodity_dlr_type_id');

        var fhome = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
        var fcommodity_class = $("#haved-commodtiy").find("tr:first-child").find(".commodity_class").val();

        if(fhome != home){
            if(fhome == 1){
//                layer.msg('只能添加“快递到家“的商品，该商品为“到店安装”');
//                return false;
            }
            if(fhome == 0){
//                layer.msg('只能添加“到店安装“的商品，该商品为“快递到家”');
//                return false;
            }
        }

        $("#modal_home").val("");
        $("#modal_home").val(home);
        $("#modal_commodity_class").val(commodity_class);
        $("#commodity_dlr_type_id").val(commodity_dlr_type_id);

        var deposit = $('input[name="deposit"]').val();
        var deduction = $('input[name="deduction"]').val();
        $.getJSON(getSkuUrl,{commodity_id:$(this).data('comm-id'),commodity_set_id:$(this).data('comm-set-id')},function (res) {
            var sku_list=res.data.sku_list;
            $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
            $(".sku-comm").html(res.data.commodity_row.commodity_name);
            $("#comm-name-header").html(res.data.commodity_row.commodity_name);
            var html='';
            $.each(sku_list,function(i,val){
                var sku_check='<input type="checkbox" style="display: none" checked class="sku-ckeck" value="6" name="">';
                var new_set_money = (val.price-deduction).toFixed(2);
                var sku_price= '<input type="hidden" class="sku-ckeck sku-price" value="'+(new_set_money)+'" name="sku_id_price">'+(new_set_money);
                html += '<tr set_sku_id="'+val.id+'">' +
                    '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                    '<td id="td_price" class="text-center">'+ val.price +'</td>' +
                    '<td class="text-center">'+sku_price+'</td>' +
                    '<td class="text-center">' + val.stock +
                    '</td> </tr>';
            });

            $("#sku-modal").data("per-page",obj.data('per-page'));
            $("#sku-modal").data("commodity-class",obj.data('commodity-class'));
            $("#sku-modal").data("comm-id",obj.data('comm-id'));
            $("#sku-modal").data("comm-id",obj.data('comm-id'));
            $("#sku-modal").data("comm-set-id",obj.data('comm-set-id'));
            $("#sku-modal").data('type','add');
            $(".sku-tb").html(html);
            $("#sku-modal").modal('show');
            //dlr初始化
            $("#sku-modal").find(".sku-dlr").val("").data("sku-dlr","");
        });
    })

    //sku 点击确认
    $("#sku-confirm").on('click',function (e) {
        var sku_modal=$(this).parents("#sku-modal");
        var per_page = sku_modal.data('per-page');
        var commodity_class =  sku_modal.find("#modal_commodity_class").val();
        var commodity_dlr_type_id =  sku_modal.find("#commodity_dlr_type_id").val();


        var commodity_list =new Object();
        var home =  sku_modal.find("#modal_home").val();
        commodity_list.commodity_id    =sku_modal.data('comm-id');
        commodity_list.commodity_set_id=sku_modal.data('comm-set-id');
        commodity_list.commodity_name  =sku_modal.find("#comm-name-header").html();

        var image=sku_modal.find(".cover-image").attr("src");
        var  commodity_name=sku_modal.find(".sku-comm").html();
        var sku_list={};
        $(".sku-tb tr").each(function () {
            if($(this).find(".sku-ckeck").attr("checked")){
                var set_sku_id=$(this).attr("set_sku_id");
                sku_list[set_sku_id]=$(this).find(".sku-price").val();
            }
        })
        commodity_list.sku_list=sku_list;
        console.log(sku_list)
        //新增数据添加到后面
        var json_st=JSON.stringify(commodity_list);
        var type   =sku_modal.data('type');
        //添加
        if (type=='add'){
            if(oneCommodityClass==0){
                oneCommodityClass = commodity_class;
            }
            if(oneCommodityClass != commodity_class){
                $("#sku-modal").modal('hide');
                var msg1 = "只能添加“"+commodityClassJson[oneCommodityClass]+"“的商品，该商品为“"+commodityClassJson[commodity_class]+"”";
                layer.msg(msg1);
                return;
            }

            if(oneCommodityDlrTypeId==0){
                oneCommodityDlrTypeId = commodity_dlr_type_id;
            }
            if(oneCommodityDlrTypeId != commodity_dlr_type_id){
                $("#sku-modal").modal('hide');
                var msg2 = "只能添加同种类型的商品，该商品为“"+commDlrTypeJson[oneCommodityDlrTypeId]+"“";
                layer.msg(msg2);
                return;
            }

            //已经添加的直接跳过
            var index = $.inArray(commodity_list.commodity_set_id, comm_set_id_arr);
            if(index > -1){
                $("#sku-modal").modal('hide');
                return;
            }

            //将已添加的商品入栈
            comm_set_id_arr.push(commodity_list.commodity_set_id);
            var html='<tr class="info haved-add" data><td style="width: 350px;"><input type="hidden" name="home"  class="home" value="'+home+'"/><img class="cover-image" src="'+image+'">'+
                '<a class="init-commodity-preview" data-commodity-id="'+commodity_list.commodity_id+'">'+commodity_list.commodity_name+'</a></td><td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list">删除</button></td></tr>';
            //alert(html);
            $("#haved-commodtiy").append(html);

            var delConfirmPage = $("#sku-confirm-all").data("current-page");
            //如果没读取到值，就不操作批量添加
            if(delConfirmPage){
                var skuConfirmAllUnm = getSkuConfirmAllUnm(comm_set_id_arr);
                //设置批量添加操作按钮
                setSkuConfirmAllStatus(1, skuConfirmAllUnm, delConfirmPage, per_page);
            }
            commodity_select()
        }else {  //查看
            //
            dlr_code='NISSAN';
            var html='<td style="width: 350px;"><img class="cover-image" src="'+image+'">'+
                '<a class="init-commodity-preview" data-dlr-code="'+dlr_code+'" data-commodity-id="'+commodity_list.commodity_id+'">'+commodity_name+'</td><td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list ">删除</button></td>';
            $(".btn-fight-view").eq(type).parents('tr').html(html);
        }

        //专营店 禁用添加按钮
        // if(1){
        var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+commodity_list.commodity_set_id+"']");
        btn_obj.removeClass("btn-white").addClass("btn-default active btn-sm").attr("disabled",true).html('已添加');
        //btn_obj.parents("button").html('已添加');

        // }

        $("#sku-modal").modal('hide');
        console.log(commodity_list);

    })


/**
 * 获取当前页已选中的商品数量
 * @param comm_set_id_arr
 * @returns {number}
 */
function getSkuConfirmAllUnm(comm_set_id_arr){
    var num = 0;
    for (var i=0; i < currentPageCommSetIds.length; i++){
        var index = $.inArray(currentPageCommSetIds[i], comm_set_id_arr);
        if(index > -1){
            num ++;
        }
    }
    return num;
}

/**
 * 批量添加操作
 * @param sku_modal
 * @param type
 */
function skuConfirm(sku_modal, type = 'add'){
    var home =  sku_modal.home;
    var commodity_class =  sku_modal.commodity_class;

    var commodity_list =new Object();

    commodity_list.commodity_id    =sku_modal.commodity_id;
    commodity_list.commodity_set_id=sku_modal.commodity_set_id;
    commodity_list.dlr_code        =sku_modal.dlr_code;
    commodity_list.dlr_name        =sku_modal.dlr_name;
    commodity_list.commodity_name  =sku_modal.commodity_name;
    var image= sku_modal.cover_image;
    var  commodity_name=sku_modal.commodity_name;
    if ((!$(".sku-dlr").val() || !commodity_list.dlr_code) && $("input[nam='set_type']") == 1){
        layer.msg('请选择经销商');
        return ;
    }
    //新增数据添加到后面
    var json_st=JSON.stringify(commodity_list);

    //添加
    if (type=='add'){
        //已经添加的直接跳过
        var index = $.inArray(commodity_list.commodity_set_id, comm_set_id_arr);
        if(index > -1){
            $("#sku-modal").modal('hide');
            return;
        }

        //将已添加的商品入栈
        comm_set_id_arr.push(commodity_list.commodity_set_id);
        var html='<tr class="info haved-add" data><td style="width: 350px;"><input type="hidden" name="home"  class="home" value="'+home+'"/><img class="cover-image" src="'+image+'">'+
            '<a class="init-commodity-preview" data-commodity-id="'+commodity_list.commodity_id+'">'+commodity_list.commodity_name+'</a></td><td class="text-right">'+
            "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
            '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list">删除</button></td></tr>';
        //alert(html);
        $("#haved-commodtiy").append(html);
        commodity_select()
    }else {

    }

    //选择添加完毕后，禁用添加按钮
    var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+commodity_list.commodity_set_id+"']");
    btn_obj.removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
    btn_obj.parents("button").html('已添加');
    $("#sku-modal").modal('hide');

    console.log("add:comm_set_id_arr = "+comm_set_id_arr);
}

/**
 * 设置批量添加操作按钮
 * @param isSkuConfirmAll
 * @param skuConfirmAllUnm
 * @param per_page
 */
function setSkuConfirmAllStatus(isSkuConfirmAll, skuConfirmAllUnm, current_page, per_page){
    current_page = parseInt(current_page);
    //oneCommodityClass  oneCommodityDlrTypeId
    var obj = $(".comm-type-search2").parents('.form-group');
    var commodity_class = obj.find("select[name='commodity_class']").val();
    var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();
    var pageAllStr = current_page +'x'+ commodity_class +'v'+ commodity_dlr_type_id;

    //增加批量添加按钮禁用操作
    if(skuConfirmAllUnm == per_page){
        $("#sku-confirm-all")
            .removeClass("btn-white")
            .addClass("btn-default active")
            .html('已全部添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 0);
        skuConfirmAllCurrentPage.push(pageAllStr);
    }else if((isSkuConfirmAll > 0) && (skuConfirmAllUnm != per_page)){
        $("#sku-confirm-all")
            .removeClass("btn-default active")
            .addClass("btn-white")
            .html('批量添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 1);
        skuConfirmAllCurrentPage.splice(skuConfirmAllCurrentPage.indexOf(pageAllStr), 1);
    }else if((isSkuConfirmAll == 0) && (skuConfirmAllCurrentPage.indexOf(pageAllStr) != '-1')){
        $("#sku-confirm-all")
            .removeClass("btn-white")
            .addClass("btn-default active")
            .html('已全部添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 0);
    }else {
        $("#sku-confirm-all")
            .removeClass("btn-default active")
            .addClass("btn-white")
            .html('批量添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 1);
    }

    $("#sku-confirm-all").data("current-page", current_page);

    //批量操作的时候，需要重新选中当前批量选中操作
    if(isSkuConfirmAll > 0){
        //清除当前选中分页
        $("#comm-pagination li").removeClass('active');
        //选中当前页面
        $("#comm-pagination").find('[pnum="'+current_page+'"]').addClass("active");
    }

}

    //查看商品
    $("body").on("click",".btn-fight-view",function (e) {

        e.preventDefault();
        var deposit = $('input[name="deposit"]').val();
        var deduction = $('input[name="deduction"]').val();
        var set_sku_list=$(this).data('sku-list');

        var sku_price_list=set_sku_list.sku_list;

        var obj=$(this);
        $.getJSON(getSkuUrl,{commodity_id:set_sku_list.commodity_id,commodity_set_id:set_sku_list.commodity_set_id},function (res) {

            var sku_list=res.data.sku_list;
            $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
            $(".sku-comm").html(res.data.commodity_row.commodity_name);
            var html='';
            $.each(sku_list,function(i,val){
                var price='';
                var checked=''
//                if(sku_price_list[val.id]){
//                    checked='checked';
//                    price  =sku_price_list[val.id];
//                }else{
                    price  = (val.price-deduction).toFixed(2);
//                }
                var sku_check='<input style="display: none" type="checkbox" ' +checked+'  class="sku-ckeck" value="6" name="">';
                html += '<tr set_sku_id="'+val.id+'">' +
                    '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                    '<td id="td_price" class="text-center">'+ val.price +'</td>' +
                    '<td class="text-center">'+price+'</td>' +
                    '<td class="text-center">' + val.stock +
                    '</td> </tr>';
            });

            $("#sku-modal").data("comm-id",set_sku_list.commodity_id);
            $("#sku-modal").data("comm-set-id",set_sku_list.commodity_set_id);
            $("#comm-name-header").html(set_sku_list.commodity_name);
            var index=$(".btn-fight-view").index(obj);
            $("#sku-modal").data('type',index);
            $(".sku-tb").html(html);
            $("#sku-modal").modal('show');

            //dlr初始化
            $("#sku-modal").find(".sku-dlr").val(set_sku_list.dlr_name).data("sku-dlr",set_sku_list.dlr_code);
        });
        //alert($(this).data("sku-list"));
    })

    /**********删除sku************/
    $("body").on("click",".del-sku-list",function (e) {

        if($("#act_status").length > 0){
            if($("#act_status").val() == 2){
                alert('活动已开始,禁止删除')
                return false;
            }
        }

        e.preventDefault();
        var sku_list=$(this).parents("tr").find(".btn-fight-view").data("sku-list");
        var type=$(".del-sku-list").index($(this));
        $("#del-sku-modal").find("#del-data-id").val(type).data('comm-set-id',sku_list.commodity_set_id);
        $("#del-sku-modal").modal('show');
    })

    $("#del-confirm").on("click",function () {
        var comm_set_id = $("#del-sku-modal").find("#del-data-id").data('comm-set-id');

        $(".del-sku-list").eq($("#del-sku-modal").find("#del-data-id").val()).parents('tr').remove();
        $("#del-sku-modal").modal('hide');
        //专营店端修改添加按钮
        //if(admin_type==2){
        var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+$("#del-sku-modal").find("#del-data-id").data('comm-set-id')+"']");
        btn_obj.removeClass("btn-default active").addClass("btn-white").attr("disabled",false).html('选择添加');
        comm_set_id_arr.splice($.inArray(comm_set_id,comm_set_id_arr),1);
        console.log("del:comm_set_id_arr = "+comm_set_id_arr);
        if(comm_set_id_arr.length==0){
            commodity_dlr_type_selected = 0;
        }

        var delConfirmPage = $("#sku-confirm-all").data("current-page");
        //如果没读取到值，就不操作批量添加
        if(delConfirmPage){
            if(comm_set_id_arr.length<1){
                oneCommodityClass = oneCommodityDlrTypeId = 0;
            }

            //设置批量添加操作按钮
            setSkuConfirmAllStatus(1, -1, delConfirmPage, 0);
        }
    })

    $("#put-form").on('click',function(){
        var $form=$("#fight-form");
        // dealer_select 有hidden, 就清空 dlr_hide 的value
        if($("#dealer_select").hasClass("hidden")){
            $("#dlr_hide").attr('value','');
        }else{
            var dlr_value = $("#dlr_hide").val();
            //遍历 checkbox-inline, 把没有勾选的数据从dlr_hide 中去除
            $(".checkbox-inline input:not(:checked)").each(function(i,v){
                var reg = new RegExp(this.value,"g");//g,表示全部替换。
                dlr_value = dlr_value.replace(reg,"");
            })
            $("#dlr_hide").attr('value',dlr_value);
        }
        var up_down_channel_name = getUpDownChannel();
        var validate=$form.psly().validate();  //表单验证
        if(!validate) return false;
        var form_data=$form.serialize();
        var home = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
        // 活动图片
        var activity_image=[];
        $(".activity_image_group .goods_pic").each(function(){
            $(this).find('li img').each(function(){
                activity_image.push($(this).attr("image-value"));
            });
        });

        var data=$.param({sku_list:getSkuList(),'up_down_channel_name':up_down_channel_name,'home':home,"activity_image":activity_image}) + '&' + form_data;
        Custom.ajaxPost(save_url,data,null,index_url);
    });

    function getSkuList() {
        var sku_list=[];
        $("#haved-commodtiy tr").each(function () {
            sku_list.push($(this).find(".btn-fight-view").data("sku-list"));
        })
        return sku_list;

    }

    function gethavedCommodityId() {
        var comm_set_id_arr=[];
        $("#haved-commodtiy tr").each(function () {
            var sku_list=$(this).find(".btn-fight-view").data("sku-list");
            comm_set_id_arr.push(sku_list.commodity_set_id);
        });
        return comm_set_id_arr;
    }

