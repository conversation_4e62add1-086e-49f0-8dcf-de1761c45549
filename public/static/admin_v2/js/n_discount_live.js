/**
 * Created by www on 17/11/24.
 */
if(action!='view'){
    initComm(ajaxCommUrl, 0);
}

//搜索商品列表
$("#comm-type-search").on('click',function(){
    var obj = $(this).parents('.form-group');
    var comm_parent_id = obj.find("select[name='comm_parent_id']").val();
    var sub_comm_type_id = obj.find("select[name='sub_comm_type_id']").val();
    var three_comm_type_id = obj.find("select[name='three_comm_type_id']").val();
    var commodity_name = obj.find("input[name='commodity_name']").val();
    var commodity_class = obj.find("select[name='commodity_class']").val();
    var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+commodity_name+'&commodity_class='+commodity_class;

    //额外参数验证
    if(typeof(isInitComm) != "undefined") {
        // console.log(param);
        if(isInitComm == 0){
            return false;
        }
    }
    var isSkuConfirmAll = $("#sku-confirm-all").data('is-sku-confirm-all');
    initComm(param);
});

/**
 * 活动商品列表搜索
 * @param isSkuConfirmAll
 * @returns {boolean}
 */
function commTypeSearch(isSkuConfirmAll = 0) {
    var comm_parent_id = $("select[name='comm_parent_id']").val();
    var sub_comm_type_id = $("select[name='sub_comm_type_id']").val();
    var three_comm_type_id = $("select[name='three_comm_type_id']").val();
    var commodity_name = $("input[name='commodity_name']").val();
    var commodity_class = $("select[name='commodity_class']").val();


    var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+commodity_name+'&commodity_class='+commodity_class;

    //额外参数验证
    if(typeof(isInitComm) != "undefined") {
        if(isInitComm == 0){
            return false;
        }
    }
    initComm(param, isSkuConfirmAll);

}

function initComm(url, isSkuConfirmAll) {
    var start_time =$("[name='start_time']").val();
    var end_time =$("[name='end_time']").val();
    // 获取定向人群
    var user_segment = $("*[name='user_segment']:checked").val();

    url = url+'&start_time='+start_time+'&end_time='+end_time;
    if (user_segment) {
        url = url + '&user_segment='+user_segment;
    }
    $.getJSON(url, null, function (resData) {
        resData.data.list.isSkuConfirmAll = isSkuConfirmAll;
        createPageComm(10, 10, resData.data.list.total, url);//创建翻页功能按钮，翻
        $("#add-comm-tbody").empty();
        if (resData.data.list.total > 0) {                          //页向后台请求连接
            setComm(url, isSkuConfirmAll);
        }
    });
}
function createPageComm(pageSize, buttons, total, url) {        //contracts_url为点击
    $("#comm-pagination").jBootstrapPage({      //功能按钮后需要访问action路径
        pageSize : pageSize,
        total : total,
        maxPageButton:buttons,
        onPageClicked: function(obj, page) {    //分页事件
            $("#add-comm-tbody").empty();
            setComm(url+"&page="+(page+1));
        }
    });
}

$(".datetimepicker3").datetimepicker({
    format:"YYYY-MM-DD HH:mm:00",
    locale: moment.locale('zh-cn'),
});


//获取并设置商品
function setComm(param, isSkuConfirmAll){

    var url = ajaxCommUrl;
    if(param!=null) url=param;
    var user_segment = $("*[name='user_segment']:checked").val();

    $.get(url,function(res){
        var html='';
        var list = res.data.list.data;

        //当前页是否已全选择
        var skuConfirmAllUnm = 0;
        //当前页
        var current_page = res.data.list.current_page;
        //当前分页条数
        var per_page = res.data.list.per_page;
        //重置当前页所有的comm_set_id值
        currentPageCommSetIds = [];

        // console.log('res.datares.datares.data');
        // console.log(res.data);
        $.each(list,function(i,val){
            currentPageCommSetIds.push(val.commodity_set_id)
            var index= $.inArray(val.commodity_set_id, comm_set_id_arr);
            if((isSkuConfirmAll > 0) && (val.is_n_discount != 1) && (val.is_pre != 1) && (val.is_user_segment != 2) && (val.is_seckill != 1)){
                index = 1;
                //批量添加操作
                skuConfirm(val)
                skuConfirmAllUnm +=1;
            }

            if(index>-1){
                var button='<button data-per-page="'+per_page+'"  data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+'"'+
                ' commodity_class="'+val.commodity_class+'" ' +
                ' commodity_dlr_type_id="'+val.commodity_dlr_type_id+'" ' +
                'data-dlr-code="'+val.dlr_code+'" class="btn btn-sm btn-fight btn-default active" disabled>已添加</button>';
            }else{
                if(val.is_n_discount ==1){
                    var button='<button data-per-page="'+per_page+'"  data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                        '"data-dlr-code="'+val.dlr_code+'" class="btn btn-white active btn-sm" disabled>已参与其他N件N折</button>';
                }else if(val.is_pre == 1){
                    var button='<button data-per-page="'+per_page+'"  data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+'"'+
                        ' commodity_class="'+val.commodity_class+'" ' +
                        ' commodity_dlr_type_id="'+val.commodity_dlr_type_id+'" ' +
                        'data-dlr-code="'+val.dlr_code+'" class="btn btn-sm btn-fight btn-default active" disabled>已参与预售</button>';
                }else if(val.is_seckill == 1) {
                    var button = '<button data-per-page="' + per_page + '"  data-comm-id="' + val.commodity_id + '" home="' + val.home + '" data-comm-set-id="' + val.commodity_set_id + '"' +
                        ' commodity_class="' + val.commodity_class + '" ' +
                        ' commodity_dlr_type_id="' + val.commodity_dlr_type_id + '" ' +
                        'data-dlr-code="' + val.dlr_code + '" class="btn btn-sm btn-fight btn-default active" disabled>已参与秒杀</button>';

                } else if(user_segment != undefined && val.is_user_segment == 2) {
                    var button = '<button data-per-page="' + per_page + '"  data-comm-id="' + val.commodity_id + '" home="' + val.home + '" data-comm-set-id="' + val.commodity_set_id + '"' +
                        ' commodity_class="' + val.commodity_class + '" ' +
                        ' commodity_dlr_type_id="' + val.commodity_dlr_type_id + '" ' +
                        'data-dlr-code="' + val.dlr_code + '" class="btn btn-sm btn-fight btn-default active" disabled>定向人群不一致</button>';
                }else{
                    var button='<button data-per-page="'+per_page+'"  data-commodity-dlr-type="'+val.commodity_dlr_type+'" home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                        ' commodity_class="'+val.commodity_class+'" ' +
                        ' commodity_dlr_type_id="'+val.commodity_dlr_type_id+'" ' +
                        'data-comm-set-id="'+val.commodity_set_id+'" data-dlr-code="'+val.dlr_code+'" class="btn btn-white btn-sm btn-fight">选择添加</button>';
                }
            }

            html += '<tr id="comm_tab_tr_'+val.commodity_id+'">' +
                '<td class="text-left"><image class="cover-image" src="'+val.cover_image+'"><a class="init-commodity-preview"'+
                'data-dlr-code="'+val.dlr_code+'" data-commodity-id="'+val.commodity_id+'">'+  val.commodity_name +'</a></td>' +
                '<td class="text-center">'+ val.up_down_channel_name +'</td>' +
                '<td class="text-center">'+ val.price +'</td>' +
                '<td class="text-center">'+ val.count_stock +'</td>' +
                '<td class="text-center">' + button +
                '</td> </tr>';
        });
        $("#add-comm-tbody").html(html);

        if(isSkuConfirmAll == 0 || !isSkuConfirmAll){
            skuConfirmAllUnm = getSkuConfirmAllUnm(comm_set_id_arr);
        }
        //增加批量添加按钮禁用操作
        setSkuConfirmAllStatus(isSkuConfirmAll, skuConfirmAllUnm, current_page, per_page);

    },'json');
}

//添加商品
$("#add-comm-tbody").on('click',".btn-fight",function (e) {
    e.preventDefault();
    var sku_modal=$(this).parents("tr");
    var home = $(this).attr('home');
    var commodity_class = $(this).attr('commodity_class');
    var commodity_dlr_type_id = $(this).attr('commodity_dlr_type_id');
    var per_page = sku_modal.data('per-page');

    var fhome = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
    var fcommodity_class = $("#haved-commodtiy").find("tr:first-child").find(".commodity_class").val();
    if(fhome != home){
        if(fhome == 1){
            //layer.msg('只能添加“快递到家“的商品，该商品为“到店安装”');
            //return false;
        }
        if(fhome == 0){
            //layer.msg('只能添加“到店安装“的商品，该商品为“快递到家”');
            //return false;
        }
    }

    if( (typeof(fcommodity_class) != "undefined") && (fcommodity_class != commodity_class)){
        layer.msg('活动商品只能选择相同类型/种类;<br/>您已选择的是:'+commodity_class_type[fcommodity_class]);
        return false;
    }

    $("#modal_home").val("");
    $("#modal_home").val(home);
    $("#modal_commodity_class").val("");
    $("#modal_commodity_class").val(commodity_class);
    $("#commodity_dlr_type_id").val("");
    $("#commodity_dlr_type_id").val(commodity_dlr_type_id);

    var commodity_list =new Object();
    commodity_list.commodity_id    =$(this).attr('data-comm-id');
    commodity_list.commodity_set_id=$(this).attr("data-comm-set-id");
    commodity_list.commodity_name  =sku_modal.find("a").html();
    commodity_list.image  =sku_modal.find(".cover-image").attr("src");

    if(oneCommodityClass==0){
        oneCommodityClass = commodity_class;
    }
    if(oneCommodityClass != commodity_class){
        $("#sku-modal").modal('hide');
        var msg1 = "只能添加“"+commodityClassJson[oneCommodityClass]+"“的商品，该商品为“"+commodityClassJson[commodity_class]+"”";
        layer.msg(msg1);
        return;
    }

    // if(oneCommodityDlrTypeId==0){
    //     oneCommodityDlrTypeId = commodity_dlr_type_id;
    // }
    // if(oneCommodityDlrTypeId != commodity_dlr_type_id){
    //     $("#sku-modal").modal('hide');
    //     var msg2 = "只能添加同种类型的商品，该商品为“"+commDlrTypeJson[oneCommodityDlrTypeId]+"“";
    //     layer.msg(msg2);
    //     return;
    // }

    //已经添加的直接跳过
    var index = $.inArray(commodity_list.commodity_set_id, comm_set_id_arr);
    if(index > -1){
        $("#sku-modal").modal('hide');
        return;
    }

    comm_set_id_arr.push(commodity_list.commodity_set_id);
    console.info("add:"+commodity_list.commodity_id);
    comm_id.push(Number(commodity_list.commodity_id));
    console.log('comm_id:',comm_id)
    var json_st=JSON.stringify(commodity_list);
    var html='<tr class="info" commid="'+$(this).attr('data-comm-id')+'" set_id="'+$(this).attr("data-comm-set-id")+'"  data>' +
        '<td style="width: 350px;">' +
        '<input type="hidden" name="home"  class="home" value="'+home+'"/><input type="hidden" name="commodity_class" '+
        ' class="commodity_class" value="'+commodity_class+'"/><img class="cover-image" src="'+commodity_list.image+'">'+commodity_list.commodity_name+'' +
        '</td>' +
        '<td class="text-right">'+
        '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list"  data-comm_id="'+commodity_list.commodity_id+'" data-comm_set_id="'+commodity_list.commodity_set_id+'" data-comm_name="'+commodity_list.commodity_name+'">删除</button>' +
        '</td>' +
        '</tr>';
    $("#haved-commodtiy").append(html);

    var delConfirmPage = $("#sku-confirm-all").data("current-page");
    //如果没读取到值，就不操作批量添加
    if(delConfirmPage){
        var skuConfirmAllUnm = getSkuConfirmAllUnm(comm_set_id_arr);
        //设置批量添加操作按钮
        setSkuConfirmAllStatus(1, skuConfirmAllUnm, delConfirmPage, per_page);
    }

    $(this).removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');


})

/**
 * 获取当前页已选中的商品数量
 * @param comm_set_id_arr
 * @returns {number}
 */
function getSkuConfirmAllUnm(comm_set_id_arr){
    var num = 0;
    for (var i=0; i < currentPageCommSetIds.length; i++){
        var index = $.inArray(currentPageCommSetIds[i], comm_set_id_arr);
        if(index > -1){
            num ++;
        }
    }
    return num;
}

/**
 * 批量添加操作
 * @param sku_modal
 * @param type
 */
function skuConfirm(sku_modal, type = 'add'){

    // console.log('sku_modal:',sku_modal);
    var home =  sku_modal.home;
    var commodity_class =  sku_modal.commodity_class;

    var commodity_list =new Object();

    commodity_list.commodity_id    =sku_modal.commodity_id;
    commodity_list.commodity_set_id=sku_modal.commodity_set_id;
    commodity_list.dlr_code        =sku_modal.dlr_code;
    commodity_list.dlr_name        =sku_modal.dlr_name;
    commodity_list.commodity_name  =sku_modal.commodity_name;
    commodity_list.image           =sku_modal.cover_image;
    var image= sku_modal.cover_image;
    var  commodity_name=sku_modal.commodity_name;
    if ((!$(".sku-dlr").val() || !commodity_list.dlr_code) && $("input[nam='set_type']") == 1){
        layer.msg('请选择经销商');
        return ;
    }
    //新增数据添加到后面
    var json_st=JSON.stringify(commodity_list);

    //添加
    if (type=='add'){
        //已经添加的直接跳过
        var index = $.inArray(commodity_list.commodity_id, comm_id);
        console.log('index:',index);
        if(index > -1){
            $("#sku-modal").modal('hide');
            return;
        }
        console.log('add:',commodity_list.commodity_id)
        comm_set_id_arr.push(commodity_list.commodity_set_id);
        comm_id.push(commodity_list.commodity_id);
        console.log('comm_id:',comm_id)

        var json_st=JSON.stringify(commodity_list);
        var html='<tr class="info" commid="'+ commodity_list.commodity_id +'" set_id="'+ commodity_list.commodity_set_id +'"  data>' +
            '<td style="width: 350px;">' +
            '<input type="hidden" name="home"  class="home" value="'+home+'"/><input type="hidden" name="commodity_class" '+
            ' class="commodity_class" value="'+commodity_class+'"/><img class="cover-image" src="'+commodity_list.image+'">'+commodity_list.commodity_name+'' +
            '</td>' +
            '<td class="text-right">'+
            '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list"  data-comm_id="'+commodity_list.commodity_id+'" data-comm_set_id="'+commodity_list.commodity_set_id+'" data-comm_name="'+commodity_list.commodity_name+'">删除</button>' +
            '</td>' +
            '</tr>';
        $("#haved-commodtiy").append(html);
        commodity_select()

    }else {

    }

    //选择添加完毕后，禁用添加按钮
    var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+commodity_list.commodity_set_id+"']");
    btn_obj.removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
    btn_obj.parents("button").html('已添加');
    $("#sku-modal").modal('hide');

}

/**
 * 设置批量添加操作按钮
 * @param isSkuConfirmAll
 * @param skuConfirmAllUnm
 * @param per_page
 */
function setSkuConfirmAllStatus(isSkuConfirmAll, skuConfirmAllUnm, current_page, per_page){
    current_page = parseInt(current_page);
    //oneCommodityClass  oneCommodityDlrTypeId
    var obj = $(".comm-type-search2").parents('.form-group');
    var commodity_class = obj.find("select[name='commodity_class']").val();
    var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();
    var pageAllStr = current_page +'x'+ commodity_class +'v'+ commodity_dlr_type_id;

    //增加批量添加按钮禁用操作
    if(skuConfirmAllUnm == per_page){
        $("#sku-confirm-all")
            .removeClass("btn-white")
            .addClass("btn-default active")
            .html('已全部添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 0);
        skuConfirmAllCurrentPage.push(pageAllStr);
    }else if((isSkuConfirmAll > 0) && (skuConfirmAllUnm != per_page)){
        $("#sku-confirm-all")
            .removeClass("btn-default active")
            .addClass("btn-white")
            .html('批量添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 1);
        skuConfirmAllCurrentPage.splice(skuConfirmAllCurrentPage.indexOf(pageAllStr), 1);
    }else if((isSkuConfirmAll == 0) && (skuConfirmAllCurrentPage.indexOf(pageAllStr) != '-1')){
        $("#sku-confirm-all")
            .removeClass("btn-white")
            .addClass("btn-default active")
            .html('已全部添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 0);
    }else {
        $("#sku-confirm-all")
            .removeClass("btn-default active")
            .addClass("btn-white")
            .html('批量添加');
        $("#sku-confirm-all").data("is-sku-confirm-all", 1);
    }

    $("#sku-confirm-all").data("current-page", current_page);

    //批量操作的时候，需要重新选中当前批量选中操作
    if(isSkuConfirmAll > 0){
        //清除当前选中分页
        $("#comm-pagination li").removeClass('active');
        //选中当前页面
        $("#comm-pagination").find('[pnum="'+current_page+'"]').addClass("active");
    }

    // console.log('skuConfirmAllCurrentPage');
    // console.log(skuConfirmAllCurrentPage);

}

//批量选择商品
$("#comm-all-s").on('click',function(){
    var obj = $(this).parents('.form-group');
    var comm_parent_id = obj.find("select[name='comm_parent_id']").val();
    var comm_type_id = obj.find("select[name='comm_type_id']").val();
    var commodity_name = obj.find("input[name='commodity_name']").val();
    var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&comm_type_id='+comm_type_id+'&commodity_name='+commodity_name+'&all_s=1';
    var url = ajaxCommUrl;
    if(param!=null) url=param;
    console.info("url:"+param);
    $.get(url,function(res){
        var html='';
        var list = res.data.list.data;
        $.each(list,function(i,val){
            comm_set_id_arr.push(val.commodity_set_id);
            comm_id.push(val.commodity_id);
            html +='<tr class="info" >' +
                '<td style="width: 350px;">' +
                '<img class="cover-image" src="'+val.cover_image+'">'+val.commodity_name+'' +
                '</td>' +
                '<td class="text-right">'+
                '<button   class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list" data-comm_name="'+val.commodity_name+'">删除</button>' +
                '</td>' +
                '</tr>';
        });
        $("btn-white").removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
        $("#haved-commodtiy").html(html);
        commodity_select()
    },'json');
});




//查看商品
$("body").on("click",".btn-fight-view",function (e) {
    e.preventDefault();
    var set_sku_list=$(this).data('sku-list');
    var obj=$(this);
    $.getJSON(getSkuUrl,{commodity_id:set_sku_list.commodity_id,commodity_set_id:set_sku_list.commodity_set_id},function (res) {
        var sku_list=res.data.sku_list;
        $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
        $(".sku-comm").html(res.data.commodity_row.commodity_name);
        var html='';
        $.each(sku_list,function(i,val){
            html += '<tr set_sku_id="'+val.id+'">' +
                '<td class="text-left">'+val.sku_val+'</td>' +
                '<td >'+ val.price +'</td>' +
                '<td >' + val.stock +
                '</td> </tr>';
        });
        $("#sku-modal").data("comm-id",set_sku_list.commodity_id);
        $("#sku-modal").data("comm-set-id",set_sku_list.commodity_set_id);
        $("#comm-name-header").html(set_sku_list.commodity_name);
        var index=$(".btn-fight-view").index(obj);
        $("#sku-modal").data('type',index);
        $(".sku-tb").html(html);
        $("#sku-modal").modal('show');

        //dlr初始化
        $("#sku-modal").find(".sku-dlr").val(set_sku_list.dlr_name).data("sku-dlr",set_sku_list.dlr_code);


    });

    //alert($(this).data("sku-list"));

})

/**********删除sku************/
$("body").on("click",".del-sku-list",function (e) {
    e.preventDefault();
    var sku_list=$(this).parents("tr").find(".btn-fight-view").data("sku-list");
    var comm_set_id =  $(this).data('comm_set_id');
    var type=$(".del-sku-list").index($(this));
    var comm_name = $(this).data('comm_name');
    console.info(comm_set_id);
    $("#del-sku-modal").find("#del-data-id").val(comm_set_id);
    $("#del-sku-modal").find(".alert").html('<i class="fa fa-info-circle"></i>您选择对商品 '+comm_name+' 进行移除操作吗？');
    $("#del-sku-modal").modal('show');
})

$("#del-confirm").on("click",function () {
    var comm_set_id = $("#del-sku-modal").find("#del-data-id").val();
    // console.log(comm_set_id);
    // console.log("[data-comm-set-id='"+comm_set_id+"']");
    // console.log('comm_set_id_arr');
    // console.log(comm_set_id_arr);

    $("#haved-commodtiy").find("[data-comm_set_id='"+comm_set_id+"']").html('去掉').parents('tr').remove();
    var comm_id_del =  $("#haved-commodtiy").find("[data-comm_set_id='"+comm_set_id+"']").attr('data-comm_id');
    $("#del-sku-modal").modal('hide');
    var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+comm_set_id+"']");
    btn_obj.removeClass("btn-default active").addClass("btn-white").attr("disabled",false).html('选择添加');
    comm_set_id_arr.splice($.inArray(comm_set_id,comm_set_id_arr),1);
    comm_id.splice($.inArray(comm_id_del,comm_id),1);
    // console.log("del:comm_set_id_arr = "+comm_set_id_arr);
    // console.log("del:comm_id_arr = "+comm_id);
    if(comm_set_id_arr.length==0){
        commodity_dlr_type_selected = 0;
    }

    var delConfirmPage = $("#sku-confirm-all").data("current-page");
    //如果没读取到值，就不操作批量添加
    if(delConfirmPage){
        if(comm_set_id_arr.length<1){
            oneCommodityClass = oneCommodityDlrTypeId = 0;
        }

        //设置批量添加操作按钮
        setSkuConfirmAllStatus(1, -1, delConfirmPage, 0);
    }

})

/**********删除sku************/



var sku_dlrs = [];
//获取专营店
$(".sku-dlr").on('click',function () {
    var commodity_set_id=$(this).parents('#sku-modal').data('comm-set-id');
    var dlr_sku_arr = sku_dlrs = $(this).data('sku-dlr').split(",");
    $.getJSON(getDlr_url,{commodity_set_id:commodity_set_id},function (resData) {
        if(resData.error==0){
            $("#dlr_content").empty();
            var data = $.parseJSON(resData.data);
            // console.log(data);
            Custom.selectDlr(data,dlr_sku_arr,function (dlr_code_arr,dlr_name_arr) {
                console.log('dlr_code_arr = '+dlr_code_arr);
                console.log('dlr_name_arr = '+dlr_name_arr);
                var dlr_ids = dlr_code_arr.join(',');
                var dlr_names = dlr_name_arr.join(',');
                $(".sku-dlr").val(dlr_names)
                $(".sku-dlr").data("sku-dlr",dlr_ids);
            })
            // for (var i = 0;i<data.length;i++){
            //     var index= $.inArray(data[i].dlr_code, dlr_sku_arr);
            //     var checked = index>-1 ? 'checked' : '';
            //     var label = $('<label class="m-r-5 dlr_label text-hidden" data-toggle="tip" title="'+data[i].dlr_name+'"></label>');
            //     label.appendTo($("#dlr_content"));
            //     var input = $('<input type="checkbox"'+checked+' class="dlr_checkbox single min" data-id="'+data[i].dlr_code+'" data-title="'+data[i].dlr_name+'">');
            //     input.appendTo(label);
            //     label.append(data[i].dlr_name);
            // }
            // $("#dlr-modal").modal("show");
        }else {
            layer.msg(resData.msg);
        }
    })

})


function getSkuList() {
    var sku_list=[];
    $("#haved-commodtiy tr").each(function () {
        sku_list.push($(this).find(".btn-fight-view").data("sku-list"));
    })
    return sku_list;

}

$("body").on("click","#put-form",function () {

    var $form=$("#add-form");
    var validate=$form.psly().validate();  //表单验证
    if(!validate) return false;
    var form_data=$form.serialize();
    var s_ids =  comm_set_id_arr;
    var goods_ids =  comm_id;

    var comm_set_id_arr_new = [];
    var comm_id_new = [];

    $("#haved-commodtiy tr").each(function(){
        if($(this).attr('set_id') != undefined){
            comm_set_id_arr_new.push($(this).attr('set_id'));
        }
    })

    $("#haved-commodtiy tr").each(function(){
        if($(this).attr('commid') != undefined){
            comm_id_new.push($(this).attr('commid'));
        }
    })

    var data_l=[];
    $('#ruleEdit').find('.form-control').each(function(){
        if($(this).val()!==''){
            data_l.push($(this).val());
        }
    })
    // 判断定向人群
    var user_segment = $("input[name='user_segment']:checked").val();
    // console.log('user_segment:',user_segment);
    if (user_segment == undefined) {
        if(data_l.length!=$('#ruleEdit').find('.form-control').length){
            layer.msg('请填写完整的折扣信息');
            return ;
        }
        var dis_info =  JSON.stringify(get_data());

    } else {
       // 定向人群
        var dis_info =  JSON.stringify(get_data_new());
    }

    // console.log('dis_info:',dis_info);
    if (dis_info == 'false') {
        return false;
    }

    if (dis_info === '') {
        alert('折扣信息不能为空');
        return false;
    }

    if(comm_set_id_arr_new.length > 0){
        s_ids = comm_set_id_arr_new;
        goods_ids = comm_id_new;
    }

    // dealer_select 有hidden, 就清空 dlr_hide 的value
    if($("#dealer_select").hasClass("hidden")){
        $("#dlr_hide").attr('value','');
    }else{
        var dlr_value = $("#dlr_hide").val();
        //遍历 checkbox-inline, 把没有勾选的数据从dlr_hide 中去除
        $(".checkbox-inline input:not(:checked)").each(function(i,v){
            var reg = new RegExp(this.value,"g");//g,表示全部替换。
            dlr_value = dlr_value.replace(reg,"");
        })
        $("#dlr_hide").attr('value',dlr_value);
    }
    var up_down_channel_name = getUpDownChannel();

    // return;
    // 活动图片
    var activity_image=[];
    $(".activity_image_group .goods_pic").each(function(){
        $(this).find('li img').each(function(){
            activity_image.push($(this).attr("image-value"));
        });
    });

    var home = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
    var commodity_class = $("#haved-commodtiy").find("tr:first-child").find(".commodity_class").val();
    // var data=form_data +"&s_ids="+s_ids+"&goods="+goods_ids;
    var data= form_data + '&' + $.param({n_info:dis_info,'up_down_channel_name':up_down_channel_name,'commodity_class':commodity_class,'home':home,'activity_image':activity_image})+"&s_ids="+s_ids+"&goods="+goods_ids;
    // console.log(get_data());
    // console.info(data);return false;
    // Custom.ajaxPost(save_url,data,null);
    Custom.ajaxPost(save_url,data,null,index_url);
})

$("body").on("click","#edit_submit",function () {
    var $form=$("#edit-form");
    // dealer_select 有hidden, 就清空 dlr_hide 的value
    if($("#dealer_select").hasClass("hidden")){
        $("#dlr_hide").attr('value','');
    }else{
        var dlr_value = $("#dlr_hide").val();
        //遍历 checkbox-inline, 把没有勾选的数据从dlr_hide 中去除
        $(".checkbox-inline input:not(:checked)").each(function(i,v){
            var reg = new RegExp(this.value,"g");//g,表示全部替换。
            dlr_value = dlr_value.replace(reg,"");
        })
        $("#dlr_hide").attr('value',dlr_value);
    }
    var up_down_channel_name = getUpDownChannel();
    var validate=$form.psly().validate();  //表单验证
    if(!validate) return false;
    var form_data=$form.serialize();
    var home = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
    var commodity_class = $("#haved-commodtiy").find("tr:first-child").find(".commodity_class").val();
    var data= form_data + '&' + $.param({'up_down_channel_name':up_down_channel_name,'home':home,'commodity_class':commodity_class}) ;
    Custom.ajaxPost(save_url,data,null,index_url);
})

//更改活动时间
$('input[name=start_time],input[name=end_time]').on('blur',function(e){
    var s_time = $('input[name=start_time]').val();
    var e_time = $('input[name=end_time]').val();
    if(start_time != s_time || end_time != e_time){
        if($('#haved-commodtiy tr').length != 0 ){
            var index = layer.open({
                title: ['操作提醒'],
                btn: ['确认', '取消'],
                content:"<div style='font-size: 15px'>更改活动时间会清空下方添加商品列表</div>",
                yes: function (res) {
                    layer.close(index)
                    start_time = s_time;
                    end_time = e_time;
                    comm_id = [];
                    comm_set_id_arr = [];
                    $('#haved-commodtiy').text('');
                    initComm(ajaxCommUrl);
                }
            })
        }

    }
});


// 刷新会员列表
var refresh_user_level =  function (order_no) {
    console.log('order_no:',order_no)
    new_user_level = user_level_list.filter((val) => {
        return val.order_no >= order_no;
    });
    console.log('new_user_level:',new_user_level)
    // 标题
    var html = '';
    $.each(new_user_level,function(i,val){
        html += '<div class="stylelt user_level county_name" data-title="' + val.value_code +'" id="' + val.order_no + '"  data-code="' + val.value_code +'">'+ val.county_name +'</div>'
    })


    // 输入框
    var k_html = '';
    $.each(new_user_level, function (i, val) {
        k_html += '<div class="stylelt level_jian form-z value_code"><input size="4" class="v_dis_type" value_code="' +val.value_code+ '"  data-parsley-required="true"/><span class="c_dis_type">折</span></div>'
    })

    $(".county_name").remove();
    $("#county_name").append(html);
    $(".value_code").remove();
    $("#value_code").append(k_html);
    // 清除追加的规则
    $('.user_level_1').remove();
    $('.user_level_2').remove();
}

// 获取新的规则
var get_data_new=function () {
    var user_segment = $("input[name='user_segment']:checked").val();
    // 会员折扣

    if (user_segment == 1) {
        var data = [];
        var is_user_level_die = 0;
        $('#member_box').find('.level_item').each(function (index,value){
            // 件
            var jian = $(this).find('.form-j').val();
            var zhe_arr = [];

            var old_num = 0;
            $(this).find('.form-z').each(function (key,val){
                var num = $(this).find('.v_dis_type').val();
                var value_code = $(this).find('.v_dis_type').attr('value_code');

                if (key > 0 && old_num < num*100) {
                    is_user_level_die = 1;
                } else {
                    // 赋值
                    old_num = num * 100;
                }

                let zhe = {
                    [value_code]:num,
                }
                zhe_arr.push(zhe);
            })
            let zhe_obj = '';
            let newJson = {};
            zhe_arr.forEach(function (key,val) {
                zhe_obj = Object.assign(newJson,key);
            })
            arr = {};
            arr['jian'] = jian;
            arr['zhe'] = zhe_obj;
            data[index] = arr;
        })
        if (is_user_level_die == 1) {
            layer.msg('高级别会员优惠折扣应大于低级别会员');
            return false
        }
    }
    // 车主折扣
    if (user_segment == 2) {
        var data = [];
        var no_car_val_old = 0;
        var no_car_val_new = 0;
        var is_die = 0;  // 多个维度

        var car_val_old = 0;
        var car_val_new = 0;
        var is_die_car = 0;  // 多个维度

        var is_car_die = 0; // 判断等级优惠力度

        var is_car_a_die= 0; // pz1a
        $('#car_onwer_box').find('.level_item').each(function (index,value){
            var jian = $(this).find('.form-j').val();
            var no_car = $(this).find('.carv_dis_type_1').val();
            var car = $(this).find('.carv_dis_type_2').val();
            // 判断非车主梯度
            var no_re_obj = judgeIsEmpty(index, no_car, no_car_val_new, no_car_val_old, is_die)
            no_car_val_new = no_re_obj.new_val;
            no_car_val_old = no_re_obj.old_val;
            is_die = no_re_obj.is_die;

            var zhe = {
                'no_car':no_car,
                'car':car
            };
            if (no_car != '' && no_car * 100 < car * 100) {
                is_car_die = 1;
            }
            console.log('set_type:',set_type)
            // pz1a
            if  (set_type == 6) {
                // 判断日产车主梯度
                var car_obj = judgeIsEmpty(index, car, car_val_new, car_val_old, is_die_car)
                console.log('car_index_'+index+':',car_obj)
                car_val_new = car_obj.new_val;
                car_val_old = car_obj.old_val;
                is_die_car = car_obj.is_die;

                var a_car = $(this).find('.carv_dis_type_3').val();
                zhe = {
                    'no_car':no_car,
                    'car':car,
                    'a_car':a_car // pz1a车主
                };
                if (no_car != '') {
                    if (no_car * 100 < a_car * 100) {
                        is_car_die = 1;
                    }
                }
                if (car != '') {
                    // 日产和pz1a
                    if (car * 100 < a_car * 100) {
                        is_car_a_die = 1;
                    }
                }
                if (no_car != '' && car != '') {
                    if (no_car * 100 < car * 100) {
                        is_car_die = 1;
                    }
                }


            }

            var arr = {
                'jian':jian,
                'zhe':zhe,
            };
            data[index] = arr;
        });
        console.log('is_die_car:',is_die_car);

        if (is_die == 1 || is_die_car == 1) {
            layer.msg('多个梯度，请保证多个梯度资格一致');
            return false
        }
        if (is_car_die == 1) {
            layer.msg('车主的优惠力度需大于等于非车主');
            return false
        }
        if (is_car_a_die == 1) {
            layer.msg('Ariya车主的优惠力度需大于日产车主');
            return false
        }
    }
    return data;

}


/**
 * 判断
 * @param index
 * @param car_value
 * @param new_val
 * @param old_val
 * @param is_die
 * @returns {{new_val: number, old_val: number, is_die: number}}
 */
function judgeIsEmpty(index, car_value, new_val=0, old_val=0, is_die=0)
{

    if (index === 0) {
        // 非车主
        if (car_value == '') {
            old_val = 0
        } else {
            old_val = 1
        }

    } else {
        if (car_value == '') {
            new_val = 0;
        } else {
            new_val = 1;
        }
        if (new_val != old_val) {
            is_die = 1;
        }
        old_val = new_val
    }


    return {'is_die':is_die,'new_val':new_val,'old_val':old_val};
}


// 添加车主折扣
$(".adbtn2").on('click',function(){
    var l=$('.level_name_box_2').length;
    if(l<3) {

        var html = '<div class="level_item level_name_box_2 car_'+ l +'">'
            +   '<div class="stylelt level_jian">'
            +   '<input size="4"  class=" piece form-j" data-parsley-required="true"><span class="c_dis_type">件</span>'
            +   '</div>'
            +   '<div class="level_name_box stylelt level_pi">'
            +   '<div class="level_name_item_box" ><a href="#" data-type="text" class="btn btn-primary btn-sm editable editable-click car_batch_price" data-value="" data-placeholder="批量设置" data-title="批量设置">批量设置<i class="fa fa-edit m-l-5"></i></a></div>'
            +   '</div>'
            +   '<div class="level_zhe_car stylelt">'
        if (set_type == 5 || set_type == 7) {
            html +=   '<div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_1"/><span class="c_dis_type">折</span></div>'
                +   '<div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_2" data-parsley-required="true"/><span class="c_dis_type">折</span></div>';
        } else {
            html += '<div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_1"/><span class="c_dis_type">折</span></div>'
                + '<div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_2" /><span class="c_dis_type">折</span></div>'
                + '<div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_3" data-parsley-required="true"/><span class="c_dis_type">折</span></div>';
        }
        html +=   '</div>'
            +   '<div class="btnc level_btn">'
            +   '<i  data-toggle="modal" class="fa fa-2x fa-minus-circle text-danger delbtn2"></i>'
            +   '</div>'
            +   '</div>';

        $("#car_onwer_box .rule_new_box").append(html);
        $('.car_batch_price').editable({
            success: function (response, newValue) {
                $(this).parent().parent().parent().find(".carv_dis_type").val(newValue)
                $(".editable-cancel").click();
                return false;
            },
            validate: function (value) { //字段验证
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9]+(.[0-9]{1,2})?$/).test(value)) {
                    return '格式有误';
                }
            }
        });
    }


})

// 添加会员折扣
$(".adbtn").on("click",function(){
    // 获取会员级别
    var user_segment_options = $("input[name='user_segment_options']:checked").val();
    // console.log('user_segment_options:',user_segment_options);
    if (user_segment_options == undefined) {
        layer.msg('请先选择定向人群会员级别');
        return false;
    }
    var l=$('.level_name_box_1').length;
    // console.log('lenth:',l);
    // console.log('new_user_level:',new_user_level)
    if(l<3) {
        var html =  '<div class="level_item level_name_box_1 user_level_'+ l +'">'
            +   '<div class="stylelt level_jian">'
            +   '<input size="4"  class=" piece form-j" data-parsley-required="true"><span class="c_dis_type">件</span>'
            +   '</div>'
            +   '<div class="level_name_box stylelt level_pi">'
            +   '<div class="level_name_item_box" ><a href="#" data-type="text" class="btn btn-primary btn-sm editable editable-click batch_price" data-value="" data-placeholder="批量设置" data-title="批量设置">批量设置<i class="fa fa-edit m-l-5"></i></a></div>'
            +   '</div>'
            +   '<div class="level_zhe_box stylelt" id="value_code">'

        $.each(new_user_level, function (i, val) {
            html += '<div class="stylelt level_jian form-z value_code"><input size="4" class="v_dis_type" value_code="' +val.value_code+ '"  data-parsley-required="true"/><span class="c_dis_type">折</span></div>'
        });
        html +=  '</div>'
            +   '<div class="btnc level_btn">'
            +   '<i  data-toggle="modal" class="fa fa-2x fa-minus-circle text-danger delbtn"></i>'
            +   '</div>'
            +   '</div>';
        $("#member_box .rule_new_box").append(html);
        $('.batch_price').editable({
            success: function (response, newValue) {
                $(this).parent().parent().parent().find(".v_dis_type").val(newValue)
                $(".editable-cancel").click();
                return false;
            },
            validate: function (value) { //字段验证
                // console.log('value:',value)
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9]+(.[0-9]{1,2})?$/).test(value)) {
                    return '格式有误';
                }
            }
        });
    }
})


$(".delbtn2").live('click',function(){
    $(this).parentsUntil(".level_item").parent().remove();
    $(this).parentsUntil(".level_item").parent().remove();

})

$(".delbtn").live('click',function(){
    $(this).parentsUntil(".level_item").parent().remove();
})


// 会员批量设置
$('.batch_price').editable({
    success: function (response, newValue) {
        $(this).parent().parent().parent().find(".v_dis_type").val(newValue)
        $(".editable-cancel").click();
        return false;
    },
    validate: function (value) { //字段验证
        if (!$.trim(value)) {
            return '不能为空';
        }
        if (!(/^[0-9]+(.[0-9]{1,2})?$/).test(value)) {
            return '格式有误';
        }
    }
});

// 车主批量设置
$('.car_batch_price').editable({
    success: function (response, newValue) {
        $(this).parent().parent().parent().find(".carv_dis_type").val(newValue)
        $(".editable-cancel").click();
        return false;
    },
    validate: function (value) { //字段验证
        if (!$.trim(value)) {
            return '不能为空';
        }
        if (!(/^[0-9]+(.[0-9]{1,2})?$/).test(value)) {
            return '格式有误';
        }
    }
});


// 选择会员级别 规则跟着变动
var new_user_level = {}
$('.user_segment_options').click(function () {

    var _this = $(this);
    var order_no = _this.attr('id');
    refresh_user_level(order_no)
})

$(".user_segment").click(function(){

    console.log('comm_id:',comm_id);
    if (comm_id.length > 0) {
        var index = layer.open({
            title: ['操作提醒'],
            btn: ['确认', '取消'],
            content:"<div style='font-size: 15px'>更改人群类型会清空下方添加商品列表</div>",
            yes: function (res) {
                layer.close(index)
                comm_id = [];
                comm_set_id_arr = [];
                $('#haved-commodtiy').text('');
            }
        })
    }
    // 获取会员级别
    var order_no = $("input[name='user_segment_options']:checked").attr('id');
    if (order_no != undefined) {
        refresh_user_level(order_no);
    } else {
        var html = '';
        $.each(user_level_list,function(i,val){
            html += '<div class="stylelt user_level county_name" data-title="' + val.value_code +'" id="' + val.order_no + '"  data-code="' + val.value_code +'">'+ val.county_name +'</div>'
        })

        $(".county_name").remove();
        $("#county_name").append(html);
        // 输入框
        var k_html = '';
        $.each(user_level_list, function (i, val) {
            k_html += '<div class="stylelt level_jian form-z value_code"><input size="4" class="v_dis_type" value_code="' +val.value_code+ '"  data-parsley-required="true" /><span class="c_dis_type">折</span></div>'
        })
        $(".value_code").remove();
        $("#value_code").append(k_html);
    }

    $("#rule_new").show()
    $("#rule_old").hide()
    if($(this).val() == 1){
        $("#user_level_box").show();
        $("#car_onwer_box").hide();
        $("#member_box").show();
        user_segment = 1;
    }else{
        $("#user_level_box").hide();
        $("#member_box").hide();
        $("#car_onwer_box").show();
        user_segment = 2
    }
    commTypeSearch2(0)
})






