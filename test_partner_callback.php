<?php
/**
 * 合作伙伴回调测试文件
 * 用于测试合作伙伴发货回调功能
 */

// 测试数据
$testData = [
    "orderDetailNo" => "TEST_ORDER_20250121001",
    "deliveryList" => [
        [
            "deliveryNo" => "DELIVERY_001",
            "logisticsCompany" => "SF",
            "logisticsCompanyName" => "顺丰速运",
            "logisticsNo" => "SF1234567890",
            "deliveryTime" => "2025-01-21 10:30:00",
            "deliveryStatus" => "已发货",
            "commodityList" => [
                [
                    "orderCommodityId" => "123",
                    "commodityName" => "测试商品1",
                    "skuCode" => "SKU001",
                    "supplierCommodityCode" => "SUP001",
                    "commodityQuantity" => 2,
                    "commodityOrderStatus" => "已发货"
                ],
                [
                    "orderCommodityId" => "124",
                    "commodityName" => "测试商品2",
                    "skuCode" => "SKU002",
                    "supplierCommodityCode" => "SUP002",
                    "commodityQuantity" => 1,
                    "commodityOrderStatus" => "已发货"
                ]
            ]
        ]
    ]
];

// 输出测试数据的JSON格式
echo "测试数据 (JSON格式):\n";
echo json_encode($testData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
echo "\n\n";

// 输出curl测试命令
echo "curl测试命令:\n";
echo "curl -X POST \\\n";
echo "  http://your-domain.com/net-small/partner-callback/delivery \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -d '" . json_encode($testData, JSON_UNESCAPED_UNICODE) . "'\n\n";

// 输出Postman测试说明
echo "Postman测试说明:\n";
echo "1. 方法: POST\n";
echo "2. URL: http://your-domain.com/net-small/partner-callback/delivery\n";
echo "3. Headers: Content-Type: application/json\n";
echo "4. Body: 选择raw，格式选择JSON，粘贴上面的测试数据\n\n";

// 输出预期响应格式
echo "预期成功响应格式:\n";
$successResponse = [
    "code" => 200,
    "message" => "回调处理成功",
    "data" => [
        "order_code" => "TEST_ORDER_20250121001",
        "updated_order_status" => 4,
        "updated_commodities" => 1
    ],
    "timestamp" => time()
];
echo json_encode($successResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
echo "\n\n";

// 输出预期错误响应格式
echo "预期错误响应格式:\n";
$errorResponse = [
    "code" => 400,
    "message" => "订单不存在: TEST_ORDER_20250121001",
    "data" => [],
    "timestamp" => time()
];
echo json_encode($errorResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
echo "\n\n";

// 输出健康检查测试
echo "健康检查测试:\n";
echo "curl -X GET http://your-domain.com/net-small/partner-callback/health\n";
echo "预期响应:\n";
$healthResponse = [
    "code" => 200,
    "message" => "合作伙伴回调服务正常运行",
    "data" => [],
    "timestamp" => time()
];
echo json_encode($healthResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
echo "\n";
?>
