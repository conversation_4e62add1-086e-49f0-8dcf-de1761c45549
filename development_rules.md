# Net Small 开发规则快速参考

## 🚨 强制规则（必须遵守）

### 1. 架构分层规则
```php
// ✅ Controller → Service
$netOrder = new NetOrder();
$result = $netOrder->createOrder($data, $user, $channel);

// ❌ Service → Controller (禁止)
```

### 2. 代码复用规则
```php
// ✅ 抽取通用服务，避免重复代码
// ✅ 复用现有字段如 $all_change_car
```

### 3. 订单状态规则
```php
// ✅ 待支付订单查询
$where['order_status'] = ['in', [1, 8]];
```

### 4. 卡券状态规则
```php
// ✅ 订单占用卡券状态
$card_data['status'] = 5;

// ✅ 已核销卡券状态
if ($card['status'] == 3) {
    $card['status_text'] = '已使用';
}
```

### 5. 依赖管理规则
```bash
# ✅ 使用 package manager
composer require vendor/package

# ❌ 不手动编辑配置文件
```

### 6. 字段使用规则
```php
// 🤔 字段存在疑问时，必须先问用户确认
// ❌ 不应该造不存在的字段
```

## 📋 状态值速查表

### 订单状态 (order_status)
- `[1, 8]` - 待支付订单
- `1` - 待支付
- `8` - 待支付（其他状态）

### 卡券状态 (BuCardReceiveRecord.status)
- `5` - 被订单占用
- `3` - 已核销（显示"已使用"）

## 🔧 常用代码模板

### Controller 调用 Service
```php
class SomeController extends Common
{
    public function someAction()
    {
        // 参数验证
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result = $validate->scene("scene_name")->check($requestData);
        
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        
        // 调用服务层
        $service = new SomeService();
        $data = $service->processData($requestData, $this->user, $this->channel_type);
        
        return $this->setResponseSuccess($data)->send();
    }
}
```

### Service 层通用结构
```php
class SomeService extends Common
{
    public function processData($requestData, $user, $channel_type)
    {
        $this->user = $user;
        $this->channel_type = $channel_type;
        
        try {
            // 业务逻辑处理
            $result = $this->handleBusinessLogic($requestData);
            return $this->re_msg($result);
        } catch (\Exception $e) {
            Logger::error('业务处理错误', [
                'error' => $e->getMessage(),
                'data' => $requestData
            ]);
            return $this->re_msg('系统错误', 500);
        }
    }
}
```

### 待支付订单查询
```php
// 标准的待支付订单查询
$where = [
    'order_status' => ['in', [1, 8]],
    'user_id' => $user_id,
    'is_enable' => 1
];
$pending_orders = $order_model->where($where)->select();
```

### 卡券状态处理
```php
// 订单占用卡券
$update_data = ['status' => 5];
BuCardReceiveRecord::where('id', $card_id)->update($update_data);

// 卡券状态显示处理
foreach ($cards as &$card) {
    switch ($card['status']) {
        case 3:
            $card['status_text'] = '已使用';
            break;
        case 5:
            $card['status_text'] = '订单占用中';
            break;
        default:
            $card['status_text'] = '可用';
    }
}
```

## ⚠️ 常见错误

### ❌ 错误示例
```php
// 1. Service 调用 Controller
class NetOrder extends Common
{
    public function someMethod()
    {
        $controller = new OrderController(); // 禁止
    }
}

// 2. 错误的订单状态查询
$where['order_status'] = 0; // 应该使用 [1, 8]

// 3. 错误的卡券状态
$card_data['status'] = 1; // 订单占用应该使用 5

// 4. 使用未确认的字段
$data['unknown_field'] = $value; // 必须先确认字段是否存在
```

## 📞 字段确认流程

当遇到不确定的字段时，按以下格式询问：

```
请确认 [表名] 表中是否存在 '[字段名]' 字段？
如果存在，请说明该字段的用途和可能的值。
```

例如：
- "请确认订单表中是否存在 'delivery_method' 字段？"
- "请确认用户表中是否存在 'vip_level' 字段？"

## 🎯 开发检查清单

开发完成后，请检查：

- [ ] Controller 是否只调用 Service，没有反向调用
- [ ] 是否复用了现有代码，避免重复实现
- [ ] 待支付订单查询是否使用 `order_status=[1,8]`
- [ ] 卡券占用状态是否使用 `status=5`
- [ ] 已核销卡券是否显示"已使用"
- [ ] 依赖是否通过 package manager 管理
- [ ] 使用的字段是否都已确认存在
- [ ] 代码是否有适当的错误处理和日志记录