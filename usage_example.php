<?php
/**
 * 合作伙伴回调服务使用示例
 */

// 示例1: 直接使用服务类
use app\common\service\PartnerCallbackService;

// 模拟回调数据
$callbackData = [
    "orderDetailNo" => "ORDER_20250121001",
    "deliveryList" => [
        [
            "deliveryNo" => "DELIVERY_001",
            "logisticsCompany" => "SF",
            "logisticsCompanyName" => "顺丰速运",
            "logisticsNo" => "SF1234567890",
            "deliveryTime" => "2025-01-21 10:30:00",
            "deliveryStatus" => "已发货",
            "commodityList" => [
                [
                    "orderCommodityId" => "123",
                    "commodityName" => "测试商品1",
                    "skuCode" => "SKU001",
                    "supplierCommodityCode" => "SUP001",
                    "commodityQuantity" => 2,
                    "commodityOrderStatus" => "已发货"
                ]
            ]
        ]
    ]
];

// 创建服务实例并处理回调
$service = new PartnerCallbackService();
$result = $service->handleDeliveryCallback($callbackData);

if ($result['success']) {
    echo "回调处理成功: " . $result['message'] . "\n";
    print_r($result['data']);
} else {
    echo "回调处理失败: " . $result['message'] . "\n";
}

// 示例2: 查询回调日志
use app\common\model\inter\IPartnerCallbackLog;

$logModel = new IPartnerCallbackLog();

// 查询特定订单的回调日志
$params = [
    'where' => ['order_detail_no' => 'ORDER_20250121001'],
    'field' => 'id,callback_type,order_detail_no,process_status,created_date,error_message',
    'order' => 'id desc',
    'pagesize' => 10
];

$logList = $logModel->getListData($params);
echo "回调日志:\n";
foreach ($logList as $log) {
    echo "ID: {$log['id']}, 订单: {$log['order_detail_no']}, 状态: {$log['process_status']}, 时间: {$log['created_date']}\n";
}

// 示例3: 手动记录回调日志
$logData = [
    'callback_type' => 'partner_delivery_callback',
    'order_detail_no' => 'ORDER_20250121002',
    'request_url' => '/partner/delivery/callback',
    'request_method' => 'POST',
    'request_params' => json_encode($callbackData, JSON_UNESCAPED_UNICODE),
    'process_status' => 1, // 成功
    'remark' => '手动测试记录'
];

$logId = $logModel->logCallback($logData);
echo "记录日志ID: {$logId}\n";

// 示例4: 更新日志处理结果
$updateData = [
    'process_status' => 2, // 失败
    'error_message' => '测试错误信息',
    'response_data' => json_encode(['error' => 'test error'], JSON_UNESCAPED_UNICODE)
];

$updated = $logModel->updateProcessResult($logId, $updateData);
echo "更新日志结果: " . ($updated ? '成功' : '失败') . "\n";

// 示例5: 批量查询订单状态
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;

$orderModel = new BuOrder();
$orderCommodityModel = new BuOrderCommodity();

// 查询订单状态
$order = $orderModel->getOne([
    'where' => ['order_code' => 'ORDER_20250121001'],
    'field' => 'id,order_code,order_status,delivery_time,common_carrier,waybill_number'
]);

if ($order) {
    echo "订单信息:\n";
    echo "订单号: {$order['order_code']}\n";
    echo "订单状态: {$order['order_status']}\n";
    echo "发货时间: {$order['delivery_time']}\n";
    echo "物流公司: {$order['common_carrier']}\n";
    echo "物流单号: {$order['waybill_number']}\n";
}

// 查询订单商品状态
$commodities = $orderCommodityModel->getList([
    'where' => ['order_code' => 'ORDER_20250121001'],
    'field' => 'id,commodity_name,order_commodity_status,delivery_time,common_carrier,waybill_number'
]);

echo "商品信息:\n";
foreach ($commodities as $commodity) {
    echo "商品ID: {$commodity['id']}, 商品名: {$commodity['commodity_name']}, 状态: {$commodity['order_commodity_status']}\n";
}
?>
