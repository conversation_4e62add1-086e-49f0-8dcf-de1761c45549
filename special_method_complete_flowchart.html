<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Special方法完整调用流程图 - 详细版</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            box-sizing: border-box;
            min-width: 2000px;
            width: max-content;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .description {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }

        .description h3 {
            margin-top: 0;
            color: #1976d2;
        }

        .mermaid-container {
            width: 100%;
            overflow-x: auto;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
            padding: 10px;
            margin: 20px 0;
            max-height: 80vh;
        }

        .mermaid {
            text-align: left;
            margin: 0;
            min-width: 2000px;
            width: max-content;
            font-size: 14px;
        }

        .mermaid svg {
            max-width: none !important;
            width: auto !important;
            height: auto !important;
        }

        .legend {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        .legend h3 {
            margin-top: 0;
            color: #495057;
        }

        .legend-item {
            display: inline-block;
            margin: 5px 10px;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
        }

        .db-query {
            background-color: #ffcdd2;
            border: 2px solid #d32f2f;
        }

        .heavy-process {
            background-color: #ff5722;
            border: 3px solid #bf360c;
            color: white;
        }

        .cache-hit {
            background-color: #c8e6c9;
            border: 2px solid #388e3c;
        }

        .api-call {
            background-color: #fff3e0;
            border: 2px solid #f57c00;
        }

        .start-end {
            background-color: #e1f5fe;
            border: 2px solid #0277bd;
        }

        .error-path {
            background-color: #ffebee;
            border: 2px solid #c62828;
        }

        .export-buttons {
            text-align: center;
            margin: 20px 0;
        }

        .export-btn {
            margin: 0 10px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .export-btn:hover {
            background-color: #0056b3;
        }

        .performance-notes {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }

        .performance-notes h3 {
            color: #856404;
            margin-top: 0;
        }

        .performance-notes ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .performance-notes li {
            margin: 5px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 onclick="printDiagram()">Special方法完整调用流程图 - 详细版</h1>
      

        
        <!-- <div class="export-buttons">
            <button class="export-btn" onclick="exportSVG()">导出为SVG</button>
            <button class="export-btn" onclick="exportPNG()">导出为PNG</button>
            <button class="export-btn" onclick="printDiagram()">打印图表</button>
        </div> -->
        
        <div class="mermaid" id="mermaid-diagram">
graph TD
    A[Special方法开始] --> B[参数验证 SpecialValidate]
    B --> C[性能监控开始 microtime]
    C --> D[缓存Key生成]
    
    D --> E{专题基础信息缓存检查}
    E -->|缓存被禁用| F[强制数据库查询]
    E -->|缓存命中| G[返回缓存数据]
    F --> F1["SELECT title,data_json,bg_color,bg_img,is_dd,is_share,<br/>share_title,share_img,share_url,is_staff<br/>FROM t_db_special_sm<br/>WHERE page_type=? AND id=?"]
    F1 --> F2[Redis缓存写入 5-10小时]
    
    G --> H[二维码处理]
    F2 --> H
    H --> H1{根据page_type判断渠道}
    H1 -->|渠道1-日产| H2[QrCodeService::qrCode<br/>生成日产渠道二维码]
    H1 -->|渠道2-启辰| H3[QrCodeService::qrCode<br/>生成启辰渠道二维码]
    H1 -->|其他渠道| H4[跳过二维码生成]
    H2 --> H5[二维码缓存写入]
    H3 --> H5
    H4 --> I
    H5 --> I{员工验证条件检查<br/>is_staff=1?}
    
    I -->|是| J[员工验证流程]
    I -->|否| K[跳过员工验证]
    J --> J1[DbUser::getOneByPk<br/>用户信息查询]
    J1 --> J2{staff_phone存在?}
    J2 -->|是| J3[设置is_staff=2]
    J2 -->|否| J4[AcPhonelist::getOne<br/>电话验证查询]
    J4 --> J5{电话在列表中?}
    J5 -->|是| J6[更新用户员工电话<br/>DbUser::saveData]
    J5 -->|否| K
    J6 --> J3
    J3 --> K
    
    K --> L{工会认证条件检查}
    L -->|需要认证| M[工会认证流程]
    L -->|否| N[推荐数据获取]
    M --> M1[AcGongHuiInfo::getOneBySp<br/>工会信息查询]
    M1 --> M2{工会存在?}
    M2 -->|否| N
    M2 -->|是| M3[设置is_staff=3]
    M3 --> M4[AcHaveTradeList::getOne<br/>用户工会关联查询]
    M4 --> M5{用户已关联?}
    M5 -->|是| M6[设置trade_phone]
    M5 -->|否| M7[检查电话是否已关联工会]
    M7 --> M8{电话已关联?}
    M8 -->|是| M9[返回错误:已使用]
    M8 -->|否| M10[AcPhonelist::getOne<br/>检查工会电话列表]
    M10 --> M11{电话在工会列表?}
    M11 -->|是| M12[插入工会关联数据<br/>AcHaveTradeList::insertData]
    M11 -->|否| M13[设置have_trade_order=2]
    M12 --> M6
    M6 --> M13
    M13 --> M14[BuOrder复杂联表查询]
    M14 --> M15["SELECT a.*,b.waybill_number<br/>FROM t_bu_order a<br/>JOIN t_bu_order_commodity b<br/>ON a.order_code=b.order_code<br/>WHERE a.order_status NOT IN (1,3,8,18)<br/>AND b.commodity_id IN (...)<br/>AND user_id=? ORDER BY a.id DESC"]
    M15 --> M16{有订单?}
    M16 -->|是| M17[处理订单状态信息]
    M16 -->|否| N
    M17 --> N
    
    N --> N1[获取用户车辆信息<br/>user_car_type_redis]
    N1 --> N2[品牌检查]
    N2 -->|否| O[内容定制]
    N2 -->|是| N3{user_status=2?}
    N3 -->|否| N4{$this->brand=1?}
    N3 -->|是| O1[三路推荐数据并行获取]
    
    O1 --> P1[getCacheBdpVin]
    O1 --> P2[getAbsDataNew]
    N4 -->|是| P3[getCacheAbsMall]
    N4 -->|否| O2[准备推荐数据]
    
    P1 --> P1A{BDP推荐缓存检查}
    P1A -->|命中| P1B[返回缓存数据]
    P1A -->|未命中| P1C["DbBdpRecommend::getOne<br/>WHERE vin=?"]
    P1C --> P1D[缓存写入30-60秒]
    P1B --> O2
    P1D --> O2
    
    P2 --> P2A{ADS推荐缓存检查}
    P2A -->|命中| P2B[返回缓存数据]
    P2A -->|未命中| P2C["DbAdsRecommendedDatas::getOne<br/>WHERE vin=? AND recommend_type=?"]
    P2C --> P2D["DbAdsRecommendedDatas::column<br/>goods WHERE ..."]
    P2D --> P2E[JSON解析和数组合并处理<br/>嵌套循环处理]
    P2E --> P2F[缓存写入5-10分钟]
    P2B --> O2
    P2F --> O2
    
    P3 --> P3A{Mall推荐缓存检查}
    P3A -->|命中| P3B[返回缓存数据]
    P3A -->|未命中| P3C["DbAdsMallGoodsRefereeD::getOne<br/>WHERE vin=?"]
    P3C --> P3D["DbAdsMallGoodsRefereeD::column<br/>goods WHERE ..."]
    P3D --> P3E[JSON解析和数组合并处理]
    P3E --> P3F[缓存写入5-10分钟]
    P3B --> O2
    P3F --> O2
    
    O2[推荐数据汇总] --> O
    O --> O3[设置sm_type=2和set_id]
    O3 --> O4[生成sp_key缓存键]
    O4 --> R{商品数据缓存检查}
    R -->|缓存被强制禁用| S[执行GoodsCustomize::analyze]
    R -->|缓存命中| T[返回缓存数据]
    
    %% GoodsCustomize::analyze 详细流程
    S --> S1[JSON数据解析<br/>json_decode data_json]
    S1 --> S2[遍历data_json_info数组<br/>foreach循环开始]
    S2 --> S3{组件类型判断<br/>item type}
    
    S3 -->|cGoods/cWaterfallGoods商品组件| S4[商品数据处理分支]
    S3 -->|cCoupon卡券组件| S5[卡券数据处理分支]
    S3 -->|其他组件类型| S6[其他组件处理分支]
    
    S4 --> S4A[检查推荐数据是否存在<br/>data_bdp data_ads data_ads_like]
    S4A --> S4B[推荐数据分类处理<br/>data_tc套餐 data_bj备件 data_pt普通]
    S4B --> S4C[getRecommend方法调用<br/>获取推荐商品信息]
    S4C --> S4D[推荐数据整合处理<br/>recommend_info list]
    S4D --> S4E[商品分类和排序<br/>根据recommend_order]
    S4E --> S4F[商品去重处理<br/>have_id_all数组]
    S4F --> S4G[更新商品数据<br/>item attribute goods_data]
    S4G --> S7
    
    S5 --> S5A[检查定向人群范围<br/>range == 2]
    S5A --> S5B[提取卡券ID数组<br/>card_ids]
    S5B --> S5C[NetUser::canGetCards<br/>卡券权限查询]
    S5C --> S5D[NetCard::getUserCard<br/>用户卡券状态查询]
    S5D --> S5E[过滤不可领取卡券<br/>检查available_count]
    S5E --> S5F[更新卡券状态<br/>is_received vin article change_car]
    S5F --> S7
    
    S6 --> S7[analyze方法完成<br/>调用filterSpec方法]
    
    %% GoodsCustomize::filterSpec 详细流程
    S7 --> S7A[处理所有组件<br/>遍历data_json_info]
    S7A --> S7B{组件类型判断<br/>cLimit cSeckill cGoods cWaterfallGoods}
    S7B -->|cLimit| S7C[限时折扣商品处理]
    S7B -->|cSeckill| S7D[秒杀商品处理]
    S7B -->|cGoods/cWaterfallGoods| S7E[普通商品处理]
    S7B -->|其他| S7F[其他组件直接返回]
    
    S7C --> S7C1[DbLimitDiscountCommodity查询<br/>获取SKU价格信息]
    S7C1 --> S7G[调用NetGoods::goodsList]
    
    S7D --> S7D1[DbSeckillCommodity查询<br/>获取SKU价格信息]
    S7D1 --> S7G
    
    S7E --> S7G
    S7F --> S7H[filterSpec方法完成]
    
    S7G --> S7G1[NetGoods::goodsList<br/>获取实时商品数据]
    S7G1 --> S7G2[更新商品信息<br/>价格库存标签等]
    S7G2 --> S7G3[商品数量限制处理<br/>show_count切片]
    S7G3 --> S7H
    
    S7H --> S8[GoodsCustomize::activity调用]

    %% GoodsCustomize::activity 详细流程
    S8 --> S8A[JSON数据解析<br/>json_decode data_json]
    S8A --> S8B[return_data数组初始化<br/>WlzCrowdsLogs实例化]
    S8B --> S8C[Banner推荐数据处理<br/>ads_like数据检查]
    S8C --> S8C1{有广告喜好数据?}
    S8C1 -->|是| S8C2[BuGwRecommendationBannerCommodity查询<br/>获取Banner商品]
    S8C1 -->|否| S8D
    S8C2 --> S8D[商品ID收集循环<br/>遍历所有cGoods组件]
    S8D --> S8E[提取commodity_ids<br/>goods_aids数组]
    S8E --> S8F[NetGoods::goodsList<br/>重量级查询调用]

    %% NetGoods::goodsList 内部流程
    S8F --> S8F1[DbCommodityFlat::getCommodityListUSku<br/>商品平铺表查询]
    S8F1 --> S8F2["复杂多表联查:<br/>t_db_commodity_flat a<br/>JOIN t_db_commodity_set b<br/>JOIN t_db_commodity_sku c<br/>JOIN t_db_commodity_card_c card_c<br/>WHERE find_in_set channel up_down_channel_dlr"]
    S8F2 --> S8F3[商品库存价格计算<br/>final_price current_price]
    S8F3 --> S8F4[卡券关联查询<br/>card_id_arr处理]
    S8F4 --> S8F5[用户权限验证<br/>can_get_card_list]
    S8F5 --> S8G[商品列表数据返回<br/>goods_new_msg数组]

    S8G --> S8H[组件类型循环处理<br/>foreach data as v]
    S8H --> S8I{组件类型判断<br/>v type}

    %% 各种组件类型处理
    S8I -->|cLimit限时折扣| S8J[限时折扣处理]
    S8I -->|cGoods商品组件| S8K[商品组件处理]
    S8I -->|cWaterfallGoods瀑布流| S8K1[瀑布流商品处理]
    S8I -->|cSeckill秒杀组件| S8L[秒杀组件处理]
    S8I -->|cLottery抽奖组件| S8M[抽奖组件处理]
    S8I -->|cCoupon卡券组件| S8N[卡券组件处理]
    S8I -->|cCouponAndGoods卡券商品| S8N1[卡券商品组件处理]
    S8I -->|cCarousel轮播组件| S8O[轮播Banner处理]
    S8I -->|cAdvertising广告组件| S8P[广告组件处理]
    S8I -->|cAdvertising1广告组件1| S8P1[广告组件1处理]
    S8I -->|cAdvertising2广告组件2| S8P2[广告组件2处理]
    S8I -->|cFloatWindow浮窗组件| S8Q[浮窗组件处理]
    S8I -->|cFloatingWindow浮窗组件2| S8Q1[浮窗组件2处理]
    S8I -->|cTabs标签页组件| S8R1[标签页组件处理]
    S8I -->|cSuit套装组件| S8S1[套装组件处理]
    S8I -->|cCrowdfunding众筹组件| S8T1[众筹组件处理]

    %% 限时折扣处理
    S8J --> S8J1[check_activity活动验证<br/>DbLimitDiscount::getOne]
    S8J1 --> S8J2["WHERE id=? AND is_enable=1<br/>AND act_status=2"]
    S8J2 --> S8J3[活动信息添加到商品<br/>id title start_time end_time]
    S8J3 --> S8Z

    %% 商品组件处理
    S8K --> S8K1[商品数据与goodsList结果合并<br/>goods_new_msg匹配]
    S8K1 --> S8K2[价格标签信息更新<br/>price tag_name commodity_label]
    S8K2 --> S8K3[商品数量限制处理<br/>show_count切片]
    S8K3 --> S8Z

    %% 瀑布流商品处理
    S8K1 --> S8K1A[瀑布流商品数据处理]
    S8K1A --> S8K1B[瀑布流商品缓存<br/>cWaterfallGoods特殊处理]
    S8K1B --> S8K1C[设置分页信息<br/>total per_page current_page all_page]
    S8K1C --> S8K1D[商品数据切片<br/>array_slice 0-12]
    S8K1D --> S8Z

    %% 秒杀组件处理
    S8L --> S8L1[Seckill::getSeckillTime<br/>活动时间查询]
    S8L1 --> S8L2[秒杀商品状态验证<br/>end_time is_enable act_status]
    S8L2 --> S8L3[NetGoods::goodsList<br/>秒杀商品专用查询]
    S8L3 --> S8L4[秒杀库存和价格处理<br/>limited_stock final_price]
    S8L4 --> S8L5[商品排序<br/>有库存优先显示]
    S8L5 --> S8Z

    %% 抽奖组件处理
    S8M --> S8M1[DbDraw::getOne<br/>抽奖活动查询]
    S8M1 --> S8M2[活动时间验证<br/>end_time is_enable]
    S8M2 --> S8M3[HaoWan::getLotteryNum<br/>用户抽奖次数查询]
    S8M3 --> S8M4[抽奖信息更新<br/>game_id draw_url draw_num]
    S8M4 --> S8Z

    %% 卡券组件处理
    S8N --> S8N1[componentCoupon<br/>卡券组件处理方法]
    S8N1 --> S8N2[NetUser::canGetCards<br/>定向人群过滤]
    S8N2 --> S8N3[check_activity<br/>卡券活动验证]
    S8N3 --> S8N4[NetCard::getUserCard<br/>用户卡券状态查询]
    S8N4 --> S8N5[卡券可领取状态更新<br/>is_received available_count]
    S8N5 --> S8Z

    %% 卡券商品组件处理
    S8N1 --> S8N1A[卡券商品组件处理<br/>同时处理卡券和商品]
    S8N1A --> S8Z

    %% 轮播Banner处理
    S8O --> S8O1[Banner数据处理<br/>attribute imgs遍历]
    S8O1 --> S8O2[推荐逻辑类型判断<br/>recommended_logical_type]
    S8O2 --> S8O3{推荐逻辑类型}
    S8O3 -->|2后台配置| S8O4[后台配置Banner处理]
    S8O3 -->|4猜你喜欢| S8O5[猜你喜欢Banner处理]
    S8O4 --> S8O6[用户群组验证<br/>WlzCrowdsLogs::count]
    S8O5 --> S8O7[数据组推荐商品整合<br/>ads_like数据合并]
    S8O6 --> S8O8[Banner显示时间验证]
    S8O7 --> S8O8
    S8O8 --> S8Z

    %% 广告组件处理
    S8P --> S8P1[广告显示时间验证<br/>show_time start_time end_time]
    S8P1 --> S8P2[WlzCrowdsLogs::count<br/>用户群组验证查询]
    S8P2 --> S8P3[不符合条件的广告移除<br/>unset处理]
    S8P3 --> S8Z

    %% 广告组件1处理
    S8P1 --> S8P1A[广告组件1处理<br/>同广告组件处理逻辑]
    S8P1A --> S8Z

    %% 广告组件2处理
    S8P2 --> S8P2A[广告组件2处理<br/>同广告组件处理逻辑]
    S8P2A --> S8Z

    %% 浮窗组件处理
    S8Q --> S8Q1[浮窗类型判断<br/>attribute type]
    S8Q1 --> S8Q2{浮窗类型}
    S8Q2 -->|1单张优惠券| S8Q3[单张优惠券处理]
    S8Q2 -->|其他类型| S8Q4[其他类型处理]
    S8Q3 --> S8Q5[canGetCards<br/>卡券权限验证]
    S8Q5 --> S8Q6[NetCard::getUserCard<br/>单张卡券查询]
    S8Q6 --> S8Q7[浮窗显示状态更新<br/>is_received available_count]
    S8Q4 --> S8Q7
    S8Q7 --> S8Z

    %% 浮窗组件2处理
    S8Q1 --> S8Q1A[浮窗组件2处理<br/>同浮窗组件处理逻辑]
    S8Q1A --> S8Z

    %% 标签页组件处理
    S8R1 --> S8R1A[NetGoods::goodsClass<br/>获取商品分类]
    S8R1A --> S8R1B[商品分类缓存处理<br/>864000秒缓存]
    S8R1B --> S8R1C[设置is_classify标识]
    S8R1C --> S8Z

    %% 套装组件处理
    S8S1 --> S8S1A[check_activity<br/>套装活动验证]
    S8S1A --> S8S1B[BuCheapSuitIndex查询<br/>套装状态验证]
    S8S1B --> S8S1C[NetGoods::suitList<br/>套装商品查询]
    S8S1C --> S8S1D[套装价格和图片处理<br/>suit_price imgs]
    S8S1D --> S8Z

    %% 众筹组件处理
    S8T1 --> S8T1A[crowdfundList<br/>众筹信息查询]
    S8T1A --> S8T1B[DbCrowdfund复杂联表查询<br/>众筹商品信息]
    S8T1B --> S8T1C[DbCrowdfundOrder查询<br/>众筹订单统计]
    S8T1C --> S8T1D[众筹进度计算<br/>money sum_num peo_count]
    S8T1D --> S8T1E[众筹商品信息更新<br/>tag price crowdfund_info]
    S8T1E --> S8Z

    S8Z[组件处理完成] --> S8ZA{是否还有组件<br/>循环继续?}
    S8ZA -->|是| S8H
    S8ZA -->|否| S8ZB[activity方法完成<br/>返回return_data]

    S8ZB --> S9[商品数据缓存写入<br/>30-40分钟]
    S9 --> U
    T --> U[专题信息最终处理]

    U --> U1[Redis缓存写入<br/>专题信息缓存]
    U1 --> U2[Redis SADD集合管理<br/>缓存key管理]
    U2 --> U3[性能日志记录<br/>Logger::error]
    U3 --> V[返回JSON响应<br/>setResponseData]
    M9 --> V

    %% 样式定义
    classDef dbQuery fill:#ffcdd2,stroke:#d32f2f,stroke-width:2px
    classDef heavyProcess fill:#ff5722,stroke:#bf360c,stroke-width:3px
    classDef cacheHit fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    classDef apiCall fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef startEnd fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef errorPath fill:#ffebee,stroke:#c62828,stroke-width:2px

    %% 应用样式
    class A,V startEnd
    class F1,M15,P1C,P2C,P2D,P3C,P3D,S8J2,S8M1,S8P2,S7C1,S7D1,S7G1,S8F1,S8F2,S8C2,S8T1B,S8T1C,S8R1A,S8S1B,S8S1C dbQuery
    class S,S8F,S8F1,S8F2,S8L3,S7G,S7G1 heavyProcess
    class P1B,P2B,P3B,T cacheHit
    class H2,H3,S8M3,N1 apiCall
    class M9 errorPath
        </div>
        
        <!-- <div class="legend">
            <h3>图例说明</h3>
            <div class="legend-item start-end">开始/结束节点</div>
            <div class="legend-item db-query">数据库查询</div>
            <div class="legend-item heavy-process">重量级处理</div>
            <div class="legend-item cache-hit">缓存命中</div>
            <div class="legend-item api-call">第三方API调用</div>
            <div class="legend-item error-path">错误路径</div>
        </div> -->
        
        <!-- <div class="performance-notes">
            <h3>性能瓶颈点分析</h3>
            <ul>
                <li><strong>数据库查询密集</strong>：涉及多个表的连续查询，特别是工会验证和商品查询</li>
                <li><strong>缓存机制不完善</strong>：主缓存检索被注释掉，导致每次都查询数据库</li>
                <li><strong>复杂数据处理</strong>：analyze和activity方法执行复杂的数据转换</li>
                <li><strong>外部服务依赖</strong>：多次调用第三方服务，如推荐系统、抽奖服务等</li>
                <li><strong>重复数据处理</strong>：同一商品数据在不同组件中可能被重复处理</li>
                <li><strong>大量Redis操作</strong>：多个缓存键的创建和管理</li>
            </ul>
        </div> -->
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // 导出为SVG
        function exportSVG() {
            const svg = document.querySelector('#mermaid-diagram svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const svgUrl = URL.createObjectURL(svgBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = svgUrl;
                downloadLink.download = 'special_method_complete_flowchart.svg';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
            }
        }

        // 导出为PNG
        function exportPNG() {
            const svg = document.querySelector('#mermaid-diagram svg');
            if (svg) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const url = URL.createObjectURL(svgBlob);
                
                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);
                    URL.revokeObjectURL(url);
                    
                    canvas.toBlob(function(blob) {
                        const pngUrl = URL.createObjectURL(blob);
                        const downloadLink = document.createElement('a');
                        downloadLink.href = pngUrl;
                        downloadLink.download = 'special_method_complete_flowchart.png';
                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);
                    });
                };
                
                img.src = url;
            }
        }

        // 打印图表
        function printDiagram() {
            window.print();
        }
    </script>
</body>
</html>