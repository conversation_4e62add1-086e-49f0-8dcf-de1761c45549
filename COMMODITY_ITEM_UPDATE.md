# CommodityItem 字段更新说明

## 更新内容

根据 `pushOrderToPartner` 方法中的商品详情数据结构，为 `CommodityItem` schema 添加了缺失的字段。

## 📋 新增字段列表

### 商品基础信息
- `supplierCommodityCode` - 供应商商品代码 (`$item['third_sku_code']`)
- `commodityCode` - 商品代码 (`$item['commodity_id']`)
- `skuName` - SKU名称 (`$item['sku_info']`)
- `skuInfo` - SKU信息 (`$item['sku_info']`)

### 金额相关字段
- `couponDiscountAmount` - 卡券优惠金额 (`$item['card_all_dis']*100`)
- `originalTotalCostAmount` - 原始总成本金额 (`$item['actual_use_money']*100`)
- `logisticsDeliveryFee` - 物流配送费 (`$item['mail_price']*100`)
- `commodityPurchasePrice` - 商品采购价格 (`$item['cost_price']*100`)

### 积分字段
- `totalCostIntegral` - 总成本积分 (`$item['actual_point']*10`)

### 活动和供应商信息
- `couponInfo` - 卡券信息 (JSON格式)
- `activityId` - 活动ID (`$act_id`)
- `activityName` - 活动名称 (`$item['act_name']`)
- `supplierCompanyCode` - 供应商代码 (`$item['supplier']`)

### 税务信息
- `commodityTaxCode` - 商品税收代码 (`$item['tax_code']`)
- `commodityTaxRate` - 商品税率 (`$item['tax']`)

## 🔧 数据格式化处理

### 金额字段转换（分转元）
在控制器的 `detail` 方法中，以下字段会除以100转换为元：
- `commodityPrice`
- `commodityTotalPrice`
- `couponDiscountAmount`
- `originalTotalCostAmount`
- `logisticsDeliveryFee`
- `commodityPurchasePrice`

### 积分字段转换
- `totalCostIntegral` - 除以10进行转换

### 空值处理
所有字段都添加了空值保护，使用 `?? 0` 或 `?? ''` 避免空值错误。

## 📝 完整的 CommodityItem 结构

```json
{
  "orderDetailNo": "123",
  "supplierCommodityCode": "SUP_SKU001",
  "commodityCode": "COMM001",
  "commodityName": "测试商品",
  "skuCode": "SKU001",
  "skuName": "规格1",
  "skuInfo": "颜色:红色,尺寸:L",
  "commodityPrice": 100.00,
  "commodityQuantity": 2,
  "commodityTotalPrice": 200.00,
  "couponDiscountAmount": 10.00,
  "originalTotalCostAmount": 190.00,
  "totalCostIntegral": 100,
  "logisticsDeliveryFee": 5.00,
  "couponInfo": "[{\"coupon_id\":1,\"coupon_name\":\"优惠券\"}]",
  "activityId": 1,
  "activityName": "限时优惠活动",
  "activityType": "限时优惠",
  "supplierCompanyName": "供应商A",
  "supplierCompanyCode": "SUP_A",
  "commodityPurchasePrice": 80.00,
  "commodityTaxCode": "1010101000000000000",
  "commodityTaxRate": "13%"
}
```

## 🔄 更新的文件

1. **API文档**: `supplier_order_push_openapi.json`
   - 更新了 `CommodityItem` schema 定义
   - 添加了所有新字段的类型和描述
   - 更新了详情接口的示例数据

2. **控制器**: `application/admin_v2/controller/SupplierOrderPush.php`
   - 更新了 `detail` 方法中的商品详情格式化逻辑
   - 添加了积分字段的转换处理
   - 添加了空值保护

## 📊 字段映射关系

| API字段 | 数据库来源 | 转换规则 | 描述 |
|---------|------------|----------|------|
| `supplierCommodityCode` | `third_sku_code` | 直接映射 | 供应商商品代码 |
| `commodityCode` | `commodity_id` | 直接映射 | 商品代码 |
| `skuName` | `sku_info` | 直接映射 | SKU名称 |
| `skuInfo` | `sku_info` | 直接映射 | SKU信息 |
| `couponDiscountAmount` | `card_all_dis*100` | ÷100转元 | 卡券优惠金额 |
| `originalTotalCostAmount` | `actual_use_money*100` | ÷100转元 | 原始总成本 |
| `totalCostIntegral` | `actual_point*10` | ÷10转换 | 总成本积分 |
| `logisticsDeliveryFee` | `mail_price*100` | ÷100转元 | 物流配送费 |
| `couponInfo` | 卡券查询结果 | JSON格式 | 卡券信息 |
| `activityId` | 活动解析结果 | 直接映射 | 活动ID |
| `activityName` | `act_name` | 直接映射 | 活动名称 |
| `supplierCompanyCode` | `supplier` | 直接映射 | 供应商代码 |
| `commodityPurchasePrice` | `cost_price*100` | ÷100转元 | 采购价格 |
| `commodityTaxCode` | `tax_code` | 直接映射 | 税收代码 |
| `commodityTaxRate` | `tax` | 直接映射 | 税率 |

## ⚠️ 注意事项

1. **数据完整性**: 新增字段可能在某些历史数据中为空，已添加空值保护
2. **金额精度**: 所有金额字段都保持2位小数精度
3. **JSON格式**: `couponInfo` 字段为JSON字符串格式
4. **向后兼容**: 新增字段不影响现有功能

## 🚀 使用建议

1. **重新导入API文档**: 使用更新后的 `supplier_order_push_openapi.json`
2. **前端适配**: 前端需要适配新增的商品详情字段
3. **数据展示**: 可以展示更完整的商品信息，包括税务、供应商等信息
4. **测试验证**: 建议测试各种商品类型的数据返回是否正确

更新完成后，商品详情将包含完整的供应商订单推送信息，与 `pushOrderToPartner` 方法中的数据结构完全一致。
