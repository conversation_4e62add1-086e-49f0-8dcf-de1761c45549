<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component控制器方法分析 - 流程图与时序图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            text-align: center;
            color: #333;
        }
        .diagram-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
            overflow-x: auto;
        }
        .export-buttons {
            text-align: center;
            margin: 20px 0;
        }
        .export-btn {
            margin: 0 10px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .export-btn:hover {
            background-color: #0056b3;
        }
        .analysis {
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .analysis h3 {
            margin-top: 0;
            color: #495057;
        }
        .method-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .method-card {
            padding: 15px;
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        .method-card h4 {
            margin-top: 0;
            color: #007bff;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            padding: 15px;
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
        .performance-issue {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .performance-issue h4 {
            color: #856404;
            margin-top: 0;
        }
        .optimization-tip {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .optimization-tip h4 {
            color: #0c5460;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 onclick="printDiagram()">Component控制器方法分析</h1>
        <p style="text-align: center; color: #666;">
            分析 pageComponentList 和 componentDetail 两个核心方法的调用流程
        </p>
        
        <!-- <div class="export-buttons">
            <button class="export-btn" onclick="exportFlowchartSVG()">导出流程图SVG</button>
            <button class="export-btn" onclick="exportSequenceSVG()">导出时序图SVG</button>
            <button class="export-btn" onclick="printDiagram()">打印图表</button>
        </div> -->

        <!-- <div class="stats">
            <div class="stat-card">
                <div class="stat-number">2</div>
                <div class="stat-label">核心方法</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">11</div>
                <div class="stat-label">组件类型</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">10+</div>
                <div class="stat-label">数据库查询</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">3</div>
                <div class="stat-label">推荐服务</div>
            </div>
        </div> -->

        <!-- <div class="method-comparison">
            <div class="method-card">
                <h4>pageComponentList</h4>
                <p><strong>功能</strong>：加载所有组件列表</p>
                <p><strong>特点</strong>：</p>
                <ul>
                    <li>移除动态组件的详情配置</li>
                    <li>处理静态组件（广告、轮播）</li>
                    <li>支持千人千面推荐</li>
                    <li>用户群组过滤</li>
                    <li>时间范围验证</li>
                </ul>
                <p><strong>性能</strong>：中等，有缓存优化</p>
            </div>
            <div class="method-card">
                <h4>componentDetail</h4>
                <p><strong>功能</strong>：按需获取单个组件详情</p>
                <p><strong>特点</strong>：</p>
                <ul>
                    <li>支持11种组件类型</li>
                    <li>动态数据查询和处理</li>
                    <li>活动状态验证</li>
                    <li>商品数据实时查询</li>
                    <li>卡券权限验证</li>
                </ul>
                <p><strong>性能</strong>：较重，涉及多次数据库查询</p>
            </div>
        </div> -->
        
        <div class="diagram-section">
            <h2>流程图 - Component控制器完整调用流程</h2>
            <div class="mermaid" id="flowchart-diagram">
                <!-- 流程图将在这里渲染 -->
            </div>
        </div>
        
        <div class="diagram-section">
            <h2>时序图 - Component控制器UML时序图</h2>
            <div class="mermaid" id="sequence-diagram">
                <!-- 时序图将在这里渲染 -->
            </div>
        </div>
        
        <!-- <div class="analysis">
            <h3>架构分析</h3>
            
            <div class="performance-issue">
                <h4>⚠️ 性能瓶颈</h4>
                <ul>
                    <li><strong>千人千面查询</strong>：getAbsDataNew + NetGoods::goodsList + Banner关联查询</li>
                    <li><strong>组件详情重查询</strong>：componentDetail 中的 NetGoods::goodsList 重复调用</li>
                    <li><strong>活动验证查询</strong>：每个组件都需要验证活动状态</li>
                    <li><strong>用户群组查询</strong>：WlzCrowdsLogs 表的频繁查询</li>
                    <li><strong>缓存策略不一致</strong>：组件配置缓存时间过长，实时数据无缓存</li>
                </ul>
            </div>
            
            <div class="optimization-tip">
                <h4>💡 优化建议</h4>
                <ul>
                    <li><strong>批量查询优化</strong>：将多个组件的数据查询合并为批量查询</li>
                    <li><strong>缓存策略优化</strong>：为活动状态、用户群组等添加短期缓存</li>
                    <li><strong>推荐数据预处理</strong>：将千人千面数据预处理并缓存</li>
                    <li><strong>组件懒加载</strong>：非关键组件采用懒加载策略</li>
                    <li><strong>数据库索引优化</strong>：为频繁查询的字段添加复合索引</li>
                </ul>
            </div>
            
            <h4>🔄 调用模式分析</h4>
            <p><strong>两阶段加载模式</strong>：</p>
            <ol>
                <li><strong>第一阶段</strong>：pageComponentList 快速返回组件骨架，移除重数据</li>
                <li><strong>第二阶段</strong>：componentDetail 按需加载具体组件的详细数据</li>
            </ol>
            
            <p><strong>优势</strong>：</p>
            <ul>
                <li>首屏加载速度快</li>
                <li>减少不必要的数据传输</li>
                <li>支持组件级别的缓存策略</li>
            </ul>
            
            <p><strong>挑战</strong>：</p>
            <ul>
                <li>增加了接口调用次数</li>
                <li>组件间数据一致性需要保证</li>
                <li>错误处理更加复杂</li>
            </ul>
        </div> -->
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            }
        });

        // 流程图定义
        const flowchartDefinition = `graph TD
    A[Component控制器开始] --> B{请求方法判断}
    B -->|pageComponentList| C[pageComponentList方法]
    B -->|componentDetail| D[componentDetail方法]
    
    %% pageComponentList 流程
    C --> C1[参数验证 ComponentLayoutValidate]
    C1 --> C2[获取home_sm_id<br/>默认值处理]
    C2 --> C3[调用getComponentData<br/>获取组件配置]
    
    C3 --> C3A[构建缓存Key<br/>home:component_data:pageId]
    C3A --> C3B{检查Redis缓存}
    C3B -->|缓存命中| C3C[返回缓存数据]
    C3B -->|缓存未命中| C3D[数据库查询<br/>DbHomeSm::find]
    C3D --> C3E["SELECT data_json<br/>FROM t_db_home_sm<br/>WHERE id=?"]
    C3E --> C3F[Redis缓存写入<br/>3600-7200*10秒]
    C3F --> C3G[Redis SADD集合管理<br/>缓存key管理]
    C3G --> C3H[JSON解析<br/>json_decode_assoc]
    C3C --> C3H
    
    C3H --> C4[遍历组件数组<br/>生成component_id]
    C4 --> C5{组件类型判断<br/>richComponentType?}
    C5 -->|是动态组件| C6[移除attribute属性<br/>不返回详情配置]
    C5 -->|是静态组件| C7[保留完整配置]
    C6 --> C8[staticComponentProcess<br/>静态组件处理]
    C7 --> C8`;

        // 时序图定义（简化版）
        const sequenceDefinition = `sequenceDiagram
    participant Client as 客户端
    participant Controller as Component控制器
    participant Cache as Redis缓存
    participant DB as 数据库
    participant NetGoods as NetGoods服务
    participant GoodsCustomize as GoodsCustomize

    Note over Client,GoodsCustomize: Component控制器双接口调用时序

    Client->>Controller: GET /pageComponentList
    Controller->>Cache: 检查组件配置缓存
    alt 缓存未命中
        Controller->>DB: DbHomeSm::find查询
        DB-->>Controller: 组件配置JSON
        Controller->>Cache: 缓存写入
    else 缓存命中
        Cache-->>Controller: 返回缓存数据
    end
    Controller->>Controller: 静态组件处理
    Controller-->>Client: 返回组件列表

    Client->>Controller: GET /componentDetail
    Controller->>GoodsCustomize: componentHandle调用
    GoodsCustomize->>NetGoods: goodsList查询
    NetGoods->>DB: 商品详情查询
    DB-->>NetGoods: 商品数据
    NetGoods-->>GoodsCustomize: 商品列表
    GoodsCustomize-->>Controller: 处理后的组件数据
    Controller-->>Client: 返回组件详情`;

        // 渲染图表
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.render('flowchart-svg', flowchartDefinition).then(function(result) {
                document.getElementById('flowchart-diagram').innerHTML = result.svg;
            });
            
            mermaid.render('sequence-svg', sequenceDefinition).then(function(result) {
                document.getElementById('sequence-diagram').innerHTML = result.svg;
            });
        });

        // 导出功能
        function exportFlowchartSVG() {
            const svg = document.querySelector('#flowchart-diagram svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const svgUrl = URL.createObjectURL(svgBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = svgUrl;
                downloadLink.download = 'component_flowchart.svg';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
            }
        }

        function exportSequenceSVG() {
            const svg = document.querySelector('#sequence-diagram svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const svgUrl = URL.createObjectURL(svgBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = svgUrl;
                downloadLink.download = 'component_sequence.svg';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
            }
        }

        function printDiagram() {
            window.print();
        }
    </script>
</body>
</html>
