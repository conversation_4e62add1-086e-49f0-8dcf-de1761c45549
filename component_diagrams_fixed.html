<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component控制器流程图与时序图 - 修复版</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            text-align: center;
            color: #333;
            cursor: pointer;
        }
        .diagram-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
            overflow-x: auto;
        }
        .export-buttons {
            text-align: center;
            margin: 20px 0;
        }
        .export-btn {
            margin: 0 10px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .export-btn:hover {
            background-color: #0056b3;
        }
        .legend {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
            font-size: 14px;
        }
        .legend h4 {
            margin-top: 0;
            color: #495057;
        }
        .legend-item {
            display: inline-block;
            margin: 5px 10px;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
        }
        .db-query { background-color: #ffcdd2; border: 2px solid #d32f2f; }
        .heavy-process { background-color: #ff5722; border: 3px solid #bf360c; color: white; }
        .cache-hit { background-color: #c8e6c9; border: 2px solid #388e3c; }
        .api-call { background-color: #fff3e0; border: 2px solid #f57c00; }
        .start-end { background-color: #e1f5fe; border: 2px solid #0277bd; }
    </style>
</head>
<body>
    <div class="container">
        <h1 onclick="printDiagram()">Component控制器流程图与时序图 - 修复版</h1>
        <p style="text-align: center; color: #666;">
            pageComponentList 和 componentDetail 两个核心方法的完整调用流程分析
        </p>
        
        <div class="export-buttons">
            <button class="export-btn" onclick="exportFlowchartSVG()">导出流程图SVG</button>
            <button class="export-btn" onclick="exportSequenceSVG()">导出时序图SVG</button>
            <button class="export-btn" onclick="printDiagram()">打印图表</button>
        </div>
        
        <div class="diagram-section">
            <h2>流程图 - Component控制器完整调用流程</h2>
            <div class="mermaid" id="flowchart-diagram">
flowchart TD
    A[Component控制器开始] --> B{请求方法判断}
    B -->|pageComponentList| C[pageComponentList方法]
    B -->|componentDetail| D[componentDetail方法]
    
    %% pageComponentList 流程
    C --> C1[参数验证]
    C1 --> C2[获取home_sm_id默认值处理]
    C2 --> C3[调用getComponentData获取组件配置]
    
    C3 --> C3A[构建缓存Key]
    C3A --> C3B{检查Redis缓存}
    C3B -->|缓存命中| C3C[返回缓存数据]
    C3B -->|缓存未命中| C3D[数据库查询DbHomeSm]
    C3D --> C3E[SELECT data_json FROM t_db_home_sm WHERE id=?]
    C3E --> C3F[Redis缓存写入]
    C3F --> C3G[Redis SADD集合管理]
    C3G --> C3H[JSON解析]
    C3C --> C3H
    
    C3H --> C4[遍历组件数组生成component_id]
    C4 --> C5{组件类型判断richComponentType}
    C5 -->|是动态组件| C6[移除attribute属性]
    C5 -->|是静态组件| C7[保留完整配置]
    C6 --> C8[staticComponentProcess静态组件处理]
    C7 --> C8
    
    %% staticComponentProcess 详细流程
    C8 --> C8A[getUserGroups获取用户群组]
    C8A --> C8B[WlzCrowdsLogs查询WHERE user_id=?]
    C8B --> C8C[提取crowd_id数组]
    
    C8C --> C8D[检查是否需要千人千面]
    C8D --> C8E{推荐逻辑类型包含4猜你喜欢}
    C8E -->|是| C8F[调用getAbsDataNew数据组推荐]
    C8E -->|否| C8G[跳过推荐数据获取]
    
    C8F --> C8F1[getAbsDataNew调用WHERE vin=?]
    C8F1 --> C8F2[提取commodity_id数组]
    C8F2 --> C8F3[NetGoods::goodsList商品详情查询]
    C8F3 --> C8F4[BuGwRecommendationBannerCommodity查询]
    C8F4 --> C8F5[复杂联表查询Banner商品关联]
    C8F5 --> C8G
    
    C8G --> C8H[组件类型循环处理foreach componentList]
    C8H --> C8I{组件类型判断}
    
    %% 广告组件处理
    C8I -->|cAdvertising类型| C8J[广告组件时间验证]
    C8J --> C8J1[遍历imgs数组检查show_time]
    C8J1 --> C8J2{时间是否有效}
    C8J2 -->|无效| C8J3[移除该广告]
    C8J2 -->|有效| C8J4[用户群组验证]
    C8J4 --> C8J5{用户是否在群组中}
    C8J5 -->|否| C8J3
    C8J5 -->|是| C8J6[保留广告]
    C8J3 --> C8J7[检查下一个广告]
    C8J6 --> C8J7
    C8J7 --> C8K
    
    %% 轮播组件处理
    C8I -->|cCarousel轮播| C8L[轮播组件处理]
    C8L --> C8L1[获取推荐逻辑配置]
    C8L1 --> C8L2{推荐逻辑类型判断}
    C8L2 -->|类型2后台配置| C8L3[后台配置图片处理]
    C8L2 -->|类型4猜你喜欢| C8L4[千人千面推荐处理]
    
    C8L3 --> C8L3A[遍历imgs数组时间和群组验证]
    C8L3A --> C8L3B[图片排序处理sort_order]
    C8L3B --> C8L3C[渠道适配GWAPP/GWSM取首个]
    C8L3C --> C8K
    
    C8L4 --> C8L4A[匹配banner_commodity_list推荐商品数据]
    C8L4A --> C8L4B{是否固定顶部isFixedTop}
    C8L4B -->|是| C8L4C[匹配image_type=1]
    C8L4B -->|否| C8L4D[匹配bannerSize类型]
    C8L4C --> C8L4E[构建商品跳转数据savePath配置]
    C8L4D --> C8L4E
    C8L4E --> C8L4F{推荐数据是否匹配}
    C8L4F -->|否| C8L4G[使用默认配置数据]
    C8L4F -->|是| C8K
    C8L4G --> C8K
    
    %% 其他组件处理
    C8I -->|cTabs标签| C8M[设置is_classify=1]
    C8I -->|其他组件| C8N[保持原样]
    C8M --> C8K
    C8N --> C8K
    
    C8K[组件处理完成] --> C8O{是否还有组件循环继续}
    C8O -->|是| C8H
    C8O -->|否| C9[返回组件列表array_values重新索引]
    
    C9 --> C10[返回JSON响应setResponseData]
    
    %% componentDetail 流程
    D --> D1[参数验证ComponentDetailValidate]
    D1 --> D2[获取home_sm_id默认值处理]
    D2 --> D3[调用getComponentData获取组件配置]
    D3 --> D4[解析component_id提取索引]
    D4 --> D5[根据索引获取组件]
    D5 --> D6{组件是否存在}
    D6 -->|否| D7[返回空组件数据]
    D6 -->|是| D8{是否广告类组件需要act_code}
    D8 -->|是且缺少act_code| D9[返回错误act_code不能为空]
    D8 -->|否或有act_code| D10[GoodsCustomize::componentHandle组件详情处理]
    
    %% GoodsCustomize::componentHandle 详细流程
    D10 --> D10A{组件类型判断switch component type}
    
    D10A -->|cLottery抽奖| D11[componentLottery处理]
    D11 --> D11A[DbDraw::getOne抽奖活动查询]
    D11A --> D11B[活动时间验证end_time is_enable]
    D11B --> D11C[HaoWan::getLotteryNum用户抽奖次数查询]
    D11C --> D11D[抽奖信息更新]
    D11D --> D12
    
    D10A -->|cSeckill秒杀| D13[componentSeckill处理]
    D13 --> D13A[Seckill::getSeckillTime活动时间查询]
    D13A --> D13B[秒杀商品状态验证]
    D13B --> D13C[NetGoods::goodsList秒杀商品查询]
    D13C --> D13D[秒杀库存和价格处理]
    D13D --> D13E[商品排序有库存优先]
    D13E --> D12
    
    D10A -->|cLimit限时折扣| D14[componentLimit处理]
    D14 --> D14A[check_activity活动验证]
    D14A --> D14B[WHERE id=? AND is_enable=1 AND act_status=2]
    D14B --> D14C[活动信息添加到商品]
    D14C --> D12
    
    D10A -->|cCoupon卡券| D17[componentCoupon处理]
    D17 --> D17A[定向人群过滤range == 2检查]
    D17A --> D17B[NetUser::canGetCards用户权限验证]
    D17B --> D17C[check_activity卡券活动验证]
    D17C --> D17D[NetCard::getUserCard用户卡券状态查询]
    D17D --> D17E[卡券状态更新]
    D17E --> D12
    
    D10A -->|cGoods商品| D19[componentGoods处理]
    D19 --> D19A[解析find_commodity_ids商品ID列表]
    D19A --> D19B[获取goods_data商品配置数据]
    D19B --> D19C[NetGoods::goodsList商品详情查询]
    D19C --> D19D[商品数据合并处理]
    D19D --> D19E[价格标签信息更新]
    D19E --> D19F[分页处理current_page page_size]
    D19F --> D12
    
    D12[组件处理完成] --> D22[返回处理后的组件数据]
    D7 --> D23[返回JSON响应setResponseData]
    D9 --> D23
    D22 --> D23
    
    %% 样式定义
    classDef dbQuery fill:#ffcdd2,stroke:#d32f2f,stroke-width:2px
    classDef heavyProcess fill:#ff5722,stroke:#bf360c,stroke-width:3px
    classDef cacheHit fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    classDef apiCall fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef startEnd fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    
    %% 应用样式
    class A,C10,D23 startEnd
    class C3E,C8B,C8F5,D11A,D13A,D14B,D17C,D17D,D19C dbQuery
    class C8F3,D10,D13C,D19C heavyProcess
    class C3C cacheHit
    class C8F1,D11C apiCall
            </div>
            
            <div class="legend">
                <h4>流程图图例说明</h4>
                <div class="legend-item start-end">开始/结束节点</div>
                <div class="legend-item db-query">数据库查询</div>
                <div class="legend-item heavy-process">重量级处理</div>
                <div class="legend-item cache-hit">缓存命中</div>
                <div class="legend-item api-call">第三方API调用</div>
            </div>
        </div>

        <div class="diagram-section">
            <h2>时序图 - Component控制器UML时序图</h2>
            <div class="mermaid" id="sequence-diagram">
sequenceDiagram
    participant Client as 客户端
    participant Controller as Component控制器
    participant Cache as Redis缓存
    participant DB as 数据库
    participant NetGoods as NetGoods服务
    participant GoodsCustomize as GoodsCustomize
    participant UserGroup as 用户群组服务
    participant Recommend as 推荐服务
    participant Activity as 活动服务
    participant Card as 卡券服务

    Note over Client,Card: Component控制器双接口调用时序

    %% pageComponentList 流程
    rect rgb(240, 248, 255)
        Note over Client,Card: pageComponentList - 组件列表加载

        Client->>Controller: GET /pageComponentList
        Controller->>Controller: 参数验证和默认值处理

        Controller->>Cache: 检查组件配置缓存
        alt 缓存未命中
            Controller->>DB: DbHomeSm::find查询
            Note over DB: SELECT data_json FROM t_db_home_sm WHERE id=?
            DB-->>Controller: 组件配置JSON
            Controller->>Cache: 缓存写入
        else 缓存命中
            Cache-->>Controller: 返回缓存数据
        end

        Controller->>Controller: JSON解析和组件数组遍历

        Controller->>UserGroup: getUserGroups
        UserGroup->>DB: WlzCrowdsLogs查询
        DB-->>UserGroup: 用户群组数据
        UserGroup-->>Controller: crowd_id数组

        alt 包含推荐逻辑类型4
            Controller->>Recommend: getAbsDataNew调用
            Recommend->>DB: 数据组推荐查询
            DB-->>Recommend: 推荐商品数据
            Recommend-->>Controller: commodity_id数组

            Controller->>NetGoods: goodsList查询
            NetGoods->>DB: 商品详情查询
            DB-->>NetGoods: 商品信息
            NetGoods-->>Controller: 商品列表

            Controller->>DB: Banner商品关联查询
            DB-->>Controller: Banner商品数据
        end

        loop 每个组件
            alt cAdvertising广告组件
                Controller->>Controller: 时间验证和用户群组验证
            else cCarousel轮播组件
                Controller->>Controller: 推荐逻辑处理
            else 其他组件
                Controller->>Controller: 保持原样
            end
        end

        Controller-->>Client: 返回组件列表JSON
    end

    %% componentDetail 流程
    rect rgb(248, 255, 248)
        Note over Client,Card: componentDetail - 按需组件详情

        Client->>Controller: GET /componentDetail
        Controller->>Controller: 参数验证和组件获取

        alt 组件不存在
            Controller-->>Client: 返回空数据
        else 组件存在
            Controller->>GoodsCustomize: componentHandle调用

            alt cLottery抽奖组件
                GoodsCustomize->>DB: DbDraw::getOne
                DB-->>GoodsCustomize: 抽奖活动信息
                GoodsCustomize->>Activity: HaoWan::getLotteryNum
                Activity-->>GoodsCustomize: 用户抽奖次数

            else cSeckill秒杀组件
                GoodsCustomize->>Activity: Seckill::getSeckillTime
                Activity-->>GoodsCustomize: 活动时间
                GoodsCustomize->>NetGoods: goodsList秒杀专用
                NetGoods->>DB: 秒杀商品查询
                DB-->>NetGoods: 秒杀商品数据
                NetGoods-->>GoodsCustomize: 商品结果

            else cCoupon卡券组件
                GoodsCustomize->>Card: NetUser::canGetCards
                Card-->>GoodsCustomize: 用户权限结果
                GoodsCustomize->>Activity: check_activity验证
                Activity-->>GoodsCustomize: 卡券活动状态
                GoodsCustomize->>Card: NetCard::getUserCard
                Card-->>GoodsCustomize: 用户卡券状态

            else cGoods商品组件
                GoodsCustomize->>NetGoods: goodsList查询
                NetGoods->>DB: 商品详情查询
                DB-->>NetGoods: 商品数据
                NetGoods-->>GoodsCustomize: 商品列表
            end

            GoodsCustomize-->>Controller: 处理后的组件数据
            Controller-->>Client: 返回组件详情JSON
        end
    end

    Note over Client,Card: 双接口协作完成组件系统
            </div>

            <div class="legend">
                <h4>时序图说明</h4>
                <p><strong>蓝色区域</strong>：pageComponentList 流程 - 快速返回组件骨架</p>
                <p><strong>绿色区域</strong>：componentDetail 流程 - 按需加载组件详情</p>
                <p><strong>关键特点</strong>：两阶段加载模式，首屏快速，按需详情</p>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            }
        });

        // 导出流程图为SVG
        function exportFlowchartSVG() {
            const svg = document.querySelector('#flowchart-diagram svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const svgUrl = URL.createObjectURL(svgBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = svgUrl;
                downloadLink.download = 'component_flowchart_fixed.svg';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                URL.revokeObjectURL(svgUrl);
            }
        }

        // 导出时序图为SVG
        function exportSequenceSVG() {
            const svg = document.querySelector('#sequence-diagram svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const svgUrl = URL.createObjectURL(svgBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = svgUrl;
                downloadLink.download = 'component_sequence_fixed.svg';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                URL.revokeObjectURL(svgUrl);
            }
        }

        // 打印图表
        function printDiagram() {
            window.print();
        }
    </script>
</body>
</html>
