sequenceDiagram
    participant Client as 客户端
    participant Controller as Component控制器
    participant Validator as 参数验证器
    participant C<PERSON> as Redis缓存
    participant DB as 数据库
    participant NetGoods as NetGoods服务
    participant GoodsCustomize as GoodsCustomize
    participant UserGroup as 用户群组服务
    participant Recommend as 推荐服务
    participant Activity as 活动服务
    participant Card as 卡券服务

    Note over Client,Card: Component控制器双接口调用时序

    %% pageComponentList 流程
    rect rgb(240, 248, 255)
        Note over Client,Card: pageComponentList - 组件列表加载
        
        Client->>Controller: GET /pageComponentList
        Controller->>Validator: ComponentLayoutValidate验证
        Validator-->>Controller: 验证通过
        Controller->>Controller: home_sm_id默认值处理
        
        %% getComponentData 调用
        Controller->>Controller: getComponentData调用
        Controller->>Cache: 检查组件配置缓存
        alt 缓存未命中
            Controller->>DB: DbHomeSm::find查询
            Note over DB: SELECT data_json<br/>FROM t_db_home_sm<br/>WHERE id=?
            DB-->>Controller: 组件配置JSON
            Controller->>Cache: 缓存写入(3600-7200*10秒)
            Controller->>Cache: SADD集合管理
        else 缓存命中
            Cache-->>Controller: 返回缓存数据
        end
        Controller->>Controller: JSON解析
        
        %% 组件列表处理
        Controller->>Controller: 遍历组件数组
        loop 每个组件
            Controller->>Controller: 生成component_id
            alt 动态组件(richComponentType)
                Controller->>Controller: 移除attribute属性
            else 静态组件
                Controller->>Controller: 保留完整配置
            end
        end
        
        %% staticComponentProcess 处理
        Controller->>UserGroup: getUserGroups
        UserGroup->>DB: WlzCrowdsLogs查询
        Note over DB: WHERE user_id=?
        DB-->>UserGroup: 用户群组数据
        UserGroup-->>Controller: crowd_id数组
        
        %% 千人千面检查
        Controller->>Controller: 检查cCarousel组件
        alt 包含推荐逻辑类型4
            Controller->>Recommend: getAbsDataNew调用
            Recommend->>DB: 数据组推荐查询
            DB-->>Recommend: 推荐商品数据
            Recommend-->>Controller: commodity_id数组
            
            Controller->>NetGoods: goodsList查询
            NetGoods->>DB: 商品详情查询
            DB-->>NetGoods: 商品信息
            NetGoods-->>Controller: 商品列表
            
            Controller->>DB: BuGwRecommendationBannerCommodity查询
            Note over DB: 复杂联表查询<br/>Banner商品关联
            DB-->>Controller: Banner商品数据
        end
        
        %% 组件类型处理循环
        loop 每个组件
            alt cAdvertising广告组件
                Controller->>Controller: 时间验证(show_time)
                Controller->>Controller: 用户群组验证
                Controller->>Controller: 过滤无效广告
            else cCarousel轮播组件
                alt 推荐逻辑类型2(后台配置)
                    Controller->>Controller: 图片时间群组验证
                    Controller->>Controller: 排序处理
                    Controller->>Controller: 渠道适配
                else 推荐逻辑类型4(猜你喜欢)
                    Controller->>Controller: 匹配推荐商品数据
                    alt 固定顶部
                        Controller->>Controller: 匹配image_type=1
                    else 非固定顶部
                        Controller->>Controller: 匹配bannerSize类型
                    end
                    Controller->>Controller: 构建商品跳转数据
                    alt 推荐数据未匹配
                        Controller->>Controller: 使用默认配置
                    end
                end
            else cTabs标签组件
                Controller->>Controller: 设置is_classify=1
            else 其他组件
                Controller->>Controller: 保持原样
            end
        end
        
        Controller->>Controller: array_values重新索引
        Controller-->>Client: 返回组件列表JSON
    end

    %% componentDetail 流程
    rect rgb(248, 255, 248)
        Note over Client,Card: componentDetail - 按需组件详情

        Client->>Controller: GET /componentDetail
        Controller->>Validator: ComponentDetailValidate验证
        Validator-->>Controller: 验证通过
        Controller->>Controller: home_sm_id默认值处理

        Controller->>Controller: getComponentData调用
        Note over Controller: 复用相同缓存逻辑
        Controller->>Controller: 解析component_id
        Controller->>Controller: 根据索引获取组件

        alt 组件不存在
            Controller-->>Client: 返回空数据
        else 组件存在
            alt 广告类组件且缺少act_code
                Controller-->>Client: 返回错误(act_code不能为空)
            else 正常处理
                Controller->>GoodsCustomize: componentHandle调用

                %% 组件类型处理分支
                alt cLottery抽奖组件
                    GoodsCustomize->>DB: DbDraw::getOne
                    DB-->>GoodsCustomize: 抽奖活动信息
                    GoodsCustomize->>GoodsCustomize: 活动时间验证
                    GoodsCustomize->>Activity: HaoWan::getLotteryNum
                    Activity-->>GoodsCustomize: 用户抽奖次数
                    GoodsCustomize->>GoodsCustomize: 抽奖信息更新

                else cSeckill秒杀组件
                    GoodsCustomize->>Activity: Seckill::getSeckillTime
                    Activity-->>GoodsCustomize: 活动时间
                    GoodsCustomize->>GoodsCustomize: 商品状态验证
                    GoodsCustomize->>NetGoods: goodsList(秒杀专用)
                    NetGoods->>DB: 秒杀商品查询
                    DB-->>NetGoods: 秒杀商品数据
                    NetGoods-->>GoodsCustomize: 商品结果
                    GoodsCustomize->>GoodsCustomize: 库存价格处理
                    GoodsCustomize->>GoodsCustomize: 商品排序

                else cLimit限时折扣组件
                    GoodsCustomize->>Activity: check_activity验证
                    Activity->>DB: DbLimitDiscount::getOne
                    Note over DB: WHERE id=? AND is_enable=1<br/>AND act_status=2
                    DB-->>Activity: 活动信息
                    Activity-->>GoodsCustomize: 活动验证结果
                    GoodsCustomize->>GoodsCustomize: 活动信息添加

                else cCrowdfunding众筹组件
                    GoodsCustomize->>GoodsCustomize: 活动商品验证
                    GoodsCustomize->>GoodsCustomize: crowdfundList查询

                else cSuit套餐组件
                    GoodsCustomize->>Activity: check_activity验证
                    Activity-->>GoodsCustomize: 活动状态
                    GoodsCustomize->>DB: BuCheapSuitIndex查询
                    DB-->>GoodsCustomize: 套餐状态
                    GoodsCustomize->>NetGoods: suitList查询
                    NetGoods->>DB: 套餐商品查询
                    DB-->>NetGoods: 套餐商品数据
                    NetGoods-->>GoodsCustomize: 套餐结果

                else cCoupon卡券组件
                    GoodsCustomize->>GoodsCustomize: 定向人群过滤
                    GoodsCustomize->>Card: NetUser::canGetCards
                    Card-->>GoodsCustomize: 用户权限结果
                    GoodsCustomize->>Activity: check_activity验证
                    Activity-->>GoodsCustomize: 卡券活动状态
                    GoodsCustomize->>Card: NetCard::getUserCard
                    Card-->>GoodsCustomize: 用户卡券状态
                    GoodsCustomize->>GoodsCustomize: 卡券状态更新

                else cFloatWindow浮窗组件
                    GoodsCustomize->>GoodsCustomize: 浮窗类型判断
                    alt 单张卡券类型
                        GoodsCustomize->>Card: canGetCards权限验证
                        Card-->>GoodsCustomize: 权限结果
                        GoodsCustomize->>Card: NetCard::getUserCard
                        Card-->>GoodsCustomize: 卡券详情
                        GoodsCustomize->>GoodsCustomize: 浮窗状态更新
                    else 其他类型
                        GoodsCustomize->>GoodsCustomize: 其他类型处理
                    end

                else cGoods商品组件
                    GoodsCustomize->>GoodsCustomize: 解析find_commodity_ids
                    GoodsCustomize->>GoodsCustomize: 获取goods_data配置
                    GoodsCustomize->>NetGoods: goodsList查询
                    NetGoods->>DB: 商品详情查询
                    Note over DB: 复杂多表联查
                    DB-->>NetGoods: 商品数据
                    NetGoods-->>GoodsCustomize: 商品列表
                    GoodsCustomize->>GoodsCustomize: 商品数据合并
                    GoodsCustomize->>GoodsCustomize: 价格标签更新
                    GoodsCustomize->>GoodsCustomize: 分页处理

                else cCarousel轮播组件
                    GoodsCustomize->>GoodsCustomize: 解析act_code参数
                    GoodsCustomize->>GoodsCustomize: 遍历imgs查找匹配
                    GoodsCustomize->>GoodsCustomize: 提取couponList
                    GoodsCustomize->>GoodsCustomize: cardDraw卡券抽奖

                else cAdvertising广告组件
                    GoodsCustomize->>GoodsCustomize: 根据act_code匹配
                    GoodsCustomize->>GoodsCustomize: 提取couponList
                    GoodsCustomize->>GoodsCustomize: cardDraw卡券抽奖
                end

                GoodsCustomize-->>Controller: 处理后的组件数据
                Controller-->>Client: 返回组件详情JSON
            end
        end
    end

    Note over Client,Card: 双接口协作完成组件系统
