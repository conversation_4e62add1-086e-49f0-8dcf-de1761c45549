<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Special方法UML时序图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
            overflow-x: auto;
        }
        .description {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .description h3 {
            margin-top: 0;
            color: #495057;
        }
        .phase {
            margin: 15px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background-color: #e7f3ff;
        }
        .phase h4 {
            margin: 0 0 10px 0;
            color: #0056b3;
        }
        .export-buttons {
            text-align: center;
            margin: 20px 0;
        }
        .export-btn {
            margin: 0 10px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .export-btn:hover {
            background-color: #0056b3;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            padding: 15px;
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Special方法UML时序图</h1>
        
        <div class="export-buttons">
            <button class="export-btn" onclick="exportSVG()">导出为SVG</button>
            <button class="export-btn" onclick="exportPNG()">导出为PNG</button>
            <button class="export-btn" onclick="printDiagram()">打印图表</button>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">13</div>
                <div class="stat-label">参与组件</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">9</div>
                <div class="stat-label">主要阶段</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">15+</div>
                <div class="stat-label">数据库查询</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">8</div>
                <div class="stat-label">组件类型</div>
            </div>
        </div>
        
        <div class="mermaid" id="sequence-diagram">
sequenceDiagram
    participant Client as 客户端
    participant Controller as HomeSpecialAuth
    participant Validator as SpecialValidate
    participant Cache as Redis缓存
    participant DB as 数据库
    participant QrCode as 二维码服务
    participant SMS as 短信服务
    participant BDP as BDP推荐服务
    participant ADS as ADS推荐服务
    participant Mall as Mall推荐服务
    participant Goods as GoodsCustomize
    participant NetGoods as NetGoods服务
    participant Activity as 活动服务
    participant Card as 卡券服务

    Note over Client,Card: Special方法完整调用时序

    %% 1. 请求初始化阶段
    Client->>Controller: POST /special 请求
    Controller->>Validator: 参数验证
    Validator-->>Controller: 验证通过
    Controller->>Controller: 性能监控开始
    Controller->>Controller: 缓存Key生成

    %% 2. 专题基础信息获取
    Controller->>Cache: 检查专题信息缓存
    alt 缓存被禁用
        Controller->>DB: SELECT FROM t_db_special_sm
        DB-->>Controller: 专题基础信息
        Controller->>Cache: 写入缓存(5-10小时)
    else 缓存命中
        Cache-->>Controller: 返回缓存数据
    end

    %% 3. 二维码处理
    Controller->>Controller: 根据page_type判断渠道
    alt 渠道1-日产
        Controller->>QrCode: rcQrCode调用
        QrCode->>QrCode: CarLive API
        QrCode-->>Controller: 二维码数据
    else 渠道2-启辰
        Controller->>QrCode: qcQrCode调用
        QrCode->>QrCode: InterFun API
        QrCode-->>Controller: 二维码数据
    else 渠道3-pz1a
        Controller->>QrCode: pzQrCode调用
        QrCode->>QrCode: Pz1a API
        QrCode-->>Controller: 二维码数据
    end
    Controller->>Cache: 二维码缓存写入

    %% 4. 员工验证(条件触发)
    alt is_staff=1
        Controller->>DB: DbUser::getOneByPk 用户信息查询
        DB-->>Controller: 用户信息
        Controller->>DB: AcPhonelist::getOne 电话验证
        DB-->>Controller: 电话验证结果
        alt 验证失败
            Controller->>SMS: SendSms::send_sms
            SMS->>SMS: BaseTool::sms_send API调用
            SMS-->>Controller: 短信发送结果
        end
    end

    %% 5. 工会认证(条件触发)
    alt 需要工会认证
        Controller->>DB: AcGongHuiInfo::getOneBySp
        DB-->>Controller: 工会信息
        Controller->>DB: AcHaveTradeList::getOne
        DB-->>Controller: 交易记录
        Controller->>DB: BuOrder复杂联表查询
        Note over DB: 复杂JOIN查询<br/>t_bu_order + t_bu_order_commodity
        DB-->>Controller: 订单数据
    end

    %% 6. 推荐数据并行获取
    par BDP推荐
        Controller->>BDP: getCacheBdpVin
        BDP->>Cache: 检查BDP缓存
        alt 缓存未命中
            BDP->>DB: DbBdpRecommend::getOne
            DB-->>BDP: BDP推荐数据
            BDP->>Cache: 缓存写入(30-60秒)
        else 缓存命中
            Cache-->>BDP: 返回缓存数据
        end
        BDP-->>Controller: BDP推荐结果
    and ADS推荐
        Controller->>ADS: getAbsDataNew
        ADS->>Cache: 检查ADS缓存
        alt 缓存未命中
            ADS->>DB: DbAdsRecommendedDatas::getOne
            DB-->>ADS: 基础数据
            ADS->>DB: DbAdsRecommendedDatas::column
            DB-->>ADS: 商品数据
            ADS->>ADS: JSON解析和数组合并
            ADS->>Cache: 缓存写入(5-10分钟)
        else 缓存命中
            Cache-->>ADS: 返回缓存数据
        end
        ADS-->>Controller: ADS推荐结果
    and Mall推荐
        Controller->>Mall: getCacheAbsMall
        Mall->>Cache: 检查Mall缓存
        alt 缓存未命中
            Mall->>DB: DbAdsMallGoodsRefereeD::getOne
            DB-->>Mall: 基础数据
            Mall->>DB: DbAdsMallGoodsRefereeD::column
            DB-->>Mall: 商品数据
            Mall->>Mall: JSON解析和数组合并
            Mall->>Cache: 缓存写入(5-10分钟)
        else 缓存命中
            Cache-->>Mall: 返回缓存数据
        end
        Mall-->>Controller: Mall推荐结果
    end

    %% 7. 商品数据解析 - GoodsCustomize::analyze
    Controller->>Cache: 检查商品数据缓存
    Note over Cache: 缓存被强制禁用
    Controller->>Goods: GoodsCustomize::analyze调用

    Goods->>Goods: JSON数据解析
    loop 遍历组件数组
        alt cGoods商品组件
            Goods->>Goods: 检查推荐数据
            Goods->>Goods: getRecommend方法调用
            Goods->>Goods: 推荐数据整合处理
            Goods->>Goods: 商品分类和排序
            Goods->>Goods: 商品去重处理
        else cCoupon卡券组件
            Goods->>Card: NetUser::canGetCards
            Card-->>Goods: 卡券权限结果
            Goods->>Card: NetCard::getUserCard
            Card-->>Goods: 用户卡券状态
            Goods->>Goods: 过滤不可领取卡券
        else 其他组件
            Goods->>Goods: 其他组件处理
        end
    end
    Goods-->>Controller: analyze处理完成

    %% 8. GoodsCustomize::activity 详细处理
    Controller->>Goods: GoodsCustomize::activity调用

    Goods->>Goods: JSON数据解析
    Goods->>Goods: return_data数组初始化
    Goods->>Goods: Banner推荐数据处理
    Goods->>Goods: 商品ID收集循环

    %% NetGoods::goodsList 重量级查询
    Goods->>NetGoods: NetGoods::goodsList调用
    NetGoods->>DB: DbCommodityFlat::getCommodityListUSku
    Note over DB: 复杂多表联查<br/>t_db_commodity_flat<br/>+ t_db_commodity_set<br/>+ t_db_commodity_sku<br/>+ t_db_commodity_card_c
    DB-->>NetGoods: 商品基础数据
    NetGoods->>NetGoods: 商品库存价格计算
    NetGoods->>NetGoods: 卡券关联查询
    NetGoods->>NetGoods: 用户权限验证
    NetGoods-->>Goods: 商品列表数据

    %% 组件类型循环处理
    loop 组件类型处理
        alt cLimit限时折扣
            Goods->>Activity: check_activity活动验证
            Activity->>DB: DbLimitDiscount::getOne
            DB-->>Activity: 活动信息
            Activity-->>Goods: 活动验证结果
        else cGoods商品组件
            Goods->>Goods: 商品数据合并处理
            Goods->>Goods: 价格标签信息更新
            Goods->>Goods: 商品数量限制处理
            alt cWaterfallGoods瀑布流
                Goods->>Cache: 瀑布流商品缓存
            end
        else cSeckill秒杀组件
            Goods->>Activity: Seckill::getSeckillTime
            Activity-->>Goods: 活动时间
            Goods->>Goods: 秒杀商品状态验证
            Goods->>NetGoods: NetGoods::goodsList(秒杀专用)
            NetGoods->>DB: 秒杀商品查询
            DB-->>NetGoods: 秒杀商品数据
            NetGoods-->>Goods: 秒杀商品结果
            Goods->>Goods: 库存价格处理和排序
        else cLottery抽奖组件
            Goods->>DB: DbDraw::getOne
            DB-->>Goods: 抽奖活动信息
            Goods->>Activity: HaoWan::getLotteryNum
            Activity-->>Goods: 用户抽奖次数
            Goods->>Goods: 抽奖信息更新
        else cCoupon卡券组件
            Goods->>Card: componentCoupon处理
            Card->>Card: NetUser::canGetCards
            Card->>Activity: check_activity验证
            Activity-->>Card: 活动状态
            Card->>Card: NetCard::getUserCard
            Card-->>Goods: 卡券状态更新
        else cCarousel轮播组件
            Goods->>Goods: Banner数据处理
            Goods->>Goods: 推荐逻辑类型判断
            Goods->>Goods: 数据组推荐商品整合
        else cAdvertising广告组件
            Goods->>Goods: 广告显示时间验证
            Goods->>DB: WlzCrowdsLogs::count
            DB-->>Goods: 用户群组验证结果
            Goods->>Goods: 不符合条件广告移除
        else cFloatWindow浮窗组件
            Goods->>Goods: 浮窗类型判断
            Goods->>Card: canGetCards权限验证
            Card-->>Goods: 权限结果
            Goods->>Card: NetCard::getUserCard
            Card-->>Goods: 卡券详情
            Goods->>Goods: 浮窗状态更新
        end
    end

    Goods-->>Controller: activity处理完成

    %% 9. 最终处理
    Controller->>Cache: 商品数据缓存写入(30-40分钟)
    Controller->>Cache: Redis缓存写入
    Controller->>Cache: Redis SADD集合管理
    Controller->>Controller: 性能日志记录
    Controller-->>Client: 返回JSON响应

    Note over Client,Card: 完整调用流程结束
        </div>
        
        <div class="description">
            <h3>时序图说明</h3>
            
            <div class="phase">
                <h4>阶段1: 请求初始化</h4>
                <p>客户端发起请求，控制器进行参数验证和性能监控初始化。</p>
            </div>
            
            <div class="phase">
                <h4>阶段2: 专题基础信息获取</h4>
                <p>从数据库查询专题页基础信息，由于缓存被禁用，每次都需要查询数据库。</p>
            </div>
            
            <div class="phase">
                <h4>阶段3: 二维码处理</h4>
                <p>根据不同渠道调用相应的二维码生成服务，支持日产、启辰、pz1a三个渠道。</p>
            </div>
            
            <div class="phase">
                <h4>阶段4-5: 条件验证</h4>
                <p>员工验证和工会认证只在特定条件下触发，包含复杂的数据库查询。</p>
            </div>
            
            <div class="phase">
                <h4>阶段6: 推荐数据并行获取</h4>
                <p>同时从BDP、ADS、Mall三个推荐服务获取数据，每个服务都有独立的缓存策略。</p>
            </div>
            
            <div class="phase">
                <h4>阶段7-8: 商品数据解析</h4>
                <p>GoodsCustomize的analyze和activity方法是最复杂的部分，包含8种不同组件类型的处理。</p>
            </div>
            
            <div class="phase">
                <h4>阶段9: 最终处理</h4>
                <p>缓存写入、性能日志记录，最终返回JSON响应给客户端。</p>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            }
        });

        // 导出为SVG
        function exportSVG() {
            const svg = document.querySelector('#sequence-diagram svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const svgUrl = URL.createObjectURL(svgBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = svgUrl;
                downloadLink.download = 'special_method_sequence_diagram.svg';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
            }
        }

        // 导出为PNG
        function exportPNG() {
            const svg = document.querySelector('#sequence-diagram svg');
            if (svg) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const url = URL.createObjectURL(svgBlob);
                
                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);
                    URL.revokeObjectURL(url);
                    
                    canvas.toBlob(function(blob) {
                        const pngUrl = URL.createObjectURL(blob);
                        const downloadLink = document.createElement('a');
                        downloadLink.href = pngUrl;
                        downloadLink.download = 'special_method_sequence_diagram.png';
                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);
                    });
                };
                
                img.src = url;
            }
        }

        // 打印图表
        function printDiagram() {
            window.print();
        }
    </script>
</body>
</html>
