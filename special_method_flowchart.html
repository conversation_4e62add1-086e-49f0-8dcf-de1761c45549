<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Special方法完整调用流程图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
        }
        .legend {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .legend h3 {
            margin-top: 0;
            color: #495057;
        }
        .legend-item {
            display: inline-block;
            margin: 5px 10px;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
        }
        .db-query { background-color: #ffcdd2; border: 2px solid #d32f2f; }
        .heavy-process { background-color: #ff5722; border: 3px solid #bf360c; color: white; }
        .cache-hit { background-color: #c8e6c9; border: 2px solid #388e3c; }
        .api-call { background-color: #fff3e0; border: 2px solid #f57c00; }
        .start-end { background-color: #e1f5fe; border: 2px solid #0277bd; }
        .export-buttons {
            text-align: center;
            margin: 20px 0;
        }
        .export-btn {
            margin: 0 10px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .export-btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Special方法完整调用流程图</h1>
        
        <!-- <div class="export-buttons">
            <button class="export-btn" onclick="exportSVG()">导出为SVG</button>
            <button class="export-btn" onclick="exportPNG()">导出为PNG</button>
            <button class="export-btn" onclick="printDiagram()">打印图表</button>
        </div> -->
        
        <div class="mermaid" id="mermaid-diagram">
graph TD
    A[Special方法开始] --> B[参数验证 SpecialValidate]
    B --> C[性能监控开始 microtime]
    C --> D[缓存Key生成]
    
    D --> E{专题基础信息缓存检查}
    E -->|缓存被禁用| F[强制数据库查询]
    E -->|缓存命中| G[返回缓存数据]
    F --> F1["SELECT title,data_json,bg_color,bg_img<br/>FROM t_db_special_sm<br/>WHERE page_type=? AND id=?"]
    F1 --> F2[Redis缓存写入 5-10小时]
    
    G --> H[二维码处理]
    F2 --> H
    H --> H1{根据page_type判断渠道}
    H1 -->|渠道1-日产| H2[QrCodeService::rcQrCode<br/>CarLive API调用]
    H1 -->|渠道2-启辰| H3[QrCodeService::qcQrCode<br/>InterFun API调用]
    H1 -->|渠道3-pz1a| H4[QrCodeService::pzQrCode<br/>Pz1a API调用]
    H2 --> H5[二维码缓存写入]
    H3 --> H5
    H4 --> H5
    
    H5 --> I{员工验证条件检查<br/>is_staff=1?}
    I -->|是| J[员工验证流程]
    I -->|否| K[跳过员工验证]
    J --> J1[DbUser::getOneByPk<br/>用户信息查询]
    J1 --> J2[AcPhonelist::getOne<br/>电话验证查询]
    J2 --> J3{验证结果}
    J3 -->|失败| J4[SendSms::send_sms<br/>发送短信验证码]
    J3 -->|成功| K
    J4 --> J5[BaseTool::sms_send<br/>API调用]
    J5 --> K
    
    K --> L{工会认证条件检查}
    L -->|需要认证| M[工会认证流程]
    L -->|否| N[推荐数据获取]
    M --> M1[AcGongHuiInfo::getOneBySp<br/>工会信息查询]
    M1 --> M2[AcHaveTradeList::getOne<br/>交易记录查询]
    M2 --> M3[BuOrder复杂联表查询]
    M3 --> M4["SELECT a.*,b.waybill_number<br/>FROM t_bu_order a<br/>JOIN t_bu_order_commodity b<br/>ON a.order_code=b.order_code<br/>WHERE a.order_status NOT IN (1,3,8,18)<br/>AND b.commodity_id IN (...)<br/>AND user_id=? ORDER BY a.id DESC"]
    M4 --> N
    
    N --> O[三路推荐数据并行获取]
    O --> P1[getCacheBdpVin]
    O --> P2[getAbsDataNew]
    O --> P3[getCacheAbsMall]
    
    P1 --> P1A{BDP推荐缓存检查}
    P1A -->|命中| P1B[返回缓存数据]
    P1A -->|未命中| P1C["DbBdpRecommend::getOne<br/>WHERE vin=?"]
    P1C --> P1D[缓存写入30-60秒]
    P1B --> Q
    P1D --> Q
    
    P2 --> P2A{ADS推荐缓存检查}
    P2A -->|命中| P2B[返回缓存数据]
    P2A -->|未命中| P2C["DbAdsRecommendedDatas::getOne<br/>WHERE vin=? AND recommend_type=?"]
    P2C --> P2D["DbAdsRecommendedDatas::column<br/>goods WHERE ..."]
    P2D --> P2E[JSON解析和数组合并处理<br/>嵌套循环处理]
    P2E --> P2F[缓存写入5-10分钟]
    P2B --> Q
    P2F --> Q
    
    P3 --> P3A{Mall推荐缓存检查}
    P3A -->|命中| P3B[返回缓存数据]
    P3A -->|未命中| P3C["DbAdsMallGoodsRefereeD::getOne<br/>WHERE vin=?"]
    P3C --> P3D["DbAdsMallGoodsRefereeD::column<br/>goods WHERE ..."]
    P3D --> P3E[JSON解析和数组合并处理]
    P3E --> P3F[缓存写入5-10分钟]
    P3B --> Q
    P3F --> Q
    
    Q[推荐数据汇总] --> R{商品数据缓存检查}
    R -->|缓存被强制禁用| S[执行GoodsCustomize::analyze]
    R -->|缓存命中| T[返回缓存数据]

    %% GoodsCustomize::analyze 详细流程
    S --> S1[JSON数据解析<br/>json_decode data_json]
    S1 --> S2[遍历data_json_info数组<br/>foreach循环开始]
    S2 --> S3{组件类型判断<br/>item type}

    S3 -->|cGoods商品组件| S4[商品数据处理分支]
    S3 -->|cCoupon卡券组件| S5[卡券数据处理分支]
    S3 -->|其他组件类型| S6[其他组件处理分支]

    S4 --> S4A[检查推荐数据是否存在<br/>data_bdp data_ads]
    S4A --> S4B[getRecommend方法调用<br/>获取推荐商品信息]
    S4B --> S4C[推荐数据整合处理<br/>recommend_info list]
    S4C --> S4D[商品分类和排序<br/>根据recommend_order]
    S4D --> S4E[商品去重处理<br/>have_id_all数组]
    S4E --> S4F[更新商品数据<br/>item attribute goods_data]
    S4F --> S7

    S5 --> S5A[检查定向人群范围<br/>range == 2]
    S5A --> S5B[提取卡券ID数组<br/>card_ids]
    S5B --> S5C[NetUser::canGetCards<br/>卡券权限查询]
    S5C --> S5D[NetCard::getUserCard<br/>用户卡券状态查询]
    S5D --> S5E[过滤不可领取卡券]
    S5E --> S7

    S6 --> S7[analyze方法完成<br/>返回处理后的data_json]

    S7 --> S8[GoodsCustomize::activity调用]

    %% GoodsCustomize::activity 详细流程
    S8 --> S8A[JSON数据解析<br/>json_decode data_json]
    S8A --> S8B[return_data数组初始化<br/>WlzCrowdsLogs实例化]
    S8B --> S8C[Banner推荐数据处理<br/>ads_like数据检查]
    S8C --> S8D[商品ID收集循环<br/>遍历所有cGoods组件]
    S8D --> S8E[提取commodity_ids<br/>goods_aids数组]
    S8E --> S8F[NetGoods::goodsList<br/>重量级查询调用]

    %% NetGoods::goodsList 内部流程
    S8F --> S8F1[DbCommodityFlat::getCommodityListUSku<br/>商品平铺表查询]
    S8F1 --> S8F2["复杂多表联查:<br/>t_db_commodity_flat a<br/>JOIN t_db_commodity_set b<br/>JOIN t_db_commodity_sku c<br/>JOIN t_db_commodity_card_c card_c<br/>WHERE find_in_set channel up_down_channel_dlr"]
    S8F2 --> S8F3[商品库存价格计算<br/>final_price current_price]
    S8F3 --> S8F4[卡券关联查询<br/>card_id_arr处理]
    S8F4 --> S8F5[用户权限验证<br/>can_get_card_list]
    S8F5 --> S8G[商品列表数据返回<br/>goods_new_msg数组]

    S8G --> S8H[组件类型循环处理<br/>foreach data as v]
    S8H --> S8I{组件类型判断<br/>v type}

    %% 各种组件类型处理
    S8I -->|cLimit限时折扣| S8J[限时折扣处理]
    S8I -->|cGoods商品组件| S8K[商品组件处理]
    S8I -->|cSeckill秒杀组件| S8L[秒杀组件处理]
    S8I -->|cLottery抽奖组件| S8M[抽奖组件处理]
    S8I -->|cCoupon卡券组件| S8N[卡券组件处理]
    S8I -->|cCarousel轮播组件| S8O[轮播Banner处理]
    S8I -->|cAdvertising广告组件| S8P[广告组件处理]
    S8I -->|cFloatWindow浮窗组件| S8Q[浮窗组件处理]

    %% 限时折扣处理
    S8J --> S8J1[check_activity活动验证<br/>DbLimitDiscount::getOne]
    S8J1 --> S8J2["WHERE id=? AND is_enable=1<br/>AND act_status=2"]
    S8J2 --> S8J3[活动信息添加到商品]
    S8J3 --> S8R

    %% 商品组件处理
    S8K --> S8K1[商品数据与goodsList结果合并<br/>goods_new_msg匹配]
    S8K1 --> S8K2[价格标签信息更新<br/>price tag_name commodity_label]
    S8K2 --> S8K3[商品数量限制处理<br/>show_count切片]
    S8K3 --> S8K4[瀑布流商品缓存<br/>cWaterfallGoods特殊处理]
    S8K4 --> S8R

    %% 秒杀组件处理
    S8L --> S8L1[Seckill::getSeckillTime<br/>活动时间查询]
    S8L1 --> S8L2[秒杀商品状态验证<br/>end_time is_enable act_status]
    S8L2 --> S8L3[NetGoods::goodsList<br/>秒杀商品专用查询]
    S8L3 --> S8L4[秒杀库存和价格处理<br/>limited_stock final_price]
    S8L4 --> S8L5[商品排序<br/>有库存优先显示]
    S8L5 --> S8R

    %% 抽奖组件处理
    S8M --> S8M1[DbDraw::getOne<br/>抽奖活动查询]
    S8M1 --> S8M2[活动时间验证<br/>end_time is_enable]
    S8M2 --> S8M3[HaoWan::getLotteryNum<br/>用户抽奖次数查询]
    S8M3 --> S8M4[抽奖信息更新<br/>game_id draw_url draw_num]
    S8M4 --> S8R

    %% 卡券组件处理
    S8N --> S8N1[componentCoupon<br/>卡券组件处理方法]
    S8N1 --> S8N2[NetUser::canGetCards<br/>定向人群过滤]
    S8N2 --> S8N3[check_activity<br/>卡券活动验证]
    S8N3 --> S8N4[NetCard::getUserCard<br/>用户卡券状态查询]
    S8N4 --> S8N5[卡券可领取状态更新<br/>is_received available_count]
    S8N5 --> S8R

    %% 轮播Banner处理
    S8O --> S8O1[Banner数据处理<br/>attribute imgs遍历]
    S8O1 --> S8O2[推荐逻辑类型判断<br/>recommended_logical_type]
    S8O2 --> S8O3[数据组推荐商品整合<br/>ads_like数据合并]
    S8O3 --> S8R

    %% 广告组件处理
    S8P --> S8P1[广告显示时间验证<br/>show_time start_time end_time]
    S8P1 --> S8P2[WlzCrowdsLogs::count<br/>用户群组验证查询]
    S8P2 --> S8P3[不符合条件的广告移除<br/>unset处理]
    S8P3 --> S8R

    %% 浮窗组件处理
    S8Q --> S8Q1[浮窗类型判断<br/>attribute type]
    S8Q1 --> S8Q2[canGetCards<br/>卡券权限验证]
    S8Q2 --> S8Q3[NetCard::getUserCard<br/>单张卡券查询]
    S8Q3 --> S8Q4[浮窗显示状态更新<br/>is_received available_count]
    S8Q4 --> S8R

    S8R[组件处理完成] --> S8S{是否还有组件<br/>循环继续?}
    S8S -->|是| S8H
    S8S -->|否| S8T[activity方法完成<br/>返回return_data]

    S8T --> S9[商品数据缓存写入<br/>30-40分钟]
    S9 --> U
    T --> U[专题信息最终处理]

    U --> U1[Redis缓存写入<br/>专题信息缓存]
    U1 --> U2[Redis SADD集合管理<br/>缓存key管理]
    U2 --> U3[性能日志记录<br/>Logger::error]
    U3 --> V[返回JSON响应<br/>setResponseData]

    %% 样式定义
    classDef dbQuery fill:#ffcdd2,stroke:#d32f2f,stroke-width:2px
    classDef heavyProcess fill:#ff5722,stroke:#bf360c,stroke-width:3px
    classDef cacheHit fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    classDef apiCall fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef startEnd fill:#e1f5fe,stroke:#0277bd,stroke-width:2px

    %% 应用样式
    class A,V startEnd
    class F1,M4,P1C,P2C,P2D,P3C,P3D,S8J2,S8M1,S8P2 dbQuery
    class S,S8F,S8F1,S8F2,S8L3 heavyProcess
    class P1B,P2B,P3B,T cacheHit
    class H2,H3,H4,J5,S8M3 apiCall
        </div>
        
        <div class="legend">
            <h3>图例说明</h3>
            <div class="legend-item start-end">开始/结束节点</div>
            <div class="legend-item db-query">数据库查询</div>
            <div class="legend-item heavy-process">重量级处理</div>
            <div class="legend-item cache-hit">缓存命中</div>
            <div class="legend-item api-call">第三方API调用</div>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // 导出为SVG
        function exportSVG() {
            const svg = document.querySelector('#mermaid-diagram svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const svgUrl = URL.createObjectURL(svgBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = svgUrl;
                downloadLink.download = 'special_method_flowchart.svg';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
            }
        }

        // 导出为PNG
        function exportPNG() {
            const svg = document.querySelector('#mermaid-diagram svg');
            if (svg) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const url = URL.createObjectURL(svgBlob);
                
                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);
                    URL.revokeObjectURL(url);
                    
                    canvas.toBlob(function(blob) {
                        const pngUrl = URL.createObjectURL(blob);
                        const downloadLink = document.createElement('a');
                        downloadLink.href = pngUrl;
                        downloadLink.download = 'special_method_flowchart.png';
                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);
                    });
                };
                
                img.src = url;
            }
        }

        // 打印图表
        function printDiagram() {
            window.print();
        }
    </script>
</body>
</html>
