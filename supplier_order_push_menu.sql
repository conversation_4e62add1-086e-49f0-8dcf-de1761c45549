-- 供应商订单推送报表菜单配置
-- 需要根据实际的菜单结构调整父级菜单ID

-- 查找合适的父级菜单（订单管理或报表管理）
-- SELECT * FROM t_sys_menu WHERE menu_name LIKE '%订单%' OR menu_name LIKE '%报表%' ORDER BY id;

-- 假设订单管理的菜单ID为某个值，这里需要根据实际情况调整
-- 添加供应商订单推送报表菜单项
INSERT INTO `t_sys_menu` (
    `menu_name`, 
    `menu_url`, 
    `menu_pid`, 
    `menu_rank`, 
    `controller`, 
    `method`, 
    `left_view`, 
    `sort`, 
    `is_enable`,
    `web_menu_url`
) VALUES (
    '供应商订单推送报表', 
    'SupplierOrderPush/index', 
    0, -- 这里需要根据实际的父级菜单ID调整，0表示顶级菜单
    2, -- 菜单级别：1-一级菜单，2-二级菜单，3-三级菜单
    'SupplierOrderPush', 
    'index', 
    1, -- 是否在左侧菜单显示：1-显示，0-不显示
    100, -- 排序值
    1, -- 是否启用：1-启用，0-禁用
    '' -- web菜单URL，如果有的话
);

-- 如果需要添加到现有的订单管理菜单下，可以使用以下语句：
-- 首先查找订单管理菜单的ID
-- UPDATE语句示例（需要根据实际情况调整）：
-- UPDATE `t_sys_menu` SET `menu_pid` = (SELECT id FROM t_sys_menu WHERE menu_name = '订单管理' LIMIT 1) WHERE menu_name = '供应商订单推送报表';

-- 或者如果要添加到报表管理下：
-- UPDATE `t_sys_menu` SET `menu_pid` = (SELECT id FROM t_sys_menu WHERE menu_name = '报表管理' LIMIT 1) WHERE menu_name = '供应商订单推送报表';
