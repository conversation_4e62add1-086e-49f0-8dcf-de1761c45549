# 合作伙伴回调服务

## 概述

本服务用于处理合作伙伴的发货回调，当合作伙伴发货后会调用我们的接口，我们根据回调信息更新订单状态和物流信息。

## 功能特性

1. **订单状态更新**: 根据`orderDetailNo`（对应`order_code`）更新订单状态为已发货（状态4）
2. **商品状态更新**: 根据`orderCommodityId`（对应`id`）更新BuOrderCommodity表的商品订单状态为已发货（状态4）
3. **物流信息记录**: 记录物流公司、物流单号、发货时间等信息
4. **回调日志记录**: 完整记录回调请求和处理结果
5. **事务处理**: 确保数据一致性
6. **错误处理**: 完善的异常处理和日志记录

## 文件结构

```
application/
├── common/
│   ├── service/
│   │   └── PartnerCallbackService.php          # 回调处理服务
│   └── model/
│       └── inter/
│           └── IPartnerCallbackLog.php         # 回调日志模型
└── net_small/
    └── controller/
        └── PartnerCallback.php                 # 回调控制器

partner_callback_log_table.sql                 # 数据库表结构
test_partner_callback.php                      # 测试文件
README_partner_callback.md                     # 说明文档
```

## API接口

### 1. 发货回调接口

**接口地址**: `POST /net-small/partner-callback/delivery`

**请求头**: `Content-Type: application/json`

**请求参数**:
```json
{
  "orderDetailNo": "订单详情编号",
  "deliveryList": [
    {
      "deliveryNo": "发货单号",
      "logisticsCompany": "物流公司代码",
      "logisticsCompanyName": "物流公司名称",
      "logisticsNo": "物流单号",
      "deliveryTime": "发货时间",
      "deliveryStatus": "发货状态",
      "commodityList": [
        {
          "orderCommodityId": "订单商品ID",
          "commodityName": "商品名称",
          "skuCode": "SKU代码",
          "supplierCommodityCode": "供应商商品代码",
          "commodityQuantity": 数量,
          "commodityOrderStatus": "商品订单状态"
        }
      ]
    }
  ]
}
```

**成功响应**:
```json
{
  "code": 200,
  "message": "回调处理成功",
  "data": {
    "order_code": "订单编号",
    "updated_order_status": 4,
    "updated_commodities": 1
  },
  "timestamp": **********
}
```

**错误响应**:
```json
{
  "code": 400,
  "message": "错误信息",
  "data": [],
  "timestamp": **********
}
```

### 2. 健康检查接口

**接口地址**: `GET /net-small/partner-callback/health`

**响应**:
```json
{
  "code": 200,
  "message": "合作伙伴回调服务正常运行",
  "data": [],
  "timestamp": **********
}
```

## 数据库表

### t_i_partner_callback_log (合作伙伴回调日志表)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int(11) | 主键ID |
| callback_type | varchar(50) | 回调类型 |
| order_detail_no | varchar(50) | 订单详情编号 |
| request_url | varchar(255) | 请求URL |
| request_method | varchar(10) | 请求方法 |
| request_params | text | 请求参数(JSON) |
| response_data | text | 响应数据(JSON) |
| process_status | tinyint(4) | 处理状态(0-处理中 1-成功 2-失败) |
| error_message | text | 错误信息 |
| created_date | datetime | 创建时间 |
| last_updated_date | datetime | 最后更新时间 |
| remark | varchar(255) | 备注 |

## 部署步骤

1. **创建数据库表**:
   ```sql
   -- 执行 partner_callback_log_table.sql 中的SQL语句
   ```

2. **部署代码文件**:
   - 将服务文件放到对应目录
   - 确保文件权限正确

3. **配置路由**:
   - 路由已配置在 `config/net_small.php` 中
   - 发货回调路径: `/net-small/partner-callback/delivery`
   - 健康检查路径: `/net-small/partner-callback/health`

4. **测试接口**:
   ```bash
   # 使用test_partner_callback.php中的测试数据
   php test_partner_callback.php
   ```

## 订单状态说明

- **订单状态**: 4 = 已发货
- **商品订单状态**: 4 = 已发货

## 错误处理

1. **参数验证**: 检查必要参数是否存在
2. **订单验证**: 检查订单是否存在
3. **事务处理**: 确保数据更新的原子性
4. **日志记录**: 记录所有操作和错误信息
5. **异常捕获**: 捕获并处理所有异常

## 监控和日志

1. **回调日志**: 所有回调请求都会记录在`t_i_partner_callback_log`表中
2. **系统日志**: 使用Logger记录关键操作和错误信息
3. **状态监控**: 可通过`process_status`字段监控处理状态

## 注意事项

1. **幂等性**: 服务支持重复调用，不会重复更新状态
2. **数据验证**: 严格验证输入参数，防止无效数据
3. **性能考虑**: 使用事务确保数据一致性，但要注意事务时间
4. **安全性**: 建议添加签名验证或IP白名单等安全措施

## 扩展功能

1. **签名验证**: 可添加请求签名验证
2. **IP白名单**: 限制调用来源
3. **重试机制**: 处理失败时的重试逻辑
4. **通知机制**: 处理完成后的通知功能
