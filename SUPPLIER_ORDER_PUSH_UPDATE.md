# 供应商订单推送报表API更新说明

## 更新内容

根据 `pushOrderToPartner` 方法中的 `BuSupplierOrderPushLog` 表结构，已更新API接口返回的字段。

## 📋 新增返回字段

### 获取推送列表数据接口 (`/admin_v2/supplier_order_push/getList`)

在原有字段基础上，新增以下字段：

#### 金额相关字段：
- `delivery_fee` - 运费（元）
- `coupon_discount_amount` - 卡券优惠（元）
- `activity_discount_amount` - 活动优惠（元）

#### 订单信息字段：
- `receipt_address` - 收货地址
- `remark` - 备注
- `payment_method` - 支付方式
- `order_paid_time` - 支付时间

#### 用户信息字段：
- `register_mobile` - 注册手机号

#### 系统字段：
- `delivery_callback_url` - 发货回调地址
- `split_time` - 分账时间
- `split_status` - 分账状态（1-未分账，2-已分账）
- `total_payment_integral` - 积分抵扣

## 🔧 字段格式化说明

### 金额字段转换
数据库中以"分"为单位存储，API返回时转换为"元"：
- `total_amount` ÷ 100
- `delivery_fee` ÷ 100
- `actual_payment_amount` ÷ 100
- `coupon_discount_amount` ÷ 100
- `activity_discount_amount` ÷ 100

### 积分字段转换
数据库中积分需要除以10：
- `total_payment_integral` ÷ 10

### 状态字段
- `push_status_text` - 推送状态文本（成功/失败）
- `audit_status_text` - 审核状态文本（未审核/审核通过/审核拒绝）

## 📝 完整字段列表

### 列表接口返回字段：
```json
{
  "id": 1,                              // 记录ID
  "order_detail_no": "ORDER_001",       // 订单编码
  "total_amount": 1000.00,              // 订单总额（元）
  "delivery_fee": 10.00,                // 运费（元）
  "actual_payment_amount": 950.00,      // 实付金额（元）
  "order_status": "已支付",             // 订单状态
  "receipt_address": "北京市朝阳区xxx",  // 收货地址
  "remark": "备注信息",                 // 备注
  "coupon_discount_amount": 50.00,      // 卡券优惠（元）
  "payment_method": "微信支付",         // 支付方式
  "order_paid_time": "2025-01-21 09:05:00", // 支付时间
  "total_payment_integral": 100,        // 积分抵扣
  "user_name": "张三",                  // 用户姓名
  "user_mobile": "13800138000",         // 用户手机
  "register_mobile": "13800138000",     // 注册手机
  "delivery_callback_url": "http://domain.com/callback", // 回调地址
  "split_time": "2025-01-21 10:00:00",  // 分账时间
  "split_status": 1,                    // 分账状态
  "activity_discount_amount": 30.00,    // 活动优惠（元）
  "push_status": 1,                     // 推送状态
  "push_status_text": "成功",          // 推送状态文本
  "audit_status": 1,                    // 审核状态
  "audit_status_text": "审核通过",     // 审核状态文本
  "push_time": "2025-01-21 10:30:00",  // 推送时间
  "created_date": "2025-01-21 09:00:00" // 下单时间
}
```

## 📊 数据来源映射

基于 `pushOrderToPartner` 方法中的数据映射：

| API字段 | 数据库字段 | 数据来源 | 转换规则 |
|---------|------------|----------|----------|
| `order_detail_no` | `order_detail_no` | `order.order_code` | 直接映射 |
| `total_amount` | `total_amount` | `order.total_money * 100` | ÷100转元 |
| `delivery_fee` | `delivery_fee` | `order.mail_price * 100` | ÷100转元 |
| `actual_payment_amount` | `actual_payment_amount` | `order.money * 100` | ÷100转元 |
| `coupon_discount_amount` | `coupon_discount_amount` | `order.card_money` | ÷100转元 |
| `activity_discount_amount` | `activity_discount_amount` | `order.all_act_yh` | ÷100转元 |
| `total_payment_integral` | `total_payment_integral` | `order.integral / 10` | ÷10转换 |
| `payment_method` | `payment_method` | `paymentMethod()` | 方法转换 |
| `user_name` | `user_name` | `order.name` | 直接映射 |
| `user_mobile` | `user_mobile` | `order.phone` | 直接映射 |
| `register_mobile` | `register_mobile` | `user.mid_phone` | 直接映射 |
| `split_status` | `split_status` | `order.settlement_state==1?2:1` | 条件转换 |

## 🔄 更新的文件

1. **控制器**: `application/admin_v2/controller/SupplierOrderPush.php`
   - 更新了 `getList` 方法的字段列表
   - 添加了金额和积分字段的格式化处理

2. **API文档**: `supplier_order_push_openapi.json`
   - 更新了 `OrderPushItem` schema
   - 更新了示例数据
   - 添加了所有新字段的定义

## 🚀 使用说明

1. **重新导入API文档**: 使用更新后的 `supplier_order_push_openapi.json`
2. **前端适配**: 前端需要适配新增的字段
3. **数据展示**: 可以展示更完整的订单推送信息

## ⚠️ 注意事项

1. **向后兼容**: 新增字段不影响现有功能
2. **数据格式**: 金额字段已转换为元，前端直接使用
3. **空值处理**: 部分字段可能为空，前端需要做空值处理
4. **性能影响**: 返回字段增多，可能略微影响接口性能

## 📋 测试建议

1. **字段完整性**: 验证所有字段都能正确返回
2. **数据格式**: 验证金额和积分字段的转换是否正确
3. **状态文本**: 验证状态文本是否正确显示
4. **分页功能**: 验证分页功能是否正常

更新完成后，API接口将返回更完整的订单推送信息，便于前端展示和管理。
