{"openapi": "3.0.0", "info": {"title": "供应商订单推送报表API", "description": "供应商订单推送报表管理接口文档", "version": "1.0.0"}, "servers": [{"url": "http://your-domain.com", "description": "开发环境"}], "paths": {"/admin_v2/supplier_order_push/getList": {"get": {"summary": "获取推送列表数据", "description": "获取供应商订单推送记录列表，支持筛选和分页", "parameters": [{"name": "push_status", "in": "query", "description": "推送状态：0-失败，1-成功", "required": false, "schema": {"type": "integer", "enum": [0, 1]}}, {"name": "start_time", "in": "query", "description": "开始时间，格式：2025-01-01 00:00:00", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "end_time", "in": "query", "description": "结束时间，格式：2025-01-31 23:59:59", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "order_code", "in": "query", "description": "订单编码（模糊搜索）", "required": false, "schema": {"type": "string"}}, {"name": "pagesize", "in": "query", "description": "每页数量，默认20", "required": false, "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse"}, "example": {"code": 0, "msg": "success", "data": {"list": [{"id": 1, "order_detail_no": "ORDER_20250121001", "total_amount": 1000.0, "delivery_fee": 10.0, "actual_payment_amount": 950.0, "order_status": "已支付", "receipt_address": "北京市朝阳区xxx", "remark": "备注信息", "coupon_discount_amount": 50.0, "payment_method": "微信支付", "order_paid_time": "2025-01-21 09:05:00", "total_payment_integral": 100, "user_name": "张三", "user_mobile": "13800138000", "register_mobile": "13800138000", "delivery_callback_url": "http://domain.com/callback", "split_time": "2025-01-21 10:00:00", "split_status": 1, "activity_discount_amount": 30.0, "push_status": 1, "push_status_text": "成功", "audit_status": 1, "audit_status_text": "审核通过", "push_time": "2025-01-21 10:30:00", "created_date": "2025-01-21 09:00:00"}], "total": 100, "current_page": 1, "per_page": 20}}}}}}}}, "/admin_v2/supplier_order_push/detail": {"get": {"summary": "获取推送详情", "description": "获取指定订单推送记录的详细信息", "parameters": [{"name": "id", "in": "query", "description": "推送记录ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetailResponse"}, "example": {"code": 0, "msg": "success", "data": {"id": 1, "order_detail_no": "ORDER_20250121001", "total_amount": 1000.0, "delivery_fee": 10.0, "actual_payment_amount": 950.0, "order_status": "已支付", "receipt_address": "北京市朝阳区xxx", "remark": "备注信息", "created_date": "2025-01-21 09:00:00", "coupon_discount_amount": 50.0, "payment_method": "微信支付", "user_name": "张三", "user_mobile": "13800138000", "push_status": 1, "push_status_text": "成功", "audit_status": 1, "audit_status_text": "审核通过", "push_time": "2025-01-21 10:30:00", "commodity_list": [{"orderDetailNo": "123", "supplierCommodityCode": "SUP_SKU001", "commodityCode": "COMM001", "commodityName": "测试商品", "skuCode": "SKU001", "skuName": "规格1", "skuInfo": "颜色:红色,尺寸:L", "commodityPrice": 100.0, "commodityQuantity": 2, "commodityTotalPrice": 200.0, "couponDiscountAmount": 10.0, "originalTotalCostAmount": 190.0, "totalCostIntegral": 1000, "logisticsDeliveryFee": 5.0, "couponInfo": "[{\"coupon_id\":1,\"coupon_name\":\"优惠券\"}]", "activityId": 1, "activityName": "限时优惠活动", "activityType": "限时优惠", "supplierCompanyName": "供应商A", "supplierCompanyCode": "SUP_A", "commodityPurchasePrice": 80.0, "commodityTaxCode": "1010101000000000000", "commodityTaxRate": "13%"}]}}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 1, "msg": "参数错误", "data": {}}}}}}}}, "/admin_v2/supplier_order_push/retransmit": {"post": {"summary": "重传订单", "description": "更新订单信息并重新推送到供应商系统\n\n注意：以下字段不可修改\n- order_detail_no（订单编码）\n- order_status（订单状态）\n- payment_method（支付方式）\n- created_date（下单时间）\n- 商品详情中的 activityId, activityType, couponInfo", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/RetransmitRequest"}}}}, "responses": {"200": {"description": "重传成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RetransmitResponse"}, "example": {"code": 0, "msg": "重传成功", "data": {"push_status": 1, "response": {"result": 1, "message": "推送成功"}}}}}}, "400": {"description": "重传失败", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 1, "msg": "重传失败：具体错误信息", "data": {}}}}}}}}}, "components": {"schemas": {"ListResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，0-成功，1-失败"}, "msg": {"type": "string", "description": "消息"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/OrderPushItem"}}, "total": {"type": "integer", "description": "总记录数"}, "current_page": {"type": "integer", "description": "当前页码"}, "per_page": {"type": "integer", "description": "每页数量"}}}}}, "DetailResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，0-成功，1-失败"}, "msg": {"type": "string", "description": "消息"}, "data": {"$ref": "#/components/schemas/OrderPushDetail"}}}, "RetransmitResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码，0-成功，1-失败"}, "msg": {"type": "string", "description": "消息"}, "data": {"type": "object", "properties": {"push_status": {"type": "integer", "description": "推送状态，0-失败，1-成功"}, "response": {"type": "object", "description": "供应商接口返回的响应"}}}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码，1-失败"}, "msg": {"type": "string", "description": "错误消息"}, "data": {"type": "object", "description": "错误数据"}}}, "OrderPushItem": {"type": "object", "properties": {"id": {"type": "integer", "description": "记录ID"}, "order_detail_no": {"type": "string", "description": "订单编码"}, "total_amount": {"type": "number", "format": "float", "description": "订单总额（元）"}, "delivery_fee": {"type": "number", "format": "float", "description": "运费（元）"}, "actual_payment_amount": {"type": "number", "format": "float", "description": "实付金额（元）"}, "order_status": {"type": "string", "description": "订单状态"}, "receipt_address": {"type": "string", "description": "收货地址"}, "remark": {"type": "string", "description": "备注"}, "coupon_discount_amount": {"type": "number", "format": "float", "description": "卡券优惠（元）"}, "payment_method": {"type": "string", "description": "支付方式"}, "order_paid_time": {"type": "string", "format": "date-time", "description": "支付时间"}, "total_payment_integral": {"type": "integer", "description": "积分抵扣"}, "user_name": {"type": "string", "description": "用户姓名"}, "user_mobile": {"type": "string", "description": "用户手机"}, "register_mobile": {"type": "string", "description": "注册手机"}, "delivery_callback_url": {"type": "string", "description": "发货回调地址"}, "split_time": {"type": "string", "format": "date-time", "description": "分账时间"}, "split_status": {"type": "integer", "description": "分账状态，1-未分账，2-已分账"}, "activity_discount_amount": {"type": "number", "format": "float", "description": "活动优惠（元）"}, "push_status": {"type": "integer", "description": "推送状态，0-失败，1-成功"}, "push_status_text": {"type": "string", "description": "推送状态文本"}, "audit_status": {"type": "integer", "description": "审核状态，0-未审核，1-审核通过，2-审核拒绝"}, "audit_status_text": {"type": "string", "description": "审核状态文本"}, "push_time": {"type": "string", "format": "date-time", "description": "推送时间"}, "created_date": {"type": "string", "format": "date-time", "description": "下单时间"}}}, "OrderPushDetail": {"type": "object", "properties": {"id": {"type": "integer", "description": "记录ID"}, "order_detail_no": {"type": "string", "description": "订单编码"}, "total_amount": {"type": "number", "format": "float", "description": "订单总额（元）"}, "delivery_fee": {"type": "number", "format": "float", "description": "运费（元）"}, "actual_payment_amount": {"type": "number", "format": "float", "description": "实付金额（元）"}, "order_status": {"type": "string", "description": "订单状态"}, "receipt_address": {"type": "string", "description": "收货地址"}, "remark": {"type": "string", "description": "备注"}, "created_date": {"type": "string", "format": "date-time", "description": "下单时间"}, "coupon_discount_amount": {"type": "number", "format": "float", "description": "卡券优惠（元）"}, "payment_method": {"type": "string", "description": "支付方式"}, "user_name": {"type": "string", "description": "用户姓名"}, "user_mobile": {"type": "string", "description": "用户手机"}, "push_status": {"type": "integer", "description": "推送状态，0-失败，1-成功"}, "push_status_text": {"type": "string", "description": "推送状态文本"}, "audit_status": {"type": "integer", "description": "审核状态"}, "audit_status_text": {"type": "string", "description": "审核状态文本"}, "push_time": {"type": "string", "format": "date-time", "description": "推送时间"}, "commodity_list": {"type": "array", "items": {"$ref": "#/components/schemas/CommodityItem"}, "description": "商品列表"}}}, "CommodityItem": {"type": "object", "properties": {"orderDetailNo": {"type": "string", "description": "订单详情编号"}, "supplierCommodityCode": {"type": "string", "description": "供应商商品代码"}, "commodityCode": {"type": "string", "description": "商品代码"}, "commodityName": {"type": "string", "description": "商品名称"}, "skuCode": {"type": "string", "description": "SKU代码"}, "skuName": {"type": "string", "description": "SKU名称"}, "skuInfo": {"type": "string", "description": "SKU信息"}, "commodityPrice": {"type": "number", "format": "float", "description": "商品价格（元）"}, "commodityQuantity": {"type": "integer", "description": "商品数量"}, "commodityTotalPrice": {"type": "number", "format": "float", "description": "商品总价（元）"}, "couponDiscountAmount": {"type": "number", "format": "float", "description": "卡券优惠金额（元）"}, "originalTotalCostAmount": {"type": "number", "format": "float", "description": "原始总成本金额（元）"}, "totalCostIntegral": {"type": "integer", "description": "总成本积分"}, "logisticsDeliveryFee": {"type": "number", "format": "float", "description": "物流配送费（元）"}, "couponInfo": {"type": "string", "description": "卡券信息（JSON格式）"}, "activityId": {"type": "integer", "description": "活动ID"}, "activityName": {"type": "string", "description": "活动名称"}, "activityType": {"type": "string", "description": "活动类型"}, "supplierCompanyName": {"type": "string", "description": "供应商名称"}, "supplierCompanyCode": {"type": "string", "description": "供应商代码"}, "commodityPurchasePrice": {"type": "number", "format": "float", "description": "商品采购价格（元）"}, "commodityTaxCode": {"type": "string", "description": "商品税收代码"}, "commodityTaxRate": {"type": "string", "description": "商品税率"}}}, "RetransmitRequest": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "description": "推送记录ID（必填）"}, "total_amount": {"type": "number", "format": "float", "description": "订单总额（元）"}, "delivery_fee": {"type": "number", "format": "float", "description": "运费（元）"}, "actual_payment_amount": {"type": "number", "format": "float", "description": "实付金额（元）"}, "receipt_address": {"type": "string", "description": "收货地址"}, "remark": {"type": "string", "description": "备注"}, "coupon_discount_amount": {"type": "number", "format": "float", "description": "卡券优惠（元）"}, "user_name": {"type": "string", "description": "用户姓名"}, "user_mobile": {"type": "string", "description": "用户手机"}, "register_mobile": {"type": "string", "description": "注册手机"}, "activity_discount_amount": {"type": "number", "format": "float", "description": "活动优惠（元）"}, "total_payment_integral": {"type": "integer", "description": "积分抵扣"}}}}}}