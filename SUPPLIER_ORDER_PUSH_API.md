# 供应商订单推送报表 API 接口文档

## 接口概述

本文档描述了供应商订单推送报表的后台API接口，参考 CardApi.php 的写法，使用 `print_json` 格式返回数据。

## 基础信息

- **控制器**: `application/admin_v2/controller/SupplierOrderPush.php`
- **基础路径**: `/admin_v2/supplier_order_push/`
- **返回格式**: JSON
- **认证方式**: 后台管理员登录认证

## 接口列表

### 1. 跳转到前端项目页面

**接口地址**: `GET /admin_v2/supplier_order_push/index`

**功能说明**: 跳转到前端项目页面（如果配置了web_menu_url）

**请求参数**: 无

**返回数据**: 重定向到前端页面

---

### 2. 获取推送列表数据

**接口地址**: `GET /admin_v2/supplier_order_push/getList`

**功能说明**: 获取供应商订单推送记录列表，支持筛选和分页

**请求参数**:
```json
{
    "push_status": "0|1",           // 可选，推送状态：0-失败，1-成功
    "start_time": "2025-01-01 00:00:00",  // 可选，开始时间
    "end_time": "2025-01-31 23:59:59",    // 可选，结束时间
    "order_code": "ORDER123",       // 可选，订单编码（模糊搜索）
    "pagesize": 20                  // 可选，每页数量，默认20
}
```

**返回数据**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "order_detail_no": "ORDER_20250121001",
                "total_amount": 1000.00,
                "actual_payment_amount": 950.00,
                "order_status": "已支付",
                "user_name": "张三",
                "user_mobile": "13800138000",
                "push_status": 1,
                "push_status_text": "成功",
                "audit_status": 1,
                "audit_status_text": "审核通过",
                "push_time": "2025-01-21 10:30:00",
                "created_date": "2025-01-21 09:00:00"
            }
        ],
        "total": 100,
        "current_page": 1,
        "per_page": 20
    }
}
```

---

### 3. 获取推送详情

**接口地址**: `GET /admin_v2/supplier_order_push/detail`

**功能说明**: 获取指定订单推送记录的详细信息

**请求参数**:
```json
{
    "id": 1  // 必填，推送记录ID
}
```

**返回数据**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1,
        "order_detail_no": "ORDER_20250121001",
        "total_amount": 1000.00,
        "delivery_fee": 10.00,
        "actual_payment_amount": 950.00,
        "order_status": "已支付",
        "receipt_address": "北京市朝阳区xxx",
        "remark": "备注信息",
        "created_date": "2025-01-21 09:00:00",
        "coupon_discount_amount": 50.00,
        "payment_method": "微信支付",
        "order_paid_time": "2025-01-21 09:05:00",
        "total_payment_integral": 100,
        "user_name": "张三",
        "user_mobile": "13800138000",
        "register_mobile": "13800138000",
        "delivery_callback_url": "http://domain.com/callback",
        "split_time": "2025-01-21 10:00:00",
        "split_status": 1,
        "activity_discount_amount": 30.00,
        "push_status": 1,
        "push_status_text": "成功",
        "audit_status": 1,
        "audit_status_text": "审核通过",
        "push_time": "2025-01-21 10:30:00",
        "commodity_list": [
            {
                "orderDetailNo": "123",
                "supplierCommodityCode": "SUP001",
                "commodityCode": "COMM001",
                "commodityName": "测试商品",
                "skuCode": "SKU001",
                "skuName": "规格1",
                "commodityPrice": 100.00,
                "commodityQuantity": 2,
                "commodityTotalPrice": 200.00,
                "couponDiscountAmount": 10.00,
                "originalTotalCostAmount": 190.00,
                "logisticsDeliveryFee": 5.00,
                "couponInfo": "卡券信息",
                "activityId": 1,
                "activityName": "限时优惠",
                "activityType": "限时优惠",
                "supplierCompanyName": "供应商A",
                "supplierCompanyCode": "SUP_A",
                "commodityPurchasePrice": 80.00
            }
        ]
    }
}
```

---

### 4. 重传订单

**接口地址**: `POST /admin_v2/supplier_order_push/retransmit`

**功能说明**: 更新订单信息并重新推送到供应商系统

**请求参数**:
```json
{
    "id": 1,                        // 必填，推送记录ID
    "total_amount": 1000.00,        // 可选，订单总额
    "delivery_fee": 10.00,          // 可选，运费
    "actual_payment_amount": 950.00, // 可选，实付金额
    "receipt_address": "新地址",     // 可选，收货地址
    "remark": "新备注",             // 可选，备注
    "coupon_discount_amount": 50.00, // 可选，卡券优惠
    "order_paid_time": "2025-01-21 09:05:00", // 可选，支付时间
    "total_payment_integral": 100,   // 可选，积分抵扣
    "user_name": "张三",            // 可选，用户姓名
    "user_mobile": "13800138000",   // 可选，用户手机
    "register_mobile": "13800138000", // 可选，注册手机
    "split_time": "2025-01-21 10:00:00", // 可选，分账时间
    "split_status": 1,              // 可选，分账状态
    "activity_discount_amount": 30.00, // 可选，活动优惠
    "detail": [                     // 可选，商品详情数组
        {
            "commodityName": "新商品名",
            "skuCode": "NEW_SKU",
            "commodityPrice": 100.00,
            "commodityQuantity": 2,
            "activityName": "新活动名",
            "supplierCompanyName": "新供应商"
            // 注意：activityId, activityType, couponInfo 不可修改
        }
    ]
}
```

**不可修改字段**:
- `order_detail_no` - 订单编码
- `order_status` - 订单状态  
- `payment_method` - 支付方式
- `created_date` - 下单时间
- 商品详情中的 `activityId`, `activityType`, `couponInfo` - 活动类型和卡券信息

**返回数据**:
```json
{
    "code": 0,
    "msg": "重传成功",
    "data": {
        "push_status": 1,
        "response": {
            "result": 1,
            "message": "推送成功"
        }
    }
}
```

**错误返回**:
```json
{
    "code": 1,
    "msg": "重传失败：具体错误信息",
    "data": {}
}
```

---

## 状态码说明

### 推送状态 (push_status)
- `0`: 失败
- `1`: 成功

### 审核状态 (audit_status)  
- `0`: 未审核
- `1`: 审核通过
- `2`: 审核拒绝

## 错误处理

所有接口都使用统一的错误返回格式：
```json
{
    "code": 1,
    "msg": "错误信息",
    "data": {}
}
```

常见错误：
- `参数错误`: 必填参数缺失或格式错误
- `记录不存在`: 指定的ID对应的记录不存在
- `重传失败`: 调用供应商接口失败或数据更新失败

## 日志记录

所有重传操作都会记录到以下位置：
1. **DbLog表**: 记录供应商接口调用结果
   - `type`: `retransmit_supplier_order`
   - `is_success`: 推送是否成功
   - `send_note`: 发送的数据
   - `receive_note`: 接收的响应
   - `order_code`: 订单编码

2. **系统日志**: 记录操作日志和错误信息

## 使用示例

### JavaScript 调用示例

```javascript
// 获取列表
fetch('/admin_v2/supplier_order_push/getList?push_status=0&pagesize=10')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      console.log('列表数据:', data.data.list);
    }
  });

// 获取详情
fetch('/admin_v2/supplier_order_push/detail?id=1')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      console.log('详情数据:', data.data);
    }
  });

// 重传订单
fetch('/admin_v2/supplier_order_push/retransmit', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: 'id=1&total_amount=1000.00&user_name=张三'
})
.then(response => response.json())
.then(data => {
  if (data.code === 0) {
    console.log('重传成功');
  } else {
    console.error('重传失败:', data.msg);
  }
});
```
