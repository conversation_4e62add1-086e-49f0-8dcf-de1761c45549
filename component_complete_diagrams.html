<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component控制器完整流程图与时序图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            text-align: center;
            color: #333;
            cursor: pointer;
        }
        h1:hover, h2:hover {
            color: #007bff;
        }
        .diagram-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
            overflow-x: auto;
        }
        .export-buttons {
            text-align: center;
            margin: 20px 0;
        }
        .export-btn {
            margin: 0 10px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .export-btn:hover {
            background-color: #0056b3;
        }
        .diagram-toggle {
            text-align: center;
            margin: 20px 0;
        }
        .toggle-btn {
            margin: 0 10px;
            padding: 8px 16px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .toggle-btn:hover {
            background-color: #218838;
        }
        .toggle-btn.active {
            background-color: #dc3545;
        }
        .hidden {
            display: none;
        }
        .legend {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
            font-size: 14px;
        }
        .legend h4 {
            margin-top: 0;
            color: #495057;
        }
        .legend-item {
            display: inline-block;
            margin: 5px 10px;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
        }
        .db-query { background-color: #ffcdd2; border: 2px solid #d32f2f; }
        .heavy-process { background-color: #ff5722; border: 3px solid #bf360c; color: white; }
        .cache-hit { background-color: #c8e6c9; border: 2px solid #388e3c; }
        .api-call { background-color: #fff3e0; border: 2px solid #f57c00; }
        .start-end { background-color: #e1f5fe; border: 2px solid #0277bd; }
        .component-process { background-color: #f3e5f5; border: 2px solid #7b1fa2; }
    </style>
</head>
<body>
    <div class="container">
        <h1 onclick="printDiagram()">Component控制器完整流程图与时序图</h1>
        <p style="text-align: center; color: #666;">
            pageComponentList 和 componentDetail 两个核心方法的完整调用流程分析
        </p>
        
        <div class="export-buttons">
            <button class="export-btn" onclick="exportFlowchartSVG()">导出流程图SVG</button>
            <button class="export-btn" onclick="exportSequenceSVG()">导出时序图SVG</button>
            <button class="export-btn" onclick="printDiagram()">打印图表</button>
        </div>

        <div class="diagram-toggle">
            <button class="toggle-btn" id="flowchart-toggle" onclick="toggleDiagram('flowchart')">显示/隐藏流程图</button>
            <button class="toggle-btn" id="sequence-toggle" onclick="toggleDiagram('sequence')">显示/隐藏时序图</button>
        </div>
        
        <div class="diagram-section" id="flowchart-section">
            <h2 onclick="toggleDiagram('flowchart')">流程图 - Component控制器完整调用流程</h2>
            <div class="mermaid" id="flowchart-diagram">
graph TD
    A[Component控制器开始] --> B{请求方法判断}
    B -->|pageComponentList| C[pageComponentList方法]
    B -->|componentDetail| D[componentDetail方法]

    %% pageComponentList 流程
    C --> C1[参数验证 ComponentLayoutValidate]
    C1 --> C2[获取home_sm_id<br/>默认值处理]
    C2 --> C3[调用getComponentData<br/>获取组件配置]

    C3 --> C3A[构建缓存Key<br/>home:component_data:pageId]
    C3A --> C3B{检查Redis缓存}
    C3B -->|缓存命中| C3C[返回缓存数据]
    C3B -->|缓存未命中| C3D[数据库查询<br/>DbHomeSm::find]
    C3D --> C3E[SELECT data_json<br/>FROM t_db_home_sm<br/>WHERE id=?]
    C3E --> C3F[Redis缓存写入<br/>3600-7200*10秒]
    C3F --> C3G[Redis SADD集合管理<br/>缓存key管理]
    C3G --> C3H[JSON解析<br/>json_decode_assoc]
    C3C --> C3H

    C3H --> C4[遍历组件数组<br/>生成component_id]
    C4 --> C5{组件类型判断<br/>richComponentType?}
    C5 -->|是动态组件| C6[移除attribute属性<br/>不返回详情配置]
    C5 -->|是静态组件| C7[保留完整配置]
    C6 --> C8[staticComponentProcess<br/>静态组件处理]
    C7 --> C8

    %% staticComponentProcess 详细流程
    C8 --> C8A[getUserGroups<br/>获取用户群组]
    C8A --> C8B[WlzCrowdsLogs查询<br/>WHERE user_id=?]
    C8B --> C8C[提取crowd_id数组]

    C8C --> C8D[检查是否需要千人千面<br/>遍历cCarousel组件]
    C8D --> C8E{推荐逻辑类型包含4?<br/>猜你喜欢}
    C8E -->|是| C8F[调用getAbsDataNew<br/>数据组推荐]
    C8E -->|否| C8G[跳过推荐数据获取]

    C8F --> C8F1[getAbsDataNew调用<br/>WHERE vin=?]
    C8F1 --> C8F2[提取commodity_id数组]
    C8F2 --> C8F3[NetGoods::goodsList<br/>商品详情查询]
    C8F3 --> C8F4[BuGwRecommendationBannerCommodity<br/>Banner商品关联查询]
    C8F4 --> C8F5[复杂联表查询:<br/>t_bu_gw_recommendation_banner_commodity a<br/>JOIN t_bu_gw_recommendation_banner b<br/>WHERE a.commodity_id IN (...)]
    C8F5 --> C8G
    
    C8G --> C8H[组件类型循环处理<br/>foreach componentList]
    C8H --> C8I{组件类型判断}
    
    %% 广告组件处理
    C8I -->|cAdvertising类型| C8J[广告组件时间验证]
    C8J --> C8J1[遍历imgs数组<br/>检查show_time]
    C8J1 --> C8J2{时间是否有效}
    C8J2 -->|无效| C8J3[移除该广告<br/>unset imgs[key]]
    C8J2 -->|有效| C8J4[用户群组验证<br/>userGroups检查]
    C8J4 --> C8J5{用户是否在群组中}
    C8J5 -->|否| C8J3
    C8J5 -->|是| C8J6[保留广告]
    C8J3 --> C8J7[检查下一个广告]
    C8J6 --> C8J7
    C8J7 --> C8K
    
    %% 轮播组件处理
    C8I -->|cCarousel轮播| C8L[轮播组件处理]
    C8L --> C8L1[获取推荐逻辑配置<br/>recommended_logical_types]
    C8L1 --> C8L2{推荐逻辑类型判断}
    C8L2 -->|类型2-后台配置| C8L3[后台配置图片处理]
    C8L2 -->|类型4-猜你喜欢| C8L4[千人千面推荐处理]
    
    C8L3 --> C8L3A[遍历imgs数组<br/>时间和群组验证]
    C8L3A --> C8L3B[图片排序处理<br/>sort_order]
    C8L3B --> C8L3C[渠道适配<br/>GWAPP/GWSM取首个]
    C8L3C --> C8K
    
    C8L4 --> C8L4A[匹配banner_commodity_list<br/>推荐商品数据]
    C8L4A --> C8L4B{是否固定顶部<br/>isFixedTop}
    C8L4B -->|是| C8L4C[匹配image_type=1]
    C8L4B -->|否| C8L4D[匹配bannerSize类型]
    C8L4C --> C8L4E[构建商品跳转数据<br/>savePath配置]
    C8L4D --> C8L4E
    C8L4E --> C8L4F{推荐数据是否匹配}
    C8L4F -->|否| C8L4G[使用默认配置数据]
    C8L4F -->|是| C8K
    C8L4G --> C8K
    
    %% 其他组件处理
    C8I -->|cTabs标签| C8M[设置is_classify=1]
    C8I -->|其他组件| C8N[保持原样]
    C8M --> C8K
    C8N --> C8K
    
    C8K[组件处理完成] --> C8O{是否还有组件<br/>循环继续?}
    C8O -->|是| C8H
    C8O -->|否| C9[返回组件列表<br/>array_values重新索引]
    
    C9 --> C10[返回JSON响应<br/>setResponseData]
    
    %% componentDetail 流程
    D --> D1[参数验证 ComponentDetailValidate]
    D1 --> D2[获取home_sm_id<br/>默认值处理]
    D2 --> D3[调用getComponentData<br/>获取组件配置]
    D3 --> D4[解析component_id<br/>提取索引]
    D4 --> D5[根据索引获取组件<br/>componentList[componentId]]
    D5 --> D6{组件是否存在}
    D6 -->|否| D7[返回空组件数据]
    D6 -->|是| D8{是否广告类组件<br/>需要act_code}
    D8 -->|是且缺少act_code| D9[返回错误<br/>act_code不能为空]
    D8 -->|否或有act_code| D10[GoodsCustomize::componentHandle<br/>组件详情处理]

    %% GoodsCustomize::componentHandle 详细流程
    D10 --> D10A{组件类型判断<br/>switch component type}

    D10A -->|cLottery抽奖| D11[componentLottery处理]
    D11 --> D11A[DbDraw::getOne<br/>抽奖活动查询]
    D11A --> D11B[活动时间验证<br/>end_time is_enable]
    D11B --> D11C[HaoWan::getLotteryNum<br/>用户抽奖次数查询]
    D11C --> D11D[抽奖信息更新<br/>game_id draw_url draw_num]
    D11D --> D12

    D10A -->|cSeckill秒杀| D13[componentSeckill处理]
    D13 --> D13A[Seckill::getSeckillTime<br/>活动时间查询]
    D13A --> D13B[秒杀商品状态验证]
    D13B --> D13C[NetGoods::goodsList<br/>秒杀商品查询]
    D13C --> D13D[秒杀库存和价格处理]
    D13D --> D13E[商品排序<br/>有库存优先]
    D13E --> D12

    D10A -->|cLimit限时折扣| D14[componentLimit处理]
    D14 --> D14A[check_activity活动验证<br/>DbLimitDiscount::getOne]
    D14A --> D14B[WHERE id=? AND is_enable=1<br/>AND act_status=2]
    D14B --> D14C[活动信息添加到商品]
    D14C --> D12

    D10A -->|cCrowdfunding众筹| D15[componentCrowdfunding处理]
    D15 --> D15A[众筹活动和商品验证]
    D15A --> D15B[crowdfundList查询]
    D15B --> D12

    D10A -->|cSuit套餐| D16[componentSuit处理]
    D16 --> D16A[check_activity活动验证]
    D16A --> D16B[BuCheapSuitIndex查询<br/>套餐状态验证]
    D16B --> D16C[NetGoods::suitList<br/>套餐商品查询]
    D16C --> D12

    D10A -->|cCoupon卡券| D17[componentCoupon处理]
    D17 --> D17A[定向人群过滤<br/>range == 2检查]
    D17A --> D17B[NetUser::canGetCards<br/>用户权限验证]
    D17B --> D17C[check_activity<br/>卡券活动验证]
    D17C --> D17D[NetCard::getUserCard<br/>用户卡券状态查询]
    D17D --> D17E[卡券状态更新<br/>is_received available_count]
    D17E --> D12

    D10A -->|cFloatWindow浮窗| D18[componentFloatWindow处理]
    D18 --> D18A[浮窗类型判断<br/>attribute type]
    D18A --> D18B{类型是否为1<br/>单张卡券}
    D18B -->|是| D18C[canGetCards权限验证]
    D18B -->|否| D18D[其他类型处理]
    D18C --> D18E[NetCard::getUserCard<br/>单张卡券查询]
    D18E --> D18F[浮窗状态更新]
    D18F --> D12
    D18D --> D12

    D10A -->|cGoods商品| D19[componentGoods处理]
    D19 --> D19A[解析find_commodity_ids<br/>商品ID列表]
    D19A --> D19B[获取goods_data<br/>商品配置数据]
    D19B --> D19C[NetGoods::goodsList<br/>商品详情查询]
    D19C --> D19D[商品数据合并处理]
    D19D --> D19E[价格标签信息更新]
    D19E --> D19F[分页处理<br/>current_page page_size]
    D19F --> D12

    D10A -->|cCarousel轮播| D20[componentCCarousel处理]
    D20 --> D20A[解析act_code参数]
    D20A --> D20B[遍历imgs查找匹配项]
    D20B --> D20C[提取couponList<br/>卡券ID数组]
    D20C --> D20D[cardDraw卡券抽奖处理]
    D20D --> D12

    D10A -->|cAdvertising广告| D21[componentCAdvertising处理]
    D21 --> D21A[根据act_code匹配广告]
    D21A --> D21B[提取couponList<br/>卡券ID数组]
    D21B --> D21C[cardDraw卡券抽奖处理]
    D21C --> D12

    D12[组件处理完成] --> D22[返回处理后的组件数据]
    D7 --> D23[返回JSON响应<br/>setResponseData]
    D9 --> D23
    D22 --> D23

    %% 样式定义
    classDef dbQuery fill:#ffcdd2,stroke:#d32f2f,stroke-width:2px
    classDef heavyProcess fill:#ff5722,stroke:#bf360c,stroke-width:3px
    classDef cacheHit fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    classDef apiCall fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef startEnd fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef componentProcess fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px

    %% 应用样式
    class A,C10,D23 startEnd
    class C3E,C8B,C8F5,D11A,D13A,D14B,D16B,D17C,D17D,D19C dbQuery
    class C8F3,D10,D13C,D16C,D19C heavyProcess
    class C3C cacheHit
    class C8F1,D11C apiCall
    class C8,D10A componentProcess
            </div>
            
            <div class="legend">
                <h4>流程图图例说明</h4>
                <div class="legend-item start-end">开始/结束节点</div>
                <div class="legend-item db-query">数据库查询</div>
                <div class="legend-item heavy-process">重量级处理</div>
                <div class="legend-item cache-hit">缓存命中</div>
                <div class="legend-item api-call">第三方API调用</div>
                <div class="legend-item component-process">组件处理</div>
            </div>
        </div>

        <div class="diagram-section" id="sequence-section">
            <h2 onclick="toggleDiagram('sequence')">时序图 - Component控制器UML时序图</h2>
            <div class="mermaid" id="sequence-diagram">
sequenceDiagram
    participant Client as 客户端
    participant Controller as Component控制器
    participant Validator as 参数验证器
    participant Cache as Redis缓存
    participant DB as 数据库
    participant NetGoods as NetGoods服务
    participant GoodsCustomize as GoodsCustomize
    participant UserGroup as 用户群组服务
    participant Recommend as 推荐服务
    participant Activity as 活动服务
    participant Card as 卡券服务

    Note over Client,Card: Component控制器双接口调用时序

    %% pageComponentList 流程
    rect rgb(240, 248, 255)
        Note over Client,Card: pageComponentList - 组件列表加载

        Client->>Controller: GET /pageComponentList
        Controller->>Validator: ComponentLayoutValidate验证
        Validator-->>Controller: 验证通过
        Controller->>Controller: home_sm_id默认值处理

        %% getComponentData 调用
        Controller->>Controller: getComponentData调用
        Controller->>Cache: 检查组件配置缓存
        alt 缓存未命中
            Controller->>DB: DbHomeSm::find查询
            Note over DB: SELECT data_json<br/>FROM t_db_home_sm<br/>WHERE id=?
            DB-->>Controller: 组件配置JSON
            Controller->>Cache: 缓存写入(3600-7200*10秒)
            Controller->>Cache: SADD集合管理
        else 缓存命中
            Cache-->>Controller: 返回缓存数据
        end
        Controller->>Controller: JSON解析

        %% 组件列表处理
        Controller->>Controller: 遍历组件数组
        loop 每个组件
            Controller->>Controller: 生成component_id
            alt 动态组件(richComponentType)
                Controller->>Controller: 移除attribute属性
            else 静态组件
                Controller->>Controller: 保留完整配置
            end
        end

        %% staticComponentProcess 处理
        Controller->>UserGroup: getUserGroups
        UserGroup->>DB: WlzCrowdsLogs查询
        Note over DB: WHERE user_id=?
        DB-->>UserGroup: 用户群组数据
        UserGroup-->>Controller: crowd_id数组

        %% 千人千面检查
        Controller->>Controller: 检查cCarousel组件
        alt 包含推荐逻辑类型4
            Controller->>Recommend: getAbsDataNew调用
            Recommend->>DB: 数据组推荐查询
            DB-->>Recommend: 推荐商品数据
            Recommend-->>Controller: commodity_id数组

            Controller->>NetGoods: goodsList查询
            NetGoods->>DB: 商品详情查询
            DB-->>NetGoods: 商品信息
            NetGoods-->>Controller: 商品列表

            Controller->>DB: BuGwRecommendationBannerCommodity查询
            Note over DB: 复杂联表查询<br/>Banner商品关联
            DB-->>Controller: Banner商品数据
        end

        %% 组件类型处理循环
        loop 每个组件
            alt cAdvertising广告组件
                Controller->>Controller: 时间验证(show_time)
                Controller->>Controller: 用户群组验证
                Controller->>Controller: 过滤无效广告
            else cCarousel轮播组件
                alt 推荐逻辑类型2(后台配置)
                    Controller->>Controller: 图片时间群组验证
                    Controller->>Controller: 排序处理
                    Controller->>Controller: 渠道适配
                else 推荐逻辑类型4(猜你喜欢)
                    Controller->>Controller: 匹配推荐商品数据
                    alt 固定顶部
                        Controller->>Controller: 匹配image_type=1
                    else 非固定顶部
                        Controller->>Controller: 匹配bannerSize类型
                    end
                    Controller->>Controller: 构建商品跳转数据
                    alt 推荐数据未匹配
                        Controller->>Controller: 使用默认配置
                    end
                end
            else cTabs标签组件
                Controller->>Controller: 设置is_classify=1
            else 其他组件
                Controller->>Controller: 保持原样
            end
        end

        Controller->>Controller: array_values重新索引
        Controller-->>Client: 返回组件列表JSON
    end

    %% componentDetail 流程
    rect rgb(248, 255, 248)
        Note over Client,Card: componentDetail - 按需组件详情

        Client->>Controller: GET /componentDetail
        Controller->>Validator: ComponentDetailValidate验证
        Validator-->>Controller: 验证通过
        Controller->>Controller: home_sm_id默认值处理

        Controller->>Controller: getComponentData调用
        Note over Controller: 复用相同缓存逻辑
        Controller->>Controller: 解析component_id
        Controller->>Controller: 根据索引获取组件

        alt 组件不存在
            Controller-->>Client: 返回空数据
        else 组件存在
            alt 广告类组件且缺少act_code
                Controller-->>Client: 返回错误(act_code不能为空)
            else 正常处理
                Controller->>GoodsCustomize: componentHandle调用

                %% 组件类型处理分支
                alt cLottery抽奖组件
                    GoodsCustomize->>DB: DbDraw::getOne
                    DB-->>GoodsCustomize: 抽奖活动信息
                    GoodsCustomize->>GoodsCustomize: 活动时间验证
                    GoodsCustomize->>Activity: HaoWan::getLotteryNum
                    Activity-->>GoodsCustomize: 用户抽奖次数
                    GoodsCustomize->>GoodsCustomize: 抽奖信息更新

                else cSeckill秒杀组件
                    GoodsCustomize->>Activity: Seckill::getSeckillTime
                    Activity-->>GoodsCustomize: 活动时间
                    GoodsCustomize->>GoodsCustomize: 商品状态验证
                    GoodsCustomize->>NetGoods: goodsList(秒杀专用)
                    NetGoods->>DB: 秒杀商品查询
                    DB-->>NetGoods: 秒杀商品数据
                    NetGoods-->>GoodsCustomize: 商品结果
                    GoodsCustomize->>GoodsCustomize: 库存价格处理
                    GoodsCustomize->>GoodsCustomize: 商品排序

                else cLimit限时折扣组件
                    GoodsCustomize->>Activity: check_activity验证
                    Activity->>DB: DbLimitDiscount::getOne
                    Note over DB: WHERE id=? AND is_enable=1<br/>AND act_status=2
                    DB-->>Activity: 活动信息
                    Activity-->>GoodsCustomize: 活动验证结果
                    GoodsCustomize->>GoodsCustomize: 活动信息添加

                else cCoupon卡券组件
                    GoodsCustomize->>GoodsCustomize: 定向人群过滤
                    GoodsCustomize->>Card: NetUser::canGetCards
                    Card-->>GoodsCustomize: 用户权限结果
                    GoodsCustomize->>Activity: check_activity验证
                    Activity-->>GoodsCustomize: 卡券活动状态
                    GoodsCustomize->>Card: NetCard::getUserCard
                    Card-->>GoodsCustomize: 用户卡券状态
                    GoodsCustomize->>GoodsCustomize: 卡券状态更新

                else cGoods商品组件
                    GoodsCustomize->>GoodsCustomize: 解析find_commodity_ids
                    GoodsCustomize->>GoodsCustomize: 获取goods_data配置
                    GoodsCustomize->>NetGoods: goodsList查询
                    NetGoods->>DB: 商品详情查询
                    Note over DB: 复杂多表联查
                    DB-->>NetGoods: 商品数据
                    NetGoods-->>GoodsCustomize: 商品列表
                    GoodsCustomize->>GoodsCustomize: 商品数据合并
                    GoodsCustomize->>GoodsCustomize: 价格标签更新
                    GoodsCustomize->>GoodsCustomize: 分页处理

                else cCarousel轮播组件
                    GoodsCustomize->>GoodsCustomize: 解析act_code参数
                    GoodsCustomize->>GoodsCustomize: 遍历imgs查找匹配
                    GoodsCustomize->>GoodsCustomize: 提取couponList
                    GoodsCustomize->>GoodsCustomize: cardDraw卡券抽奖

                else cAdvertising广告组件
                    GoodsCustomize->>GoodsCustomize: 根据act_code匹配
                    GoodsCustomize->>GoodsCustomize: 提取couponList
                    GoodsCustomize->>GoodsCustomize: cardDraw卡券抽奖
                end

                GoodsCustomize-->>Controller: 处理后的组件数据
                Controller-->>Client: 返回组件详情JSON
            end
        end
    end

    Note over Client,Card: 双接口协作完成组件系统
            </div>

            <div class="legend">
                <h4>时序图说明</h4>
                <p><strong>蓝色区域</strong>：pageComponentList 流程 - 快速返回组件骨架</p>
                <p><strong>绿色区域</strong>：componentDetail 流程 - 按需加载组件详情</p>
                <p><strong>关键特点</strong>：两阶段加载模式，首屏快速，按需详情</p>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            }
        });

        // 图表显示/隐藏切换
        function toggleDiagram(type) {
            const section = document.getElementById(type + '-section');
            const button = document.getElementById(type + '-toggle');

            if (section.classList.contains('hidden')) {
                section.classList.remove('hidden');
                button.classList.remove('active');
                button.textContent = '隐藏' + (type === 'flowchart' ? '流程图' : '时序图');
            } else {
                section.classList.add('hidden');
                button.classList.add('active');
                button.textContent = '显示' + (type === 'flowchart' ? '流程图' : '时序图');
            }
        }

        // 导出流程图为SVG
        function exportFlowchartSVG() {
            const svg = document.querySelector('#flowchart-diagram svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const svgUrl = URL.createObjectURL(svgBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = svgUrl;
                downloadLink.download = 'component_flowchart_complete.svg';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                URL.revokeObjectURL(svgUrl);
            }
        }

        // 导出时序图为SVG
        function exportSequenceSVG() {
            const svg = document.querySelector('#sequence-diagram svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const svgUrl = URL.createObjectURL(svgUrl);
                const downloadLink = document.createElement('a');
                downloadLink.href = svgUrl;
                downloadLink.download = 'component_sequence_complete.svg';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                URL.revokeObjectURL(svgUrl);
            }
        }

        // 打印图表
        function printDiagram() {
            // 确保所有图表都显示
            document.getElementById('flowchart-section').classList.remove('hidden');
            document.getElementById('sequence-section').classList.remove('hidden');

            // 延迟打印，确保图表完全渲染
            setTimeout(() => {
                window.print();
            }, 1000);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示所有图表
            document.getElementById('flowchart-toggle').textContent = '隐藏流程图';
            document.getElementById('sequence-toggle').textContent = '隐藏时序图';
        });
    </script>
</body>
</html>
