<?php

use think\Route;

Route::group('admin_v2', function () {

    Route::get('home_title/adsType', 'admin_v2/HomeChange/adsType');
    Route::get('home_title/get_find', 'admin_v2/HomeChange/get_find');
    Route::get('home_title/ajax_get_class', 'admin_v2/HomeChange/ajax_get_class');
    Route::post('home_title/edit', 'admin_v2/HomeChange/edit');
    Route::get('home_title/get_card', 'admin_v2/HomeChange/get_card');
    Route::get('home_title/get_product', 'admin_v2/HomeChange/get_product');
    Route::get('home_change/get_product', 'admin_v2/HomeChange/get_product');
    Route::get('home_title/get_special', 'admin_v2/HomeChange/get_special');
    Route::get('home_title/get_activity', 'admin_v2/HomeChange/get_activity');
    Route::get('home_title/get_suit', 'admin_v2/HomeChange/get_suit');

    // 推荐服务
    // 活动
    Route::get('recommend/activity/list', 'admin_v2/RecommendActivity/index');
    Route::post('recommend/activity/save', 'admin_v2/RecommendActivity/save');
    Route::post('recommend/activity/upd-enable', 'admin_v2/RecommendActivity/updEnable');
    Route::get('recommend/activity/detail', 'admin_v2/RecommendActivity/detail');
    Route::delete('recommend/activity-del', 'admin_v2/RecommendActivity/delete');
    Route::get('recommend/crowds/list', 'admin_v2/RecommendActivity/crowdsList');
    Route::get('recommend/get-act-card', 'admin_v2/RecommendActivity/getActivityCard');
    Route::get('recommend/get-commodity', 'admin_v2/RecommendActivity/getActivityCommodity');

    // 热卖
    // banner
    Route::post('recommend/hot-sale/save-banner', 'admin_v2/RecommendHotSale/saveBanner');
    Route::get('recommend/hot-sale/banner-list', 'admin_v2/RecommendHotSale/bannerList');
    // tab
    Route::get('recommend/hot-sale/comm-type-list', 'admin_v2/RecommendHotSale/commTypePageList');
    Route::post('recommend/hot-sale/save-tab', 'admin_v2/RecommendHotSale/saveTab');
    Route::get('recommend/hot-sale/tab-list', 'admin_v2/RecommendHotSale/tabList');
    Route::get('recommend/hot-sale/page-class', 'admin_v2/RecommendHotSale/pageClass');
    // 商品
    Route::get('recommend/hot-sale/get-set-commodities', 'admin_v2/RecommendHotSale/getSetCommodities');
    Route::post('recommend/hot-sale/save-commodity', 'admin_v2/RecommendHotSale/saveCommodity');
    Route::get('recommend/hot-sale/get-commodity', 'admin_v2/RecommendHotSale/getCommodities');
    Route::post('recommend/hot-sale/save-tab-commodity', 'admin_v2/RecommendHotSale/saveTabCommodity');
    Route::post('recommend/hot-sale/save-location-commodity', 'admin_v2/RecommendHotSale/saveLocationCommodity');
    Route::post('recommend/hot-sale/save-sort-commodity', 'admin_v2/RecommendHotSale/saveSortCommodity');
    Route::delete('recommend/hot-sale/del-commodity', 'admin_v2/RecommendHotSale/delCommodity');
    // 轮播
    Route::post('recommend/hot-sale/save-carousel-tab', 'admin_v2/RecommendHotSale/saveCarouselTab');
    Route::get('recommend/hot-sale/carousel-comm-type-list', 'admin_v2/RecommendHotSale/carouselCommTypePageList');
    Route::get('recommend/hot-sale/carousel-tab-list', 'admin_v2/RecommendHotSale/carouselTabList');
    Route::get('recommend/hot-sale/carousel-page-class', 'admin_v2/RecommendHotSale/carouselPageClass');
    Route::get('recommend/hot-sale/get-carousel-commodity', 'admin_v2/RecommendHotSale/getCarouselCommodity');


    // 潜客
    Route::post('recommend/pot-customer/save-link', 'admin_v2/PotentialCustomer/saveLink');
    Route::get('recommend/pot-customer/get-link', 'admin_v2/PotentialCustomer/getLink');
    Route::get('recommend/pot-customer/get-set-commodities', 'admin_v2/PotentialCustomer/getSetCommodities');
    Route::post('recommend/pot-customer/save-commodity', 'admin_v2/PotentialCustomer/saveCommodity');
    Route::get('recommend/pot-customer/get-commodity', 'admin_v2/PotentialCustomer/getCommodities');
    Route::delete('recommend/pot-customer/del-commodity', 'admin_v2/PotentialCustomer/delCommodity');
    Route::post('recommend/pot-customer/sort-commodity', 'admin_v2/PotentialCustomer/sortCommodities');
    Route::post('recommend/pot-customer/save-advertisement', 'admin_v2/PotentialCustomer/saveAdvertisement');
    Route::get('recommend/pot-customer/get-advertisement', 'admin_v2/PotentialCustomer/getAdvertisement');
    Route::post('recommend/pot-customer/upd-enable', 'admin_v2/PotentialCustomer/updEnable');
    Route::delete('recommend/pot-customer/del-advertisement', 'admin_v2/PotentialCustomer/delAdvertisement');

    // 千人千面
    Route::post('recommend/pot-customer/save-banner', 'admin_v2/RecommendationBanner/saveBanner');
    Route::get('recommend/pot-customer/get-list', 'admin_v2/RecommendationBanner/getList');
    Route::get('recommend/pot-customer/get-banner-commodity', 'admin_v2/RecommendationBanner/getBannerCommodity');
    Route::get('recommend/pot-customer/get-set-commodity', 'admin_v2/RecommendationBanner/getSetCommodities');
    Route::delete('recommend/pot-customer/del-banner', 'admin_v2/RecommendationBanner/deleteBanner');


    // 新媒体管理
    Route::get('new-media-commodity/index', 'admin_v2/NewMediaCommodity/index'); // 列表
    Route::get('new-media-commodity/download', 'admin_v2/NewMediaCommodity/download'); // 模板下载
    Route::get('new-media-commodity/dy-download', 'admin_v2/NewMediaCommodity/getDyCommodityDownload'); // 抖音商品拉取
    Route::get('new-media-commodity/ks-download', 'admin_v2/NewMediaCommodity/getKsCommodity'); // 快手商品拉取
    Route::post('new-media-commodity/import', 'admin_v2/NewMediaCommodity/import'); // 导入更新
    Route::get('new-media-commodity/info', 'admin_v2/NewMediaCommodity/info'); // 明细
    Route::get('new-media-commodity/down-commodity', 'admin_v2/NewMediaCommodity/downloadCommodity'); // 下载商品
    Route::delete('new-media-commodity/del-commodity', 'admin_v2/NewMediaCommodity/delCommodity'); // 下载商品

    Route::get('new-media-dlr/index', 'admin_v2/NewMediaDlr/index'); // 列表
    Route::get('new-media-dlr/download', 'admin_v2/NewMediaDlr/download'); // 模板下载
    Route::post('new-media-dlr/import', 'admin_v2/NewMediaDlr/import'); // 导入
    Route::get('new-media-dlr/down-dlr', 'admin_v2/NewMediaDlr/downloadDlr'); // 导出
    Route::delete('new-media-dlr/del-dlr', 'admin_v2/NewMediaDlr/delDlr'); // 删除

    Route::get('new-media-order/index', 'admin_v2/NewMediaOrder/index'); // 列表
    Route::get('new-media-order/search-config', 'admin_v2/NewMediaOrder/searchConfig'); // 订单列表搜索配置
    Route::get('new-media-order/apply-to-dlr', 'admin_v2/NewMediaOrder/applyToDlr'); // 使用门店
    Route::get('new-media-order/order-commodities', 'admin_v2/NewMediaOrder/orderCommodities'); // 使用门店



});