<?php

use think\Route;

Route::group('net-small', function () {

    #获取token
    Route::post('auth', 'net_small/auth/create');

    #刷新opcache
    Route::get('reset-opcache', 'net_small/auth/opcache');

    #生成小程序编码
    Route::post('wx-code', 'net_small/WxCode/create');

    Route::get('home-test', 'net_small/home/<USER>');
    #首页
    Route::get('home-title', 'net_small/component/title');
    Route::get('home', 'net_small/home/<USER>');
    Route::get('home-auth', 'net_small/HomeSpecialAuth/home');
    Route::get('get-dsn', 'net_small/Home/getDlrSupportNeedMsg');
    Route::get('combination-msg', 'net_small/Home/combinationMsg');
    Route::get('get-user-vin-list', 'net_small/Home/getUserVinList');

    Route::get('cache_time', 'net_small/component/cache_time');

    # 页面配置-自定义组件
    Route::get('page/component-list', 'net_small/component/pageComponentList');
    Route::get('page/component-detail','net_small/component/componentDetail');

    Route::get('waterfall-goods', 'net_small/Home/getWaterfallGoods');
    Route::get('waterfall-goods-auth', 'net_small/HomeSpecialAuth/getWaterfallGoodsAuth');
    Route::post('charge-service-status', 'net_small/Home/chargeServiceStatus');
    #专题页
    Route::get('special', 'net_small/special/index');
    Route::get('special-auth', 'net_small/HomeSpecialAuth/special');
    Route::get('check-staff', 'net_small/HomeSpecialAuth/checkStaff');
    Route::get('check-trade', 'net_small/HomeSpecialAuth/checkTrade');
    Route::get('trade-order', 'net_small/HomeSpecialAuth/tradeOrder');
    Route::get('trade-sms', 'net_small/HomeSpecialAuth/tradeSms');

    Route::get('wechat-get-bdp', 'net_small/Home/weChatGetBdp');
    Route::get('wechat-have-order', 'net_small/Home/weChatHaveOrder');
    Route::get('wechat-pay-order', 'net_small/Home/weChatPayOrder');

    #商品
    Route::get('goods/list', 'net_small/goods/goodsList');
    Route::get('goods/class', 'net_small/goods/goodsClass');
    Route::get('goods/detail', 'net_small/goods/detail', []);
    Route::get('goods/group-detail', 'net_small/goods/groupDetail', []);
    Route::get('goods/suit', 'net_small/goods/suit');
    Route::get('goods/waybill', 'net_small/goods/waybill');
    Route::get('goods/mail-price', 'net_small/goods/goodsMailPrice');
    Route::post('goods/collection', 'net_small/goods/saveCollection');
    Route::get('goods/sku-info', 'net_small/goods/goodsSkuInfo');
    Route::get('goods/change-suit', 'net_small/goods/changeSuit');
    Route::get('goods/hot', 'net_small/goods/goodsHot');
    Route::get('goods/mate', 'net_small/goods/goodsCarMate');
    Route::get('goods/xi-kf', 'net_small/goods/XiKf');
    Route::get('goods/get-section', 'net_small/home/<USER>');
    Route::get('goods/get-wi-price', 'net_small/goods/getWiPrice');
    Route::get('goods/xi-kf-sm', 'net_small/goods/XiKfSm'); //小程序版本小i
    Route::get('goods/get-gift-list', 'net_small/goods/getGiftList');
    Route::get('goods/get-seckill-user', 'net_small/goods/getSeckillUser');
    Route::get('goods/sku-detail-content', 'net_small/goods/skuDetailContent');
    Route::get('goods/suit-detail', 'net_small/goods/suitDetail');//套装详情
    Route::get('goods/suit-price', 'net_small/goods/suitPrice');//套装修改后价格
    Route::post('goods/best-activity-card', 'net_small/goods/bestActivityCard');
    Route::get('goods/point-js', 'net_small/goods/point_js');
    Route::get('goods/gift_card', 'net_small/goods/getGiftCard');//买赠卡券
    Route::post('goods/gift_card_num', 'net_small/goods/getGiftCardNum');//买赠卡券剩余次数
    Route::get('goods/check_gift_card', 'net_small/goods/checkGiftCard');//判断赠品是否有券
    Route::post('goods/check-vin-sku', 'net_small/goods/checkVinSku');//检查VIN与SKU匹配

    #订单
    Route::get('order/confirm', 'net_small/order/orderConfirm');
    Route::post('order/calc-discounts', 'net_small/order/calcDiscounts');

    Route::post('order/calc-discounts-bak', 'net_small/order/calcDiscountsBak');
    Route::post('order/confirm-dynamic', 'net_small/order/confirmDynamic');
    Route::get('order/mail', 'net_small/order/orderMail');
    Route::post('order/go-pay', 'net_small/order/goPay');
    Route::get('order/list', 'net_small/order/orderList');
    Route::post('order/fill', 'net_small/order/fill');
    Route::get('order/detail', 'net_small/order/detail');
    Route::get('order/finish', 'net_small/order/finish');
    Route::delete('order/cancel', 'net_small/order/cancel');
    Route::get('order/card-clash', 'net_small/order/card_clash');
    Route::get('order/refund', 'net_small/order/refund');
    Route::post('order/refund', 'net_small/order/refund_save');
    Route::get('order/sms', 'net_small/order/sms');
    Route::get('order/check-sms', 'net_small/order/check_sms');
    Route::get('order/check-phone', 'net_small/order/check_phone');
    Route::put('order/receive', 'net_small/order/receive');
    Route::delete('order/order-del', 'net_small/order/orderDelete');
    Route::get('order/card-most-point', 'net_small/order/card_most_point');
    Route::get('order/by-suit', 'net_small/order/BySuit'); //保养套餐列表
    Route::get('order/v2/by-suit', 'net_small/order/newBySuit'); //保养套餐列表
    Route::put('order/change-order-dlr', 'net_small/order/changOrderDlr'); //修改订单专营店编码
    Route::get('order/app-sm-pay', 'net_small/order/app_sm_pay'); //app支付小程序
    Route::get('order/pay-status', 'net_small/order/pay_status'); //支付状态
    Route::post('order/delaer-order-status', 'net_small/Order/delaerOrderStatus');
    Route::get('order/finish-order', 'net_small/order/finishOrder');
    Route::post('order/user-order', 'net_small/order/user_order');//用户某个分类下是否有订单
    Route::get('order/confirm-pick-up', 'net_small/order/orderConfirmPickUp');//取送车订单确认
    Route::POST('order/order-complete', 'net_small/order/orderComplete');//取送车订单完成
    // 根据卡券中心id获取订单信息
    Route::get('order/get-coupon-list', 'net_small/Order/getOrderListByCardId');
    Route::get('order/check-dlr', 'net_small/Order/checkDlr');//验证dlr是否合法
    Route::post('order/return-ship', 'net_small/order/returnShip');//回寄物流单号
    Route::get('order/check-buy-car-order', 'net_small/order/checkBuyCarOrder');//检查买车合同号是否有订单
    Route::get('order/logistics-info', 'net_small/order/logisticsInfo');//订单物流信息

    #售后-退款,退货,换货 & 物流公司
    Route::post('order/after-sale', 'net_small/OrderAfterSale/create');
    Route::get('order/after-sale', 'net_small/OrderAfterSale/show');
    Route::put('order/after-sale', 'net_small/OrderAfterSale/update');
    Route::get('express', 'net_small/OrderAfterSale/express');
    Route::get('order/check-refund-reason', 'net_small/order/checkRefundReasonByCommodityClass');
    Route::post('order/after-sale-refund', 'net_small/OrderAfterSale/refundCreate');
    Route::post('order/after-sale-refund-new', 'net_small/OrderAfterSale/refundCreateNew');


    #用户数据
    Route::get('user/card', 'net_small/user/getCard');
    Route::get('user/card-center', 'net_small/user/cardCenter');
    Route::get('user/card-center-page', 'net_small/user/cardCenterPage');
    Route::delete('user/archive', 'net_small/user/delUserArchives');
    Route::post('user/user-archives', 'net_small/user/bindUserArchives');
    Route::get('user/car-series', 'net_small/user/CarSeries');
    Route::get('user/car-displacement', 'net_small/user/carDisplacement');
    Route::get('user/car-year', 'net_small/user/carYear');
    Route::get('user/car-year-v2', 'net_small/user/carYearTwo');
    Route::get('user/archives', 'net_small/user/archives');
    Route::post('user/archives', 'net_small/user/archivesSave');
    Route::post('user/address', 'net_small/user/userAddressSave');
    Route::delete('user/address', 'net_small/user/del_address');
    Route::get('user/address', 'net_small/user/address');
    Route::get('user/collection', 'net_small/user/collection');
    Route::get('user/car-type', 'net_small/user/carType');
    Route::get('user/address-v2', 'net_small/user/addressTwo');
    Route::get('user/address-info-v2', 'net_small/user/userAddressInfo');
    Route::delete('user/address-v2', 'net_small/user/delAddressTwo');
    Route::post('user/address-v2', 'net_small/user/userAddressSaveTwo');
    Route::get('user/get-nick-head', 'net_small/user/getNickHead');
    Route::post('user/unfreeze-coupon', 'net_small/user/unfreezeCoupon');
    Route::post('user/freeze-coupon', 'net_small/user/freezeCoupon');

    Route::get('user/archives-v2', 'net_small/user/archivesTwo');
    Route::delete('user/archive-v2', 'net_small/user/delUserArchivesTwo');
    Route::post('user/user-archives-v2', 'net_small/user/bindUserArchivesTwo');
    Route::get('user/car-series-v2', 'net_small/user/carSeriesTwo');
    Route::get('user/car-type-v2', 'net_small/user/carTypeTwo');
    Route::post('user/archives-v2', 'net_small/user/archivesSaveTwo');
    Route::get('user/user-car-info', 'net_small/user/userCarInfo');
    Route::get('user/license-plate-vin', 'net_small/user/licensePlateVin');
    Route::get('user/card-available', 'net_small/user/cardAvailable');
    Route::post('user/card-use', 'net_small/user/cardUse');
    Route::post('user/card-message-e3s', 'net_small/user/cardMessageE3s');
    Route::get('user/is-new-diving-customer', 'net_small/user/isNewDivingCustomer');
    Route::post('user/revive', 'net_small/user/revive');
    Route::post('user/consume-coupon', 'net_small/user/consumeCoupon');

    #购物车
    Route::post('cart/cart', 'net_small/cart/addCart');
    Route::put('cart/cart', 'net_small/cart/putCart');
    Route::delete('cart/cart', 'net_small/cart/delCart');
    Route::get('cart/list', 'net_small/cart/CartList');
    Route::get('cart/dlr', 'net_small/cart/choose_dlr');
    Route::get('cart/act-change', 'net_small/cart/actChange');
    Route::get('cart/area', 'net_small/home/<USER>');
    Route::get('cart/point-money', 'net_small/cart/cart_point_money');
    Route::get('cart/cart-count', 'net_small/cart/cart_count');
    # 新增：购物车赠品券校验接口
    Route::post('cart/check-gift-card-count', 'net_small/cart/checkGiftCardCount');

    #团购
    Route::get('group/list', 'net_small/group/group_list');
    Route::get('group/info', 'net_small/group/groupInfo');

    #评价
    Route::get('comment/goods-comment', 'net_small/comment/goodsComment');
    Route::post('comment/comment', 'net_small/comment/save');
    Route::post('comment/like', 'net_small/comment/likeSave');
    Route::get('comment/comment', 'net_small/comment/comment');
    Route::put('comment/comment', 'net_small/comment/commentEdit'); //修改点评

    Route::get('normal/goods', 'net_small/normal/goods_kf');
    Route::get('normal/order', 'net_small/normal/order_list_kf');
    Route::get('normal/waybill', 'net_small/normal/waybill');

    #ccs 保养
    Route::get('ccs/ccs', 'net_small/ccs/ccs');

    #积分专区
    Route::get('exchange/convert', 'net_small/exchange/convert');
    Route::get('exchange/record', 'net_small/exchange/record');
    Route::get('exchange/basics', 'net_small/exchange/basics');
    Route::get('exchange/sign', 'net_small/exchange/sign');
    Route::post('exchange/do-sign', 'net_small/exchange/doSign');
    Route::get('exchange/winning-list', 'net_small/exchange/winning');
    Route::post('exchange/luck-draw', 'net_small/exchange/lottery');
    Route::put('exchange/up-address', 'net_small/exchange/upAddress');

    #N延保
    Route::get('insurance/car-series', 'net_small/Insurance/carSeries');
    Route::get('insurance/select-product', 'net_small/Insurance/selectProduct');
    Route::get('insurance/prices', 'net_small/Insurance/prices');
    Route::get('insurance/card-type', 'net_small/Insurance/cardType');
    Route::get('insurance/card-status', 'net_small/Insurance/cardStatus');
    Route::get('insurance/binding', 'net_small/Insurance/binding');
    Route::get('insurance/point', 'net_small/Insurance/point');
    Route::get('insurance/sms', 'net_small/Insurance/sms');
    Route::post('insurance/order', 'net_small/Insurance/orderSave');
    Route::get('insurance/order/:order_id', 'net_small/Insurance/order');

    #老友惠
    Route::get('friend/list', 'net_small/Friend/basicsList');
    Route::get('friend/list2', 'net_small/Friend/basicsList2');
    Route::get('friend/specs', 'net_small/Friend/specsList');
    Route::get('friend/vehicle-age', 'net_small/Friend/vehicleAge');
    Route::get('friend/vehicle-age2', 'net_small/Friend/vehicleAge2');
    Route::get('friend/upgrade-package', 'net_small/Friend/upgradePackage');
    Route::get('friend/upgrade-package2', 'net_small/Friend/upgradePackage2');
    Route::get('friend/card-status', 'net_small/Friend/cardStatus');
    Route::get('friend/combination-judge', 'net_small/Friend/combinationJudge');
    Route::get('friend/get-carer', 'net_small/Friend/getCarer');
    Route::get('friend/oldFriend', 'net_small/Friend/oldFriend');

    #官网设置
    Route::get('gwset/products', 'net_small/GwSet/products');
    Route::get('gwset/banners', 'net_small/GwSet/banners');
    Route::get('gwset/catalogs', 'net_small/GwSet/catalogs');
    Route::get('gwset/hots', 'net_small/GwSet/hots');
    Route::get('gwset/recommendations', 'net_small/GwSet/recommendations');
    Route::get('gwset/orders', 'net_small/GwSet/orders');
    Route::get('gwset/messages', 'net_small/GwSet/messages');
    Route::get('gwset/products', 'net_small/GwSet/products');
    Route::get('gwset/product-imgs', 'net_small/GwSet/productImg');
    Route::get('gwset/type-product', 'net_small/GwSet/getTypeProduct');
    Route::get('gwset/get-popup', 'net_small/GwSet/getPopup');
    Route::post('gwset/close-popup', 'net_small/GwSet/closePopup');
    Route::get('gwset/get-advert', 'net_small/GwSet/getAdvert');

    #预约保养/维修/钣喷
    Route::get('appointment/list', 'net_small/Appointment/detailsList');
    Route::get('appointment/technician', 'net_small/Appointment/technician');
    Route::get('appointment/adviser', 'net_small/Appointment/adviser');
    Route::get('appointment/dlr-time', 'net_small/Appointment/dlrTime');
    Route::post('appointment/subscribe', 'net_small/Appointment/subscribe');
    Route::post('appointment/cancel-subscribe', 'net_small/Appointment/cancelSubscribe');

    Route::post('subscribe/subscribe-status', 'net_small/Subscribe/subscribeStatus');

    #图片上传
    Route::post('file', 'net_small/File/create');
    Route::post('ocr', 'net_small/File/ocr');
    Route::post('files', 'net_small/File/multiple');

    #oneapp后台
    Route::get('admin/goods-list', 'net_small/Admin/getGoodsList');
    Route::get('admin/getGoodsForIds', 'net_small/Admin/getGoodsForIds');
    Route::get('admin/special-custom', 'net_small/Admin/specialCustom');
    Route::get('admin/special-fixed', 'net_small/Admin/specialFixed');
    Route::get('admin/card-goods', 'net_small/Admin/getCardGoodsList');
    Route::get('admin/card-goods-sku', 'net_small/Admin/getGoodsSku');
    #订单卡券
    Route::get('admin/order-card', 'net_small/Admin/order_card');
    Route::post('admin/order-card-consume', 'net_small/Admin/order_card_consume');
    Route::get('admin/card-redirect-url', 'net_small/Admin/card_redirect_url');
    Route::get('admin/user-card-status', 'net_small/Admin/user_card_status');
    #商品后台分类接口
    Route::get('admin/ajax_get_class', 'net_small/Admin/ajaxGetClass');


    #优惠券
    Route::get('card/card-poster', 'net_small/Card/getCardPoster');
    Route::get('card/help-msg', 'net_small/Card/helpMsg');
    Route::put('card/help-init', 'net_small/Card/helpInit');
    Route::get('card/help-status', 'net_small/Card/getHelpStatus');
    Route::get('card/card-list-by-point-code', 'net_small/Card/getCardListByPointCode');
    Route::get('card/ajax-get-card-info', 'net_small/Card/ajaxGetCardInfo');
    Route::get('card/get-card-info', 'net_small/Card/getCardRecINfo');
    # 新增：卡券激活相关接口
    Route::post('card/activation-jump', 'net_small/Card/getCardActivationJump');

    #用户注销条件判断
    Route::get('check/user', 'net_small/CheckUser/checkUser');
    Route::post('check/user-cancel', 'net_small/CheckUser/cancelUser');
    Route::post('check/sync', 'net_small/CheckUser/syncHeadNick');

    #专题页
    Route::get('ac-special/de202111', 'net_small/AcSpecial/de202111');
    Route::get('ac-special/de202111-card', 'net_small/AcSpecial/de202111_index_card');
    Route::get('ac-special/de202201-card', 'net_small/AcSpecial/de202201_index_card');
    Route::get('ac-special/draw202201-num', 'net_small/AcSpecial/draw202201_num');
    Route::post('ac-special/draw202201', 'net_small/AcSpecial/draw202201');
    Route::put('ac-special/draw202201-address', 'net_small/AcSpecial/draw202201_address');
    Route::get('ac-special/draw202201-list', 'net_small/AcSpecial/draw202201_list');
    Route::get('ac-special/draw202201-test', 'net_small/AcSpecial/test11');

    Route::get('ac-special/drive-goods', 'net_small/AcSpecial/drive_goods');
    Route::post('ac-special/drive-add-order', 'net_small/AcSpecial/drive_add_order');
    Route::get('ac-special/drive-can-order', 'net_small/AcSpecial/drive_can_order');
    Route::get('ac-special/new20220304', 'net_small/AcSpecial/new20220304');
    Route::get('ac-special/p33a-card-status', 'net_small/AcSpecial/p33aCardStatus');
    Route::post('ac-special/p33a-card-record', 'net_small/AcSpecial/p33aCardRecord');
    Route::get('ac-special/big-wheel-list', 'net_small/AcSpecial/bigWheelDrawList');
    Route::post('ac-special/big-wheel-draw', 'net_small/AcSpecial/bigWheelDraw');
    Route::get('ac-special/big-wheel-prize', 'net_small/AcSpecial/bigWheelDrawPrize');
    Route::get('ac-special/big-wheel-rotation', 'net_small/AcSpecial/bigWheelDrawRotation');
    Route::post('ac-special/big-wheel-point', 'net_small/AcSpecial/bigWheelDrawPoint');
    Route::post('ac-special/big-wheel-share', 'net_small/AcSpecial/bigWheelDrawShare');

    #大数据平台,创建or更新#
    Route::post('admin/bdp-info', 'net_small/Home/bdpInfo');
    Route::get('admin/bdp-order', 'net_small/Home/bdpOrder');

    #保养推荐
    Route::post('maintainance/recommend-discount-detail', 'net_small/Maintainance/discountDetail');
    Route::get('maintainance/recommend-selected', 'net_small/Maintainance/selectedSku');
    Route::get('maintainance/recommend-base', 'net_small/Maintainance/redommendBase');
    Route::get('maintainance/recommend-combo', 'net_small/Maintainance/recommendCombo');
    Route::get('maintainance/recommend-parts', 'net_small/Maintainance/recommendParts');
    Route::post('maintainance/activity-change', 'net_small/Maintainance/activityChange');


    Route::get('maintainance/get-sku-combo', 'net_small/Maintainance/getComboSkuByCommodityid');
    Route::get('maintainance/get-sku-part', 'net_small/Maintainance/getPartSkuByCommodityid');
    Route::get('maintainance/change-sku', 'net_small/Maintainance/changeSku');
    Route::post('maintainance/actual-recommend', 'net_small/Maintainance/actualRecommend');
    Route::post('maintainance/actual-recommend-ai', 'net_small/Maintainance/actualRecommendAi');
    Route::get('maintainance/recommend-base-ai', 'net_small/Maintainance/redommendBaseAi');
    Route::post('maintainance/check-group-card', 'net_small/Maintainance/checkGroupCard');

    Route::get('maintainance/test', 'net_small/Maintainance/test');

    #外部接口


    # 测试联友支付接口
    Route::any('normal/unifyPayService', 'net_small/normal/unifyPayService');
    Route::any('normal/unifyPayCallback', 'net_small/normal/unifyPayCallback');
    Route::any('normal/refundCallback', 'net_small/normal/refundCallback');
    Route::any('normal/unify-pay-result-info', 'net_small/normal/unifyPayResultInfo'); // 支付详情
    Route::any('normal/refund-order', 'net_small/normal/refundOrder'); // 退款接口
    Route::any('normal/unify-refund-info', 'net_small/normal/unifyRefundInfo'); // 退款详情接口
    Route::any('normal/query-merchant-info', 'net_small/normal/queryMerchantInfo'); // 商户列表
    Route::any('normal/payment-source-list', 'net_small/normal/getPaymentSourceList'); // 支付方式
    Route::any('normal/order-settle-doit', 'net_small/normal/orderSettleDoIt'); // 手动触发批量结算方法处理
    Route::any('normal/settle', 'net_small/normal/settle'); // 结算服务--弃用
    Route::any('normal/ly-settle', 'net_small/normal/ly_settle'); // 结算服务
    Route::get('normal/trade-bill-query', 'net_small/normal/tradeBillQuery'); // 查询交易账单信息
    Route::get('normal/draw-bill-query', 'net_small/normal/drawBillQuery'); // 查询交易账单信息
    Route::post('normal/set-app-id', 'net_small/normal/setAppId');
    # 小程序短链接
    Route::post('normal/url-link', 'net_small/normal/urlLink');

    # 测试e3s接口
    Route::get('normal/setBuOrder', 'net_small/normal/seBuServerInfo'); // 电商信息接收接口
    Route::post('normal/updateOrderStatus', 'net_small/normal/updateOrderStatus'); // 电商信息接收接口
    Route::post('normal/updateOrderStatus', 'net_small/normal/updateOrderStatus'); // 电商信息接收接口


    Route::get('ac-special/draw22618-list', 'net_small/AcSpecial/draw22618_list');
    Route::post('ac-special/draw22618', 'net_small/AcSpecial/draw22618');
    Route::post('ac-special/draw22618-buy', 'net_small/AcSpecial/draw22618_buy');
    Route::get('ac-special/draw22618-prize', 'net_small/AcSpecial/draw22618_prize');
    Route::get('ac-special/draw22618-rotation', 'net_small/AcSpecial/draw_rotation');

    Route::get('ac-special/draw220801-list', 'net_small/AcSpecial/draw_220801_list');
    Route::post('ac-special/draw220801', 'net_small/AcSpecial/draw_220801');
    Route::get('ac-special/draw220801-prize', 'net_small/AcSpecial/draw_220801_prize');
    Route::get('ac-special/draw220801-rotation', 'net_small/AcSpecial/draw_220801_rotation');

    # 联友支付
    Route::any('pay_plat/unifyPayCallback', 'net_small/PayPlat/unifyPayCallback');
    Route::post('pay_plat/unify-pay-callback', 'net_small/PayPlat/unifyPayCallback');
    # 结算通知
    Route::post('home/sett-notice', 'net_small/Home/settNotice');
    # 提供给罗骏对账查询
    Route::get('normal/getPayList', 'net_small/normal/getPayList');

    Route::get('ac-special/fu-pin', 'net_small/AcSpecial/fuPin');
    ##--------------------基础数据--------------------
    Route::get('basic/config', 'net_small/basic/getConfig');

    //臻享服务包
    Route::get('service-package/enjoy', 'net_small/ServicePackage/enjoy');
    Route::get('service-package/enjoy-sku', 'net_small/ServicePackage/enjoySku');

    //服务页
    Route::get('service-page/like', 'net_small/ServicePage/youLike');
    Route::get('service-page/act', 'net_small/ServicePage/actList');
    Route::get('service-page/hot-sale', 'net_small/ServicePage/hotSaleRec');
    Route::get('service-page/pot-cust-rec', 'net_small/ServicePage/potCustRec');
    Route::get('service-page/rec-page', 'net_small/ServicePage/recPage');



    //启辰积分兑换专区
    Route::get('exchange/new_basics', 'net_small/exchange/new_basics');
    Route::get('exchange/good_detail', 'net_small/exchange/good_detail');
    Route::get('exchange/new_convert', 'net_small/exchange/new_convert');

    # 提供给罗骏对账查询
    Route::get('normal/getPayList', 'net_small/normal/getPayList');

    # 移动授权(CYS0001)
    Route::post('yd_point/auth', 'net_small/YdPayByPoint/auth');

    # 移动下单(CYS0003)
    Route::post('yd_point/place-order', 'net_small/YdPayByPoint/placeOrder');

    # 移动扣减(CYS0004)
    Route::post('yd_point/dec-order', 'net_small/YdPayByPoint/decOrder');

    # 移动发送短信(CYS0005)
    Route::post('yd_point/send-sms', 'net_small/YdPayByPoint/sendSms');

    //PZ1AJifen
    Route::any('pay_plat/pointCallBack', 'net_small/PayPlat/pointCallBack');

    # 移动兑换成功下单
    Route::post('yd_point/fill', 'net_small/YdPayByPoint/fill');

    # 移动兑换查看是否已授权
    Route::get('yd_point/check-auth', 'net_small/YdPayByPoint/checkAuth');

    # 移动兑换订单确认
    Route::get('yd_point/order-confirm', 'net_small/YdPayByPoint/orderConfirm');

    # 移动兑换订单详情
    Route::get('yd_point/order-detail', 'net_small/YdPayByPoint/orderDetail');


    // 抽奖
    Route::get('draw/add', 'net_small/Draw/addDrawNum');
    Route::get('draw/sub', 'net_small/Draw/subDrawNum');

    Route::get('group/double_eleven', 'net_small/Group/double_eleven');

    Route::get('file/vin-dlr', 'net_small/File/vinDlr');


    // 京东云仓销售出库回调
    Route::post('jd_warehouse/delivery-detail', 'net_small/JdWarehouse/deliveryDetail');
    Route::post('jd_warehouse/return-to-warehouse', 'net_small/JdWarehouse/returnToWarehouse');

    //活动中心
    Route::get('activity-center/verify-activity-user', 'net_small/ActivityCenter/verifyActivitySingUser');
    Route::post('activity-center/verify-activity-batch-user', 'net_small/ActivityCenter/verifyActivityBatchUser');
    Route::post('activity-center/send-behavior-coupon', 'net_small/ActivityCenter/sendBehaviorCoupon');
    Route::post('activity-center/send-activate-coupon', 'net_small/ActivityCenter/sendActivateCoupon');
    Route::post('activity-center/activate-coupon', 'net_small/ActivityCenter/activateCoupon');
    Route::get('activity-center/activity-coupon-list', 'net_small/ActivityCenter/getMallActivityCouponList');
    Route::post('activity-center/activity-receive-coupon', 'net_small/ActivityCenter/mallActivityReceiveCoupon');
    Route::post('activity-center/notify-activity-center', 'net_small/ActivityCenter/notifyActivityCenter');

    //开票
    Route::get('invoice/detail', 'net_small/Invoice/detail');
    Route::post('invoice/save-invoice', 'net_small/Invoice/saveInvoice');
    Route::post('invoice/send-email', 'net_small/Invoice/sendEmail');
    Route::get('invoice/desc', 'net_small/Invoice/desc');
//    Route::post('invoice/create-invoice', 'net_small/Invoice/createInvoice');
    Route::get('invoice/get-invoice-info', 'net_small/Invoice/getInvoiceInfo');
    Route::get('invoice/get-example', 'net_small/Invoice/getExample');
    Route::get('invoice/cancle-invoice', 'net_small/Invoice/cancleInvoice');
    Route::get('invoice/invoice-order-commoidty-list', 'net_small/Invoice/invoiceOrderCommodityList');
    Route::post('invoice/record-invoice', 'net_small/Invoice/redoInvoice');
    Route::post('invoice/retry-invoice', 'net_small/Invoice/reTryInvoice');
    Route::post('invoice/update-invoice', 'net_small/Invoice/updateInvoice');
    # 合作伙伴回调接口
    Route::post('partner-callback/delivery', 'net_small/PartnerCallback/deliveryCallback');
    Route::get('partner-callback/health', 'net_small/PartnerCallback/health');






});
