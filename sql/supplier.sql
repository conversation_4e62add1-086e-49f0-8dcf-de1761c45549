alter table t_db_gift add COLUMN   `card_available` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否可用券 0否1是';
alter table t_db_gift add COLUMN     `rel_card_ids` varchar(255) DEFAULT '' COMMENT '关联卡券ids';


-- 备注示例：时间日期 + 需求功能

CREATE TABLE `t_bu_supplier_order_push_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `order_detail_no` varchar(64) NOT NULL COMMENT '订单编码',
  `total_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '订单总额',
  `delivery_fee` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '运费',
  `actual_payment_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '订单实付现金',
  `order_status` varchar(32) NOT NULL DEFAULT '' COMMENT '订单状态',
  `receipt_address` varchar(255) NOT NULL DEFAULT '' COMMENT '收货地址',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `order_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
  `coupon_discount_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '卡券优惠总额',
  `payment_method` varchar(64) NOT NULL DEFAULT '' COMMENT '支付方式',
  `order_paid_time` datetime DEFAULT NULL COMMENT '支付时间',
  `total_payment_integral` int(11) NOT NULL DEFAULT '0' COMMENT '订单实付积分',
  `user_name` varchar(64) NOT NULL DEFAULT '' COMMENT '用户姓名',
  `user_mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '用户手机号',
  `register_mobile` varchar(32) NOT NULL DEFAULT '' COMMENT '注册手机号',
  `delivery_callback_url` varchar(255) NOT NULL DEFAULT '' COMMENT '回调地址',
  `split_time` datetime DEFAULT NULL COMMENT '结算时间',
  `split_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '结算状态',
  `activity_discount_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '活动优惠总额',
  `detail` text COMMENT '商品明细',
  `push_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '传送状态 0-失败 1-成功',
  `audit_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '第三方审单状态',
  `push_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '传送时间',
  `is_enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可用',
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
  `last_updated_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `modifier` varchar(10) DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  COMMENT='合作伙伴调用日志表';




-- 2025-01-21 合作伙伴回调日志表
CREATE TABLE `t_i_partner_callback_log` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                            `callback_type` varchar(50) NOT NULL DEFAULT '' COMMENT '回调类型(partner_delivery_callback等)',
                                            `order_detail_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单详情编号',
                                            `request_url` varchar(255) NOT NULL DEFAULT '' COMMENT '请求URL',
                                            `request_method` varchar(10) NOT NULL DEFAULT 'POST' COMMENT '请求方法',
                                            `request_params` text COMMENT '请求参数(JSON格式)',
                                            `response_data` text COMMENT '响应数据(JSON格式)',
                                            `process_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '处理状态(0-处理中 1-成功 2-失败)',
                                            `error_message` text COMMENT '错误信息',
                                            `created_date` datetime NOT NULL COMMENT '创建时间',
                                            `last_updated_date` datetime DEFAULT NULL COMMENT '最后更新时间',
                                            `remark` varchar(255) DEFAULT '' COMMENT '备注',
                                            PRIMARY KEY (`id`),
                                            KEY `idx_order_detail_no` (`order_detail_no`),
                                            KEY `idx_callback_type` (`callback_type`),
                                            KEY `idx_process_status` (`process_status`),
                                            KEY `idx_created_date` (`created_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合作伙伴回调日志表';

