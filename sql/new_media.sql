CREATE TABLE `t_new_media_commodity`
(
    `id`              bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `commodity_id`    varchar(30)  NOT NULL DEFAULT '' COMMENT '商品id',
    `platform`        varchar(10)  NOT NULL DEFAULT '' COMMENT '新媒体平台',
    `commodity_name`  varchar(200) NOT NULL DEFAULT '' COMMENT '商品名称',
    `commodity_type`  tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '商品类型 1-次卡  2-团购套餐  3-代金券  4-提货券',
    `type_value`      varchar(30)  NOT NULL DEFAULT '' COMMENT '类型值',
    `commodity_class` varchar(200) NOT NULL DEFAULT '' COMMENT '商品分类',
    `is_enable`       tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否可用',
    `created_date`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`         varchar(50)           DEFAULT '' COMMENT '创建人',
    `modified_date`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `modifier`        varchar(50)           DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY               `commodity_id_index` (`commodity_id`) USING BTREE
) ENGINE=InnoDB  COMMENT='新媒体商品表';

CREATE TABLE `t_new_media_commodity_sku`
(
    `id`                     bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `commodity_id`           varchar(30)  NOT NULL DEFAULT '' COMMENT '商品id',
    `sku_id`                 varchar(30)  NOT NULL DEFAULT '' COMMENT 'sku_id',
    `sku_name`               varchar(200) NOT NULL DEFAULT '' COMMENT 'sku名称',
    `spare_parts_type`       varchar(30)  NOT NULL DEFAULT '' COMMENT '备件类型',
    `working_hours_type`     varchar(30)  NOT NULL DEFAULT '' COMMENT '工时类型',
    `spare_parts_activity`   varchar(100) NOT NULL DEFAULT '' COMMENT '备件活动',
    `working_hours_activity` varchar(100) NOT NULL DEFAULT '' COMMENT '工时活动',
    `factory_activity_ratio` varchar(20)  NOT NULL DEFAULT '' COMMENT '厂家活动补贴比例',
    `card_ratio`             varchar(20)  NOT NULL DEFAULT '' COMMENT '优惠券补贴比例',
    `activity_ratio`         varchar(20)  NOT NULL DEFAULT '' COMMENT '活动补贴比例',
    `member_ratio`           varchar(20)  NOT NULL DEFAULT '' COMMENT '会员补贴比例',
    `integral_ratio`         varchar(20)  NOT NULL DEFAULT '' COMMENT '积分补贴比例',
    `voucher_ratio`          varchar(20)  NOT NULL DEFAULT '' COMMENT '通兑券补贴比例',
    `is_enable`              tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否可用',
    `created_date`           datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`                varchar(50)           DEFAULT '' COMMENT '创建人',
    `modified_date`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `modifier`               varchar(50)           DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                      `commodity_sku_id_index` (`commodity_id`,`sku_id`) USING BTREE
) ENGINE=InnoDB  COMMENT='新媒体商品sku表';

CREATE TABLE `t_new_media_commodity_spare_parts`
(
    `id`                         bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `commodity_id`               varchar(30) NOT NULL DEFAULT '' COMMENT '商品id',
    `sku_id`                     varchar(30) NOT NULL DEFAULT '' COMMENT 'sku_id',
    `spare_parts_code`           varchar(50) NOT NULL DEFAULT '' COMMENT '备件编码',
    `spare_parts_name`           varchar(50) NOT NULL DEFAULT '' COMMENT '备件名称',
    `spare_parts_price`          bigint(20) NOT NULL DEFAULT '0' COMMENT '备件原价 分',
    `spare_parts_activity_price` bigint(20) NOT NULL DEFAULT '0' COMMENT '备件活动价 分',
    `spare_parts_num`            int(10) NOT NULL DEFAULT '0' COMMENT '备件数量',
    `is_enable`                  tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可用',
    `created_date`               datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`                    varchar(50)          DEFAULT '' COMMENT '创建人',
    `modified_date`              datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `modifier`                   varchar(50)          DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                          `commodity_sku_id_index` (`commodity_id`,`sku_id`) USING BTREE
) ENGINE=InnoDB  COMMENT='新媒体商品备件表';


CREATE TABLE `t_new_media_commodity_working_hours`
(
    `id`                           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `commodity_id`                 varchar(30)  NOT NULL DEFAULT '' COMMENT '商品id',
    `sku_id`                       varchar(30)  NOT NULL DEFAULT '' COMMENT 'sku_id',
    `working_hours_code`           varchar(100) NOT NULL DEFAULT '' COMMENT '工时编码',
    `working_hours_name`           varchar(100) NOT NULL DEFAULT '' COMMENT '工时名称',
    `working_hours_price`          bigint(20) NOT NULL DEFAULT '0' COMMENT '工时原价 分',
    `working_hours_activity_price` bigint(20) NOT NULL DEFAULT '0' COMMENT '工时活动价 分',
    `working_hours_num`            int(10) unsigned NOT NULL DEFAULT '0' COMMENT '工时数量',
    `is_enable`                    tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否可用',
    `created_date`                 datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`                      varchar(50)           DEFAULT '' COMMENT '创建人',
    `modified_date`                datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `modifier`                     varchar(50)           DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                            `commodity_sku_id_index` (`commodity_id`,`sku_id`) USING BTREE
) ENGINE=InnoDB  COMMENT='新媒体商品工时表';


CREATE TABLE `t_new_media_commodity_card`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `commodity_id`  varchar(30) NOT NULL DEFAULT '' COMMENT '商品id',
    `sku_id`        varchar(30) NOT NULL DEFAULT '' COMMENT 'sku_id',
    `card_price`    bigint(20) NOT NULL DEFAULT '0' COMMENT '次卡原价 分',
    `is_enable`     tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否可用',
    `created_date`  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`       varchar(50)          DEFAULT '' COMMENT '创建人',
    `modified_date` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `modifier`      varchar(50)          DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY             `commodity_sku_id_index` (`commodity_id`,`sku_id`) USING BTREE
) ENGINE=InnoDB  COMMENT='新媒体商品次卡表';


CREATE TABLE `t_new_media_dlr`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `platform`      varchar(10) NOT NULL DEFAULT '' COMMENT '新媒体平台',
    `dlr_id`        varchar(30) NOT NULL DEFAULT '' COMMENT '新媒体门店id',
    `dfn_dlr_code`  varchar(30) NOT NULL DEFAULT '' COMMENT 'dfn门店编码',
    `dfn_dlr_name`  varchar(30) NOT NULL DEFAULT '' COMMENT 'dfn门店名称',
    `is_enable`     tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否可用',
    `created_date`  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`       varchar(50)          DEFAULT '' COMMENT '创建人',
    `modified_date` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `modifier`      varchar(50)          DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY             `dfn_dlr_code_index` (`dfn_dlr_code`) USING BTREE,
    KEY             `dlr_id_index` (`dlr_id`) USING BTREE
) ENGINE=InnoDB  COMMENT='新媒体门店表';

CREATE TABLE `t_new_media_import`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `import`        tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '导入类型 1-商品 2-门店',
    `file_name`     varchar(100) NOT NULL DEFAULT '' COMMENT '文件名称',
    `path`          varchar(100) NOT NULL DEFAULT '' COMMENT '路径',
    `remark`        varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `is_enable`     tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否可用',
    `created_date`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`       varchar(50)           DEFAULT '' COMMENT '创建人',
    `modified_date` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `modifier`      varchar(50)           DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB  COMMENT='新媒体导入表';


CREATE TABLE `t_new_media_import_record`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `import_id`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '导入文件id',
    `is_success`    tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否成功 0-失败 1-成功',
    `record`        text COMMENT '导入记录',
    `is_enable`     tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否可用',
    `created_date`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`       varchar(50)       DEFAULT '' COMMENT '创建人',
    `modified_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `modifier`      varchar(50)       DEFAULT '' COMMENT '修改人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY             `import_id_index` (`import_id`) USING BTREE
) ENGINE=InnoDB  COMMENT='新媒体导入详情表';