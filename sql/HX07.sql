CREATE TABLE `t_db_jifen_send_log` (
                                       `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `order_code` VARCHAR(64) NOT NULL COMMENT '订单号',
                                       `user_id` INT(11) NOT NULL COMMENT '用户ID',
                                       `status_code` VARCHAR(11) DEFAULT NULL COMMENT '响应状态码',
                                       `response` TEXT COMMENT '接口响应内容',
                                       `is_enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可用',
                                       `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `creator` varchar(10) DEFAULT NULL COMMENT '创建人',
                                       `last_updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `modifier` varchar(10) DEFAULT NULL COMMENT '修改人',
                                       PRIMARY KEY (`id`),
                                       KEY `idx_order_code` (`order_code`),
                                       KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分下发请求日志表';
